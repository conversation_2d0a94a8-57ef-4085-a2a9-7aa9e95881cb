# yaml-language-server: $schema=https://coderabbit.ai/integrations/schema.v2.json
language: en
early_access: true
reviews:
  profile: chill
  request_changes_workflow: true
  high_level_summary: true
  poem: false
  review_status: true
  collapse_walkthrough: false
  sequence_diagrams: true
  tools:
    golangci-lint:
      enabled: true
  path_filters:
    - "!**/*.pb.go"
    - "!**/*.pb.gw.go"
    - "!**/*.mod"
    - "!**/*.sum"
    - "!**/generated/**"
    - "!**/mocks/**"
  path_instructions:
    - path: "**/*.go"
      instructions: >
        Review the Go code, point out issues relative to principles of clean code, expressiveness, and performance. 
        Domain-Driven Design: Verify that Domain-Driven Design principles are integrated properly, with clear separation of concerns and well-defined domain models. Code
        Quality and Maintainability: Focus on readability, proper use of Go idioms, and maintainable code structure. Check for consistent naming conventions, efficient error handling, and adequate documentation, including meaningful comments where needed.
        Performance and Scalability: Identify any potential areas for performance improvement, especially in areas like database access, concurrency, caching, and data persistence. Suggest refactoring if the code can be streamlined or optimized.
    - path: "tests/e2e/**/*"
      instructions: >
        Assess the integration and e2e test code assessing sufficient code coverage for the changes associated in the pull request, suggest improvements by exploring edge cases
    - path: "**/*_test.go"
      instructions: >
        Assess the unit test code assessing sufficient code coverage for the changes associated in the pull request, suggest improvements by exploring edge cases
    - path: "**/*.md"
      instructions: >
        Assess the documentation for misspellings, grammatical errors, missing documentation and correctness
  auto_review:
    enabled: true
    drafts: false
    auto_incremental_review: true
chat:
  auto_reply: true
enable_free_tier: false
