# Ignore the Git folder
.git
.gitignore
.github
.augmentignore

# Ignore local environment and dependency files
*.env
*.env.*
vendor/
docker-compose.yml
k8s-secrets.yaml
deployments/k8s/
deployments/docker-compose.yaml

# Ignore logs and temporary files
*.log
tmp/
.magicodeconfig
.vscode/
.idea/
qodana.yaml
.air.toml

# Ignore test data or test-specific folders (optional)
test/
coverage/

# Ignore binaries
*.exe
*.out
*.o
*.a
main
server
