name: Build and Deploy

on:
  push:
    branches:
      - release/prod

env:
  PROJECT_ID: matiks-go
  DEPLOYMENT_NAME: matiks-go
  CLUSTER: cluster-1
  ZONE: us-west1-a
  IMAGE: gcr.io/matiks-go/matiks-go-image

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Generate build timestamp
        id: timestamp
        run: echo "timestamp=$(date +'%Y%m%d-%H%M%S')" >> $GITHUB_OUTPUT

      - name: Get short SHA
        id: sha
        run: echo "sha=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - id: auth
        name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}
          install_components: gke-gcloud-auth-plugin

      - name: Configure Docker Auth
        run: gcloud auth configure-docker gcr.io

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Cache Docker layers
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Build and Push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: deployments/Dockerfile
          platforms: linux/amd64
          push: true
          tags: |
            ${{ env.IMAGE }}:latest
            ${{ env.IMAGE }}:${{ steps.sha.outputs.sha }}
            ${{ env.IMAGE }}:${{ steps.timestamp.outputs.timestamp }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max

      - name: Move cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache

      - name: Get GKE Credentials
        run: |
          gcloud container clusters get-credentials ${{ env.CLUSTER }} \
            --zone ${{ env.ZONE }} \
            --project ${{ env.PROJECT_ID }}

      - name: Update Deployment
        run: |
          kubectl set image deployment/${{ env.DEPLOYMENT_NAME }} \
            *=${{ env.IMAGE }}:${{ steps.sha.outputs.sha }}
          kubectl rollout status deployment/${{ env.DEPLOYMENT_NAME }}
          kubectl create configmap last-successful-deploy-${{ steps.timestamp.outputs.timestamp }} \
            --from-literal=image=${{ env.IMAGE }}:${{ steps.sha.outputs.sha }} \
            --from-literal=timestamp="${{ steps.timestamp.outputs.timestamp }}" \
            --from-literal=commit_sha="${{ steps.sha.outputs.sha }}" \
            -o yaml --dry-run=client | kubectl apply -f -

      - name: Add deployment annotation
        if: success()
        run: |
          kubectl annotate deployment ${{ env.DEPLOYMENT_NAME }} \
            kubernetes.io/change-cause="Deploy ${{ steps.sha.outputs.sha }} at ${{ steps.timestamp.outputs.timestamp }}"
