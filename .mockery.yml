all: false
dir: 'mocks/{{replaceAll "internal" "internal_" .InterfaceDirRelative}}'
filename: '{{.InterfaceName}}.go'
force-file-write: true
formatter: goimports
log-level: info
structname: '{{.Mock}}{{.InterfaceName}}'
pkgname: '{{.SrcPackageName}}'
recursive: true
require-template-schema-exists: true
template: testify
template-schema: '{{.Template}}.schema.json'
packages:
  matiksOfficial/matiks-server-go:
    config:
      all: true
