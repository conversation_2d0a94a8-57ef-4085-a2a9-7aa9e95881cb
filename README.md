[![Matiks](https://github.com/user-attachments/assets/89e1ee98-28e9-44d5-b3b0-a97eaf0f7550)](http://matiks.com)

<h3 align="center">
<em>Improve your Mental Aptitude, practice and compete with peers across the Globe 🌏 <br>
Play Mental Aptitude games, rank up and join the world's smartest community at Matiks. </em>
</h3>

---

## ⚙️ Dev Docs

This document provides essential information for developers working on the Matiks server project.

## Build/Configuration Instructions

### Prerequisites

- Go 1.23.2 (version must match the one specified in `go.mod`)
- MongoDB
- Redis
- Firebase account (for storage and push notifications)

### Environment Setup

The application uses environment variables for configuration, which can be set in a `.env` file at the project root. Key
configuration parameters include:

```
PORT=4000                      # Server port
MONGODB_URI=<connection-uri>   # MongoDB connection URI
MONGODB_NAME=<db-name>         # MongoDB database name
REDIS_ADDR=localhost:6379      # Redis address
JWT_SECRET=<secret>            # Secret for JWT token generation
ENVIRONMENT=local              # Environment (local, dev, prod)
GOOGLE_CLIENT_ID=<id>          # Google OAuth client ID
GOOGLE_CLIENT_SECRET=<secret>  # Google OAuth client secret
FIREBASE_STORAGE_SERVICE_ACCOUNT_KEY=<path>  # Path to Firebase storage service account key
FIREBASE_PUSH_NOTIFICATION_SERVICE_ACCOUNT_KEY=<path>  # Path to Firebase push notification service account key
```

See `pkg/config/config.go` for a complete list of configuration parameters.

### Building the Application

To build the application:

```bash
go build -o server cmd/server/main.go
```

### Running the Application

To run the application without building:

```bash
go run cmd/server/main.go
```

Or run the built binary:

```bash
./server
```

### GraphQL Schema Generation

The project uses [gqlgen](https://gqlgen.com/) to generate type-safe code for GraphQL resolvers and structs. After
modifying the GraphQL schema, run:

```bash
go generate ./...
```

## Testing Information

### Running Tests

To run all tests:

```bash
go test ./...
```

To run tests in a specific package with verbose output:

```bash
go test -v ./path/to/package
```

To run a specific test:

```bash
go test -v ./path/to/package -run TestName
```

### Writing Tests

Tests should be placed in the same package as the code they test, with filenames ending in `_test.go`. The project uses
the standard Go testing package along with the testify library for assertions and mocking.

#### Example Test

Here's an example of a table-driven test for the `Contains` function in the `slices` package:

```go
package test

import (
  "slices"
  "testing"
)

func TestContains(t *testing.T) {
  tests := []struct {
    name     string
    slice    []int
    element  int
    expected bool
  }{
    {
      name:     "element exists in slice",
      slice:    []int{1, 2, 3, 4, 5},
      element:  3,
      expected: true,
    },
    {
      name:     "element does not exist in slice",
      slice:    []int{1, 2, 3, 4, 5},
      element:  6,
      expected: false,
    },
    {
      name:     "empty slice",
      slice:    []int{},
      element:  1,
      expected: false,
    },
  }

  for _, tt := range tests {
    t.Run(tt.name, func(t *testing.T) {
      result := slices.Contains(tt.slice, tt.element)
      if result != tt.expected {
        t.Errorf("Contains(%v, %v) = %v, expected %v", tt.slice, tt.element, result, tt.expected)
      }
    })
  }
}
```

### Mocking

The project uses [mockery](https://github.com/vektra/mockery) to generate mocks for interfaces. Mocks are stored in the
`mocks` directory. The mockery configuration is in `.mockery.yml`.

## Additional Development Information

### Project Structure

- `cmd/`: Contains the main applications
    - `server/`: The main server application
- `internal/`: Contains internal packages
    - `api/`: API-related code
    - `auth/`: Authentication-related code
    - `constants/`: Constants used throughout the application
    - `di/`: Dependency injection setup
    - `domain/`: Business logic organized by domain
    - `graph/`: GraphQL-related code
        - `generated/`: Generated GraphQL code
        - `resolvers/`: GraphQL resolvers
        - `schema/`: GraphQL schema definitions
    - `infrastructure/`: Infrastructure-related code (database, cache, etc.)
    - `middleware/`: HTTP middleware
    - `models/`: Data models
- `pkg/`: Shared packages
    - `config/`: Configuration management
    - `logger/`: Logging utilities
    - `metrics/`: Metrics collection
- `utils/`: Utility functions
- `tests/`: Test-related code
    - `e2e/`: End-to-end tests

### Dependency Injection

The project uses Uber's [fx](https://github.com/uber-go/fx) for dependency injection. Services and repositories should
be designed to be easily injectable.

### Code Style

The project follows the [Uber Go Style Guide](https://github.com/uber-go/guide/blob/master/style.md). Key points
include:

- Use meaningful variable names
- Group similar declarations
- Minimize the scope of variables
- Use consistent error handling
- Prefer table-driven tests

### Error Handling

Follow the principles outlined
in [Don't just check errors, handle them gracefully](https://dave.cheney.net/2016/04/27/dont-just-check-errors-handle-them-gracefully):

- Errors should be handled at the appropriate level
- Wrap errors with context when returning them up the call stack
- Don't use panic for normal error handling

### GraphQL Development

The project uses GraphQL for its API. The schema is defined in `.graphqls` files in the `internal/graph/schema`
directory. After modifying the schema, run `go generate ./...` to regenerate the GraphQL code.

### Database Access

The project uses MongoDB as its primary database. Database access is abstracted through repository interfaces in the
`internal/infrastructure/repository` package.

### Caching

Redis is used for caching. Cache access is abstracted through the `internal/infrastructure/cache` package.
