package main

//const (
//	mongoURI = "mongodb+srv://myAtlasDBUsername:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster077"
//	dbName   = "test"
//	collName = "users"
//	csvFile  = "dummy_data.csv"
//)

//func main() {
//	clientOptions := options.Client().ApplyURI(mongoURI)
//	client, err := mongo.Connect(context.TODO(), clientOptions)
//	if err != nil {
//		log.Fatalf("MongoDB connection error: %v", err)
//	}
//	defer client.Disconnect(context.TODO())
//
//	collection := client.Database(dbName).Collection(collName)
//
//	nameImagePairs, err := readCSV(csvFile)
//	if err != nil {
//		log.Fatalf("Error reading CSV file: %v", err)
//	}
//
//	if len(nameImagePairs) == 0 {
//		log.Fatal("CSV file is empty or incorrectly formatted")
//	}
//
//	startTime := time.Now()
//
//	filter := bson.M{"isHumanBot": true}
//	cursor, err := collection.Find(context.TODO(), filter)
//	if err != nil {
//		log.Fatalf("Error fetching bot users: %v", err)
//	}
//	defer cursor.Close(context.TODO())
//
//	var botUsers []bson.M
//	if err = cursor.All(context.TODO(), &botUsers); err != nil {
//		log.Fatalf("Error decoding bot users: %v", err)
//	}
//
//	if len(botUsers) > len(nameImagePairs) {
//		log.Printf("Warning: Not enough names in CSV. Some users will not be updated.")
//	}
//
//	var models []mongo.WriteModel
//	for i, user := range botUsers {
//		if i >= len(nameImagePairs) {
//			break
//		}
//
//		userID, ok := user["_id"]
//		if !ok {
//			continue
//		}
//
//		url := strings.TrimSpace(nameImagePairs[i][1])
//
//		filter := bson.M{"_id": userID}
//		update := bson.M{
//			"$set": bson.M{
//				"profileImageUrl": url,
//			},
//		}
//
//		model := mongo.NewUpdateOneModel().
//			SetFilter(filter).
//			SetUpdate(update)
//		models = append(models, model)
//	}
//
//	if len(models) > 0 {
//		bulkOptions := options.BulkWrite().SetOrdered(false)
//		_, err = collection.BulkWrite(context.TODO(), models, bulkOptions)
//		if err != nil {
//			log.Fatalf("Bulk update error: %v", err)
//		}
//		log.Printf("Successfully updated %d bot users.", len(models))
//	} else {
//		log.Println("No bot users found to update.")
//	}
//
//	elapsedTime := time.Since(startTime)
//	log.Printf("Process completed. Time taken: %v", elapsedTime)
//}
//
//func readCSV(filename string) ([][]string, error) {
//	file, err := os.Open(filename)
//	if err != nil {
//		return nil, err
//	}
//	defer file.Close()
//
//	reader := csv.NewReader(file)
//	records, err := reader.ReadAll()
//	if err != nil {
//		return nil, err
//	}
//
//	if len(records) > 0 && (strings.EqualFold(records[0][0], "Name") || strings.EqualFold(records[0][1], "URL")) {
//		records = records[1:]
//	}
//
//	for i := range records {
//		for j := range records[i] {
//			records[i][j] = strings.TrimSpace(records[i][j])
//		}
//	}
//
//	return records, nil
//}
