package main

//import (
//	"bufio"
//	"context"
//	"fmt"
//	"log"
//	"os"
//	"strings"
//	"time"
//
//	"github.com/spf13/viper"
//	"go.mongodb.org/mongo-driver/bson"
//	"go.mongodb.org/mongo-driver/mongo"
//	"go.mongodb.org/mongo-driver/mongo/options"
//	"go.mongodb.org/mongo-driver/mongo/readpref"
//)
//
//func main() {
//	// Configure Viper to read from .env file
//	viper.SetConfigFile(".env")
//
//	// Read the config file
//	err := viper.ReadInConfig()
//	if err != nil {
//		log.Fatalf("Error reading config file: %v", err)
//	}
//
//	// Configure Viper to also read environment variables
//	viper.AutomaticEnv()
//
//	// Get MongoDB URI and database name from environment variables
//	mongoURI := viper.GetString("MONGODB_URI")
//	if mongoURI == "" {
//		log.Fatalf("MONGODB_URI environment variable not set")
//	}
//
//	dbName := viper.GetString("MONGODB_NAME")
//	if dbName == "" {
//		log.Fatalf("MONGODB_NAME environment variable not set")
//	}
//
//	fmt.Printf("Using database: %s\n", dbName)
//
//	// Connect to MongoDB
//	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
//	defer cancel()
//
//	client, err := mongo.Connect(ctx, options.Client().ApplyURI(mongoURI))
//	if err != nil {
//		log.Fatalf("Failed to connect to MongoDB: %v", err)
//	}
//	defer client.Disconnect(ctx)
//
//	// Ping the MongoDB server to verify connection
//	err = client.Ping(ctx, readpref.Primary())
//	if err != nil {
//		log.Fatalf("Failed to ping MongoDB: %v", err)
//	}
//
//	// Get database and collections
//	db := client.Database(dbName)
//	sourceCollection := db.Collection("userActivity")
//	targetCollection := db.Collection("userDailyActivity")
//
//	// Count documents in source collection
//	count, err := sourceCollection.CountDocuments(ctx, bson.M{})
//	if err != nil {
//		log.Fatalf("Failed to count documents: %v", err)
//	}
//	fmt.Printf("Found %d documents to migrate\n", count)
//
//	// Create a cursor for the source collection
//	cursor, err := sourceCollection.Find(ctx, bson.M{})
//	if err != nil {
//		log.Fatalf("Failed to get cursor: %v", err)
//	}
//	defer cursor.Close(ctx)
//
//	// Process documents in batches
//	batchSize := 100000
//	batch := make([]interface{}, 0, batchSize)
//	totalMigrated := 0
//
//	for cursor.Next(ctx) {
//		var doc bson.M
//		if err := cursor.Decode(&doc); err != nil {
//			log.Printf("Failed to decode document: %v", err)
//			continue
//		}
//
//		batch = append(batch, doc)
//
//		// Insert batch when it reaches the batch size
//		if len(batch) >= batchSize {
//			if err := insertBatch(ctx, targetCollection, batch); err != nil {
//				log.Printf("Failed to insert batch: %v", err)
//			} else {
//				totalMigrated += len(batch)
//				fmt.Printf("Migrated %d/%d documents\n", totalMigrated, count)
//			}
//			batch = make([]interface{}, 0, batchSize)
//		}
//	}
//
//	// Insert any remaining documents
//	if len(batch) > 0 {
//		if err := insertBatch(ctx, targetCollection, batch); err != nil {
//			log.Printf("Failed to insert final batch: %v", err)
//		} else {
//			totalMigrated += len(batch)
//		}
//	}
//
//	fmt.Printf("Migration completed. Migrated %d/%d documents\n", totalMigrated, count)
//
//	// Verify migration by counting documents in target collection
//	targetCount, err := targetCollection.CountDocuments(ctx, bson.M{})
//	if err != nil {
//		log.Fatalf("Failed to count documents in target collection: %v", err)
//	}
//
//	fmt.Printf("Source collection: %d documents\n", count)
//	fmt.Printf("Target collection: %d documents\n", targetCount)
//
//	// Ask for confirmation before deleting source collection data
//	if count > 0 && targetCount == count {
//		fmt.Println("\nMigration appears successful.")
//		fmt.Print("Do you want to delete the data from the userActivity collection? (yes/no): ")
//		reader := bufio.NewReader(os.Stdin)
//		response, _ := reader.ReadString('\n')
//		response = strings.TrimSpace(strings.ToLower(response))
//
//		if response == "yes" || response == "y" {
//			// Delete all documents from source collection
//			deleteResult, err := sourceCollection.DeleteMany(ctx, bson.M{})
//			if err != nil {
//				log.Fatalf("Failed to delete documents from source collection: %v", err)
//			}
//			fmt.Printf("Successfully deleted %d documents from userActivity collection\n", deleteResult.DeletedCount)
//		} else {
//			fmt.Println("Data deletion cancelled.")
//		}
//	} else {
//		fmt.Println("\nMigration may not be complete. Not deleting source collection data.")
//		fmt.Println("Please verify the migration manually and run a separate delete command if needed.")
//	}
//}
//
//func insertBatch(ctx context.Context, collection *mongo.Collection, batch []interface{}) error {
//	opts := options.InsertMany().SetOrdered(false)
//	_, err := collection.InsertMany(ctx, batch, opts)
//	if err != nil {
//		// Check for duplicate key errors and continue
//		if mongo.IsDuplicateKeyError(err) {
//			fmt.Println("Some documents already exist in the target collection (duplicate key error)")
//			return nil
//		}
//		return err
//	}
//	return nil
//}
