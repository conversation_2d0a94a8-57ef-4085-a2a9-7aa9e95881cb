package main

//import (
//	"context"
//	"fmt"
//	"log"
//	"time"
//
//	"go.mongodb.org/mongo-driver/bson"
//	"go.mongodb.org/mongo-driver/bson/primitive"
//	"go.mongodb.org/mongo-driver/mongo"
//	"go.mongodb.org/mongo-driver/mongo/options"
//
//	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
//	"matiksOfficial/matiks-server-go/internal/models"
//
//	"github.com/spf13/viper"
//	"go.mongodb.org/mongo-driver/mongo/readpref"
//)
//
//const (
//	batchSize  = 30000 // Number of buckets to process in one batch
//	timeSpent  = 60000 // Default time spent in milliseconds (1 minute)
//	maxBulkOps = 30000 // Maximum number of operations in a single bulk write
//)
//
//// Stats to track progress
//type Stats struct {
//	BucketsProcessed     int
//	GamesProcessed       int
//	PuzzleGamesProcessed int
//	ActivitiesCreated    int
//	Errors               int
//}
//
//func (s *Stats) IncrementBuckets() {
//	s.BucketsProcessed++
//}
//
//func (s *Stats) IncrementGames() {
//	s.GamesProcessed++
//}
//
//func (s *Stats) IncrementPuzzleGames() {
//	s.PuzzleGamesProcessed++
//}
//
//func (s *Stats) IncrementActivities() {
//	s.ActivitiesCreated++
//}
//
//func (s *Stats) IncrementErrors() {
//	s.Errors++
//}
//
//func (s *Stats) String() string {
//	return fmt.Sprintf("Buckets processed: %d, Games processed: %d, Puzzle games processed: %d, Activities created: %d, Errors: %d",
//		s.BucketsProcessed, s.GamesProcessed, s.PuzzleGamesProcessed, s.ActivitiesCreated, s.Errors)
//}
//
//// Renamed main to avoid conflict with other scripts in the same package
//func main() { // Ensure the function name is correct
//	// Initialize context
//	ctx := context.Background()
//
//	// // Initialize logger - Removed zap logger initialization
//	// logger, err := zap.NewProduction()
//	// if err != nil {
//	// 	log.Fatalf("Failed to initialize logger: %v", err)
//	// }
//	// defer logger.Sync()
//
//	viper.SetConfigFile(".env")
//
//	// Read the config file
//	var err error // Re-declare err variable
//	err = viper.ReadInConfig()
//	if err != nil {
//		log.Fatalf("Error reading config file: %v", err)
//	}
//
//	// Configure Viper to also read environment variables
//	viper.AutomaticEnv()
//	// // zlog.SetLogger(logger) // Removed zlog setup
//
//	mongoURI := viper.GetString("MONGODB_URI")
//	if mongoURI == "" {
//		log.Fatalf("MONGODB_URI environment variable not set")
//	}
//
//	dbName := viper.GetString("MONGODB_NAME")
//	if dbName == "" {
//		log.Fatalf("MONGODB_NAME environment variable not set")
//	}
//
//	client, err := mongo.Connect(ctx, options.Client().ApplyURI(mongoURI))
//	if err != nil {
//		log.Fatalf("Failed to connect to MongoDB: %v", err)
//	}
//	defer client.Disconnect(ctx)
//
//	// Ping the MongoDB server to verify connection
//	err = client.Ping(ctx, readpref.Primary())
//	if err != nil {
//		log.Fatalf("Failed to ping MongoDB: %v", err)
//	}
//
//	// Get database and collections
//	db := client.Database(dbName)
//
//	// Initialize repositories
//	userGameBucketRepo := db.Collection("usergames")
//	gameRepo := db.Collection("games")
//	puzzleGameRepo := db.Collection("puzzleGames")
//	userActivitiesRepo := db.Collection("user_activities")
//
//	// Initialize stats
//	stats := &Stats{}
//
//	// Process all game buckets
//	processAllGameBuckets(ctx, userGameBucketRepo, gameRepo, puzzleGameRepo, userActivitiesRepo, stats)
//
//	fmt.Printf("Migration completed successfully. %s\n", stats)
//}
//
//// mongoDatabase implements the database.Database interface
//type mongoDatabase struct {
//	client *mongo.Client
//	dbName string
//}
//
//func (m *mongoDatabase) Collection(name string) *mongo.Collection {
//	return m.client.Database(m.dbName).Collection(name)
//}
//
//func (m *mongoDatabase) Ping(ctx context.Context) error {
//	return m.client.Ping(ctx, nil)
//}
//
//func processAllGameBuckets(
//	ctx context.Context,
//	userGameBucketCollection *mongo.Collection,
//	gameCollection *mongo.Collection,
//	puzzleGameCollection *mongo.Collection,
//	userActivitiesCollection *mongo.Collection,
//	stats *Stats,
//) {
//	var lastBucketID primitive.ObjectID
//	var writeModels []mongo.WriteModel
//
//	for {
//		// Find game buckets in batches
//		filter := bson.M{}
//		if !lastBucketID.IsZero() {
//			filter["_id"] = bson.M{"$gt": lastBucketID}
//		}
//
//		opts := options.Find().SetLimit(int64(batchSize)).SetSort(bson.M{"_id": 1})
//		cursor, err := userGameBucketCollection.Find(ctx, filter, opts)
//		if err != nil {
//			fmt.Printf("ERROR: Failed to fetch game buckets cursor: %v\n", err)
//			break
//		}
//		defer cursor.Close(ctx)
//
//		var buckets []*models.UserGameBucket
//		if err = cursor.All(ctx, &buckets); err != nil {
//			fmt.Printf("ERROR: Failed to decode game buckets: %v\n", err)
//			break
//		}
//
//		if len(buckets) == 0 {
//			break // No more buckets to process
//		}
//
//		// Process all buckets in this batch together
//		batchWriteModels := processBatchOfGameBuckets(ctx, buckets, gameCollection, puzzleGameCollection, stats)
//		writeModels = append(writeModels, batchWriteModels...)
//
//		// Update the last bucket ID for the next iteration
//		lastBucketID = buckets[len(buckets)-1].ID
//
//		fmt.Printf("Processed batch of %d buckets. Current stats: %s\n", len(buckets), stats)
//	}
//
//	// Perform bulk write if there are any write models
//	if len(writeModels) > 0 {
//		// Split into smaller batches to avoid MongoDB document size limits
//		for i := 0; i < len(writeModels); i += maxBulkOps {
//			end := i + maxBulkOps
//			if end > len(writeModels) {
//				end = len(writeModels)
//			}
//			batch := writeModels[i:end]
//
//			// Bulk insert activities
//			bulkWriteOpts := options.BulkWrite().SetOrdered(false) // Allow partial success
//			result, err := userActivitiesCollection.BulkWrite(ctx, batch, bulkWriteOpts)
//			if err != nil {
//				fmt.Printf("ERROR: Failed to bulk write activities: %v\n", err)
//				stats.Errors++
//			} else {
//				stats.ActivitiesCreated += int(result.InsertedCount)
//				fmt.Printf("SUCCESS: Bulk inserted %d activities.\n", result.InsertedCount)
//			}
//		}
//	}
//}
//
//// processBatchOfGameBuckets processes multiple buckets at once to minimize DB calls
//func processBatchOfGameBuckets(
//	ctx context.Context,
//	buckets []*models.UserGameBucket,
//	gameCollection *mongo.Collection,
//	puzzleGameCollection *mongo.Collection,
//	stats *Stats,
//) []mongo.WriteModel {
//	// Collect all game IDs from all buckets
//	var allGameIDs []primitive.ObjectID
//	// Maps game ID to a list of user IDs (since one game can be associated with multiple users)
//	gameIDToUserIDsMap := make(map[string][]primitive.ObjectID)
//
//	// Track which buckets are processed
//	for _, bucket := range buckets {
//		if len(bucket.Games) == 0 {
//			stats.IncrementBuckets()
//			continue
//		}
//
//		for _, gameID := range bucket.Games {
//			// Check if we've already seen this game ID
//			gameIDStr := gameID.Hex()
//			if _, exists := gameIDToUserIDsMap[gameIDStr]; !exists {
//				// If this is the first time seeing this game ID, add it to allGameIDs
//				allGameIDs = append(allGameIDs, gameID)
//			}
//			// Add this user ID to the list of users for this game
//			gameIDToUserIDsMap[gameIDStr] = append(gameIDToUserIDsMap[gameIDStr], bucket.UserID)
//		}
//		stats.IncrementBuckets()
//	}
//
//	if len(allGameIDs) == 0 {
//		return []mongo.WriteModel{}
//	}
//
//	// Bulk fetch all regular games at once
//	regularGames := fetchRegularGamesInBulk(ctx, allGameIDs, gameCollection)
//
//	// Determine which game IDs were not found in regular games
//	missingGameIDs := findMissingGameIDs(allGameIDs, regularGames)
//
//	// Bulk fetch all puzzle games for the missing IDs at once
//	puzzleGames := fetchPuzzleGamesInBulk(ctx, missingGameIDs, puzzleGameCollection)
//
//	// Check if any games were not found in either collection
//	notFoundGameIDs := findMissingGameIDs(missingGameIDs, puzzleGames)
//	for _, gameID := range notFoundGameIDs {
//		gameIDStr := gameID.Hex()
//		userIDs, exists := gameIDToUserIDsMap[gameIDStr]
//		if !exists {
//			fmt.Printf("ERROR: Failed to fetch game %s and it's not associated with any users in the current batch\n", gameIDStr)
//			stats.IncrementErrors()
//			continue
//		}
//
//		for _, userID := range userIDs {
//			fmt.Printf("ERROR: Failed to fetch game %s for user %s in either collection: game not found\n", gameIDStr, userID.Hex())
//			stats.IncrementErrors()
//		}
//	}
//
//	// Generate activities for all games
//	var activitiesToInsert []*models.UserActivity
//
//	// Process regular games
//	for _, game := range regularGames {
//		if game.ID == nil {
//			fmt.Printf("WARNING: Found regular game with nil ID, skipping\n")
//			continue
//		}
//
//		gameIDStr := game.ID.Hex()
//		userIDs, exists := gameIDToUserIDsMap[gameIDStr]
//		if !exists {
//			fmt.Printf("WARNING: Game %s found but not associated with any users in the current batch\n", gameIDStr)
//			continue
//		}
//
//		for _, userID := range userIDs {
//			activity := generateActivityForRegularGame(ctx, userID, game, stats)
//			if activity != nil {
//				activitiesToInsert = append(activitiesToInsert, activity)
//				// Don't increment stats.ActivitiesCreated here - it's done after bulk write
//			}
//		}
//		stats.GamesProcessed++
//	}
//
//	// Process puzzle games
//	for _, game := range puzzleGames {
//		if game.ID.IsZero() {
//			fmt.Printf("WARNING: Found puzzle game with zero ID, skipping\n")
//			continue
//		}
//
//		gameIDStr := game.ID.Hex()
//		userIDs, exists := gameIDToUserIDsMap[gameIDStr]
//		if !exists {
//			fmt.Printf("WARNING: Puzzle game %s found but not associated with any users in the current batch\n", gameIDStr)
//			continue
//		}
//
//		for _, userID := range userIDs {
//			activity := generateActivityForPuzzleGame(ctx, userID, game, stats)
//			if activity != nil {
//				activitiesToInsert = append(activitiesToInsert, activity)
//				// Don't increment stats.ActivitiesCreated here - it's done after bulk write
//			}
//		}
//		stats.PuzzleGamesProcessed++
//	}
//
//	// Create write models for bulk insert
//	activityModels := make([]mongo.WriteModel, len(activitiesToInsert))
//	for i, activity := range activitiesToInsert {
//		activityModels[i] = mongo.NewInsertOneModel().SetDocument(activity)
//	}
//
//	return activityModels
//}
//
//// Original function kept for reference but no longer used
//func processGameBucket(
//	ctx context.Context,
//	bucket *models.UserGameBucket,
//	gameCollection *mongo.Collection,
//	puzzleGameCollection *mongo.Collection,
//	stats *Stats,
//) []mongo.WriteModel {
//	// This function is kept for reference but is no longer used
//	// The functionality has been moved to processBatchOfGameBuckets
//	return []mongo.WriteModel{}
//}
//
//// generateActivityForRegularGame creates a UserActivity model for a regular game, returns nil if invalid
//func generateActivityForRegularGame(
//	ctx context.Context,
//	userID primitive.ObjectID,
//	game *models.Game,
//	stats *Stats, // Keep stats for potential warnings/errors during generation
//) *models.UserActivity { // Return type changed
//	// Check if user was a player in this game
//	var isPlayerInGame bool
//	for _, player := range game.Players {
//		if player.UserID == userID {
//			isPlayerInGame = true
//			break
//		}
//	}
//
//	if !isPlayerInGame {
//		// fmt.Printf("WARN: User %s is not a player in game %s\n", userID.Hex(), game.ID.Hex()) // Uncomment if needed
//		// fmt.Printf("DEBUG: User %s not a player in game %s, skipping activity creation.\n", userID.Hex(), game.ID.Hex())
//		return nil // Return nil instead of exiting
//	}
//
//	// Create user activity entry
//	activityType := gameutils.GetActivityTypeFromGameType(game.GameType)
//
//	// Create the activity
//	activity := &models.UserActivity{
//		ID:           primitive.NewObjectID(),
//		UserID:       userID,
//		ActivityType: activityType,
//		ActivityID:   game.ID,
//		TimeSpent:    timeSpent, // Default time spent
//		Timestamp:    getGameTimestamp(game),
//	}
//
//	return activity // Return the generated activity
//}
//
//// generateActivityForPuzzleGame creates a UserActivity model for a puzzle game, returns nil if invalid
//func generateActivityForPuzzleGame(
//	ctx context.Context,
//	userID primitive.ObjectID,
//	game *models.PuzzleGame,
//	stats *Stats, // Keep stats for potential warnings/errors during generation
//) *models.UserActivity { // Return type changed
//	// Check if user was a player in this game
//	var isPlayerInGame bool
//	for _, player := range game.Players {
//		if player.UserID == userID {
//			isPlayerInGame = true
//			break
//		}
//	}
//
//	if !isPlayerInGame {
//		return nil // Return nil instead of exiting
//	}
//
//	// Create user activity entry
//	activityType := gameutils.GetActivityTypeFromPuzzleGameType(game.GameType)
//
//	// Create the activity
//	activity := &models.UserActivity{
//		ID:           primitive.NewObjectID(),
//		UserID:       userID,
//		ActivityType: activityType,
//		ActivityID:   &game.ID,
//		TimeSpent:    timeSpent, // Default time spent
//		Timestamp:    getPuzzleGameTimestamp(game),
//	}
//
//	return activity // Return the generated activity
//}
//
//func getGameTimestamp(game *models.Game) time.Time {
//	// Use start time if available, otherwise use current time
//	if game.StartTime != nil {
//		return *game.StartTime
//	}
//
//	if game.CreatedAt != nil {
//		return *game.CreatedAt
//	}
//
//	return time.Now()
//}
//
//func getPuzzleGameTimestamp(game *models.PuzzleGame) time.Time {
//	// Use start time if available, otherwise use current time
//	if game.StartTime != nil {
//		return *game.StartTime
//	}
//
//	return time.Now()
//}
//
//// fetchRegularGamesInBulk fetches multiple games at once with optimized batching
//func fetchRegularGamesInBulk(ctx context.Context, gameIDs []primitive.ObjectID, gameCollection *mongo.Collection) []*models.Game {
//	if len(gameIDs) == 0 {
//		return []*models.Game{}
//	}
//
//	// Process in batches to avoid MongoDB query size limits
//	var allGames []*models.Game
//
//	// Process in batches
//	for i := 0; i < len(gameIDs); i += maxBulkOps {
//		end := i + maxBulkOps
//		if end > len(gameIDs) {
//			end = len(gameIDs)
//		}
//		batchIDs := gameIDs[i:end]
//
//		// Create filter for bulk fetch
//		filter := bson.M{"_id": bson.M{"$in": batchIDs}}
//
//		// Fetch games
//		cursor, err := gameCollection.Find(ctx, filter)
//		if err != nil {
//			fmt.Printf("ERROR: Failed to bulk fetch regular games cursor (batch %d-%d): %v\n", i, end, err)
//			continue // Skip this batch but continue with others
//		}
//
//		var batchGames []*models.Game
//		if err = cursor.All(ctx, &batchGames); err != nil {
//			fmt.Printf("ERROR: Failed to decode regular games (batch %d-%d): %v\n", i, end, err)
//			cursor.Close(ctx)
//			continue // Skip this batch but continue with others
//		}
//		cursor.Close(ctx)
//
//		// Add batch results to overall results
//		allGames = append(allGames, batchGames...)
//		fmt.Printf("Fetched %d regular games in batch %d-%d\n", len(batchGames), i, end)
//	}
//
//	return allGames
//}
//
//// fetchPuzzleGamesInBulk fetches multiple puzzle games at once with optimized batching
//func fetchPuzzleGamesInBulk(ctx context.Context, gameIDs []primitive.ObjectID, puzzleGameCollection *mongo.Collection) []*models.PuzzleGame {
//	if len(gameIDs) == 0 {
//		return []*models.PuzzleGame{}
//	}
//
//	// Process in batches to avoid MongoDB query size limits
//	var allGames []*models.PuzzleGame
//
//	// Process in batches
//	for i := 0; i < len(gameIDs); i += maxBulkOps {
//		end := i + maxBulkOps
//		if end > len(gameIDs) {
//			end = len(gameIDs)
//		}
//		batchIDs := gameIDs[i:end]
//
//		// Create filter for bulk fetch
//		filter := bson.M{"_id": bson.M{"$in": batchIDs}}
//
//		// Fetch puzzle games
//		cursor, err := puzzleGameCollection.Find(ctx, filter)
//		if err != nil {
//			fmt.Printf("ERROR: Failed to bulk fetch puzzle games cursor (batch %d-%d): %v\n", i, end, err)
//			continue // Skip this batch but continue with others
//		}
//
//		var batchGames []*models.PuzzleGame
//		if err = cursor.All(ctx, &batchGames); err != nil {
//			fmt.Printf("ERROR: Failed to decode puzzle games (batch %d-%d): %v\n", i, end, err)
//			cursor.Close(ctx)
//			continue // Skip this batch but continue with others
//		}
//		cursor.Close(ctx)
//
//		// Add batch results to overall results
//		allGames = append(allGames, batchGames...)
//		fmt.Printf("Fetched %d puzzle games in batch %d-%d\n", len(batchGames), i, end)
//	}
//
//	return allGames
//}
//
//// findMissingGameIDs finds game IDs that are in the original list but not in the fetched games
//// Optimized for large batches
//func findMissingGameIDs(originalIDs []primitive.ObjectID, games interface{}) []primitive.ObjectID {
//	// Create a map of found game IDs for a quick lookup
//	foundIDs := make(map[string]bool, len(originalIDs)) // Pre-allocate for better performance
//
//	switch typedGames := games.(type) {
//	case []*models.Game:
//		for _, game := range typedGames {
//			if game.ID != nil {
//				foundIDs[game.ID.Hex()] = true
//			}
//		}
//	case []*models.PuzzleGame:
//		for _, game := range typedGames {
//			foundIDs[game.ID.Hex()] = true
//		}
//	}
//
//	// Find missing IDs - pre-allocate for better performance
//	// Estimate capacity based on typical miss rate (adjust if needed)
//	estimatedMissingCount := len(originalIDs) / 10 // Assume ~10% miss rate
//	if estimatedMissingCount < 10 {
//		estimatedMissingCount = 10 // Minimum allocation
//	}
//
//	missingIDs := make([]primitive.ObjectID, 0, estimatedMissingCount)
//	for _, id := range originalIDs {
//		if !foundIDs[id.Hex()] {
//			missingIDs = append(missingIDs, id)
//		}
//	}
//
//	return missingIDs
//}
