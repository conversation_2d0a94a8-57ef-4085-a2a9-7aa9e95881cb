package main

//import (
//	"context"
//	"fmt"
//	"log"
//	"math/rand/v2"
//	"time"
//
//	"github.com/spf13/viper"
//	"go.mongodb.org/mongo-driver/bson"
//	"go.mongodb.org/mongo-driver/bson/primitive"
//	"go.mongodb.org/mongo-driver/mongo"
//	"go.mongodb.org/mongo-driver/mongo/options"
//)
//
//// Constants for referral code generation
//const (
//	ReferralCodeCounterName = "referral_code_counter"
//	ReferralCodeLength      = 8 // Total length of the referral code (2-letter prefix + 6-digit number)
//)
//
//// Counter represents a named counter in the database
//type Counter struct {
//	Name  string `bson:"_id"`
//	Value int64  `bson:"value"`
//}
//
//type User struct {
//	ID           primitive.ObjectID `json:"id" bson:"_id"`
//	ReferralCode *string            `json:"referralCode" bson:"referralCode"`
//}
//
//// Constants for batch processing
//const (
//	UserBatchSize      = 10000 // Number of users to process in a batch
//	BulkWriteBatchSize = 1000  // Number of write operations to perform in a single bulk write
//)
//
//func main() {
//	// Configure Viper to read from .env file
//	viper.SetConfigFile(".env")
//
//	// Read the config file
//	err := viper.ReadInConfig()
//	if err != nil {
//		log.Fatalf("Error reading config file: %v", err)
//	}
//
//	// Configure Viper to also read environment variables
//	viper.AutomaticEnv()
//
//	// Get MongoDB URI and database name from environment variables
//	mongoURI := viper.GetString("MONGODB_MAIN_URI")
//	if mongoURI == "" {
//		log.Fatalf("MONGODB_URI environment variable not set")
//	}
//
//	dbName := viper.GetString("MONGODB_NAME")
//	if dbName == "" {
//		log.Fatalf("MONGODB_NAME environment variable not set")
//	}
//
//	fmt.Printf("Using database: %s\n", dbName)
//
//	// Connect to MongoDB
//	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute) // Longer timeout for batch processing
//	defer cancel()
//
//	client, err := mongo.Connect(ctx, options.Client().ApplyURI(mongoURI))
//	if err != nil {
//		log.Fatalf("MongoDB connection error: %v", err)
//	}
//	defer client.Disconnect(ctx)
//
//	usersCol := client.Database(dbName).Collection("users")
//	countersCol := client.Database(dbName).Collection("counters")
//
//	// Create a map to store all assigned codes to ensure uniqueness
//	assignedCodes := make(map[string]struct{})
//
//	// First, load all existing valid referral codes to avoid duplicates
//	fmt.Println("Loading existing referral codes...")
//	existingCodesFilter := bson.M{"referralCode": bson.M{"$exists": true, "$ne": nil}}
//	existingCodesCursor, err := usersCol.Find(ctx, existingCodesFilter, options.Find().SetProjection(bson.M{"referralCode": 1}))
//	if err != nil {
//		log.Fatalf("Error fetching existing referral codes: %v", err)
//	}
//	defer existingCodesCursor.Close(ctx)
//
//	var existingCodeUsers []User
//	if err = existingCodesCursor.All(ctx, &existingCodeUsers); err != nil {
//		log.Fatalf("Error decoding existing referral codes: %v", err)
//	}
//
//	for _, u := range existingCodeUsers {
//		if u.ReferralCode != nil && len(*u.ReferralCode) == ReferralCodeLength {
//			assignedCodes[*u.ReferralCode] = struct{}{}
//		}
//	}
//	fmt.Printf("Loaded %d existing referral codes\n", len(assignedCodes))
//
//	// Find users without valid referral codes
//	fmt.Println("Processing users without valid referral codes...")
//	filter := bson.M{
//		"$or": []bson.M{
//			{"referralCode": bson.M{"$exists": false}},
//			{"referralCode": nil},
//			{"referralCode": ""},
//			{"referralCode": bson.M{"$not": bson.M{"$regex": fmt.Sprintf("^.{%d}$", ReferralCodeLength)}}},
//		},
//	}
//
//	// Count total users to process
//	totalUsers, err := usersCol.CountDocuments(ctx, filter)
//	if err != nil {
//		log.Fatalf("Error counting users: %v", err)
//	}
//	fmt.Printf("Found %d users without valid referral codes\n", totalUsers)
//
//	// Get the current counter value or initialize it if it doesn't exist
//	fmt.Println("Getting current referral code counter value...")
//	var currentCounter Counter
//	var startingCounterValue int64 = 1 // Default starting value if counter doesn't exist
//
//	err = countersCol.FindOne(ctx, bson.M{"_id": ReferralCodeCounterName}).Decode(&currentCounter)
//	if err != nil {
//		if err == mongo.ErrNoDocuments {
//			fmt.Println("Counter not found, will initialize with value 1")
//		} else {
//			log.Printf("Warning: Error fetching counter: %v. Will start from value 1.", err)
//		}
//	} else {
//		startingCounterValue = currentCounter.Value
//		fmt.Printf("Current counter value: %d\n", startingCounterValue)
//	}
//
//	// Initialize the sequence counter for generating referral codes
//	sequenceCounter := startingCounterValue
//
//	// Process users in batches
//	processedUsers := int64(0)
//	processedBatches := 0
//	totalBulkWrites := 0
//
//	// Create options for batch processing
//	findOptions := options.Find().SetBatchSize(UserBatchSize)
//
//	// Start processing users in batches
//	cursor, err := usersCol.Find(ctx, filter, findOptions)
//	if err != nil {
//		log.Fatalf("Error creating cursor for users: %v", err)
//	}
//	defer cursor.Close(ctx)
//
//	// Process all batches
//	var userBatch []User
//	for {
//		userBatch = []User{}
//		writeModels := make([]mongo.WriteModel, 0, UserBatchSize)
//		userCount := 0
//
//		// Read a batch of users
//		for cursor.Next(ctx) {
//			var user User
//			if err := cursor.Decode(&user); err != nil {
//				log.Printf("Error decoding user: %v, skipping", err)
//				continue
//			}
//			userBatch = append(userBatch, user)
//			userCount++
//
//			if userCount >= UserBatchSize {
//				break
//			}
//		}
//
//		// If no users were read, we're done
//		if len(userBatch) == 0 {
//			break
//		}
//
//		test := ""
//
//		// Process the batch
//		for _, u := range userBatch {
//			// Generate a unique referral code using the in-memory counter
//			referralCode := generateSequentialReferralCode(sequenceCounter, &assignedCodes)
//			sequenceCounter++ // Increment the counter for the next code
//			test = referralCode
//
//			writeModels = append(writeModels, mongo.NewUpdateOneModel().
//				SetFilter(bson.M{"_id": u.ID}).
//				SetUpdate(bson.M{"$set": bson.M{"referralCode": referralCode, "isReferred": false}}))
//		}
//
//		fmt.Println("Used Referral Code", test)
//
//		// Write in smaller batches for better performance
//		if len(writeModels) > 0 {
//			for i := 0; i < len(writeModels); i += BulkWriteBatchSize {
//				end := i + BulkWriteBatchSize
//				if end > len(writeModels) {
//					end = len(writeModels)
//				}
//
//				batchSlice := writeModels[i:end]
//				_, err = usersCol.BulkWrite(ctx, batchSlice)
//				if err != nil {
//					log.Fatalf("Bulk update error: %v", err)
//				}
//				totalBulkWrites++
//			}
//		}
//
//		processedUsers += int64(len(userBatch))
//		processedBatches++
//
//		fmt.Printf("Processed batch %d: %d users (total: %d/%d)\n",
//			processedBatches, len(userBatch), processedUsers, totalUsers)
//	}
//
//	if err := cursor.Err(); err != nil {
//		log.Fatalf("Cursor error: %v", err)
//	}
//
//	// Update the counter in the database with the final value
//	if processedUsers > 0 {
//		fmt.Printf("Updating counter from %d to %d\n", startingCounterValue, sequenceCounter)
//		_, err = countersCol.UpdateOne(
//			ctx,
//			bson.M{"_id": ReferralCodeCounterName},
//			bson.M{"$set": bson.M{"value": sequenceCounter}},
//			options.Update().SetUpsert(true),
//		)
//		if err != nil {
//			log.Printf("Warning: Failed to update counter: %v", err)
//		}
//	}
//
//	fmt.Printf("\nCompleted processing %d users in %d batches with %d bulk writes\n",
//		processedUsers, processedBatches, totalBulkWrites)
//	return
//}
//
//// generateSequentialReferralCode creates a referral code using a sequential counter
//// Format: 2-letter prefix + 6-digit sequential counter (padded with zeros)
//func generateSequentialReferralCode(seq int64, assignedCodes *map[string]struct{}) string {
//	// Create a prefix to make codes more readable and categorizable
//	prefixes := []string{"MT", "ST", "MK", "SK"} // Matiks-related prefixes
//
//	// Use the sequence number to determine which prefix to use (for variety)
//	prefixIndex := int(seq % int64(len(prefixes)))
//	prefix := prefixes[prefixIndex]
//
//	// Format the sequence number as a 6-digit string with leading zeros
//	sequentialPart := fmt.Sprintf("%06d", seq)
//
//	// If the sequential part is longer than 6 digits, truncate it to the last 6 digits
//	if len(sequentialPart) > 6 {
//		sequentialPart = sequentialPart[len(sequentialPart)-6:]
//	}
//
//	// Combine prefix and sequential part
//	code := prefix + sequentialPart
//
//	// Check if the code is already assigned (should never happen with a counter)
//	if _, ok := (*assignedCodes)[code]; ok {
//		log.Printf("Unexpected duplicate referral code detected: %s", code)
//		return fallbackToRandomReferralCode(assignedCodes)
//	}
//
//	// Add to assigned codes
//	(*assignedCodes)[code] = struct{}{}
//	return code
//}
//
//// fallbackToRandomReferralCode is a helper method to generate a unique random code
//// when the sequential method fails
//func fallbackToRandomReferralCode(assignedCodes *map[string]struct{}) string {
//	for i := 0; i < 10; i++ { // Try up to 10 times to generate a unique code
//		code := generateRandomReferralCode()
//
//		// Check if code already exists
//		if _, ok := (*assignedCodes)[code]; !ok {
//			// Code is unique, add it to assigned codes and return it
//			(*assignedCodes)[code] = struct{}{}
//			return code
//		}
//	}
//
//	// If we couldn't generate a unique code after several attempts, generate one more
//	// and hope for the best
//	code := generateRandomReferralCode()
//	(*assignedCodes)[code] = struct{}{}
//	return code
//}
//
//// generateRandomReferralCode creates a referral code with random characters
//func generateRandomReferralCode() string {
//	// Use a wider character set including both letters and numbers
//	chars := "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789" // Removed confusing chars like O,0,I,l,1
//
//	// Create a prefix to make codes more readable and categorizable
//	prefixes := []string{"MT", "ST", "MK", "SK"} // Matiks-related prefixes
//	prefix := prefixes[rand.IntN(len(prefixes))]
//
//	// Generate the random part (6 characters)
//	randomPart := ""
//	for range 6 {
//		randomPart += string(chars[rand.IntN(len(chars))])
//	}
//
//	// Combine prefix and random part
//	return prefix + randomPart
//}
