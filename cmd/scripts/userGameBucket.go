package main

//import (
//	"context"
//	"log"
//	"math"
//	"time"
//
//	"go.mongodb.org/mongo-driver/bson"
//	"go.mongodb.org/mongo-driver/mongo"
//	"go.mongodb.org/mongo-driver/mongo/options"
//
//	"go.mongodb.org/mongo-driver/bson/primitive"
//)
//
//type ObjectID = primitive.ObjectID
//
//type UserGame struct {
//	ID         *string    `json:"id,omitempty" bson:"id,omitempty"`
//	OpponentID *ObjectID  `json:"opponentId,omitempty" bson:"opponentId,omitempty"`
//	IsWinner   *bool      `json:"isWinner,omitempty" bson:"isWinner,omitempty"`
//	ST         *time.Time `json:"sT,omitempty" bson:"sT,omitempty"`
//}
//
//type UserGameBucket struct {
//	ID        primitive.ObjectID   `json:"id" bson:"_id"`
//	UserID    primitive.ObjectID   `json:"userId" bson:"userId"`
//	BucketNum int                  `json:"bucketNum" bson:"bucketNum"`
//	NumGames  int                  `json:"numGames" bson:"numGames"`
//	StartTime time.Time            `json:"startTime" bson:"startTime"`
//	EndTime   time.Time            `json:"endTime" bson:"endTime"`
//	Games     []primitive.ObjectID `json:"games" bson:"games"`
//}
//
//const (
//	mongoURI              = "mongodb+srv://myAtlasDBUsername:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster077"
//	dbName                = "test"
//	usersCollection       = "users"
//	gameBucketsCollection = "usergames"
//	gamesPerBucket        = 100
//)
//
//func main() {
//	clientOptions := options.Client().ApplyURI(mongoURI)
//	client, err := mongo.Connect(context.TODO(), clientOptions)
//	if err != nil {
//		log.Fatalf("MongoDB connection error: %v", err)
//	}
//	defer client.Disconnect(context.TODO())
//
//	usersCol := client.Database(dbName).Collection(usersCollection)
//	gameBucketsCol := client.Database(dbName).Collection(gameBucketsCollection)
//
//	startTime := time.Now()
//	log.Println("Starting data reading and bucketing process...")
//	readStart := time.Now()
//	buckets := readUsersAndCreateBuckets(usersCol)
//	log.Printf("Data reading and bucket creation completed in %s", time.Since(readStart))
//
//	log.Println("Creating indexes...")
//	createIndexes(gameBucketsCol)
//
//	log.Println("Starting data writing process...")
//	writeStart := time.Now()
//	insertGameBuckets(gameBucketsCol, buckets)
//	log.Printf("Data writing completed in %s", time.Since(writeStart))
//
//	log.Printf("Total processing completed in %s", time.Since(startTime))
//}
//
//func readUsersAndCreateBuckets(usersCol *mongo.Collection) []mongo.WriteModel {
//	projection := bson.M{
//		"_id":         1,
//		"stats.games": 1,
//	}
//
//	findOptions := options.Find().SetProjection(projection)
//	cursor, err := usersCol.Find(context.TODO(), bson.M{}, findOptions)
//	if err != nil {
//		log.Fatalf("Error fetching users: %v", err)
//	}
//	defer cursor.Close(context.TODO())
//
//	var buckets []mongo.WriteModel
//	var users []bson.M
//
//	if err := cursor.All(context.TODO(), &users); err != nil {
//		log.Fatalf("Error decoding users: %v", err)
//	}
//
//	for _, user := range users {
//		userID, ok := user["_id"].(primitive.ObjectID)
//		if !ok {
//			log.Println("Skipping user without a valid ID")
//			continue
//		}
//
//		var games []*UserGame
//		if stats, found := user["stats"].(bson.M); found {
//			if gamesRaw, exists := stats["games"]; exists {
//				gameArray, ok := gamesRaw.(bson.A)
//				if !ok {
//					log.Println("Skipping user: 'games' field is not an array")
//				} else {
//					for _, gameEntry := range gameArray {
//						var game UserGame
//						bsonBytes, err := bson.Marshal(gameEntry)
//						if err != nil {
//							log.Println("Skipping game due to marshal error:", err)
//							continue
//						}
//						err = bson.Unmarshal(bsonBytes, &game)
//						if err != nil {
//							log.Println("Skipping game due to unmarshal error:", err)
//							continue
//						}
//						// Only add games with all required fields
//						if game.ID != nil {
//							games = append(games, &game)
//						}
//					}
//				}
//			}
//		}
//
//		bucketModels := createBucketsForUser(userID, games)
//		buckets = append(buckets, bucketModels...)
//	}
//	return buckets
//}
//
//func createBucketsForUser(userID primitive.ObjectID, games []*UserGame) []mongo.WriteModel {
//	var bucketModels []mongo.WriteModel
//
//	if len(games) == 0 {
//		return bucketModels
//	}
//
//	bucketCount := int(math.Ceil(float64(len(games)) / float64(gamesPerBucket)))
//
//	for i := range bucketCount {
//		startIdx := i * gamesPerBucket
//		endIdx := startIdx + gamesPerBucket
//		endIdx = min(endIdx, len(games))
//
//		bucketGames := games[startIdx:endIdx]
//
//		startTime := bucketGames[0].ST
//		endTime := bucketGames[0].ST
//		gameIDs := make([]primitive.ObjectID, 0, len(bucketGames))
//
//		for _, game := range bucketGames {
//			if game.ST.Before(*startTime) {
//				startTime = game.ST
//			}
//			if game.ST.After(*endTime) {
//				endTime = game.ST
//			}
//			gameObjID, err := primitive.ObjectIDFromHex(*game.ID)
//			if err != nil {
//				log.Printf("Skipping invalid game ID: %v", err)
//				continue
//			}
//			gameIDs = append(gameIDs, gameObjID)
//		}
//
//		bucket := UserGameBucket{
//			ID:        primitive.NewObjectID(),
//			UserID:    userID,
//			BucketNum: i + 1,
//			NumGames:  len(gameIDs),
//			StartTime: *startTime,
//			EndTime:   *endTime,
//			Games:     gameIDs,
//		}
//
//		model := mongo.NewInsertOneModel().SetDocument(bucket)
//		bucketModels = append(bucketModels, model)
//	}
//
//	return bucketModels
//}
//
//func createIndexes(gameBucketsCol *mongo.Collection) {
//	userBucketIndex := mongo.IndexModel{
//		Keys:    bson.D{{Key: "userId", Value: 1}, {Key: "bucketNum", Value: 1}},
//		Options: options.Index().SetUnique(true),
//	}
//
//	timeRangeIndex := mongo.IndexModel{
//		Keys: bson.D{{Key: "userId", Value: 1}, {Key: "startTime", Value: 1}, {Key: "endTime", Value: 1}},
//	}
//
//	_, err := gameBucketsCol.Indexes().CreateMany(context.TODO(), []mongo.IndexModel{userBucketIndex, timeRangeIndex})
//	if err != nil {
//		log.Fatalf("Failed to create indexes: %v", err)
//	}
//	log.Println("Successfully created indexes")
//}
//
//func insertGameBuckets(gameBucketsCol *mongo.Collection, buckets []mongo.WriteModel) {
//	if len(buckets) > 0 {
//		result, err := gameBucketsCol.BulkWrite(context.TODO(), buckets)
//		if err != nil {
//			log.Fatalf("Bulk insert error: %v", err)
//		}
//		log.Printf("Successfully inserted %d game buckets containing multiple games each.", result.InsertedCount)
//	} else {
//		log.Println("No game buckets found to insert.")
//	}
//}
