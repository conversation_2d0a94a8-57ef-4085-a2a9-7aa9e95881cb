package main

//import (
//	"context"
//	"fmt"
//	"log"
//	"math"
//	"sort"
//	"sync"
//	"time"
//
//	"github.com/spf13/viper"
//	"go.mongodb.org/mongo-driver/bson"
//	"go.mongodb.org/mongo-driver/bson/primitive"
//	"go.mongodb.org/mongo-driver/mongo"
//	"go.mongodb.org/mongo-driver/mongo/options"
//	"go.mongodb.org/mongo-driver/mongo/readpref"
//
//	"matiksOfficial/matiks-server-go/internal/constants"
//	"matiksOfficial/matiks-server-go/internal/models"
//)
//
//// Game models
//type Player struct {
//	UserID primitive.ObjectID `bson:"userId"`
//	Rating *int               `bson:"rating"`
//}
//
//type LeaderBoardEntry struct {
//	UserID            *primitive.ObjectID `bson:"userId"`
//	RatingChange      *int                `bson:"ratingChange"`
//	StatikCoinsEarned *int                `bson:"statikCoinsEarned"`
//}
//
//type Game struct {
//	ID          primitive.ObjectID `bson:"_id"`
//	GameType    string             `bson:"gameType"`
//	GameStatus  string             `bson:"gameStatus"`
//	UpdatedAt   *time.Time         `bson:"updatedAt"`
//	StartTime   *time.Time         `bson:"startTime"`
//	Players     []Player           `bson:"players"`
//	LeaderBoard []LeaderBoardEntry `bson:"leaderBoard"`
//}
//
//// Rating history models
//type RatingHistory struct {
//	Date          time.Time `json:"date" bson:"date"`
//	InitialRating int       `json:"initialRating" bson:"initialRating"`
//	RatingChange  int       `json:"ratingChange" bson:"ratingChange"`
//	XpGained      int       `json:"xpGained" bson:"xpGained"`
//}
//
//type UserRatingHistory struct {
//	ID            primitive.ObjectID `json:"_id" bson:"_id"`
//	UserID        primitive.ObjectID `json:"userId" bson:"userId"`
//	BucketNum     int                `json:"bucketNum" bson:"bucketNum"`
//	NumHistory    int                `json:"numHistory" bson:"numHistory"`
//	RatingType    string             `json:"ratingType" bson:"ratingType"`
//	RatingHistory []RatingHistory    `json:"ratingHistory" bson:"ratingHistory"`
//}
//
//type Stats2 struct {
//	UsersProcessed       int
//	UserBatchesProcessed int
//	GamesProcessed       int
//	BucketsCreated       int
//	BulkWritesPerformed  int
//	Errors               int
//	mutex                sync.Mutex
//}
//
//func (s *Stats2) IncrementUsers(count int) {
//	s.mutex.Lock()
//	defer s.mutex.Unlock()
//	s.UsersProcessed += count
//}
//
//func (s *Stats2) IncrementUserBatches() {
//	s.mutex.Lock()
//	defer s.mutex.Unlock()
//	s.UserBatchesProcessed++
//}
//
//func (s *Stats2) IncrementGames(count int) {
//	s.mutex.Lock()
//	defer s.mutex.Unlock()
//	s.GamesProcessed += count
//}
//
//func (s *Stats2) IncrementBuckets(count int) {
//	s.mutex.Lock()
//	defer s.mutex.Unlock()
//	s.BucketsCreated += count
//}
//
//func (s *Stats2) IncrementBulkWrites() {
//	s.mutex.Lock()
//	defer s.mutex.Unlock()
//	s.BulkWritesPerformed++
//}
//
//func (s *Stats2) IncrementErrors() {
//	s.mutex.Lock()
//	defer s.mutex.Unlock()
//	s.Errors++
//}
//
//func (s *Stats2) String() string {
//	s.mutex.Lock()
//	defer s.mutex.Unlock()
//	return fmt.Sprintf("Users processed: %d, User batches: %d, Games processed: %d, Buckets created: %d, Bulk writes: %d, Errors: %d",
//		s.UsersProcessed, s.UserBatchesProcessed, s.GamesProcessed, s.BucketsCreated, s.BulkWritesPerformed, s.Errors)
//}
//
//const (
//	usersCollection         = "users"
//	userGamesCollection     = "usergames"
//	gamesCollection         = "games"
//	ratingHistoryCollection = "userRatingHistory"
//	userBatchSize           = 100
//	historyPerBucket        = 100
//	bulkWriteBatchSize      = 100
//	gameStatusEnded         = "ENDED"
//	numWorkers2             = 5
//	dateFormatLayout        = "2006-01-02"
//	testMode                = false // Set to true to run in test mode (no DB writes)
//)
//
//func main() {
//	// Configure Viper to read from .env file
//	viper.SetConfigFile(".env")
//
//	// Read the config file
//	err := viper.ReadInConfig()
//	if err != nil {
//		log.Fatalf("Error reading config file: %v", err)
//	}
//
//	// Configure Viper to also read environment variables
//	viper.AutomaticEnv()
//
//	// Get MongoDB URI and database name from environment variables
//	mongoURI := viper.GetString("MONGODB_URI")
//	if mongoURI == "" {
//		log.Fatalf("MONGODB_URI environment variable not set")
//	}
//
//	dbName := viper.GetString("MONGODB_NAME")
//	if dbName == "" {
//		log.Fatalf("MONGODB_NAME environment variable not set")
//	}
//
//	fmt.Printf("Using database: %s\n", dbName)
//
//	// Connect to MongoDB
//	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
//	defer cancel()
//
//	client, err := mongo.Connect(ctx, options.Client().ApplyURI(mongoURI))
//	if err != nil {
//		log.Fatalf("Failed to connect to MongoDB: %v", err)
//	}
//	defer client.Disconnect(ctx)
//
//	// Ping the MongoDB server to verify connection
//	err = client.Ping(ctx, readpref.Primary())
//	if err != nil {
//		log.Fatalf("Failed to ping MongoDB: %v", err)
//	}
//
//	log.Println("Connected to MongoDB.")
//
//	// Get database and collections
//	db := client.Database(dbName)
//	usersCol := db.Collection(usersCollection)
//	userGamesCol := db.Collection(userGamesCollection)
//	gamesCol := db.Collection(gamesCollection)
//	ratingHistoryCol := db.Collection(ratingHistoryCollection)
//
//	// Create indexes before processing
//	log.Println("Creating indexes...")
//	indexStart := time.Now()
//	createIndexesRatingHistory(ratingHistoryCol)
//	log.Printf("Index creation completed in %s", time.Since(indexStart))
//
//	// Initialize stats
//	stats := &Stats2{}
//
//	startTime := time.Now()
//
//	// Process users in batches
//	log.Println("Starting user batch processing...")
//	processAllUsers(context.Background(), usersCol, userGamesCol, gamesCol, ratingHistoryCol, stats)
//
//	log.Printf("Total processing completed in %s. %s", time.Since(startTime), stats)
//}
//
//// Creates necessary indexes on the target collection
//func createIndexesRatingHistory(ratingHistoryCol *mongo.Collection) {
//	log.Println("Creating indexes for UserRatingHistory collection...")
//	indexStartTime := time.Now()
//
//	// Check if collection exists first
//	db := ratingHistoryCol.Database()
//	collections, err := db.ListCollectionNames(context.TODO(), bson.M{"name": ratingHistoryCollection})
//	if err != nil {
//		log.Printf("Error checking if collection exists: %v", err)
//	}
//
//	// If collection doesn't exist, create it with a dummy document and then delete it
//	collectionExists := false
//	for _, name := range collections {
//		if name == ratingHistoryCollection {
//			collectionExists = true
//			break
//		}
//	}
//
//	if !collectionExists {
//		log.Println("Collection doesn't exist yet, creating it...")
//		// Insert a dummy document to create the collection
//		dummyDoc := UserRatingHistory{
//			ID:            primitive.NewObjectID(),
//			UserID:        primitive.NewObjectID(),
//			BucketNum:     0,
//			NumHistory:    0,
//			RatingType:    "DUMMY",
//			RatingHistory: []RatingHistory{},
//		}
//
//		_, err := ratingHistoryCol.InsertOne(context.TODO(), dummyDoc)
//		if err != nil {
//			log.Printf("Error creating collection with dummy document: %v", err)
//		} else {
//			// Delete the dummy document
//			_, err = ratingHistoryCol.DeleteOne(context.TODO(), bson.M{"_id": dummyDoc.ID})
//			if err != nil {
//				log.Printf("Error deleting dummy document: %v", err)
//			}
//		}
//	} else {
//		log.Println("Collection already exists")
//	}
//
//	// Create indexes
//	indexModels := []mongo.IndexModel{
//		{
//			Keys:    bson.D{{Key: "userId", Value: 1}, {Key: "ratingType", Value: 1}, {Key: "bucketNum", Value: 1}},
//			Options: options.Index().SetUnique(true),
//		},
//		{
//			Keys: bson.D{
//				{Key: "userId", Value: 1},
//				{Key: "ratingType", Value: 1},
//				{Key: "ratingHistory.date", Value: 1},
//			},
//		},
//	}
//
//	_, err = ratingHistoryCol.Indexes().CreateMany(context.TODO(), indexModels)
//	if err != nil {
//		log.Printf("Warning: Failed to create indexes (may already exist): %v", err)
//	} else {
//		log.Println("Successfully created/ensured indexes.")
//	}
//	log.Printf("Index creation completed in %s", time.Since(indexStartTime))
//}
//
//// Process all users in batches
//func processAllUsers(
//	ctx context.Context,
//	usersCol *mongo.Collection,
//	userGamesCol *mongo.Collection,
//	gamesCol *mongo.Collection,
//	ratingHistoryCol *mongo.Collection,
//	stats *Stats2,
//) {
//	var lastUserID primitive.ObjectID
//
//	// Create a worker pool
//	jobs := make(chan []primitive.ObjectID, 5)
//	wg := &sync.WaitGroup{}
//
//	// Start workers
//	for i := 0; i < numWorkers2; i++ {
//		wg.Add(1)
//		go func() {
//			defer wg.Done()
//			for userIDs := range jobs {
//				processUserBatch(ctx, userIDs, userGamesCol, gamesCol, ratingHistoryCol, stats)
//			}
//		}()
//	}
//
//	// Count total users
//	totalUsers, err := usersCol.CountDocuments(ctx, bson.M{})
//	if err != nil {
//		log.Fatalf("Error counting users: %v", err)
//	}
//	log.Printf("Total users to process: %d", totalUsers)
//
//	for {
//		// Find users in batches
//		filter := bson.M{}
//		if !lastUserID.IsZero() {
//			filter["_id"] = bson.M{"$gt": lastUserID}
//		}
//
//		opts := options.Find().SetLimit(int64(userBatchSize)).SetSort(bson.M{"_id": 1})
//		cursor, err := usersCol.Find(ctx, filter, opts)
//		if err != nil {
//			log.Printf("ERROR: Failed to fetch users cursor: %v\n", err)
//			break
//		}
//
//		// Extract just the user IDs
//		var users []struct {
//			ID primitive.ObjectID `bson:"_id"`
//		}
//		if err = cursor.All(ctx, &users); err != nil {
//			log.Printf("ERROR: Failed to decode users: %v\n", err)
//			cursor.Close(ctx)
//			break
//		}
//		cursor.Close(ctx)
//
//		if len(users) == 0 {
//			break // No more users to process
//		}
//
//		// Extract user IDs
//		userIDs := make([]primitive.ObjectID, len(users))
//		for i, user := range users {
//			userIDs[i] = user.ID
//			lastUserID = user.ID // Update last user ID for next batch
//		}
//
//		// Send user batch to workers
//		jobs <- userIDs
//		stats.IncrementUserBatches()
//		stats.IncrementUsers(len(userIDs))
//
//		log.Printf("Queued batch of %d users. Progress: %d/%d (%.2f%%). %s",
//			len(userIDs), stats.UsersProcessed, totalUsers, float64(stats.UsersProcessed)/float64(totalUsers)*100, stats)
//	}
//
//	// Close the jobs channel to signal workers to exit
//	close(jobs)
//
//	// Wait for all workers to finish
//	wg.Wait()
//}
//
//// Process a batch of users
//func processUserBatch(
//	ctx context.Context,
//	userIDs []primitive.ObjectID,
//	userGamesCol *mongo.Collection,
//	gamesCol *mongo.Collection,
//	ratingHistoryCol *mongo.Collection,
//	stats *Stats2,
//) {
//	log.Printf("Processing batch of %d users...", len(userIDs))
//	batchStartTime := time.Now()
//
//	// Get all game buckets for these users
//	gameBuckets, err := fetchUserGameBuckets(ctx, userGamesCol, userIDs)
//	if err != nil {
//		log.Printf("ERROR: Failed to fetch game buckets for user batch: %v", err)
//		stats.IncrementErrors()
//		return
//	}
//
//	if len(gameBuckets) == 0 {
//		log.Printf("No game buckets found for this user batch, skipping.")
//		return
//	}
//
//	log.Printf("Found %d game buckets for %d users", len(gameBuckets), len(userIDs))
//
//	// Extract all game IDs from the buckets
//	var allGameIDs []primitive.ObjectID
//	userGameMap := make(map[primitive.ObjectID][]primitive.ObjectID)
//
//	for _, bucket := range gameBuckets {
//		if _, ok := userGameMap[bucket.UserID]; !ok {
//			userGameMap[bucket.UserID] = []primitive.ObjectID{}
//		}
//		userGameMap[bucket.UserID] = append(userGameMap[bucket.UserID], bucket.Games...)
//		allGameIDs = append(allGameIDs, bucket.Games...)
//	}
//
//	// Fetch all games in bulk
//	games, err := fetchGamesInBulk(ctx, gamesCol, allGameIDs)
//	if err != nil {
//		log.Printf("ERROR: Failed to fetch games for user batch: %v", err)
//		stats.IncrementErrors()
//		return
//	}
//
//	stats.IncrementGames(len(games))
//	log.Printf("Fetched %d games for processing", len(games))
//
//	// Process games for each user
//	var allBucketModels []mongo.WriteModel
//	bucketsCreated := 0
//	usersWithGames := 0
//
//	for userID, gameIDs := range userGameMap {
//		// Filter games for this user
//		userGames := filterGamesForUser(games, userID, gameIDs)
//
//		if len(userGames) == 0 {
//			continue
//		}
//
//		usersWithGames++
//
//		// Process games and create rating history buckets
//		bucketModels, userBucketsCreated := processUserGames(ctx, userID, userGames, ratingHistoryCol)
//		allBucketModels = append(allBucketModels, bucketModels...)
//		bucketsCreated += userBucketsCreated
//
//		// Write buckets in batches to avoid memory issues
//		if len(allBucketModels) >= bulkWriteBatchSize {
//			writeBucketBatch(ctx, ratingHistoryCol, allBucketModels, stats)
//			allBucketModels = []mongo.WriteModel{} // Clear after writing
//		}
//	}
//
//	// Write any remaining buckets
//	if len(allBucketModels) > 0 {
//		writeBucketBatch(ctx, ratingHistoryCol, allBucketModels, stats)
//	}
//
//	stats.IncrementBuckets(bucketsCreated)
//	log.Printf("Batch processing completed in %s. Created %d buckets for %d users with games.",
//		time.Since(batchStartTime), bucketsCreated, usersWithGames)
//}
//
//// Fetch all game buckets for a batch of users
//func fetchUserGameBuckets(
//	ctx context.Context,
//	userGamesCol *mongo.Collection,
//	userIDs []primitive.ObjectID,
//) ([]*models.UserGameBucket, error) {
//	log.Printf("Fetching game buckets for %d users...", len(userIDs))
//
//	// Log the first few user IDs for debugging
//	if len(userIDs) > 0 {
//		logUserIDs := userIDs
//		if len(logUserIDs) > 5 {
//			logUserIDs = logUserIDs[:5]
//		}
//		for i, id := range logUserIDs {
//			log.Printf("User ID %d: %s", i+1, id.Hex())
//		}
//	}
//
//	filter := bson.M{"userId": bson.M{"$in": userIDs}}
//	log.Printf("Executing find with filter: %v", filter)
//	cursor, err := userGamesCol.Find(ctx, filter)
//	if err != nil {
//		return nil, fmt.Errorf("failed to fetch game buckets: %w", err)
//	}
//	defer cursor.Close(ctx)
//
//	var buckets []*models.UserGameBucket
//	if err = cursor.All(ctx, &buckets); err != nil {
//		return nil, fmt.Errorf("failed to decode game buckets: %w", err)
//	}
//
//	return buckets, nil
//}
//
//// Fetch games in bulk
//func fetchGamesInBulk(
//	ctx context.Context,
//	gamesCol *mongo.Collection,
//	gameIDs []primitive.ObjectID,
//) ([]*Game, error) {
//	if len(gameIDs) == 0 {
//		return []*Game{}, nil
//	}
//
//	// Create filter for bulk fetch
//	filter := bson.M{
//		"_id":        bson.M{"$in": gameIDs},
//		"gameStatus": gameStatusEnded,
//	}
//
//	// Fetch games
//	cursor, err := gamesCol.Find(ctx, filter)
//	if err != nil {
//		return nil, fmt.Errorf("failed to fetch games: %w", err)
//	}
//	defer cursor.Close(ctx)
//
//	var games []*Game
//	if err = cursor.All(ctx, &games); err != nil {
//		return nil, fmt.Errorf("failed to decode games: %w", err)
//	}
//
//	return games, nil
//}
//
//// Filter games for a specific user
//func filterGamesForUser(
//	allGames []*Game,
//	userID primitive.ObjectID,
//	userGameIDs []primitive.ObjectID,
//) []*Game {
//	// Create a map for quick lookup of game IDs
//	gameIDMap := make(map[string]bool)
//	for _, id := range userGameIDs {
//		gameIDMap[id.Hex()] = true
//	}
//
//	// Filter games that belong to this user
//	var userGames []*Game
//	for _, game := range allGames {
//		if gameIDMap[game.ID.Hex()] {
//			// Verify user is a player in this game
//			isPlayer := false
//			for _, player := range game.Players {
//				if player.UserID == userID {
//					isPlayer = true
//					break
//				}
//			}
//			if isPlayer {
//				userGames = append(userGames, game)
//			}
//		}
//	}
//
//	return userGames
//}
//
//// Process games for a user and create rating history buckets
//func processUserGames(
//	ctx context.Context,
//	userID primitive.ObjectID,
//	games []*Game,
//	ratingHistoryCol *mongo.Collection,
//) ([]mongo.WriteModel, int) {
//	log.Printf("Processing %d games for user %s", len(games), userID.Hex())
//	// Track daily data for each rating type
//	type TempDailyData struct {
//		InitialRating *int
//		RatingChange  int
//		XpGained      int
//		Date          time.Time
//	}
//
//	// Maps to track user data by rating type and date
//	tempDailyAggregates := make(map[string]map[string]*TempDailyData)
//
//	// Process each game
//	for _, game := range games {
//		ratingType := getRatingTypeForGame(game.GameType)
//		if ratingType == "" {
//			fmt.Println("No rating type for game ", game.GameType)
//			continue
//		}
//
//		// Use UpdatedAt if available, otherwise try StartTime
//		if game.UpdatedAt == nil && game.StartTime == nil {
//			fmt.Println("Game has no date information, skipping: ", game.ID.Hex())
//			continue
//		}
//
//		var gameDate time.Time
//		if game.UpdatedAt != nil {
//			gameDate = *game.UpdatedAt
//		} else {
//			gameDate = *game.StartTime
//		}
//
//		gameDateUTC := time.Date(gameDate.Year(), gameDate.Month(), gameDate.Day(), 0, 0, 0, 0, time.UTC)
//		dateStr := gameDateUTC.Format(dateFormatLayout)
//
//		// Find player data for this user
//		var playerRating *int
//		for _, player := range game.Players {
//			if player.UserID == userID {
//				playerRating = player.Rating
//				break
//			}
//		}
//
//		if playerRating == nil {
//			continue // Skip if player rating not found
//		}
//
//		// Initialize rating type map if needed
//		if _, ok := tempDailyAggregates[ratingType]; !ok {
//			tempDailyAggregates[ratingType] = make(map[string]*TempDailyData)
//		}
//
//		// Initialize date entry if needed
//		if _, ok := tempDailyAggregates[ratingType][dateStr]; !ok {
//			tempDailyAggregates[ratingType][dateStr] = &TempDailyData{
//				InitialRating: playerRating,
//				Date:          gameDateUTC,
//			}
//		}
//
//		// Find and add rating change and XP from leaderboard
//		tempData := tempDailyAggregates[ratingType][dateStr]
//		ratingChange, xpGained := findLeaderboardData(game.LeaderBoard, userID)
//		tempData.RatingChange += ratingChange
//		tempData.XpGained += xpGained
//	}
//
//	// Create buckets for each rating type
//	var bucketModels []mongo.WriteModel
//	bucketsCreated := 0
//
//	// Check if user already has buckets
//	existingBuckets, err := getUserBucketCounts(ctx, ratingHistoryCol, userID)
//	if err != nil {
//		log.Printf("ERROR: Failed to get existing bucket counts for user %s: %v", userID.Hex(), err)
//		existingBuckets = make(map[string]int)
//	}
//
//	// Convert daily data to rating history and create buckets
//	log.Printf("Found data for %d rating types for user %s", len(tempDailyAggregates), userID.Hex())
//	for ratingType, datesMap := range tempDailyAggregates {
//		log.Printf("Processing rating type %s with %d dates for user %s", ratingType, len(datesMap), userID.Hex())
//		// Convert to rating history entries
//		var dailySummaries []RatingHistory
//		for _, tempData := range datesMap {
//			if tempData.InitialRating == nil {
//				continue // Skip if initial rating was never found
//			}
//
//			dailySummary := RatingHistory{
//				Date:          tempData.Date,
//				InitialRating: *tempData.InitialRating,
//				RatingChange:  tempData.RatingChange,
//				XpGained:      tempData.XpGained,
//			}
//			dailySummaries = append(dailySummaries, dailySummary)
//		}
//
//		// Sort by date
//		sort.SliceStable(dailySummaries, func(i, j int) bool {
//			return dailySummaries[i].Date.Before(dailySummaries[j].Date)
//		})
//
//		// Get current bucket number for this rating type
//		currentBucketNum := existingBuckets[ratingType] + 1
//
//		// Create buckets with max historyPerBucket entries
//		numBuckets := int(math.Ceil(float64(len(dailySummaries)) / float64(historyPerBucket)))
//		for i := 0; i < numBuckets; i++ {
//			startIdx := i * historyPerBucket
//			endIdx := startIdx + historyPerBucket
//			if endIdx > len(dailySummaries) {
//				endIdx = len(dailySummaries)
//			}
//
//			bucketHistory := dailySummaries[startIdx:endIdx]
//			if len(bucketHistory) == 0 {
//				continue
//			}
//
//			bucket := UserRatingHistory{
//				ID:            primitive.NewObjectID(),
//				UserID:        userID,
//				BucketNum:     currentBucketNum + i,
//				NumHistory:    len(bucketHistory),
//				RatingType:    ratingType,
//				RatingHistory: bucketHistory,
//			}
//
//			model := mongo.NewInsertOneModel().SetDocument(bucket)
//			bucketModels = append(bucketModels, model)
//			bucketsCreated++
//		}
//	}
//
//	log.Printf("Created %d buckets for user %s across %d rating types",
//		bucketsCreated, userID.Hex(), len(tempDailyAggregates))
//	return bucketModels, bucketsCreated
//}
//
//// Get existing bucket counts for a user
//func getUserBucketCounts(
//	ctx context.Context,
//	ratingHistoryCol *mongo.Collection,
//	userID primitive.ObjectID,
//) (map[string]int, error) {
//	// Aggregate to get max bucket number for each rating type
//	pipeline := mongo.Pipeline{
//		{{"$match", bson.M{"userId": userID}}},
//		{{"$group", bson.M{
//			"_id":          "$ratingType",
//			"maxBucketNum": bson.M{"$max": "$bucketNum"},
//		}}},
//	}
//
//	cursor, err := ratingHistoryCol.Aggregate(ctx, pipeline)
//	if err != nil {
//		return nil, fmt.Errorf("failed to aggregate bucket counts: %w", err)
//	}
//	defer cursor.Close(ctx)
//
//	type BucketCount struct {
//		RatingType   string `bson:"_id"`
//		MaxBucketNum int    `bson:"maxBucketNum"`
//	}
//
//	var results []BucketCount
//	if err = cursor.All(ctx, &results); err != nil {
//		return nil, fmt.Errorf("failed to decode bucket counts: %w", err)
//	}
//
//	// Convert to map
//	bucketCounts := make(map[string]int)
//	for _, result := range results {
//		bucketCounts[result.RatingType] = result.MaxBucketNum
//	}
//
//	return bucketCounts, nil
//}
//
//// Write a batch of buckets to the database
//func writeBucketBatch(
//	ctx context.Context,
//	ratingHistoryCol *mongo.Collection,
//	bucketModels []mongo.WriteModel,
//	stats *Stats2,
//) {
//	if len(bucketModels) == 0 {
//		return
//	}
//
//	if testMode {
//		// In test mode, just log what would be written
//		log.Printf("TEST MODE: Would write %d buckets to database", len(bucketModels))
//		stats.IncrementBulkWrites()
//		return
//	}
//
//	// In normal mode, actually write to the database
//	writeStartTime := time.Now()
//	log.Printf("Writing %d buckets to database...", len(bucketModels))
//
//	// Check if buckets already exist and filter them out
//	var filteredModels []mongo.WriteModel
//	for _, model := range bucketModels {
//		if insertModel, ok := model.(*mongo.InsertOneModel); ok {
//			if doc, ok := insertModel.Document.(UserRatingHistory); ok {
//				// Check if this bucket already exists
//				filter := bson.M{
//					"userId":     doc.UserID,
//					"ratingType": doc.RatingType,
//					"bucketNum":  doc.BucketNum,
//				}
//				count, err := ratingHistoryCol.CountDocuments(ctx, filter)
//				if err != nil {
//					log.Printf("Error checking if bucket exists: %v", err)
//					continue
//				}
//
//				if count == 0 {
//					// Bucket doesn't exist, add it to the list
//					filteredModels = append(filteredModels, model)
//				} else {
//					log.Printf("Skipping bucket %d for user %s, rating type %s - already exists",
//						doc.BucketNum, doc.UserID.Hex(), doc.RatingType)
//				}
//			}
//		}
//	}
//
//	// If we have no buckets to write, skip
//	if len(filteredModels) == 0 {
//		log.Printf("No new buckets to write after filtering")
//		return
//	}
//
//	opts := options.BulkWrite().SetOrdered(false) // Allow partial success
//	result, err := ratingHistoryCol.BulkWrite(ctx, filteredModels, opts)
//	if err != nil {
//		log.Printf("ERROR: Failed to bulk insert buckets: %v", err)
//		stats.IncrementErrors()
//		return
//	}
//
//	writeTime := time.Since(writeStartTime)
//	log.Printf("Successfully wrote %d buckets in %s (%.2f documents/second)",
//		result.InsertedCount, writeTime, float64(result.InsertedCount)/writeTime.Seconds())
//	stats.IncrementBulkWrites()
//}
//
//// Get rating type for a game
//func getRatingTypeForGame(gameType string) string {
//	// Handle DMAS game type
//	if gameType == "DMAS" || gameType == "ONLINE_CHALLENGE" || gameType == "GROUP_PLAY" {
//		return "BLITZ"
//	} else if gameType == "PUZZLE" {
//		return "PUZZLE"
//	}
//
//	switch constants.GameType(gameType) {
//	case constants.GameType(constants.ActivityTypePlayOnline),
//		constants.GameType(constants.ActivityTypeFastestFinger),
//		constants.GameType(constants.ActivityTypePlayWithFriend):
//		return "BLITZ"
//	case constants.GameType(constants.ActivityTypeAbilityDuels):
//		return "CLASSIC"
//	case constants.GameType(constants.ActivityTypeFlashAnzan):
//		return "MEMORY"
//	case constants.GameType(constants.ActivityTypeCrossMathPuzzleDuel),
//		constants.GameType(constants.ActivityTypeCrossMathPuzzleWithFriend):
//		return "PUZZLE"
//	case constants.GameType(constants.ActivityTypePracticeOnline),
//		constants.GameType(constants.ActivityTypeSumdayShowdown),
//		constants.GameType(constants.ActivityTypeGroupPlay):
//		return ""
//	default:
//		return ""
//	}
//}
//
//// Find leaderboard data for a user
//func findLeaderboardData(leaderboard []LeaderBoardEntry, userID primitive.ObjectID) (ratingChange, xpGained int) {
//	for _, entry := range leaderboard {
//		if entry.UserID != nil && *entry.UserID == userID {
//			if entry.RatingChange != nil {
//				ratingChange = *entry.RatingChange
//			}
//			if entry.StatikCoinsEarned != nil {
//				xpGained = *entry.StatikCoinsEarned
//			}
//			return
//		}
//	}
//	return 0, 0
//}
