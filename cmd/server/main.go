package main

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	"matiksOfficial/matiks-server-go/internal/api"
	"matiksOfficial/matiks-server-go/internal/di"
	"matiksOfficial/matiks-server-go/internal/graph/resolvers"
	"matiksOfficial/matiks-server-go/pkg/config"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/pkg/metrics"

	"go.uber.org/fx"
	"go.uber.org/fx/fxevent"
	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/constants"

	"cloud.google.com/go/profiler"
	cloudtrace "github.com/GoogleCloudPlatform/opentelemetry-operations-go/exporter/trace"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.24.0"
)

func initProfiler(ctx context.Context, config *config.Config) error {
	projectID := config.GoogleCloudProject
	serviceName := constants.DevServiceName
	if config.Environment == constants.ProdEnvironment {
		serviceName = constants.ProdServiceName
	}

	cfg := profiler.Config{
		Service:           serviceName,
		ServiceVersion:    constants.Version,
		ProjectID:         projectID,
		EnableOCTelemetry: true,
	}
	return profiler.Start(cfg)
}

func initTracer(ctx context.Context, config *config.Config) (func(), error) {
	projectID := config.GoogleCloudProject
	serviceName := constants.DevServiceName
	if config.Environment == constants.ProdEnvironment {
		serviceName = constants.ProdServiceName
	}
	// Create Google Cloud Trace exporter
	exporter, err := cloudtrace.New(
		cloudtrace.WithProjectID(projectID),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create cloud trace exporter: %v", err)
	}

	// Create resource with service information
	res, err := resource.New(ctx,
		resource.WithAttributes(
			semconv.ServiceName(serviceName),
			semconv.ServiceVersion(constants.Version),
			semconv.DeploymentEnvironment(config.Environment),
		),
	)
	if err != nil {
		return nil, err
	}

	// Configure trace provider with adaptive sampler and cloud trace exporter
	tp := sdktrace.NewTracerProvider(
		sdktrace.WithBatcher(exporter),
		sdktrace.WithResource(res),
		sdktrace.WithSampler(sdktrace.ParentBased(
			// sdktrace.TraceIDRatioBased(0.1), // Sample 10% of requests by default
			sdktrace.AlwaysSample(),
		)),
	)

	// Set global trace provider and propagator
	otel.SetTracerProvider(tp)
	otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(
		propagation.TraceContext{},
		propagation.Baggage{},
	))

	// Return cleanup function
	return func() {
		if err := tp.Shutdown(ctx); err != nil {
			zlog.Error(ctx, "Error shutting down tracer provider", err)
		}
	}, nil
}

func main() {
	fx.New(
		di.Module,
		api.Module,
		resolvers.Module,
		metrics.Module,
		fx.WithLogger(func(log *zap.Logger) fxevent.Logger {
			return &fxevent.ZapLogger{Logger: log}
		}),
		fx.Invoke(startServer),
	).Run()
}

func startServer(lc fx.Lifecycle, config *config.Config, srv *api.Server) error {
	ctx := context.Background()
	err := initProfiler(ctx, config)
	if err != nil {
		zlog.Error(ctx, "Failed to initialize profiler", err)
	}

	tracerCleanup, err := initTracer(ctx, config)
	if err != nil {
		zlog.Error(ctx, "Failed to initialize tracer", err)
	} else {
		defer tracerCleanup()
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Info(ctx, "Starting application",
				zap.String("environment", config.Environment),
				zap.String("port", config.ServerPort))
			go func() {
				if err := srv.Start(); err != nil && !errors.Is(err, http.ErrServerClosed) {
					zlog.Error(ctx, "Server failed", err)
				}
			}()
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Warn(ctx, "Shutting down server...")
			if err := srv.Shutdown(ctx); err != nil {
				zlog.Error(ctx, "Server forced to shutdown", err)
			}
			zlog.Warn(ctx, "Server exiting")
			return nil
		},
	})

	return nil
}
