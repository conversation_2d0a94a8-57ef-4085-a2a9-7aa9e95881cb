services:
  app:
    build:
      context: ..
      dockerfile: deployments/Dockerfile
    image: matiks/server:latest
    container_name: matiks-server
    ports:
      - "4000:4000"
    env_file:
      - ../.local.env
    depends_on:
      - redis
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379"
    volumes:
      - redis_data:/data
    command: [ "redis-server", "--appendonly", "yes", "--maxmemory", "128mb", "--maxmemory-policy", "allkeys-lru" ]

volumes:
  redis_data:
    driver: local
