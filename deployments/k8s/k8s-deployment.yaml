apiVersion: apps/v1
kind: Deployment
metadata:
  name: matiks-go
  annotations:
    kubernetes.io/change-cause: "Initial deployment"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: matiks-go
  template:
    metadata:
      labels:
        app: matiks-go
    spec:
      containers:
        - name: matiks-go
          image: gcr.io/matiks-go/matiks-go-image:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 4000
              name: http
          envFrom:
            - secretRef:
                name: matiks-secrets
          resources:
            requests:
              cpu: 500m
              memory: 768Mi
            limits:
              cpu: 1250m
              memory: 2048Mi
