apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: matiks-ingress
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "matiks-go-ip"
    networking.gke.io/managed-certificates: "matiks-go-certificate"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "86400"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "86400"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "86400"
spec:
  ingressClassName: gce
  rules:
    - host: server.matiks.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: matiks-go-service
                port:
                  number: 80
