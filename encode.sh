while IFS='=' read -r key value; do
    # Skip empty lines and comments
    if [[ -z "$key" || "$key" == \#* ]]; then
        continue
    fi

    # Remove carriage returns (Windows CRLF issue) and trim spaces
    value=$(echo "$value" | tr -d '\r' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

    # Remove leading and trailing quotes (but keep spaces)
    if [[ "$value" =~ ^\"(.*)\"$ ]]; then
        value="${BASH_REMATCH[1]}"
    fi

    # Base64 encode without newlines
    encoded=$(printf '%s' "$value" | base64 -w 0)

    # Preserve spaces by enclosing output in quotes
    if [[ "$value" == *" "* ]]; then
        echo "$key: \"$encoded\""
    else
        echo "$key: $encoded"
    fi
done < .prod.env
