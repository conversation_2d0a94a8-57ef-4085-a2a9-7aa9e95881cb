package api

import (
	"context"
	"net/http"
	"time"

	"matiksOfficial/matiks-server-go/internal/auth"
	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/graph/generated"
	"matiksOfficial/matiks-server-go/internal/graph/resolvers"
	internalMiddleware "matiksOfficial/matiks-server-go/internal/middleware"
	"matiksOfficial/matiks-server-go/pkg/config"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	ws "matiksOfficial/matiks-server-go/internal/infrastructure/websocket"

	"github.com/go-chi/httprate"
	"go.uber.org/fx"

	"github.com/99designs/gqlgen/graphql"
	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/handler/extension"
	"github.com/99designs/gqlgen/graphql/handler/lru"
	"github.com/99designs/gqlgen/graphql/handler/transport"
	"github.com/99designs/gqlgen/graphql/playground"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/gorilla/websocket"
	"github.com/vektah/gqlparser/v2/ast"
	"github.com/vektah/gqlparser/v2/gqlerror"

	"matiksOfficial/matiks-server-go/internal/constants"
)

type Server struct {
	router *chi.Mux
	server *http.Server
}

func NewServer(cfg *config.Config, wsServer ws.Websocket, eventsService domain.EventsStore, authService auth.AuthStore, resolver *resolvers.Resolver) *Server {
	router := chi.NewRouter()

	// Middleware
	router.Use(internalMiddleware.CorsMiddleware(cfg).Handler)
	router.Use(httprate.LimitByIP(1000, time.Minute))
	router.Use(middleware.RequestID)
	router.Use(middleware.RealIP)
	router.Use(internalMiddleware.SecurityHeaders)
	router.Use(internalMiddleware.TimeoutMiddleware(30 * time.Second))
	router.Use(internalMiddleware.AuthMiddleware(authService, cfg))
	router.Use(internalMiddleware.RequestLogger())
	router.Use(middleware.Compress(5))
	router.Use(middleware.Recoverer)

	gqlLogger := internalMiddleware.NewGraphQLLogger()
	gqlTracer := internalMiddleware.NewGraphQLTracer()

	// GraphQL setup
	gqlConfig := generated.Config{
		Resolvers: resolver,
	}
	gqlConfig.Directives.Auth = internalMiddleware.AuthDirective

	// WebSocket upgrader
	upgrader := websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	srv := handler.New(generated.NewExecutableSchema(gqlConfig))
	srv.Use(gqlLogger.AsExtension())
	srv.Use(gqlTracer.AsExtension())
	srv.SetErrorPresenter(func(ctx context.Context, e error) *gqlerror.Error {
		zlog.Error(ctx, "GraphQL error", graphql.DefaultErrorPresenter(ctx, e))
		// metrics.Get().RecordGraphQLError(ctx, )
		return graphql.DefaultErrorPresenter(ctx, e)
	})

	srv.AddTransport(transport.Websocket{
		KeepAlivePingInterval: 10 * time.Second,
		Upgrader:              upgrader,
	})

	srv.AddTransport(transport.Options{})
	srv.AddTransport(transport.GET{})
	srv.AddTransport(transport.POST{})
	srv.AddTransport(transport.MultipartForm{
		MaxMemory:     32 * (1 << 20), // 4MB
		MaxUploadSize: 32 * (1 << 20), // 4MB
	})
	srv.AddTransport(transport.GRAPHQL{})

	srv.SetQueryCache(lru.New[*ast.QueryDocument](1000))

	srv.Use(extension.AutomaticPersistedQuery{
		Cache: lru.New[string](100),
	})
	srv.Use(extension.Introspection{})

	// Routes
	router.Get("/", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})

	router.Handle("/api", srv)
	if cfg.Environment == constants.DevEnvironment {
		router.Handle("/api/playground", playground.Handler("GraphQL playground", "/api"))
	}
	// Deprecated route for subscriptions compatibility with older clients
	router.HandleFunc("/subscriptions", func(w http.ResponseWriter, r *http.Request) {
		srv.ServeHTTP(w, r)
	})

	wsServer.RegisterHandler(ws.SubmitAnswer, eventsService.SubmitAnswer)
	wsServer.RegisterHandler(ws.SubmitPuzzleGameAnswer, eventsService.SubmitPuzzleGameAnswer)
	wsServer.RegisterHandler(ws.PingPong, eventsService.PingPong)
	wsServer.RegisterHandler(ws.AddMessage, eventsService.AddMessage)
	wsServer.RegisterHandler(ws.SendMessage, eventsService.SendMessage)
	router.HandleFunc("/ws", wsServer.ServeHTTP)

	return &Server{
		router: router,
		server: &http.Server{
			Addr:    ":" + cfg.ServerPort,
			Handler: router,
		},
	}
}

func (s *Server) Start() error {
	return s.server.ListenAndServe()
}

func (s *Server) Shutdown(ctx context.Context) error {
	return s.server.Shutdown(ctx)
}

var Module = fx.Module(
	"api",
	fx.Provide(NewServer),
)
