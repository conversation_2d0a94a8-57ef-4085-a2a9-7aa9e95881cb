package auth

import (
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"

	"matiksOfficial/matiks-server-go/pkg/config"

	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
)

type AuthStore interface {
	GenerateToken(userID primitive.ObjectID) (string, error)
	VerifyToken(tokenString string) (jwt.MapClaims, error)
	HashPassword(password string) (string, error)
	ComparePasswords(hashedPassword, password string) bool
	OAuthClient() *oauth2.Config
}

type AuthService struct {
	jwtSecret   []byte
	oauthConfig *oauth2.Config
}

func NewAuthService(cfg *config.Config) AuthStore {
	return &AuthService{
		jwtSecret:   []byte(cfg.JWTSecret),
		oauthConfig: oauthClient(cfg),
	}
}

func (s *AuthService) GenerateToken(userID primitive.ObjectID) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"id":  userID,
		"exp": time.Now().Add(time.Hour * 24 * 365).Unix(), // Token expires in 365 days
	})

	return token.SignedString(s.jwtSecret)
}

func (s *AuthService) VerifyToken(tokenString string) (jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (any, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, jwt.ErrSignatureInvalid
		}
		return s.jwtSecret, nil
	})
	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

func (s *AuthService) HashPassword(password string) (string, error) {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedPassword), nil
}

func (s *AuthService) ComparePasswords(hashedPassword, password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	return err == nil
}

func (s *AuthService) OAuthClient() *oauth2.Config {
	return s.oauthConfig
}

func oauthClient(cfg *config.Config) *oauth2.Config {
	return &oauth2.Config{
		ClientID:     cfg.GoogleClientID,
		ClientSecret: cfg.GoogleClientSecret,
		RedirectURL:  "postmessage",
		Endpoint:     google.Endpoint,
	}
}
