package constants

type ContestStatusType struct {
	UPCOMING          ContestStatus
	REGISTRATION_OPEN ContestStatus
	ONGOING           ContestStatus
	ENDED             ContestStatus
	CANCELLED         ContestStatus
}

type ContestStatus string

var ContestStatusEnum = ContestStatusType{
	UPCOMING:          "UPCOMING",
	REGISTRATION_OPEN: "REGISTRATION_OPEN",
	ONGOING:           "ONGOING",
	ENDED:             "ENDED",
	CANCELLED:         "CANCELLED",
}

const Optiver80Tag = "OPTIVER80"

func (e ContestStatus) String() string {
	return string(e)
}
