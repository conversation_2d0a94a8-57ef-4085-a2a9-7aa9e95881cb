package constants

type Key string

const (
	UserContextKey  Key = "authorizationUser"
	UserAgentKey    Key = "userAgent"
	IPKey           Key = "ip"
	RequestIDKey    Key = "requestID"
	GameIDKey       Key = "gameID"
	UserTimezoneKey Key = "userTimezone"
	ServiceName     Key = "serviceName"
	SpanContextKey  Key = "spanContext"
)

func (e Key) String() string {
	return string(e)
}

var AllKeys = []Key{
	UserContextKey,
	UserAgentKey,
	IPKey,
	RequestIDKey,
	GameIDKey,
	UserTimezoneKey,
	ServiceName,
	SpanContextKey,
}
