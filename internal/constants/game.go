package constants

type (
	PlayerStatus           string
	RematchRequestGameType string
	GameStatus             string
	GameType               string
	GameEvent              string
	UserEvent              string
)

type PlayerStatusEnumStruct struct {
	INVITED  PlayerStatus
	ACCEPTED PlayerStatus
	REJECTED PlayerStatus
}

type GameStatusEnumStruct struct {
	CREATED   GameStatus
	READY     GameStatus
	STARTED   GameStatus
	PAUSED    GameStatus
	ENDED     GameStatus
	CANCELLED GameStatus
}

type GameTypeEnumStruct struct {
	PLAY_ONLINE      GameType
	PLAY_WITH_FRIEND GameType
	PRACTICE         GameType
	ONLINE_CHALLENGE GameType
	SUMDAY_SHOWDOWN  GameType
	FLASH_ANZAN      GameType
}

type GameEventEnumStruct struct {
	USER_JOINED         GameEvent
	GAME_STARTED        GameEvent
	CORRECT_MOVE_MADE   GameEvent
	INCORRECT_MOVE_MADE GameEvent
	GAME_CANCELLED      GameEvent
	GAME_ENDED          GameEvent
	PLAYER_REMOVED      GameEvent
	USER_LEFT           GameEvent
}

type RematchRequestGameTypeEnumStruct struct {
	DMAS   RematchRequestGameType
	PUZZLE RematchRequestGameType
}

type UserEventEnumStruct struct {
	USER_MATCHED   UserEvent
	SEARCH_TIMEOUT UserEvent
	// SHOWDOWN_FICTURES_CREATED UserEvent
	// SHOWDOWN_TO_START         UserEvent
	// SHOWDOWN_OPPONENT_WAITING UserEvent
}

type RematchGameEnumStruct struct {
	REMATCH_REQUESTED   UserEvent
	REMATCH_ACCEPTED    UserEvent
	REMATCH_REJECTED    UserEvent
	REMATCH_AUTO_CLOSED UserEvent
	REMATCH_CANCELLED   UserEvent
}

var PlayerStatusEnum = PlayerStatusEnumStruct{
	INVITED:  "INVITED",
	ACCEPTED: "ACCEPTED",
	REJECTED: "REJECTED",
}

var GameStatusEnum = GameStatusEnumStruct{
	CREATED:   "CREATED",
	READY:     "READY",
	STARTED:   "STARTED",
	PAUSED:    "PAUSED",
	ENDED:     "ENDED",
	CANCELLED: "CANCELLED",
}

var GameTypeEnum = GameTypeEnumStruct{
	PLAY_ONLINE:      "PLAY_ONLINE",
	PLAY_WITH_FRIEND: "PLAY_WITH_FRIEND",
	PRACTICE:         "PRACTICE",
	ONLINE_CHALLENGE: "ONLINE_CHALLENGE",
	SUMDAY_SHOWDOWN:  "SUMDAY_SHOWDOWN",
}

var GameEventEnum = GameEventEnumStruct{
	USER_JOINED:         "USER_JOINED",
	PLAYER_REMOVED:      "PLAYER_REMOVED",
	USER_LEFT:           "USER_LEFT",
	GAME_STARTED:        "GAME_STARTED",
	CORRECT_MOVE_MADE:   "CORRECT_MOVE_MADE",
	INCORRECT_MOVE_MADE: "INCORRECT_MOVE_MADE",
	GAME_CANCELLED:      "GAME_CANCELLED",
	GAME_ENDED:          "GAME_ENDED",
}

var UserEventEnum = UserEventEnumStruct{
	USER_MATCHED:   "USER_MATCHED",
	SEARCH_TIMEOUT: "SEARCH_TIMEOUT",
	// SHOWDOWN_FICTURES_CREATED: "SHOWDOWN_FICTURES_CREATED",
	// SHOWDOWN_TO_START:         "SHOWDOWN_TO_START",
	// SHOWDOWN_OPPONENT_WAITING: "SHOWDOWN_OPPONENT_WAITING",
}

var RematchGameEnum = RematchGameEnumStruct{
	REMATCH_REQUESTED:   "REMATCH_REQUESTED",
	REMATCH_ACCEPTED:    "REMATCH_ACCEPTED",
	REMATCH_REJECTED:    "REMATCH_REJECTED",
	REMATCH_AUTO_CLOSED: "REMATCH_AUTO_CLOSED",
	REMATCH_CANCELLED:   "REMATCH_CANCELLED", // Added
}

func (e PlayerStatus) String() string {
	return string(e)
}

func (e GameEvent) String() string {
	return string(e)
}

func (e GameStatus) String() string {
	return string(e)
}

func (e GameType) String() string {
	return string(e)
}

func (e UserEvent) String() string {
	return string(e)
}

const (
	TotalGamesKey = "total-games"
)

const (
	RematchRequestGameTypeDMAS   = "DMAS"
	RematchRequestGameTypePUZZLE = "PUZZLE"
)

const AutoCloseRematchRequestDuration = 10
