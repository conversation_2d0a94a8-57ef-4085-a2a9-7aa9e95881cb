package constants

type (
	QuestionType string
	QuestionTag  string
)

type QuestionTypeStruct struct {
	SINGLE_CHOICE      QuestionType
	MULTI_CHOICE       QuestionType
	FILL_IN_THE_BLANKS QuestionType
}

type QuestionTagStruct struct {
	ADDITION       QuestionTag
	SUBTRACTION    QuestionTag
	ARITHMETIC     QuestionTag
	MULTIPLICATION QuestionTag
	DIVISION       QuestionTag
	OPTIVER80      QuestionTag
}

var QuestionTypeEnum = QuestionTypeStruct{
	SINGLE_CHOICE:      "SINGLE_CHOICE",
	MULTI_CHOICE:       "MULTI_CHOICE",
	FILL_IN_THE_BLANKS: "FILL_IN_THE_BLANKS",
}

var QuestionTagEnum = QuestionTagStruct{
	ADDITION:       "ADDITION",
	SUBTRACTION:    "SUB<PERSON>ACTION",
	ARITHMETIC:     "ARITHMETIC",
	MULTIPLICATION: "MULTIPLICATION",
	DIVISION:       "DIVISION",
	OPTIVER80:      "OPTIVER80",
}

func (e QuestionType) String() string {
	return string(e)
}

func (e QuestionTag) String() string {
	return string(e)
}
