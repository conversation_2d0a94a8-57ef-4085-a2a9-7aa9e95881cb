package constants

type (
	FieldType       string
	FieldTypeStruct struct {
		TEXT          FieldType
		NUMBER        FieldType
		EMAIL         FieldType
		MOBILE        FieldType
		SINGLE_SELECT FieldType
		MULTI_SELECT  FieldType
		CHECKBOX      FieldType
		RADIO         FieldType
		IMAGE_INPUT   FieldType
		FILE_INPUT    FieldType
	}
)

var FieldTypeEnum = FieldTypeStruct{
	TEXT:          "TEXT",
	NUMBER:        "NUMBER",
	EMAIL:         "EMAIL",
	MOBILE:        "MO<PERSON><PERSON>",
	SINGLE_SELECT: "SINGLE_SELECT",
	MULTI_SELECT:  "MULTI_SELECT",
	CHECKBOX:      "CHECKBOX",
	RADIO:         "RADIO",
	IMAGE_INPUT:   "IMAGE_INPUT",
	FILE_INPUT:    "FILE_INPUT",
}

func (e FieldType) String() string {
	return string(e)
}
