package constants

const (
	SHOWDOWN_PLAYERS                = 2
	SHOWDOWN_GAME_BATCH_SIZE        = 100
	LEADERBOARD_PAGE_SIZE           = 100
	SHOWDOWN_EVENT_KEY              = "SHOWDOWN_EVENT"
	SHOWDOWN_PARTICIPANT_BATCH_SIZE = 100
)

// In quantity of 100
type SHOWDOWN_POINTS struct {
	GAME_WIN_POINTS              float64
	CONSEQUTIVE_GAME_WIN_BONUS   float64
	ROUND_WIN_POINT              float64
	CONSEQUTIVE_ROUND_WIN_BONUS  float64
	PER_QUES_CORRECT_BONUS       float64
	PER_QUES_INCORRECT_DEDUCTION float64
	PER_QUES_LEVERAGE_BONUS      float64
}

var ShowdownPoints = SHOWDOWN_POINTS{
	GAME_WIN_POINTS:              5,
	CONSEQUTIVE_GAME_WIN_BONUS:   5,
	ROUND_WIN_POINT:              100,
	CONSEQUTIVE_ROUND_WIN_BONUS:  10,
	PER_QUES_CORRECT_BONUS:       1,
	PER_QUES_INCORRECT_DEDUCTION: -1,
	PER_QUES_LEVERAGE_BONUS:      2,
}
