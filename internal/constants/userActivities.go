package constants

type ActivityType string

const (
	// daily-challenge
	ActivityTypeDailyChallenge       ActivityType = "DAILY_CHALLENGE"
	ActivityTypeDailyPuzzleCrossMath ActivityType = "DAILY_PUZZLE_CROSS_MATH"
	ActivityTypeDailyPuzzleKenKen    ActivityType = "DAILY_PUZZLE_KEN_KEN"
	ActivityTypeDailyPuzzleHectoc    ActivityType = "DAILY_PUZZLE_HECTOC"

	// puzzle-rush
	ActivityTypeCrossMathPuzzleRush ActivityType = "CROSS_MATH_PUZZLE_RUSH"

	// puzzle-games
	ActivityTypeCrossMathPuzzleDuel       ActivityType = "CROSS_MATH_PUZZLE_DUEL"
	ActivityTypeCrossMathPuzzleWithFriend ActivityType = "CROSS_MATH_PUZZLE_WITH_FRIEND"

	// math-games
	ActivityTypeFlashAnzan     ActivityType = "FLASH_ANZAN"
	ActivityTypePlayWithFriend ActivityType = "PLAY_WITH_FRIEND"
	ActivityTypePlayOnline     ActivityType = "PLAY_ONLINE"
	ActivityTypeFastestFinger  ActivityType = "FASTEST_FINGER"
	ActivityTypeSumdayShowdown ActivityType = "SUMDAY_SHOWDOWN"
	ActivityTypeGroupPlay      ActivityType = "GROUP_PLAY"
	ActivityTypeAbilityDuels   ActivityType = "ABILITY_DUELS"

	// practice
	ActivityTypePracticeOnline ActivityType = "PRACTICE"
	ActivityTypePracticeNets   ActivityType = "PRACTICE_NETS"

	// contests
	ActivityTypeContest80In8          ActivityType = "CONTEST_80_IN_8"
	ActivityTypeContestSumdayShowdown ActivityType = "CONTEST_SUMDAY_SHOWDOWN"
)

func (e ActivityType) String() string {
	return string(e)
}
