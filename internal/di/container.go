package di

import (
	"matiksOfficial/matiks-server-go/internal/auth"
	"matiksOfficial/matiks-server-go/internal/domain/announcement"
	"matiksOfficial/matiks-server-go/internal/domain/botDetection"
	"matiksOfficial/matiks-server-go/internal/domain/club"
	"matiksOfficial/matiks-server-go/internal/domain/clubAnnouncement"
	"matiksOfficial/matiks-server-go/internal/domain/clubEvent"
	"matiksOfficial/matiks-server-go/internal/domain/contest"
	"matiksOfficial/matiks-server-go/internal/domain/core"
	"matiksOfficial/matiks-server-go/internal/domain/dailyChallenge"
	"matiksOfficial/matiks-server-go/internal/domain/events"
	"matiksOfficial/matiks-server-go/internal/domain/feed"
	"matiksOfficial/matiks-server-go/internal/domain/forum"
	"matiksOfficial/matiks-server-go/internal/domain/friends"
	"matiksOfficial/matiks-server-go/internal/domain/game"
	"matiksOfficial/matiks-server-go/internal/domain/institution"
	"matiksOfficial/matiks-server-go/internal/domain/league"
	"matiksOfficial/matiks-server-go/internal/domain/messages"
	"matiksOfficial/matiks-server-go/internal/domain/notification"
	"matiksOfficial/matiks-server-go/internal/domain/puzzle"
	"matiksOfficial/matiks-server-go/internal/domain/puzzleGame"
	"matiksOfficial/matiks-server-go/internal/domain/questions/presets"
	"matiksOfficial/matiks-server-go/internal/domain/scheduler"
	"matiksOfficial/matiks-server-go/internal/domain/showdown"
	"matiksOfficial/matiks-server-go/internal/domain/streakShieldTransaction"
	"matiksOfficial/matiks-server-go/internal/domain/user"
	"matiksOfficial/matiks-server-go/internal/domain/userResolution"
	userstreak "matiksOfficial/matiks-server-go/internal/domain/userStreak"
	"matiksOfficial/matiks-server-go/internal/domain/usersettings"
	"matiksOfficial/matiks-server-go/internal/domain/weeklyLeague"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cdn"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/infrastructure/firebase"
	"matiksOfficial/matiks-server-go/internal/infrastructure/hset"
	"matiksOfficial/matiks-server-go/internal/infrastructure/leaderboard"
	"matiksOfficial/matiks-server-go/internal/infrastructure/list"
	"matiksOfficial/matiks-server-go/internal/infrastructure/locks"
	"matiksOfficial/matiks-server-go/internal/infrastructure/pubsub"
	"matiksOfficial/matiks-server-go/internal/infrastructure/queue"
	"matiksOfficial/matiks-server-go/internal/infrastructure/redis"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/infrastructure/sortedset"
	"matiksOfficial/matiks-server-go/internal/infrastructure/storage"
	"matiksOfficial/matiks-server-go/internal/infrastructure/websocket"
	"matiksOfficial/matiks-server-go/pkg/config"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

var infraModule = fx.Module(
	"infrastructure",
	fx.Provide(storage.InitializeStorageClient),
	fx.Provide(redis.NewClient),
	fx.Provide(websocket.NewServer),
	fx.Provide(cdn.NewCDNClient),
)

var leaderboardModule = fx.Module(
	"leaderboard",
	fx.Provide(leaderboard.NewRatingCounter),
	fx.Provide(leaderboard.NewGlobalLeaderboard),
)

var cachingModule = fx.Module(
	"caching",
	fx.Provide(cache.NewRedisCache),
	fx.Provide(sortedset.NewRedisSortedSet),
	fx.Provide(pubsub.NewRedisPubSub),
	fx.Provide(locks.NewRedisLock),
	fx.Provide(cache.NewRedisLeaderboardCache),
	fx.Provide(hset.NewRedisHSet),
	fx.Provide(list.NewRedisList),
	fx.Provide(queue.GetWaitingPlayersQueue),
)

var repoModule = fx.Module(
	"repository",
	fx.Provide(database.NewDatabase),
	repository.Module,
)

var domainSubModule = fx.Module(
	"domain",
	fx.Provide(
		auth.NewAuthService,
		notification.NewNotificationService,
		user.NewUserService,
		dailyChallenge.NewDailyChallengeService,
		game.NewGameService,
		contest.NewContestService,
		presets.NewPresetsService,
		friends.NewFriendAndFollowersService,
		messages.NewMessageService,
		events.NewEventsService,
		userResolution.NewUserResolutionService,
		usersettings.NewUserSettingsService,
		league.NewService,
		puzzle.NewPuzzleService,
		showdown.NewShowdownService,
		weeklyLeague.NewWeeklyLeagueService,
		puzzleGame.NewPuzzleGameService,
		club.NewClubsService,
		clubEvent.NewClubEventsService,
		clubAnnouncement.NewClubAnnouncementsService,
		forum.NewForumService,
		announcement.NewAnnouncementService,
		institution.NewInstitutionService,
		feed.NewFeedService,
		core.NewCoreService,
		userstreak.NewUserStreakService,
		streakShieldTransaction.NewStreakShieldTransactionService,
		botDetection.NewBotDetectionService,
	),
)

var Module = fx.Module(
	"container",
	infraModule,
	firebase.FirebaseAppModule,
	repoModule,
	cachingModule,
	domainSubModule,
	leaderboardModule,
	fx.Provide(
		config.Load,
		zlog.NewLogger,
	),
	fx.Invoke(scheduler.NewSchedulerService),
)
