package announcement

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *announcementService) CreateAnnouncement(ctx context.Context, input models.CreateAnnouncementInput) (*models.Announcement, error) {
	announcement := &models.Announcement{
		Type:             input.Type,
		Title:            input.Title,
		Description:      input.Description,
		ImageURL:         input.ImageURL,
		MediaURL:         input.MediaURL,
		Priority:         0,
		PublishedAt:      input.PublishedAt,
		ExpiresAt:        input.ExpiresAt,
		RiveAnimationURL: input.RiveAnimationURL,
	}

	if input.Priority != nil {
		announcement.Priority = *input.Priority
	}

	if input.Ctas != nil {
		announcement.CTAs = make([]models.CallToAction, len(input.Ctas))
		for i, ctaInput := range input.Ctas {
			if ctaInput != nil {
				announcement.CTAs[i] = models.CallToAction{
					Text:       ctaInput.Text,
					Target:     ctaInput.Target,
					ActionType: ctaInput.ActionType,
					Style:      ctaInput.Style,
				}
			}
		}
	}

	newID, err := s.announcementRepo.CreateAnnouncement(ctx, announcement)
	if err != nil {
		return nil, err
	}

	return s.announcementRepo.GetAnnouncementByID(ctx, newID)
}
