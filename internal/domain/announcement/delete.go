package announcement

import (
	"context"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *announcementService) DeleteAnnouncement(ctx context.Context, id primitive.ObjectID) (*models.AnnouncementMutationResponse, error) {
	err := s.announcementRepo.DeleteAnnouncement(ctx, id)
	if err != nil {
		return &models.AnnouncementMutationResponse{
			Success: false,
			Message: utils.AllocPtr("Failed to delete announcement"),
		}, err
	}

	// TODO: Consider deleting associated UserAnnouncementStatus entries asynchronously
	return &models.AnnouncementMutationResponse{
		Success: true,
		Message: utils.AllocPtr("Announcement deleted successfully"),
	}, nil
}
