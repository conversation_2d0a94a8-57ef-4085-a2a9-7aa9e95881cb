package announcement

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
)

func (s *announcementService) GetAnnouncementByID(ctx context.Context, id primitive.ObjectID) (*models.Announcement, error) {
	return s.announcementRepo.GetAnnouncementByID(ctx, id)
}

// GetAllAnnouncements is primarily for admin use.
func (s *announcementService) GetAllAnnouncements(ctx context.Context, limit, offset *int, announcementType *models.AnnouncementType) ([]*models.Announcement, error) {
	// Validate input parameters
	if limit == nil || *limit <= 0 {
		limit = utils.AllocPtr(50) // default limit
	}
	if *limit > 100 {
		limit = utils.AllocPtr(100) // max limit
	}
	if offset == nil || *offset < 0 {
		offset = utils.AllocPtr(0) // default offset
	}

	filter := bson.M{}
	if announcementType != nil && *announcementType != "" {
		filter["type"] = *announcementType
	}

	return s.announcementRepo.FindAnnouncements(ctx, filter, int64(*limit), int64(*offset))
}

// GetUnreadAnnouncements fetches active announcements and filters out those already read by the user.
func (s *announcementService) GetUnreadAnnouncements(ctx context.Context, limit, offset *int) ([]*models.Announcement, error) {
	// Validate input parameters
	if limit == nil || *limit <= 0 {
		limit = utils.AllocPtr(50) // default limit
	}
	if *limit > 100 {
		limit = utils.AllocPtr(100) // max limit
	}
	if offset == nil || *offset < 0 {
		offset = utils.AllocPtr(0) // default offset
	}

	userID, err := utils.GetUserFromContext(ctx)

	now := time.Now().UTC()
	activeAnnouncements, err := s.announcementRepo.FindActiveAnnouncements(ctx, now)
	if err != nil {
		return nil, err
	}

	if len(activeAnnouncements) == 0 {
		return []*models.Announcement{}, nil
	}

	readIDsMap, err := s.announcementRepo.GetReadAnnouncementIDs(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Filter out read announcements
	unreadAnnouncements := make([]*models.Announcement, 0)
	for _, ann := range activeAnnouncements {
		if _, read := readIDsMap[ann.ID]; !read {
			unreadAnnouncements = append(unreadAnnouncements, ann)
		}
	}

	// Apply pagination
	start := *offset
	end := *offset + *limit
	if start >= len(unreadAnnouncements) {
		return []*models.Announcement{}, nil
	}
	if end > len(unreadAnnouncements) {
		end = len(unreadAnnouncements)
	}

	return unreadAnnouncements[start:end], nil
}
