package announcement

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *announcementService) MarkAnnouncementAsRead(ctx context.Context, announcementID primitive.ObjectID) (*models.AnnouncementMutationResponse, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return &models.AnnouncementMutationResponse{
			Success: false,
			Message: utils.AllocPtr("Failed to get user from context"),
		}, err
	}

	err = s.announcementRepo.MarkAsRead(ctx, userID, announcementID)
	if err != nil {
		return &models.AnnouncementMutationResponse{
			Success: false,
			Message: utils.AllocPtr("Failed to mark announcement as read"),
		}, err
	}

	return &models.AnnouncementMutationResponse{
		Success: true,
		Message: utils.AllocPtr("Announcement marked as read successfully"),
	}, nil
}

func (s *announcementService) MarkAllAnnouncementsAsRead(ctx context.Context) (*models.AnnouncementMutationResponse, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return &models.AnnouncementMutationResponse{
			Success: false,
			Message: utils.AllocPtr("Failed to get user from context"),
		}, err
	}
	now := time.Now().UTC()
	activeAnnouncements, err := s.announcementRepo.FindActiveAnnouncements(ctx, now)
	if err != nil {
		return &models.AnnouncementMutationResponse{
			Success: false,
			Message: utils.AllocPtr("Failed to fetch active announcements"),
		}, err
	}

	if len(activeAnnouncements) == 0 {
		return &models.AnnouncementMutationResponse{
			Success: true,
			Message: utils.AllocPtr("No active announcements to mark as read"),
		}, nil
	}

	readIDsMap, err := s.announcementRepo.GetReadAnnouncementIDs(ctx, userID)
	if err != nil {
		return &models.AnnouncementMutationResponse{
			Success: false,
			Message: utils.AllocPtr("Failed to fetch read announcement IDs"),
		}, err
	}

	unreadIDs := make([]primitive.ObjectID, 0, len(activeAnnouncements))
	for _, ann := range activeAnnouncements {
		if _, read := readIDsMap[ann.ID]; !read {
			unreadIDs = append(unreadIDs, ann.ID)
		}
	}

	if len(unreadIDs) == 0 {
		return &models.AnnouncementMutationResponse{
			Success: true,
			Message: utils.AllocPtr("All announcements are already marked as read"),
		}, nil
	}

	err = s.announcementRepo.MarkMultipleAsRead(ctx, userID, unreadIDs)
	if err != nil {
		return &models.AnnouncementMutationResponse{
			Success: false,
			Message: utils.AllocPtr("Failed to mark announcements as read"),
		}, err
	}

	return &models.AnnouncementMutationResponse{
		Success: true,
		Message: utils.AllocPtr("All announcements marked as read successfully"),
	}, nil
}
