package announcement

import (
	"context"

	"go.uber.org/fx"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

type announcementService struct {
	userService      domain.UserStore
	userRepo         repository.UserRepository
	announcementRepo repository.AnnouncementRepository
}

// NewAnnouncementService creates a new instance of AnnouncementService.
func NewAnnouncementService(lc fx.Lifecycle, userService domain.UserStore, repositoryFactory *repository.RepositoryFactory) domain.AnnouncementStore {
	s := &announcementService{
		userService:      userService,
		userRepo:         repositoryFactory.UserRepository,
		announcementRepo: repositoryFactory.AnnouncementRepository,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting Announcement Service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down Announcement service")
			return nil
		},
	})

	return s
}
