package announcement

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *announcementService) UpdateAnnouncement(ctx context.Context, id primitive.ObjectID, input models.UpdateAnnouncementInput) (*models.Announcement, error) {
	_, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	_, err = s.announcementRepo.GetAnnouncementByID(ctx, id)

	updates := bson.M{}

	if input.Type != nil {
		updates["type"] = input.Type
	}
	if input.Title != nil {
		updates["title"] = *input.Title
	}
	if input.Description != nil {
		updates["description"] = *input.Description
	}
	if input.ImageURL != nil {
		updates["imageUrl"] = *input.ImageURL
	}
	if input.RiveAnimationURL != nil {
		updates["riveAnimationURL"] = *input.RiveAnimationURL
	}
	if input.MediaURL != nil {
		updates["mediaUrl"] = *input.MediaURL
	}
	if input.Priority != nil {
		updates["priority"] = *input.Priority
	}
	if input.PublishedAt != nil {
		updates["publishedAt"] = *input.PublishedAt
	}
	if input.ExpiresAt != nil {
		updates["expiresAt"] = *input.ExpiresAt
	}
	if input.Ctas != nil {
		dbCTAs := make([]models.CallToAction, 0, len(input.Ctas))
		for _, ctaInput := range input.Ctas {
			if ctaInput != nil {
				dbCTAs = append(dbCTAs, models.CallToAction{
					Text:       ctaInput.Text,
					Target:     ctaInput.Target,
					ActionType: ctaInput.ActionType,
					Style:      ctaInput.Style,
				})
			}
		}
		updates["ctas"] = dbCTAs
	}

	if len(updates) == 0 {
		return s.announcementRepo.GetAnnouncementByID(ctx, id)
	}

	err = s.announcementRepo.UpdateAnnouncement(ctx, id, updates)
	if err != nil {
		return nil, err
	}

	return s.announcementRepo.GetAnnouncementByID(ctx, id)
}
