package analyzer

import (
	"math"
	"sort"
	"time"

	"matiksOfficial/matiks-server-go/internal/models"

	"github.com/montanaflynn/stats"
)

const (
	RapidSubmissionThreshold = 300 // in ms - submissions faster than this is suspicious
	MinTimeDiffThreshold     = 400 // in ms - consecutive submissions should be at least this far apart

	FixedTimeVarianceThreshold = 300 // in ms - variance below this suggests automated timing
	StandardDeviationThreshold = 100 // in ms - standard deviation below this suggests a pattern
	MaxVarianceForStraightLine = 100 // in ms - maximum variance for a straight line pattern

	MinSubmissionsForPattern   = 3 // minimum submissions needed to detect a pattern
	ConsistentPatternThreshold = 5 // minimum submissions with a consistent pattern to detect a bot
	MinStraightLineSubmissions = 7 // minimum submissions needed for straight line detection (to allow for ignoring first and last)
	MinStraightLinePoints      = 5 // minimum points needed after removing first and last points

	HighConfidenceThreshold   = 0.85 // 85% confidence
	MediumConfidenceThreshold = 0.7  // 70% confidence

	// Bot detection scoring thresholds
	BotScoreThreshold = 0.2 // Minimum normalized score to flag as bot

	// Detection weights
	HighConfidenceWeight   = 0.8
	MediumConfidenceWeight = 0.5
	LowConfidenceWeight    = 0.3
)

type Evidence struct {
	SubmissionTimes []int                  `json:"submissionTimes"`
	TimeDiffs       []int                  `json:"timeDiffs,omitempty"`
	Mean            float64                `json:"mean,omitempty"`
	Median          float64                `json:"median,omitempty"`
	StdDev          float64                `json:"stdDev,omitempty"`
	Variance        float64                `json:"variance,omitempty"`
	MinTimeDiff     int                    `json:"minTimeDiff,omitempty"`
	MaxTimeDiff     int                    `json:"maxTimeDiff,omitempty"`
	FastCount       int                    `json:"fastCount,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

type Detection struct {
	Type       models.BotDetectionType       `json:"type"`
	Confidence models.BotFlagConfidenceLevel `json:"confidence"`
	Evidence   Evidence                      `json:"evidence"`
	Timestamp  time.Time                     `json:"timestamp"`
}

type AnalysisResult struct {
	IsBot             bool                          `json:"isBot"`
	Detections        []Detection                   `json:"detections,omitempty"`
	HighestConfidence models.BotFlagConfidenceLevel `json:"highestConfidence,omitempty"`
	Score             float64                       `json:"score,omitempty"`
	NormalizedScore   float64                       `json:"normalizedScore,omitempty"`
}

// DetectorFunc is a function that detects a specific bot pattern
type DetectorFunc func(sortedTimes, timeDiffs []int) *Detection

type Analyzer struct {
	detectors []DetectorFunc
}

func NewAnalyzer() *Analyzer {
	a := &Analyzer{}
	// Initialize all detector functions
	a.detectors = []DetectorFunc{
		a.detectRapidSubmissions,
		a.detectMinTimeDiffViolations,
		a.detectFixedTimePattern,
		a.detectStraightLinePattern,
		a.detectConsecutiveFastSubmissions,
	}
	return a
}

// Analyze analyzes submission times to detect bot behavior
func (a *Analyzer) Analyze(submissionTimes []int, gradRequired bool) *AnalysisResult {
	// Not enough data to analyze
	if len(submissionTimes) < 2 {
		return &AnalysisResult{IsBot: false}
	}

	// Initialize result
	result := &AnalysisResult{
		IsBot:      false,
		Detections: []Detection{},
		Score:      0.0,
	}

	// Prepare data for analysis
	sortedTimes := make([]int, len(submissionTimes))
	copy(sortedTimes, submissionTimes)
	sort.Ints(sortedTimes)
	timeDiffs := submissionTimes
	if gradRequired {
		timeDiffs = grad(sortedTimes)
	}

	// Run all detectors and collect results
	result.Detections = a.runDetectors(sortedTimes, timeDiffs)

	// Calculate scores
	totalScore := a.calculateTotalScore(result.Detections)
	normalizedScore := a.calculateNormalizedScore(totalScore)

	// Set result fields
	result.Score = totalScore
	result.NormalizedScore = normalizedScore
	result.IsBot = a.isBot(normalizedScore)
	result.HighestConfidence = a.determineHighestConfidence(result.Detections)

	return result
}

// getDetectorsCount returns the number of detector functions
func (a *Analyzer) getDetectorsCount() int {
	return len(a.detectors)
}

// getBotScoreThreshold returns the threshold for bot detection
func (a *Analyzer) getBotScoreThreshold() float64 {
	return BotScoreThreshold
}

// getConfidenceWeight returns the weight for a confidence level
func (a *Analyzer) getConfidenceWeight(confidence models.BotFlagConfidenceLevel) float64 {
	switch confidence {
	case models.ConfidenceLevelHigh:
		return HighConfidenceWeight
	case models.ConfidenceLevelMedium:
		return MediumConfidenceWeight
	case models.ConfidenceLevelLow:
		return LowConfidenceWeight
	default:
		return 0.0
	}
}

// getMaxConfidenceWeight returns the maximum weight for confidence levels
func (a *Analyzer) getMaxConfidenceWeight() float64 {
	return HighConfidenceWeight
}

// calculateNormalizedScore calculates a normalized score from 0 to 1
func (a *Analyzer) calculateNormalizedScore(totalScore float64) float64 {
	maxScore := float64(a.getDetectorsCount()) * a.getMaxConfidenceWeight()
	if maxScore <= 0 {
		return 0.0
	}
	return totalScore / maxScore
}

// isBot determines if a user is a bot based on the normalized score
func (a *Analyzer) isBot(normalizedScore float64) bool {
	return normalizedScore >= a.getBotScoreThreshold()
}

// runDetectors runs all detector functions and collects results
func (a *Analyzer) runDetectors(sortedTimes, timeDiffs []int) []Detection {
	var detections []Detection

	for _, detector := range a.detectors {
		if detection := detector(sortedTimes, timeDiffs); detection != nil {
			detections = append(detections, *detection)
		}
	}

	return detections
}

// calculateTotalScore calculates the total score from detections
func (a *Analyzer) calculateTotalScore(detections []Detection) float64 {
	totalScore := 0.0
	for _, detection := range detections {
		totalScore += a.getConfidenceWeight(detection.Confidence)
	}
	return totalScore
}

// determineHighestConfidence finds the highest confidence level among detections
func (a *Analyzer) determineHighestConfidence(detections []Detection) models.BotFlagConfidenceLevel {
	if len(detections) == 0 {
		return ""
	}

	highestConfidence := models.ConfidenceLevelLow
	for _, detection := range detections {
		switch detection.Confidence {
		case models.ConfidenceLevelHigh:
			return models.ConfidenceLevelHigh
		case models.ConfidenceLevelMedium:
			highestConfidence = models.ConfidenceLevelMedium
		}
	}

	return highestConfidence
}

func (a *Analyzer) detectRapidSubmissions(sortedTimes, timeDiffs []int) *Detection {
	if len(timeDiffs) == 0 {
		return nil
	}

	violationCount := 0
	minTimeDiff := timeDiffs[0]

	for _, diff := range timeDiffs {
		if diff < minTimeDiff {
			minTimeDiff = diff
		}

		if diff < RapidSubmissionThreshold {
			violationCount++
		}
	}

	if violationCount >= len(timeDiffs)/4 {
		violationRatio := float64(violationCount) / float64(len(timeDiffs))
		confidence := models.ConfidenceLevelMedium

		if violationRatio >= HighConfidenceThreshold {
			confidence = models.ConfidenceLevelHigh
		}

		return &Detection{
			Type:       models.BotDetectionTypeRapidSubmission,
			Confidence: confidence,
			Evidence: Evidence{
				SubmissionTimes: sortedTimes,
				TimeDiffs:       timeDiffs,
				MinTimeDiff:     minTimeDiff,
				FastCount:       violationCount,
				Metadata: map[string]interface{}{
					"threshold":      RapidSubmissionThreshold,
					"violationCount": violationCount,
					"violationRatio": violationRatio,
				},
			},
			Timestamp: time.Now(),
		}
	}

	return nil
}

func (a *Analyzer) detectMinTimeDiffViolations(sortedTimes, timeDiffs []int) *Detection {
	if len(timeDiffs) == 0 {
		return nil
	}

	violationCount := 0
	totalSubmissions := len(timeDiffs)

	minTimeDiff := timeDiffs[0]
	for _, diff := range timeDiffs {
		if diff < minTimeDiff {
			minTimeDiff = diff
		}

		if diff < MinTimeDiffThreshold {
			violationCount++
		}
	}

	violationRatio := float64(violationCount) / float64(totalSubmissions)

	if violationCount >= len(timeDiffs)/3 {
		confidence := models.ConfidenceLevelLow

		// Adjust confidence based on violation ratio
		if violationRatio >= HighConfidenceThreshold {
			confidence = models.ConfidenceLevelHigh
		} else if violationRatio >= MediumConfidenceThreshold {
			confidence = models.ConfidenceLevelMedium
		}

		return &Detection{
			Type:       models.BotDetectionTypeMinTimeDiffViolation,
			Confidence: confidence,
			Evidence: Evidence{
				SubmissionTimes: sortedTimes,
				TimeDiffs:       timeDiffs,
				MinTimeDiff:     minTimeDiff,
				FastCount:       violationCount,
				Metadata: map[string]interface{}{
					"threshold":        MinTimeDiffThreshold,
					"violationCount":   violationCount,
					"totalSubmissions": totalSubmissions,
					"violationRatio":   violationRatio,
				},
			},
			Timestamp: time.Now(),
		}
	}

	return nil
}

func (a *Analyzer) detectFixedTimePattern(sortedTimes, timeDiffs []int) *Detection {
	if len(timeDiffs) < MinSubmissionsForPattern-1 {
		return nil
	}

	timeDiffsFloat := make([]float64, len(timeDiffs))
	for i, diff := range timeDiffs {
		timeDiffsFloat[i] = float64(diff)
	}

	mean, _ := stats.Mean(timeDiffsFloat)
	stdDev, _ := stats.StandardDeviation(timeDiffsFloat)
	variance := stdDev * stdDev

	if stdDev < StandardDeviationThreshold && mean < FixedTimeVarianceThreshold {
		confidence := models.ConfidenceLevelMedium

		if stdDev < StandardDeviationThreshold/2 {
			confidence = models.ConfidenceLevelHigh
		}

		return &Detection{
			Type:       models.BotDetectionTypeFixedPattern,
			Confidence: confidence,
			Evidence: Evidence{
				SubmissionTimes: sortedTimes,
				TimeDiffs:       timeDiffs,
				Mean:            mean,
				StdDev:          stdDev,
				Variance:        variance,
				Metadata: map[string]interface{}{
					"stdDevThreshold": StandardDeviationThreshold,
					"meanThreshold":   FixedTimeVarianceThreshold,
				},
			},
			Timestamp: time.Now(),
		}
	}

	return nil
}

func (a *Analyzer) detectStraightLinePattern(sortedTimes, timeDiffs []int) *Detection {
	if len(sortedTimes) < MinStraightLineSubmissions {
		return nil
	}

	analyzedTimes := sortedTimes[1 : len(sortedTimes)-1]

	if len(analyzedTimes) < MinStraightLinePoints {
		return nil
	}

	xValues := make([]float64, len(analyzedTimes))
	yValues := make([]float64, len(analyzedTimes))

	for i := range analyzedTimes {
		xValues[i] = float64(i)
		yValues[i] = float64(analyzedTimes[i])
	}

	slope, intercept, r2 := linearRegression(xValues, yValues)

	residuals := make([]float64, len(analyzedTimes))
	for i := range analyzedTimes {
		predicted := slope*xValues[i] + intercept
		residuals[i] = math.Abs(yValues[i] - predicted)
	}

	mae, _ := stats.Mean(residuals)

	// If R² is close to 1 and MAE is small, it's a straight line
	if r2 > 0.95 && mae < float64(MaxVarianceForStraightLine) {
		confidence := models.ConfidenceLevelMedium

		if r2 > 0.98 && mae < float64(MaxVarianceForStraightLine/2) {
			confidence = models.ConfidenceLevelHigh
		}

		return &Detection{
			Type:       models.BotDetectionTypeStraightLinePattern,
			Confidence: confidence,
			Evidence: Evidence{
				SubmissionTimes: sortedTimes,
				Metadata: map[string]interface{}{
					"r2":                  r2,
					"mae":                 mae,
					"slope":               slope,
					"analyzedPointsCount": len(analyzedTimes),
					"ignoredFirstPoint":   sortedTimes[0],
					"ignoredLastPoint":    sortedTimes[len(sortedTimes)-1],
					"totalPoints":         len(sortedTimes),
				},
			},
			Timestamp: time.Now(),
		}
	}

	return nil
}

func (a *Analyzer) detectConsecutiveFastSubmissions(sortedTimes, timeDiffs []int) *Detection {
	if len(timeDiffs) < 2 {
		return nil
	}

	consecutiveFast := 0
	maxConsecutive := 0
	totalViolations := 0

	for _, diff := range timeDiffs {
		if diff < MinTimeDiffThreshold {
			consecutiveFast++
			totalViolations++
			if consecutiveFast > maxConsecutive {
				maxConsecutive = consecutiveFast
			}
		} else {
			consecutiveFast = 0
		}
	}

	if maxConsecutive >= len(timeDiffs)/3 {
		violationRatio := float64(totalViolations) / float64(len(timeDiffs))
		confidence := models.ConfidenceLevelMedium

		if maxConsecutive >= 8 || violationRatio >= HighConfidenceThreshold {
			confidence = models.ConfidenceLevelHigh
		}

		return &Detection{
			Type:       models.BotDetectionTypeConsecutiveFast,
			Confidence: confidence,
			Evidence: Evidence{
				SubmissionTimes: sortedTimes,
				TimeDiffs:       timeDiffs,
				FastCount:       maxConsecutive,
				Metadata: map[string]interface{}{
					"threshold":       MinTimeDiffThreshold,
					"maxConsecutive":  maxConsecutive,
					"totalViolations": totalViolations,
					"violationRatio":  violationRatio,
				},
			},
			Timestamp: time.Now(),
		}
	}

	return nil
}

// calculateTimeDiffs calculates the time differences between consecutive submissions
func grad(sortedTimes []int) []int {
	if len(sortedTimes) < 2 {
		return []int{}
	}

	timeDiffs := make([]int, len(sortedTimes)-1)
	for i := 1; i < len(sortedTimes); i++ {
		timeDiffs[i-1] = sortedTimes[i] - sortedTimes[i-1]
	}

	return timeDiffs
}

// linearRegression calculates the slope, intercept, and R² value for a linear regression
func linearRegression(x, y []float64) (slope, intercept, r2 float64) {
	n := float64(len(x))

	if n < 2 {
		return 0, 0, 0
	}

	sumX, sumY := 0.0, 0.0
	sumXY, sumX2 := 0.0, 0.0

	for i := range x {
		sumX += x[i]
		sumY += y[i]
		sumXY += x[i] * y[i]
		sumX2 += x[i] * x[i]
	}

	// Calculate slope and intercept
	denom := n*sumX2 - sumX*sumX
	if denom == 0 {
		return 0, 0, 0 // insufficient variation in X
	}
	slope = (n*sumXY - sumX*sumY) / denom
	intercept = (sumY - slope*sumX) / n

	// Calculate R²
	sumResiduals2 := 0.0
	sumTotal2 := 0.0
	meanY := sumY / n

	for i := range y {
		predicted := slope*x[i] + intercept
		sumResiduals2 += math.Pow(y[i]-predicted, 2)
		sumTotal2 += math.Pow(y[i]-meanY, 2)
	}

	if sumTotal2 == 0 {
		r2 = 0
	} else {
		r2 = 1 - (sumResiduals2 / sumTotal2)
	}

	return slope, intercept, r2
}
