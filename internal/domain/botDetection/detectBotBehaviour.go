package botDetection

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) DetectBotBehavior(ctx context.Context, userID primitive.ObjectID, gameID *primitive.ObjectID, submissionTimes []int) (*models.BotDetection, error) {
	userAgent := ctx.Value(constants.UserAgentKey)
	ip := ctx.Value(constants.IPKey)

	userAgentStr := ""
	ipStr := ""

	if ua, ok := userAgent.(string); ok {
		userAgentStr = ua
	}
	if addr, ok := ip.(string); ok {
		ipStr = addr
	}

	result := s.analyzer.Analyze(submissionTimes, false)

	if !result.IsBot || len(result.Detections) == 0 {
		return nil, nil
	}

	primaryDetection := result.Detections[0]

	detectionType := primaryDetection.Type

	var status models.BotDetectionStatus
	switch primaryDetection.Confidence {
	case models.ConfidenceLevelHigh:
		status = models.BotDetectionStatusConfirmed
	case models.ConfidenceLevelMedium:
		status = models.BotDetectionStatusConfirmed
	default:
		status = models.BotDetectionStatusSuspicious
	}

	detection := &models.BotDetection{
		ID:              primitive.NewObjectID(),
		UserID:          userID,
		GameID:          gameID,
		DetectionType:   detectionType,
		Status:          status,
		Evidence:        primaryDetection.Evidence.Metadata,
		SubmissionTimes: submissionTimes,
		UserAgent:       userAgentStr,
		IPAddress:       ipStr,
		CreatedAt:       time.Now(),
	}

	err := s.botDetectionRepo.Create(ctx, detection)
	if err != nil {
		zlog.Error(ctx, "Failed to create bot detection", err,
			zap.String("userId", userID.Hex()))
		return nil, err
	}

	go s.checkAndShadowBanUser(context.Background(), userID)

	return detection, nil
}

func (s *service) checkAndShadowBanUser(ctx context.Context, userID primitive.ObjectID) {
	since := time.Now().Add(-RecentDetectionWindow)
	recentDetections, err := s.GetRecentUserBotDetections(ctx, userID, since)
	if err != nil {
		zlog.Error(ctx, "Failed to get recent bot detections", err,
			zap.String("userId", userID.Hex()))
		return
	}

	totalDetections, err := s.botDetectionRepo.CountByUserID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to count bot detections", err,
			zap.String("userId", userID.Hex()))
		return
	}

	shadowBan, err := s.GetUserShadowBanStatus(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get user shadow ban status", err,
			zap.String("userId", userID.Hex()))
		return
	}

	if shadowBan != nil && shadowBan.Status == models.UserShadowBanStatusFull {
		return
	}

	if len(recentDetections) >= DetectionCountThresholdPartial && (shadowBan == nil || shadowBan.Status == models.UserShadowBanStatusNone) {
		detectionIDs := make([]primitive.ObjectID, len(recentDetections))
		for i, detection := range recentDetections {
			detectionIDs[i] = detection.ID
		}

		// Note: Race conditions are handled by the atomic upsert operation in the repository
		// If multiple goroutines attempt to create the same shadow ban record, only one will succeed
		err = s.ShadowBanUser(ctx, userID, models.UserShadowBanStatusPartial, "Multiple bot detections in 24 hours", detectionIDs)
		if err != nil {
			zlog.Error(ctx, "Failed to shadow ban user", err,
				zap.String("userId", userID.Hex()))
		}
	} else if totalDetections >= DetectionCountThresholdFull && (shadowBan == nil || shadowBan.Status != models.UserShadowBanStatusFull) {
		allDetections, err := s.GetUserBotDetections(ctx, userID)
		if err != nil {
			zlog.Error(ctx, "Failed to get all bot detections", err,
				zap.String("userId", userID.Hex()))
			return
		}

		detectionIDs := make([]primitive.ObjectID, len(allDetections))
		for i, detection := range allDetections {
			detectionIDs[i] = detection.ID
		}

		// Note: Race conditions are handled by the atomic upsert operation in the repository
		// If multiple goroutines attempt to create the same shadow ban record, only one will succeed
		err = s.ShadowBanUser(ctx, userID, models.UserShadowBanStatusFull, "Multiple bot detections over time", detectionIDs)
		if err != nil {
			zlog.Error(ctx, "Failed to shadow ban user", err,
				zap.String("userId", userID.Hex()))
		}
	}
}
