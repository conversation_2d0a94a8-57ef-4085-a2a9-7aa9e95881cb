package botDetection

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) GetUserBotDetections(ctx context.Context, userID primitive.ObjectID) ([]*models.BotDetection, error) {
	return s.botDetectionRepo.FindByUserID(ctx, userID)
}

func (s *service) GetRecentUserBotDetections(ctx context.Context, userID primitive.ObjectID, since time.Time) ([]*models.BotDetection, error) {
	return s.botDetectionRepo.FindRecentByUserID(ctx, userID, since)
}
