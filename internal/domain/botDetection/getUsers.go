package botDetection

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *service) GetShadowBannedUsers(ctx context.Context) ([]primitive.ObjectID, error) {
	bannedUsers, err := s.userShadowBanRepo.Find(ctx, bson.M{"$or": bson.A{bson.M{"status": models.UserShadowBanStatusPartial}, bson.M{"status": models.UserShadowBanStatusFull}}})
	if err != nil {
		return nil, err
	}

	var bannedUserIDs []primitive.ObjectID
	for _, user := range bannedUsers {
		bannedUserIDs = append(bannedUserIDs, user.UserID)
	}

	return bannedUserIDs, nil
}
