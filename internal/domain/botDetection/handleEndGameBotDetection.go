package botDetection

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/botDetection/analyzer"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

// HandleEndGameBotDetection analyzes game submissions for bot behavior
func (s *service) HandleEndGameBotDetection(ctx context.Context, game *models.Game, winnerID, loserID primitive.ObjectID) map[primitive.ObjectID]bool {
	if game == nil {
		return nil
	}

	if game.GameType == models.GameTypeFastestFinger {
		return nil
	}

	// Extract submission times from game data
	userSubmissionTimes := extractSubmissionTimes(game)

	// Analyze and detect bots
	botUsers := make(map[primitive.ObjectID]bool)
	for userIDStr, times := range userSubmissionTimes {
		if len(times) < analyzer.ConsistentPatternThreshold {
			continue
		}

		// Analyze user submissions for bot behavior
		result, userID, err := analyzeUserSubmissions(ctx, s.analyzer, userIDStr, times)
		if err != nil || !result.IsBot {
			continue
		}

		botUsers[userIDStr] = true

		// Record bot detection
		detection := recordBotDetection(ctx, s.botDetectionRepo, userID, game, result, times)
		if detection == nil {
			continue
		}

		// Handle follow-up actions (shadow banning, rating updates)
		go handleBotDetectionFollowup(userID, winnerID, loserID, detection, s)
	}

	return botUsers
}

// extractSubmissionTimes extracts submission times from game data
func extractSubmissionTimes(game *models.Game) map[primitive.ObjectID][]int {
	userSubmissionTimes := make(map[primitive.ObjectID][]int)

	for _, question := range game.Questions {
		for _, submission := range question.Submissions {
			if submission.UserID != nil && submission.TimeTaken != nil {
				userSubmissionTimes[*submission.UserID] = append(userSubmissionTimes[*submission.UserID], *submission.TimeTaken)
			}
		}
	}

	return userSubmissionTimes
}

// analyzeUserSubmissions analyzes submission patterns for bot behavior
func analyzeUserSubmissions(ctx context.Context, analyzer *analyzer.Analyzer, userID primitive.ObjectID, times []int) (*analyzer.AnalysisResult, primitive.ObjectID, error) {
	result := analyzer.Analyze(times, true)

	if !result.IsBot {
		return result, primitive.NilObjectID, nil
	}

	return result, userID, nil
}

// recordBotDetection creates and stores a bot detection record
func recordBotDetection(ctx context.Context, repo repository.BotDetectionRepository, userID primitive.ObjectID, game *models.Game, result *analyzer.AnalysisResult, times []int) *models.BotDetection {
	var detectionType models.BotDetectionType
	if len(result.Detections) > 0 {
		detectionType = result.Detections[0].Type
	} else {
		detectionType = models.BotDetectionTypeStraightLinePattern
	}

	detection := &models.BotDetection{
		ID:              primitive.NewObjectID(),
		UserID:          userID,
		GameID:          game.ID,
		DetectionType:   detectionType,
		Status:          models.BotDetectionStatusConfirmed,
		Evidence:        map[string]interface{}{"endGameAnalysis": true, "gameType": game.GameType, "submissionTimes": times},
		SubmissionTimes: times,
		CreatedAt:       time.Now(),
	}

	err := repo.Create(ctx, detection)
	if err != nil {
		zlog.Error(ctx, "Failed to create bot detection", err, zap.String("userID", userID.Hex()))
		return nil
	}

	return detection
}

// handleBotDetectionFollowup handles follow-up actions after bot detection
func handleBotDetectionFollowup(userID, winnerID, loserID primitive.ObjectID, detection *models.BotDetection, s *service) {
	ctx := context.Background()

	// Apply shadow ban if not already fully banned
	shadowBan, err := s.GetUserShadowBanStatus(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get shadow ban status", err,
			zap.String("userID", userID.Hex()))
		return
	}

	if shadowBan == nil || shadowBan.Status != models.UserShadowBanStatusFull {
		// Note: Race conditions are handled by the atomic upsert operation in the repository
		// If multiple goroutines attempt to create the same shadow ban record, only one will succeed
		err = s.ShadowBanUser(ctx, userID, models.UserShadowBanStatusFull,
			"Bot behavior detected at end of game", []primitive.ObjectID{detection.ID})
		if err != nil {
			zlog.Error(ctx, "Failed to shadow ban user", err,
				zap.String("userID", userID.Hex()))
		}
	}

	zlog.Info(ctx, "Bot user detected - rating changes will be limited",
		zap.String("botUserID", userID.Hex()),
		zap.String("gameID", detection.GameID.Hex()),
		zap.String("detectionType", string(detection.DetectionType)))
}
