package botDetection

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) HandleSubmissionBotDetection(ctx context.Context, userID primitive.ObjectID, gameID *primitive.ObjectID, submissionTimes []int) (bool, *models.BotDetection, error) {
	detection, err := s.DetectBotBehavior(ctx, userID, gameID, submissionTimes)
	if err != nil {
		zlog.Error(ctx, "Failed to detect bot behavior", err,
			zap.String("gameID", gameID.Hex()),
			zap.String("userID", userID.Hex()))
		return false, nil, err
	}

	if detection == nil {
		return false, nil, nil
	}

	zlog.Warn(ctx, "Bot behavior detected during submission",
		zap.String("gameID", gameID.Hex()),
		zap.String("userID", userID.Hex()),
		zap.String("detectionType", string(detection.DetectionType)))

	return true, detection, nil
}
