package botDetection

import (
	"context"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func (s *service) IsUserShadowBanned(ctx context.Context, userID primitive.ObjectID) (bool, error) {
	user, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get user", err,
			zap.String("userId", userID.Hex()))
		return false, err
	}

	if user == nil {
		return false, nil
	}

	isShadowBanned := user.IsShadowBanned != nil && *user.IsShadowBanned

	return isShadowBanned, nil
}

func (s *service) ShouldPublishEventToUser(ctx context.Context, userID, eventSourceUserID primitive.ObjectID) bool {
	if userID == eventSourceUserID {
		return true
	}

	shadowBanned, err := s.IsUserShadowBanned(ctx, eventSourceUserID)
	if err != nil {
		zlog.Error(ctx, "Failed to check if user is shadow banned", err,
			zap.String("userID", eventSourceUserID.Hex()))
		return true
	}

	return !shadowBanned
}
