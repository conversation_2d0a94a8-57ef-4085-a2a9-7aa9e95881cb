package botDetection

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/domain/botDetection/analyzer"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

const (
	// Detection thresholds
	DetectionCountThresholdPartial = 3 // Number of detections in 24h for partial shadow ban
	DetectionCountThresholdFull    = 5 // Total number of detections for full shadow ban

	// Time windows
	RecentDetectionWindow = 24 * time.Hour // Window for recent detections
)

type service struct {
	botDetectionRepo  repository.BotDetectionRepository
	userShadowBanRepo repository.UserShadowBanRepository
	userService       domain.UserStore
	cache             cache.Cache
	analyzer          *analyzer.Analyzer
}

func NewBotDetectionService(
	lc fx.Lifecycle,
	repositoryFactory *repository.RepositoryFactory,
	cacheInstance cache.Cache,
	userService domain.UserStore,
) domain.BotDetectionStore {
	s := &service{
		botDetectionRepo:  repositoryFactory.BotDetectionRepository,
		userShadowBanRepo: repositoryFactory.UserShadowBanRepository,
		cache:             cacheInstance,
		userService:       userService,
		analyzer:          analyzer.NewAnalyzer(),
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting bot detection service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down bot detection service")
			return nil
		},
	})

	return s
}
