package botDetection

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) ShadowBanUser(ctx context.Context, userID primitive.ObjectID, status models.UserShadowBanStatus, reason string, detectionIDs []primitive.ObjectID) error {
	shadowBan := &models.UserShadowBan{
		ID:           primitive.NewObjectID(),
		UserID:       userID,
		Status:       status,
		Reason:       reason,
		DetectionIDs: detectionIDs,
		CreatedAt:    time.Now(),
	}

	err := s.userShadowBanRepo.Create(ctx, shadowBan)
	if err != nil {
		zlog.Error(ctx, "Failed to create user shadow ban", err,
			zap.String("userId", userID.Hex()))
		return err
	}

	isShadowBanned := status != models.UserShadowBanStatusNone
	err = s.userService.UpdateOne(ctx, bson.M{"_id": userID}, bson.M{
		"$set": bson.M{
			"isShadowBanned":  isShadowBanned,
			"shadowBanStatus": status,
		},
	})
	if err != nil {
		zlog.Error(ctx, "Failed to update user shadow ban status", err,
			zap.String("userId", userID.Hex()))
		return err
	}

	return nil
}

func (s *service) UpdateUserShadowBanStatus(ctx context.Context, userID primitive.ObjectID, status models.UserShadowBanStatus) error {
	shadowBan, err := s.userShadowBanRepo.FindByUserID(ctx, userID)
	if err != nil {
		return err
	}

	if shadowBan == nil {
		return nil
	}

	err = s.userShadowBanRepo.UpdateStatus(ctx, shadowBan.ID, status)
	if err != nil {
		return err
	}

	isShadowBanned := status != models.UserShadowBanStatusNone
	err = s.userService.UpdateOne(ctx, bson.M{"_id": userID}, bson.M{
		"$set": bson.M{
			"isShadowBanned":  isShadowBanned,
			"shadowBanStatus": status,
		},
	})
	if err != nil {
		return err
	}

	return nil
}
