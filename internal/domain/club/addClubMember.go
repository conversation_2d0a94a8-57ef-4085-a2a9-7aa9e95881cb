package club

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) AddClubMember(ctx context.Context, clubID, userID primitive.ObjectID) (bool, error) {
	currentUserId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	clubInfo, err := s.clubRepo.FindClubByID(ctx, clubID)

	if err != nil || clubInfo == nil {
		return false, fmt.Errorf("club not found %w", err)
	}

	memberInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, currentUserId, clubID)
	if err != nil {
		return false, err
	}

	if memberInfo != nil && memberInfo.Role == models.ClubMemberRoleAdmin {
		newMemberInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, userID, clubID)

		if err != nil || newMemberInfo == nil {
			return false, fmt.Errorf("user Request to joinClub Not found %w", err)
		}

		err = s.clubMemberRepo.AcceptMemberJoinRequest(ctx, clubID, userID)
		if err != nil {
			return false, err
		}

		if clubInfo.ChatRoomID != primitive.NilObjectID {
			err = s.messageGroupRepo.AddParticipant(ctx, clubInfo.ChatRoomID, utils.AllocPtr(userID))
			if err != nil {
				return false, err
			}
		}

		err = s.clubRepo.IncrementMemberCount(ctx, clubID)
		if err != nil {
			return false, err
		}

		return true, nil
	}

	return false, fmt.Errorf("you're not the admin so you can not add members")
}
