package club

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// TODO : @k123ritesh Implement transaction for atomic club creation.
func (s *service) CreateClub(ctx context.Context, input models.CreateClubInput) (*models.Club, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if input.Name == "" || len(input.Name) < 5 {
		return nil, fmt.Errorf("club name cannot be empty")
	}

	if len(input.Name) > 100 {
		return nil, fmt.Errorf("club name cannot exceed 100 characters")
	}

	if input.Description != nil && len(*input.Description) > 500 {
		return nil, fmt.Errorf("club description cannot exceed 500 characters")
	}

	clubId := primitive.NewObjectID()
	forumId := primitive.NewObjectID()

	chatRoomID := primitive.NewObjectID()
	leagueID := primitive.NewObjectID()

	members := make([]*primitive.ObjectID, 0)

	err = s.messageGroupRepo.Create(ctx, &models.MessageGroup{
		ID:        chatRoomID,
		GroupName: &input.Name,
		GroupType: models.GroupTypeCommunity,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Alias:     fmt.Sprintf("%s_%s", chatRoomID, leagueID),
		Members:   members,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create chat for club: %w", err)
	}

	forum := &models.Forum{
		ID:     forumId,
		ClubID: clubId,
		Title:  input.Name,
		Description: func() *string {
			if input.Description != nil {
				return input.Description
			}
			return nil
		}(),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		CreatedBy: userId,
	}

	_, err = s.forumRepo.CreateForum(ctx, forum)
	if err != nil {
		return nil, err
	}

	club := models.Club{
		ID:              clubId,
		Name:            input.Name,
		ChatRoomID:      chatRoomID,
		Description:     input.Description,
		Visibility:      input.Visibility,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
		CreatedBy:       userId,
		ForumID:         forumId,
		MembersCount:    0,
		ClubEventsCount: 0,
		Category:        input.Category,
	}

	// filesToUpload := make([]*graphql.Upload, 0)

	// if input.LogoImage != nil {
	// 	filesToUpload = append(filesToUpload, input.LogoImage)
	// }

	// if input.BannerImage != nil {
	// 	filesToUpload = append(filesToUpload, input.BannerImage)
	// }

	// if len(filesToUpload) > 0 {
	// 	uploadedFiles, err := s.userService.UploadFiles(ctx, filesToUpload)

	// 	if err != nil {
	// 		return nil, err
	// 	}

	// 	if uploadedFiles != nil && len(uploadedFiles) == len(filesToUpload) {
	// 		if len(uploadedFiles) > 0 && uploadedFiles[0] != nil {
	// 			club.LogoImage = &uploadedFiles[0].URL
	// 		}
	// 		if len(uploadedFiles) > 1 && uploadedFiles[1] != nil {
	// 			club.BannerImage = &uploadedFiles[1].URL
	// 		}
	// 	}
	// }

	err = s.clubRepo.CreateClub(ctx, &club)
	if err != nil {
		return nil, err
	}

	memberInfo := &models.ClubMember{
		ID:                   primitive.NewObjectID(),
		ClubID:               clubId,
		UserID:               userId,
		Role:                 models.ClubMemberRoleAdmin,
		JoinedAt:             time.Now(),
		ClubMembershipStatus: models.ClubMembershipStatusAccepted,
	}

	err = s.clubMemberRepo.AddMember(ctx, memberInfo)
	if err != nil {
		return nil, err
	}

	err = s.messageGroupRepo.AddParticipant(ctx, chatRoomID, utils.AllocPtr(userId))
	if err != nil {
		return nil, fmt.Errorf("failed to add participant to chat room: %w", err)
	}

	if input.LogoImage != nil && input.LogoImage.Filename != "" {
		logoFile, err := s.UploadClubLogoImage(ctx, *input.LogoImage, club.ID)
		if err != nil || logoFile == nil {
			return nil, err
		}
	}

	if input.BannerImage != nil && input.BannerImage.Filename != "" {
		bannerFile, err := s.UploadClubBannerImage(ctx, *input.BannerImage, club.ID)
		if err != nil || bannerFile == nil {
			return nil, err
		}
	}

	return &club, nil
}
