package club

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) DeleteClub(ctx context.Context, id primitive.ObjectID) (bool, error) {
	if id == primitive.NilObjectID {
		return false, fmt.Errorf("club ID cannot be empty")
	}
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	userInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, userId, id)

	if userInfo != nil && userInfo.Role == models.ClubMemberRoleAdmin {
		err := s.clubRepo.DeleteClub(ctx, id)
		if err != nil {
			return false, err
		}
		return true, nil
	}

	if err != nil {
		return false, err
	}

	return false, fmt.Errorf("you're not the admin of this club")
}
