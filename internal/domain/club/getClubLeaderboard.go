package club

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) GetClubLeaderboard(ctx context.Context, clubID primitive.ObjectID, page, pageSize *int) (*models.ClubLeaderboard, error) {
	if clubID == primitive.NilObjectID {
		return nil, fmt.Errorf("club ID cannot be empty")
	}

	if page == nil {
		page = utils.AllocPtr(1)
	}

	if pageSize == nil {
		pageSize = utils.AllocPtr(20)
	}

	return s.clubMemberRepo.GetClubLeaderboard(ctx, clubID, *page, *pageSize)
}
