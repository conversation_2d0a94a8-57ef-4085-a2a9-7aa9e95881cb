package club

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) GetClubMemberInfo(ctx context.Context, clubID primitive.ObjectID) (*models.ClubMember, error) {
	if clubID == primitive.NilObjectID {
		return nil, fmt.Errorf("club ID cannot be empty")
	}
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	clubMemberInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, userID, clubID)
	if err != nil {
		return nil, err
	}

	return clubMemberInfo, nil
}
