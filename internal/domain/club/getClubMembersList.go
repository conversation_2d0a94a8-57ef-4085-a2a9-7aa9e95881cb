package club

import (
	"context"
	"errors"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) Members(ctx context.Context, clubID primitive.ObjectID, membershipStatus models.ClubMembershipStatus, page, pageSize *int) (*models.ClubMembersPage, error) {
	if clubID == primitive.NilObjectID {
		return nil, fmt.Errorf("club ID cannot be empty")
	}
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	currUserMembership, err := s.clubMemberRepo.GetMemberInfo(ctx, userId, clubID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, fmt.Errorf("you are not a member of this club")
		}
		return nil, err
	}

	if currUserMembership == nil {
		return nil, fmt.Errorf("you are not a member of this club")
	}

	if currUserMembership.ClubMembershipStatus == models.ClubMembershipStatusPending {
		return nil, fmt.Errorf("you not a member of this club")
	}

	if currUserMembership.Role != models.ClubMemberRoleAdmin {
		return nil, fmt.Errorf("you are not a admin of this club")
	}

	currentPage := 1
	if page != nil {
		currentPage = *page
	}

	limit := 20
	if pageSize != nil {
		limit = *pageSize
	}

	skip := (currentPage - 1) * limit

	pipeline := mongo.Pipeline{}

	if clubID != primitive.NilObjectID {
		pipeline = append(pipeline, bson.D{{Key: "$match", Value: bson.M{
			"clubId":               clubID,
			"clubMembershipStatus": membershipStatus,
		}}})
	} else {
		return nil, fmt.Errorf("club ID cannot be empty")
	}

	pipeline = append(pipeline, bson.D{{Key: "$sort", Value: bson.M{"joinedAt": -1}}})

	pipeline = append(pipeline, bson.D{{Key: "$lookup", Value: bson.M{
		"from":         "users",
		"localField":   "userId",
		"foreignField": "_id",
		"as":           "userInfo",
	}}})

	pipeline = append(pipeline, bson.D{{Key: "$unwind", Value: bson.M{
		"path":                       "$userInfo",
		"preserveNullAndEmptyArrays": true,
	}}})

	pipeline = append(pipeline, bson.D{{Key: "$project", Value: bson.M{
		"_id":                  1,
		"clubId":               1,
		"userId":               1,
		"role":                 1,
		"joinedAt":             1,
		"clubMembershipStatus": 1,
		"memberInfo": bson.M{
			"$cond": bson.M{
				"if": bson.M{"$ifNull": []interface{}{"$userInfo", false}},
				"then": bson.M{
					"username":        "$userInfo.username",
					"profileImageUrl": "$userInfo.profileImageUrl",
					"rating":          "$userInfo.rating",
				},
				"else": nil,
			},
		},
	}}})

	pipeline = append(pipeline, bson.D{{Key: "$skip", Value: skip}})
	pipeline = append(pipeline, bson.D{{Key: "$limit", Value: limit}})

	countPipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"clubId":               clubID,
			"clubMembershipStatus": membershipStatus,
		}}},

		{{Key: "$count", Value: "total"}},
	}

	facetPipeline := mongo.Pipeline{
		{{Key: "$facet", Value: bson.M{
			"results": pipeline,
			"count":   countPipeline,
		}}},
	}

	cursor, err := s.clubMemberRepo.AggregateProjected(ctx, facetPipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var aggregateResult []struct {
		Results []*models.ClubMember `bson:"results"`
		Count   []struct {
			Total int `bson:"total"`
		} `bson:"count"`
	}

	if err = cursor.All(ctx, &aggregateResult); err != nil {
		return nil, err
	}

	if len(aggregateResult) == 0 {
		return &models.ClubMembersPage{
			Results:      []*models.ClubMember{},
			PageNumber:   currentPage,
			PageSize:     limit,
			HasMore:      utils.AllocPtr(false),
			TotalResults: utils.AllocPtr(0),
		}, nil
	}

	members := aggregateResult[0].Results
	totalResults := 0
	if len(aggregateResult[0].Count) > 0 {
		totalResults = aggregateResult[0].Count[0].Total
	}

	hasMore := (skip + len(members)) < totalResults

	result := &models.ClubMembersPage{
		Results:      members,
		PageNumber:   currentPage,
		PageSize:     limit,
		HasMore:      &hasMore,
		TotalResults: &totalResults,
	}

	return result, nil
}
