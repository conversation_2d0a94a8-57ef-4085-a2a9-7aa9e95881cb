package club

import (
	"context"
	"errors"
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"go.mongodb.org/mongo-driver/mongo"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) Club(ctx context.Context, id primitive.ObjectID) (*models.Club, error) {
	if id == primitive.NilObjectID {
		return nil, fmt.Errorf("club ID cannot be empty")
	}
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	clubInfo, err := s.clubRepo.FindClubByID(ctx, id)
	if err != nil {
		return nil, err
	}

	membershipInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, userId, id)
	clubInfo.IsClubMember = true
	clubInfo.HasRequestedToJoin = false
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			clubInfo.IsClubMember = false
		} else {
			return nil, err
		}
	}

	if membershipInfo != nil && membershipInfo.ClubMembershipStatus != models.ClubMembershipStatusAccepted {
		clubInfo.IsClubMember = false
	}

	if membershipInfo != nil && membershipInfo.ClubMembershipStatus == models.ClubMembershipStatusPending {
		clubInfo.HasRequestedToJoin = true
	}

	if membershipInfo != nil && membershipInfo.Role == models.ClubMemberRoleAdmin {
		clubInfo.IsAdmin = true
	} else {
		clubInfo.IsAdmin = false
	}

	return clubInfo, nil
}

func (s *service) Clubs(ctx context.Context, page, pageSize *int, visibility *models.Visibility, search *string) (*models.ClubsPage, error) {
	_, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	if page == nil {
		page = utils.AllocPtr(1)
	}

	if pageSize == nil {
		pageSize = utils.AllocPtr(20)
	}

	filter := bson.M{}

	if visibility != nil {
		filter["visibility"] = *visibility
	}

	if search != nil && *search != "" {
		filter["$or"] = []bson.M{
			{"name": bson.M{"$regex": *search, "$options": "i"}},
			{"description": bson.M{"$regex": *search, "$options": "i"}},
		}
	}

	skip := (*page - 1) * (*pageSize)
	opts := options.Find().SetLimit(int64(*pageSize)).SetSkip(int64(skip))
	opts.SetSort(bson.D{{Key: "_id", Value: 1}})

	clubs, err := s.clubRepo.ListClubs(ctx, filter, opts)
	if err != nil {
		return nil, err
	}

	totalCount, err := s.clubRepo.CountClubs(ctx, filter)
	if err != nil {
		return nil, err
	}

	hasMore := (skip + len(clubs)) < int(totalCount)
	hasMorePtr := &hasMore
	totalCountInt := int(totalCount)
	totalCountPtr := &totalCountInt

	return &models.ClubsPage{
		Results:      clubs,
		PageNumber:   *page,
		PageSize:     *pageSize,
		HasMore:      hasMorePtr,
		TotalResults: totalCountPtr,
	}, nil
}
