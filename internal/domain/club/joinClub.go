package club

import (
	"context"
	"errors"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) JoinClub(ctx context.Context, clubID primitive.ObjectID) (bool, error) {
	if clubID == primitive.NilObjectID {
		return false, fmt.Errorf("club ID cannot be empty")
	}
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	club, err := s.clubRepo.FindClubByID(ctx, clubID)
	if err != nil {
		return false, err
	}

	newMemberInfo := &models.ClubMember{
		ID:                   primitive.NewObjectID(),
		ClubID:               clubID,
		UserID:               userId,
		Role:                 models.ClubMemberRoleMember,
		JoinedAt:             time.Now(),
		ClubMembershipStatus: models.ClubMembershipStatusAccepted,
	}

	if club.Visibility == models.VisibilityPrivate {
		newMemberInfo.ClubMembershipStatus = models.ClubMembershipStatusPending
	}

	_, err = s.clubMemberRepo.GetMemberInfo(ctx, userId, clubID)

	if err != nil && errors.Is(err, mongo.ErrNoDocuments) {

		err = s.clubMemberRepo.AddMember(ctx, newMemberInfo)
		if err != nil {
			return false, err
		}

		if newMemberInfo.ClubMembershipStatus == models.ClubMembershipStatusAccepted {
			if club.ChatRoomID != primitive.NilObjectID {
				err = s.messageGroupRepo.AddParticipant(ctx, club.ChatRoomID, utils.AllocPtr(userId))
				if err != nil {
					return false, fmt.Errorf("failed to add participant to chat room: %w", err)
				}
			}
		}

		return true, nil
	}

	if err == nil {
		return false, fmt.Errorf("user is already a member of this club")
	}

	return false, fmt.Errorf("failed to check membership: %w", err)
}
