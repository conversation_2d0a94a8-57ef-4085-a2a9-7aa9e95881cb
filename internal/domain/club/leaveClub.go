package club

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) LeaveClub(ctx context.Context, clubID primitive.ObjectID) (bool, error) {
	if clubID == primitive.NilObjectID {
		return false, fmt.Errorf("club ID cannot be empty")
	}
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	club, err := s.clubRepo.FindClubByID(ctx, clubID)
	if err != nil {
		return false, err
	}

	if club == nil {
		return false, fmt.Errorf("club not found")
	}

	memberInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, userId, clubID)
	if err != nil {
		return false, err
	}

	if memberInfo.Role == models.ClubMemberRoleAdmin || memberInfo.Role == models.ClubMemberRoleOwner {
		return false, fmt.Errorf("cannot leave club: transfer admin rights to another member first")
	}

	err = s.clubMemberRepo.RemoveMember(ctx, clubID, userId)
	if err != nil {
		return false, err
	}

	if club.ChatRoomID != primitive.NilObjectID {
		err = s.messageGroupRepo.RemoveParticipant(ctx, club.ChatRoomID, userId)
		if err != nil {
			return false, fmt.Errorf("failed to remove participant from chat room: %w", err)
		}
	}

	return true, nil
}
