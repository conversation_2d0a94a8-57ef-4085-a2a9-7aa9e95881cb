package club

import (
	"context"
	"errors"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) RemoveClubMember(ctx context.Context, clubID, userID primitive.ObjectID) (bool, error) {
	if clubID == primitive.NilObjectID || userID == primitive.NilObjectID {
		return false, fmt.Errorf("club ID and user ID cannot be empty")
	}
	currentUserId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	currUserInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, currentUserId, clubID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, fmt.Errorf("you can't remove a member you're not admin of this club")
		}
		return false, err
	}

	memberInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, userID, clubID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, fmt.Errorf("user is not a member of this club")
		}
		return false, err
	}

	if currUserInfo != nil && currUserInfo.Role == models.ClubMemberRoleAdmin {
		if memberInfo.ClubMembershipStatus == models.ClubMembershipStatusPending {
			err = s.clubMemberRepo.RejectMemberJoinRequest(ctx, clubID, userID)
			if err != nil {
				return false, err
			}
		} else {
			err = s.clubMemberRepo.RemoveMember(ctx, clubID, userID)
			if err != nil {
				return false, err
			}
		}
		return true, nil
	}

	return false, fmt.Errorf("you're not the Admin so you can't remove members")
}
