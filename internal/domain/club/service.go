package club

import (
	"context"

	"go.uber.org/fx"
	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cdn"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/infrastructure/storage"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

type service struct {
	clubRepo         repository.ClubRepository
	clubMemberRepo   repository.ClubMemberRepository
	forumRepo        repository.ForumRepository
	messageGroupRepo repository.MessageGroupRepository
	storage          *storage.Storage
	cdnClient        cdn.CDNClient
}

func NewClubsService(lc fx.Lifecycle, repositoryFactory *repository.RepositoryFactory, storage *storage.Storage,
	cdnClient cdn.CDNClient,
) domain.ClubsStore {
	s := &service{
		clubRepo:         repositoryFactory.ClubRepository,
		clubMemberRepo:   repositoryFactory.ClubMemberRepository,
		forumRepo:        repositoryFactory.ForumRepository,
		messageGroupRepo: repositoryFactory.MessageGroupRepository,
		storage:          storage,
		cdnClient:        cdnClient,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting clubs service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down clubs service")
			return nil
		},
	})

	return s
}
