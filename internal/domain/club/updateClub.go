package club

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) UpdateClub(ctx context.Context, input models.UpdateClubInput) (bool, error) {
	if input.ClubID == primitive.NilObjectID {
		return false, fmt.Errorf("club ID cannot be empty")
	}
	clubID := input.ClubID
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, fmt.Errorf("failed to get user ID: %w", err)
	}

	club, err := s.clubRepo.FindClubByID(ctx, clubID)
	if err != nil {
		return false, fmt.Errorf("failed to get club: %w", err)
	}

	if club == nil {
		return false, fmt.Errorf("club not found")
	}

	membershipInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, userID, clubID)
	if err != nil {
		return false, err
	}

	if membershipInfo == nil {
		return false, fmt.Errorf("you are not a member of this club")
	}

	if membershipInfo.Role != models.ClubMemberRoleAdmin {
		return false, fmt.Errorf("you are not an admin of this club")
	}

	if input.Name != nil && *input.Name != "" {
		club.Name = *input.Name
	}

	if input.Description != nil && *input.Description != "" {
		club.Description = func() *string {
			if input.Description != nil {
				return input.Description
			}
			return nil
		}()
	}

	if input.Visibility != nil && input.Visibility.IsValid() {
		club.Visibility = *input.Visibility
	}

	err = s.clubRepo.UpdateClub(ctx, clubID, bson.M{
		"$set": bson.M{
			"name":        club.Name,
			"description": club.Description,
			"visibility":  club.Visibility,
		},
	})
	if err != nil {
		return false, err
	}

	return true, nil
}
