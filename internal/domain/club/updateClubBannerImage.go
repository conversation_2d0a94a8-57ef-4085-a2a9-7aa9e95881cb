package club

import (
	"context"
	"fmt"
	"io"
	"time"

	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"cloud.google.com/go/storage"
	"github.com/99designs/gqlgen/graphql"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) UploadClubBannerImage(ctx context.Context, file graphql.Upload, clubID primitive.ObjectID) (*models.File, error) {
	if clubID == primitive.NilObjectID {
		return nil, fmt.Errorf("club ID cannot be empty")
	}
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get user ID: %w", err)
	}

	club, err := s.clubRepo.FindClubByID(ctx, clubID)
	if err != nil {
		return nil, fmt.Errorf("failed to get club: %w", err)
	}

	if club == nil {
		return nil, fmt.Errorf("club not found")
	}

	membershipInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, userID, clubID)
	if err != nil {
		return nil, err
	}

	if membershipInfo == nil {
		return nil, fmt.Errorf("you are not a member of this club")
	}

	if membershipInfo.Role != models.ClubMemberRoleAdmin {
		return nil, fmt.Errorf("you are not an admin of this club")
	}

	bucket := s.storage.Storage.Bucket(constants.StorageBucket)
	objectName := "club_banner/" + clubID.Hex() + "_club-banner.jpeg"
	object := bucket.Object(objectName)

	if file.File == nil {
		return nil, fmt.Errorf("file cannot be nil")
	}

	content, err := io.ReadAll(file.File)
	if err != nil {
		return nil, fmt.Errorf("failed to read file content: %w", err)
	}

	writer := object.NewWriter(ctx)
	writer.ObjectAttrs.ContentType = file.ContentType
	writer.ObjectAttrs.ACL = []storage.ACLRule{{Entity: storage.AllUsers, Role: storage.RoleReader}}
	writer.ObjectAttrs.CacheControl = "public, max-age=31536000, immutable"

	if _, err := writer.Write(content); err != nil {
		return nil, fmt.Errorf("failed to write to GCS: %w", err)
	}

	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("failed to close GCS writer: %w", err)
	}

	timestamp := time.Now().Unix()
	url := fmt.Sprintf("https://cdn.matiks.com/%s?timestamp=%d", objectName, timestamp)

	err = s.clubRepo.UpdateClub(ctx, clubID, bson.M{"$set": bson.M{"bannerImage": url}})
	if err != nil {
		return nil, fmt.Errorf("failed to update club banner image URL in database: %w", err)
	}

	// Extract the path from the URL and invalidate the CDN cache
	urlPath := s.cdnClient.ExtractPathFromURL(url)

	// Try to invalidate the specific URL path first
	err = s.cdnClient.InvalidateCache(ctx, urlPath)
	if err != nil {
		// Log the error but don't fail the upload
		zlog.Error(ctx, "Failed to invalidate CDN cache for specific club banner URL", err,
			zap.String("path", urlPath))
	}

	return &models.File{
		Name:        file.Filename,
		Content:     string(content),
		ContentType: file.ContentType,
		URL:         url,
	}, nil
}
