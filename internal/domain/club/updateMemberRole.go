package club

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) UpdateMemberRole(ctx context.Context, clubID, userID primitive.ObjectID, role models.ClubMemberRole) (*models.ClubMember, error) {
	if clubID == primitive.NilObjectID || userID == primitive.NilObjectID {
		return nil, fmt.Errorf("club ID and user ID cannot be empty")
	}

	currentUserId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	memberInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, currentUserId, clubID)

	if memberInfo != nil && memberInfo.Role == models.ClubMemberRoleAdmin {
		err := s.clubMemberRepo.UpdateMemberRole(ctx, clubID, userID, role.String())
		if err != nil {
			return nil, err
		}

		return memberInfo, nil
	}

	if err != nil {
		return nil, err
	}

	return nil, fmt.<PERSON><PERSON><PERSON>("you're not the Admin so you can't update member role")
}
