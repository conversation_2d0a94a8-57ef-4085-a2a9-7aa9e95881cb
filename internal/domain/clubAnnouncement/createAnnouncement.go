package clubAnnouncement

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) CreateClubAnnouncement(ctx context.Context, input models.CreateClubAnnouncementInput) (*models.ClubAnnouncement, error) {
	if input.ClubID == primitive.NilObjectID {
		return nil, fmt.Errorf("club ID cannot be empty")
	}

	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	clubInfo, err := s.clubRepo.FindClubByID(ctx, input.ClubID)

	if err != nil || clubInfo == nil {
		return nil, fmt.Errorf("club not found %w", err)
	}

	memberInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, userId, input.ClubID)
	if err != nil {
		return nil, err
	}

	if input.Content == "" || input.Title == "" {
		return nil, fmt.Errorf("title and content are required")
	}

	if len(input.Title) > 100 {
		return nil, fmt.Errorf("title cannot exceed 100 characters")
	}

	if len(input.Content) > 500 {
		return nil, fmt.Errorf("content cannot exceed 500 characters")
	}

	if memberInfo != nil && memberInfo.Role == models.ClubMemberRoleAdmin {
		announcementInfo := &models.ClubAnnouncement{
			ID:        primitive.NewObjectID(),
			ClubID:    input.ClubID,
			Title:     input.Title,
			Content:   input.Content,
			CreatedAt: time.Now(),
			CreatedBy: userId,
		}
		err = s.clubAnnouncementRepo.CreateAnnouncement(ctx, announcementInfo)
		if err != nil {
			return nil, err
		}
		return announcementInfo, nil
	}

	return nil, fmt.Errorf("you're not the admin so you can not make announcements")
}
