package clubAnnouncement

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) DeleteClubAnnouncement(ctx context.Context, id primitive.ObjectID) (bool, error) {
	if id == primitive.NilObjectID {
		return false, fmt.Errorf("announcement ID cannot be empty")
	}
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	announcementInfo, err := s.clubAnnouncementRepo.FindAnnouncementByID(ctx, id)
	if err != nil {
		return false, fmt.Errorf("announcement not found: %w", err)
	}

	memberInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, userId, announcementInfo.ClubID)
	if err != nil {
		return false, err
	}

	if memberInfo != nil && memberInfo.Role == models.ClubMemberRoleAdmin {
		err = s.clubAnnouncementRepo.DeleteAnnouncement(ctx, id)
		if err != nil {
			return false, err
		}
		return true, nil
	}

	return false, fmt.Errorf("you're not the admin so you can't delete announcements")
}
