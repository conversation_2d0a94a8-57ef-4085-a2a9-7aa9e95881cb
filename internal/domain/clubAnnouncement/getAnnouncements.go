package clubAnnouncement

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const DefaultPageSize = 20

func (s *service) ClubAnnouncements(ctx context.Context, page, pageSize *int, clubID *primitive.ObjectID, from, to *time.Time) (*models.ClubAnnouncementsPage, error) {
	_, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	if page == nil {
		page = utils.AllocPtr(1)
	}

	if pageSize == nil {
		pageSize = utils.AllocPtr(DefaultPageSize)
	}

	filter := bson.M{}

	if clubID != nil && *clubID != primitive.NilObjectID {
		filter["clubId"] = clubID
	}

	timeFilter := bson.M{}
	if from != nil {
		timeFilter["$gte"] = *from
	}
	if to != nil {
		timeFilter["$lte"] = *to
	}
	if len(timeFilter) > 0 {
		filter["createdAt"] = timeFilter
	}

	skip := (*page - 1) * (*pageSize)
	opts := options.Find().SetLimit(int64(*pageSize)).SetSkip(int64(skip))
	opts.SetSort(bson.D{{Key: "createdAt", Value: -1}, {Key: "_id", Value: 1}})

	var announcements []*models.ClubAnnouncement
	if clubID != nil {
		announcements, err = s.clubAnnouncementRepo.ListAnnouncements(ctx, *clubID, opts)
		if err != nil {
			return nil, err
		}
	} else {
		announcements, err = s.clubAnnouncementRepo.Find(ctx, filter, opts)
		if err != nil {
			return nil, err
		}
	}

	count, err := s.clubAnnouncementRepo.Count(ctx, filter)
	if err != nil {
		return nil, err
	}

	totalCount := int(count)
	hasMore := (skip + len(announcements)) < totalCount

	return &models.ClubAnnouncementsPage{
		Results:      announcements,
		PageNumber:   *page,
		PageSize:     *pageSize,
		HasMore:      &hasMore,
		TotalResults: &totalCount,
	}, nil
}
