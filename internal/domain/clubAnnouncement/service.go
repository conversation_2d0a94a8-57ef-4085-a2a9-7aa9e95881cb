package clubAnnouncement

import (
	"context"

	"go.uber.org/fx"
	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

type service struct {
	clubRepo             repository.ClubRepository
	clubAnnouncementRepo repository.ClubAnnouncementRepository
	clubMemberRepo       repository.ClubMemberRepository
}

func NewClubAnnouncementsService(lc fx.Lifecycle, repositoryFactory *repository.RepositoryFactory,
) domain.ClubAnnouncementStore {
	s := &service{
		clubRepo:             repositoryFactory.ClubRepository,
		clubAnnouncementRepo: repositoryFactory.ClubAnnouncementRepository,
		clubMemberRepo:       repositoryFactory.ClubMemberRepository,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting club announcements service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down club announcements service")
			return nil
		},
	})

	return s
}
