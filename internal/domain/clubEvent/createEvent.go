package clubEvent

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/questions/questionsGenerator"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

const ContestTimeDuration = 8 * 60

func (s *service) CreateClubEvent(ctx context.Context, input models.CreateClubEventInput) (*models.ClubEvent, error) {
	if input.ClubID == primitive.NilObjectID {
		return nil, fmt.Errorf("club ID cannot be empty")
	}
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	clubInfo, err := s.clubRepo.FindClubByID(ctx, input.ClubID)

	if err != nil || clubInfo == nil {
		return nil, fmt.Errorf("club not found %w", err)
	}

	memberInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, userId, input.ClubID)
	if err != nil {
		return nil, err
	}

	if input.Description != nil && len(*input.Description) > 500 {
		return nil, fmt.Errorf("description cannot exceed 500 characters")
	}

	if input.Title == "" {
		return nil, fmt.Errorf("title and content are required")
	}

	if len(input.Title) > 100 {
		return nil, fmt.Errorf("title cannot exceed 100 characters")
	}

	clubEventPlayId := primitive.NewObjectID()

	if memberInfo != nil && memberInfo.Role == models.ClubMemberRoleAdmin {
		var playerSetting *models.PlayerSetting

		if input.PlayerSetting != nil {
			playerSetting = &models.PlayerSetting{
				MinRating: input.PlayerSetting.MinRating,
				MaxRating: input.PlayerSetting.MaxRating,
			}
		}
		var gameConfig *models.GameConfig
		if input.GameConfig != nil {
			gameConfig = &models.GameConfig{
				TimeLimit:          &input.GameConfig.TimeLimit,
				NumPlayers:         &input.GameConfig.NumPlayers,
				QuestionTags:       input.GameConfig.QuestionTags,
				GameType:           input.GameConfig.GameType,
				DifficultyLevel:    input.GameConfig.DifficultyLevel,
				MaxTimePerQuestion: input.GameConfig.MaxTimePerQuestion,
			}
		}
		eventInfo := &models.ClubEvent{
			ID:                 primitive.NewObjectID(),
			ClubID:             input.ClubID,
			ParticipationCount: 0,
			ClubEventPlayID:    clubEventPlayId,
			Title:              input.Title,
			Description: func() *string {
				if input.Description != nil {
					return input.Description
				}
				return nil
			}(),
			ClubEventType: input.ClubEventType,
			StartTime:     input.StartTime,
			CreatedBy:     userId,
			Visibility:    input.Visibility,
			PlayerSetting: playerSetting,
			RatedEvent:    input.RatedEvent,
			GameConfig:    gameConfig,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}

		err = s.clubEventRepo.CreateEvent(ctx, eventInfo)
		if err != nil {
			return nil, err
		}

		if input.ClubEventType == models.ClubEventTypeContest80In8 {
			_, err = s.CreateContestForClub(ctx, input.ClubID, input, clubEventPlayId)
			if err != nil {
				return nil, err
			}
		}

		if input.ClubEventType == models.ClubEventTypeSumdayShowdown {
			_, err = s.CreateShowdownForClub(ctx, input.ClubID, input, clubEventPlayId)
			if err != nil {
				return nil, err
			}
		}

		return eventInfo, nil
	}

	return nil, fmt.Errorf("not authorized to create an event")
}

func (s *service) CreateContestForClub(ctx context.Context, clubID primitive.ObjectID, input models.CreateClubEventInput, clubEventPlayId primitive.ObjectID) (*models.Contest, error) {
	clubInfo, err := s.clubRepo.FindClubByID(ctx, clubID)
	if err != nil {
		return nil, err
	}

	questions := questionsGenerator.GenerateOptiver80Questions()

	contest := &models.Contest{
		ID:     clubEventPlayId,
		ClubID: utils.AllocPtr(clubID),
		Name:   input.Title,
		Description: func() *string {
			if input.Description != nil {
				return input.Description
			}
			return nil
		}(),
		StartTime: input.StartTime,
		EndTime:   input.StartTime.Add(time.Duration(time.Hour)),
		HostedBy:  &clubInfo.Name,
		HostedByV2: &models.HostDetails{
			Name: &clubInfo.Name,
			Logo: clubInfo.LogoImage,
		},
		Details: &models.ContestDetails{
			About:        utils.AllocPtr("About"),
			Requirements: utils.AllocPtr("Requirements"),
			Instructions: utils.AllocPtr("Instructions"),
		},
		ContestDuration:       ContestTimeDuration,
		RegistrationStartTime: utils.AllocPtr(time.Now()),
		RegistrationEndTime:   utils.AllocPtr(input.StartTime.Add(time.Duration(time.Hour))),
		Status:                models.ContestStatusRegistrationOpen,
		CreatedAt:             time.Now(),
		UpdatedAt:             time.Now(),
		RegistrationForm: &models.RegistrationForm{
			ID:        primitive.NewObjectID(),
			Fields:    make([]*models.FormField, 0),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Questions:         questions,
		RegistrationCount: utils.AllocPtr(0),
	}

	err = s.contestRepo.Create(ctx, contest)
	if err != nil {
		return nil, err
	}

	return contest, err
}

func (s *service) CreateShowdownForClub(ctx context.Context, clubID primitive.ObjectID, input models.CreateClubEventInput, clubEventPlayId primitive.ObjectID) (*models.Showdown, error) {
	if input.GameConfig == nil {
		return nil, fmt.Errorf("GameConfig is required for Showdown events")
	}

	description := "Showdown for " + input.Title

	if input.Description != nil {
		description = *input.Description
	}

	if len(description) > 500 {
		return nil, fmt.Errorf("description cannot exceed 500 characters")
	}
	showDown := &models.Showdown{
		ID:                    utils.AllocPtr(clubEventPlayId),
		ClubID:                utils.AllocPtr(clubID),
		Name:                  input.Title,
		Description:           description,
		Rounds:                7,
		StartTime:             input.StartTime,
		EndTime:               input.StartTime.Add(time.Duration(input.GameConfig.TimeLimit) * time.Second),
		RegistrationStartTime: utils.AllocPtr(time.Now()),
		RegistrationEndTime:   utils.AllocPtr(input.StartTime),
		Status:                models.ShowdownContestStatusRegistrationOpen,
		GapBwRounds:           30,
		RoundTime:             60,
		CreatedAt:             time.Now(),
		UpdatedAt:             time.Now(),
		RoundConfig: models.RoundConfig{
			GameDuration: input.GameConfig.TimeLimit,
			NumOfPlayer:  input.GameConfig.NumPlayers,
			GameType:     input.GameConfig.GameType,
			MaxGapBwGame: 20,
			MaxWaitTime:  20,
			NumOfGames:   3,
		},
		RegistrationCount: utils.AllocPtr(0),
	}

	_, err := s.showdownRepo.CreateNewShowdown(ctx, showDown)
	if err != nil {
		return nil, err
	}

	return showDown, nil
}
