package clubEvent

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) DeleteClubEvent(ctx context.Context, id primitive.ObjectID) (bool, error) {
	if id == primitive.NilObjectID {
		return false, fmt.Errorf("event ID cannot be empty")
	}
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	eventInfo, err := s.clubEventRepo.FindEventByID(ctx, id)
	if err != nil {
		return false, fmt.Errorf("event not found: %w", err)
	}

	memberInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, userId, eventInfo.ClubID)
	if err != nil {
		return false, err
	}

	if memberInfo != nil && memberInfo.Role == models.ClubMemberRoleAdmin {
		err = s.clubEventRepo.DeleteEvent(ctx, id)
		if err != nil {
			return false, err
		}
		return true, nil
	}

	return false, fmt.Errorf("you're not the Admin so you can't delete events")
}
