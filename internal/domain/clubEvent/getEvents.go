package clubEvent

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) ClubEvent(ctx context.Context, id primitive.ObjectID) (*models.ClubEvent, error) {
	if id == primitive.NilObjectID {
		return nil, fmt.Errorf("event ID cannot be empty")
	}
	eventInfo, err := s.clubEventRepo.FindEventByID(ctx, id)
	if err != nil {
		return eventInfo, err
	}

	return eventInfo, nil
}

func (s *service) ClubEvents(ctx context.Context, page, pageSize *int, clubID *primitive.ObjectID, eventType *models.ClubEventType, from, to *time.Time) (*models.ClubEventsPage, error) {
	_, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if page == nil {
		page = utils.AllocPtr(1)
	}

	if pageSize == nil {
		pageSize = utils.AllocPtr(20)
	}

	filter := bson.M{}

	if clubID != nil && *clubID != primitive.NilObjectID {
		filter["clubId"] = clubID
	}

	if eventType != nil {
		filter["clubEventType"] = *eventType
	}

	timeFilter := bson.M{}
	if from != nil {
		timeFilter["$gte"] = *from
	}
	if to != nil {
		timeFilter["$lte"] = *to
	}
	if len(timeFilter) > 0 {
		filter["startTime"] = timeFilter
	}

	skip := (*page - 1) * (*pageSize)
	opts := options.Find().SetLimit(int64(*pageSize)).SetSkip(int64(skip))
	opts.SetSort(bson.D{{Key: "startTime", Value: 1}, {Key: "_id", Value: 1}})

	var events []*models.ClubEvent
	if clubID != nil {
		events, err = s.clubEventRepo.ListEvents(ctx, *clubID, opts)
		if err != nil {
			return nil, err
		}
	} else {
		events, err = s.clubEventRepo.Find(ctx, filter, opts)
		if err != nil {
			return nil, err
		}
	}

	count, err := s.clubEventRepo.Count(ctx, filter)
	if err != nil {
		return nil, err
	}

	totalCount := int(count)
	hasMore := (skip + len(events)) < totalCount

	return &models.ClubEventsPage{
		Results:      events,
		PageNumber:   *page,
		PageSize:     *pageSize,
		HasMore:      &hasMore,
		TotalResults: &totalCount,
	}, nil
}
