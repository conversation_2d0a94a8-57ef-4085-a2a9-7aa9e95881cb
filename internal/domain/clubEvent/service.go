package clubEvent

import (
	"context"

	"go.uber.org/fx"
	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

type service struct {
	clubRepo       repository.ClubRepository
	clubEventRepo  repository.ClubEventRepository
	clubMemberRepo repository.ClubMemberRepository
	contestRepo    repository.ContestRepository
	showdownRepo   repository.ShowdownRepository
}

func NewClubEventsService(lc fx.Lifecycle, repositoryFactory *repository.RepositoryFactory) domain.ClubEventStore {
	s := &service{
		clubRepo:       repositoryFactory.ClubRepository,
		clubEventRepo:  repositoryFactory.ClubEventRepository,
		clubMemberRepo: repositoryFactory.ClubMemberRepository,
		contestRepo:    repositoryFactory.ContestRepository,
		showdownRepo:   repositoryFactory.ShowdownRepository,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting club events service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down club events service")
			return nil
		},
	})

	return s
}
