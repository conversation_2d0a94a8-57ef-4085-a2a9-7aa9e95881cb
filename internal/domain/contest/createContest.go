package contest

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/questions/questionsGenerator"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) CreateContest(ctx context.Context, input models.CreateContestInput) (*models.Contest, error) {
	currentTime := time.Now().UTC()

	newRegistrationForm, err := createRegistrationForm(input.RegistrationForm)
	if err != nil {
		return nil, err
	}

	var defTime time.Time

	if input.RegistrationStartTime == defTime {
		input.RegistrationStartTime = currentTime
	}
	if input.RegistrationEndTime == defTime {
		input.RegistrationEndTime = input.StartTime
	}

	isRegistrationStarted := (input.RegistrationStartTime).Before(currentTime)

	// Generate Optiver-like 80 questions
	questions := questionsGenerator.GenerateOptiver80Questions()

	hostDetails := &models.HostDetails{
		Name: &input.HostName,
		Logo: &input.HostLogo,
	}
	// Create the contest
	newContest := &models.Contest{
		ID:                    primitive.NewObjectID(),
		Name:                  input.Name,
		Description:           &input.Description,
		HostedBy:              &input.HostName,
		HostedByV2:            hostDetails,
		StartTime:             input.StartTime,
		EndTime:               input.EndTime,
		ContestDuration:       input.ContestDuration,
		RegistrationStartTime: &input.RegistrationStartTime,
		RegistrationEndTime:   &input.RegistrationEndTime,
		RegistrationCount:     utils.AllocPtr(0),
		Status:                models.ContestStatusUpcoming,
		RegistrationForm:      newRegistrationForm,
		Questions:             questions,
		CreatedAt:             time.Now(),
		UpdatedAt:             time.Now(),
	}

	if isRegistrationStarted {
		newContest.Status = models.ContestStatusRegistrationOpen
	}

	newContest.Details = &models.ContestDetails{
		About:        &input.Details.About,
		Requirements: &input.Details.Requirements,
		Instructions: &input.Details.Instructions,
	}

	err = s.contestRepo.Create(ctx, newContest)
	if err != nil {
		return nil, err
	}

	return newContest, nil
}

func createRegistrationForm(input models.RegistrationFormInput) (*models.RegistrationForm, error) {
	now := time.Now()
	form := &models.RegistrationForm{
		ID:        primitive.NewObjectID(),
		Fields:    make([]*models.FormField, len(input.Fields)),
		CreatedAt: now,
		UpdatedAt: now,
	}

	for i, field := range input.Fields {
		validation := &models.FieldValidation{
			Regex:         &field.Validation.Regex,
			Min:           &field.Validation.Min,
			Max:           &field.Validation.Max,
			EmailSuffix:   field.Validation.EmailSuffix,
			EmailSuffixes: field.Validation.EmailSuffixes,
		}

		form.Fields[i] = &models.FormField{
			ID:         primitive.NewObjectID(),
			Name:       field.Name,
			Type:       field.Type,
			Label:      field.Label,
			Required:   field.Required,
			Options:    field.Options,
			Validation: validation,
		}
	}

	return form, nil
}
