package contest

import (
	"context"
	"errors"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

func (s *service) GetContestByID(ctx context.Context, contestID primitive.ObjectID) (*models.Contest, error) {
	zlog.Debug(ctx, "Fetching contest", zap.String("contestID", contestID.Hex()))

	contest, err := s.contestRepo.FindByID(ctx, contestID)
	if err != nil || contest == nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zlog.Warn(ctx, "Contest not found", zap.String("contestID", contestID.Hex()))
			return nil, fmt.Errorf("contest not found")
		}
		zlog.Error(ctx, "Failed to fetch contest", err, zap.String("contestID", contestID.Hex()), zap.Error(err))
		return nil, fmt.Errorf("failed to fetch contest: %w", err)
	}

	if err := s.updateContestStatus(ctx, contest); err != nil {
		zlog.Warn(ctx, "Failed to update contest status", zap.String("contestID", contestID.Hex()), zap.Error(err))
	}

	currentTime := time.Now()
	isContestStarted := contest.StartTime.Sub(currentTime) <= 60*time.Second

	recentParticipants, err := s.getRecentParticipants(ctx, contest.ID)
	if err != nil {
		zlog.Warn(ctx, "Failed to get recent participants", zap.String("contestID", contestID.Hex()), zap.Error(err))
	}
	contest.RecentParticipants = make([]*models.UserPublicDetails, len(recentParticipants))
	for i, participant := range recentParticipants {
		contest.RecentParticipants[i] = utils.GetUserPublicDetails(participant)
	}

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Warn(ctx, "User not authenticated", zap.Error(err))
		return contest, nil
	}

	participant, err := s.participantRepo.GetByContestAndUser(ctx, contestID, userID)
	if err != nil {
		if !errors.Is(err, mongo.ErrNoDocuments) {
			zlog.Error(ctx, "Failed to fetch participant", err, zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
		}
	} else if participant != nil {
		contest.CurrentUserParticipation = &models.CurrentUserParticipation{
			ContestID:          &contestID,
			UserID:             &userID,
			RegistrationData:   participant.RegistrationData,
			Score:              &participant.Score,
			LastSubmissionTime: participant.LastSubmissionTime,
		}
		zlog.Debug(ctx, "Participant found", zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
	}

	if !isContestStarted {
		contest.Questions = nil
	}

	if err := s.addEncryptedQuestions(contest); err != nil {
		return nil, err
	}

	zlog.Debug(ctx, "Contest fetched successfully", zap.String("contestID", contestID.Hex()))
	return contest, nil
}
