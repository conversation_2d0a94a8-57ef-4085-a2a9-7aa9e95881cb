// Path: /goserver/internal/domain/contest/getContestLeaderboard.go

package contest

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type aggregationResult struct {
	Participants []struct {
		ID                  primitive.ObjectID `bson:"_id"`
		Score               int                `bson:"score"`
		StartTime           time.Time          `bson:"startTime"`
		LastSubmissionTime  time.Time          `bson:"lastSubmissionTime"`
		CorrectSubmission   int                `bson:"correctSubmission"`
		IncorrectSubmission int                `bson:"incorrectSubmission"`
		User                struct {
			ID              primitive.ObjectID `bson:"_id"`
			Name            string             `bson:"name"`
			Username        string             `bson:"username"`
			ProfileImageURL string             `bson:"profileImageUrl"`
			Rating          int                `bson:"rating"`
			Badge           models.BadgeType   `bson:"badge"`
			GlobalRank      int                `bson:"globalRank"`
		} `bson:"user"`
	} `bson:"participants"`
	TotalParticipants []struct {
		Count int `bson:"count"`
	} `bson:"totalParticipants"`
}

func (s *service) GetContestLeaderboard(ctx context.Context, contestID primitive.ObjectID, pageNumber, pageSize *int) (*models.ContestLeaderboard, error) {
	zlog.Info(ctx, "Fetching contest leaderboard", zap.String("contestID", contestID.Hex()))

	if pageNumber == nil {
		defaultPageNumber := 1
		pageNumber = &defaultPageNumber
	}
	if pageSize == nil {
		defaultPageSize := 20
		pageSize = &defaultPageSize
	}

	zlog.Debug(ctx, "Leaderboard pagination details",
		zap.Int("pageNumber", *pageNumber),
		zap.Int("pageSize", *pageSize))

	skip := (*pageNumber - 1) * *pageSize

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"contestId":            contestID,
			"isVirtualParticipant": bson.M{"$ne": true},
		}}},
		{{Key: "$facet", Value: bson.M{
			"participants": bson.A{
				bson.M{"$addFields": bson.M{
					"timeDifference": bson.M{"$subtract": bson.A{"$lastSubmissionTime", "$startTime"}},
				}},
				bson.M{"$sort": bson.D{
					{Key: "score", Value: -1},
					{Key: "timeDifference", Value: 1},
				}},
				bson.M{"$skip": skip},
				bson.M{"$limit": *pageSize},
				bson.M{"$lookup": bson.M{
					"from":         "users",
					"localField":   "userId",
					"foreignField": "_id",
					"as":           "user",
				}},
				bson.M{"$unwind": "$user"},
				bson.M{"$project": bson.M{
					"_id":                  1,
					"score":                1,
					"startTime":            1,
					"lastSubmissionTime":   1,
					"correctSubmission":    1,
					"incorrectSubmission":  1,
					"user._id":             1,
					"user.name":            1,
					"user.username":        1,
					"user.profileImageUrl": 1,
					"user.rating":          1,
					"user.badge":           1,
					"user.globalRank":      1,
				}},
			},
			"totalParticipants": bson.A{
				bson.M{"$match": bson.M{"isVirtualParticipant": bson.M{"$ne": true}}},
				bson.M{"$count": "count"},
			},
		}}},
	}

	cursor, err := s.participantRepo.AggregateProjected(ctx, pipeline)
	if err != nil {
		zlog.Error(ctx, "Failed to execute aggregation",
			err,
			zap.String("contestID", contestID.Hex()))
		return nil, fmt.Errorf("failed to execute aggregation: %w", err)
	}
	defer cursor.Close(ctx)

	var result []aggregationResult
	if err := cursor.All(ctx, &result); err != nil {
		zlog.Error(ctx, "Failed to decode aggregation result",
			err,
			zap.String("contestID", contestID.Hex()))
		return nil, fmt.Errorf("failed to decode aggregation result: %w", err)
	}

	if len(result) == 0 || len(result[0].Participants) == 0 {
		return &models.ContestLeaderboard{
			Participants:      []*models.ContestParticipant{},
			TotalParticipants: 0,
		}, nil
	}

	totalParticipants := 0
	if len(result[0].TotalParticipants) > 0 {
		totalParticipants = result[0].TotalParticipants[0].Count
	}

	zlog.Debug(ctx, "Aggregation result details",
		zap.Int("participantsCount", len(result[0].Participants)),
		zap.Int("totalParticipants", totalParticipants))

	leaderboard := &models.ContestLeaderboard{
		Participants:      make([]*models.ContestParticipant, len(result[0].Participants)),
		TotalParticipants: totalParticipants,
	}

	for i, p := range result[0].Participants {
		leaderboard.Participants[i] = &models.ContestParticipant{
			ID:                  p.ID,
			Score:               p.Score,
			StartTime:           &p.StartTime,
			LastSubmissionTime:  &p.LastSubmissionTime,
			CorrectSubmission:   &p.CorrectSubmission,
			IncorrectSubmission: &p.IncorrectSubmission,
			Rank:                utils.AllocPtr(skip + i + 1),
			User: &models.UserPublicDetails{
				ID:              p.User.ID,
				Name:            &p.User.Name,
				Username:        p.User.Username,
				ProfileImageURL: &p.User.ProfileImageURL,
				Rating:          &p.User.Rating,
				Badge:           &p.User.Badge,
				GlobalRank:      &p.User.GlobalRank,
			},
		}
	}

	zlog.Info(ctx, "Successfully fetched contest leaderboard",
		zap.String("contestID", contestID.Hex()),
		zap.Int("participantsCount", len(leaderboard.Participants)),
		zap.Int("totalParticipants", leaderboard.TotalParticipants))

	return leaderboard, nil
}
