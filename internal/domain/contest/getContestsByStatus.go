package contest

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

func (s *service) GetContestsByStatus(ctx context.Context, statuses []models.ContestStatus, page, pageSize *int, sortDirection *string) (*models.PaginatedContests, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if page == nil {
		defaultPage := 1
		page = &defaultPage
	}
	if pageSize == nil {
		defaultPageSize := 20
		pageSize = &defaultPageSize
	}

	defaultSortDirection := "DESC"
	if sortDirection == nil {
		sortDirection = &defaultSortDirection
	}

	skip := (*page - 1) * *pageSize

	sortOrder := -1
	if *sortDirection == "ASC" {
		sortOrder = 1
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"status": bson.M{"$in": statuses},
			"$or": []bson.M{
				{"clubId": bson.M{"$exists": false}},
				{"clubId": nil},
			},
		}}},
		{{Key: "$facet", Value: bson.M{
			"totalCount": bson.A{
				bson.M{"$count": "count"},
			},
			"contests": bson.A{
				bson.M{"$sort": bson.M{"startTime": sortOrder}},
				bson.M{"$skip": skip},
				bson.M{"$limit": *pageSize},
				bson.M{"$lookup": bson.M{
					"from": "contestparticipants",
					"pipeline": bson.A{
						bson.M{"$match": bson.M{"contestId": "$contestId", "userId": "$userId"}},
						bson.M{"$addFields": bson.M{
							"timeDifference": bson.M{"$subtract": bson.A{"$lastSubmissionTime", "$startTime"}},
						}},
						bson.M{"$sort": bson.D{
							{Key: "score", Value: -1},
							{Key: "timeDifference", Value: 1},
						}},
						bson.M{"$limit": 3},
						bson.M{"$lookup": bson.M{
							"from":         "users",
							"localField":   "userId",
							"foreignField": "_id",
							"as":           "user",
						}},
						bson.M{"$unwind": "$user"},
						bson.M{"$project": bson.M{
							"_id":             "$user._id",
							"name":            "$user.name",
							"profileImageUrl": "$user.profileImageUrl",
							"rating":          "$user.rating",
							"badge":           "$user.badge",
							"globalRank":      "$user.globalRank",
						}},
					},
					"as": "recentParticipants",
				}},
			},
		}}},
		{{Key: "$project", Value: bson.M{
			"totalCount": bson.M{"$arrayElemAt": bson.A{"$totalCount.count", 0}},
			"contests":   "$contests",
		}}},
	}

	cursor, err := s.contestRepo.AggregateProjected(ctx, pipeline)
	if err != nil {
		zlog.Error(ctx, "Failed to fetch contests", err)
		return nil, fmt.Errorf("failed to fetch contests: %w", err)
	}

	var result []*models.PaginatedContests

	if err := cursor.All(ctx, &result); err != nil {
		zlog.Error(ctx, "Failed to decode contests", err)
		return nil, fmt.Errorf("failed to decode contests: %w", err)
	}

	zlog.Debug(ctx, "Contests fetched successfully", zap.Int("count", len(result[0].Contests)))

	var contestIds []*models.ObjectID
	for _, contest := range result[0].Contests {
		contestIds = append(contestIds, &contest.ID)
	}

	if len(contestIds) == 0 {
		return result[0], nil
	}

	participants, err := s.participantRepo.GetParticipantByUserAndContest(ctx, contestIds, userId)
	if err != nil {
		zlog.Error(ctx, "Failed to get participants", err)
		return nil, fmt.Errorf("failed to get participants: %w", err)
	}

	if len(participants) == 0 {
		return result[0], nil
	}

	participantsMap := map[primitive.ObjectID]*models.ContestParticipant{}
	for _, participant := range participants {
		if participant == nil {
			continue
		}
		participantsMap[participant.ContestID] = participant
	}

	for _, contest := range result[0].Contests {
		if contest == nil || contest.ID == primitive.NilObjectID {
			continue
		}

		if participant, ok := participantsMap[contest.ID]; ok && participant != nil {
			var currentUserParticipant models.CurrentUserParticipation
			err := MapContestParticipantToCurrentParticipant(participant, &currentUserParticipant)
			if err != nil {
				zlog.Error(ctx, "Failed to map participant to currentUserParticipant", err)
			}
			contest.CurrentUserParticipation = &currentUserParticipant

		}
	}

	return result[0], nil
}

func MapContestParticipantToCurrentParticipant(participant *models.ContestParticipant, currentParticipant *models.CurrentUserParticipation) error {
	if participant == nil {
		return fmt.Errorf("participant is nil")
	}
	if currentParticipant == nil {
		return fmt.Errorf("current participant is nil")
	}
	currentParticipant.ContestID = &participant.ContestID
	currentParticipant.UserID = &participant.UserID
	currentParticipant.Score = &participant.Score
	return nil
}
