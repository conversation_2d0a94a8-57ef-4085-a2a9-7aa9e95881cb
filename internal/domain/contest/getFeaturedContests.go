package contest

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func (s *service) GetFeaturedContests(ctx context.Context) ([]*models.Contest, error) {
	featuredContestStatus := []models.ContestStatus{
		models.ContestStatusOngoing,
	}

	featuredContests, err := s.GetContestsByStatus(ctx, featuredContestStatus, utils.AllocPtr(1), utils.AllocPtr(10), utils.AllocPtr("ASC"))
	if err != nil {
		return nil, err
	}

	currentTime := time.Now().UTC()
	for _, contest := range featuredContests.Contests {
		if err := s.updateContestStatus(ctx, contest); err != nil {
			zlog.Error(ctx, "Failed to update contest status", err)
		}

		isContestStarted := contest.StartTime.Sub(currentTime) <= 60*time.Second

		recentParticipants, err := s.getRecentParticipants(ctx, contest.ID)
		if err != nil {
			zlog.Error(ctx, "Failed to get recent participants", err)
		}

		participantPublicDetails := make([]*models.UserPublicDetails, len(recentParticipants))
		for i, participant := range recentParticipants {
			participantPublicDetails[i] = utils.GetUserPublicDetails(participant)
		}

		contest.RecentParticipants = participantPublicDetails
		if err := s.addEncryptedQuestions(contest); err != nil {
			return nil, err
		}

		if !isContestStarted {
			contest.Questions = nil
		}
	}

	return featuredContests.Contests, nil
}
