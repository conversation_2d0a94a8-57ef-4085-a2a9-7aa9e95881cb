package contest

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/zap"
)

func (s *service) GetRegisteredContests(ctx context.Context) ([]*models.Contest, error) {
	zlog.Debug(ctx, "Fetching registered contests")

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Failed to get user from context", err)
		return nil, fmt.Errorf("unauthorized: %w", err)
	}

	participants, err := s.participantRepo.GetByUserID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to fetch user's contest participations", err, zap.String("userID", userID.Hex()))
		return nil, fmt.<PERSON><PERSON><PERSON>("failed to fetch user's contest participations: %w", err)
	}

	var registeredContests []*models.Contest
	currentTime := time.Now()

	for _, participant := range participants {
		contest, err := s.contestRepo.FindByID(ctx, participant.ContestID)
		if err != nil {
			zlog.Warn(ctx, "Failed to fetch contest", zap.Error(err), zap.String("contestID", participant.ContestID.Hex()))
			continue
		}

		if err := s.updateContestStatus(ctx, contest); err != nil {
			zlog.Warn(ctx, "Failed to update contest status", zap.Error(err), zap.String("contestID", contest.ID.Hex()))
		}

		isContestStarted := currentTime.After(contest.StartTime) || currentTime.Equal(contest.StartTime)

		if !isContestStarted {
			contest.Questions = nil
		}

		contest.CurrentUserParticipation = &models.CurrentUserParticipation{
			ContestID:          &contest.ID,
			UserID:             &userID,
			RegistrationData:   participant.RegistrationData,
			Score:              &participant.Score,
			LastSubmissionTime: participant.LastSubmissionTime,
			StartTime:          participant.StartTime,
		}

		registeredContests = append(registeredContests, contest)
	}

	zlog.Info(ctx, "Successfully fetched registered contests", zap.Int("count", len(registeredContests)))
	return registeredContests, nil
}
