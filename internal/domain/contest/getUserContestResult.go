package contest

import (
	"context"
	"errors"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) GetUserContestResult(ctx context.Context, contestID primitive.ObjectID) (*models.UserContestResult, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("unauthorized: user not authenticated")
	}

	participant, err := s.participantRepo.GetByContestAndUser(ctx, contestID, userID)

	if err != nil || participant == nil {
		return &models.UserContestResult{
			IsVirtualParticipant: utils.AllocPtr(false),
		}, nil
	}

	contest, err := s.contestRepo.FindByID(ctx, contestID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errors.New("contest not found")
		}
		return nil, err
	}

	totalParticipants, err := s.participantRepo.Count(ctx, bson.M{"contestId": contestID})
	if err != nil {
		return nil, err
	}

	timeTaken := (time.Duration(contest.ContestDuration) * time.Second).Milliseconds()
	if participant.StartTime != nil && participant.LastSubmissionTime != nil {
		timeTaken = participant.LastSubmissionTime.Sub(*participant.StartTime).Milliseconds()
	}

	participantsWithBetterScore, err := s.participantRepo.Count(ctx, bson.M{
		"contestId":            contestID,
		"isVirtualParticipant": bson.M{"$ne": true},
		"$or": bson.A{
			bson.M{"score": bson.M{"$gt": participant.Score}},
			bson.M{
				"score": participant.Score,
				"$expr": bson.M{
					"$lt": bson.A{bson.M{"$subtract": bson.A{"$lastSubmissionTime", "$startTime"}}, timeTaken},
				},
			},
		},
	})
	if err != nil {
		return nil, err
	}

	user, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	isVirtualParticipant := false

	if participant.IsVirtualParticipant != nil {
		isVirtualParticipant = *participant.IsVirtualParticipant
	}

	userRank := participantsWithBetterScore + 1

	if participantsWithBetterScore == 0 {
		userRank = 1
	}

	return &models.UserContestResult{
		StartTime:            participant.StartTime,
		CorrectSubmission:    participant.CorrectSubmission,
		IncorrectSubmission:  participant.IncorrectSubmission,
		TotalScore:           float64(participant.Score),
		LastSubmissionTime:   participant.LastSubmissionTime,
		User:                 utils.GetUserPublicDetails(user),
		Rank:                 utils.AllocPtr(int(userRank)),
		QuestionsSolved:      participant.CorrectSubmission,
		TotalParticipants:    utils.AllocPtr(int(totalParticipants)),
		IsVirtualParticipant: &isVirtualParticipant,
	}, nil
}
