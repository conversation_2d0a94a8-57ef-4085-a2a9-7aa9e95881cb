package contest

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) GetUserContestSubmissions(ctx context.Context, userID *primitive.ObjectID, contestID primitive.ObjectID) (*models.UserContestSubmissions, error) {
	authenticatedUserID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Failed to get user from context", err)
		return nil, fmt.Errorf("unauthorized: %w", err)
	}

	if userID == nil {
		userID = &authenticatedUserID
	}
	zlog.Debug(ctx, "Fetching user contest submissions", zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))

	participant, err := s.participantRepo.GetByContestAndUser(ctx, contestID, *userID)
	if err != nil {
		zlog.Error(ctx, "Failed to fetch participant", err, zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
		return nil, fmt.Errorf("failed to fetch participant: %w", err)
	}

	contest, err := s.contestRepo.FindByID(ctx, contestID)
	if err != nil {
		zlog.Error(ctx, "Failed to fetch contest", err, zap.String("contestID", contestID.Hex()))
		return nil, fmt.Errorf("failed to fetch contest: %w", err)
	}

	hasContestEnded := time.Now().After(contest.EndTime)

	submissions := participant.Submissions
	if !hasContestEnded && *userID != authenticatedUserID {
		submissions = nil
	}

	user, err := s.userService.GetUserByID(ctx, *userID)
	if err != nil {
		zlog.Error(ctx, "Failed to fetch user", err, zap.String("userID", userID.Hex()))
		return nil, fmt.Errorf("failed to fetch user: %w", err)
	}

	userContestSubmissions := &models.UserContestSubmissions{
		TotalScore:          float64(participant.Score),
		StartTime:           participant.StartTime,
		LastSubmissionTime:  participant.LastSubmissionTime,
		CorrectSubmission:   participant.CorrectSubmission,
		IncorrectSubmission: participant.IncorrectSubmission,
		Submissions:         submissions,
		User:                utils.GetUserPublicDetails(user),
	}

	zlog.Info(ctx, "Successfully fetched user contest submissions", zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
	return userContestSubmissions, nil
}
