package contest

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) JoinVirtualContest(ctx context.Context, contestID primitive.ObjectID) (bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, fmt.Errorf("unauthorized: user not authenticated")
	}

	_, err = s.contestRepo.FindByID(ctx, contestID)
	if err != nil {
		return false, fmt.Errorf("failed to fetch contest: %w", err)
	}

	existingParticipant, err := s.participantRepo.GetByContestAndUser(ctx, contestID, userID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return false, fmt.Errorf("failed to check existing participation: %w", err)
	}

	if existingParticipant != nil && existingParticipant.IsVirtualParticipant == nil {
		if existingParticipant.StartTime == nil {
			existingParticipant.StartTime = utils.AllocPtr(time.Now())
			existingParticipant.IsVirtualParticipant = utils.AllocPtr(true)
			err = s.participantRepo.UpdateOne(ctx, bson.M{"_id": existingParticipant.ID},
				bson.M{"$set": bson.M{
					"startTime":            existingParticipant.StartTime,
					"isVirtualParticipant": existingParticipant.IsVirtualParticipant,
				}})
			// err = s.participantRepo.Update(ctx, existingParticipant)
			return err == nil, err
		}
		return false, errors.New("user has already participated in this contest")
	}

	newParticipant := &models.ContestParticipant{
		ID:                   primitive.NewObjectID(),
		UserID:               userID,
		ContestID:            contestID,
		Score:                0,
		StartTime:            utils.AllocPtr(time.Now()),
		CorrectSubmission:    utils.AllocPtr(0),
		IncorrectSubmission:  utils.AllocPtr(0),
		IsVirtualParticipant: utils.AllocPtr(true),
		CreatedAt:            time.Now(),
		UpdatedAt:            time.Now(),
	}

	err = s.participantRepo.Create(ctx, newParticipant)
	return err == nil, err
}
