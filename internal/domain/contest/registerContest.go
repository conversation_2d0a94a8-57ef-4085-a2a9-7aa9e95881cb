package contest

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) RegisterForContest(ctx context.Context, input models.RegistrationFormValuesInput) (bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Failed to get user from context", err)
		return false, fmt.Errorf("unauthorized: %w", err)
	}

	contestID := input.ContestID

	contest, err := s.contestRepo.FindByID(ctx, contestID)
	if err != nil {
		zlog.Error(ctx, "Failed to fetch contest", err, zap.String("contestID", contestID.Hex()))
		return false, fmt.Errorf("failed to fetch contest: %w", err)
	}

	if contest.ClubID != nil {
		clubMembershipInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, userID, *contest.ClubID)
		if err != nil {
			zlog.Error(ctx, "Failed to fetch club membership info", err, zap.String("contestID", contestID.Hex()))
			return false, fmt.Errorf("failed to fetch club membership info: %w", err)
		}
		if clubMembershipInfo == nil {
			zlog.Warn(ctx, "User is not a member of the club", zap.String("contestID", contestID.Hex()))
			return false, fmt.Errorf("user is not a member of the club")
		}
	}

	currentTime := time.Now()
	if (contest.RegistrationStartTime != nil && currentTime.Before(*contest.RegistrationStartTime)) || currentTime.After(contest.EndTime) {
		zlog.Warn(ctx, "Registration is not open", zap.String("contestID", contestID.Hex()))
		return false, fmt.Errorf("registration is not open for this contest")
	}

	if contest.RegistrationForm == nil {
		zlog.Warn(ctx, "Registration form not found", zap.String("contestID", contestID.Hex()))
		return false, fmt.Errorf("registration form not found")
	}

	isValid := validateFormData(input.FormData, contest.RegistrationForm.Fields)
	if !isValid {
		zlog.Warn(ctx, "Invalid form data", zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("invalid form data")
	}

	zlog.Debug(ctx, "Checking for existing participant", zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))

	existingParticipant, err := s.participantRepo.GetByContestAndUser(ctx, contestID, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to check for existing participant", err, zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("failed to check for existing participant: %w", err)
	}
	if existingParticipant != nil {
		zlog.Warn(ctx, "Participant already registered", zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("you have already registered for this contest")
	}

	registrationData := make([]*models.RegistrationFieldData, len(input.FormData))
	for i, field := range input.FormData {
		registrationData[i] = &models.RegistrationFieldData{
			Name:   field.Name,
			Values: field.Values,
		}
	}
	newParticipant := &models.ContestParticipant{
		ID:                   primitive.NewObjectID(),
		UserID:               userID,
		ContestID:            contestID,
		RegistrationData:     registrationData,
		Score:                0,
		CreatedAt:            currentTime,
		UpdatedAt:            currentTime,
		IsVirtualParticipant: utils.AllocPtr(false),
	}

	err = s.participantRepo.Create(ctx, newParticipant)
	if err != nil {
		zlog.Error(ctx, "Failed to create participant", err, zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("failed to create participant: %w", err)
	}

	err = s.contestRepo.IncrementRegistrationCount(ctx, contestID)
	if err != nil {
		zlog.Error(ctx, "Failed to update contest registration count", err, zap.String("contestID", contestID.Hex()))
		return false, fmt.Errorf("failed to update contest registration count: %w", err)
	}

	if contest.ClubID != nil {
		err = s.clubEventRepo.IncrementEventParticipationCount(ctx, *contest.ClubID, contestID)
		if err != nil {
			zlog.Error(ctx, "Failed to update club event participation count", err, zap.String("contestID", contestID.Hex()))
			return false, fmt.Errorf("failed to update club event participation count: %w", err)
		}
	}

	zlog.Info(ctx, "Successfully registered participant", zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
	return true, nil
}

func validateFormData(formData []*models.RegistrationFormFieldValueInput, formFields []*models.FormField) bool {
	for _, field := range formFields {
		formFieldSubmission := findFormFieldSubmission(formData, field.Name)
		if formFieldSubmission == nil {
			if field.Required {
				return false
			}
			continue
		}
		// TODO: Add more validation rules based on field type and validation properties
	}
	return true
}

func findFormFieldSubmission(formData []*models.RegistrationFormFieldValueInput, fieldName string) *models.RegistrationFormFieldValueInput {
	for _, field := range formData {
		if field.Name == fieldName {
			return field
		}
	}
	return nil
}
