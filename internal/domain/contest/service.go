package contest

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type service struct {
	contestRepo     repository.ContestRepository
	participantRepo repository.ContestParticipantRepository
	clubMemberRepo  repository.ClubMemberRepository
	clubEventRepo   repository.ClubEventRepository
	userService     domain.UserStore
}

func NewContestService(lc fx.Lifecycle, repositoryFactory *repository.RepositoryFactory, userService domain.UserStore) domain.ContestStore {
	s := &service{
		contestRepo:     repositoryFactory.ContestRepository,
		participantRepo: repositoryFactory.ParticipantRepository,
		userService:     userService,
		clubMemberRepo:  repositoryFactory.ClubMemberRepository,
		clubEventRepo:   repositoryFactory.ClubEventRepository,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting contest service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down contest service")
			return nil
		},
	})

	return s
}
