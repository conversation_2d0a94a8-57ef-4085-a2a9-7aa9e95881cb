package contest

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

const (
	DELAY_ALLOWED = 500 * time.Millisecond
)

func (s *service) SubmitContestAnswer(ctx context.Context, contestID primitive.ObjectID, questionID, answer string) (bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Failed to get user from context", err)
		return false, fmt.Errorf("unauthorized: %w", err)
	}

	contest, err := s.contestRepo.FindByID(ctx, contestID)
	if err != nil {
		zlog.Error(ctx, "Failed to fetch contest", err, zap.String("contestID", contestID.Hex()))
		return false, fmt.Errorf("failed to fetch contest: %w", err)
	}

	// if contest.Status != models.ContestStatusOngoing {
	// 	zlog.Warn(ctx, "Attempted submission for non-ongoing contest", zap.String("contestID", contestID.Hex()), zap.String("status", string(contest.Status)))
	// 	return false, fmt.Errorf("contest is not in progress")
	// }

	participant, err := s.participantRepo.GetByContestAndUser(ctx, contestID, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to fetch participant", err, zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("failed to fetch participant: %w", err)
	}

	questionEntry := findQuestionByID(contest.Questions, questionID)
	if questionEntry == nil {
		zlog.Warn(ctx, "Question not found in contest", zap.String("contestID", contestID.Hex()), zap.String("questionID", questionID))
		return false, fmt.Errorf("question not found in this contest")
	}

	if hasSubmittedAnswer(participant.Submissions, questionID) {
		zlog.Info(ctx, "Participant already submitted answer", zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()), zap.String("questionID", questionID))
		return false, nil
	}

	now := time.Now()
	if !isSubmissionTimeValid(now, contest, participant) {
		zlog.Warn(ctx, "Submission time invalid", zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("contest has ended for this participant")
	}

	isCorrect := isAnswerCorrect(questionEntry, answer)
	earnedPoints := calculatePoints(isCorrect, now, participant.StartTime, questionEntry.Points)

	updateParticipantStats(participant, isCorrect, earnedPoints, now)

	newSubmission := createNewSubmission(questionID, answer, isCorrect, earnedPoints, now)
	participant.Submissions = append(participant.Submissions, &newSubmission)

	err = s.participantRepo.UpdateOne(ctx, bson.M{"_id": participant.ID}, bson.M{"$set": participant})
	if err != nil {
		zlog.Error(ctx, "Failed to update participant", err, zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("failed to update participant: %w", err)
	}

	zlog.Info(ctx, "Contest answer submitted successfully",
		zap.String("contestID", contestID.Hex()),
		zap.String("userID", userID.Hex()),
		zap.String("questionID", questionID),
		zap.Bool("isCorrect", isCorrect),
		zap.Float64("earnedPoints", earnedPoints))

	return isCorrect, nil
}

func findQuestionByID(questions []*models.ContestQuestion, questionID string) *models.ContestQuestion {
	for _, q := range questions {
		if *q.Question.ID == questionID {
			return q
		}
	}
	return nil
}

func hasSubmittedAnswer(submissions []*models.ContestSubmission, questionID string) bool {
	for _, submission := range submissions {
		if submission.QuestionID == questionID {
			return true
		}
	}
	return false
}

func isSubmissionTimeValid(now time.Time, contest *models.Contest, participant *models.ContestParticipant) bool {
	if participant.StartTime == nil {
		participant.StartTime = &now
		return true
	}
	participantEndTime := participant.StartTime.Add(time.Duration(contest.ContestDuration) * time.Second)
	effectiveEndTime := participantEndTime

	if participant.IsVirtualParticipant == nil || !*participant.IsVirtualParticipant {
		effectiveEndTime = minTime(participantEndTime, contest.EndTime)
	}

	return now.Before(effectiveEndTime.Add(DELAY_ALLOWED))
}

func isAnswerCorrect(question *models.ContestQuestion, answer string) bool {
	for _, correctAnswer := range question.Question.Answers {
		if correctAnswer == answer {
			return true
		}
	}
	return false
}

func calculatePoints(isCorrect bool, now time.Time, startTime *time.Time, maxPoints int) float64 {
	if !isCorrect || startTime == nil {
		return 0.0
	}
	return 4.0
	// timeTaken := now.Sub(*startTime).Seconds()
	// return math.Max(0, float64(maxPoints)-math.Floor(math.Max(0, timeTaken)))
}

func updateParticipantStats(participant *models.ContestParticipant, isCorrect bool, earnedPoints float64, now time.Time) {
	participant.Score += int(earnedPoints)
	if isCorrect {
		if participant.CorrectSubmission == nil {
			participant.CorrectSubmission = utils.AllocPtr(1)
		} else {
			*participant.CorrectSubmission++
		}
		participant.LastSubmissionTime = &now
	} else {
		if participant.IncorrectSubmission == nil {
			participant.IncorrectSubmission = utils.AllocPtr(1)
		} else {
			*participant.IncorrectSubmission++
		}
	}
}

func createNewSubmission(questionID, answer string, isCorrect bool, earnedPoints float64, now time.Time) models.ContestSubmission {
	return models.ContestSubmission{
		QuestionID:     questionID,
		Answer:         answer,
		IsCorrect:      isCorrect,
		Points:         earnedPoints,
		SubmissionTime: now,
	}
}

func minTime(a, b time.Time) time.Time {
	if a.Before(b) {
		return a
	}
	return b
}
