package contest

import (
	"context"
	"errors"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) SubmitVirtualContestAnswer(ctx context.Context, contestID primitive.ObjectID, questionID, answer string) (bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, fmt.Errorf("unauthorized: user not authenticated")
	}

	participant, err := s.participantRepo.GetByContestAndUser(ctx, contestID, userID)
	if err != nil {
		return false, fmt.Errorf("failed to fetch participant: %w", err)
	}

	if participant.IsVirtualParticipant == nil {
		return false, fmt.Errorf("Not a Virtual Participant")
	}

	if !*participant.IsVirtualParticipant {
		return false, errors.New("this is not a virtual participation")
	}

	return s.SubmitContestAnswer(ctx, contestID, questionID, answer)
}
