package contest

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) UnregisterFromContest(ctx context.Context, contestID primitive.ObjectID) (bool, error) {
	zlog.Debug(ctx, "Unregistering from contest", zap.String("contestID", contestID.Hex()))

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Failed to get user from context", err)
		return false, fmt.Errorf("unauthorized: %w", err)
	}

	contest, err := s.contestRepo.FindByID(ctx, contestID)
	if err != nil {
		zlog.Error(ctx, "Failed to fetch contest", err, zap.String("contestID", contestID.Hex()))
		return false, fmt.Errorf("failed to fetch contest: %w", err)
	}

	if contest.ClubID != nil {
		clubMembershipInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, userID, *contest.ClubID)
		if err != nil {
			zlog.Error(ctx, "Failed to fetch club membership info", err, zap.String("contestID", contestID.Hex()))
			return false, fmt.Errorf("failed to fetch club membership info: %w", err)
		}
		if clubMembershipInfo == nil {
			zlog.Warn(ctx, "User is not a member of the club", zap.String("contestID", contestID.Hex()))
			return false, fmt.Errorf("user is not a member of the club")
		}
	}

	currentTime := time.Now()
	if currentTime.After(contest.StartTime) {
		zlog.Warn(ctx, "Attempted to unregister after contest start", zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("contest has already started. You cannot unregister at this time")
	}

	deleted, err := s.participantRepo.DeleteByContestAndUser(ctx, contestID, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to delete participant", err, zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("failed to unregister from contest: %w", err)
	}
	if !deleted {
		zlog.Warn(ctx, "Participant not found", zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("you are not registered for this contest")
	}

	err = s.contestRepo.DecrementRegistrationCount(ctx, contestID)
	if err != nil {
		zlog.Error(ctx, "Failed to update contest registration count", err, zap.String("contestID", contestID.Hex()))
		return false, fmt.Errorf("failed to update contest registration count: %w", err)
	}

	if contest.ClubID != nil {
		err = s.clubEventRepo.DecrementEventParticipationCount(ctx, *contest.ClubID, contestID)
		if err != nil {
			zlog.Error(ctx, "Failed to update club event participation count", err, zap.String("contestID", contestID.Hex()))
			return false, fmt.Errorf("failed to update club event participation count: %w", err)
		}
	}

	zlog.Info(ctx, "Successfully unregistered from contest", zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
	return true, nil
}
