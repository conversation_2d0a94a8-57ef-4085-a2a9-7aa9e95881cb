package contest

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) UpdateContestParticipantStartTime(ctx context.Context, contestID primitive.ObjectID) (bool, error) {
	zlog.Debug(ctx, "Updating contest participant start time", zap.String("contestID", contestID.Hex()))

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Failed to get user from context", err)
		return false, fmt.Errorf("unauthorized: %w", err)
	}

	contest, err := s.contestRepo.FindByID(ctx, contestID)
	if err != nil {
		zlog.Error(ctx, "Failed to fetch contest", err, zap.String("contestID", contestID.Hex()))
		return false, fmt.Errorf("failed to fetch contest: %w", err)
	}

	now := time.Now()
	if now.Before(contest.StartTime) {
		zlog.Warn(ctx, "Attempted to update start time before contest start", zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("contest has not started yet")
	}

	participant, err := s.participantRepo.GetByContestAndUser(ctx, contestID, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to fetch participant", err, zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("failed to fetch participant: %w", err)
	}

	if participant.StartTime != nil {
		zlog.Info(ctx, "Participant start time already set", zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
		return true, nil
	}

	participant.StartTime = &now
	err = s.participantRepo.UpdateOne(ctx, bson.M{"_id": participant.ID}, bson.M{"$set": bson.M{"startTime": participant.StartTime}})
	if err != nil {
		zlog.Error(ctx, "Failed to update participant start time", err, zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("failed to update participant start time: %w", err)
	}

	zlog.Info(ctx, "Successfully updated participant start time", zap.String("contestID", contestID.Hex()), zap.String("userID", userID.Hex()))
	return true, nil
}
