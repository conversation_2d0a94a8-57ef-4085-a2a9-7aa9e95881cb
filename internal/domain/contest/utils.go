package contest

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils/encryption"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) updateContestStatus(ctx context.Context, contest *models.Contest) error {
	currentTime := time.Now().UTC()
	if contest == nil {
		return fmt.Errorf("contest not found")
	}
	newStatus := contest.Status

	if currentTime.After(contest.StartTime) && currentTime.Before(contest.EndTime) {
		newStatus = models.ContestStatusOngoing
	} else if currentTime.After(contest.EndTime) {
		newStatus = models.ContestStatusEnded
	} else if currentTime.After(*contest.RegistrationStartTime) && currentTime.Before(*contest.RegistrationEndTime) {
		newStatus = models.ContestStatusRegistrationOpen
	} else if currentTime.Before(*contest.RegistrationStartTime) {
		newStatus = models.ContestStatusUpcoming
	}

	if newStatus != contest.Status {
		contest.Status = newStatus
		contest.UpdatedAt = time.Now()
		err := s.contestRepo.Update(ctx, bson.M{"_id": contest.ID}, bson.M{"$set": bson.M{"status": newStatus, "updatedAt": contest.UpdatedAt}})
		if err != nil {
			zlog.Error(ctx, "Failed to update contest status", err, zap.String("contestID", contest.ID.Hex()))
		}
		return err
	}

	return nil
}

func (s *service) getRecentParticipants(ctx context.Context, contestID primitive.ObjectID) ([]*models.User, error) {
	participants, err := s.participantRepo.GetTopParticipantsByContest(ctx, contestID, 3)
	if err != nil {
		return nil, err
	}

	var recentParticipants []*models.User
	for _, participant := range participants {
		user, err := s.userService.GetUserByID(ctx, participant.UserID)
		if err != nil {
			zlog.Error(ctx, "Failed to fetch user", err, zap.String("userID", participant.UserID.Hex()))
			continue
		}
		recentParticipants = append(recentParticipants, user)
	}

	return recentParticipants, nil
}

func (s *service) addEncryptedQuestions(contest *models.Contest) error {
	var encryptedQuestions []*string
	for _, question := range contest.Questions {
		encQ, err := encryption.EncryptObject(question)
		if err != nil {
			return err
		}
		encryptedQuestions = append(encryptedQuestions, &encQ)
	}
	contest.EncryptedQuestions = encryptedQuestions
	return nil
}
