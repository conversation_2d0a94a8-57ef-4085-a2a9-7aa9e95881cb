package core

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/infrastructure/firebase"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/infrastructure/websocket"
	"matiksOfficial/matiks-server-go/pkg/config"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type service struct {
	feedRepository     repository.FeedRepository
	feedDataRepository repository.FeedDataRepository
	friendRepository   repository.FriendsAndFollowersRepository
	userRepository     repository.UserRepository
	firebaseClient     firebase.FirebaseClient
	userCache          cache.UserCache
	ws                 websocket.Websocket
}

func NewCoreService(
	lc fx.Lifecycle,
	cfg *config.Config,
	repositoryFactory *repository.RepositoryFactory,
	firebaseClient firebase.FirebaseClient,
	cacheInstance cache.Cache,
	wsInstance websocket.Websocket,
) domain.CoreLogicStore {
	s := &service{
		feedRepository:     repositoryFactory.FeedRepository,
		friendRepository:   repositoryFactory.FriendsAndFollowersRepository,
		feedDataRepository: repositoryFactory.FeedDataRepository,
		userRepository:     repositoryFactory.UserRepository,
		firebaseClient:     firebaseClient,
		userCache:          cache.NewUserCacheWrapper(cacheInstance),
		ws:                 wsInstance,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting core service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down core service")
			return nil
		},
	})

	return s
}
