package core

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

func (s *service) GetUsersByIDs(ctx context.Context, userIds []primitive.ObjectID) ([]*models.User, error) {
	usersFromDB, err := s.userRepository.GetUsersByIDs(ctx, userIds)
	if err != nil {
		zlog.Error(ctx, "Failed to get users", err)
		return nil, err
	}
	return usersFromDB, nil
}

// TODO: implement cache
// func (s *service) GetUsersByIDs(ctx context.Context, userIds []primitive.ObjectID) ([]*models.User, error) {
// 	keys := make([]string, len(userIds))
// 	for i, id := range userIds {
// 		keys[i] = id.Hex()
// 	}
// 	usersFromCache, err := s.userCache.GetUsersByIDs(ctx, keys)
// 	if err != nil {
// 		zlog.Error(ctx, "Failed to get users", err)
// 		return nil, err
// 	}
// 	var users map[primitive.ObjectID]*models.User = make(map[primitive.ObjectID]*models.User)
// 	var idsToFetch []primitive.ObjectID
// 	for i, id := range userIds {
// 		if usersFromCache[i] == nil {
// 			idsToFetch = append(idsToFetch, id)
// 		} else {
// 			users[id] = usersFromCache[i]
// 		}
// 	}
// 	if len(idsToFetch) > 0 {
// 		usersFromDB, err := s.userRepository.GetUsersByIDs(ctx, idsToFetch)
// 		if err != nil {
// 			zlog.Error(ctx, "Failed to get users", err)
// 			return nil, err
// 		}
// 		for _, user := range usersFromDB {
// 			if user == nil {
// 				continue
// 			}
// 			users[user.ID] = user
// 		}
// 	}

// 	var result []*models.User
// 	for _, id := range userIds {
// 		userObj, ok := users[id]
// 		if !ok {
// 			zlog.Error(ctx, "User not found", systemErrors.ErrUserNotFound, zap.String("id", id.Hex()))
// 			return nil, systemErrors.ErrUserNotFound
// 		}
// 		result = append(result, userObj)
// 	}
// 	return result, nil
// }

// TODO: implement cache
func (s *service) GetUserByID(ctx context.Context, userId primitive.ObjectID) (*models.User, error) {
	if userId.IsZero() {
		zlog.Error(ctx, "User id is nil", systemErrors.ErrUserNil)
		return nil, systemErrors.ErrUserNil
	}
	// userFromCache, err := s.userCache.GetUser(ctx, userId.Hex())
	// if err != nil || userFromCache == nil {
	user, err := s.userRepository.GetByID(ctx, userId)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zlog.Error(ctx, "User not found", systemErrors.ErrUserNotFound, zap.String("id", userId.Hex()))
			return nil, systemErrors.ErrUserNotFound
		}
		zlog.Error(ctx, "Failed to get user", err)
		return nil, err
	}
	if user == nil {
		zlog.Error(ctx, "User not found", systemErrors.ErrUserNotFound, zap.String("id", userId.Hex()))
		return nil, systemErrors.ErrUserNotFound
	}
	// s.userCache.SetUser(ctx, userId.Hex(), user)
	return user, nil
	// }

	// return userFromCache, nil
}

// func (s *service) UpdateUser(ctx context.Context, user *models.User) (err error) {
// 	if user == nil {
// 		zlog.Error(ctx, "CORE:UpdateUser: User not found", systemErrors.ErrUserNil)
// 		return systemErrors.ErrUserNil
// 	}
// 	err = s.userRepository.UpdateOne(ctx, )
// 	return nil
// }

func (s *service) PublishUserEvent(ctx context.Context, userID primitive.ObjectID, event models.UserEvent) error {
	if event == nil {
		zlog.Error(ctx, "Event is nil", systemErrors.ErrUserEventNil)
		return systemErrors.ErrUserEventNil
	}
	channel := fmt.Sprintf("%s_%s", constants.SubscriptionPrefixEnum.USER_EVENT, userID.Hex())
	zlog.Debug(ctx, "Publishing user event", zap.String("channel", channel), zap.String("eventType", event.GetEventType().String()))

	eventWithType := struct {
		Type  string          `json:"type"`
		Event json.RawMessage `json:"event"`
	}{
		Type: event.GetEventType().String(),
	}

	eventJSON, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal event: %w", err)
	}
	eventWithType.Event = eventJSON

	err = s.ws.Publish(ctx, channel, eventWithType)
	if err != nil {
		return fmt.Errorf("failed to publish event: %w", err)
	}

	return nil
}
