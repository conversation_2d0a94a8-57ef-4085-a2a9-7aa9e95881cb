package dailyChallenge

import (
	"context"
	"errors"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

func (s *service) AttemptDailyChallenge(ctx context.Context, challengeID primitive.ObjectID) (bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, fmt.Errorf("unauthorized: user not authenticated")
	}

	challenge, err := s.dailyChallengeRepo.FindByID(ctx, challengeID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, fmt.Errorf("challenge not found for challengeId : %s", challengeID.Hex())
		}
		return false, err
	}
	zlog.Info(ctx, "Attempting daily challenge 1", zap.String("challengeId", challengeID.Hex()))

	// Check if the user has already attempted or completed this challenge
	existingResult, err := s.dailyChallengeResultRepo.FindOne(ctx, bson.M{"userId": userID, "challengeId": challengeID})
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zlog.Info(ctx, "could not find the document, hence making entry", zap.String("challengeId", challengeID.Hex()))
		} else {
			return false, err
		}
	}
	if existingResult != nil {
		zlog.Info(ctx, "found already attempted or completed this challenge", zap.String("challengeId", challengeID.Hex()))
		return false, fmt.Errorf("you have already attempted or completed this challenge")
	}

	zlog.Info(ctx, "could not find the document, hence making entry", zap.String("challengeId", challengeID.Hex()))
	// Create a new DailyChallengeResult with ATTEMPTED status
	tempNewResult := models.DailyChallengeResult{
		UserID:          userID,
		ChallengeID:     &challengeID,
		ChallengeNumber: &challenge.ChallengeNumber,
		ResultStatus:    models.ResultStatusAttempted,
		Division:        challenge.Division,
	}

	err = s.dailyChallengeResultRepo.Create(ctx, &tempNewResult)
	if err != nil {
		return false, fmt.Errorf("failed to record challenge attempt")
	}

	// Update UserDailyChallengeStats
	err = s.updateUserDailyChallengeAttemptStats(ctx, userID, challenge.Division)
	if err != nil {
		zlog.Error(ctx, "Failed to update UserDailyChallengeStats", err)
	}

	err = s.updateDailyChallengeAttemptStat(ctx, challengeID)
	if err != nil {
		zlog.Error(ctx, "Failed to update DailyChallenge.Stats", err)
	}

	return true, nil
}
