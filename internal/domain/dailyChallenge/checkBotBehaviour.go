package dailyChallenge

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) CheckBotBehavior(ctx context.Context, challengeID, userID primitive.ObjectID) (*models.BotDetectionResult, error) {
	result, err := s.dailyChallengeResultRepo.FindOne(ctx, bson.M{
		"challengeId": challengeID,
		"userId":      userID,
	})
	if err != nil {
		return nil, err
	}

	if result.ChallengeID == nil {
		return &models.BotDetectionResult{
			IsBotBehavior: false,
			UserID:        userID,
			ChallengeID:   challengeID,
		}, nil
	}

	intSubmittedTimes := make([]int, 0, len(result.SubmittedTimes))
	for _, t := range result.SubmittedTimes {
		if t != nil {
			intSubmittedTimes = append(intSubmittedTimes, *t)
		}
	}

	detection, err := s.botDetectionService.DetectBotBehavior(ctx, userID, &challengeID, intSubmittedTimes)
	if err != nil {
		return nil, err
	}

	return &models.BotDetectionResult{
		IsBotBehavior: detection != nil,
		UserID:        userID,
		ChallengeID:   challengeID,
	}, nil
}
