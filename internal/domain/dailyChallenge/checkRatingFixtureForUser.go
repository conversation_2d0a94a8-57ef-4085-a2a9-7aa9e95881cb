package dailyChallenge

import (
	"context"
	"errors"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) CheckRatingFixtureForUser(ctx context.Context, userID primitive.ObjectID, score int, submissions []*int) {
	user, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return
	}

	if score == 0 || (user.HasFixedRating != nil && *user.HasFixedRating) {
		return
	}

	timeTakenInSec := score / 1000
	newRating := utils.GetRatingBasedOnFixtureTest(int(timeTakenInSec), 20)

	if user.Rating != nil && newRating <= *user.Rating {
		return
	}

	_, err = s.userRatingFixtureSubmissionRepo.FindByUserID(ctx, userID)
	if !errors.Is(err, mongo.ErrNoDocuments) {
		return
	}

	userRatingFixtureSubmission := &models.UserRatingFixtureSubmission{
		ID:             primitive.NewObjectID(),
		UserID:         userID,
		Submissions:    submissions,
		UserScore:      20,
		TimeTaken:      int(timeTakenInSec),
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
		CurrentRating:  *user.Rating,
		ProposedRating: newRating,
		UserStance:     models.UserStancePending,
	}

	_, err = s.userRatingFixtureSubmissionRepo.InsertOne(ctx, userRatingFixtureSubmission)
	if err != nil {
		return
	}

	err = s.coreService.PublishUserEvent(ctx, userID, models.RatingFixtureOutput{
		NewRating: &newRating,
	})
	if err != nil {
		return
	}
}
