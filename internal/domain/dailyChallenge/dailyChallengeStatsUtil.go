package dailyChallenge

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/models"

	"matiksOfficial/matiks-server-go/internal/domain/utils"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func getEmptyUserDailyChallengeStats(userID primitive.ObjectID, division models.ChallengeDivision) *models.UserDailyChallengeStats {
	return &models.UserDailyChallengeStats{
		UserID:          userID,
		Division:        division,
		TotalAttempts:   0,
		TotalSubmission: 0,
		AverageTime:     60 * 60 * 1000,
		BestTime:        60 * 60 * 1000,
	}
}

func (s *service) updateUserDailyChallengeAttemptStats(ctx context.Context, userID primitive.ObjectID, division models.ChallengeDivision) error {
	// Find existing stats or create new ones
	stats, err := s.userDailyChallengeStatsRepo.FindByUserIDAndDivision(ctx, userID, division)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			stats = getEmptyUserDailyChallengeStats(userID, division)
		} else {
			return err
		}
	}

	stats.TotalAttempts++

	if stats.ID.IsZero() {
		err = s.userDailyChallengeStatsRepo.Create(ctx, stats)
	} else {
		err = s.userDailyChallengeStatsRepo.Update(ctx, stats)
	}

	return err
}

func (s *service) updateUserDailyChallengeSubmissionStats(ctx context.Context, userID primitive.ObjectID, division models.ChallengeDivision, score int) error {
	// Find existing stats or create new ones
	stats, err := s.userDailyChallengeStatsRepo.FindByUserIDAndDivision(ctx, userID, division)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			stats = getEmptyUserDailyChallengeStats(userID, division)
		} else {
			return err
		}
	}

	stats.TotalSubmission++

	// Update streaks
	now := time.Now()
	if stats.Streaks == nil {
		stats.Streaks = &models.UserDailyChallengeStreaks{}
	}
	if stats.Streaks.LastPlayedDate == nil || now.Sub(*stats.Streaks.LastPlayedDate).Hours() > 24 {
		stats.Streaks.Current = 1
	} else {
		stats.Streaks.Current++
	}
	if stats.Streaks.Current > stats.Streaks.Highest {
		stats.Streaks.Highest = stats.Streaks.Current
	}
	stats.Streaks.LastPlayedDate = &now

	totalSubmissions := stats.TotalSubmission
	newAvg := int((stats.AverageTime*(totalSubmissions-1) + score) / totalSubmissions)
	stats.AverageTime = newAvg

	// Update best time
	if score < stats.BestTime {
		stats.BestTime = score
	}

	// Save or update the stats
	if stats.ID.IsZero() {
		err = s.userDailyChallengeStatsRepo.Create(ctx, stats)
	} else {
		err = s.userDailyChallengeStatsRepo.Update(ctx, stats)
	}

	return err
}

func (s *service) updateDailyChallengeAttemptStat(ctx context.Context, challengeID primitive.ObjectID) error {
	challenge, err := s.dailyChallengeRepo.FindByID(ctx, challengeID)
	if err != nil {
		return err
	}

	if challenge.Stats == nil {
		challenge.Stats = &models.DailyChallengeStat{
			TotalAttempts:   utils.AllocPtr(0),
			TotalSubmission: utils.AllocPtr(0),
			AverageTime:     nil,
			BestTime:        nil,
			AverageAccuracy: utils.AllocPtr(1.0),
		}
	}

	if challenge.Stats.TotalAttempts == nil {
		challenge.Stats.TotalAttempts = utils.AllocPtr(1)
	} else {
		*challenge.Stats.TotalAttempts++
	}

	return s.dailyChallengeRepo.Update(ctx, challenge)
}

func (s *service) updateDailyChallengeSubmissionStats(ctx context.Context, challengeID primitive.ObjectID, score int) error {
	challenge, err := s.dailyChallengeRepo.FindByID(ctx, challengeID)
	if err != nil {
		return err
	}

	if challenge.Stats == nil {
		challenge.Stats = &models.DailyChallengeStat{
			TotalAttempts:   utils.AllocPtr(0),
			TotalSubmission: utils.AllocPtr(0),
			AverageTime:     nil,
			BestTime:        nil,
			AverageAccuracy: utils.AllocPtr(1.0),
		}
	}

	if challenge.Stats.TotalSubmission == nil {
		challenge.Stats.TotalSubmission = utils.AllocPtr(1)
	} else {
		*challenge.Stats.TotalSubmission++
	}

	// Update average time
	if challenge.Stats.AverageTime == nil {
		challenge.Stats.AverageTime = utils.AllocPtr(score)
	} else {
		totalSubmissions := int(*challenge.Stats.TotalSubmission)
		newAvg := int((*challenge.Stats.AverageTime*(totalSubmissions-1) + score) / totalSubmissions)
		challenge.Stats.AverageTime = &newAvg
	}

	// Update best time
	if challenge.Stats.BestTime == nil {
		challenge.Stats.BestTime = utils.AllocPtr(score)
	} else if score < *challenge.Stats.BestTime {
		*challenge.Stats.BestTime = score
	}

	return s.dailyChallengeRepo.Update(ctx, challenge)
}
