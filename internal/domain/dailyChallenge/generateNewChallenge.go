package dailyChallenge

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/questions/questionsGenerator"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

const DailyChallengeQuestions = 20

func (s *service) GenerateNewChallenge(ctx context.Context, division models.ChallengeDivision) (*models.DailyChallenge, error) {
	now := time.Now()
	startTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)
	endTime := startTime.Add(24 * time.Hour)

	// Check if a challenge already exists for this start time
	existingChallenge, err := s.dailyChallengeRepo.FindOne(ctx, bson.M{"startTime": startTime, "division": division})
	if err == nil {
		zlog.Info(ctx, "Daily challenge already exists. Skipping creation.", zap.String("start time", startTime.String()))
		return existingChallenge, nil
	} else if !errors.Is(err, mongo.ErrNoDocuments) {
		zlog.Error(ctx, "Error finding existing challenge:", err)
		return nil, err
	}

	var minRating int
	var maxRating int

	switch division {
	case models.ChallengeDivisionDiv1:
		{
			minRating = 1800
			maxRating = 2500
		}
	case models.ChallengeDivisionDiv2:
		{
			minRating = 1200
			maxRating = 1800
		}
	default:
		{
			minRating = 800
			maxRating = 1400
		}
	}

	ratings := questionsGenerator.GenerateUniformRatings(minRating, maxRating, DailyChallengeQuestions)

	dailyChallengeId := primitive.NewObjectID()
	questions := make([]*models.GameQuestion, 0, DailyChallengeQuestions)
	uniqueExpressions := make(map[string]struct{})

	for i := 0; i < len(ratings); i++ {
		var question *models.Question
		unique := false
		attempts := 10
		question = questionsGenerator.GetRandomArithmeticQuestion(ratings[i], nil)

		for !unique && attempts > 0 {
			expressionKey := strings.Join(question.Expression, " ")
			if _, exists := uniqueExpressions[expressionKey]; !exists {
				unique = true
				uniqueExpressions[expressionKey] = struct{}{}
			} else {
				attempts--
				question = questionsGenerator.GetRandomArithmeticQuestion(ratings[i], nil)
			}
		}
		questionId := fmt.Sprintf("%s_%d", dailyChallengeId.Hex(), i)
		question.ID = &questionId
		questions = append(questions, &models.GameQuestion{
			Question: question,
			Stats: models.GameQuestionStats{
				FastestTime: utils.AllocPtr(60 * 60 * 1000), // 1 hour in milliseconds
				UserIds:     []*primitive.ObjectID{},
			},
		})
	}

	filter := bson.M{
		"$or": []bson.M{
			{"division": "OPEN"},
			{"division": bson.M{"$exists": false}},
			{"division": nil},
		},
	}

	if division != models.ChallengeDivisionOpen {
		filter = bson.M{"division": division.String()}
	}

	totalChallenges, err := s.dailyChallengeRepo.Count(ctx, filter)
	if err != nil {
		zlog.Error(ctx, "Error counting total challenges:", err)
		return nil, err
	}
	newChallenge := &models.DailyChallenge{
		ID:              dailyChallengeId,
		Questions:       questions,
		StartTime:       &startTime,
		EndTime:         &endTime,
		ChallengeNumber: int(totalChallenges) + 1,
		ChallengeStatus: utils.AllocPtr("RUNNING"),
		CreatedAt:       utils.AllocPtr(time.Now()),
		UpdatedAt:       utils.AllocPtr(time.Now()),
		Division:        division,
	}

	err = s.dailyChallengeRepo.Create(ctx, newChallenge)
	if err != nil {
		zlog.Error(ctx, "Error inserting new challenge:", err)
		return nil, err
	}

	zlog.Info(ctx, "New daily challenge generated", zap.String("start time", startTime.String()))
	return newChallenge, nil
}
