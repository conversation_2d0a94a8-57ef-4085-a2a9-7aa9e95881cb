package dailyChallenge

import (
	"context"
	"errors"
	"fmt"
	"math/rand/v2"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (s *service) GetDailyChallenge(ctx context.Context) (*models.DailyChallenge, error) {
	zlog.Info(ctx, "Getting Daily Challenge")
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("unauthorized: user not authenticated")
	}

	now := time.Now().UTC()
	startTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)
	endTime := startTime.Add(24 * time.Hour)

	dailyChallenge, err := s.dailyChallengeRepo.FindOne(ctx, bson.M{
		"startTime": bson.M{
			"$gte": startTime,
			"$lt":  endTime,
		},
	}, options.FindOne().SetSort(bson.M{"startTime": -1}))
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zlog.Info(ctx, "No daily challenge found for today, generating a new one")
			newChallenge, err := s.GenerateNewChallenge(ctx, models.ChallengeDivisionOpen)
			if err != nil {
				zlog.Error(ctx, "Failed to generate new challenge", err)
				return nil, fmt.Errorf("failed to generate new challenge: %w", err)
			}
			dailyChallenge = newChallenge
		} else {
			zlog.Error(ctx, "Error finding daily challenge", err)
			return nil, fmt.Errorf("error finding daily challenge: %w", err)
		}
	}

	// Shuffle the questions
	rand.Shuffle(len(dailyChallenge.Questions), func(i, j int) {
		dailyChallenge.Questions[i], dailyChallenge.Questions[j] = dailyChallenge.Questions[j], dailyChallenge.Questions[i]
	})

	_, err = s.dailyChallengeResultRepo.FindOne(ctx, bson.M{
		"userId":          userId,
		"challengeNumber": dailyChallenge.ChallengeNumber,
	})
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return nil, err
	}
	hasAttempted := !errors.Is(err, mongo.ErrNoDocuments)
	dailyChallenge.HasAttempted = &hasAttempted

	err = s.addEncryptedQuestionsInDC(dailyChallenge)
	if err != nil {
		return nil, err
	}

	return dailyChallenge, nil
}
