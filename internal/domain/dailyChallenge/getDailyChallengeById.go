package dailyChallenge

import (
	"context"
	"errors"
	"fmt"
	"math/rand/v2"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) GetDailyChallengeByID(ctx context.Context, id primitive.ObjectID) (*models.DailyChallenge, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("unauthorized: user not authenticated")
	}

	var dailyChallenge *models.DailyChallenge

	dailyChallenge, err = s.dailyChallengeRepo.FindByID(ctx, id)
	if err != nil {
		return nil, fmt.E<PERSON><PERSON>("failed to get Daily Challenge By Id")
	}

	rand.Shuffle(len(dailyChallenge.Questions), func(i, j int) {
		dailyChallenge.Questions[i], dailyChallenge.Questions[j] = dailyChallenge.Questions[j], dailyChallenge.Questions[i]
	})

	err = s.addEncryptedQuestionsInDC(dailyChallenge)
	if err != nil {
		return nil, err
	}

	_, err = s.dailyChallengeResultRepo.FindOne(ctx, bson.M{
		"userId":      userId,
		"challengeId": dailyChallenge.ID,
	})
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return nil, fmt.Errorf("failed to get Daily Challenge By Id")
	}

	hasAttempted := !errors.Is(err, mongo.ErrNoDocuments)
	dailyChallenge.HasAttempted = utils.AllocPtr(hasAttempted)

	return dailyChallenge, nil
}
