package dailyChallenge

import (
	"context"
	"errors"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

const DefaultPageSize = 20

func (s *service) GetDailyChallengeLeaderboard(ctx context.Context, challengeNumber, pageNumber, pageSize *int) (*models.LeaderboardPage, error) {
	if challengeNumber == nil {
		return nil, fmt.Errorf("challenge number is required")
	}

	if pageNumber == nil || *pageNumber == 0 {
		defaultPageNumber := 1
		pageNumber = &defaultPageNumber
	}

	if pageSize == nil {
		defaultPageSize := DefaultPageSize
		pageSize = &defaultPageSize
	}

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	bannedIDs, err := s.botDetectionService.GetShadowBannedUsers(ctx)
	if err != nil {
		zlog.Error(ctx, "Failed to get shadow banned users", err)
	}

	bannedIDsStr := []string{
		"6737985c017de8d955e41352", "670e8462f42c2b89f3a8217b", "67e1a6dcfc81f50df2a2d0d1", "67fa1e13d83744b468e2c047", "6816e279938bf4a7e6e0ecb3",
		"681b60e12fda3a0da5d7ed98", "66f45bddd93afbd829643852", "681b650b95bb0605254d49ed", "67fd154e94168d273725a420", "6738f374650cad273cd15100",
		"680a506061e082a513dac47d", "67fd82140397e7c8ed75b3dc", "680e9d3938b2e13634e248dc", "67fe38e9cb1e1aff238a63f7", "6816deda11e2a69441abc5d4",
		"6813e050a593b1bca13261f7", "6814a6eb302cb899de1dc0f8", "67fa7564f63fff1d71936cd8", "6737985c017de8d955e41352", "670f9fec50eda0cb36ce4860",
		"67092a21340114ceca88897f", "671114bf9670bbf54fa9925f", "681db78f4ea66a4648a3f8e8",
	}

	for _, bannedIDStr := range bannedIDsStr {
		bannedID, err := primitive.ObjectIDFromHex(bannedIDStr)
		if err != nil {
			return nil, err
		}
		if userID != bannedID {
			bannedIDs = append(bannedIDs, bannedID)
		}
	}

	challenge, err := s.dailyChallengeRepo.FindByChallengeNumberAndDivison(ctx, *challengeNumber, models.ChallengeDivisionOpen.String())
	if err != nil || challenge == nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return &models.LeaderboardPage{
				Results:      []models.ChallengeResult{},
				PageNumber:   *pageNumber,
				PageSize:     *pageSize,
				HasMore:      false,
				TotalResults: 0,
			}, nil
		}
		return nil, fmt.Errorf("failed to find challenge: %w", err)
	}

	skip := (*pageNumber - 1) * *pageSize

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"challengeId":  challenge.ID,
			"resultStatus": bson.M{"$ne": models.ResultStatusAttempted},
			"userId":       bson.M{"$nin": bannedIDs},
		}}},
		{{Key: "$sort", Value: bson.M{"score": 1}}},
		{{Key: "$facet", Value: bson.M{
			"metadata": bson.A{bson.M{"$count": "total"}},
			"data": bson.A{
				bson.M{"$skip": skip},
				bson.M{"$limit": *pageSize},
				bson.M{"$lookup": bson.M{
					"from":         "users",
					"localField":   "userId",
					"foreignField": "_id",
					"as":           "user",
					"pipeline": bson.A{
						bson.M{"$project": bson.M{
							"_id":             1,
							"name":            1,
							"username":        1,
							"profileImageUrl": 1,
							"rating":          1,
							"badge":           1,
							"globalRank":      1,
							"stats":           1,
							"userStreaks":     1,
						}},
					},
				}},
				bson.M{"$unwind": "$user"},
			},
		}}},
		{{Key: "$project", Value: bson.M{
			"results": bson.M{
				"$map": bson.M{
					"input": "$data",
					"as":    "result",
					"in": bson.M{
						"_id":   "$$result._id",
						"user":  "$$result.user",
						"score": "$$result.score",
						"rank":  bson.M{"$add": bson.A{skip, bson.M{"$indexOfArray": bson.A{"$data", "$$result"}}, 1}},
					},
				},
			},
			"pageNumber":   bson.M{"$literal": *pageNumber},
			"pageSize":     bson.M{"$literal": *pageSize},
			"totalResults": bson.M{"$arrayElemAt": bson.A{"$metadata.total", 0}},
		}}},
	}

	cur, err := s.dailyChallengeResultRepo.AggregateProjected(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("aggregation failed: %w", err)
	}
	defer cur.Close(ctx)

	var results []bson.M
	if err := cur.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("error decoding results: %w", err)
	}

	if len(results) == 0 || results[0]["results"] == nil {
		return &models.LeaderboardPage{
			Results:      []models.ChallengeResult{},
			PageNumber:   *pageNumber,
			PageSize:     *pageSize,
			HasMore:      false,
			TotalResults: 0,
		}, nil
	}

	resultData := results[0]
	totalResults := utils.ToInt(resultData["totalResults"])
	hasMore := totalResults > (skip + *pageSize)

	rankedResults := make([]models.ChallengeResult, 0)
	if resultsArr, ok := resultData["results"].(bson.A); ok {
		// TODO: shouldn't do always
		go s.updateRanks(context.Background(), resultsArr, skip)

		for i, result := range resultsArr {
			if resultDoc, ok := result.(bson.M); ok {
				if user, ok := resultDoc["user"].(bson.M); ok {
					rankedResults = append(rankedResults, models.ChallengeResult{
						User:  utils.ExtractUserPublicDetails(user),
						Score: utils.ToFloat64(resultDoc["score"]),
						Rank:  skip + i + 1,
					})
				}
			}
		}
	}

	return &models.LeaderboardPage{
		Results:      rankedResults,
		PageNumber:   *pageNumber,
		PageSize:     *pageSize,
		HasMore:      hasMore,
		TotalResults: totalResults,
	}, nil
}

func (s *service) updateRanks(ctx context.Context, results bson.A, skip int) {
	for i, result := range results {
		if resultDoc, ok := result.(bson.M); ok {
			err := s.dailyChallengeResultRepo.UpdateOne(
				ctx,
				bson.M{"_id": resultDoc["_id"].(primitive.ObjectID)},
				bson.M{"$set": bson.M{"rank": skip + i + 1}},
			)
			if err != nil {
				zlog.Error(ctx, "Failed to update rank", err)
			}
		}
	}
}
