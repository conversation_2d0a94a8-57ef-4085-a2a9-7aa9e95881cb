package dailyChallenge

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func (s *service) GetDailyChallenges(ctx context.Context) ([]*models.DailyChallenge, error) {
	zlog.Info(ctx, "Getting Daily Challenge")
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	user, err := s.userService.GetUserByID(ctx, userId)
	if err != nil || user == nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	dailyChallenge, err := s.getDailyChallengeByDivision(ctx, user, models.ChallengeDivisionOpen)
	if err != nil {
		return nil, fmt.Errorf("daily challenge not found: %w", err)
	}

	isGuest := user.IsGuest

	var returnValues []*models.DailyChallenge
	returnValues = append(returnValues, dailyChallenge)

	if *isGuest {
		return returnValues, nil
	}

	challengeDivision := s.getDailyChallengeDivision(user)

	categorisedDailyChallenge, err := s.getDailyChallengeByDivision(ctx, user, challengeDivision)
	if err != nil {
		return returnValues, nil
	}

	returnValues = append(returnValues, categorisedDailyChallenge)

	return returnValues, nil
}
