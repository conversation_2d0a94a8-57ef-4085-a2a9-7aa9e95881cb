package dailyChallenge

import (
	"context"
	"errors"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) GetUserChallengeResult(ctx context.Context, challengeNumber *int) (*models.UserResult, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("unauthorized: user not authenticated")
	}

	if challengeNumber == nil || *challengeNumber == 0 {
		return &models.UserResult{
			Success: false,
			Error:   "Challenge Number cannot be null",
		}, nil
	}

	var challenge *models.DailyChallenge

	challenge, err = s.dailyChallengeRepo.FindByChallengeNumberAndDivison(ctx, *challengeNumber, models.ChallengeDivisionOpen.String())
	if errors.Is(err, mongo.ErrNoDocuments) || challenge == nil {
		return &models.UserResult{
			Success: false,
			Error:   "Challenge not found",
		}, nil
	} else if err != nil {
		return &models.UserResult{
			Success: false,
			Error:   "Something went wrong",
		}, err
	}

	challengeId := challenge.ID

	userResult, err := s.dailyChallengeResultRepo.FindOne(ctx, bson.M{"userId": userId, "challengeId": challengeId})

	if errors.Is(err, mongo.ErrNoDocuments) || userResult == nil {
		return &models.UserResult{
			Success: false,
			Error:   "User result not found for the given challenge",
		}, nil
	} else if err != nil {
		return &models.UserResult{
			Success: false,
			Error:   "Something went wrong",
		}, err
	}

	userScore := userResult.Score
	userSavedRank := userResult.Rank

	if userSavedRank != 0 {
		return &models.UserResult{
			Success: true,
			Result:  userResult,
		}, nil
	}

	rank, err := s.dailyChallengeResultRepo.Count(ctx, bson.M{
		"challengeId": challengeId,
		"resultStatus": bson.M{
			"$nin": []models.ResultStatus{
				models.ResultStatusAttempted,
			},
		},
		"score": bson.M{"$lt": userScore},
	})
	if err != nil {
		return &models.UserResult{
			Success: false,
			Error:   "Something went wrong",
		}, err
	}
	rank += 1

	isPastChallengeFilter := bson.M{"challengeNumber": bson.M{"$gt": challengeNumber}}
	isPastChallenge, err := s.dailyChallengeResultRepo.FindOne(ctx, isPastChallengeFilter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		zlog.Error(ctx, "Error in checking past challenges: ", err)
		return &models.UserResult{
			Success: false,
			Error:   "Something went wrong",
		}, err
	}

	if isPastChallenge.UserID != primitive.NilObjectID {
		userResult.Rank = int(rank)
		err := s.dailyChallengeResultRepo.UpdateOne(ctx, bson.M{"challengeNumber": userResult.ChallengeNumber}, bson.M{"$set": bson.M{"rank": rank}})
		if err != nil {
			return &models.UserResult{
				Success: false,
				Error:   "Error updating user rank",
			}, err
		}
		zlog.Info(ctx, "Updating user rank in database")
	}

	userResult.Rank = int(rank)

	return &models.UserResult{
		Success: true,
		Result:  userResult,
	}, nil
}
