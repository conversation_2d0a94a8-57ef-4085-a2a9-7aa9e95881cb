package dailyChallenge

import (
	"context"
	"errors"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) GetUserChallengeResultByDailyChallengeID(ctx context.Context, challengeID primitive.ObjectID) (*models.UserDailyChallengeResultWithStats, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("unauthorized: user not authenticated")
	}

	challenge, err := s.dailyChallengeRepo.FindByID(ctx, challengeID)
	if errors.Is(err, mongo.ErrNoDocuments) || challenge == nil {
		return &models.UserDailyChallengeResultWithStats{
			Success: utils.AllocPtr(false),
			Error:   utils.AllocPtr("Challenge not found"),
		}, nil
	} else if err != nil {
		return &models.UserDailyChallengeResultWithStats{
			Success: utils.AllocPtr(false),
			Error:   utils.AllocPtr("Something went wrong"),
		}, err
	}

	dailyChallengeResultWithStats := models.DailyChallengeResultWithStats{}

	dailyChallengeResult, err := s.GetUsersDailyChallengeResultByChallengeId(ctx, challenge)
	if err != nil {
		return &models.UserDailyChallengeResultWithStats{
			Success: utils.AllocPtr(false),
			Error:   utils.AllocPtr("Something went wrong"),
		}, err
	}

	dailyChallengeDivision := challenge.Division

	dailyChallengeResultWithStats.Result = dailyChallengeResult
	stats, err := s.userDailyChallengeStatsRepo.FindByUserIDAndDivision(ctx, userId, dailyChallengeDivision)
	if err != nil {
		return &models.UserDailyChallengeResultWithStats{
			Success: utils.AllocPtr(false),
			Error:   utils.AllocPtr("Something went wrong"),
		}, err
	}
	dailyChallengeResultWithStats.Stats = stats

	return &models.UserDailyChallengeResultWithStats{
		Success: utils.AllocPtr(true),
		Result:  &dailyChallengeResultWithStats,
	}, nil
}
