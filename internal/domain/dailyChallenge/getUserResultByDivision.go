package dailyChallenge

import (
	"context"
	"errors"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) GetUsersDailyChallengeResultByChallengeId(ctx context.Context, challenge *models.DailyChallenge) (*models.DailyChallengeResult, error) {
	challengeID := challenge.ID
	startTime := challenge.StartTime

	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	dailyChallengeResult, err := s.dailyChallengeResultRepo.FindOne(ctx, bson.M{"userId": userId, "challengeId": challengeID})
	if err != nil || dailyChallengeResult == nil {
		return nil, err
	}

	userScore := dailyChallengeResult.Score
	userSavedRank := dailyChallengeResult.Rank

	if userSavedRank != 0 {
		return dailyChallengeResult, nil
	}

	rank, err := s.dailyChallengeResultRepo.Count(ctx, bson.M{
		"challengeId": challengeID,
		"resultStatus": bson.M{
			"$nin": []models.ResultStatus{
				models.ResultStatusAttempted,
			},
		},
		"score": bson.M{"$lt": userScore},
	})
	if err != nil {
		return nil, err
	}
	rank += 1

	isPastChallenge := !isToday(*startTime)

	if isPastChallenge {
		dailyChallengeResult.Rank = int(rank)
		err := s.dailyChallengeResultRepo.UpdateOne(ctx, bson.M{"challengeId": dailyChallengeResult.ChallengeID}, bson.M{"$set": bson.M{"rank": rank}})
		if err != nil {
			zlog.Error(ctx, "Error while updating rank in past daily challenge result:", err)
		}
	}

	dailyChallengeResult.Rank = int(rank)
	return dailyChallengeResult, nil
}

func isToday(date time.Time) bool {
	now := time.Now().UTC()
	date = date.UTC()
	return date.Year() == now.Year() && date.Month() == now.Month() && date.Day() == now.Day()
}

func (s *service) GetUserChallengeResultByDivision(ctx context.Context, dateStr *string, division *models.ChallengeDivision) (*models.UserResult, error) {
	var date time.Time
	var err error

	date, err = time.Parse(time.RFC3339, *dateStr)
	if err != nil {
		date, err = time.Parse("2006-01-02", *dateStr)
		if err != nil {
			return nil, fmt.Errorf("invalid date format, expected YYYY-MM-DD or full ISO 8601: %w", err)
		}
	}

	startTime := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, time.UTC)

	challenge, err := s.dailyChallengeRepo.FindOne(ctx, bson.M{
		"startTime": startTime,
		"division":  division.String(),
	})

	if errors.Is(err, mongo.ErrNoDocuments) || challenge == nil {
		return &models.UserResult{
			Success: false,
			Error:   "Challenge not found",
		}, nil
	} else if err != nil {
		return &models.UserResult{
			Success: false,
			Error:   "Something went wrong",
		}, err
	}

	var userResult *models.DailyChallengeResult

	userResult, err = s.GetUsersDailyChallengeResultByChallengeId(ctx, challenge)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return &models.UserResult{
				Success: false,
				Error:   "User Did not attempt challenge",
			}, nil
		}
		return &models.UserResult{
			Success: false,
			Error:   "Something went wrong",
		}, err
	}

	return &models.UserResult{
		Success: true,
		Result:  userResult,
	}, nil
}
