package dailyChallenge

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type service struct {
	userRepo                        repository.UserRepository
	dailyChallengeRepo              repository.DailyChallengeRepository
	dailyChallengeResultRepo        repository.DailyChallengeResultRepository
	userRatingFixtureSubmissionRepo repository.UserRatingFixtureSubmissionRepository
	userDailyChallengeStatsRepo     repository.UserDailyChallengeStatsRepository
	flaggedDCResultRepo             repository.DailyChallengeResultRepository
	userService                     domain.UserStore
	presetsService                  domain.PresetsStore
	notificationService             domain.NotificationStore
	coreService                     domain.CoreLogicStore
	userStreakRepo                  repository.UserStreakRepository
	botDetectionService             domain.BotDetectionStore
}

func NewDailyChallengeService(lc fx.Lifecycle, repositoryFactory *repository.RepositoryFactory,
	userService domain.UserStore, presetsService domain.PresetsStore,
	notificationService domain.NotificationStore,
	coreService domain.CoreLogicStore, botDetectionService domain.BotDetectionStore,
) domain.DailyChallengeStore {
	s := &service{
		userRepo:                        repositoryFactory.UserRepository,
		dailyChallengeRepo:              repositoryFactory.DailyChallengeRepository,
		dailyChallengeResultRepo:        repositoryFactory.DailyChallengeResultRepository,
		flaggedDCResultRepo:             repositoryFactory.FlaggedDCResultRepository,
		userService:                     userService,
		presetsService:                  presetsService,
		userDailyChallengeStatsRepo:     repositoryFactory.UserDailyChallengeStatsRepository,
		userRatingFixtureSubmissionRepo: repositoryFactory.UserRatingFixtureSubmissionRepository,
		notificationService:             notificationService,
		coreService:                     coreService,
		userStreakRepo:                  repositoryFactory.UserStreakRepository,
		botDetectionService:             botDetectionService,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting daily challenge service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down daily challenge service")
			return nil
		},
	})

	return s
}
