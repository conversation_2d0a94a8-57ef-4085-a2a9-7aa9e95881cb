package dailyChallenge

import (
	"context"
	"errors"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/userStreak"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

const BOT_DETECTION_THRESHOLD_TIME = 60 * 1000 // 60 seconds in milliseconds

func (s *service) SubmitDailyChallenge(ctx context.Context, input models.SubmitSolutionInput) (*models.SubmitChallengeResult, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("unauthorized: user not authenticated")
	}

	challengeId := input.ChallengeID
	submittedTimes := input.SubmittedTimes

	score := 0
	for _, time := range submittedTimes {
		if time != nil {
			score += *time
		}
	}

	if score == 0 || len(submittedTimes) == 0 {
		zlog.Warn(ctx, "submitDailyChallenge ERROR: 0 score or empty submittedTimes",
			zap.String("userID", userId.Hex()),
			zap.Any("submittedTimes", submittedTimes))
		return &models.SubmitChallengeResult{
			Success: false,
			Message: "Something went wrong!",
		}, nil
	}

	existingResult, err := s.dailyChallengeResultRepo.FindOne(ctx, bson.M{"userId": userId, "challengeId": challengeId})

	if err == nil {
		if existingResult.ResultStatus == models.ResultStatusCompleted {
			zlog.Info(ctx, "User has already completed this challenge",
				zap.String("userID", userId.Hex()),
				zap.String("challengeId", challengeId.Hex()))
			return &models.SubmitChallengeResult{
				Success: false,
				Message: "You have already completed this challenge.",
			}, nil
		}
	} else if !errors.Is(err, mongo.ErrNoDocuments) {
		zlog.Error(ctx, "Error finding existing result", err)
		return &models.SubmitChallengeResult{
			Success: false,
			Message: "Something went wrong!",
		}, err
	}

	var challenge *models.DailyChallenge
	challenge, err = s.dailyChallengeRepo.FindByID(ctx, challengeId)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return &models.SubmitChallengeResult{
				Success: false,
				Message: fmt.Sprintf("Challenge not found for challengeId : %s", challengeId.Hex()),
			}, nil
		}
		return &models.SubmitChallengeResult{
			Success: false,
			Message: "Something went wrong!",
		}, err
	}

	questions := challenge.Questions

	if len(questions) != len(submittedTimes) {
		zlog.Warn(ctx, "submitDailyChallenge ERROR: submitted answers is not equal to questions size",
			zap.String("userID", userId.Hex()))
		return &models.SubmitChallengeResult{
			Success: false,
			Message: "Something went wrong!",
		}, nil
	}

	dailyChallengeResult := models.DailyChallengeResult{
		UserID:            userId,
		ChallengeID:       &challengeId,
		ChallengeNumber:   &challenge.ChallengeNumber,
		StatikCoinsEarned: utils.AllocPtr(15),
		SubmittedTimes:    submittedTimes,
		Score:             &score,
		CompletedAt:       utils.AllocPtr(time.Now()),
		ResultStatus:      models.ResultStatusCompleted,
		IP:                utils.AllocPtr(ctx.Value(constants.IPKey).(string)),
	}

	if score <= BOT_DETECTION_THRESHOLD_TIME {

		intSubmittedTimes := make([]int, 0, len(submittedTimes))
		for _, t := range submittedTimes {
			if t != nil {
				intSubmittedTimes = append(intSubmittedTimes, *t)
			}
		}
		detection, err := s.botDetectionService.DetectBotBehavior(ctx, userId, &challengeId, intSubmittedTimes)
		if err != nil {
			zlog.Error(ctx, "Error detecting bot behavior", err)
		}

		if (detection != nil) && (detection.Status == models.BotDetectionStatusSuspicious || detection.Status == models.BotDetectionStatusConfirmed) {
			if err := s.flaggedDCResultRepo.Create(ctx, &dailyChallengeResult); err != nil {
				zlog.Error(ctx, "Error creating flagged daily challenge result", err)
				return nil, err
			}
			return &models.SubmitChallengeResult{
				Success: false,
				Message: "Suspicious activity detected in your submission. If you think that we have made a mistake, reach out to <NAME_EMAIL>",
			}, nil
		}
	}

	hasDailyChallengeUpdated := false

	for i, question := range questions {
		stats := question.Stats
		submissionTime := submittedTimes[i]
		if *stats.FastestTime == *submissionTime {
			challenge.Questions[i].Stats.UserIds = append(stats.UserIds, &userId)
			hasDailyChallengeUpdated = true
		} else if *stats.FastestTime > *submissionTime {
			challenge.Questions[i].Stats.FastestTime = submissionTime
			challenge.Questions[i].Stats.UserIds = []*primitive.ObjectID{&userId}
			hasDailyChallengeUpdated = true
		}
	}

	if hasDailyChallengeUpdated {
		err := s.dailyChallengeRepo.Update(ctx, challenge)
		if err != nil {
			return &models.SubmitChallengeResult{
				Success: false,
				Message: "Failed to update challenge data: " + err.Error(),
			}, err
		}
	}

	err = s.dailyChallengeResultRepo.Update(ctx, &dailyChallengeResult)
	if err != nil {
		return &models.SubmitChallengeResult{
			Success: false,
			Message: "An error occurred while submitting the challenge.",
		}, err
	}

	err = s.updateUserDailyChallengeSubmissionStats(ctx, userId, challenge.Division, score)
	if err != nil {
		zlog.Error(ctx, "Failed to update UserDailyChallengeStats", err)
	}

	err = s.updateDailyChallengeSubmissionStats(ctx, challengeId, score)
	if err != nil {
		zlog.Error(ctx, "Failed to update DailyChallenge.Stats", err)
	}

	result := models.Result{
		UserID:         userId,
		ChallengeID:    challengeId,
		SubmittedTimes: submittedTimes,
		CompletedAt:    challenge.EndTime,
		Score:          &score,
	}

	go func() {
		if _, err := s.presetsService.SubmitUserQuestions(utils.DeriveContextWithoutCancel(ctx), questions, userId); err != nil {
			zlog.Error(ctx, "Error submitting questions", err)
		}
	}()

	go func() {
		if err := userStreak.UpdateUserStreak(utils.DeriveContextWithoutCancel(ctx), userId, s.userRepo, true, s.notificationService, s.coreService, s.userStreakRepo); err != nil {
			zlog.Error(ctx, "Error updating user streaks", err)
		}
	}()

	go func() {
		if err := s.userService.UpdateUserStatikCoinsAndTimeSpent(utils.DeriveContextWithoutCancel(ctx), userId, constants.ActivityTypeDailyChallenge, 15, int64(*result.Score), &challengeId); err != nil {
			zlog.Error(ctx, "Failed to update user activity", err)
		}
	}()

	go s.userBadgeEvent(context.Background(), userId)

	go s.CheckRatingFixtureForUser(context.Background(), userId, score, submittedTimes)

	return &models.SubmitChallengeResult{
		Success: true,
		Message: "Challenge submitted successfully.",
		Result:  &result,
	}, nil
}

func (s *service) userBadgeEvent(ctx context.Context, userId primitive.ObjectID) {
	user, err := s.userService.GetUserByID(ctx, userId)
	if err != nil || user == nil {
		return
	}

	if user.IsGuest != nil && *user.IsGuest {
		return
	}

	newBadge := utils.GetInitialBadge(*user.Rating)
	if user.Badge == nil || *user.Badge != newBadge {
		user.Badge = utils.AllocPtr(utils.GetInitialBadge(*user.Rating))
		err = s.userRepo.UpdateOne(ctx, bson.M{"_id": userId}, bson.M{"$set": bson.M{"badge": user.Badge}})
		if err != nil {
			zlog.Error(ctx, "Error updating user badge", err)
		}
		if err := s.coreService.PublishUserEvent(ctx, userId, &models.BadgeAssignedEvent{
			InitialBadge: nil,
			NewBadge:     user.Badge,
		}); err != nil {
			zlog.Error(ctx, "Error publishing user badge", err)
		}
	}
}
