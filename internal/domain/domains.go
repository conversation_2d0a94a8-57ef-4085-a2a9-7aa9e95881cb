package domain

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"matiksOfficial/matiks-server-go/internal/constants"

	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/infrastructure/websocket"
	"matiksOfficial/matiks-server-go/internal/models"

	"github.com/99designs/gqlgen/graphql"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CoreLogicStore interface {
	GetUserByID(ctx context.Context, userId primitive.ObjectID) (*models.User, error)
	GetUsersByIDs(ctx context.Context, userIds []primitive.ObjectID) ([]*models.User, error)
	PublishUserEvent(ctx context.Context, userID primitive.ObjectID, event models.UserEvent) error
}

type FeedStore interface {
	GetUserFeed(ctx context.Context, lastId *primitive.ObjectID, pageSize *int) (*models.FeedResponse, error)
	UpdateLikeStatus(ctx context.Context, feedID primitive.ObjectID) (bool, error)
	AddFeed(ctx context.Context, notification *models.UserNotification) error
	UpdateLastReadFeedId(ctx context.Context, lastReadFeedID primitive.ObjectID) (bool, error)
}

type GameStore interface {
	CreateGame(ctx context.Context, gameConfig *models.GameConfigInput) (*models.Game, error)
	JoinGame(ctx context.Context, joinGameInput *models.JoinGameInput) (*models.Game, error)
	StartGame(ctx context.Context, startGameInput *models.StartGameInput) (*models.Game, error)
	RemovePlayer(ctx context.Context, gameID, playerID primitive.ObjectID) (bool, error)
	LeaveGame(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error)

	SubmitAnswer(ctx context.Context, answerInput *models.SubmitAnswerInput) (*models.Game, error)
	SubmitFlashAnzanAnswer(ctx context.Context, answerInput *models.SubmitFlashAnzanAnswerInput) (*models.Game, error)

	EndGame(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error)
	EndAbilityDuelsGame(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error)
	CancelGame(ctx context.Context, gameID primitive.ObjectID) (*bool, error)

	GetGameByID(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error)
	GetGamesByUser(ctx context.Context, payload *models.GetGamesInput) (*models.GetGamesOutput, error)
	GetGamesByRatingType(ctx context.Context, payload *models.GetGamesByRatingInput) (*models.GetGamesByRatingOutput, error)

	GameEvent(ctx context.Context, gameID *primitive.ObjectID) (<-chan *models.SubscriptionOutput, error)

	StartSearching(ctx context.Context, gameConfig *models.GameConfigInput) (*bool, error)
	AbortSearching(ctx context.Context) (*bool, error)

	GetPracticeGame(ctx context.Context, gameConfigInput *models.GameConfigInput) (*models.Game, error)
	SearchPlayer(ctx context.Context, userID *models.ObjectID) (<-chan *models.SearchSubscriptionOutput, error)

	RequestRematch(ctx context.Context, gameID primitive.ObjectID) (bool, error)
	AcceptRematch(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error)
	RejectRematch(ctx context.Context, gameID primitive.ObjectID) (bool, error)
	CancelRematchRequest(ctx context.Context, gameID primitive.ObjectID) (bool, error)

	GetPlatformStats(ctx context.Context) (*models.PlatformStats, error)
	GetGameDetailedAnalysis(ctx context.Context, gameID *primitive.ObjectID) (*models.GameDetailedAnalysis, error)

	CreateGameForShowdown(ctx context.Context, showdownConfig *models.ShowdownConfig) ([]*models.Game, error)
	EndGameForShowdown(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error)
	CheckIfOpponentNotJoinedForShowdown(ctx context.Context, showdownId, gameId primitive.ObjectID) error

	ChallengeUser(ctx context.Context, challengeUserInput *models.ChallengeUserInput) (*models.Game, error)
	AcceptChallenge(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error)
	RejectChallenge(ctx context.Context, gameID primitive.ObjectID) (*bool, error)
	PublishChallengeEvent(ctx context.Context, game *models.Game, eventType string) error
	UpdateShowdownParticipant(ctx context.Context, participant *models.ShowdownParticipant, round int) error

	HandleGroupPlayChat(ctx context.Context, message models.GroupPlayChatMessage) error
	UserRepo() repository.UserRepository
}

type PuzzleStore interface {
	GetDailyPuzzle(ctx context.Context, date string) (*models.Puzzle, error)                                           // Deprecated
	GetPuzzleSubmissionsByMonth(ctx context.Context, months []string) ([]*models.PuzzleMonthlySubmissionReport, error) // Deprecated
	GetUserPuzzleStats(ctx context.Context) (*models.PuzzleUserStats, error)                                           // Deprecated
	GenerateDailyPuzzle(ctx context.Context, puzzleType models.PuzzleType) (*models.Puzzle, error)
	SubmitPuzzleSolution(ctx context.Context, puzzleID primitive.ObjectID, timeSpent int) (*models.PuzzleResult, error)

	GetDailyPuzzleByType(ctx context.Context, date string, puzzleType models.PuzzleType) (*models.Puzzle, error)
	GetPuzzleSubmissionsByMonthByType(ctx context.Context, months []string, puzzleType models.PuzzleType) ([]*models.PuzzleMonthlySubmissionReport, error)
	GetUserPuzzleStatsByType(ctx context.Context, puzzleType models.PuzzleType) (*models.PuzzleUserStats, error)
}

type PuzzleGameStore interface {
	CreatePuzzleGame(ctx context.Context, gameConfig *models.PuzzleGameConfigInput) (*models.PuzzleGame, error)
	SubmitPuzzleGameAnswer(ctx context.Context, answerInput *models.SubmitPuzzleGameAnswerInput) (*models.PuzzleGame, error)
	JoinPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)
	RemovePlayerFromPuzzleGame(ctx context.Context, gameID, playerID primitive.ObjectID) (bool, error)
	LeavePuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)
	StartPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)
	EndPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)
	StartSearchingForPuzzleGame(ctx context.Context, gameConfig *models.PuzzleGameConfigInput) (*bool, error)
	AbortSearchingForPuzzleGame(ctx context.Context) (*bool, error)
	CancelPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*bool, error)
	RequestRematchForPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (bool, error)
	AcceptRematchOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)
	RejectRematchOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (bool, error)
	ChallengeUserForPuzzleGame(ctx context.Context, challengeUserInput *models.ChallengeUserForPuzzleGameInput) (*models.PuzzleGame, error)
	AcceptChallengeOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)
	RejectChallengeOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*bool, error)
	GetPuzzleGameByID(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)
	GetPuzzleGamesByUser(ctx context.Context, payload *models.GetPuzzleGamesInput) (*models.GetPuzzleGamesOutput, error)

	// Puzzle Game Rush
	SubmitPuzzleGameRush(ctx context.Context, input models.SubmitPuzzleRushGame) (*models.CrossMathPuzzleRush, error)
	GetMyCrossMathPuzzleRushStats(ctx context.Context) (*models.CrossMathPuzzleRushStats, error)
	GetGlobalTop5CrossMathPuzzleRushStats(ctx context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error)
	GetFriendsTop5CrossMathPuzzleRushStats(ctx context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error)
}

type LeagueStore interface {
	CreateLeague(ctx context.Context, input models.CreateLeagueInput) (*models.League, error)
	JoinLeague(ctx context.Context, joinLeagueInput *models.JoinLeagueInput) (bool, error)
	GetLeague(ctx context.Context, id primitive.ObjectID) (*models.League, error)
	GetLeaguesByStatus(ctx context.Context, statuses []models.LeagueStatus, page, pageSize *int, sortDirection *string) (*models.PaginatedLeagues, error)
	GetLeagueLeaderboard(ctx context.Context, leagueID primitive.ObjectID, page, pageSize int) (*models.LeagueLeaderboardPage, error)
}

type UserStore interface {
	CreateUser(ctx context.Context, input *models.UserInput) (*models.User, error)
	LoginAsGuest(ctx context.Context, id models.ObjectID) (*models.User, error)
	LegacyGoogleLogin(ctx context.Context, idToken string, guestID *models.ObjectID) (*models.User, error)
	GoogleLogin(ctx context.Context, authCode string, tokenType, expiresIn *string, guestID *models.ObjectID) (*models.User, error)
	UpdateUser(ctx context.Context, updateUserInput *models.UpdateUserInput) (*models.User, error)
	UpdateUserFromObject(ctx context.Context, user *models.User) error
	UpdateOne(ctx context.Context, filter, update bson.M, opts ...*options.UpdateOptions) error
	GenerateUserName(ctx context.Context, name string) (string, error)

	Login(ctx context.Context, email, password string) (*models.User, error)
	VerifyToken(ctx context.Context, token string) (*models.User, error)
	SendOtp(ctx context.Context, email string) (bool, error)
	VerifyOTP(ctx context.Context, otp string) (bool, error)
	GetUserByID(ctx context.Context, id models.ObjectID) (*models.User, error)
	GetCurrentUser(ctx context.Context) (*models.User, error)
	SignInWithApple(ctx context.Context, input *models.AppleSignInInput) (*models.User, error)
	// Review Names
	LeaderboardNew(ctx context.Context, countryCode, searchKey *string, page, pageSize *int, ratingType *string) (*models.UserLeaderboardPage, error)
	Leaderboard(ctx context.Context, countryCode, searchKey *string, first *int, after *string) (*models.LeaderboardConnection, error)
	GetGlobalTopPlayers(ctx context.Context) (*models.TopPlayersLeaderboard, error)
	GetFriendsLeaderboard(ctx context.Context, page, pageSize *int, ratingType *string) (*models.UserLeaderboardPage, error)
	GetFriendsTopPlayers(ctx context.Context) (*models.TopPlayersLeaderboard, error)
	GetUserByUsername(ctx context.Context, username *string) (*models.SearchUserOutput, error)
	IsUsernameAvailable(ctx context.Context, username *string) (bool, error)

	GetRatingFixtureQuestions(cts context.Context) ([]*models.Question, error)
	GetRatingFixtureSubmission(cts context.Context) (*models.UserRatingFixtureSubmission, error)
	SubmitRatingFixtureResponses(ctx context.Context, submission []*int, timeTaken int) (*models.UserRatingFixtureSubmission, error)
	UpdateRatingBasedOnFixtureResponse(ctx context.Context, userStance models.UserStance) (*models.User, error)
	CountUsersWithHigherRating(ctx context.Context, userRepo repository.UserRepository, rating int) (int, error)

	UserEventsSubscription(ctx context.Context, userID *primitive.ObjectID) (<-chan models.UserEvent, error)
	UploadProfilePicture(ctx context.Context, upload graphql.Upload) (*models.File, error)
	UploadFiles(ctx context.Context, files []*graphql.Upload) ([]*models.File, error)

	OnlineUsers(ctx context.Context, page, pageSize int) (*models.OnlineUsersPage, error)
	GetStatikCoinsLeaderboard(ctx context.Context, page int, pageSize *int, leaderboardType *models.StatikCoinLeaderboardType) (*models.StatikCoinLeaderboardPage, error)

	UpdateUserStatikCoinsAndTimeSpent(ctx context.Context, userID primitive.ObjectID, activityType constants.ActivityType, coins int, duration int64, activityID *primitive.ObjectID) error
	LeaderBoardV3(ctx context.Context, countryCode, searchKey *string, page, pageSize *int) (*models.UserLeaderboardPage, error)

	GetTimeSpentByUser(ctx context.Context, date *string) (int, error)
	GetUserLeagueGroupLeaderboard(ctx context.Context, page, pageSize *int) (*models.WeeklyLeagueLeaderboardPage, error)
	GetUsersWeeklyStatikCoins(ctx context.Context) (int, error)
	GetUsersWeeklyStatikCoinsV2(ctx context.Context, userID primitive.ObjectID) (*models.UsersWeeklyStatikCoinsOutput, error)

	GetUsersOfMyInstitute(ctx context.Context, page, pageSize *int) (*models.MyInstituteUsersPage, error)
	SearchUsersInMyInstitute(ctx context.Context, searchKey *string, page, pageSize *int) (*models.MyInstituteUsersPage, error)

	SubmitReferral(ctx context.Context, referralCode string) (bool, error)
	GenerateUniqueReferralCode(ctx context.Context) (string, error)
	DeleteUser(ctx context.Context) (bool, error)
}

type UserStreakStore interface {
	GetUserStreakHistoryByMonth(ctx context.Context, yearMonths []string) ([]*models.StreakEntry, error)
	CheckUserStreakStatus(ctx context.Context) (*models.StreakStatusResponse, error)
	UseStreakFreezer(ctx context.Context) (bool, error)
	GetUserUpdatedStreak(ctx context.Context) (*models.UserStreaks, error)
}

type StreakShieldTransactionStore interface {
	CreateTransaction(
		ctx context.Context,
		userID primitive.ObjectID,
		quantity int,
		transactionType models.TransactionType,
		earnVia *models.EarnVia,
		referralID *primitive.ObjectID,
		transactionID *string,
	) (*models.StreakShieldTransaction, error)
	GetUserTransactions(
		ctx context.Context,
		userID primitive.ObjectID,
		page, pageSize int,
	) ([]*models.StreakShieldTransaction, int, error)
	GetAllTransactions(
		ctx context.Context,
		page, pageSize int,
	) ([]*models.StreakShieldTransaction, int, error)
	AddRedemptionDate(
		ctx context.Context,
		transactionID primitive.ObjectID,
		redemptionDate time.Time,
	) error
	GetUserStreakShieldTransactions(
		ctx context.Context,
		page, pageSize int,
	) (*models.StreakShieldTransactionPage, error)
}

type WeeklyLeagueStore interface {
	ProcessWeeklyLeagueAssignments(ctx context.Context) error
	RunWeeklyLeagueProcess(ctx context.Context) error
}

type InstitutionStore interface {
	AddNewInstitute(ctx context.Context, input models.CreateInstitutionInput) (*models.Institution, error)
	SearchInstitute(ctx context.Context, query string, limit *int) ([]*models.Institution, error)
}

type BotDetectionStore interface {
	DetectBotBehavior(ctx context.Context, userID primitive.ObjectID, gameID *primitive.ObjectID, submissionTimes []int) (*models.BotDetection, error)
	GetUserBotDetections(ctx context.Context, userID primitive.ObjectID) ([]*models.BotDetection, error)
	GetRecentUserBotDetections(ctx context.Context, userID primitive.ObjectID, since time.Time) ([]*models.BotDetection, error)
	ShadowBanUser(ctx context.Context, userID primitive.ObjectID, status models.UserShadowBanStatus, reason string, detectionIDs []primitive.ObjectID) error
	GetUserShadowBanStatus(ctx context.Context, userID primitive.ObjectID) (*models.UserShadowBan, error)
	IsUserShadowBanned(ctx context.Context, userID primitive.ObjectID) (bool, error)
	UpdateUserShadowBanStatus(ctx context.Context, userID primitive.ObjectID, status models.UserShadowBanStatus) error
	HandleEndGameBotDetection(ctx context.Context, game *models.Game, winnerID, loserID primitive.ObjectID) map[primitive.ObjectID]bool
	HandleSubmissionBotDetection(ctx context.Context, userID primitive.ObjectID, gameID *primitive.ObjectID, submissionTimes []int) (bool, *models.BotDetection, error)
	ShouldPublishEventToUser(ctx context.Context, userID, eventSourceUserID primitive.ObjectID) bool
	GetShadowBannedUsers(ctx context.Context) ([]primitive.ObjectID, error)
}

type UserResolutionStore interface {
	TakePledge(ctx context.Context, duration *int) (*bool, error)
	CheckIfPledgeTaken(ctx context.Context) (*bool, error)
	GetUserResolution(ctx context.Context) (*models.UserResolution, error)
}

type ContestStore interface {
	CreateContest(ctx context.Context, input models.CreateContestInput) (*models.Contest, error)
	RegisterForContest(ctx context.Context, input models.RegistrationFormValuesInput) (bool, error)
	SubmitContestAnswer(ctx context.Context, contestID primitive.ObjectID, questionID, answer string) (bool, error)
	UnregisterFromContest(ctx context.Context, contestID primitive.ObjectID) (bool, error)
	UpdateContestParticipantStartTime(ctx context.Context, contestID primitive.ObjectID) (bool, error)
	GetFeaturedContests(ctx context.Context) ([]*models.Contest, error)
	GetContestByID(ctx context.Context, contestID primitive.ObjectID) (*models.Contest, error)
	GetContestLeaderboard(ctx context.Context, contestID primitive.ObjectID, pageNumber, pageSize *int) (*models.ContestLeaderboard, error)
	GetRegisteredContests(ctx context.Context) ([]*models.Contest, error)
	GetUserContestSubmissions(ctx context.Context, userID *primitive.ObjectID, contestID primitive.ObjectID) (*models.UserContestSubmissions, error)
	GetContestsByStatus(ctx context.Context, statuses []models.ContestStatus, page, pageSize *int, sortDirection *string) (*models.PaginatedContests, error)
	GetUserContestResult(ctx context.Context, contestID primitive.ObjectID) (*models.UserContestResult, error)

	// Virtual Contest
	JoinVirtualContest(ctx context.Context, contestID primitive.ObjectID) (bool, error)
	SubmitVirtualContestAnswer(ctx context.Context, contestID primitive.ObjectID, questionID, answer string) (bool, error)
}

type DailyChallengeStore interface {
	GetDailyChallenge(ctx context.Context) (*models.DailyChallenge, error)
	GetDailyChallengeLeaderboard(ctx context.Context, challengeNumber, pageNumber, pageSize *int) (*models.LeaderboardPage, error)
	GetUserChallengeResult(ctx context.Context, challengeNumber *int) (*models.UserResult, error)
	SubmitDailyChallenge(ctx context.Context, input models.SubmitSolutionInput) (*models.SubmitChallengeResult, error)
	GetUserChallengeResultByDailyChallengeID(ctx context.Context, challengeID primitive.ObjectID) (*models.UserDailyChallengeResultWithStats, error)
	AttemptDailyChallenge(ctx context.Context, challengeID primitive.ObjectID) (bool, error)
	GetDailyChallengeByID(ctx context.Context, id primitive.ObjectID) (*models.DailyChallenge, error)
	CheckBotBehavior(ctx context.Context, challengeID, userID primitive.ObjectID) (*models.BotDetectionResult, error)
	GetDailyChallenges(ctx context.Context) ([]*models.DailyChallenge, error)
	GetUserChallengeResultByDivision(ctx context.Context, dateStr *string, division *models.ChallengeDivision) (*models.UserResult, error)
	GetDailyChallengeLeaderboardByDivision(ctx context.Context, dateStr *string, pageNumber, pageSize *int, division *models.ChallengeDivision) (*models.LeaderboardPage, error)
	GenerateNewChallenge(ctx context.Context, division models.ChallengeDivision) (*models.DailyChallenge, error)
}

type PresetsStore interface {
	SubmitUserPresetResult(ctx context.Context, userPresetResultInput *models.UserPresetResultInput) (*bool, error)
	SaveUserPreset(ctx context.Context, identifier, name *string) (*models.UserPreset, error)
	GetGlobalPresets(ctx context.Context, page, pageSize *int) (*models.GlobalPresets, error)
	GetGlobalPresetsByIdentifier(ctx context.Context, identifier *string) (*models.GlobalPreset, error)
	GetUserPresetsByIdentifier(ctx context.Context, identifier *string) (*models.UserPreset, error)
	GetUserPresetStatsByDate(ctx context.Context, username *string, durationFilter *int, identifier *string) ([]*models.UserPresetDayStats, error)
	GetUserRecentPresets(ctx context.Context, page, pageSize *int) (*models.UserPresets, error)
	GetUserSavedPresets(ctx context.Context, page, pageSize *int) (*models.UserPresets, error)
	SubmitUserQuestions(ctx context.Context, questions any, userID primitive.ObjectID) (*bool, error)

	GetUsersAllPlayedPresets(ctx context.Context, username *string) (*models.AllPlayedPresetsOutput, error)

	GetUserPresetByIdentifier(ctx context.Context, identifier *string, userId primitive.ObjectID) (*models.UserPreset, error)

	DeleteUserSavedPreset(ctx context.Context, presetId primitive.ObjectID) (*bool, error)
}

type FriendsAndFollowersStore interface {
	SendFriendRequest(ctx context.Context, sendRequestInput *models.FriendRequestInput) (bool, error)
	WithdrawFriendRequest(ctx context.Context, withrawFriendRequestInput *models.WithdrawFriendRequestInput) (bool, error)
	AcceptFriendRequest(ctx context.Context, acceptRequestInput *models.FriendRequestInput) (bool, error)
	RejectFriendRequest(ctx context.Context, rejectRequestInput *models.FriendRequestInput) (bool, error)
	RemoveFriend(ctx context.Context, removeFriendInput *models.RemoveFriendInput) (bool, error)
	FollowUser(ctx context.Context, followUserInput *models.FollowUserInput) (bool, error)
	UnFollowUser(ctx context.Context, unFollowUserInput *models.UnFollowUserInput) (bool, error)
	RemoveFollower(ctx context.Context, removeFollowerInput *models.RemoveFollowerInput) (bool, error)
	GetFollowers(ctx context.Context, page, pageSize *int) (*models.FollowersAndFolloweePage, error)
	GetFollowings(ctx context.Context, page, pageSize *int) (*models.FollowersAndFolloweePage, error)
	GetFriends(ctx context.Context, page, pageSize *int, sortOption *models.SortOptions) (*models.FriendsPage, error)
	GetPendingFriendRequests(ctx context.Context, page, pageSize *int) (*models.FriendRequestPage, error)
}

type EventsStore interface {
	SubmitAnswer(ctx context.Context, client *websocket.Client, msg websocket.Message) error
	SubmitPuzzleGameAnswer(ctx context.Context, client *websocket.Client, msg websocket.Message) error
	PingPong(ctx context.Context, client *websocket.Client, msg websocket.Message) error
	AddMessage(ctx context.Context, client *websocket.Client, msg websocket.Message) error
	SendMessage(ctx context.Context, client *websocket.Client, msg websocket.Message) error
}

type NotificationStore interface {
	SendPushNotificationToUsers(ctx context.Context, body, title string, data map[string]string, userIDs []primitive.ObjectID) error
	SendInAppPopup(ctx context.Context, message string, userIDs []primitive.ObjectID) error
	SendEmailNotification(ctx context.Context, subject, body string, to []string) error
	RegisterDeviceToken(ctx context.Context, pushNotificationToken string, deviceID, platform *string) (*models.DeviceTokenRegistrationResponse, error)
	UnregisterDeviceToken(ctx context.Context, pushNotificationToken, deviceID *string) (*models.DeviceTokenRegistrationResponse, error)
	SendNotification(ctx context.Context, notification *models.UserNotification) error
	SendFeedback(ctx context.Context, feedBack *models.Feedback) (*bool, error)
}

type ShowdownStore interface {
	CreateShowdown(ctx context.Context, input models.CreateShowdownInput) (*models.Showdown, error)
	UnregisterFromShowdown(ctx context.Context, showdownID primitive.ObjectID) (bool, error)
	GetShowdownByID(ctx context.Context, showdownID primitive.ObjectID) (*models.Showdown, error)
	RegisterForShowdown(ctx context.Context, input models.ShowdownRegistrationFormValuesInput) (bool, error)
	GetFixturesByShowdownId(ctx context.Context, showdownID primitive.ObjectID) (*models.FicturesCollection, error)
	GetPaginatedLeaderboard(ctx context.Context, input *models.PaginatedLeaderboardInput) (*models.PaginatedLeaderboard, error)
	GetShowdownsByStatus(ctx context.Context, statuses []models.ShowdownContestStatus, page, pageSize *int, sortDirection *string) (*models.PaginatedShowdowns, error)
	CreateFixtures(ctx context.Context, showdownId primitive.ObjectID, currentRound int) error
	OnFixtureCreation(ctx context.Context, showdownId primitive.ObjectID, currentRound int) error
	NotifyUsersAsFixturesCreated(ctx context.Context, showdownId primitive.ObjectID, round int, byeParticipantId *primitive.ObjectID) error
	UpdateShowdownStatus(ctx context.Context, showdownId primitive.ObjectID, status models.ShowdownContestStatus) error
	NotifyUsersAsShowdownStarts(ctx context.Context, showdownId primitive.ObjectID, round int) error
	UpdateShowdownCurrentRound(ctx context.Context, showdownId primitive.ObjectID, round int) error
	OnShowdownEnd(ctx context.Context, showdownId primitive.ObjectID) error
	GetFeaturedShowdown(ctx context.Context) ([]*models.Showdown, error)
	CheckShowdownPlayersStatus(ctx context.Context, showdownId primitive.ObjectID, currentRound int) error
}

type SchedulerStore interface {
	ProcessDueTasks()
}

type UserSettingsStore interface {
	GetUserSettings(ctx context.Context) (*models.UserSettings, error)
	UpdateUserSettings(ctx context.Context, settings *models.UpdateSettingsInput) (*models.UserSettings, error)
}

type MessageStore interface {
	CreateMessage(ctx context.Context, messageInput models.CreateMessageInput) (*models.Message, error)
	GetMessagesByGroupID(ctx context.Context, groupID primitive.ObjectID, lastMessageId *primitive.ObjectID, pageSize *int, sortDirection *models.SortDirection) (paginatedMessages *models.PaginatedMessage, err error)
	GetAllMessageGroup(ctx context.Context, input *models.GetAllMessageGroupsInput) (*models.PaginatedMessageGroups, error)
	GetMessageGroupIdForFriends(ctx context.Context, friendID primitive.ObjectID) (primitive.ObjectID, error)
	GetMessageGroupDetailsByID(ctx context.Context, groupID primitive.ObjectID) (*models.MessageGroup, error)
	UpdateLastMessageRead(ctx context.Context, groupID, lastMessageRead primitive.ObjectID) (bool, error)
}

type ClubsStore interface {
	Clubs(ctx context.Context, page, pageSize *int, visibility *models.Visibility, search *string) (*models.ClubsPage, error)
	Club(ctx context.Context, id primitive.ObjectID) (*models.Club, error)
	CreateClub(ctx context.Context, input models.CreateClubInput) (*models.Club, error)
	UpdateClub(ctx context.Context, input models.UpdateClubInput) (bool, error)
	UploadClubLogoImage(ctx context.Context, file graphql.Upload, clubID primitive.ObjectID) (*models.File, error)
	UploadClubBannerImage(ctx context.Context, file graphql.Upload, clubID primitive.ObjectID) (*models.File, error)
	DeleteClub(ctx context.Context, id primitive.ObjectID) (bool, error)

	GetClubLeaderboard(ctx context.Context, clubID primitive.ObjectID, page, pageSize *int) (*models.ClubLeaderboard, error)

	JoinClub(ctx context.Context, clubID primitive.ObjectID) (bool, error)
	LeaveClub(ctx context.Context, clubID primitive.ObjectID) (bool, error)
	AddClubMember(ctx context.Context, clubID, userID primitive.ObjectID) (bool, error)
	UpdateMemberRole(ctx context.Context, clubID, userID primitive.ObjectID, role models.ClubMemberRole) (*models.ClubMember, error)
	RemoveClubMember(ctx context.Context, clubID, userID primitive.ObjectID) (bool, error)
	GetClubMemberInfo(ctx context.Context, clubID primitive.ObjectID) (*models.ClubMember, error)
	Members(ctx context.Context, clubID primitive.ObjectID, membershipStatus models.ClubMembershipStatus, page, pageSize *int) (*models.ClubMembersPage, error)
}

type ClubEventStore interface {
	ClubEvents(ctx context.Context, page, pageSize *int, clubID *primitive.ObjectID, eventType *models.ClubEventType, from, to *time.Time) (*models.ClubEventsPage, error)
	ClubEvent(ctx context.Context, id primitive.ObjectID) (*models.ClubEvent, error)
	CreateClubEvent(ctx context.Context, input models.CreateClubEventInput) (*models.ClubEvent, error)
	UpdateClubEvent(ctx context.Context, input models.UpdateClubEventInput) (*models.ClubEvent, error)
	DeleteClubEvent(ctx context.Context, id primitive.ObjectID) (bool, error)
	JoinClubEvent(ctx context.Context, eventID primitive.ObjectID) (*models.ClubEventParticipant, error)
	LeaveClubEvent(ctx context.Context, eventID primitive.ObjectID) (bool, error)
}

type ClubAnnouncementStore interface {
	ClubAnnouncements(ctx context.Context, page, pageSize *int, clubID *primitive.ObjectID, from, to *time.Time) (*models.ClubAnnouncementsPage, error)
	CreateClubAnnouncement(ctx context.Context, input models.CreateClubAnnouncementInput) (*models.ClubAnnouncement, error)
	DeleteClubAnnouncement(ctx context.Context, id primitive.ObjectID) (bool, error)
}

type ForumStore interface {
	CreateForum(ctx context.Context, input models.CreateForumInput) (*models.Forum, error)
	CreateForumThread(ctx context.Context, input models.CreateForumThreadInput) (*models.ForumThread, error)
	CreateForumReply(ctx context.Context, input models.CreateForumReplyInput) (*models.ForumReply, error)
	Forum(ctx context.Context, id primitive.ObjectID) (*models.Forum, error)
	Forums(ctx context.Context, clubID primitive.ObjectID, page, pageSize *int) (*models.ForumPage, error)
	ForumThread(ctx context.Context, id primitive.ObjectID) (*models.ForumThread, error)
	ForumThreads(ctx context.Context, forumID primitive.ObjectID, page, pageSize *int) (*models.ThreadsPage, error)
	ForumReplies(ctx context.Context, threadID primitive.ObjectID, page, pageSize *int) (*models.RepliesPage, error)
}

type AnnouncementStore interface {
	// Queries
	GetUnreadAnnouncements(ctx context.Context, limit, offset *int) ([]*models.Announcement, error)
	GetAnnouncementByID(ctx context.Context, id primitive.ObjectID) (*models.Announcement, error)
	GetAllAnnouncements(ctx context.Context, limit, offset *int, announcementType *models.AnnouncementType) ([]*models.Announcement, error) // Admin query

	// Mutations
	MarkAnnouncementAsRead(ctx context.Context, announcementID primitive.ObjectID) (*models.AnnouncementMutationResponse, error)
	MarkAllAnnouncementsAsRead(ctx context.Context) (*models.AnnouncementMutationResponse, error)

	// Admin Mutations
	CreateAnnouncement(ctx context.Context, input models.CreateAnnouncementInput) (*models.Announcement, error)
	UpdateAnnouncement(ctx context.Context, id primitive.ObjectID, input models.UpdateAnnouncementInput) (*models.Announcement, error)
	DeleteAnnouncement(ctx context.Context, id primitive.ObjectID) (*models.AnnouncementMutationResponse, error)
}
