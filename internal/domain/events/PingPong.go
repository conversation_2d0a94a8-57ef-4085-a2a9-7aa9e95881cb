package events

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/infrastructure/websocket"
)

type PingResponse struct {
	ClientRequestTime int64  `json:"clientRequestTime"`
	ServerTime        int64  `json:"serverResponseTime"`
	CurrentActivity   string `json:"currentActivity"`
	UserId            string `json:"userId"`
}

type UserActivity struct {
	UserID       string `json:"userId"`
	LastActivity string `json:"lastActivity"`
	Timestamp    int64  `json:"timestamp"`
}

func (s *service) PingPong(ctx context.Context, client *websocket.Client, msg websocket.Message) error {
	data, ok := msg.Data.(map[string]any)
	if !ok {
		return fmt.Errorf("data of invalid format")
	}

	clientTime, ok := data["clientTime"].(float64)
	if !ok {
		return fmt.Errorf("clientTime of invalid format")
	}
	clientTimeUnix := int64(clientTime)

	currentActivity, ok := data["currentActivity"].(string)
	if !ok {
		return fmt.Errorf("currentActivity of invalid format")
	}

	userID, ok := data["userId"].(string)
	if !ok {
		return fmt.Errorf("userId of invalid format")
	}

	err := s.publishPingEvent(ctx, PingResponse{
		ClientRequestTime: clientTimeUnix,
		ServerTime:        time.Now().UTC().UnixMilli(),
		CurrentActivity:   currentActivity,
		UserId:            userID,
	}, msg.Channel)
	if err != nil {
		return err
	}

	err = s.updateUserActivity(ctx, userID, currentActivity)
	if err != nil {
		return fmt.Errorf("error updating user activity: %w", err)
	}

	// TODO: Add trigger bot challenges functionality
	// if currentActivity == string(constants.UserInLobby) {
	// 	go func() {
	// 		err := s.triggerBotChallenges(ctx, userID)
	// 		if err != nil {
	// 			zlog.Error(ctx, "Error triggering bot challenges", err)
	// 		}
	// 	}()
	// }

	return nil
}

func (s *service) updateUserActivity(ctx context.Context, userID, currentActivity string) error {
	now := time.Now().Unix()
	key := "online_users"

	s.broadcastMutex.Lock()
	defer s.broadcastMutex.Unlock()

	existingEntries, err := s.redisCache.ZRangeByScore(ctx, key, &redis.ZRangeBy{
		Min: fmt.Sprintf("%d", now-30),
		Max: fmt.Sprintf("%d", now),
	})
	if err != nil {
		return fmt.Errorf("error checking existing user entries: %w", err)
	}

	for _, entry := range existingEntries {
		var userActivity UserActivity
		if err := json.Unmarshal([]byte(entry), &userActivity); err != nil {
			return fmt.Errorf("error unmarshaling existing user activity: %w", err)
		}

		if userActivity.UserID == userID {
			err = s.redisCache.ZRem(ctx, key, entry)
			if err != nil {
				return fmt.Errorf("error removing existing user entry: %w", err)
			}
			break
		}
	}

	activityData := UserActivity{
		UserID:       userID,
		LastActivity: currentActivity,
		Timestamp:    now,
	}

	activityJSON, err := json.Marshal(activityData)
	if err != nil {
		return fmt.Errorf("error marshaling user activity: %w", err)
	}

	err = s.redisCache.ZAdd(ctx, key, redis.Z{
		Score:  float64(now),
		Member: string(activityJSON),
	})
	if err != nil {
		return fmt.Errorf("error adding user to online users: %w", err)
	}

	err = s.redisCache.ZRemRangeByScore(ctx, key, fmt.Sprintf("%d", 0), fmt.Sprintf("%d", now-20))
	if err != nil {
		return fmt.Errorf("error removing old entries from online users: %w", err)
	}

	if time.Since(s.lastBroadcastTime) > 5*time.Second {
		err = s.broadcastOnlineUsers(ctx)
		if err != nil {
			return fmt.Errorf("error broadcasting online users: %w", err)
		}
		s.lastBroadcastTime = time.Now()
	}

	return nil
}

func (s *service) broadcastOnlineUsers(ctx context.Context) error {
	onlineUsersPage, err := s.userService.OnlineUsers(ctx, 1, 10)
	if err != nil {
		return fmt.Errorf("error getting online users: %w", err)
	}

	onlineUsers := onlineUsersPage.Users

	broadcastErr := s.publishPingEvent(ctx, map[string]any{
		"onlineUsers": onlineUsers,
	}, constants.SubscriptionPrefixEnum.ONLINE_USERS.String())

	return broadcastErr
}
