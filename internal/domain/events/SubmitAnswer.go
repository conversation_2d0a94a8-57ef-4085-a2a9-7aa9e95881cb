package events

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/infrastructure/websocket"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) SubmitAnswer(ctx context.Context, client *websocket.Client, msg websocket.Message) error {
	var err error
	data, ok := msg.Data.(map[string]any)
	answerInput := models.SubmitAnswerInput{
		QuestionID:     data["questionId"].(string),
		SubmittedValue: data["submittedValue"].(string),
		IsCorrect:      data["isCorrect"].(bool),
	}
	if !ok {
		zlog.Error(ctx, "Invalid data type for message.Data", nil)
		return fmt.Errorf("invalid data type for message.Data")
	}

	userIdStr, ok := data["userId"].(string)
	if !ok {
		zlog.Error(ctx, "Failed to parse userId", nil)
		return fmt.Errorf("failed to parse userId")
	}

	timeOfSubmission, ok := data["timeOfSubmission"].(float64)
	if !ok {
		zlog.Error(ctx, "Timestamp of invalid format", nil)
		return fmt.Errorf("timestamp of invalid format")
	}
	timeOfSubmissionUnix := time.Unix(0, int64(timeOfSubmission*1e6))
	answerInput.TimeOfSubmission = models.Date(timeOfSubmissionUnix)
	answerInput.GameID, err = primitive.ObjectIDFromHex(data["gameId"].(string))
	if err != nil {
		zlog.Error(ctx, "GameId of invalid format", err)
		return fmt.Errorf("GameId of invalid format: %v", err)
	}

	userCtx := context.WithValue(ctx, constants.UserContextKey, userIdStr)
	_, err = s.gameService.SubmitAnswer(userCtx, &answerInput)
	if err != nil {
		zlog.Error(ctx, "Failed to submit answer", err)
		return err
	}
	return nil
}
