package events

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/infrastructure/websocket"
	"matiksOfficial/matiks-server-go/internal/models"

	"github.com/bytedance/sonic"
)

func (s *service) AddMessage(ctx context.Context, client *websocket.Client, msg websocket.Message) error {
	var err error
	data, err := sonic.Marshal(msg.Data)
	if err != nil {
		return err
	}
	var messageInput models.CreateMessageInput
	if err := sonic.Unmarshal(data, &messageInput); err != nil {
		return err
	}

	message, err := s.messageService.CreateMessage(ctx, messageInput)
	if err != nil {
		return err
	}
	if message == nil {
		return fmt.Errorf("message found nil")
	}

	return nil
}
