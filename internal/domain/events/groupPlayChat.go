package events

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/infrastructure/websocket"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) SendMessage(ctx context.Context, client *websocket.Client, msg websocket.Message) error {
	if msg.Data == nil {
		return fmt.Errorf("message data is nil")
	}

	data, ok := msg.Data.(map[string]any)
	if !ok {
		return fmt.Errorf("invalid data type for message.Data")
	}

	messageStr, messageStrOk := data["message"].(string)
	gameIdStr, gameIdStrOk := data["gameId"].(string)
	userNameStr, userNameStrOk := data["userName"].(string)
	userIdStr, userIdStrOk := data["userId"].(string)

	if !ok || !messageStrOk || !gameIdStrOk || !userNameStrOk || !userIdStrOk {
		return fmt.Errorf("missing or invalid message fields")
	}

	message := models.GroupPlayChatMessage{
		MessageID: primitive.NewObjectID(),
		Message:   messageStr,
		GameID:    gameIdStr,
		UserName:  userNameStr,
		UserID:    userIdStr,
		SentAt:    time.Now(),
	}

	userCtx := context.WithValue(ctx, constants.UserContextKey, message.UserID)
	err := s.gameService.HandleGroupPlayChat(userCtx, message)
	if err != nil {
		return err
	}
	return nil
}
