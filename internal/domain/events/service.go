package events

import (
	"context"
	"sync"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/infrastructure/websocket"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type service struct {
	ws                websocket.Websocket
	gameCache         cache.GameCache
	gameService       domain.GameStore
	redisCache        cache.Cache
	userService       domain.UserStore
	userRepo          repository.UserRepository
	gameRepo          repository.GameRepository
	lastBroadcastTime time.Time
	broadcastMutex    sync.Mutex
	messageService    domain.MessageStore
	puzzleGameService domain.PuzzleGameStore
}

func NewEventsService(lc fx.Lifecycle, gameService domain.GameStore, ws websocket.Websocket,
	cacheInstance cache.Cache, userService domain.UserStore, repositoryFactory *repository.RepositoryFactory,
	messageService domain.MessageStore, puzzleGameService domain.PuzzleGameStore,
) domain.EventsStore {
	s := &service{
		ws:                ws,
		gameService:       gameService,
		gameCache:         cache.NewGameCacheWrapper(cacheInstance),
		redisCache:        cacheInstance,
		userService:       userService,
		puzzleGameService: puzzleGameService,
		userRepo:          repositoryFactory.UserRepository,
		gameRepo:          repositoryFactory.GameRepository,
		lastBroadcastTime: time.Now().Add(-1 * time.Second),
		messageService:    messageService,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting events service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down events service")
			return nil
		},
	})

	return s
}
