package events

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/websocket"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) SubmitPuzzleGameAnswer(ctx context.Context, client *websocket.Client, msg websocket.Message) error {
	var err error
	data, ok := msg.Data.(map[string]any)
	answerInput := models.SubmitPuzzleGameAnswerInput{
		QuestionID: utils.AllocPtr(data["questionId"].(string)),
		IsCorrect:  utils.AllocPtr(data["isCorrect"].(bool)),
	}
	if !ok {
		return fmt.Errorf("invalid data type for message.Data")
	}

	userIdStr, ok := data["userId"].(string)
	if !ok {
		zlog.Error(ctx, "Failed to parse userId", nil)
		return fmt.Errorf("failed to parse userId")
	}

	timeOfSubmission, ok := data["timeOfSubmission"].(float64)
	if !ok {
		zlog.Error(ctx, "Timestamp of invalid format", nil)
		return fmt.Errorf("timestamp of invalid format")
	}
	timeOfSubmissionUnix := time.Unix(0, int64(timeOfSubmission*1e6))
	answerInput.TimeOfSubmission = utils.AllocPtr(models.Date(timeOfSubmissionUnix))

	gameId, err := primitive.ObjectIDFromHex(data["gameId"].(string))
	if err != nil {
		zlog.Error(ctx, "GameId of invalid format", err)
		return fmt.Errorf("GameId of invalid format: %v", err)
	}
	answerInput.GameID = utils.AllocPtr(gameId)

	userCtx := context.WithValue(ctx, constants.UserContextKey, userIdStr)
	_, err = s.puzzleGameService.SubmitPuzzleGameAnswer(userCtx, &answerInput)
	if err != nil {
		return err
	}
	return nil
}
