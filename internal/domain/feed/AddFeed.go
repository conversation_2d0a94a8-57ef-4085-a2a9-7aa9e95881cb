package feed

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	stringUtils "matiksOfficial/matiks-server-go/utils/string"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) AddFeed(ctx context.Context, notification *models.UserNotification) error {
	if notification == nil {
		zlog.Error(ctx, "Notification is nil", systemErrors.ErrNotificationFoundNil)
		return systemErrors.ErrNotificationFoundNil
	}

	if notification.UserID == primitive.NilObjectID {
		zlog.Error(ctx, "User id is nil", systemErrors.ErrNotificationUserIDNil)
		return systemErrors.ErrNotificationUserIDNil
	}

	if notification.Feed == nil {
		zlog.Error(ctx, "Feed is nil", systemErrors.ErrNotificationFeedNil)
		return systemErrors.ErrNotificationFeedNil
	}

	if notification.Feed.FeedType == "" {
		zlog.Error(ctx, "Feed type is nil", systemErrors.ErrNotificationFeedTypeNil)
		return systemErrors.ErrNotificationFeedTypeNil
	}
	title, err := s.getFeedTitle(ctx, notification.AdditionalInfo)
	if err != nil {
		zlog.Error(ctx, "Failed to get feed title", err)
		return err
	}
	if title == nil {
		zlog.Error(ctx, "Feed title is nil", systemErrors.ErrNotificationFeedTitleNil)
		return systemErrors.ErrNotificationFeedTitleNil
	}

	if notification.AdditionalInfo == nil {
		zlog.Error(ctx, "Additional info is nil", systemErrors.ErrNotificationFeedAdditionalInfoNil)
		return systemErrors.ErrNotificationFeedAdditionalInfoNil
	}
	feedAdditionalInfo, err := s.getFeedAdditionalInfo(ctx, notification.AdditionalInfo)
	if err != nil {
		zlog.Error(ctx, "Failed to get feed additional info", err)
		return err
	}

	feedData := &models.FeedData{
		ID:                  primitive.NewObjectID(),
		SentAt:              time.Now(),
		Title:               *title,
		Description:         notification.Body,
		ExpirationTime:      time.Now().Add(24 * time.Hour),
		LastLikedByUserName: utils.AllocPtr(""),
		LikesCount:          0,
		AdditionalInfo:      feedAdditionalInfo,
		FeedForFriends:      notification.Feed.FeedForFriends,
		CreatedAt:           time.Now(),
		SentFor:             notification.UserID,
		UpdatedAt:           time.Now(),
	}

	if err := s.feedDataRepository.CreateFeedData(ctx, feedData); err != nil {
		zlog.Error(ctx, "Failed to create feed data", err)
		return err
	}
	userFeed := &models.UserFeed{
		ID:              primitive.NewObjectID(),
		UserID:          notification.UserID,
		FeedType:        notification.Feed.FeedType,
		FeedReferenceID: feedData.ID,
		IsLiked:         false,
		FeedData:        feedData,
		ExpirationTime:  time.Now().Add(24 * time.Hour),
		CreatedAt:       time.Now(),
		ImageURL:        notification.ImageUrl,
	}

	switch notification.Feed.FeedType {
	case models.FeedTypeCelebration, models.FeedTypeDailyChallenge:
		if err := s.HandleFeedWithFriends(ctx, userFeed); err != nil {
			zlog.Error(ctx, "Failed to handle feed with friends", err)
			return err
		}
	default:
		if err := s.HandleFeed(ctx, userFeed); err != nil {
			zlog.Error(ctx, "Failed to handle feed", err)
			return err
		}
	}
	return nil
}

func (s *service) getFeedTitle(ctx context.Context, additionalInfo *models.AdditionalInfo) (*string, error) {
	defaultTitle := "@Matiks"
	if additionalInfo == nil {
		return &defaultTitle, nil
	}

	if additionalInfo.FeedType == nil {
		zlog.Error(ctx, "Feed type is nil", systemErrors.ErrNotificationFeedTypeNil)
		return nil, systemErrors.ErrNotificationFeedTypeNil
	}

	if *additionalInfo.FeedType == models.FeedTypeConnection {
		if additionalInfo.ConnectionRequestSentBy == nil {
			zlog.Error(ctx, "Connection request sent by is nil", systemErrors.ErrNotificationFeedAdditionalInfoNil)
			return nil, systemErrors.ErrNotificationFeedAdditionalInfoNil
		}
		connectionSentBy := *additionalInfo.ConnectionRequestSentBy
		if connectionSentBy.IsZero() {
			zlog.Error(ctx, "Sent by is nil", systemErrors.ErrNotificationFeedAdditionalInfoNil)
			return nil, systemErrors.ErrNotificationFeedAdditionalInfoNil
		}

		user, err := s.coreService.GetUserByID(ctx, connectionSentBy)
		if err != nil {
			zlog.Error(ctx, "Failed to get user", err)
			return nil, err
		}

		if user == nil {
			zlog.Error(ctx, "User not found", systemErrors.ErrUserNotFound, zap.String("id", connectionSentBy.Hex()))
			return nil, systemErrors.ErrUserNotFound
		}

		title := stringUtils.Concat("@", user.Username)
		return &title, nil
	}
	return &defaultTitle, nil
}

func (s *service) getFeedAdditionalInfo(ctx context.Context, additionalInfo *models.AdditionalInfo) (*models.FeedAdditionalInfo, error) {
	if additionalInfo == nil {
		zlog.Error(ctx, "Additional info is nil", systemErrors.ErrNotificationFeedAdditionalInfoNil)
		return nil, systemErrors.ErrNotificationFeedAdditionalInfoNil
	}

	if additionalInfo.FeedType == nil {
		zlog.Error(ctx, "Feed type is nil", systemErrors.ErrNotificationFeedTypeNil)
		return nil, systemErrors.ErrNotificationFeedTypeNil
	}

	switch *additionalInfo.FeedType {
	case models.FeedTypeConnection:
		if additionalInfo.ConnectionRequestSentBy == nil {
			zlog.Error(ctx, "Connection request sent by is nil", systemErrors.ErrNotificationFeedAdditionalInfoNil)
			return nil, systemErrors.ErrNotificationFeedAdditionalInfoNil
		}
		if additionalInfo.ConnectionRequestSentBy == nil {
			zlog.Error(ctx, "Connection request sent by is nil", systemErrors.ErrNotificationFeedAdditionalInfoNil)
			return nil, systemErrors.ErrNotificationFeedAdditionalInfoNil
		}

		return &models.FeedAdditionalInfo{
			ConnectionRequest: &models.ConnectionRequest{
				SentBy: *additionalInfo.ConnectionRequestSentBy,
			},
		}, nil
	default:
		return nil, nil
	}
}
