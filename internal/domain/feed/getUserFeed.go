package feed

import (
	"bytes"
	"context"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) GetUserFeed(ctx context.Context, lastId *primitive.ObjectID, pageSize *int) (*models.FeedResponse, error) {
	zlog.Info(ctx, "Getting user feed")
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Failed to get user from context", err)
		return nil, err
	}
	_pageSize := constants.UserFeedPageSize
	if pageSize != nil {
		_pageSize = *pageSize
	}
	user, err := s.coreService.GetUserByID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get user by id", err)
		return nil, err
	}
	zlog.Info(ctx, "found user feed", zap.String("user_id", userID.Hex()))
	userFeeds, err := s.feedRepository.GetUserFeed(ctx, userID, lastId, _pageSize)
	if err != nil {
		zlog.Error(ctx, "Failed to get user feed", err)
		return nil, err
	}

	if len(userFeeds) == 0 {
		return &models.FeedResponse{
			Feeds:  userFeeds,
			IsRead: true,
		}, nil
	}

	var userIds []primitive.ObjectID
	for _, feed := range userFeeds {
		if feed.FeedData == nil {
			zlog.Error(ctx, "Feed data is nil", systemErrors.ErrNotificationFeedDataNil)
			return nil, systemErrors.ErrNotificationFeedDataNil
		}

		if feed.FeedType == models.FeedTypeConnection {
			if feed.FeedData.AdditionalInfo == nil {
				zlog.Error(ctx, "Feed additional info is nil", systemErrors.ErrNotificationFeedAdditionalInfoNil)
				return nil, systemErrors.ErrNotificationFeedAdditionalInfoNil
			}
			if feed.FeedData.AdditionalInfo.ConnectionRequest == nil {
				zlog.Error(ctx, "Connection request is nil", systemErrors.ErrNotificationFeedAdditionalInfoNil)
				return nil, systemErrors.ErrNotificationFeedAdditionalInfoNil
			}

			userIds = append(userIds, feed.FeedData.AdditionalInfo.ConnectionRequest.SentBy)
		}
	}

	var UserPublicDetails []*models.UserPublicDetails
	if len(userIds) > 0 {
		users, err := s.coreService.GetUsersByIDs(ctx, userIds)
		if err != nil {
			zlog.Error(ctx, "Failed to get users by ids", err)
			return nil, err
		}
		if users == nil {
			zlog.Error(ctx, "Users is nil", systemErrors.ErrUserNotFound)
			return nil, systemErrors.ErrUserNotFound
		}
		for _, user := range users {
			UserPublicDetails = append(UserPublicDetails, utils.GetUserPublicDetails(user))
		}
	}

	isRead := false
	if user.LastReadFeedID != nil {
		isRead = bytes.Compare(userFeeds[0].ID[:], (*user.LastReadFeedID)[:]) <= 0
	}

	return &models.FeedResponse{
		Feeds:       userFeeds,
		UserDetails: UserPublicDetails,
		HasMore:     len(userFeeds) == _pageSize,
		LastID:      &userFeeds[len(userFeeds)-1].ID,
		IsRead:      isRead,
	}, nil
}

func (s *service) HandleConnectionRequest(ctx context.Context, feedData *models.FeedData) error {
	if feedData.AdditionalInfo == nil {
		zlog.Error(ctx, "Additional info is nil", systemErrors.ErrNotificationFeedAdditionalInfoNil)
		return systemErrors.ErrNotificationFeedAdditionalInfoNil
	}

	if feedData.AdditionalInfo.ConnectionRequest == nil {
		zlog.Error(ctx, "Connection request is nil", systemErrors.ErrNotificationFeedConnectionRequestNil)
		return systemErrors.ErrNotificationFeedConnectionRequestNil
	}

	return nil
}
