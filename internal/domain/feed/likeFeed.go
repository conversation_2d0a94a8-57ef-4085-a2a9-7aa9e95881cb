package feed

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) UpdateLikeStatus(ctx context.Context, feedID primitive.ObjectID) (bool, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Failed to get user id from context: ", err)
		return false, err
	}
	userFeed, err := s.feedRepository.GetFeedById(ctx, feedID)
	if err != nil {
		zlog.Error(ctx, "Failed to get user feed: ", err)
		return false, err
	}

	if userFeed.UserID != userId {
		zlog.Error(ctx, "User id does not match", systemErrors.ErrFeedUserIdMismatch)
		return false, systemErrors.ErrFeedUserIdMismatch
	}

	if userFeed == nil {
		zlog.Error(ctx, "User feed not found", systemErrors.ErrFeedFoundNil)
		return false, systemErrors.ErrFeedFoundNil
	}
	if userFeed.FeedReferenceID.IsZero() {
		zlog.Error(ctx, "Feed reference id is nil", systemErrors.ErrFeedFeedReferenceIdNil)
		return false, systemErrors.ErrFeedFeedReferenceIdNil
	}

	shouldLike := !userFeed.IsLiked
	userFeed.IsLiked = shouldLike

	if err := s.feedRepository.UpdateFeed(ctx, userFeed); err != nil {
		zlog.Error(ctx, "Failed to update user feed: ", err)
		return false, err
	}

	zlog.Info(ctx, " updating like count: ", zap.String("feedId", userFeed.FeedReferenceID.Hex()), zap.Bool("shouldLike", shouldLike))
	return s.feedDataRepository.UpdateLikeCount(ctx, userFeed.FeedReferenceID, shouldLike)
}
