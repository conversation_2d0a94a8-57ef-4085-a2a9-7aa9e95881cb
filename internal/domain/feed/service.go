package feed

import (
	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
)

type service struct {
	feedRepository     repository.FeedRepository
	feedDataRepository repository.FeedDataRepository
	coreService        domain.CoreLogicStore
	friendRepository   repository.FriendsAndFollowersRepository
	userRepo           repository.UserRepository
}

func NewFeedService(
	repositories *repository.RepositoryFactory,
	coreService domain.CoreLogicStore,
) domain.FeedStore {
	return &service{
		feedRepository:     repositories.FeedRepository,
		feedDataRepository: repositories.FeedDataRepository,
		coreService:        coreService,
		friendRepository:   repositories.FriendsAndFollowersRepository,
		userRepo:           repositories.UserRepository,
	}
}
