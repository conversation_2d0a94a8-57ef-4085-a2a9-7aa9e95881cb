package feed

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) UpdateLastReadFeedId(ctx context.Context, lastReadFeedID primitive.ObjectID) (bool, error) {
	zlog.Info(ctx, "Updating last read at")
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Failed to get user from context", err)
		return false, err
	}

	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get user by id", err)
		return false, err
	}

	user.LastReadFeedID = &lastReadFeedID
	if err := s.userRepo.UpdateOne(ctx, bson.M{"_id": userID}, bson.M{"$set": bson.M{"lastReadFeedId": lastReadFeedID}}); err != nil {
		zlog.Error(ctx, "Failed to update last read at", err)
		return false, err
	}

	return true, nil
}
