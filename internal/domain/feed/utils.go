package feed

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) HandleFeedWithFriends(ctx context.Context, feed *models.UserFeed) error {
	var userFeeds []*models.UserFeed
	feedData := feed.FeedData
	feed.FeedData = nil
	userFeeds = append(userFeeds, feed)

	if feed.FeedType != models.FeedTypeCelebration && feed.FeedType != models.FeedTypeDailyChallenge {
		if err := s.feedRepository.CreateUserFeed(ctx, userFeeds[0]); err != nil {
			zlog.Error(ctx, "Failed to create user feed", err)
			return err
		}
		return nil
	}
	if feedData == nil {
		zlog.Error(ctx, "Feed data is nil", systemErrors.ErrNotificationFeedDataNil)
		return systemErrors.ErrNotificationFeedDataNil
	}
	if feedData.FeedForFriends == nil {
		zlog.Error(ctx, "Feed for friends is nil", systemErrors.ErrNotificationFeedForFriendsNil)
		return systemErrors.ErrNotificationFeedForFriendsNil
	}

	user, err := s.coreService.GetUserByID(ctx, feed.UserID)
	if err != nil {
		zlog.Error(ctx, "Failed to get user", err)
		return err
	}
	if user == nil {
		zlog.Error(ctx, "User not found", systemErrors.ErrUserNotFound, zap.String("id", feed.UserID.Hex()))
		return systemErrors.ErrUserNotFound
	}

	friends, err := s.friendRepository.GetFriendsUserIds(ctx, feed.UserID)
	if err != nil {
		zlog.Error(ctx, "Failed to get friends user ids", err)
		return err
	}

	for _, friendId := range friends {
		if friendId == primitive.NilObjectID || friendId == feed.UserID {
			zlog.Error(ctx, "Friend id is same as user id", systemErrors.ErrNotificationFeedForFriendsSameUserId)
			continue
		}
		userFeeds = append(userFeeds, &models.UserFeed{
			ID:              primitive.NewObjectID(),
			UserID:          friendId,
			FeedType:        feed.FeedType,
			FeedReferenceID: feed.FeedReferenceID,
			IsLiked:         false,
			ExpirationTime:  time.Now().Add(24 * time.Hour),
			CreatedAt:       time.Now(),
		})
	}

	if err := s.feedRepository.BatchInsertUserFeed(ctx, userFeeds); err != nil {
		zlog.Error(ctx, "Failed to create user feed", err)
		return err
	}

	return nil
}

func (s *service) HandleFeed(ctx context.Context, feed *models.UserFeed) error {
	if feed == nil {
		zlog.Error(ctx, "Feed is nil", systemErrors.ErrNotificationFeedNil)
		return systemErrors.ErrNotificationFeedNil
	}
	if feed.FeedType == "" {
		zlog.Error(ctx, "Feed type is nil", systemErrors.ErrNotificationFeedTypeNil)
		return systemErrors.ErrNotificationFeedTypeNil
	}
	if err := s.feedRepository.CreateUserFeed(ctx, feed); err != nil {
		zlog.Error(ctx, "Failed to create user feed", err)
		return err
	}

	return nil
}
