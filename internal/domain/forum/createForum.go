package forum

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *service) CreateForum(ctx context.Context, input models.CreateForumInput) (*models.Forum, error) {
	// userID, err := utils.GetUserFromContext(ctx)

	// if err != nil {
	// 	return nil, err
	// }

	// newForum := &models.Forum{
	// 	ID:          primitive.NewObjectID(),
	// 	ClubID:      input.ClubID,
	// 	Title:       input.Title,
	// 	Description: input.Description,
	// 	CreatedAt:   time.Now(),
	// 	UpdatedAt:   time.Now(),
	// 	CreatedBy:   userID,
	// }

	// err = s.clubRepo.CreateForum(ctx, newForum)

	// if err != nil {
	// 	return nil, err
	// }

	panic(ctx)
}
