package forum

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) CreateForumReply(ctx context.Context, input models.CreateForumReplyInput) (*models.ForumReply, error) {
	if input.ThreadID == primitive.NilObjectID {
		return nil, fmt.Errorf("thread ID cannot be empty")
	}
	if input.Content == "" {
		return nil, fmt.Errorf("content cannot be empty")
	}
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	newReply := &models.ForumReply{
		ID:        primitive.NewObjectID(),
		ThreadID:  input.ThreadID,
		Content:   input.Content,
		CreatedAt: time.Now(),
		CreatedBy: userID,
	}

	forumReply, err := s.forumRepo.CreateForumReply(ctx, newReply)
	if err != nil {
		return nil, err
	}

	return forumReply, nil
}
