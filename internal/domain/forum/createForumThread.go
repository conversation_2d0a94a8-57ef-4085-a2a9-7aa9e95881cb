package forum

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) CreateForumThread(ctx context.Context, input models.CreateForumThreadInput) (*models.ForumThread, error) {
	if input.ForumID == primitive.NilObjectID {
		return nil, fmt.Errorf("forum ID cannot be empty")
	}
	if input.Title == "" {
		return nil, fmt.Errorf("title cannot be empty")
	}
	if input.Content == "" {
		return nil, fmt.Errorf("content cannot be empty")
	}
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	newThread := &models.ForumThread{
		ID:        primitive.NewObjectID(),
		ForumID:   input.ForumID,
		Title:     input.Title,
		Content:   input.Content,
		CreatedAt: time.Now(),
		CreatedBy: userID,
	}

	forumThread, err := s.forumRepo.CreateForumThread(ctx, newThread)
	if err != nil {
		return nil, err
	}

	return forumThread, nil
}
