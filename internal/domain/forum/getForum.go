package forum

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) Forum(ctx context.Context, id primitive.ObjectID) (*models.Forum, error) {
	if id == primitive.NilObjectID {
		return nil, fmt.Errorf("forum ID cannot be empty")
	}
	forum, err := s.forumRepo.GetForum(ctx, id)
	if err != nil {
		return nil, err
	}

	if forum == nil {
		return nil, fmt.Errorf("forum not found")
	}

	creatorInfo, err := s.userService.GetUserByID(ctx, forum.CreatedBy)
	if err != nil {
		return nil, err
	}

	forum.CreatorInfo = &models.CreatorInfo{
		Username:        creatorInfo.Username,
		ProfileImageURL: creatorInfo.ProfileImageURL,
	}

	return forum, nil
}
