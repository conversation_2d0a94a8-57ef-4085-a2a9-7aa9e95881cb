package forum

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) ForumReplies(ctx context.Context, threadID primitive.ObjectID, page, pageSize *int) (*models.RepliesPage, error) {
	if threadID == primitive.NilObjectID {
		return nil, fmt.Errorf("thread ID cannot be empty")
	}
	pageNum := 1
	if page != nil && *page > 0 {
		pageNum = *page
	}

	size := 10
	if pageSize != nil && *pageSize > 0 {
		size = *pageSize
	}

	sort := bson.D{{Key: "createdAt", Value: -1}}

	replies, err := s.forumRepo.GetForumReplies(ctx, threadID, &pageNum, &size, sort)
	if err != nil {
		return nil, fmt.Errorf("failed to get forum replies: %w", err)
	}

	totalCount, err := s.forumRepo.CountForumReplies(ctx, threadID)
	if err != nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("failed to count forum replies: %w", err)
	}

	hasMore := int64(pageNum*size) < totalCount
	totalResults := int(totalCount)

	return &models.RepliesPage{
		Results:      replies,
		PageNumber:   pageNum,
		PageSize:     size,
		HasMore:      &hasMore,
		TotalResults: &totalResults,
	}, nil
}
