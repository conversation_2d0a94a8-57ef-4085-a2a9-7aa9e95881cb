package forum

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) ForumThreads(ctx context.Context, forumID primitive.ObjectID, page, pageSize *int) (*models.ThreadsPage, error) {
	if forumID == primitive.NilObjectID {
		return nil, fmt.Errorf("forum ID cannot be empty")
	}
	pageNum := 1
	if page != nil && *page > 0 {
		pageNum = *page
	}

	size := 10
	if pageSize != nil && *pageSize > 0 {
		size = *pageSize
	}

	sort := bson.D{{Key: "createdAt", Value: -1}}

	threads, err := s.forumRepo.GetForumThreads(ctx, forumID, &pageNum, &size, sort)
	if err != nil {
		return nil, fmt.Errorf("failed to get forum threads: %w", err)
	}

	totalCount, err := s.forumRepo.CountForumThreads(ctx, forumID)
	if err != nil {
		return nil, fmt.E<PERSON><PERSON>("failed to count forum threads: %w", err)
	}

	hasMore := int64(pageNum*size) < totalCount
	totalResults := int(totalCount)

	return &models.ThreadsPage{
		Results:      threads,
		PageNumber:   pageNum,
		PageSize:     size,
		HasMore:      &hasMore,
		TotalResults: &totalResults,
	}, nil
}
