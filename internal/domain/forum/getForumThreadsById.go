package forum

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) ForumThread(ctx context.Context, id primitive.ObjectID) (*models.ForumThread, error) {
	if id == primitive.NilObjectID {
		return nil, fmt.Errorf("thread ID cannot be empty")
	}
	thread, err := s.forumRepo.GetForumThread(ctx, id)
	if err != nil {
		return nil, err
	}

	if thread == nil {
		return nil, fmt.Errorf("forum thread not found")
	}

	creatorInfo, err := s.userService.GetUserByID(ctx, thread.CreatedBy)
	if err != nil {
		return nil, err
	}

	thread.CreatorInfo = &models.CreatorInfo{
		Username:        creatorInfo.Username,
		ProfileImageURL: creatorInfo.ProfileImageURL,
	}

	return thread, nil
}
