package forum

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) Forums(ctx context.Context, clubID primitive.ObjectID, page, pageSize *int) (*models.ForumPage, error) {
	if clubID == primitive.NilObjectID {
		return nil, fmt.Errorf("club ID cannot be empty")
	}
	pageNum := 1
	if page != nil && *page > 0 {
		pageNum = *page
	}

	size := 10
	if pageSize != nil && *pageSize > 0 {
		size = *pageSize
	}

	sort := bson.D{{Key: "createdAt", Value: -1}}

	forums, err := s.forumRepo.GetForums(ctx, clubID, &pageNum, &size, sort)
	if err != nil {
		return nil, fmt.Errorf("failed to get forums: %w", err)
	}

	totalCount, err := s.forumRepo.CountForums(ctx, clubID)
	if err != nil {
		return nil, fmt.Errorf("failed to count forums: %w", err)
	}

	hasMore := int64(pageNum*size) < totalCount
	totalResults := int(totalCount)

	return &models.ForumPage{
		Results:      forums,
		PageNumber:   pageNum,
		PageSize:     size,
		HasMore:      &hasMore,
		TotalResults: &totalResults,
	}, nil
}
