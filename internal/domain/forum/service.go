package forum

import (
	"context"

	"go.uber.org/fx"
	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

type service struct {
	forumRepo   repository.ForumRepository
	userService domain.UserStore
}

func NewForumService(lc fx.Lifecycle, userService domain.UserStore,
	repositoryFactory *repository.RepositoryFactory,
) domain.ForumStore {
	s := &service{
		forumRepo:   repositoryFactory.ForumRepository,
		userService: userService,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting forum service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down forum service")
			return nil
		},
	})

	return s
}
