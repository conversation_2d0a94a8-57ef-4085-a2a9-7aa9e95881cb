package friends

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func (s *service) AcceptFriendRequest(ctx context.Context, acceptRequestInput *models.FriendRequestInput) (bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Error retrieving active games:", err)
		return false, err
	}

	senderId := acceptRequestInput.UserID

	if senderId == userID {
		return false, fmt.Errorf("Cann't accept friend request of  your own")
	}

	isExist, friendRequest, err := s.friendsRepo.CheckIfFriendRequestSent(ctx, senderId, userID)
	if err != nil {
		return false, err
	}

	if !isExist {
		return false, fmt.Errorf("friend Request doesn't exist")
	}

	friend := models.Friends{
		SenderID:   senderId,
		ReceiverID: userID,
		AcceptedAt: utils.AllocPtr(time.Now()),
	}
	_, err = s.friendsRepo.CreateFriends(ctx, friend)
	if err != nil {
		return false, err
	}

	success, err := s.friendsRepo.DeleteFriendRequest(ctx, bson.M{"_id": friendRequest.ID})
	if err != nil || !success {
		return false, err
	}

	err = s.userRepo.IncrementUserFriendsCount(ctx, senderId)
	if err != nil {
		return false, fmt.Errorf("failed to Increment sender user friends count: %w", err)
	}

	err = s.userRepo.IncrementUserFriendsCount(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to Increment receiver user friends count: %w", err)
	}

	return true, nil
}
