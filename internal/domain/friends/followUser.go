package friends

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *service) FollowUser(ctx context.Context, followUserInput *models.FollowUserInput) (bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	followeeId := followUserInput.UserID

	if followeeId == userID {
		return false, fmt.Errorf("you can't follow yourself")
	}

	isExist, _, err := s.friendsRepo.CheckIfAlreadyFollowing(ctx, userID, followeeId)
	if err != nil {
		return false, err
	}

	if isExist {
		return false, fmt.Errorf("You're already following him")
	}

	followReq := models.FollowersAndFollowee{
		FollowerID: userID,
		FolloweeID: followeeId,
		FollowedAt: utils.AllocPtr(time.Now()),
	}

	_, err = s.friendsRepo.AddFollower(ctx, followReq)
	if err != nil {
		return false, err
	}

	err = s.userRepo.IncrementUserFollowersCount(ctx, followeeId)
	if err != nil {
		return false, fmt.Errorf("failed to increment followers count: %w", err)
	}

	err = s.userRepo.IncrementUserFollowingsCount(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to increment followings count: %w", err)
	}

	return true, nil
}
