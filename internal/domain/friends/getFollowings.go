package friends

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) GetFollowings(ctx context.Context, pageNumber, pageSize *int) (*models.FollowersAndFolloweePage, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("unauthorized: user not authenticated")
	}
	if pageNumber == nil || *pageNumber == 0 {
		defaultPageNumber := 1
		pageNumber = &defaultPageNumber
	}

	if pageSize == nil {
		defaultPageSize := 20
		pageSize = &defaultPageSize
	}

	skip := (*pageNumber - 1) * *pageSize

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"followerId": userID}}},
		{{Key: "$facet", Value: bson.M{
			"metadata": bson.A{bson.M{"$count": "total"}},
			"data": bson.A{
				bson.M{"$skip": skip},
				bson.M{"$limit": *pageSize},
				bson.M{"$lookup": bson.M{
					"from":         "users",
					"localField":   "followerId",
					"foreignField": "_id",
					"as":           "follower",
					"pipeline": bson.A{
						bson.M{"$project": bson.M{
							"_id":             1,
							"name":            1,
							"username":        1,
							"profileImageUrl": 1,
							"rating":          1,
						}},
					},
				}},
				bson.M{"$lookup": bson.M{
					"from":         "users",
					"localField":   "followeeId",
					"foreignField": "_id",
					"as":           "followee",
					"pipeline": bson.A{
						bson.M{"$project": bson.M{
							"_id":             1,
							"name":            1,
							"username":        1,
							"profileImageUrl": 1,
							"rating":          1,
						}},
					},
				}},
				bson.M{"$unwind": "$follower"},
				bson.M{"$unwind": "$followee"},
				bson.M{"$addFields": bson.M{
					"otherUser": bson.M{
						"$cond": bson.M{
							"if":   bson.M{"$eq": bson.A{"$followerId", userID}},
							"then": "$followee",
							"else": "$follower",
						},
					},
				}},
				bson.M{"$project": bson.M{
					"_id":        1,
					"followerId": 1,
					"followeeId": 1,
					"followedAt": 1,
					"follower":   1,
					"followee":   1,
					"otherUser":  1,
				}},
			},
		}}},
		{{Key: "$project", Value: bson.M{
			"results":      "$data",
			"pageNumber":   bson.M{"$literal": *pageNumber},
			"pageSize":     bson.M{"$literal": *pageSize},
			"totalResults": bson.M{"$arrayElemAt": bson.A{"$metadata.total", 0}},
		}}},
	}

	cur, err := s.friendsRepo.AggregateFollowers(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("aggregation failed: %w", err)
	}
	defer cur.Close(ctx)

	var results []bson.M
	if err := cur.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("error decoding results: %w", err)
	}

	if len(results) == 0 || results[0]["results"] == nil {
		return &models.FollowersAndFolloweePage{
			Results:      []*models.FollowersAndFolloweeOutput{},
			PageNumber:   *pageNumber,
			PageSize:     *pageSize,
			HasMore:      utils.AllocPtr(false),
			TotalResults: utils.AllocPtr(0),
		}, nil
	}

	resultData := results[0]
	totalResults := utils.ToInt(resultData["totalResults"])
	hasMore := totalResults > (skip + *pageSize)

	followResults := make([]*models.FollowersAndFolloweeOutput, 0)
	if resultsArr, ok := resultData["results"].(bson.A); ok {
		for _, result := range resultsArr {
			if resultDoc, ok := result.(bson.M); ok {
				fq := models.FollowersAndFolloweeOutput{
					ID:         resultDoc["_id"].(primitive.ObjectID),
					FollowerID: resultDoc["followerId"].(primitive.ObjectID),
					FolloweeID: resultDoc["followeeId"].(primitive.ObjectID),
					FollowedAt: utils.AllocPtr(resultDoc["followedAt"].(primitive.DateTime).Time()),
					UserInfo:   utils.ExtractUserPublicDetails(resultDoc["otherUser"].(bson.M)),
				}
				followResults = append(followResults, utils.AllocPtr(fq))
			}
		}
	}

	return &models.FollowersAndFolloweePage{
		Results:      followResults,
		PageNumber:   *pageNumber,
		PageSize:     *pageSize,
		HasMore:      utils.AllocPtr(hasMore),
		TotalResults: utils.AllocPtr(totalResults),
	}, nil
}
