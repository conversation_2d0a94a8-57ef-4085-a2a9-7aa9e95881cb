package friends

import (
	"context"
	"fmt"
	sorts "sort"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

const MaxPageSize = 200

func (s *service) GetFriends(ctx context.Context, pageNumber, pageSize *int, sort *models.SortOptions) (*models.FriendsPage, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("unauthorized: user not authenticated")
	}

	if pageNumber == nil || *pageNumber == 0 {
		defaultPageNumber := 1
		pageNumber = &defaultPageNumber
	}

	if pageSize == nil {
		defaultPageSize := 20
		pageSize = &defaultPageSize
	}

	sortField := "lastInteracted"
	sortDirection := -1
	if sort != nil {
		if sort.SortBy != "" {
			sortField = sort.SortBy
		}
		if sort.SortDirection != 0 {
			sortDirection = sort.SortDirection
		}
	}

	skip := (*pageNumber - 1) * *pageSize

	friendDocumentFields := map[string]bool{
		"lastInteracted": true,
		"acceptedAt":     true,
		"createdAt":      true,
	}

	userDocumentsField := map[string]bool{
		"rating": true,
	}

	var sortStage bson.D
	if friendDocumentFields[sortField] {
		sortStage = bson.D{{Key: "$sort", Value: bson.M{sortField: sortDirection}}}
	} else if userDocumentsField[sortField] {
		sortStage = bson.D{{Key: "$sort", Value: bson.M{"otherUser.rating": sortDirection}}}
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"$or": []bson.M{
				{"senderId": userID},
				{"receiverId": userID},
			},
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "users",
			"localField":   "senderId",
			"foreignField": "_id",
			"as":           "sender",
			"pipeline": bson.A{
				bson.M{"$project": bson.M{
					"_id":             1,
					"name":            1,
					"username":        1,
					"profileImageUrl": 1,
					"rating":          1,
				}},
			},
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "users",
			"localField":   "receiverId",
			"foreignField": "_id",
			"as":           "receiver",
			"pipeline": bson.A{
				bson.M{"$project": bson.M{
					"_id":             1,
					"name":            1,
					"username":        1,
					"profileImageUrl": 1,
					"rating":          1,
				}},
			},
		}}},
		{{Key: "$unwind", Value: "$sender"}},
		{{Key: "$unwind", Value: "$receiver"}},
		{{Key: "$addFields", Value: bson.M{
			"otherUser": bson.M{
				"$cond": bson.M{
					"if":   bson.M{"$eq": bson.A{"$senderId", userID}},
					"then": "$receiver",
					"else": "$sender",
				},
			},
		}}},
	}

	if !friendDocumentFields[sortField] {
		pipeline = append(pipeline, bson.D{{Key: "$addFields", Value: bson.M{
			"sortField": bson.M{
				"$cond": bson.M{
					"if":   bson.M{"$eq": bson.A{"$senderId", userID}},
					"then": fmt.Sprintf("$receiver.%s", sortField),
					"else": fmt.Sprintf("$sender.%s", sortField),
				},
			},
		}}})
	}

	pipeline = append(pipeline, bson.D{{Key: "$facet", Value: bson.M{
		"metadata": bson.A{bson.M{"$count": "total"}},
		"data": bson.A{
			sortStage,
			bson.M{"$skip": skip},
			bson.M{"$limit": *pageSize},
			bson.M{"$project": bson.M{
				"_id":            1,
				"senderId":       1,
				"receiverId":     1,
				"acceptedAt":     1,
				"lastInteracted": 1,
				"otherUser":      1,
			}},
		},
	}}})

	pipeline = append(pipeline, bson.D{{Key: "$project", Value: bson.M{
		"results":      "$data",
		"pageNumber":   bson.M{"$literal": *pageNumber},
		"pageSize":     bson.M{"$literal": *pageSize},
		"totalResults": bson.M{"$arrayElemAt": bson.A{"$metadata.total", 0}},
	}}})

	cur, err := s.friendsRepo.AggregateFriends(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("aggregation failed: %w", err)
	}
	defer cur.Close(ctx)

	var results []bson.M
	if err := cur.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("error decoding results: %w", err)
	}

	if len(results) == 0 || results[0]["results"] == nil {
		return &models.FriendsPage{
			Results:      []*models.FriendsOutput{},
			PageNumber:   *pageNumber,
			PageSize:     *pageSize,
			HasMore:      utils.AllocPtr(false),
			TotalResults: utils.AllocPtr(0),
		}, nil
	}

	onlineUsersMap := make(map[primitive.ObjectID]*models.UserDetailWithActivity)
	onlineUsers, err := s.userService.OnlineUsers(ctx, 1, MaxPageSize)
	if err != nil {
		zlog.Error(ctx, "error getting online users", err)
		return nil, fmt.Errorf("error getting online users: %w", err)
	}
	for _, onlineUser := range onlineUsers.Users {
		onlineUsersMap[onlineUser.UserInfo.ID] = onlineUser
	}

	resultData := results[0]
	totalResults := utils.ToInt(resultData["totalResults"])
	hasMore := totalResults > (skip + *pageSize)

	requestResults := make([]*models.FriendsOutput, 0)
	if resultsArr, ok := resultData["results"].(bson.A); ok {
		for _, result := range resultsArr {
			if resultDoc, ok := result.(bson.M); ok {
				friendReq := models.FriendsOutput{
					ID:         resultDoc["_id"].(primitive.ObjectID),
					SenderID:   resultDoc["senderId"].(primitive.ObjectID),
					ReceiverID: resultDoc["receiverId"].(primitive.ObjectID),
					AcceptedAt: utils.AllocPtr(resultDoc["acceptedAt"].(primitive.DateTime).Time()),
					FriendInfo: utils.ExtractUserPublicDetails(resultDoc["otherUser"].(bson.M)),
				}
				friendUserID := friendReq.FriendInfo.ID
				if onlineUser, ok := onlineUsersMap[friendUserID]; ok {
					friendReq.IsOnline = true
					friendReq.CurrActivity = onlineUser.CurrActivity
				} else {
					friendReq.IsOnline = false
					friendReq.CurrActivity = utils.AllocPtr(models.Exploring)
				}
				requestResults = append(requestResults, &friendReq)
			}
		}
	}
	sorts.Slice(requestResults, func(i, j int) bool {
		return requestResults[i].IsOnline && !requestResults[j].IsOnline
	})

	return &models.FriendsPage{
		Results:      requestResults,
		PageNumber:   *pageNumber,
		PageSize:     *pageSize,
		HasMore:      utils.AllocPtr(hasMore),
		TotalResults: utils.AllocPtr(totalResults),
	}, nil
}
