package friends

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) GetPendingFriendRequests(ctx context.Context, pageNumber, pageSize *int) (*models.FriendRequestPage, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("unauthorized: user not authenticated")
	}
	if pageNumber == nil || *pageNumber == 0 {
		defaultPageNumber := 1
		pageNumber = &defaultPageNumber
	}

	if pageSize == nil {
		defaultPageSize := 20
		pageSize = &defaultPageSize
	}

	skip := (*pageNumber - 1) * *pageSize

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"status":     models.FriendRequestStatusPending,
			"receiverId": userID,
		}}},
		{{Key: "$facet", Value: bson.M{
			"metadata": bson.A{bson.M{"$count": "total"}},
			"data": bson.A{
				bson.M{"$skip": skip},
				bson.M{"$limit": *pageSize},
				bson.M{"$lookup": bson.M{
					"from":         "users",
					"localField":   "senderId",
					"foreignField": "_id",
					"as":           "sender",
					"pipeline": bson.A{
						bson.M{"$project": bson.M{
							"_id":             1,
							"name":            1,
							"username":        1,
							"profileImageUrl": 1,
							"rating":          1,
						}},
					},
				}},
				bson.M{"$lookup": bson.M{
					"from":         "users",
					"localField":   "receiverId",
					"foreignField": "_id",
					"as":           "receiver",
					"pipeline": bson.A{
						bson.M{"$project": bson.M{
							"_id":             1,
							"name":            1,
							"username":        1,
							"profileImageUrl": 1,
							"rating":          1,
						}},
					},
				}},
				bson.M{"$unwind": "$sender"},
				bson.M{"$unwind": "$receiver"},
				bson.M{"$addFields": bson.M{
					"otherUser": bson.M{
						"$cond": bson.M{
							"if":   bson.M{"$eq": bson.A{"$senderId", userID}},
							"then": "$receiver",
							"else": "$sender",
						},
					},
				}},
				bson.M{"$project": bson.M{
					"_id":         1,
					"senderId":    1,
					"receiverId":  1,
					"status":      1,
					"sentAt":      1,
					"respondedAt": 1,
					"sender":      1,
					"receiver":    1,
					"otherUser":   1,
				}},
			},
		}}},
		{{Key: "$addFields", Value: bson.M{}}},
		{{Key: "$project", Value: bson.M{
			"results":      "$data",
			"pageNumber":   bson.M{"$literal": *pageNumber},
			"pageSize":     bson.M{"$literal": *pageSize},
			"totalResults": bson.M{"$arrayElemAt": bson.A{"$metadata.total", 0}},
		}}},
	}

	cur, err := s.friendsRepo.AggregateFriendRequests(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("aggregation failed: %w", err)
	}
	defer cur.Close(ctx)

	var results []bson.M
	if err := cur.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("error decoding results: %w", err)
	}

	if len(results) == 0 || results[0]["results"] == nil {
		return &models.FriendRequestPage{
			Results:      []*models.FriendRequestOutput{},
			PageNumber:   *pageNumber,
			PageSize:     *pageSize,
			HasMore:      utils.AllocPtr(false),
			TotalResults: utils.AllocPtr(0),
		}, nil
	}

	resultData := results[0]
	totalResults := utils.ToInt(resultData["totalResults"])
	hasMore := totalResults > (skip + *pageSize)

	requestResults := make([]*models.FriendRequestOutput, 0)
	if resultsArr, ok := resultData["results"].(bson.A); ok {
		for _, result := range resultsArr {
			if resultDoc, ok := result.(bson.M); ok {
				friendReq := models.FriendRequestOutput{
					ID:         resultDoc["_id"].(primitive.ObjectID),
					SenderID:   resultDoc["senderId"].(primitive.ObjectID),
					ReceiverID: resultDoc["receiverId"].(primitive.ObjectID),
					Status:     utils.AllocPtr(models.FriendRequestStatus(resultDoc["status"].(string))),
					SentAt:     utils.AllocPtr(resultDoc["sentAt"].(primitive.DateTime).Time()),
					Sender:     utils.ExtractUserPublicDetails(resultDoc["otherUser"].(bson.M)),
				}
				requestResults = append(requestResults, &friendReq)
			}
		}
	}

	return &models.FriendRequestPage{
		Results:      requestResults,
		PageNumber:   *pageNumber,
		PageSize:     *pageSize,
		HasMore:      utils.AllocPtr(hasMore),
		TotalResults: utils.AllocPtr(totalResults),
	}, nil
}
