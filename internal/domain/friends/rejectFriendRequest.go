package friends

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
)

func (s *service) RejectFriendRequest(ctx context.Context, rejectRequestInput *models.FriendRequestInput) (bool, error) {
	senderId := rejectRequestInput.UserID

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Error retrieving active games:", err)
		return false, err
	}

	return s.friendsRepo.DeleteFriendRequest(ctx, bson.M{"receiverId": userID, "senderId": senderId})
}
