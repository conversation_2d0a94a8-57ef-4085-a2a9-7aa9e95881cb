package friends

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func (s *service) RemoveFollower(ctx context.Context, removeFollowerInput *models.RemoveFollowerInput) (bool, error) {
	followerId := removeFollowerInput.UserID

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Error retrieving active games:", err)
		return false, err
	}

	isExist, followReq, err := s.friendsRepo.CheckIfAlreadyFollowing(ctx, followerId, userID)
	if err != nil {
		return false, err
	}

	if !isExist {
		return false, fmt.Errorf("to Remove Someone from Follower list first he/she should be following you")
	}

	_, err = s.friendsRepo.RemoveFollower(ctx, followReq.ID)
	if err != nil {
		return false, err
	}

	err = s.userRepo.DecrementUserFollowersCount(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to decrement Followers Count: %w", err)
	}

	err = s.userRepo.DecrementUserFollowingsCount(ctx, followerId)
	if err != nil {
		return false, fmt.Errorf("failed to decrement Followings Count: %w", err)
	}

	return true, nil
}
