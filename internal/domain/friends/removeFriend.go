package friends

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
)

func (s *service) RemoveFriend(ctx context.Context, removeFriendInput *models.RemoveFriendInput) (bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Error retrieving  user details:", err)
		return false, err
	}

	filter := bson.M{
		"$or": []bson.M{
			{"senderId": removeFriendInput.UserID, "receiverId": userID},
			{"senderId": userID, "receiverId": removeFriendInput.UserID},
		},
	}

	success, err := s.friendsRepo.DeleteFriend(ctx, filter)
	if err != nil || !success {
		return false, err
	}

	err = s.userRepo.DecrementUserFriendsCount(ctx, removeFriendInput.UserID)
	if err != nil {
		return false, fmt.Errorf("failed to decrement 2nd user friends count: %w", err)
	}

	err = s.userRepo.DecrementUserFriendsCount(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to decrement receiver your friends count: %w", err)
	}

	return true, nil
}
