package friends

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type service struct {
	userService         domain.UserStore
	userRepo            repository.UserRepository
	friendsRepo         repository.FriendsAndFollowersRepository
	notificationService domain.NotificationStore
}

func NewFriendAndFollowersService(lc fx.Lifecycle, userService domain.UserStore,
	repositoryFactory *repository.RepositoryFactory, notificationService domain.NotificationStore,
) domain.FriendsAndFollowersStore {
	s := &service{
		userService:         userService,
		userRepo:            repositoryFactory.UserRepository,
		friendsRepo:         repositoryFactory.FriendsAndFollowersRepository,
		notificationService: notificationService,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting friends and followers service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down friends and followers service")
			return nil
		},
	})

	return s
}
