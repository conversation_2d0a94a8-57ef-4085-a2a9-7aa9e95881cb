package friends

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func (s *service) UnFollowUser(ctx context.Context, unFollowUserInput *models.UnFollowUserInput) (bool, error) {
	followeeId := unFollowUserInput.UserID

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Error retrieving active games:", err)
		return false, err
	}

	isExist, followReq, err := s.friendsRepo.CheckIfAlreadyFollowing(ctx, userID, followeeId)
	if err != nil {
		return false, err
	}

	if !isExist {
		return false, fmt.Errorf("to unfollow someone you first need to follow them")
	}

	_, err = s.friendsRepo.RemoveFollower(ctx, followReq.ID)
	if err != nil {
		return false, err
	}

	err = s.userRepo.DecrementUserFollowersCount(ctx, followeeId)
	if err != nil {
		return false, fmt.Errorf("failed to decrement followers count: %w", err)
	}

	err = s.userRepo.DecrementUserFollowingsCount(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to decrement followings count: %w", err)
	}

	return true, nil
}
