package friends

import (
	"context"
	"errors"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) WithdrawFriendRequest(ctx context.Context, withrawFriendRequestInput *models.WithdrawFriendRequestInput) (bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Error retrieving active games:", err)
		return false, err
	}

	filter := bson.M{
		"$or": []bson.M{
			{"senderId": withrawFriendRequestInput.UserID, "receiverId": userID},
			{"senderId": userID, "receiverId": withrawFriendRequestInput.UserID},
		},
	}

	success, err := s.friendsRepo.DeleteFriendRequest(ctx, filter)
	if err != nil || !success {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, fmt.Errorf("Friend Request doesn't exist ")
		}
		return false, err
	}

	return true, nil
}
