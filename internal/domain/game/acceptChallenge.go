package game

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/game/utils/botutils"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/queue"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) AcceptChallenge(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Error retrieving user from context:", err)
		return nil, err
	}

	zlog.Debug(ctx, "Accepting challenge", zap.String("gameID", gameID.Hex()))

	game, err := s.gameRepo.GetGameByID(ctx, gameID)
	if err != nil {
		zlog.Error(ctx, "Error retrieving game:", err)
		return nil, err
	}

	if game.GameStatus != models.GameStatusCreated {
		return nil, fmt.Errorf("challenge is no longer valid")
	}

	user, err := s.userService.GetUserByID(ctx, game.Players[1].UserID)
	if err != nil {
		zlog.Error(ctx, "Error retrieving user:", err)
		return nil, err
	}

	isBot := false
	if (user.IsHumanBot != nil && *user.IsHumanBot) || (user.IsBot != nil && *user.IsBot) {
		isBot = true

		if user.IsHumanBot != nil && *user.IsHumanBot {
			if queue.PlayedInLastHour(game.Players[0].UserID, user) > 6 {
				zlog.Info(ctx, "Human bot has played a lot")
				return nil, nil
			}
			queue.UpdatePlayedInLastHour(game.Players[0].UserID, user)
			err := botutils.UpdateHumanBotCache(user, s.cache, true)
			if err != nil {
				return nil, err
			}

			go func(gameID, userID primitive.ObjectID) {
				ctx := utils.DeriveContextWithoutCancel(ctx)
				timer := time.NewTimer(20 * time.Second)
				defer timer.Stop()

				select {
				case <-ctx.Done():
					return
				case <-timer.C:
					s.cancelGameAndUpdateHumanBot(ctx, gameID, userID)
				}
			}(gameID, userID)
		}

	}

	if !isBot && game.Players[1].UserID != userID {
		return nil, fmt.Errorf("you are not the challenged player")
	}

	game.GameStatus = models.GameStatusReady

	err = s.gameRepo.UpdateGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Error updating game status:", err)
		return nil, err
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Error updating game in Cache:", err)
		return nil, err
	}

	err = s.publishGameEvent(ctx, game, constants.GameEventEnum.USER_JOINED.String())
	if err != nil {
		return nil, err
	}

	err = s.PublishChallengeEvent(ctx, game, string(models.ChallengeStatusChallengeAccepted))
	if err != nil {
		zlog.Error(ctx, "Error publishing challenge accepted event:", err)
	}

	//go func(gameID, userID primitive.ObjectID) {
	//	ctx := utils.DeriveContextWithoutCancel(ctx)
	//	timer := time.NewTimer(20 * time.Second)
	//	defer timer.Stop()
	//
	//	select {
	//	case <-ctx.Done():
	//		return
	//	case <-timer.C:
	//		s.cancelGameAndUpdateHumanBot(ctx, gameID, userID)
	//	}
	//}(gameID, userID)

	// challenger, err := s.userRepo.GetByID(ctx, game.Players[0].UserID)
	// if err != nil {
	// 	zlog.Error(ctx, "Error retrieving challenger:", err)
	// 	return nil, err
	// }

	// if (challenger.IsBot != nil && *challenger.IsBot) || (challenger.IsHumanBot != nil && *challenger.IsHumanBot) {
	// 	game, err = s.StartGame(ctx, &models.StartGameInput{
	// 		GameID: game.ID,
	// 	})
	// 	if err != nil {
	// 		zlog.Error(ctx, "Error in start game if challenger is bot", err)
	// 	}
	//
	// 	go func() {
	// 		err := botutils.RunBot(utils.DeriveContextWithoutCancel(ctx), game, challenger, s, s.cache, zlog)
	// 		if err != nil {
	// 			zlog.Error(ctx, "Error running bot after Accept Challenge", err)
	// 		}
	// 	}()
	// }
	//
	// if (user.IsBot != nil && *user.IsBot) || (user.IsHumanBot != nil && *user.IsHumanBot) {
	// 	game, err = s.StartGame(ctx, &models.StartGameInput{
	// 		GameID: game.ID,
	// 	})
	// 	if err != nil {
	// 		zlog.Error(ctx, "Error in start game if user is bot", err)
	// 	}
	//
	// 	go func() {
	// 		err := botutils.RunBot(utils.DeriveContextWithoutCancel(ctx), game, user, s, s.cache, zlog)
	// 		if err != nil {
	// 			zlog.Error(ctx, "Error running bot after Accept Challenge", err)
	// 		}
	// 	}()
	// }

	return game, nil
}

func (s *service) cancelGameAndUpdateHumanBot(ctx context.Context, gameId, userID primitive.ObjectID) {
	game, err := s.GetGameByID(ctx, &gameId)
	if err != nil {
		zlog.Error(ctx, "Error retrieving game:", err)
		return
	}

	if game.GameStatus != models.GameStatusReady {
		return
	}

	game.GameStatus = models.GameStatusCancelled

	err = s.gameRepo.UpdateGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Error updating game status:", err)
		return
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Error updating game in Cache:", err)
		return
	}

	err = s.PublishChallengeEvent(ctx, game, string(models.ChallengeStatusChallengeExpired))
	if err != nil {
		zlog.Error(ctx, "Error publishing challenge expiration event:", err)
	}

	user, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Error retrieving user:", err)
		return
	}

	if user.IsHumanBot == nil || !*user.IsHumanBot {
		return
	}
	if err = botutils.UpdateHumanBotCache(user, s.cache, false); err != nil {
		zlog.Error(ctx, "Error updating human bot cache:", err)
	}
}
