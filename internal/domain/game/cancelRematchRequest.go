package game

import (
	"context"
	"errors"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) CancelRematchRequest(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "failed to get user ID from context", err)
		return false, errors.New("unauthorized: user not found in context")
	}

	game, err := s.GetGameByID(ctx, &gameID)
	if err != nil {
		zlog.Error(ctx, "failed to get game by ID", err, zap.String("gameID", gameID.Hex()))
		return false, fmt.Errorf("failed to retrieve game: %w", err)
	}
	if game == nil {
		zlog.Warn(ctx, "game not found for cancellation", zap.String("gameID", gameID.Hex()))
		return false, errors.New("game not found")
	}

	if game.GameStatus != models.GameStatusEnded {
		zlog.Warn(ctx, "cannot cancel rematch request for a game not in ENDED status", zap.String("gameID", gameID.Hex()), zap.String("status", string(game.GameStatus)))
		return false, errors.New("rematch can only be cancelled for ended games")
	}

	if game.RematchRequestedBy == nil {
		zlog.Warn(ctx, "no pending rematch request to cancel", zap.String("gameID", gameID.Hex()))
		return false, errors.New("no rematch request found to cancel")
	}

	if *game.RematchRequestedBy != userID {
		zlog.Warn(ctx, "user attempted to cancel rematch request they did not initiate", zap.String("gameID", gameID.Hex()), zap.String("requesterID", game.RematchRequestedBy.Hex()), zap.String("userID", userID.Hex()))
		return false, errors.New("you can only cancel your own rematch request")
	}

	filter := bson.M{"_id": gameID}
	update := bson.M{"$unset": bson.M{"rematchRequestedBy": 1}}
	err = s.gameRepo.UpdateOne(ctx, filter, update)
	if err != nil {
		zlog.Error(ctx, "failed to update game to cancel rematch request", err, zap.String("gameID", gameID.Hex()))
		return false, fmt.Errorf("failed to cancel rematch request: %w", err)
	}

	game.RematchRequestedBy = nil

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "failed to update game cache after cancelling rematch", err, zap.String("gameID", gameID.Hex()))
	}

	err = s.publishRematchEventToBothUsers(ctx, game, userID, constants.RematchGameEnum.REMATCH_CANCELLED.String(), nil)
	if err != nil {
		zlog.Error(ctx, "failed to publish rematch cancellation event", err, zap.String("gameID", gameID.Hex()))
	}

	zlog.Info(ctx, "rematch request cancelled successfully", zap.String("gameID", gameID.Hex()), zap.String("userID", userID.Hex()))
	return true, nil
}
