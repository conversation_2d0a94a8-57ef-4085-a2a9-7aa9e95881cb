package game

import (
	"context"
	"fmt"

	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func (s *service) CreateGame(ctx context.Context, gameConfigInput *models.GameConfigInput) (*models.Game, error) {
	zlog.Info(ctx, "Creating Game", zap.Any("gameConfigInput", gameConfigInput))

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Error retrieving active games:", err)
		return nil, err
	}

	if err := s.validateGameConfig(gameConfigInput); err != nil {
		return nil, fmt.Errorf("invalid game configuration: %w", err)
	}

	zlog.Debug(ctx, "CreateGame", zap.String("userID", userID.Hex()))

	if gameConfigInput.GameType == models.GameTypePractice {
		return s.GetPracticeGame(ctx, gameConfigInput)
	} else if gameConfigInput.GameType == models.GameTypePlayOnline {
		zlog.Debug(ctx, "Game type play online, handled, should not reach here")
	}

	activeGames, err := s.gameRepo.Find(ctx, bson.M{
		"gameStatus": bson.M{"$nin": []string{string(constants.GameStatusEnum.ENDED), string(constants.GameStatusEnum.CANCELLED)}},
		"players": bson.M{
			"$elemMatch": bson.M{
				"userId": userID,
				"status": constants.PlayerStatusEnum.ACCEPTED,
			},
		},
	})
	if err != nil {
		zlog.Error(ctx, "Error finding games:", err)
		return nil, err
	}

	if len(activeGames) > 1 {
		for _, game := range activeGames {
			if game.GameType != models.GameTypeSumdayShowdown {
				game.GameStatus = models.GameStatusCancelled
				game.Questions = nil

				err := s.gameRepo.UpdateGame(ctx, game)
				if err != nil {
					zlog.Error(ctx, "Error cancelling game:", err)
				}

				err = s.gameCache.SetGame(ctx, game)
				if err != nil {
					zlog.Error(ctx, "Error caching game:", err)
				}
			}
		}
	}

	currentUser, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	gameConfig := models.GameConfig{
		TimeLimit:          &gameConfigInput.TimeLimit,
		NumPlayers:         &gameConfigInput.NumPlayers,
		GameType:           gameConfigInput.GameType,
		QuestionTags:       gameConfigInput.QuestionTags,
		DifficultyLevel:    gameConfigInput.DifficultyLevel,
		MaxTimePerQuestion: gameConfigInput.MaxTimePerQuestion,
	}

	currentUserRating := gameutils.GetPlayerRatingByGameType(currentUser, gameConfigInput.GameType)

	players := []*models.Player{
		{
			UserID:      userID,
			Status:      models.PlayerStatusAccepted,
			Rating:      &currentUserRating,
			StatikCoins: currentUser.StatikCoins,
			TimeLeft:    utils.AllocPtr(gameConfigInput.TimeLimit),
		},
	}

	newGame := models.Game{
		CreatedBy:  userID,
		Config:     &gameConfig,
		GameType:   gameConfig.GameType,
		GameStatus: models.GameStatusCreated,
		Players:    players,
	}

	createdGame, gameCreationError := s.gameRepo.CreateGame(ctx, &newGame)
	if gameCreationError != nil {
		zlog.Error(ctx, "Error creating game:", gameCreationError)
		return nil, gameCreationError
	}

	err = s.gameCache.SetGame(ctx, createdGame)
	if err != nil {
		zlog.Error(ctx, "Failed to save game in cache :", err)
		return nil, err
	}

	return &newGame, nil
}
