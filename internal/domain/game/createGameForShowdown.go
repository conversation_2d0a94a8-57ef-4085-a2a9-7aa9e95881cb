package game

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) CreateGameForShowdown(ctx context.Context, showdownConfig *models.ShowdownConfig) ([]*models.Game, error) {
	zlog.Info(ctx, "Creating Game", zap.Any("showdownConfig", showdownConfig))
	var games []*models.Game
	newGame, err := CreateShowdownGamePayload(ctx, s.userRepo, map[primitive.ObjectID]*models.User{}, showdownConfig)
	if err != nil {
		zlog.Error(ctx, "Error creating game for showdown: ", err)
		return nil, err
	}
	if showdownConfig.IsPlayerAlreeadyJoined {
		createdGame, gameCreationError := s.gameRepo.CreateGame(ctx, &newGame)
		if gameCreationError != nil {
			zlog.Error(ctx, "Error creating game:", gameCreationError)
			return nil, gameCreationError
		}
		newGame = *createdGame
	}
	games = append(games, &newGame)

	return games, nil
}

func CreateShowdownGamePayload(ctx context.Context, userRepo repository.UserRepository, users map[primitive.ObjectID]*models.User, showdownConfig *models.ShowdownConfig) (newGame models.Game, err error) {
	if showdownConfig == nil {
		return newGame, fmt.Errorf("showdownConfig is nil")
	}
	if len(showdownConfig.Players) < 1 || len(showdownConfig.Players) > 2 {
		return newGame, fmt.Errorf("invaild users")
	}

	playersData := make([]*models.Player, 2)
	playersStatus := models.PlayerStatusInvited
	if showdownConfig.IsPlayerAlreeadyJoined {
		playersStatus = models.PlayerStatusAccepted
	}
	for i, id := range showdownConfig.Players {
		if id == nil {
			return newGame, fmt.Errorf("user id of one of users is nil")
		}
		userData, ok := users[*id]
		if !ok || userData == nil {
			userData, err = userRepo.GetByID(ctx, *id)
			if err != nil {
				return newGame, fmt.Errorf("failed to fetch users: %w", err)
			}
		}
		playersData[i] = &models.Player{
			UserID:      *id,
			Rating:      userData.Rating,
			Status:      playersStatus,
			StatikCoins: userData.StatikCoins,
		}
	}

	gameConfig := models.GameConfig{
		TimeLimit:  showdownConfig.GameConfig.TimeLimit,
		NumPlayers: showdownConfig.GameConfig.NumPlayers,
		GameType:   showdownConfig.GameConfig.GameType,
	}

	newGame = models.Game{
		Config:     &gameConfig,
		GameType:   showdownConfig.GameConfig.GameType,
		GameStatus: models.GameStatusCreated,
		Players:    playersData,
		ShowdownId: &showdownConfig.ShowdownId,
		ShowdownGameConfig: &models.ShowdownGameConfig{
			Round:              showdownConfig.Round,
			NumOfGames:         showdownConfig.TotalGames,
			ShowdownGamePlayer: showdownConfig.ShowdownGamePlayer,
			TotalGamesPlayed:   showdownConfig.TotalGamesPlayed,
		},
	}
	return newGame, nil
}
