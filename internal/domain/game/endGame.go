package game

import (
	"context"
	"fmt"
	"math"

	"matiksOfficial/matiks-server-go/internal/constants"
	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/domain/game/utils/botutils"
	"matiksOfficial/matiks-server-go/internal/domain/userStreak"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/domain/utils/rankingutils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) EndGame(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	currentSessionUser, err := s.userService.GetUserByID(ctx, userId)
	if err != nil || currentSessionUser == nil {
		return nil, fmt.Errorf("failed to get current session user: %w", err)
	}

	game, err := s.GetGameByID(ctx, gameID)
	if err != nil {
		return nil, fmt.Errorf("failed to get game: %w", err)
	}

	zlog.Debug(ctx, "Ending game", zap.String("gameID", gameID.Hex()))

	if game == nil {
		return nil, fmt.Errorf("game not found")
	}

	zlog.Debug(ctx, "Game end event", zap.String("gameID", gameID.Hex()))

	err = s.handleUsersGameLockedStatus(ctx, currentSessionUser, game)
	if err != nil {
		return nil, err
	}

	if game.GameType == models.GameTypeFlashAnzan {
		return s.EndFlashAnzanGame(ctx, gameID, game)
	}

	if game.GameType == models.GameTypeAbilityDuels {
		return s.EndAbilityDuelsGame(ctx, gameID)
	}

	if game.Config != nil && game.Config.NumPlayers != nil && *game.Config.NumPlayers > 2 {
		return s.EndGroupPlayGame(ctx, gameID, game)
	}

	minValueForLooserRatingChange, maxValueForWinnerRatingChange := gameutils.GetMinMaxValueForRatingChange(game.GameType)

	gameStatus := game.GameStatus
	questions := game.Questions
	startTime := game.StartTime

	if gameStatus != models.GameStatusStarted {
		if gameStatus == models.GameStatusCancelled {
			game.GameStatus = models.GameStatusEnded
			return game, nil
		}

		zlog.Info(ctx, "Game is not in a started state, skipping end game")
		return nil, nil
	}

	game.GameStatus = models.GameStatusEnded
	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		return nil, err
	}
	sortedLeaderboard := gameutils.UpdateLeaderboardRanks(game)

	if len(sortedLeaderboard) < 2 {
		return nil, fmt.Errorf("not enough players")
	}

	hasSomeoneNotSubmittedAnyQuestion := *sortedLeaderboard[0].TotalPoints == 0 || *sortedLeaderboard[1].TotalPoints == 0
	isTie := *sortedLeaderboard[0].TotalPoints == *sortedLeaderboard[1].TotalPoints
	bothNotSubmittedAnyQuestion := hasSomeoneNotSubmittedAnyQuestion && isTie

	winnerID := sortedLeaderboard[0].UserID
	loserID := sortedLeaderboard[1].UserID

	if winnerID == nil || loserID == nil {
		return nil, fmt.Errorf("invalid player IDs")
	}

	winner, err := s.userService.GetUserByID(ctx, *winnerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get winner: %w", err)
	}

	loser, err := s.userService.GetUserByID(ctx, *loserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get loser: %w", err)
	}

	if winner == nil || loser == nil {
		return nil, fmt.Errorf("players not found")
	}

	if winner.Rating == nil || loser.Rating == nil {
		return nil, fmt.Errorf("invalid player ratings")
	}

	// glicko
	// oldWinnerRating := *winner.Rating
	// oldLoserRating := *loser.Rating

	var oldWinnerRatingAttributes, oldLoserRatingAttributes *models.RatingAttributes

	if winner.RatingV2 == nil {
		winner.RatingV2 = &models.UserRating{
			GlobalRating: winner.Rating,
		}
	}
	if loser.RatingV2 == nil {
		loser.RatingV2 = &models.UserRating{
			GlobalRating: loser.Rating,
		}
	}

	if winner.RatingV2.GlobalRating == nil {
		winner.RatingV2.GlobalRating = winner.Rating
	}
	if loser.RatingV2.GlobalRating == nil {
		loser.RatingV2.GlobalRating = loser.Rating
	}

	if winner.RatingV2.GlobalRatingAttributes != nil {
		oldWinnerRatingAttributes = winner.RatingV2.GlobalRatingAttributes
	}
	if loser.RatingV2.GlobalRatingAttributes != nil {
		oldLoserRatingAttributes = loser.RatingV2.GlobalRatingAttributes
	}

	// rem
	expectedWinnerScore := 1 / (1 + math.Pow(10, (float64(*loser.Rating)-float64(*winner.Rating))/400))
	expectedLoserScore := 1 / (1 + math.Pow(10, (float64(*winner.Rating)-float64(*loser.Rating))/400))

	maxScoreDifference := 0
	for _, questionObj := range questions {
		if questionObj.Question.Rating != nil && len(questionObj.Submissions) > 0 {
			maxScoreDifference += 1
		}
	}

	scoreDifference := math.Floor(math.Abs(*sortedLeaderboard[0].TotalPoints-*sortedLeaderboard[1].TotalPoints) / 4)
	// rem
	normalizedScoreDifference := math.Min(math.Max(scoreDifference/math.Max(float64(maxScoreDifference), 1), 0), 1)
	adjustmentFactor := 0.2 + 1.6*normalizedScoreDifference
	KFactor := gameutils.GetKFactor(game.Config)

	// glicko
	// winnerNGP := 0
	// loserNGP := 0
	// if winner.Stats != nil && winner.Stats.Ngp != nil {
	//	winnerNGP = *winner.Stats.Ngp
	// }
	// if loser.Stats != nil && loser.Stats.Ngp != nil {
	//	loserNGP = *loser.Stats.Ngp
	// }

	// glickoWinner := glicko2.NewGlickoPlayer(winner, "")
	// glickoLoser := glicko2.NewGlickoPlayer(loser, "")

	// Perform fair use check
	fairUseCheck := gameutils.GetFairUseCheckResult(
		*winner.Rating,
		*loser.Rating,
		game.Questions,
		sortedLeaderboard,
		game.Config,
	)

	// Log fair use check results
	zlog.Info(ctx, "Fair Use Check Results",
		zap.String("gameID", game.ID.Hex()),
		zap.String("winnerID", winner.ID.Hex()),
		zap.String("loserID", loser.ID.Hex()),
		zap.Int("winnerRating", *winner.Rating),
		zap.Int("loserRating", *loser.Rating),
		zap.Bool("winnerPassedFairUse", fairUseCheck.WinnerPassedFairUse),
		zap.Bool("loserPassedFairUse", fairUseCheck.LoserPassedFairUse),
		zap.Int("winnerExpected", fairUseCheck.WinnerExpected),
		zap.Int("winnerActual", fairUseCheck.WinnerActual),
		zap.Int("loserExpected", fairUseCheck.LoserExpected),
		zap.Int("loserActual", fairUseCheck.LoserActual),
		zap.Bool("isTie", isTie),
	)

	var winnerRatingChange, loserRatingChange int
	if !hasSomeoneNotSubmittedAnyQuestion {
		if isTie {
			// For ties, apply fair use check to both players
			if fairUseCheck.WinnerPassedFairUse {
				winnerRatingChange = int(math.Max(1, KFactor*(0.5-expectedWinnerScore)*adjustmentFactor))
			} else {
				winnerRatingChange = 1 // No rating gain if fair use check failed
			}

			if fairUseCheck.LoserPassedFairUse {
				loserRatingChange = int(math.Min(-1, KFactor*(0.5-expectedLoserScore)*adjustmentFactor))
			} else {
				loserRatingChange = -1 // No rating loss if fair use check failed
			}
		} else {
			// For wins, only apply fair use check to the winner
			if fairUseCheck.WinnerPassedFairUse {
				winnerRatingChange = int(math.Max(1, KFactor*(1-expectedWinnerScore)*adjustmentFactor))
			} else {
				winnerRatingChange = 1 // No rating gain if winner didn't meet fair use criteria
			}

			// Loser always loses rating regardless of fair use (they lost the game)
			loserRatingChange = int(math.Min(-1, KFactor*(0-expectedLoserScore)*adjustmentFactor))
		}
	} else {
		winnerRatingChange = 0
		loserRatingChange = 0
		winner.RatingV2.GlobalRatingAttributes = oldWinnerRatingAttributes
		loser.RatingV2.GlobalRatingAttributes = oldLoserRatingAttributes
		if bothNotSubmittedAnyQuestion {
			loserRatingChange = 0
		}
	}

	// Log rating changes with fair use context
	zlog.Info(ctx, "Rating Changes Applied",
		zap.String("gameID", game.ID.Hex()),
		zap.Int("winnerRatingChange", winnerRatingChange),
		zap.Int("loserRatingChange", loserRatingChange),
		zap.Bool("winnerPassedFairUse", fairUseCheck.WinnerPassedFairUse),
		zap.Bool("loserPassedFairUse", fairUseCheck.LoserPassedFairUse),
	)

	winnerRatingChange = min(winnerRatingChange, maxValueForWinnerRatingChange)
	loserRatingChange = max(loserRatingChange, minValueForLooserRatingChange)

	if game.GameType == models.GameTypeFastestFinger {
		winnerRatingChange /= 2
		winnerRatingChange = max(winnerRatingChange, 1)
		loserRatingChange /= 2
		loserRatingChange = min(loserRatingChange, -1)
	}

	winnerHighestRating := int(math.Max(float64(*winner.Rating), float64(*winner.Rating+winnerRatingChange)))
	if winner.Stats != nil {
		winnerHighestRating = int(math.Max(float64(winnerHighestRating), float64(winner.Stats.Hr)))
	}

	loserHighestRating := int(math.Max(float64(*loser.Rating), float64(*loser.Rating+loserRatingChange)))
	if loser.Stats != nil {
		loserHighestRating = int(math.Max(float64(loserHighestRating), float64(loser.Stats.Hr)))
	}

	winnerOldRating, loserOldRating := *winner.Rating, *loser.Rating

	gameutils.GetUserWithNewRatingAfterApplyingRatingChange(loser, loserRatingChange, game.GameType)
	gameutils.GetUserWithNewRatingAfterApplyingRatingChange(winner, winnerRatingChange, game.GameType)

	winnerNewRating, loserNewRating := *winner.Rating, *loser.Rating

	winnerGame := &models.UserGame{
		ID:         utils.AllocPtr(gameID.Hex()),
		ST:         startTime,
		IsWinner:   utils.AllocPtr(true),
		OpponentID: loserID,
	}

	loserGame := &models.UserGame{
		ID:         utils.AllocPtr(gameID.Hex()),
		ST:         startTime,
		IsWinner:   utils.AllocPtr(false),
		OpponentID: winnerID,
	}

	winnerInitialBadge := winner.Badge
	loserInitialBadge := loser.Badge

	success, err := s.userGameBucketRepo.AddGame(ctx, winner.ID, winnerGame)
	if err != nil || !success {
		return nil, err
	}
	success, err = s.userGameBucketRepo.AddGame(ctx, loser.ID, loserGame)
	if err != nil || !success {
		return nil, err
	}

	gameutils.UpdateUserStats(winner, loser, winnerHighestRating, winnerGame)
	gameutils.UpdateUserStats(loser, winner, loserHighestRating, loserGame)

	gameutils.UpdateBadge(winner, winnerOldRating, winnerNewRating)
	gameutils.UpdateBadge(loser, loserOldRating, loserNewRating)

	err = botutils.UpdateHumanBotCache(winner, s.cache, false)
	if err != nil {
		return nil, err
	}
	err = botutils.UpdateHumanBotCache(loser, s.cache, false)
	if err != nil {
		return nil, err
	}

	winnerFinalBadge := winner.Badge
	loserFinalBadge := loser.Badge

	err = s.userService.UpdateUserFromObject(ctx, winner)
	if err != nil {
		return nil, fmt.Errorf("failed to update winner: %w", err)
	}

	err = s.userService.UpdateUserFromObject(ctx, loser)
	if err != nil {
		return nil, fmt.Errorf("failed to update loser: %w", err)
	}

	sortedLeaderboard[0].RatingChange = utils.AllocPtr(winnerRatingChange)
	sortedLeaderboard[1].RatingChange = utils.AllocPtr(loserRatingChange)

	sortedLeaderboard[0].StatikCoinsEarned = gameutils.GetStatikCoinsEarnedForWinner(sortedLeaderboard[0])
	sortedLeaderboard[1].StatikCoinsEarned = gameutils.GetStatikCoinsEarnedForLoser(sortedLeaderboard[1])

	botUsers := s.botDetectionService.HandleEndGameBotDetection(ctx, game, winner.ID, loser.ID)

	if len(botUsers) > 0 {
		if botUsers[winner.ID] {
			loserRatingChange = -1
			sortedLeaderboard[1].RatingChange = utils.AllocPtr(loserRatingChange)
			zlog.Info(ctx, "Limited loser rating change due to bot winner",
				zap.String("winnerID", winner.ID.Hex()),
				zap.String("loserID", loser.ID.Hex()),
				zap.Int("ratingChange", loserRatingChange))
		}

		if botUsers[loser.ID] {
			winnerRatingChange = 1
			sortedLeaderboard[0].RatingChange = utils.AllocPtr(winnerRatingChange)
			zlog.Info(ctx, "Limited winner rating change due to bot loser",
				zap.String("winnerID", winner.ID.Hex()),
				zap.String("loserID", loser.ID.Hex()),
				zap.Int("ratingChange", winnerRatingChange))
		}
	}

	game.LeaderBoard = sortedLeaderboard
	game.GameStatus = models.GameStatusEnded

	if game.GameType != models.GameTypeFastestFinger && game.GameType != models.GameTypeFlashAnzan {
		gameutils.SaveMinifiedQuestions(game)
	}

	err = s.publishGameEvent(ctx, game, constants.GameEventEnum.GAME_ENDED.String())
	if err != nil {
		zlog.Error(ctx, "Failed to publish game ended event", err)
	}

	if !gameutils.AreBadgesEqual(winnerFinalBadge, winnerInitialBadge) && !*winner.IsGuest {
		go s.SendDelayedBadgeEvent(utils.DeriveContextWithoutCancel(ctx), winner.ID, winnerInitialBadge, winnerFinalBadge)
	}

	if !gameutils.AreBadgesEqual(loserFinalBadge, loserInitialBadge) && !*loser.IsGuest {
		go s.SendDelayedBadgeEvent(utils.DeriveContextWithoutCancel(ctx), loser.ID, loserInitialBadge, loserFinalBadge)
	}

	if winner.IsBot == nil || (winner.IsBot != nil && !*winner.IsBot) {
		go func() {
			success, err := s.presetsService.SubmitUserQuestions(utils.DeriveContextWithoutCancel(ctx), game.Questions, winner.ID)
			if err != nil {
				zlog.Error(ctx, "Failed to submit questions: ", err)
			}
			zlog.Info(ctx, "Preset stats update status", zap.Bool("success", *success))
		}()
	}
	if loser.IsBot == nil || (loser.IsBot != nil && !*loser.IsBot) {
		go func() {
			success, err := s.presetsService.SubmitUserQuestions(utils.DeriveContextWithoutCancel(ctx), game.Questions, loser.ID)
			if err != nil {
				zlog.Error(ctx, "Failed to submit questions: ", err)
			}
			zlog.Info(ctx, "Preset stats update status", zap.Bool("success", *success))
		}()
	}

	game.RematchRequestedBy = nil

	err = s.gameRepo.UpdateGame(ctx, game)
	if err != nil {
		return nil, err
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		return nil, err
	}

	go func() {
		if err := s.gameCache.DeleteGame(utils.DeriveContextWithoutCancel(ctx), gameID.Hex()); err != nil {
			zlog.Error(ctx, "Failed to remove game from cache", err)
		}
	}()

	if winner.Rating != nil {
		err = s.globalLeaderboard.UpdateUserRating(ctx, &winnerOldRating, *winner.Rating)
		if err != nil {
			zlog.Error(ctx, "Failed to update leaderboard", err, zap.Any("userID", winner.ID.Hex()))
		}
	}

	if loser.Rating != nil {
		err = s.globalLeaderboard.UpdateUserRating(ctx, &loserOldRating, *loser.Rating)
		if err != nil {
			zlog.Error(ctx, "Failed to update leaderboard", err, zap.Any("userID", loser.ID.Hex()))
		}
	}

	go func() {
		if err := rankingutils.UpdateRank(false, s.userRepo); err != nil {
			zlog.Error(ctx, "Failed to update ranking", err)
		}
	}()

	go func() {
		if err := userStreak.UpdateUserStreak(utils.DeriveContextWithoutCancel(ctx), winner.ID, s.userRepo, true, s.notificationService, s.coreService, s.userStreakRepo); err != nil {
			zlog.Error(ctx, "Failed to update streak", err)
		}
	}()
	go func() {
		if err := userStreak.UpdateUserStreak(utils.DeriveContextWithoutCancel(ctx), loser.ID, s.userRepo, true, s.notificationService, s.coreService, s.userStreakRepo); err != nil {
			zlog.Error(ctx, "Failed to update streak", err)
		}
	}()

	go func() {
		activityType := gameutils.GetActivityTypeFromGameType(game.GameType)
		if err := s.userService.UpdateUserStatikCoinsAndTimeSpent(utils.DeriveContextWithoutCancel(ctx), loser.ID, activityType, *sortedLeaderboard[1].StatikCoinsEarned, int64(*game.Config.TimeLimit)*1000, game.ID); err != nil {
			zlog.Error(ctx, "Failed to update user activity of loser", err)
		}
	}()

	go func() {
		activityType := gameutils.GetActivityTypeFromGameType(game.GameType)
		if err := s.userService.UpdateUserStatikCoinsAndTimeSpent(utils.DeriveContextWithoutCancel(ctx), winner.ID, activityType, *sortedLeaderboard[0].StatikCoinsEarned, int64(*game.Config.TimeLimit)*1000, game.ID); err != nil {
			zlog.Error(ctx, "Failed to update user activity of winner", err)
		}
	}()

	return game, nil
}

// EndGame handles the game ending logic based on game type
func (s *service) EndGameV2(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error) {
	game, err := s.GetGameByID(ctx, gameID)
	if err != nil {
		return nil, fmt.Errorf("failed to get game: %w", err)
	}

	zlog.Debug(ctx, "Ending game", zap.String("gameID", gameID.Hex()))

	if game == nil {
		return nil, fmt.Errorf("game not found")
	}

	// Handle cancelled games
	if game.GameStatus == models.GameStatusCancelled {
		game.GameStatus = models.GameStatusEnded
		return game, nil
	}

	// Skip if game is not in started state
	if game.GameStatus != models.GameStatusStarted {
		zlog.Info(ctx, "Game is not in a started state, skipping end game")
		return nil, nil
	}

	// Route to appropriate handler based on game type
	switch {
	case game.GameType == models.GameTypeFlashAnzan:
		return s.EndFlashAnzanGame(ctx, gameID, game)
	case game.GameType == models.GameTypeAbilityDuels:
		return s.EndAbilityDuelsGame(ctx, gameID)
	case game.Config != nil && game.Config.NumPlayers != nil && *game.Config.NumPlayers > 2:
		return s.EndGroupPlayGame(ctx, gameID, game)
	default:
		return s.endStandardGame(ctx, gameID, game)
	}
}

// endStandardGame handles the ending of standard 1v1 games
func (s *service) endStandardGame(ctx context.Context, gameID *primitive.ObjectID, game *models.Game) (*models.Game, error) {
	// Mark game as ended in cache first
	game.GameStatus = models.GameStatusEnded
	if err := s.gameCache.SetGame(ctx, game); err != nil {
		return nil, err
	}

	// Update leaderboard ranks
	sortedLeaderboard := gameutils.UpdateLeaderboardRanks(game)
	if len(sortedLeaderboard) < 2 {
		return nil, fmt.Errorf("not enough players")
	}

	// Get players
	players, err := s.getAndValidatePlayers(ctx, sortedLeaderboard)
	if err != nil {
		return nil, err
	}
	winner, loser := players.winner, players.loser

	// Calculate rating changes
	ratingChanges, err := s.calculateRatingChanges(ctx, game, sortedLeaderboard, winner, loser)
	if err != nil {
		return nil, err
	}

	// Update player stats and ratings
	if err := s.updatePlayerStatsAndRatings(ctx, game, winner, loser, ratingChanges); err != nil {
		return nil, err
	}

	s.updateLeaderboardWithRewards(sortedLeaderboard, ratingChanges)

	s.botDetectionService.HandleEndGameBotDetection(ctx, game, winner.ID, loser.ID)

	// Finalize game
	game.LeaderBoard = sortedLeaderboard
	game.GameStatus = models.GameStatusEnded
	game.RematchRequestedBy = nil

	// Save minified questions for non-special game types
	if game.GameType != models.GameTypeFastestFinger && game.GameType != models.GameTypeFlashAnzan {
		gameutils.SaveMinifiedQuestions(game)
	}

	// Publish game ended event
	if err := s.publishGameEvent(ctx, game, constants.GameEventEnum.GAME_ENDED.String()); err != nil {
		zlog.Error(ctx, "Failed to publish game ended event", err)
	}

	// Handle badge changes
	s.handleBadgeChanges(ctx, winner, loser, players.winnerInitialBadge, players.loserInitialBadge)

	// Submit questions for presets
	s.submitQuestionsForPresets(ctx, game, winner, loser)

	// Persist game to database
	if err := s.gameRepo.UpdateGame(ctx, game); err != nil {
		return nil, err
	}

	// Update game in cache
	if err := s.gameCache.SetGame(ctx, game); err != nil {
		return nil, err
	}

	// Cleanup and post-game updates
	s.triggerPostGameUpdates(ctx, gameID, winner, loser, ratingChanges)

	return game, nil
}

type playerPair struct {
	winner             *models.User
	loser              *models.User
	winnerInitialBadge *models.BadgeType
	loserInitialBadge  *models.BadgeType
}

type ratingChangeInfo struct {
	WinnerRatingChange int
	LoserRatingChange  int
	WinnerOldRating    int
	LoserOldRating     int
}

// getAndValidatePlayers retrieves and validates the players in the game
func (s *service) getAndValidatePlayers(ctx context.Context, leaderboard []*models.LeaderBoardEntry) (*playerPair, error) {
	winnerID := leaderboard[0].UserID
	loserID := leaderboard[1].UserID

	if winnerID == nil || loserID == nil {
		return nil, fmt.Errorf("invalid player IDs")
	}

	winner, err := s.userService.GetUserByID(ctx, *winnerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get winner: %w", err)
	}

	loser, err := s.userService.GetUserByID(ctx, *loserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get loser: %w", err)
	}

	if winner == nil || loser == nil {
		return nil, fmt.Errorf("players not found")
	}

	if winner.Rating == nil || loser.Rating == nil {
		return nil, fmt.Errorf("invalid player ratings")
	}

	return &playerPair{
		winner:             winner,
		loser:              loser,
		winnerInitialBadge: winner.Badge,
		loserInitialBadge:  loser.Badge,
	}, nil
}

// calculateRatingChanges calculates the rating changes for both players
func (s *service) calculateRatingChanges(
	ctx context.Context,
	game *models.Game,
	leaderboard []*models.LeaderBoardEntry,
	winner *models.User,
	loser *models.User,
) (*ratingChangeInfo, error) {
	minValueForLooserRatingChange, maxValueForWinnerRatingChange := gameutils.GetMinMaxValueForRatingChange(game.GameType)

	// Check for special cases
	hasSomeoneNotSubmittedAnyQuestion := *leaderboard[0].TotalPoints == 0 || *leaderboard[1].TotalPoints == 0
	isTie := *leaderboard[0].TotalPoints == *leaderboard[1].TotalPoints
	bothNotSubmittedAnyQuestion := hasSomeoneNotSubmittedAnyQuestion && isTie

	// Calculate expected scores and rating changes
	ratingChanges := gameutils.CalculateRatingChanges(
		*winner.Rating,
		*loser.Rating,
		game.Questions,
		leaderboard,
		isTie,
		hasSomeoneNotSubmittedAnyQuestion,
		bothNotSubmittedAnyQuestion,
		game.Config,
		game.GameType,
		minValueForLooserRatingChange,
		maxValueForWinnerRatingChange,
	)

	return &ratingChangeInfo{
		WinnerRatingChange: ratingChanges.WinnerChange,
		LoserRatingChange:  ratingChanges.LoserChange,
		WinnerOldRating:    *winner.Rating,
		LoserOldRating:     *loser.Rating,
	}, nil
}

// updatePlayerStatsAndRatings updates the stats and ratings for both players
func (s *service) updatePlayerStatsAndRatings(
	ctx context.Context,
	game *models.Game,
	winner *models.User,
	loser *models.User,
	ratingChanges *ratingChangeInfo,
) error {
	// Create game records for player history
	winnerGame := &models.UserGame{
		ID:         utils.AllocPtr(game.ID.Hex()),
		ST:         game.StartTime,
		IsWinner:   utils.AllocPtr(true),
		OpponentID: utils.AllocPtr(loser.ID),
	}

	loserGame := &models.UserGame{
		ID:         utils.AllocPtr(game.ID.Hex()),
		ST:         game.StartTime,
		IsWinner:   utils.AllocPtr(false),
		OpponentID: utils.AllocPtr(winner.ID),
	}

	// Calculate the highest ratings
	winnerHighestRating := gameutils.CalculateHighestRating(winner, ratingChanges.WinnerRatingChange)
	loserHighestRating := gameutils.CalculateHighestRating(loser, ratingChanges.LoserRatingChange)

	success, err := s.userGameBucketRepo.AddGame(ctx, winner.ID, winnerGame)
	if err != nil || !success {
		return err
	}
	success, err = s.userGameBucketRepo.AddGame(ctx, loser.ID, loserGame)
	if err != nil || !success {
		return err
	}

	// Update user stats
	gameutils.UpdateUserStats(winner, loser, winnerHighestRating, winnerGame)
	gameutils.UpdateUserStats(loser, winner, loserHighestRating, loserGame)

	// Update ratings
	// gameutils.UpdateBadge(winner, ratingChanges.WinnerRatingChange)
	// gameutils.UpdateBadge(loser, ratingChanges.LoserRatingChange)

	// Update bot cache if needed
	if err := botutils.UpdateHumanBotCache(winner, s.cache, false); err != nil {
		return err
	}

	if err := botutils.UpdateHumanBotCache(loser, s.cache, false); err != nil {
		return err
	}

	// Persist user updates
	if err := s.userService.UpdateUserFromObject(ctx, winner); err != nil {
		return fmt.Errorf("failed to update winner: %w", err)
	}

	if err := s.userService.UpdateUserFromObject(ctx, loser); err != nil {
		return fmt.Errorf("failed to update loser: %w", err)
	}

	return nil
}

// updateLeaderboardWithRewards updates the leaderboard with rating changes and coins
func (s *service) updateLeaderboardWithRewards(leaderboard []*models.LeaderBoardEntry, ratingChanges *ratingChangeInfo) {
	leaderboard[0].RatingChange = utils.AllocPtr(ratingChanges.WinnerRatingChange)
	leaderboard[1].RatingChange = utils.AllocPtr(ratingChanges.LoserRatingChange)

	leaderboard[0].StatikCoinsEarned = gameutils.GetStatikCoinsEarnedForWinner(leaderboard[0])
	leaderboard[1].StatikCoinsEarned = gameutils.GetStatikCoinsEarnedForLoser(leaderboard[1])
}

// handleBadgeChanges handles badge changes for both players
func (s *service) handleBadgeChanges(
	ctx context.Context,
	winner *models.User,
	loser *models.User,
	winnerInitialBadge *models.BadgeType,
	loserInitialBadge *models.BadgeType,
) {
	if !gameutils.AreBadgesEqual(winner.Badge, winnerInitialBadge) && !*winner.IsGuest {
		go s.SendDelayedBadgeEvent(utils.DeriveContextWithoutCancel(ctx), winner.ID, winnerInitialBadge, winner.Badge)
	}

	if !gameutils.AreBadgesEqual(loser.Badge, loserInitialBadge) && !*loser.IsGuest {
		go s.SendDelayedBadgeEvent(utils.DeriveContextWithoutCancel(ctx), loser.ID, loserInitialBadge, loser.Badge)
	}
}

// submitQuestionsForPresets submits questions for presets for both players
func (s *service) submitQuestionsForPresets(ctx context.Context, game *models.Game, winner, loser *models.User) {
	if winner.IsBot == nil || (winner.IsBot != nil && !*winner.IsBot) {
		go func() {
			success, err := s.presetsService.SubmitUserQuestions(utils.DeriveContextWithoutCancel(ctx), game.Questions, winner.ID)
			if err != nil {
				zlog.Error(ctx, "Failed to submit questions: ", err)
			}
			zlog.Info(ctx, "Preset stats update status", zap.Bool("success", *success))
		}()
	}

	if loser.IsBot == nil || (loser.IsBot != nil && !*loser.IsBot) {
		go func() {
			success, err := s.presetsService.SubmitUserQuestions(utils.DeriveContextWithoutCancel(ctx), game.Questions, loser.ID)
			if err != nil {
				zlog.Error(ctx, "Failed to submit questions: ", err)
			}
			zlog.Info(ctx, "Preset stats update status", zap.Bool("success", *success))
		}()
	}
}

// triggerPostGameUpdates handles post-game updates like cache cleanup and leaderboard updates
func (s *service) triggerPostGameUpdates(
	ctx context.Context,
	gameID *primitive.ObjectID,
	winner *models.User,
	loser *models.User,
	ratingChanges *ratingChangeInfo,
) {
	// Clean up game cache
	go func() {
		if err := s.gameCache.DeleteGame(utils.DeriveContextWithoutCancel(ctx), gameID.Hex()); err != nil {
			zlog.Error(ctx, "Failed to remove game from cache", err)
		}
	}()

	// Update global leaderboard
	if winner.Rating != nil {
		if err := s.globalLeaderboard.UpdateUserRating(ctx, &ratingChanges.WinnerOldRating, *winner.Rating); err != nil {
			zlog.Error(ctx, "Failed to update leaderboard", err, zap.Any("userID", winner.ID.Hex()))
		}
	}

	if loser.Rating != nil {
		if err := s.globalLeaderboard.UpdateUserRating(ctx, &ratingChanges.LoserOldRating, *loser.Rating); err != nil {
			zlog.Error(ctx, "Failed to update leaderboard", err, zap.Any("userID", loser.ID.Hex()))
		}
	}

	go func() {
		if err := rankingutils.UpdateRank(false, s.userRepo); err != nil {
			zlog.Error(ctx, "Failed to update ranking", err)
		}
	}()

	go func() {
		if err := rankingutils.UpdateRankingOnGamePlayed(utils.DeriveContextWithoutCancel(ctx), s.userRepo, []*models.User{winner, loser}); err != nil {
			zlog.Error(ctx, "Failed to update ranking", err)
		}
	}()

	// Update user streaks
	go func() {
		if err := userStreak.UpdateUserStreak(utils.DeriveContextWithoutCancel(ctx), winner.ID, s.userRepo, true, s.notificationService, s.coreService, s.userStreakRepo); err != nil {
			zlog.Error(ctx, "Failed to update streak", err)
		}
	}()

	go func() {
		if err := userStreak.UpdateUserStreak(utils.DeriveContextWithoutCancel(ctx), loser.ID, s.userRepo, true, s.notificationService, s.coreService, s.userStreakRepo); err != nil {
			zlog.Error(ctx, "Failed to update streak", err)
		}
	}()
}
