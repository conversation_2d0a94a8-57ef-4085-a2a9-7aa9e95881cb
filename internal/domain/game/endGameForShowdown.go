package game

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/domain/showdown"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) EndGameForShowdown(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error) {
	game, err := s.GetGameByID(ctx, gameID)
	if err != nil {
		return nil, fmt.Errorf("failed to get game: %w", err)
	}

	zlog.Debug(ctx, "Ending game for showdown", zap.String("gameID", gameID.Hex()))

	if game == nil {
		zlog.Error(ctx, "Game not found", nil, zap.String("gameID", gameID.Hex()))
		return nil, fmt.Errorf("game not found")
	}

	if game.GameStatus == models.GameStatusEnded {
		zlog.Info(ctx, "Game is already ended", zap.String("gameID", gameID.Hex()))
		return nil, nil
	}

	if game.GameType != models.GameTypeSumdayShowdown || game.ShowdownId == nil {
		zlog.Error(ctx, "Game is invalid", nil, zap.String("gameID", gameID.Hex()))
		return nil, fmt.Errorf("game is invalid")
	}
	showdownId := *game.ShowdownId
	zlog.Debug(ctx, "Game end event", zap.String("gameID", gameID.Hex()))

	gameStatus := game.GameStatus

	if gameStatus != models.GameStatusStarted && gameStatus != models.GameStatusCreated {
		if gameStatus == models.GameStatusCancelled {
			game.GameStatus = models.GameStatusEnded
			zlog.Info(ctx, "Game is cancelled, ending game", zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
			return game, nil
		}

		zlog.Info(ctx, "Game is not in a started state, skipping end game", zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, nil
	}

	game.GameStatus = models.GameStatusEnded
	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Failed to set game in cache", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}
	sortedLeaderboard := gameutils.UpdateLeaderboardRanks(game)

	if len(sortedLeaderboard) < 2 {
		zlog.Error(ctx, "Not enough players to end game", nil, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, fmt.Errorf("not enough players")
	}

	isTie := *sortedLeaderboard[0].TotalPoints == *sortedLeaderboard[1].TotalPoints

	winnerID := sortedLeaderboard[0].UserID
	loserID := sortedLeaderboard[1].UserID

	if winnerID == nil || loserID == nil {
		zlog.Error(ctx, "Invalid player IDs", nil, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, fmt.Errorf("invalid player IDs")
	}
	players := models.Players{}
	players = append(players, winnerID, loserID)

	game.LeaderBoard = sortedLeaderboard
	game.GameStatus = models.GameStatusEnded

	showdown, err := s.showdownCache.GetShowdown(ctx, *game.ShowdownId)
	if err != nil {
		zlog.Error(ctx, "Failed to get showdown", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}
	winnerUser, err := s.userRepo.GetByID(ctx, *winnerID)
	if err != nil {
		zlog.Error(ctx, "Failed to get winner user", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}

	winner, err := s.showdownParticipantRepo.GetByShowdownIDAndUserID(ctx, *game.ShowdownId, *winnerID)
	if err != nil {
		zlog.Error(ctx, "Failed to get winner", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}

	winnerRoundData, winnerRoundIndex, err := getCurrentRoundDataAndIndex(winner, game.ShowdownGameConfig.Round)
	if err != nil {
		zlog.Error(ctx, "Failed to get current round data and index", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}

	loserUser, err := s.userRepo.GetByID(ctx, *loserID)
	if err != nil {
		zlog.Error(ctx, "Failed to get loser user", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}

	loser, err := s.showdownParticipantRepo.GetByShowdownIDAndUserID(ctx, *game.ShowdownId, *loserID)
	if err != nil {
		zlog.Error(ctx, "Failed to get loser", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}
	looserRoundData, looserRoundIndex, err := getCurrentRoundDataAndIndex(loser, game.ShowdownGameConfig.Round)
	if err != nil {
		zlog.Error(ctx, "Failed to get current round data and index", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}

	showdownGamePlayer1 := models.ShowdownGamePlayer{}
	showdownGamePlayer2 := models.ShowdownGamePlayer{}

	showdownGamePlayer1.UserID = winner.UserID
	showdownGamePlayer2.UserID = loser.UserID

	winnerRoundData.TotalGamesPlayed++
	looserRoundData.TotalGamesPlayed++
	if isTie {
		winnerRoundData.Wins += 0.5
		looserRoundData.Wins += 0.5

		showdownGamePlayer1.IsTie, showdownGamePlayer2.IsTie = true, true
	} else {
		winnerRoundData.Wins += 1
		showdownGamePlayer1.IsWinner = true
	}
	showdownGamePlayer1.Wins, showdownGamePlayer2.Wins = winnerRoundData.Wins, looserRoundData.Wins
	showdownWinner := showdownGamePlayer1
	if showdownGamePlayer2.Wins > showdownGamePlayer1.Wins {
		showdownWinner = showdownGamePlayer2
	}

	isRoundEnded := false
	if (winnerRoundData.TotalGamesPlayed == showdown.RoundConfig.NumOfGames) || (showdownWinner.Wins > (float64(showdown.RoundConfig.NumOfGames) / 2.0)) {
		// winnerRoundData.IsRoundEnded, looserRoundData.IsRoundEnded = true, true
		isRoundEnded = true
		winnerRoundData.PlayerStatus, looserRoundData.PlayerStatus = models.RoundPlayerStatusRoundCompleted, models.RoundPlayerStatusRoundCompleted
		// winnerRoundData
		if showdownGamePlayer1.Wins > showdownGamePlayer2.Wins {
			winnerRoundData.Score = 1
			winner.TotalScore += 1
		} else if showdownGamePlayer1.Wins == showdownGamePlayer2.Wins {
			winnerRoundData.Score, looserRoundData.Score = 0.5, 0.5
			winner.TotalScore += 0.5
			loser.TotalScore += 0.5
		} else {
			looserRoundData.Score = 1
			loser.TotalScore += 1
		}
	}

	showdownGameConfig := models.ShowdownGameConfig{}
	ratingChangeInfo, err := s.calculateRatingChanges(ctx, game, sortedLeaderboard, winnerUser, loserUser)
	if err != nil {
		zlog.Error(ctx, "Failed to get rating change", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}

	if showdown.IsRatedEvent == nil {
		showdown.IsRatedEvent = utils.AllocPtr(true)
	}

	if *showdown.IsRatedEvent {
		winner.RatingChange += ratingChangeInfo.WinnerRatingChange
		loser.RatingChange += ratingChangeInfo.LoserOldRating

		winnerOldRating, loserOldRating := *winnerUser.Rating, *loserUser.Rating
		gameutils.GetUserWithNewRatingAfterApplyingRatingChange(winnerUser, ratingChangeInfo.WinnerRatingChange, game.GameType)
		gameutils.GetUserWithNewRatingAfterApplyingRatingChange(loserUser, ratingChangeInfo.LoserRatingChange, game.GameType)
		winnerNewRating, loserNewRating := *winnerUser.Rating, *loserUser.Rating

		gameutils.UpdateBadge(winnerUser, winnerOldRating, winnerNewRating)
		gameutils.UpdateBadge(loserUser, loserOldRating, loserNewRating)

	}

	if err := s.userService.UpdateUserFromObject(ctx, winnerUser); err != nil {
		return nil, fmt.Errorf("failed to update winner: %w", err)
	}

	if err := s.userService.UpdateUserFromObject(ctx, loserUser); err != nil {
		return nil, fmt.Errorf("failed to update loser: %w", err)
	}

	showdownGameConfig.TotalGamesPlayed = winnerRoundData.TotalGamesPlayed
	showdownGameConfig.NumOfGames = showdown.RoundConfig.NumOfGames

	if winnerRoundData.TotalGamesPlayed < showdown.RoundConfig.NumOfGames && !isRoundEnded {
		round := showdown.CurrentRound
		if game.ShowdownGameConfig != nil && game.ShowdownGameConfig.Round > 0 {
			round = game.ShowdownGameConfig.Round
		}
		newGame, err := s.CreateGameForShowdown(ctx, &models.ShowdownConfig{
			ShowdownId: *game.ShowdownId,
			GameConfig: models.GameConfig{
				NumPlayers: utils.AllocPtr(constants.SHOWDOWN_PLAYERS),
				TimeLimit:  utils.AllocPtr(showdown.RoundConfig.GameDuration),
				GameType:   models.GameTypeSumdayShowdown,
			},
			IsPlayerAlreeadyJoined: true,
			TotalGamesPlayed:       winnerRoundData.TotalGamesPlayed,
			TotalGames:             showdown.RoundConfig.NumOfGames,
			Players:                players,
			Round:                  round,
			ShowdownGamePlayer:     []*models.ShowdownGamePlayer{&showdownGamePlayer1, &showdownGamePlayer2},
		})
		if err != nil || len(newGame) == 0 || newGame[0] == nil && newGame[0].ID == nil {
			zlog.Error(ctx, "Failed to create new game", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
			return nil, fmt.Errorf("unable to create newgame %w", err)
		}
		newGameStartTime := time.Now().Add(30 * time.Second)
		_, err = s.StartGameForShowdownInternal(ctx, &models.StartGameInput{GameID: newGame[0].ID}, newGameStartTime)
		if err != nil || len(newGame) == 0 || newGame[0] == nil && newGame[0].ID == nil {
			zlog.Error(ctx, "Failed to start game", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
			return nil, fmt.Errorf("unable to start game %w", err)
		}
		winnerRoundData.Games = append(winnerRoundData.Games, *newGame[0].ID)
		looserRoundData.Games = append(looserRoundData.Games, *newGame[0].ID)

		showdownGameConfig.NextGameID = newGame[0].ID
		showdownGameConfig.NextGameStartsAt = &newGameStartTime
	} else {
		showdownGameConfig.IsRoundEnded = true
	}
	winner.Rounds[winnerRoundIndex] = winnerRoundData
	loser.Rounds[looserRoundIndex] = looserRoundData

	showdownGameConfig.ShowdownGamePlayer = append(showdownGameConfig.ShowdownGamePlayer, &showdownGamePlayer1, &showdownGamePlayer2)

	err = s.UpdateShowdownParticipant(ctx, winner, game.ShowdownGameConfig.Round)
	if err != nil {
		zlog.Error(ctx, "Failed to update showdown participant", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}

	err = s.UpdateShowdownParticipant(ctx, loser, game.ShowdownGameConfig.Round)
	if err != nil {
		zlog.Error(ctx, "Failed to update showdown participant", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}

	sortedLeaderboard[0].RatingChange = &ratingChangeInfo.WinnerRatingChange
	sortedLeaderboard[1].RatingChange = &ratingChangeInfo.LoserRatingChange

	sortedLeaderboard[0].StatikCoinsEarned = gameutils.GetStatikCoinsEarnedForWinner(sortedLeaderboard[0])
	sortedLeaderboard[1].StatikCoinsEarned = gameutils.GetStatikCoinsEarnedForLoser(sortedLeaderboard[1])

	game.ShowdownGameConfig = &showdownGameConfig

	err = s.publishGameEvent(ctx, game, constants.GameEventEnum.GAME_ENDED.String())
	if err != nil {
		zlog.Error(ctx, "Failed to publish game ended event", err)
		return nil, err
	}

	gameutils.SaveMinifiedQuestions(game)
	game.Questions = nil
	game.RematchRequestedBy = nil

	err = s.gameRepo.UpdateGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Failed to update game", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Failed to cache game", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}

	s.triggerPostGameUpdates(ctx, gameID, winnerUser, loserUser, ratingChangeInfo)
	return game, nil
}

func (s *service) UpdateShowdownParticipant(ctx context.Context, participant *models.ShowdownParticipant, round int) error {
	rank, err := s.showdownCache.SetShowdownParticipant(ctx, participant)
	if err != nil {
		zlog.Error(ctx, "UpdateShowdownParticipant: Failed to cache participant", err)
		return err
	}

	var currentUserParticipantDto models.CurrentShowdonParticipant
	err = showdown.MapShowdownParticipantToCurrentParticipant(participant, &currentUserParticipantDto, round)
	if err != nil {
		return err
	}

	err = s.coreService.PublishUserEvent(ctx, participant.UserID, &models.ShowdownParticipantUpdatedEvent{
		Participant: &currentUserParticipantDto,
	})
	if err != nil {
		zlog.Error(ctx, "Failed to publish ShowdownParticipantUpdatedEvent ", err)
		return err
	}

	showdownId := participant.ShowdownID
	page := int((rank+1)/constants.LEADERBOARD_PAGE_SIZE) + 1

	leaderboard, err := s.getPaginatedLeaderboard(ctx, showdownId, page)
	if err != nil {
		return err
	}
	err = s.publishShowdownLeaderboardEvent(ctx, participant.ShowdownID, page, &models.ShowdownEventPayload{Type: models.LEADERBOARD_UPDATE, Payload: leaderboard})
	if err != nil {
		return err
	}

	err = s.showdownParticipantRepo.Update(ctx, participant)
	return err
}

func (s *service) getPaginatedLeaderboard(ctx context.Context, showdownId primitive.ObjectID, page int) (*models.PaginatedLeaderboard, error) {
	totalCount, err := s.showdownCache.GetLeaderboardEntitiesCount(ctx, showdownId)
	if err != nil {
		return nil, err
	}
	leaderboardEntities, err := s.showdownCache.GetPaginatedLeaderboard(ctx, showdownId, page, constants.LEADERBOARD_PAGE_SIZE)
	if err != nil {
		return nil, err
	}
	participants := make([]*models.LeaderParticipantEntity, 0, len(leaderboardEntities))
	for _, v := range leaderboardEntities {
		participant, err := s.getShowdownParticipant(ctx, showdownId, v.UserId)
		if err != nil {
			return nil, err
		}
		participants = append(participants, &models.LeaderParticipantEntity{
			ShowdownId: showdownId,
			Score:      v.Score,
			Rank:       int(v.Rank),
			Participant: models.ParticipantBasicInfo{
				ID:         participant.ID,
				UserID:     participant.UserID,
				ShowdownID: participant.ShowdownID,
				Rounds:     participant.Rounds,
				UserInfo:   participant.UserInfo,
			},
			UserId: participant.UserID,
		})
	}
	return &models.PaginatedLeaderboard{
		Participants: participants,
		Count:        int(totalCount),
	}, nil
}

func (s *service) publishShowdownLeaderboardEvent(ctx context.Context, showdownId primitive.ObjectID, page int, payload *models.ShowdownEventPayload) error {
	channel := fmt.Sprintf("%s_%s_%d", constants.SHOWDOWN_EVENT_KEY, showdownId.Hex(), page)
	err := s.ws.Publish(ctx, channel, payload)
	return err
}

func getCurrentRoundDataAndIndex(participant *models.ShowdownParticipant, currentRound int) (*models.ShowdownRound, int, error) {
	for j, roundData := range participant.Rounds {
		if roundData == nil {
			return nil, j, fmt.Errorf("unable to get round data")
		}
		if roundData.Round == currentRound {
			return roundData, j, nil
		}
	}
	return nil, 0, fmt.Errorf("failed to get current round info")
}
