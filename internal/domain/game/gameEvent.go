package game

import (
	"context"
	"encoding/json"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) GameEvent(ctx context.Context, gameID *primitive.ObjectID) (<-chan *models.SubscriptionOutput, error) {
	if gameID == nil {
		return nil, fmt.Errorf("gameID is required")
	}

	zlog.Debug(ctx, "Game event", zap.String("gameID", gameID.Hex()))

	channel := fmt.Sprintf("%s_%s", constants.SubscriptionPrefixEnum.GAME_EVENT, gameID.Hex())

	subscription, err := s.ws.Subscribe(ctx, channel)
	if err != nil {
		zlog.Error(ctx, "Failed to start GameEvent subscription", err)
		return nil, fmt.Errorf("failed to start GameEvent subscription: %w", err)
	}

	outputChan := make(chan *models.SubscriptionOutput)

	go func() {
		defer close(outputChan)
		defer zlog.Info(ctx, "GameEvent subscription ended")

		for {
			select {
			case <-ctx.Done():
				return
			case msg, ok := <-subscription:
				if !ok {
					zlog.Warn(ctx, "GameEvent subscription channel closed unexpectedly")
					return
				}

				var output *models.SubscriptionOutput
				if err := json.Unmarshal([]byte(msg.(string)), &output); err != nil {
					zlog.Error(ctx, "Error unmarshalling message: %v\n", err)
					continue
				}

				select {
				case outputChan <- output:
					zlog.Debug(ctx, "GameEvent sent to client", zap.Any("event", output.Event))
				case <-ctx.Done():
					zlog.Info(ctx, "GameEvent subscription context cancelled", zap.Error(ctx.Err()))
					return
				}
			}
		}
	}()

	return outputChan, nil
}
