package game

import (
	"context"
	"errors"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

func (s *service) GetGameByID(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error) {
	if gameID == nil {
		return nil, fmt.Errorf("GameId is nil")
	}
	zlog.Debug(ctx, "Getting game by ID", zap.String("gameID", gameID.Hex()))

	cachedGame, err := s.gameCache.GetGame(ctx, gameID.Hex())
	if err == nil && cachedGame != nil {
		zlog.Debug(ctx, "Game found in cache", zap.String("gameID", gameID.Hex()))
		return cachedGame, nil
	}

	// If not in cache or there was an error, fetch from the database
	game, err := s.gameRepo.GetGameByID(ctx, *gameID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zlog.Error(ctx, "Game not found", err, zap.String("gameID", gameID.Hex()))
			return nil, fmt.Errorf("game not found")
		}
		zlog.Error(ctx, "Error fetching game", err, zap.String("gameID", gameID.Hex()))
		return nil, fmt.Errorf("error fetching game: %v", err)
	}

	// Cache the game for future use
	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Error caching game", err, zap.String("gameID", gameID.Hex()))
	}

	return game, nil
}
