package game

import (
	"context"
	"errors"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/mongo"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) GetGamesByUser(ctx context.Context, payload *models.GetGamesInput) (*models.GetGamesOutput, error) {
	if payload == nil {
		return nil, fmt.Errorf("invalid input payload")
	}

	userID := payload.UserID
	pageNumber := payload.PageInfo.PageNumber
	if pageNumber <= 0 {
		pageNumber = 0
	}
	rows := payload.PageInfo.Rows
	if rows <= 0 {
		rows = 20
	}

	gameIDs, err := s.userGameBucketRepo.GetGamesPaginated(ctx, userID, pageNumber, rows)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	filter := bson.M{"_id": bson.M{"$in": gameIDs}}

	games, err := s.gameRepo.GetMinifiedGames(ctx, filter, nil)
	if err != nil {
		zlog.Error(ctx, "Error retrieving games:", err)
		return nil, fmt.Errorf("error finding games: %w", err)
	}

	var userIDs []primitive.ObjectID
	for _, game := range games {
		for _, player := range game.Players {
			userIDs = append(userIDs, player.UserID)
		}
	}

	if len(userIDs) == 0 {
		return &models.GetGamesOutput{
			Games: games,
			Users: []*models.UserPublicDetails{},
		}, nil
	}

	users, err := s.userRepo.Find(ctx, bson.M{"_id": bson.M{"$in": userIDs}})
	if err != nil {
		zlog.Error(ctx, "Error finding users:", err)
		return nil, fmt.Errorf("error finding users: %w", err)
	}

	userPublicDetails := make([]*models.UserPublicDetails, len(users))
	for i, user := range users {
		userPublicDetails[i] = utils.GetUserPublicDetails(user)
	}

	return &models.GetGamesOutput{
		Games: games,
		Users: userPublicDetails,
	}, nil
}
