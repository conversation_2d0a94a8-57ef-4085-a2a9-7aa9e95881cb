package game

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"github.com/thoas/go-funk"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) GetGameDetailedAnalysis(ctx context.Context, gameID *primitive.ObjectID) (*models.GameDetailedAnalysis, error) {
	game, err := s.GetGameByID(ctx, gameID)
	if err != nil {
		return nil, fmt.Errorf("failed to get game: %w", err)
	}

	zlog.Debug(ctx, "Getting game detailed analysis", zap.String("gameID", gameID.Hex()))

	if game == nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("game not found")
	}

	questionAnalyses, err := s.analyzeGameQuestions(ctx, game)
	if err != nil {
		return nil, fmt.Errorf("failed to analyze game questions: %w", err)
	}

	return &models.GameDetailedAnalysis{
		Game:      game,
		Questions: questionAnalyses,
	}, nil
}

func (s *service) analyzeGameQuestions(ctx context.Context, game *models.Game) ([]*models.GameQuestionAnalysis, error) {
	uniquePresetIds := make(map[string]bool)
	uniqueUserIds := make(map[string]bool)
	questionDataMap := make(map[int]*struct {
		question  *models.Question
		userTimes map[string][]int
	})

	for i, minifiedQuestion := range game.MinifiedQuestions {
		parts := strings.Split(*minifiedQuestion, "$$")
		questionParts := strings.Split(parts[0], "__")

		rawExpression := questionParts[0]
		cleanedExpression := strings.Trim(rawExpression, "[]")
		expression := strings.Split(cleanedExpression, " ")
		presetIdentifier := questionParts[1]

		uniquePresetIds[presetIdentifier] = true

		userTimes := make(map[string][]int)
		for _, submission := range parts[1:] {
			submissionParts := strings.Split(submission, "_")
			userID := submissionParts[0]
			time, _ := strconv.Atoi(submissionParts[1])

			uniqueUserIds[userID] = true
			userTimes[userID] = append(userTimes[userID], time)
		}

		questionDataMap[i] = &struct {
			question  *models.Question
			userTimes map[string][]int
		}{
			question: &models.Question{
				Expression:       expression,
				PresetIdentifier: presetIdentifier,
			},
			userTimes: userTimes,
		}
	}

	presetIds := make([]string, 0, len(uniquePresetIds))
	for id := range uniquePresetIds {
		presetIds = append(presetIds, id)
	}

	globalPresets, err := s.presetsRepo.GetBulkGlobalPresets(ctx, presetIds)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, fmt.Errorf("failed to get global presets: %w", err)
	}

	userPresetsMap := make(map[string]map[string]*models.UserPreset)
	for userID := range uniqueUserIds {
		userObjId, err := primitive.ObjectIDFromHex(userID)
		if err != nil {
			return nil, fmt.Errorf("failed to decode userObjId: %w", err)
		}

		userPresets, err := s.presetsRepo.GetBulkUserPresets(ctx, presetIds, userObjId)
		if err != nil {
			return nil, fmt.Errorf("failed to get user presets: %w", err)
		}
		userPresetsMap[userID] = userPresets
	}

	var questionAnalyses []*models.GameQuestionAnalysis
	for i := 0; i < len(game.MinifiedQuestions); i++ {
		questionData := questionDataMap[i]
		presetId := questionData.question.PresetIdentifier

		var userAvgTimes []*models.UserAvgTime
		for userID, times := range questionData.userTimes {
			questionAvgTime := float64(funk.SumInt(times)) / float64(len(times))

			userPresets := userPresetsMap[userID]
			userPreset := userPresets[presetId]

			presetAvgTime := 0.0
			presetBestTime := 0.0
			if userPreset != nil {
				presetAvgTime = userPreset.CurAvgTime
				presetBestTime = float64(userPreset.BestTime)
			}

			userAvgTimes = append(userAvgTimes, &models.UserAvgTime{
				UserID:          userID,
				QuestionAvgTime: questionAvgTime,
				PresetAvgTime:   presetAvgTime,
				PresetBestTime:  presetBestTime,
			})
		}

		globalPreset := globalPresets[presetId]
		globalPresetAvgTime := 0.0
		globalBestTime := 0.0
		if globalPreset != nil {
			globalPresetAvgTime = globalPreset.GlobalAverageTime
			globalBestTime = float64(globalPreset.BestTime)
		}

		questionAnalyses = append(questionAnalyses, &models.GameQuestionAnalysis{
			Question:       questionData.question,
			AvgTimes:       userAvgTimes,
			GlobalAvgTime:  globalPresetAvgTime,
			GlobalBestTime: globalBestTime,
		})
	}

	return questionAnalyses, nil
}
