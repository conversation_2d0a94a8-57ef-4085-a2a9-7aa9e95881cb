package game

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) GetGameSeriesByID(ctx context.Context, gameID *primitive.ObjectID) (*models.GameSeries, error) {
	zlog.Debug(ctx, "Getting game series by ID", zap.String("gameID", gameID.Hex()))

	return s.gameSeriesRepo.GetGameSeriesByID(ctx, *gameID)
}
