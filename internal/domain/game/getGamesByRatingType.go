package game

import (
	"context"
	"fmt"
	"strings"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

const DEFAULT_PAGE_SIZE = 50

func (s *service) GetGamesByRatingType(ctx context.Context, payload *models.GetGamesByRatingInput) (*models.GetGamesByRatingOutput, error) {
	if payload == nil || payload.UserID == nil || payload.RatingType == nil || payload.PageInfo == nil {
		return nil, fmt.Errorf("invalid input payload: UserID, RatingType, and PageInfo are required")
	}
	if *payload.RatingType == "" {
		return nil, fmt.Errorf("invalid input payload: RatingType cannot be empty")
	}

	userID := *payload.UserID
	ratingType := strings.ToUpper(*payload.RatingType)
	pageNumber := payload.PageInfo.PageNumber
	if pageNumber <= 0 {
		pageNumber = 1
	}
	rows := payload.PageInfo.Rows
	if rows <= 0 {
		rows = DEFAULT_PAGE_SIZE
	}
	skip := int64((pageNumber - 1) * rows)
	limit := int64(rows)

	zlog.Info(ctx, "Fetching games for user by rating type",
		zap.String("userID", userID.Hex()),
		zap.String("ratingType", ratingType),
		zap.Int("pageNumber", pageNumber),
		zap.Int("rows", rows),
	)

	var activityTypes []constants.ActivityType
	isPuzzleGame := false
	switch ratingType {
	case "PUZZLE":
		activityTypes = []constants.ActivityType{
			constants.ActivityTypeCrossMathPuzzleWithFriend,
			constants.ActivityTypeCrossMathPuzzleDuel,
		}
		isPuzzleGame = true
	case "CLASSIC":
		activityTypes = []constants.ActivityType{constants.ActivityTypeAbilityDuels}
	case "MEMORY":
		activityTypes = []constants.ActivityType{constants.ActivityTypeFlashAnzan}
	case "BLITZ":
		activityTypes = []constants.ActivityType{
			constants.ActivityTypePlayOnline,
			constants.ActivityTypeFastestFinger,
			constants.ActivityTypePlayWithFriend,
		}
	default:
		return nil, fmt.Errorf("invalid ratingType: %s", ratingType)
	}

	var totalCount int64

	countFilter := bson.M{
		"userId":       userID,
		"activityType": bson.M{"$in": activityTypes},
		"activityId": bson.M{
			"$exists": true,
			"$ne":     primitive.NilObjectID,
		},
	}

	totalCount, err := s.userActivitiesRepo.Count(ctx, countFilter)
	if err != nil {
		zlog.Error(ctx, "Failed to count activities using generic Count", err, zap.String("userID", userID.Hex()))
		totalCount = 0
	}

	activityResults, err := s.userActivitiesRepo.GetActivitiesByActivityType(ctx, userID, activityTypes, skip, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch activities: %w", err)
	}

	if len(activityResults) == 0 {
		zlog.Info(ctx, "No activities found for the given criteria")
		return &models.GetGamesByRatingOutput{
			Games:       []*models.MinifiedGame{},
			PuzzleGames: []*models.MinifiedPuzzleGame{},
			Users:       []*models.UserPublicDetails{},
			TotalCount:  int(totalCount),
		}, nil
	}

	gameIDs := make([]primitive.ObjectID, 0, len(activityResults))
	for _, activity := range activityResults {
		if activity != nil && activity.ActivityID != nil {
			gameIDs = append(gameIDs, *activity.ActivityID)
		}
	}

	if len(gameIDs) == 0 {
		zlog.Info(ctx, "No valid game IDs found in activities")
		return &models.GetGamesByRatingOutput{
			Games:       []*models.MinifiedGame{},
			PuzzleGames: []*models.MinifiedPuzzleGame{},
			Users:       []*models.UserPublicDetails{},
			TotalCount:  int(totalCount),
		}, nil
	}

	var fetchedGames []*models.MinifiedGame
	var fetchedPuzzleGames []*models.MinifiedPuzzleGame
	var playerUserIDs []primitive.ObjectID

	findOptions := options.Find().SetSort(bson.D{{Key: "startTime", Value: -1}})
	filter := bson.M{"_id": bson.M{"$in": gameIDs}}

	if isPuzzleGame {
		fetchedPuzzleGames, err = s.puzzleGameRepo.GetMinifiedPuzzleGames(ctx, filter, findOptions)
		if err != nil {
			zlog.Error(ctx, "Error retrieving minified puzzle games", err)
			return nil, fmt.Errorf("error finding minified puzzle games: %w", err)
		}
		for _, game := range fetchedPuzzleGames {
			for _, player := range game.Players {
				playerUserIDs = append(playerUserIDs, player.UserID)
			}
		}
	} else {
		fetchedGames, err = s.gameRepo.GetMinifiedGames(ctx, filter, findOptions)
		if err != nil {
			zlog.Error(ctx, "Error retrieving games", err)
			return nil, fmt.Errorf("error finding games: %w", err)
		}
		for _, game := range fetchedGames {
			for _, player := range game.Players {
				playerUserIDs = append(playerUserIDs, player.UserID)
			}
		}
	}

	var userPublicDetails []*models.UserPublicDetails
	if len(playerUserIDs) > 0 {
		uniqueUserIDs := utils.UniqueObjectIDs(playerUserIDs)
		users, err := s.userRepo.Find(ctx, bson.M{"_id": bson.M{"$in": uniqueUserIDs}})
		if err != nil {
			zlog.Error(ctx, "Error finding users for game details", err)
			return nil, fmt.Errorf("error retrieving user details, %w", err)
		} else {
			userPublicDetails = make([]*models.UserPublicDetails, len(users))
			for i, user := range users {
				userPublicDetails[i] = utils.GetUserPublicDetails(user)
			}
		}
	} else {
		userPublicDetails = []*models.UserPublicDetails{}
	}

	output := &models.GetGamesByRatingOutput{
		Games:       fetchedGames,
		PuzzleGames: fetchedPuzzleGames,
		Users:       userPublicDetails,
		TotalCount:  int(totalCount),
	}

	return output, nil
}
