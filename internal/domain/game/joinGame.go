package game

import (
	"context"
	"fmt"
	"time"

	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/scheduler"
	"matiksOfficial/matiks-server-go/internal/domain/showdown"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) JoinGame(ctx context.Context, joinGameInput *models.JoinGameInput) (*models.Game, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Error retrieving active games:", err)
		return nil, err
	}

	zlog.Debug(ctx, "Joining game", zap.String("gameID", joinGameInput.GameID.Hex()))

	gameId := joinGameInput.GameID

	zlog.Debug(ctx, "JoinGame", zap.String("userID", userID.Hex()), zap.String("gameID", gameId.Hex()))

	game, err := s.GetGameByID(ctx, &gameId)
	if err != nil || game == nil {
		return nil, fmt.Errorf("game not found")
	}

	zlog.Info(ctx, "JoinGame", zap.String("userID", userID.Hex()), zap.String("gameID", game.ID.Hex()))
	if game.Config == nil || game.Config.NumPlayers == nil {
		return nil, fmt.Errorf("invalid game config")
	}

	if game.GameType == models.GameTypeSumdayShowdown && (game.ShowdownId == nil || game.ShowdownGameConfig == nil) {
		return nil, fmt.Errorf("invalid showdown config")
	}

	var showdown *models.Showdown
	if game.ShowdownId != nil {
		showdown, err = s.showdownCache.GetShowdown(ctx, *game.ShowdownId)
		if err != nil {
			return nil, fmt.Errorf("failed to get showdown Details")
		}
	}

	numPlayers := game.Config.NumPlayers
	acceptedPlayers := filterAcceptedPlayers(game.Players)

	zlog.Info(ctx, "JoinGame Resolver", zap.Any("acceptedPlayers", acceptedPlayers))
	currentUser, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("current user not found: %v", err)
	}

	rating := gameutils.GetPlayerRatingByGameType(currentUser, game.GameType)

	currPlayer := findPlayerByID(game.Players, userID)

	if currPlayer == nil && (len(acceptedPlayers) >= *numPlayers || game.GameType == models.GameTypeSumdayShowdown) {
		zlog.Error(ctx, "JoinGame: ", fmt.Errorf("game is already full"))
		return game, nil
	}

	if checkPlayersStatus(game.Players) && game.GameType == models.GameTypeSumdayShowdown && game.GameStatus == models.GameStatusCreated {
		game.GameStatus = models.GameStatusStarted
		err = s.publishGameEvent(ctx, game, constants.GameEventEnum.USER_JOINED.String())
		if err != nil {
			return nil, err
		}

		err = s.gameCache.SetGame(ctx, game)
		if err != nil {
			return nil, err
		}
		return game, nil
	}

	if currPlayer != nil && currPlayer.Status == models.PlayerStatusAccepted {
		zlog.Error(ctx, "JoinGame: ", fmt.Errorf("player already joined this game"))
		return game, nil
	}

	var updatedPlayers []*models.Player

	if currPlayer != nil {
		updatedPlayers = updatePlayerStatus(game.Players, userID, models.PlayerStatusAccepted)
	} else {
		player := models.Player{
			UserID:      userID,
			Status:      models.PlayerStatusAccepted,
			Rating:      &rating,
			StatikCoins: currentUser.StatikCoins,
			TimeLeft:    game.Config.TimeLimit,
		}
		updatedPlayers = append(game.Players, &player)
	}

	game.Players = updatedPlayers

	if game.GameType == models.GameTypeSumdayShowdown {
		showdownParticipant, err := s.getShowdownParticipant(ctx, *game.ShowdownId, userID.Hex())
		if err != nil {
			return nil, err
		}
		for i, roundData := range showdownParticipant.Rounds {
			if roundData.Round == game.ShowdownGameConfig.Round {
				roundData.HasJoined = true
				showdownParticipant.Rounds[i] = roundData
			}
		}
		err = s.UpdateShowdownParticipant(ctx, showdownParticipant, game.ShowdownGameConfig.Round)
		if err != nil {
			return nil, err
		}
	}

	if game.ShowdownId != nil && game.GameType == models.GameTypeSumdayShowdown {
		action := fmt.Sprintf(`JoinGame:%s:%s`, (*game.ShowdownId).Hex(), gameId.Hex())
		if !checkPlayersStatus(game.Players) {
			maxGameStartTime := showdown.StartTime.Add(time.Duration((showdown.RoundTime+showdown.GapBwRounds)*(game.ShowdownGameConfig.Round-1)+showdown.RoundConfig.MaxWaitTime) * time.Second)
			players := make([]models.Player, 0, len(updatedPlayers))
			for _, player := range updatedPlayers {
				if player != nil {
					players = append(players, *player)
				}
			}
			if err := scheduler.Schedule(ctx, s.sortedSet, &models.SchedulerPayload{
				Type:       models.JoinGameShowdownCheck,
				Action:     action,
				ContextMap: utils.GetContextValuesMap(ctx),
			}, maxGameStartTime); err != nil {
				return nil, err
			}
		} else {
			if err = scheduler.CancelScheduledTask(ctx, s.sortedSet, action); err != nil {
				return nil, err
			}
		}
	}

	if len(updatedPlayers) >= *numPlayers && game.GameType != models.GameTypeSumdayShowdown {
		game.GameStatus = models.GameStatusReady
	}

	if game.GameType == models.GameTypeSumdayShowdown && len(updatedPlayers) >= *numPlayers && checkPlayersStatus(updatedPlayers) {
		game.GameStatus = models.GameStatusReady
	}

	err = s.publishGameEvent(ctx, game, constants.GameEventEnum.USER_JOINED.String())
	if err != nil {
		return nil, err
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		return nil, err
	}

	return game, nil
}

func (s *service) CheckIfOpponentNotJoinedForShowdown(ctx context.Context, showdownId, gameId primitive.ObjectID) error {
	game, err := s.GetGameByID(ctx, &gameId)
	if err != nil {
		return err
	}
	// check if all players have already joined
	if checkPlayersStatus(game.Players) {
		return nil
	}
	if game.ShowdownGameConfig == nil {
		return fmt.Errorf("game config is nil")
	}
	for _, player := range game.Players {
		statusJoined := true
		if player.Status != models.PlayerStatusAccepted {
			statusJoined = false
		}
		err := s.updateShowdownGamePlayers(ctx, game, showdownId, player.UserID, statusJoined, game.ShowdownGameConfig)
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *service) updateShowdownGamePlayers(ctx context.Context, game *models.Game, showdownId, userId primitive.ObjectID, isJoined bool, showdownGameConfig *models.ShowdownGameConfig) error {
	participant, err := s.getShowdownParticipant(ctx, showdownId, userId.Hex())
	if err != nil {
		return err
	}
	if participant == nil {
		return fmt.Errorf("showdown participant not found")
	}
	if len(participant.Rounds) == 0 {
		participant.Rounds = append(participant.Rounds, &models.ShowdownRound{
			Score:        1,
			Round:        showdownGameConfig.Round,
			PlayerStatus: models.RoundPlayerStatusOpponentAbsent,
		})
		participant.TotalScore += 1
	} else {
		doesRoundConfigExist := false
		for i := range participant.Rounds {
			if participant.Rounds[i] != nil && participant.Rounds[i].Round == showdownGameConfig.Round {
				if isJoined {
					participant.Rounds[i].Score = 1
					participant.Rounds[i].PlayerStatus = models.RoundPlayerStatusOpponentAbsent
					participant.TotalScore += 1
				} else {
					participant.Rounds[i].PlayerStatus = models.RoundPlayerStatusDidNotPlay
				}
				doesRoundConfigExist = true
				break
			}
		}
		if !doesRoundConfigExist {
			roundData := models.ShowdownRound{}
			roundData.Round = showdownGameConfig.Round
			if isJoined {
				roundData.Score = 1
				participant.TotalScore += 1
				roundData.PlayerStatus = models.RoundPlayerStatusOpponentAbsent
			} else {
				// roundData.HasFailedToPlay = true
				roundData.PlayerStatus = models.RoundPlayerStatusDidNotPlay
			}
			participant.Rounds = append(participant.Rounds, &roundData)
		}
	}
	err = s.UpdateShowdownParticipant(ctx, participant, game.ShowdownGameConfig.Round)
	if err != nil {
		return err
	}
	game.GameStatus = models.GameStatusCancelled
	err = s.gameRepo.CancelGames(ctx, []primitive.ObjectID{*game.ID})
	if err != nil {
		return err
	}
	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Failed to update game cache ", err)
		return err
	}
	var currentUserParticipantDto models.CurrentShowdonParticipant
	err = showdown.MapShowdownParticipantToCurrentParticipant(participant, &currentUserParticipantDto, game.ShowdownGameConfig.Round)
	if err != nil {
		return err
	}
	err = s.coreService.PublishUserEvent(ctx, participant.UserID, &models.ShowdownParticipantUpdatedEvent{
		Participant: &currentUserParticipantDto,
	})
	if err != nil {
		zlog.Error(ctx, "Failed to publish ShowdownParticipantUpdatedEvent ", err)
		return err
	}
	return nil
}

func filterAcceptedPlayers(players []*models.Player) []models.Player {
	var accepted []models.Player
	for _, player := range players {
		if player.Status == models.PlayerStatusAccepted {
			accepted = append(accepted, *player)
		}
	}
	return accepted
}

func findPlayerByID(players []*models.Player, userID primitive.ObjectID) *models.Player {
	for _, player := range players {
		if player.UserID == userID {
			return player
		}
	}
	return nil
}

func updatePlayerStatus(players []*models.Player, userID primitive.ObjectID, status models.PlayerStatus) []*models.Player {
	for i, player := range players {
		if player.UserID == userID {
			players[i].Status = status
			return players
		}
	}
	return players
}

func checkPlayersStatus(players []*models.Player) bool {
	hasAllPlayerActive := true
	for _, player := range players {
		if player.Status == models.PlayerStatusInvited {
			hasAllPlayerActive = false
		}
	}
	return hasAllPlayerActive
}
