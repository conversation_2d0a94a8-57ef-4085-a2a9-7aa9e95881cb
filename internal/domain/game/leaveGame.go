package game

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) LeaveGame(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get user from context: %w", err)
	}

	game, err := s.GetGameByID(ctx, &gameID)
	if err != nil {
		return nil, fmt.Errorf("failed to get game: %w", err)
	}

	if game == nil {
		return nil, fmt.Errorf("game not found")
	}

	if game.GameStatus != models.GameStatusCreated && game.GameStatus != models.GameStatusReady {
		return nil, fmt.Errorf("cannot leave a game that has already started or ended")
	}

	isAdmin := game.CreatedBy == userID

	if isAdmin {
		return s.cancelGameAsAdmin(ctx, game)
	}

	return s.removePlayerFromGame(ctx, game, userID)
}

func (s *service) cancelGameAsAdmin(ctx context.Context, game *models.Game) (*models.Game, error) {
	game.GameStatus = models.GameStatusCancelled

	err := s.gameRepo.UpdateGame(ctx, game)
	if err != nil {
		return nil, fmt.Errorf("failed to update game status: %w", err)
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		return nil, fmt.Errorf("failed to set game in cache: %w", err)
	}

	for _, player := range game.Players {
		cancelGameOutput := models.GameCanceledOutput{
			GameID:     utils.AllocPtr(game.ID.Hex()),
			CreatorID:  utils.AllocPtr(game.CreatedBy.Hex()),
			OpponentID: utils.AllocPtr(player.UserID.Hex()),
			Status:     utils.AllocPtr(models.ChallengeStatuCancelled),
		}

		err = s.coreService.PublishUserEvent(ctx, player.UserID, cancelGameOutput)
		if err != nil {
			zlog.Error(ctx, "Failed to publish game canceled event to player", err)
		}
	}

	err = s.publishGameEvent(ctx, game, constants.GameEventEnum.GAME_CANCELLED.String())
	if err != nil {
		zlog.Error(ctx, "Failed to publish game cancelled event", err)
	}

	return game, nil
}

func (s *service) removePlayerFromGame(ctx context.Context, game *models.Game, userID primitive.ObjectID) (*models.Game, error) {
	updatedPlayers := make([]*models.Player, 0, len(game.Players))
	var removedPlayer *models.Player

	for _, player := range game.Players {
		if player.UserID != userID {
			updatedPlayers = append(updatedPlayers, player)
		} else {
			removedPlayer = player
		}
	}

	if removedPlayer == nil {
		return nil, fmt.Errorf("player not found in the game")
	}

	game.Players = updatedPlayers

	err := s.gameRepo.UpdateGame(ctx, game)
	if err != nil {
		return nil, fmt.Errorf("failed to update game: %w", err)
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		return nil, fmt.Errorf("failed to set game in cache: %w", err)
	}

	err = s.publishGameEvent(ctx, game, constants.GameEventEnum.USER_LEFT.String())
	if err != nil {
		zlog.Error(ctx, "Failed to publish user left event", err)
	}

	return game, nil
}
