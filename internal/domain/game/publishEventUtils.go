package game

import (
	"context"

	userUtils "matiksOfficial/matiks-server-go/internal/domain/user/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) publishEventToUser(ctx context.Context, targetUserID, sourceUserID primitive.ObjectID, event models.UserEvent) error {
	if targetUserID == sourceUserID {
		return s.coreService.PublishUserEvent(ctx, targetUserID, event)
	}

	sourceUser, err := s.userService.GetUserByID(ctx, sourceUserID)
	if err != nil {
		zlog.Error(ctx, "Failed to get source user for shadow ban check", err,
			zap.String("sourceUserID", sourceUserID.Hex()),
			zap.String("targetUserID", targetUserID.Hex()))
		return s.coreService.PublishUserEvent(ctx, targetUserID, event)
	}

	isShadowBanned := userUtils.IsUserShadowBanned(*sourceUser)
	if isShadowBanned {
		zlog.Debug(ctx, "Not publishing event from shadow banned user",
			zap.String("sourceUserID", sourceUserID.Hex()),
			zap.String("targetUserID", targetUserID.Hex()),
			zap.String("eventType", event.GetEventType().String()))
		return nil
	}

	return s.coreService.PublishUserEvent(ctx, targetUserID, event)
}
