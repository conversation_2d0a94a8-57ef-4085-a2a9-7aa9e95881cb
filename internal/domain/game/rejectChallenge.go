package game

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) RejectChallenge(ctx context.Context, gameID primitive.ObjectID) (*bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Error retrieving user from context:", err)
		return utils.AllocPtr(false), err
	}

	zlog.Debug(ctx, "Rejecting challenge", zap.String("gameID", gameID.Hex()))

	game, err := s.gameRepo.GetGameByID(ctx, gameID)
	if err != nil {
		zlog.Error(ctx, "Error retrieving game:", err)
		return utils.AllocPtr(false), err
	}

	if game.GameStatus != models.GameStatusCreated {
		return utils.AllocPtr(false), fmt.Errorf("challenge is no longer valid")
	}

	if game.Players[1].UserID != userID {
		return utils.AllocPtr(false), fmt.Errorf("you are not the challenged player")
	}

	game.GameStatus = models.GameStatusCancelled
	err = s.gameRepo.UpdateGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Error updating game status:", err)
		return utils.AllocPtr(false), err
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Error caching game:", err)
	}

	err = s.PublishChallengeEvent(ctx, game, string(models.ChallengeStatusChallengeRejected))
	if err != nil {
		zlog.Error(ctx, "Error publishing challenge rejected event:", err)
	}

	return utils.AllocPtr(true), nil
}
