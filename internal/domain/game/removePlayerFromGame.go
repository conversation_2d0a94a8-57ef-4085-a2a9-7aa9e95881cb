package game

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) RemovePlayer(ctx context.Context, gameId, playerId primitive.ObjectID) (bool, error) {
	zlog.Info(ctx, "Removing player from game", zap.String("gameID", gameId.Hex()), zap.String("playerID", playerId.Hex()))

	creatorId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, fmt.Errorf("failed to get User Details from Context ")
	}

	game, err := s.GetGameByID(ctx, &gameId)
	if err != nil {
		return false, fmt.Errorf("failed to get game: %w", err)
	}

	if game.CreatedBy != creatorId {
		return false, fmt.Errorf("only creator can remove players from the game")
	}

	if game == nil {
		return false, fmt.Errorf("game not found")
	}

	if game.GameStatus != models.GameStatusCreated {
		return false, fmt.Errorf("cannot remove player from a game that has already started or ended")
	}

	playerIndex := -1
	for i, player := range game.Players {
		if player.UserID == playerId {
			playerIndex = i
			break
		}
	}

	if playerIndex == -1 {
		return false, fmt.Errorf("player not found in the game")
	}

	game.Players = append(game.Players[:playerIndex], game.Players[playerIndex+1:]...)

	err = s.gameRepo.UpdateGame(ctx, game)
	if err != nil {
		return false, fmt.Errorf("failed to update game: %w", err)
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Failed to update cached game", err)
	}

	err = s.publishGameEvent(ctx, game, constants.GameEventEnum.PLAYER_REMOVED.String())
	if err != nil {
		zlog.Error(ctx, "Failed to publish player removed event", err)
	}

	return true, nil
}
