package game

import (
	"context"
	"encoding/json"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/zap"
)

func (s *service) SearchPlayer(ctx context.Context, userID *models.ObjectID) (<-chan *models.SearchSubscriptionOutput, error) {
	if userID == nil {
		return nil, fmt.Errorf("userId is required")
	}

	zlog.Debug(ctx, "Searching for player", zap.String("userID", userID.Hex()))

	channel := fmt.Sprintf("%s_%s", constants.SubscriptionPrefixEnum.USER_EVENT, userID.Hex())

	subscription, err := s.ws.Subscribe(ctx, channel)
	if err != nil {
		return nil, fmt.Errorf("failed to subscribe to channel: %w", err)
	}

	outputChan := make(chan *models.SearchSubscriptionOutput)

	go func() {
		defer close(outputChan)
		defer zlog.Info(ctx, "SearchPlayer subscription ended")

		for {
			select {
			case <-ctx.Done():
				return
			case msg, ok := <-subscription:
				if !ok {
					zlog.Warn(ctx, "SearchPlayer subscription channel closed unexpectedly")
					return
				}

				var output *models.SearchSubscriptionOutput
				if err := json.Unmarshal([]byte(msg.(string)), &output); err != nil {
					zlog.Error(ctx, "Error unmarshalling message: %v\n", err)
					continue
				}

				select {
				case outputChan <- output:
					zlog.Debug(ctx, "SearchPlayer sent to client", zap.String("channel", channel))
				case <-ctx.Done():
					zlog.Info(ctx, "SearchPlayer subscription context cancelled", zap.Error(ctx.Err()))
					return
				}
			}
		}
	}()

	return outputChan, nil
}
