package game

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) SendDelayedBadgeEvent(ctx context.Context, userID primitive.ObjectID, initialBadge, newBadge *models.BadgeType) {
	zlog.Info(ctx, "Starting delayed badge event", zap.String("userID", userID.Hex()))

	timer := time.NewTimer(3 * time.Second)
	defer timer.Stop()

	select {
	case <-timer.C:
		zlog.Info(ctx, "Publishing delayed badge event", zap.String("userID", userID.Hex()))
		err := s.coreService.PublishUserEvent(ctx, userID, &models.BadgeAssignedEvent{
			InitialBadge: initialBadge,
			NewBadge:     newBadge,
		})
		if err != nil {
			zlog.Error(ctx, "Failed to publish badge event", err)
			return
		}
	case <-ctx.Done():
		zlog.Warn(ctx, "Context cancelled before publishing delayed badge event", zap.String("userID", userID.Hex()))
		return
	}
}
