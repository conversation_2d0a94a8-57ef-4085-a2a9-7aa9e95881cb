package game

import (
	"context"
	"sync"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/infrastructure/leaderboard"
	"matiksOfficial/matiks-server-go/internal/infrastructure/locks"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/infrastructure/queue"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/infrastructure/sortedset"
	"matiksOfficial/matiks-server-go/internal/infrastructure/websocket"

	"go.uber.org/fx"
)

type service struct {
	ws                      websocket.Websocket
	gameCache               cache.GameCache
	cache                   cache.Cache
	coreService             domain.CoreLogicStore
	userService             domain.UserStore
	userRepo                repository.UserRepository
	gameRepo                repository.GameRepository
	gameSeriesRepo          repository.GameSeriesRepository
	playersQueue            queue.Queue
	presetsService          domain.PresetsStore
	userGameBucketRepo      repository.UserGameBucketRepository
	presetsRepo             repository.PresetsRepository
	showdownParticipantRepo repository.ShowdownParticipantRepository
	showdownCache           cache.ShowdownCache
	globalLeaderboard       leaderboard.GlobalLeaderboard
	mx                      *sync.Map
	locks                   locks.Locks
	sortedSet               sortedset.SortedSet
	notificationService     domain.NotificationStore
	userActivitiesRepo      repository.UserActivitiesRepository // Added for GetGamesByUserV2
	puzzleGameRepo          repository.PuzzleGameRepository     // Added for GetGamesByUserV2
	userStreakRepo          repository.UserStreakRepository
	botDetectionService     domain.BotDetectionStore
}

func NewGameService(
	lc fx.Lifecycle, ws websocket.Websocket, userService domain.UserStore,
	repositoryFactory *repository.RepositoryFactory, cacheInstance cache.Cache, playersQueue queue.Queue,
	presetsService domain.PresetsStore, locks locks.Locks, sortedSet sortedset.SortedSet,
	leaderboardCacheInstance cache.LeaderboardCache, globalLeaderboard leaderboard.GlobalLeaderboard,
	notificationService domain.NotificationStore,
	coreService domain.CoreLogicStore, botDetectionService domain.BotDetectionStore,
) domain.GameStore {
	s := &service{
		ws:                      ws,
		gameCache:               cache.NewGameCacheWrapper(cacheInstance),
		cache:                   cacheInstance,
		userService:             userService,
		showdownCache:           cache.NewShowdownCacheWrapper(cacheInstance, leaderboardCacheInstance),
		userRepo:                repositoryFactory.UserRepository,
		gameRepo:                repositoryFactory.GameRepository,
		gameSeriesRepo:          repositoryFactory.GameSeriesRepository,
		playersQueue:            playersQueue,
		presetsService:          presetsService,
		userGameBucketRepo:      repositoryFactory.UserGameBucketRepository,
		showdownParticipantRepo: repositoryFactory.ShowdownParticipantRepository,
		presetsRepo:             repositoryFactory.PresetsRepository,
		mx:                      &sync.Map{},
		globalLeaderboard:       globalLeaderboard,
		locks:                   locks,
		sortedSet:               sortedSet,
		notificationService:     notificationService,
		coreService:             coreService,
		userActivitiesRepo:      repositoryFactory.UserActivitiesRepository, // Added for GetGamesByUserV2
		puzzleGameRepo:          repositoryFactory.PuzzleGameRepository,     // Added for GetGamesByUserV2
		userStreakRepo:          repositoryFactory.UserStreakRepository,
		botDetectionService:     botDetectionService,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting game service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down game service")
			return nil
		},
	})

	return s
}
