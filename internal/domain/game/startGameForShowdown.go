package game

import (
	"context"
	"fmt"
	"sync"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/domain/scheduler"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) StartGameForShowdown(ctx context.Context, userID, gameID primitive.ObjectID) (*models.Game, error) {
	zlog.Debug(ctx, "Starting game for showdown", zap.Any("gameID", gameID))

	mtx, _ := s.mx.LoadOrStore(gameID.Hex(), &sync.Mutex{})
	mutex := mtx.(*sync.Mutex)
	mutex.Lock()
	defer (func() {
		mutex.Unlock()
		s.mx.Delete(gameID.Hex())
	})()

	game, err := s.GetGameByID(ctx, &gameID)
	if err != nil {
		zlog.Error(ctx, "failed to get game", err)
		return game, err
	}
	if game.GameStatus == models.GameStatusCancelled {
		zlog.Info(ctx, "game is cancelled")
		return game, fmt.Errorf("game is cancelled")
	}
	if game.GameStatus == models.GameStatusStarted {
		zlog.Debug(ctx, "game is cancelled")
		return game, nil
	}

	game.LeaderBoard = make([]*models.LeaderBoardEntry, len(game.Players))
	for i, player := range game.Players {
		leaderboardEntry := gameutils.GetDefaultUserLeaderBoardStand(player.UserID)
		game.LeaderBoard[i] = &leaderboardEntry
	}

	currPlayer := findPlayerByID(game.Players, userID)
	if currPlayer == nil {
		zlog.Debug(ctx, "you cannot start the game as you are not a player of this game")
		return nil, fmt.Errorf("you cannot start the game as you are not a player of this game")
	}

	showdown, err := s.showdownCache.GetShowdown(ctx, *game.ShowdownId)
	if err != nil {
		zlog.Error(ctx, "failed to get showdown", err)
		return nil, err
	}
	if game.ShowdownGameConfig == nil {
		zlog.Debug(ctx, "failed to get Showdown Config From Game")
		return nil, fmt.Errorf("failed to get Showdown Config From Game")
	}
	endTimeOfRound := int64(showdown.RoundTime*game.ShowdownGameConfig.Round) + int64((game.ShowdownGameConfig.Round-1)*showdown.GapBwRounds) + showdown.StartTime.Unix()
	if endTimeOfRound < time.Now().Unix() {
		game.GameStatus = models.GameStatusCancelled
		err = s.gameRepo.CancelGames(ctx, []primitive.ObjectID{*game.ID})
		if err != nil {
			zlog.Error(ctx, "failed to cancel Game", err)
			return nil, fmt.Errorf("failed to cancel Game")
		}
		err = s.gameCache.SetGame(ctx, game)
		if err != nil {
			zlog.Error(ctx, "failed to update Game cache", err)
			return nil, fmt.Errorf("failed to update Game cache")
		}
		zlog.Debug(ctx, "game is cancelled")
		return game, fmt.Errorf("game is cancelled")
	}

	game.GameStatus = models.GameStatusStarted
	questionsForGame := gameutils.GetQuestionsForGame(gameID.Hex(), game.Players, *game.Config)
	game.Questions = append(game.Questions, questionsForGame...)
	timeAfter5sec := time.Now().Add(5 * time.Second)
	game.StartTime = &timeAfter5sec
	err = s.addEncryptedQuestionsInGame(game)
	if err != nil {
		zlog.Error(ctx, "failed to add encrypted questions in game", err)
		return nil, err
	}

	game.StartTime = utils.AllocPtr(time.Now())
	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "failed to update Game cache", err)
		return nil, err
	}

	err = s.publishGameEvent(ctx, game, constants.GameEventEnum.GAME_STARTED.String())
	if err != nil {
		zlog.Error(ctx, "failed to publish game event", err)
		return nil, err
	}
	ctx = context.WithValue(ctx, constants.GameIDKey, game.ID.Hex())
	s.scheduleEndGame(ctx, game)
	return game, nil
}

func (s *service) StartGameForShowdownInternal(ctx context.Context, startGameInput *models.StartGameInput, startTime time.Time) (*models.Game, error) {
	zlog.Debug(ctx, "Starting game for showdown", zap.Any("startGameInput", startGameInput))

	gameID := startGameInput.GameID
	var game *models.Game
	game, err := s.GetGameByID(ctx, gameID)
	if err != nil {
		return nil, fmt.Errorf("game not found")
	}

	game.GameStatus = models.GameStatusStarted

	game.LeaderBoard = make([]*models.LeaderBoardEntry, len(game.Players))
	for i, player := range game.Players {
		leaderboardEntry := gameutils.GetDefaultUserLeaderBoardStand(player.UserID)
		game.LeaderBoard[i] = &leaderboardEntry
	}

	questionsForGame := gameutils.GetQuestionsForGame(gameID.Hex(), game.Players, *game.Config)
	game.Questions = append(game.Questions, questionsForGame...)
	game.StartTime = &startTime

	err = s.addEncryptedQuestionsInGame(game)
	if err != nil {
		return nil, err
	}

	if game.GameType == models.GameTypeSumdayShowdown && game.Config.TimeLimit != nil {
		gameEndTime := startTime.Add(time.Duration(*game.Config.TimeLimit)*time.Second + 250*time.Millisecond)
		err := scheduler.Schedule(ctx, s.sortedSet, &models.SchedulerPayload{
			Type: models.EndGameForShowdown,
			Action: models.EndGameActionPayload{
				GameID:     *startGameInput.GameID,
				StartTime:  startTime,
				GameConfig: game.Config,
			},
			ContextMap: utils.GetContextValuesMap(ctx),
		}, gameEndTime)
		if err != nil {
			return nil, err
		}
	}

	err = s.publishGameEvent(ctx, game, constants.GameEventEnum.GAME_STARTED.String())
	if err != nil {
		return nil, err
	}

	ctx = context.WithValue(ctx, constants.GameIDKey, game.ID.Hex())

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		return nil, err
	}

	return game, nil
}
