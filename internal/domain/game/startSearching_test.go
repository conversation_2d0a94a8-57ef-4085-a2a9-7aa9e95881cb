package game

import (
	"context"
	"sync"
	"testing"
	"time"

	"matiksOfficial/matiks-server-go/mocks/internal_/domain"
	"matiksOfficial/matiks-server-go/mocks/internal_/infrastructure/cache"
	"matiksOfficial/matiks-server-go/mocks/internal_/infrastructure/leaderboard"
	"matiksOfficial/matiks-server-go/mocks/internal_/infrastructure/locks"
	"matiksOfficial/matiks-server-go/mocks/internal_/infrastructure/queue"
	"matiksOfficial/matiks-server-go/mocks/internal_/infrastructure/repository"
	"matiksOfficial/matiks-server-go/mocks/internal_/infrastructure/sortedset"
	"matiksOfficial/matiks-server-go/mocks/internal_/infrastructure/websocket"

	"matiksOfficial/matiks-server-go/internal/constants"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func TestService_StartSearching(t *testing.T) {
	t.Run("successful start searching", func(t *testing.T) {
		// Setup mocks
		mockWS := websocket.NewMockWebsocket(t)
		mockGameCache := cache.NewMockGameCache(t)
		mockCache := cache.NewMockCache(t)
		mockUserService := domain.NewMockUserStore(t)
		mockUserRepo := repository.NewMockUserRepository(t)
		mockGameRepo := repository.NewMockGameRepository(t)
		mockGameSeriesRepo := repository.NewMockGameSeriesRepository(t)
		mockQueue := queue.NewMockQueue(t)
		mockPresetsService := domain.NewMockPresetsStore(t)
		mockPresetsRepo := repository.NewMockPresetsRepository(t)
		mockShowdownParticipantRepo := repository.NewMockShowdownParticipantRepository(t)
		mockShowdownCache := cache.NewMockShowdownCache(t)
		mockGlobalLeaderboard := leaderboard.NewMockGlobalLeaderboard(t)
		mockLocks := locks.NewMockLocks(t)
		mockSortedSet := sortedset.NewMockSortedSet(t)

		// Create service instance
		svc := &service{
			ws:                      mockWS,
			gameCache:               mockGameCache,
			cache:                   mockCache,
			userService:             mockUserService,
			userRepo:                mockUserRepo,
			gameRepo:                mockGameRepo,
			gameSeriesRepo:          mockGameSeriesRepo,
			playersQueue:            mockQueue,
			presetsService:          mockPresetsService,
			presetsRepo:             mockPresetsRepo,
			showdownParticipantRepo: mockShowdownParticipantRepo,
			showdownCache:           mockShowdownCache,
			globalLeaderboard:       mockGlobalLeaderboard,
			mx:                      &sync.Map{},
			locks:                   mockLocks,
			sortedSet:               mockSortedSet,
		}

		// Test data
		ctx := context.Background()
		userID := primitive.NewObjectID()
		ctx = context.WithValue(ctx, constants.UserContextKey, userID.Hex())

		user := &models.User{
			ID:     userID,
			Rating: utils.AllocPtr(1500),
		}

		gameConfigInput := &models.GameConfigInput{
			TimeLimit:  120,
			NumPlayers: 2,
			GameType:   models.GameTypePlayOnline,
		}

		gameConfig := &models.GameConfig{
			TimeLimit:  utils.AllocPtr(120),
			NumPlayers: utils.AllocPtr(2),
			GameType:   models.GameTypePlayOnline,
		}

		// Create a matched players array
		player := &models.Player{UserID: user.ID, Rating: user.Rating, StatikCoins: user.StatikCoins, Status: models.PlayerStatusAccepted}
		matchedPlayers := []*models.Player{player, player} // Two players for the match

		expectedGame := &models.Game{
			ID:         utils.AllocPtr(primitive.NewObjectID()),
			Config:     gameConfig,
			GameStatus: models.GameStatusStarted,
			Players:    matchedPlayers,
			CreatedBy:  user.ID,
			CreatedAt:  utils.AllocPtr(time.Now()),
		}

		// Setup expectations
		mockUserService.On("GetUserByID",
			mock.AnythingOfType("*context.valueCtx"),
			userID,
		).Return(user, nil)

		mockGameRepo.On("CreateGame",
			mock.AnythingOfType("*context.valueCtx"),
			mock.AnythingOfType("*models.Game"),
		).Return(expectedGame, nil)

		mockGameCache.On("SetGame",
			mock.AnythingOfType("*context.valueCtx"),
			mock.AnythingOfType("*models.Game"),
		).Return(nil)

		mockQueue.On("AddUser",
			mock.AnythingOfType("*context.valueCtx"),
			user,
			*gameConfig,
		).Return([]*models.User{user, user}, nil) // Return the matched players array

		// Add WebSocket notification mock for both players
		for _, player := range matchedPlayers {
			mockWS.On("NotifyUser",
				mock.AnythingOfType("*context.valueCtx"),
				player.UserID,
				"GAME_MATCHED",
				mock.AnythingOfType("*models.Game"),
			).Return(nil)

			// Add PublishUserEvent mock for each player
			mockWS.On("PublishUserEvent",
				mock.AnythingOfType("*context.valueCtx"),
				player.UserID,
				mock.AnythingOfType("*models.SearchSubscriptionOutput"),
			).Return(nil)
		}

		// Execute
		result, err := svc.StartSearching(ctx, gameConfigInput)

		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.True(t, *result)

		// Verify all mock expectations
		mockUserService.AssertExpectations(t)
		mockGameRepo.AssertExpectations(t)
		mockGameCache.AssertExpectations(t)
		mockQueue.AssertExpectations(t)
		mockWS.AssertExpectations(t)
	})

	t.Run("invalid game configuration", func(t *testing.T) {
		// Setup mocks (same as above)
		mockWS := websocket.NewMockWebsocket(t)
		mockGameCache := cache.NewMockGameCache(t)
		mockCache := cache.NewMockCache(t)
		mockUserService := domain.NewMockUserStore(t)
		mockUserRepo := repository.NewMockUserRepository(t)
		mockGameRepo := repository.NewMockGameRepository(t)
		mockGameSeriesRepo := repository.NewMockGameSeriesRepository(t)
		mockQueue := queue.NewMockQueue(t)
		mockPresetsService := domain.NewMockPresetsStore(t)
		mockPresetsRepo := repository.NewMockPresetsRepository(t)
		mockShowdownParticipantRepo := repository.NewMockShowdownParticipantRepository(t)
		mockShowdownCache := cache.NewMockShowdownCache(t)
		mockGlobalLeaderboard := leaderboard.NewMockGlobalLeaderboard(t)
		mockLocks := locks.NewMockLocks(t)
		mockSortedSet := sortedset.NewMockSortedSet(t)

		// Create service instance
		svc := &service{
			ws:                      mockWS,
			gameCache:               mockGameCache,
			cache:                   mockCache,
			userService:             mockUserService,
			userRepo:                mockUserRepo,
			gameRepo:                mockGameRepo,
			gameSeriesRepo:          mockGameSeriesRepo,
			playersQueue:            mockQueue,
			presetsService:          mockPresetsService,
			presetsRepo:             mockPresetsRepo,
			showdownParticipantRepo: mockShowdownParticipantRepo,
			showdownCache:           mockShowdownCache,
			globalLeaderboard:       mockGlobalLeaderboard,
			mx:                      &sync.Map{},
			locks:                   mockLocks,
			sortedSet:               mockSortedSet,
		}

		// Test data
		ctx := context.Background()
		userID := primitive.NewObjectID()
		ctx = context.WithValue(ctx, constants.UserContextKey, userID.Hex())

		invalidGameConfig := &models.GameConfigInput{
			TimeLimit:  0, // Invalid time limit
			NumPlayers: 2,
			GameType:   models.GameTypePlayOnline,
		}

		// Execute
		result, err := svc.StartSearching(ctx, invalidGameConfig)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "invalid game configuration")
	})

	t.Run("user not found", func(t *testing.T) {
		// Setup mocks (same as above)
		mockWS := websocket.NewMockWebsocket(t)
		mockGameCache := cache.NewMockGameCache(t)
		mockCache := cache.NewMockCache(t)
		mockUserService := domain.NewMockUserStore(t)
		mockUserRepo := repository.NewMockUserRepository(t)
		mockGameRepo := repository.NewMockGameRepository(t)
		mockGameSeriesRepo := repository.NewMockGameSeriesRepository(t)
		mockQueue := queue.NewMockQueue(t)
		mockPresetsService := domain.NewMockPresetsStore(t)
		mockPresetsRepo := repository.NewMockPresetsRepository(t)
		mockShowdownParticipantRepo := repository.NewMockShowdownParticipantRepository(t)
		mockShowdownCache := cache.NewMockShowdownCache(t)
		mockGlobalLeaderboard := leaderboard.NewMockGlobalLeaderboard(t)
		mockLocks := locks.NewMockLocks(t)
		mockSortedSet := sortedset.NewMockSortedSet(t)

		// Create service instance
		svc := &service{
			ws:                      mockWS,
			gameCache:               mockGameCache,
			cache:                   mockCache,
			userService:             mockUserService,
			userRepo:                mockUserRepo,
			gameRepo:                mockGameRepo,
			gameSeriesRepo:          mockGameSeriesRepo,
			playersQueue:            mockQueue,
			presetsService:          mockPresetsService,
			presetsRepo:             mockPresetsRepo,
			showdownParticipantRepo: mockShowdownParticipantRepo,
			showdownCache:           mockShowdownCache,
			globalLeaderboard:       mockGlobalLeaderboard,
			mx:                      &sync.Map{},
			locks:                   mockLocks,
			sortedSet:               mockSortedSet,
		}

		// Test data
		ctx := context.Background()
		userID := primitive.NewObjectID()
		ctx = context.WithValue(ctx, constants.UserContextKey, userID.Hex())

		gameConfig := &models.GameConfigInput{
			TimeLimit:  120,
			NumPlayers: 2,
			GameType:   models.GameTypePlayOnline,
		}

		// Setup expectations
		mockUserService.On("GetUserByID", ctx, userID).Return(nil, assert.AnError)

		// Execute
		result, err := svc.StartSearching(ctx, gameConfig)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, result)

		// Verify expectations
		mockUserService.AssertExpectations(t)
	})
}
