package botutils

import (
	"context"
	"fmt"
	"math"
	"math/rand/v2"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain"
	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/models"
)

func GetRandomRatingForBot(userRating int) int {
	lowerRange := int(math.Min(math.Max(float64(userRating-50), 500), 4000))
	higherRange := int(math.Max(math.Min(float64(userRating+50), 4000), 500))
	if higherRange < lowerRange {
		return lowerRange
	}
	return rand.IntN(higherRange-lowerRange+1) + lowerRange
}

func GetRandomV2RatingForBot(userRating int) *models.UserRating {
	lowerRange := int(math.Min(math.Max(float64(userRating-50), 500), 4000))
	higherRange := int(math.Max(math.Min(float64(userRating+50), 4000), 500))
	if higherRange < lowerRange {
		return &models.UserRating{
			GlobalRating:       &lowerRange,
			FlashAnzanRating:   &lowerRange,
			AbilityDuelsRating: &lowerRange,
			PuzzleRating:       &lowerRange,
		}
	}
	randomRating := rand.IntN(higherRange-lowerRange+1) + lowerRange
	return &models.UserRating{
		GlobalRating:       &randomRating,
		FlashAnzanRating:   &randomRating,
		AbilityDuelsRating: &randomRating,
		PuzzleRating:       &randomRating,
	}
}

func GetBotUser(ctx context.Context, currentUser *models.User, userRepo repository.UserRepository, userService domain.UserStore, gameConfig models.GameConfigInterface) (*models.User, error) {
	if currentUser == nil {
		return nil, fmt.Errorf("currentUser is nil")
	}
	if currentUser.Rating == nil {
		return nil, fmt.Errorf("currentUser.Rating is nil")
	}

	currentUserRating := gameutils.GetPlayerRatingByGameType(currentUser, gameConfig.GetGameType())

	var opponentIDs []primitive.ObjectID
	if currentUser.Stats != nil && currentUser.Stats.Last10BotGames != nil && len(currentUser.Stats.Last10BotGames) > 0 {
		numGames := min(5, len(currentUser.Stats.Last10BotGames))
		for i := 0; i < numGames; i++ {
			if currentUser.Stats.Last10BotGames[i].OpponentID != nil {
				opponentIDs = append(opponentIDs, *currentUser.Stats.Last10BotGames[i].OpponentID)
			}
		}
	}

	ratingField := gameutils.GetRatingFieldByGameType(gameConfig.GetGameType())
	// Find the bot user with the closest rating in the database
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"isBot":     true,
			"_id":       bson.M{"$nin": opponentIDs},
			"ratingV2":  bson.M{"$exists": true},
			ratingField: bson.M{"$or": bson.A{bson.M{"$exists": true}, bson.M{"$ne": nil}}},
		}}},
		{{Key: "$addFields", Value: bson.M{
			"ratingDifference": bson.M{
				"$abs": bson.M{
					"$subtract": bson.A{bson.M{"$ifNull": []interface{}{"$" + ratingField, currentUserRating}}, currentUserRating},
				},
			},
		}}},
		{{Key: "$sort", Value: bson.M{"ratingDifference": 1}}},
		{{Key: "$limit", Value: 1}},
	}

	botUsers, err := userRepo.Aggregate(ctx, pipeline)
	if err == nil && len(botUsers) != 0 {
		botUser := botUsers[0]
		botRating := gameutils.GetPlayerRatingByGameType(botUser, gameConfig.GetGameType())
		if botUser != nil && math.Abs(float64(botRating-currentUserRating)) <= 100 {
			return botUser, nil
		}
	}
	// If no suitable bot user found, create a new one
	totalUsersCount, err := userRepo.Count(ctx, bson.M{})
	if err != nil {
		return nil, fmt.Errorf("failed to count users: %w", err)
	}

	botName := fmt.Sprintf("guest%d", totalUsersCount+1)
	username, err := userService.GenerateUserName(ctx, botName)
	if err != nil {
		return nil, fmt.Errorf("failed to generate username: %w", err)
	}
	botRandomRatingV2 := GetRandomV2RatingForBot(currentUserRating)
	newBotUser := &models.User{
		ID:        primitive.NewObjectID(),
		Name:      utils.AllocPtr(botName),
		Rating:    botRandomRatingV2.GlobalRating,
		RatingV2:  botRandomRatingV2,
		Username:  username,
		IsBot:     utils.AllocPtr(true),
		IsGuest:   utils.AllocPtr(true),
		CreatedAt: utils.AllocPtr(time.Now()),
		UpdatedAt: utils.AllocPtr(time.Now()),
	}

	err = userRepo.Create(ctx, newBotUser)
	if err != nil {
		return nil, fmt.Errorf("failed to create bot user: %w", err)
	}

	return newBotUser, nil
}

func isBot(ctx context.Context, opponentID *primitive.ObjectID, userService domain.UserStore) (bool, int) {
	if opponentID == nil {
		return false, constants.DefaultRating
	}
	botUser, err := userService.GetUserByID(ctx, *opponentID)
	if err != nil || botUser == nil {
		return false, constants.DefaultRating
	}
	return botUser.IsBot != nil && *botUser.IsBot, *botUser.Rating
}
