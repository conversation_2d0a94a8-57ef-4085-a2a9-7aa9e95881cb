package botutils

import (
	"container/heap"
	"context"
	"encoding/json"
	"fmt"
	"math"
	"time"

	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/user/humanBots"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/infrastructure/priority_queue"
	"matiksOfficial/matiks-server-go/internal/infrastructure/queue"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/zap"
)

func MatchHumanBot(ctx context.Context, userRepo repository.UserRepository, cache cache.Cache, rating int, opponentID models.ObjectID) (*models.User, error) {
	groupedHumanBots, err := humanBots.GetHumanBotsGrouped(userRepo, cache)
	if err != nil {
		return nil, err
	}

	pq := make(priority_queue.PriorityQueue, len(groupedHumanBots))

	for i, group := range groupedHumanBots {
		pq[i] = &priority_queue.Item{
			Value:    group,
			Index:    i,
			Priority: -1 * int(math.Abs(float64(rating-group.TargetRating))),
		}
	}
	heap.Init(&pq)

	currentHour := time.Now().Hour()
	for pq.Len() > 0 {
		item := heap.Pop(&pq).(*priority_queue.Item)
		humanBotsGroup := item.Value.(*models.GroupedHumanBots)
		if humanBotsGroup.TargetRating > rating+200 || humanBotsGroup.TargetRating < rating-200 {
			continue
		}
		for _, bot := range humanBotsGroup.HumanBots {
			if bot.HumanBotConfig != nil &&
				bot.HumanBotConfig.ActiveStartTime < currentHour &&
				(bot.HumanBotConfig.ActiveEndTime >= currentHour || bot.HumanBotConfig.ActiveEndTime == 0) &&
				(bot.HumanBotConfig.InGame == nil || (bot.HumanBotConfig.InGame != nil && !*bot.HumanBotConfig.InGame)) {
				if queue.PlayedInLastHour(opponentID, bot) > 4 || !queue.ValidRatingMatch(rating, gameutils.GetPlayerRatingByGameType(bot, models.GameTypePlayOnline)) {
					continue
				}
				queue.UpdatePlayedInLastHour(opponentID, bot)
				zlog.Debug(ctx, "Match found", zap.String("userID", opponentID.Hex()), zap.String("matchedBotID", bot.ID.Hex()))
				return bot, nil
			}
		}
	}

	return nil, fmt.Errorf("no human bot matches found")
}

func UpdateHumanBotCache(u *models.User, cache cache.Cache, inGame bool) error {
	if u.IsHumanBot != nil && *u.IsHumanBot {
		u.HumanBotConfig.InGame = utils.AllocPtr(inGame)
		byteData, err := json.Marshal(u)
		if err != nil {
			return err
		}
		cache.Set(context.TODO(), constants.HumanBotKey+u.ID.Hex(), byteData, 3*time.Hour)
	}
	return nil
}
