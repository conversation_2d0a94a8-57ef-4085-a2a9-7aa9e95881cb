package botutils

import (
	"context"
	"errors"
	"time"

	"matiksOfficial/matiks-server-go/utils/systemErrors"

	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/domain/questions"
	"matiksOfficial/matiks-server-go/internal/domain/utils"

	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/middleware"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func RunBot(ctx context.Context, game *models.Game, botUser *models.User, gameService domain.GameStore, cache cache.Cache) error {
	ctx, span := middleware.WithSpan(ctx, "runBot")
	defer span.End()
	zlog.Debug(ctx, "Entering RunBot function", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", game.ID.Hex()))

	botRating := gameutils.GetPlayerRatingByGameType(botUser, game.Config.GetGameType())
	startTime := *game.StartTime
	maxGameDuration := time.Duration(*game.Config.TimeLimit) * time.Second
	var elapsedTime time.Duration = 0

	zlog.Info(ctx, "Running Bot", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", game.ID.Hex()))
	zlog.Debug(ctx, "Updating human bot cache", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", game.ID.Hex()))

	err := UpdateHumanBotCache(botUser, cache, true)
	if err != nil {
		zlog.Error(ctx, "Failed to update human bot cache", err, zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", game.ID.Hex()))
		return err
	}
	if botUser.IsHumanBot != nil && *botUser.IsHumanBot && botUser.HumanBotConfig != nil {
		zlog.Debug(ctx, "Using human bot configuration", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", game.ID.Hex()), zap.Int("targetRating", botUser.HumanBotConfig.TargetRating))
		botRating = botUser.HumanBotConfig.TargetRating
	}

	if game.Config.GameType == models.GameTypeFlashAnzan {
		maxScore := questions.GetBotFlashAnzanMaxScore(botRating)

		for i := 0; i < 3; i++ {
			isCorrect := questions.ShouldBotAnswerCorrectly(maxScore)
			timer := 30 * time.Second
			ticker := time.NewTicker(timer)
			defer ticker.Stop()

			select {
			case <-ticker.C:
				answerInput := models.SubmitFlashAnzanAnswerInput{
					GameID:           game.ID,
					IsCorrect:        &isCorrect,
					MaxScore:         &maxScore,
					TimeOfSubmission: utils.AllocPtr(models.DateFromTime(time.Now())),
				}

				zlog.Debug(ctx, "Submitting answer by Bot", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", game.ID.Hex()), zap.Any("answerInput", answerInput))
				botCtx := context.WithValue(ctx, constants.UserContextKey, botUser.ID.Hex())

				if _, err := gameService.SubmitFlashAnzanAnswer(botCtx, &answerInput); err != nil && !errors.Is(err, systemErrors.ErrGameAlreadyEnded) {
					return err
				}
			case <-ctx.Done():
				return nil
			}
		}
	}

	zlog.Debug(ctx, "Starting question loop", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", game.ID.Hex()), zap.Int("totalQuestions", len(game.Questions)))
	for _, question := range game.Questions {
		zlog.Debug(ctx, "Processing question", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", game.ID.Hex()), zap.String("questionID", *question.Question.ID))

		expectedTime := questions.GetExpectedTimeToSolveQuestion(botRating, *question.Question.Rating, question.Question.PresetIdentifier)
		solveAfterTime := expectedTime - time.Since(startTime)

		if elapsedTime < maxGameDuration {
			if solveAfterTime > 0 {
				elapsedTime += solveAfterTime
				select {
				case <-time.After(solveAfterTime):
				case <-ctx.Done():
					return nil
				}
			}

			answerInput := models.SubmitAnswerInput{
				GameID:           *game.ID,
				QuestionID:       *question.Question.ID,
				SubmittedValue:   question.Question.Answers[0],
				IsCorrect:        true,
				TimeOfSubmission: models.DateFromTime(time.Now()),
			}
			zlog.Debug(ctx, "Submitting answer by Bot", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", game.ID.Hex()), zap.Any("answerInput", answerInput))
			botCtx := context.WithValue(ctx, constants.UserContextKey, botUser.ID.Hex())
			if _, err := gameService.SubmitAnswer(botCtx, &answerInput); err != nil && !errors.Is(err, systemErrors.ErrGameAlreadyEnded) {
				zlog.Error(ctx, "Failed to submit answer by Bot", err, zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", game.ID.Hex()))
				return err
			}
			zlog.Debug(ctx, "Answer submitted successfully", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", game.ID.Hex()))

			startTime = time.Now()
		} else {
			zlog.Debug(ctx, "Max game duration reached", zap.String("botUserID", botUser.ID.Hex()), zap.Duration("elapsedTime", elapsedTime), zap.String("gameID", game.ID.Hex()))
			return nil
		}
	}
	zlog.Debug(ctx, "Exiting RunBot function", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", game.ID.Hex()))
	return nil
}
