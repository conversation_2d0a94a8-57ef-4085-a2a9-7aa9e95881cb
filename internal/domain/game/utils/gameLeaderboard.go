package gameutils

import (
	"sort"

	"github.com/thoas/go-funk"

	"matiksOfficial/matiks-server-go/internal/models"
)

func GetDefaultUserLeaderBoardStand(userID models.ObjectID) models.LeaderBoardEntry {
	correct := 0
	incorrect := 0
	totalPoints := 0.0
	rank := 0

	return models.LeaderBoardEntry{
		UserID:      &userID,
		Correct:     &correct,
		Incorrect:   &incorrect,
		TotalPoints: &totalPoints,
		Rank:        &rank,
	}
}

func UpdateLeaderboardRanks(game *models.Game) []*models.LeaderBoardEntry {
	leaderboard := game.LeaderBoard
	players := game.Players

	sort.Slice(leaderboard, func(i, j int) bool {
		if *leaderboard[i].TotalPoints != *leaderboard[j].TotalPoints {
			return *leaderboard[i].TotalPoints > *leaderboard[j].TotalPoints
		}

		playerI, ok := funk.Find(players, func(player *models.Player) bool {
			return player.UserID == *leaderboard[i].UserID
		}).(*models.Player)
		if !ok {
			return false
		}

		playerJ, ok := funk.Find(players, func(player *models.Player) bool {
			return player.UserID == *leaderboard[j].UserID
		}).(*models.Player)
		if !ok {
			return false
		}

		return *playerI.Rating < *playerJ.Rating
	})

	for i := range leaderboard {
		rank := i + 1
		leaderboard[i].Rank = &rank
	}

	return leaderboard
}
