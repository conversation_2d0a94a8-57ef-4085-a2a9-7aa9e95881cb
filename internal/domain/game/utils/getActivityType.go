package gameutils

import (
	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/models"
)

// GetActivityTypeFromGameType returns the appropriate ActivityType based on the game's GameType
func GetActivityTypeFromGameType(gameType models.GameType) constants.ActivityType {
	return getActivityType(string(gameType))
}

// GetActivityTypeFromPuzzleGameType returns the appropriate ActivityType based on the puzzle game's GameType
func GetActivityTypeFromPuzzleGameType(gameType models.PuzzleGameType) constants.ActivityType {
	return getActivityType(string(gameType))
}

// getActivityType is a helper function that maps game type strings to activity types
func getActivityType(gameTypeStr string) constants.ActivityType {
	switch gameTypeStr {
	case string(models.GameTypePlayOnline):
		return constants.ActivityTypePlayOnline
	case string(models.GameTypePlayWithFriend):
		return constants.ActivityTypePlayWithFriend
	case string(models.GameTypeOnlineChallenge):
		return constants.ActivityTypePlayOnline
	case string(models.GameTypePractice):
		return constants.ActivityTypePracticeOnline
	case string(models.GameTypeFlashAnzan):
		return constants.ActivityTypeFlashAnzan
	case string(models.GameTypeFastestFinger):
		return constants.ActivityTypeFastestFinger
	case string(models.GameTypeSumdayShowdown):
		return constants.ActivityTypeSumdayShowdown
	case string(models.GameTypeGroupPlay):
		return constants.ActivityTypeGroupPlay
	case string(models.GameTypeAbilityDuels):
		return constants.ActivityTypeAbilityDuels
	case string(models.PuzzleGameTypeCrossMathPuzzleDuel):
		return constants.ActivityTypeCrossMathPuzzleDuel
	case string(models.PuzzleGameTypeCrossMathPuzzleWithFriend):
		return constants.ActivityTypeCrossMathPuzzleWithFriend
	default:
		return constants.ActivityTypePlayOnline
	}
}
