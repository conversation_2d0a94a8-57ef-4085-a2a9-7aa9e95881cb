package gameutils

import (
	"context"
	"fmt"
	"math"
	"math/rand/v2"
	"strings"

	"go.uber.org/zap"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/domain/questions/questionsGenerator"
	"matiksOfficial/matiks-server-go/internal/models"
)

const (
	TYPE_MOD = iota
	TYPE_ROOT
	TYPE_EXPONENT
	TYPE_HCF
	TYPE_LCM
	TYPE_PRIME_FACTORIZATION
	TYPE_MULTIPLE_OPERATORS
	TYPE_SUM_OF_SQUARES
)

const (
	MAX_ATTEMPTS_TO_GENERATE_QUESTION   = 100
	NUMBER_OF_QUESTIONS_PER_MIN         = 60
	NUMBER_OF_QUESTIONS_PER_MIN_ABILITY = 80
	ABILITY_QUESTIONS_TYPES             = 8
)

const (
	WEIGHTAGE_MULTIPLE_OPERATORS  = 0
	WEIGHTAGE_MOD                 = 5
	WEIGHTAGE_SUM_OF_SQUARES      = 4
	WEIGHTAGE_ROOT                = 3
	WEIGHTAGE_HCF                 = 3
	WEIGHTAGE_LCM                 = 3
	WEIGHTAGE_PRIME_FACTORIZATION = 3
	WEIGHTAGE_EXPONENT            = 5
)

func GetQuestionsForGame(questionIdPrefix string, players []*models.Player, config models.GameConfig) []*models.GameQuestion {
	playerRatings := make([]int, len(players))
	for i, player := range players {
		playerRatings[i] = *player.Rating
	}

	if config.DifficultyLevel != nil {
		playerRatings = config.DifficultyLevel
	}

	minRating := playerRatings[0]
	maxRating := playerRatings[0]
	for _, rating := range playerRatings {
		if rating < minRating {
			minRating = rating
		}
		if rating > maxRating {
			maxRating = rating
		}
	}

	startRating := int(math.Max(0, float64(minRating-400)))
	endRating := maxRating + 400
	numberOfQuestions := NUMBER_OF_QUESTIONS_PER_MIN * (*config.TimeLimit) / 60

	ratings := questionsGenerator.GenerateUniformRatings(startRating, endRating, numberOfQuestions)

	questions := make([]*models.GameQuestion, 0, numberOfQuestions)
	uniqueExpressions := make(map[string]struct{})

	for i, rating := range ratings {
		var question *models.Question
		unique := false
		attempts := 10

		question = questionsGenerator.GetRandomArithmeticQuestion(rating, config.QuestionTags)

		for !unique && attempts > 0 {
			expressionKey := strings.Join(question.Expression, " ")

			if _, exists := uniqueExpressions[expressionKey]; !exists {
				unique = true
				uniqueExpressions[expressionKey] = struct{}{}
			} else {
				attempts--
				question = questionsGenerator.GetRandomArithmeticQuestion(rating, config.QuestionTags)
			}
		}

		questionID := fmt.Sprintf("%s_%d", questionIdPrefix, i)
		question.ID = &questionID
		questions = append(questions, &models.GameQuestion{
			Question: question,
		})
	}

	return questions
}

func GetAbilityQuestionsForGame(questionIdPrefix string, players []*models.Player, config models.GameConfig, weightages *[]models.QuestionTypeWeightage) ([]*models.GameQuestion, error) {
	if weightages == nil {
		weightages = &[]models.QuestionTypeWeightage{
			{Type: TYPE_MULTIPLE_OPERATORS, Weightage: WEIGHTAGE_MULTIPLE_OPERATORS},
			{Type: TYPE_MOD, Weightage: WEIGHTAGE_MOD},
			{Type: TYPE_SUM_OF_SQUARES, Weightage: WEIGHTAGE_SUM_OF_SQUARES},
			{Type: TYPE_ROOT, Weightage: WEIGHTAGE_ROOT},
			{Type: TYPE_HCF, Weightage: WEIGHTAGE_HCF},
			{Type: TYPE_LCM, Weightage: WEIGHTAGE_LCM},
			{Type: TYPE_PRIME_FACTORIZATION, Weightage: WEIGHTAGE_PRIME_FACTORIZATION},
			{Type: TYPE_EXPONENT, Weightage: WEIGHTAGE_EXPONENT},
		}
	}

	if len(players) < 2 {
		return nil, fmt.Errorf("not enough players")
	}

	if players[0].Rating == nil || players[1].Rating == nil {
		zlog.Error(context.Background(), "Rating Is nil", fmt.Errorf("rating nil"),
			zap.String("userID", players[0].UserID.Hex()), zap.String("userID", players[1].UserID.Hex()))
		return nil, fmt.Errorf("rating is nil")
	}

	minRating := math.Min(float64(*players[0].Rating), float64(*players[1].Rating))
	maxRating := math.Max(float64(*players[0].Rating), float64(*players[1].Rating))
	rating := int(minRating+maxRating) / 2
	avgRating := math.Max(0, float64(rating))

	numberOfQuestions := NUMBER_OF_QUESTIONS_PER_MIN_ABILITY * (*config.TimeLimit) / 60
	totalQuestions := numberOfQuestions
	if totalQuestions == 0 {
		totalQuestions = 1
	}
	questions := make([]*models.GameQuestion, 0, totalQuestions)
	uniqueExpressions := make(map[string]struct{})

	totalWeightage := 0
	for _, w := range *weightages {
		totalWeightage += w.Weightage
	}
	for _, w := range *weightages {
		questionsPerType := int(math.Round(float64(w.Weightage) / float64(totalWeightage) * float64(totalQuestions)))
		if questionsPerType == 0 {
			questionsPerType = 1
		}
		generateQuestionsOfType(
			w.Type,
			questionsPerType,
			int(avgRating),
			questionIdPrefix,
			&questions,
			uniqueExpressions,
		)
	}

	shuffleQuestions(questions)
	return questions, nil
}

func generateQuestionsOfType(
	questionType int,
	count int,
	rating int,
	questionIdPrefix string,
	questions *[]*models.GameQuestion,
	uniqueExpressions map[string]struct{},
) {
	for i := 0; i < count; i++ {
		var question *models.Question
		for attempt := 0; attempt < MAX_ATTEMPTS_TO_GENERATE_QUESTION; attempt++ {
			question = getQuestionByType(questionType, rating)
			if question == nil {
				zlog.Warn(context.Background(), "Failed to generate question", zap.Int("question type", questionType), zap.Int("attempt", attempt))
				return
			}
			expressionKey := strings.Join(question.Expression, " ")

			if _, exists := uniqueExpressions[expressionKey]; !exists {
				uniqueExpressions[expressionKey] = struct{}{}
				questionID := fmt.Sprintf("%s_%d", questionIdPrefix, len(*questions))
				question.ID = &questionID
				*questions = append(*questions, &models.GameQuestion{
					Question: question,
				})
				break
			}
		}
		if question == nil {
			zlog.Warn(context.Background(), "could not generate unique question", zap.Int("question type", questionType), zap.Int("max attempts", MAX_ATTEMPTS_TO_GENERATE_QUESTION))
		}
	}
}

func getQuestionByType(questionType, rating int) *models.Question {
	switch questionType {
	case TYPE_MOD:
		return questionsGenerator.CreateModQuestion(rating)
	case TYPE_ROOT:
		return questionsGenerator.CreateRootQuestion(rating)
	case TYPE_EXPONENT:
		return questionsGenerator.CreateExponentQuestion(rating)
	case TYPE_HCF:
		return questionsGenerator.CreateHCFQuestion(rating)
	case TYPE_LCM:
		return questionsGenerator.CreateLCMQuestion(rating)
	case TYPE_PRIME_FACTORIZATION:
		return questionsGenerator.CreatePrimeFactorizationQuestion(rating)
	case TYPE_SUM_OF_SQUARES:
		return questionsGenerator.CreateSumOfSquaresQuestion(rating)
	case TYPE_MULTIPLE_OPERATORS:
		// TODO: REWRITE MULTIPLE OPERATORS QUESTION GENERATOR
		// RETURNING MOD QUESTION TEMPERORALY
		return questionsGenerator.CreateModQuestion(rating)
	default:
		return nil
	}
}

func shuffleQuestions(questions []*models.GameQuestion) {
	n := len(questions)
	for i := n - 1; i > 0; i-- {
		j := rand.IntN(i + 1)
		questions[i], questions[j] = questions[j], questions[i]
	}
}
