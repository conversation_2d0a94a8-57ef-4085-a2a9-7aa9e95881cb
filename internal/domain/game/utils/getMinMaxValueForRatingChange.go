package gameutils

import "matiksOfficial/matiks-server-go/internal/models"

func GetMinMaxValueForRatingChange(gameType models.GameType) (int, int) {
	switch gameType {
	case models.GameTypeFlashAnzan:
		return -10, 10
	case models.GameTypeOnlineChallenge,
		models.GameTypePlayOnline,
		models.GameTypeSumdayShowdown,
		models.GameTypePlayWithFriend:
		return -100, 100
	case models.GameTypeFastestFinger:
		return -10, 10
	case models.GameTypeAbilityDuels:
		return -100, 100
	case models.GameTypeGroupPlay:
		return -5, 5
	default:
		return -25, 25
	}
}
