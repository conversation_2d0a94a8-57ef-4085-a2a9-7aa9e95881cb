package gameutils

import (
	"math"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func GetStatikCoinsEarnedForWinner(winnerEntry *models.LeaderBoardEntry) *int {
	if winnerEntry == nil {
		return utils.AllocPtr(0)
	}
	if winnerEntry.Correct != nil {
		return utils.AllocPtr(int(math.Min(10, float64(*winnerEntry.Correct))))
	}

	return utils.AllocPtr(2)
}

func GetStatikCoinsEarnedForLoser(loserEntry *models.LeaderBoardEntry) *int {
	if loserEntry == nil {
		return utils.AllocPtr(0)
	}
	if loserEntry.Correct != nil {
		return utils.AllocPtr(int(math.Min(5, float64(*loserEntry.Correct))))
	}

	return utils.AllocPtr(2)
}
