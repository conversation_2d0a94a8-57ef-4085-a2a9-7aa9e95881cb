package gameutils

import (
	"math"

	"matiksOfficial/matiks-server-go/internal/domain/questions"
	"matiksOfficial/matiks-server-go/internal/models"
)

// RatingChangeResult contains the calculated rating changes
type RatingChangeResult struct {
	WinnerChange int
	LoserChange  int
}

// FairUseCheck represents the result of fair use validation
type FairUseCheck struct {
	WinnerPassedFairUse bool
	LoserPassedFairUse  bool
	WinnerExpected      int
	WinnerActual        int
	LoserExpected       int
	LoserActual         int
}

// CalculateRatingChanges calculates the rating changes for both players with fair use check
func CalculateRatingChanges(
	winnerRating int,
	loserRating int,
	questions []*models.GameQuestion,
	leaderboard []*models.LeaderBoardEntry,
	isTie bool,
	hasSomeoneNotSubmittedAnyQuestion bool,
	bothNotSubmittedAnyQuestion bool,
	gameConfig *models.GameConfig,
	gameType models.GameType,
	minValueForLooserRatingChange int,
	maxValueForWinnerRatingChange int,
) *RatingChangeResult {
	// If no one submitted any questions, no rating changes
	if bothNotSubmittedAnyQuestion {
		return &RatingChangeResult{
			WinnerChange: 0,
			LoserChange:  0,
		}
	}

	// Perform fair use check
	fairUseCheck := performFairUseCheck(winnerRating, loserRating, questions, leaderboard, gameConfig)

	// Calculate expected scores based on ELO formula
	expectedWinnerScore := 1 / (1 + math.Pow(10, (float64(loserRating)-float64(winnerRating))/400))
	expectedLoserScore := 1 / (1 + math.Pow(10, (float64(winnerRating)-float64(loserRating))/400))

	// Calculate score difference factor
	maxScoreDifference := calculateMaxScoreDifference(questions)
	scoreDifference := math.Abs(float64(*leaderboard[0].TotalPoints - *leaderboard[1].TotalPoints))
	normalizedScoreDifference := math.Min(math.Max(scoreDifference/math.Max(float64(maxScoreDifference), 1), 0), 1)
	adjustmentFactor := 0.2 + 1.6*normalizedScoreDifference

	// Get K-factor based on game config
	KFactor := GetKFactor(gameConfig)

	var winnerRatingChange, loserRatingChange int

	if !hasSomeoneNotSubmittedAnyQuestion {
		if isTie {
			// For ties, apply fair use check to both players
			if fairUseCheck.WinnerPassedFairUse {
				winnerRatingChange = int(math.Max(1, float64(KFactor)*(0.5-expectedWinnerScore)*adjustmentFactor))
			} else {
				winnerRatingChange = 0 // No rating gain if fair use check failed
			}

			if fairUseCheck.LoserPassedFairUse {
				loserRatingChange = int(math.Min(-1, float64(KFactor)*(0.5-expectedLoserScore)*adjustmentFactor))
			} else {
				loserRatingChange = 0 // No rating loss if fair use check failed
			}
		} else {
			// For wins, only apply fair use check to the winner
			if fairUseCheck.WinnerPassedFairUse {
				winnerRatingChange = int(math.Max(1, float64(KFactor)*(1-expectedWinnerScore)*adjustmentFactor))
			} else {
				winnerRatingChange = 0 // No rating gain if winner didn't meet fair use criteria
			}

			// Loser always loses rating regardless of fair use (they lost the game)
			loserRatingChange = int(math.Min(-1, float64(KFactor)*(0-expectedLoserScore)*adjustmentFactor))
		}
	} else {
		winnerRatingChange = 0
		loserRatingChange = 0
	}

	// Apply limits to rating changes
	winnerRatingChange = int(math.Min(float64(winnerRatingChange), float64(maxValueForWinnerRatingChange)))
	loserRatingChange = int(math.Max(float64(loserRatingChange), float64(minValueForLooserRatingChange)))

	return &RatingChangeResult{
		WinnerChange: winnerRatingChange,
		LoserChange:  loserRatingChange,
	}
}

// performFairUseCheck checks if players solved at least the expected number of questions
func performFairUseCheck(
	winnerRating int,
	loserRating int,
	gameQuestions []*models.GameQuestion,
	leaderboard []*models.LeaderBoardEntry,
	gameConfig *models.GameConfig,
) *FairUseCheck {
	if gameConfig == nil || gameConfig.TimeLimit == nil {
		return &FairUseCheck{
			WinnerPassedFairUse: true,
			LoserPassedFairUse:  true,
		}
	}

	// Calculate expected questions solved for each player
	winnerExpected := calculateExpectedQuestionsSolved(winnerRating, gameQuestions, *gameConfig.TimeLimit)
	loserExpected := calculateExpectedQuestionsSolved(loserRating, gameQuestions, *gameConfig.TimeLimit)

	// Get actual questions solved (correct answers)
	winnerActual := 0
	loserActual := 0
	if len(leaderboard) >= 2 {
		if leaderboard[0].Correct != nil {
			winnerActual = *leaderboard[0].Correct
		}
		if leaderboard[1].Correct != nil {
			loserActual = *leaderboard[1].Correct
		}
	}

	// Check if players met the expected performance
	winnerPassed := winnerActual >= winnerExpected
	loserPassed := loserActual >= loserExpected

	// only check for online duels and online challenges
	if gameConfig.GameType != models.GameTypePlayOnline && gameConfig.GameType != models.GameTypeOnlineChallenge {
		winnerPassed = true
		loserPassed = true
	}

	return &FairUseCheck{
		WinnerPassedFairUse: winnerPassed,
		LoserPassedFairUse:  loserPassed,
		WinnerExpected:      winnerExpected,
		WinnerActual:        winnerActual,
		LoserExpected:       loserExpected,
		LoserActual:         loserActual,
	}
}

// calculateExpectedQuestionsSolved calculates how many questions a user should solve based on their rating
func calculateExpectedQuestionsSolved(userRating int, gameQuestions []*models.GameQuestion, timeLimitSeconds int) int {
	if len(gameQuestions) == 0 || timeLimitSeconds <= 0 {
		return 0
	}

	timeSpent := 0
	expectedQuestionsCount := 0
	for _, question := range gameQuestions {
		if question.Question != nil && question.Question.Rating != nil {
			expectedTime := questions.GetExpectedTimeToSolveQuestion(userRating, *question.Question.Rating, question.Question.PresetIdentifier)
			timeSpent += int(expectedTime.Milliseconds())
			if timeSpent <= timeLimitSeconds*1000 {
				expectedQuestionsCount++
			} else {
				break
			}
		}
	}

	// Return 80% of ceiling of expectedQuestionsCount
	return int(math.Ceil(float64(expectedQuestionsCount) * 0.8))
}

func calculateMaxScoreDifference(questions []*models.GameQuestion) int {
	maxScoreDifference := 0
	for _, questionObj := range questions {
		if questionObj.Question.Rating != nil && len(questionObj.Submissions) > 0 {
			maxScoreDifference += 4
		}
	}
	return maxScoreDifference
}

func CalculateHighestRating(user *models.User, change int) int {
	return int(math.Max(float64(user.Stats.Ngp), float64(*user.Rating+change)))
}

// GetFairUseCheckResult returns the fair use check result for debugging/logging purposes
func GetFairUseCheckResult(
	winnerRating int,
	loserRating int,
	gameQuestions []*models.GameQuestion,
	leaderboard []*models.LeaderBoardEntry,
	gameConfig *models.GameConfig,
) *FairUseCheck {
	return performFairUseCheck(winnerRating, loserRating, gameQuestions, leaderboard, gameConfig)
}
