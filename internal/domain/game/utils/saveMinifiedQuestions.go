package gameutils

import (
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"
)

func SaveMinifiedQuestions(game *models.Game) {
	prevSubmissionTimes := make(map[string]int)

	for i, question := range game.Questions {
		if len(question.Submissions) > 0 {
			minifiedQuestion := fmt.Sprintf("%s__%s",
				question.Question.Expression,
				question.Question.PresetIdentifier,
			)

			for _, submission := range question.Submissions {
				userID := submission.UserID.Hex()
				timeTaken := *submission.TimeTaken

				if i > 0 {
					if prevTime, exists := prevSubmissionTimes[userID]; exists {
						timeTaken -= prevTime
					}
				}

				prevSubmissionTimes[userID] = *submission.TimeTaken

				minifiedQuestion += fmt.Sprintf("$$%s_%d",
					userID,
					timeTaken,
				)
			}

			game.MinifiedQuestions = append(game.MinifiedQuestions, &minifiedQuestion)
		}
	}
	game.EncryptedQuestions = nil
	game.Questions = nil
}
