package gameutils

import (
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func UpdateGameLeaderboard(game *models.Game, userID primitive.ObjectID, isCorrect bool, changeInMarks float64) {
	var leaderboardEntry *models.LeaderBoardEntry
	for i, entry := range game.LeaderBoard {
		if entry.UserID != nil && *entry.UserID == userID {
			leaderboardEntry = game.LeaderBoard[i]
			break
		}
	}

	if leaderboardEntry == nil {
		leaderboardEntry = &models.LeaderBoardEntry{
			UserID:      &userID,
			TotalPoints: utils.AllocPtr(0.0),
			Correct:     utils.AllocPtr(0),
			Incorrect:   utils.AllocPtr(0),
		}
		game.LeaderBoard = append(game.LeaderBoard, leaderboardEntry)
	}

	*leaderboardEntry.TotalPoints += changeInMarks
	if isCorrect {
		*leaderboardEntry.Correct++
	} else {
		*leaderboardEntry.Incorrect++
	}

	game.LeaderBoard = UpdateLeaderboardRanks(game)
}
