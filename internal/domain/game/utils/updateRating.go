package gameutils

import (
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func UpdateBadge(user *models.User, prevRating, updatedRating int) {
	if *user.IsGuest {
		return
	}

	if updatedRating < prevRating {
		switch {
		case updatedRating < 974 && prevRating >= 974:
			user.Badge = utils.AllocPtr(models.BadgeTypeRookie)
		case updatedRating < 1174 && prevRating >= 1174:
			user.Badge = utils.AllocPtr(models.BadgeTypeNovice)
		case updatedRating < 1574 && prevRating >= 1574:
			user.Badge = utils.AllocPtr(models.BadgeTypeAmateur)
		case updatedRating < 1974 && prevRating >= 1974:
			user.Badge = utils.AllocPtr(models.BadgeTypeExpert)
		case updatedRating < 2474 && prevRating >= 2474:
			user.Badge = utils.AllocPtr(models.BadgeTypeCandidateMaster)
		case updatedRating < 2974 && prevRating >= 2974:
			user.Badge = utils.AllocPtr(models.BadgeTypeMaster)
		case updatedRating < 3474 && prevRating >= 3474:
			user.Badge = utils.AllocPtr(models.BadgeTypeGrandmaster)
		case updatedRating < 3974 && prevRating >= 3974:
			user.Badge = utils.AllocPtr(models.BadgeTypeLegendaryGrandmaster)
		}
	} else {
		switch {
		case updatedRating >= 4000 && prevRating < 4000:
			user.Badge = utils.AllocPtr(models.BadgeTypeGoat)
		case updatedRating >= 3500 && prevRating < 3500:
			user.Badge = utils.AllocPtr(models.BadgeTypeLegendaryGrandmaster)
		case updatedRating >= 3000 && prevRating < 3000:
			user.Badge = utils.AllocPtr(models.BadgeTypeGrandmaster)
		case updatedRating >= 2500 && prevRating < 2500:
			user.Badge = utils.AllocPtr(models.BadgeTypeMaster)
		case updatedRating >= 2000 && prevRating < 2000:
			user.Badge = utils.AllocPtr(models.BadgeTypeCandidateMaster)
		case updatedRating >= 1600 && prevRating < 1600:
			user.Badge = utils.AllocPtr(models.BadgeTypeExpert)
		case updatedRating >= 1200 && prevRating < 1200:
			user.Badge = utils.AllocPtr(models.BadgeTypeAmateur)
		case updatedRating >= 1000 && prevRating < 1000:
			user.Badge = utils.AllocPtr(models.BadgeTypeNovice)
		}
	}
}
