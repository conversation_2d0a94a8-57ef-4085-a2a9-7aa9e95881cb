package gameutils

import (
	"matiksOfficial/matiks-server-go/internal/models"
)

func UpdateUserStats(user, opponent *models.User, highestRating int, game *models.UserGame) {
	if user.Stats == nil {
		user.Stats = &models.UserStats{}
	}

	user.Stats.Ngp++

	if highestRating > user.Stats.Hr {
		user.Stats.Hr = highestRating
	}

	if opponent != nil && opponent.IsBot != nil && *opponent.IsBot {
		if user.Stats.Last10BotGames == nil {
			user.Stats.Last10BotGames = []*models.UserGame{}
		}

		user.Stats.Last10BotGames = append([]*models.UserGame{game}, user.Stats.Last10BotGames...)

		if len(user.Stats.Last10BotGames) > 10 {
			user.Stats.Last10BotGames = user.Stats.Last10BotGames[:10]
		}
	}
}
