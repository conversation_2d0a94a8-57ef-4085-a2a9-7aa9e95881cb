package gameutils

import (
	"fmt"
	"math"

	"matiksOfficial/matiks-server-go/internal/domain/utils"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/models"
)

const abilityRatingMultiplier = 0.7

func GetUserDefaultRating(user *models.User) *models.UserRating {
	return &models.UserRating{
		GlobalRating:       utils.AllocPtr(getUserDefaultGlobalRatingWithFallback(user)),
		FlashAnzanRating:   utils.AllocPtr(getUserFlashAnzanRatingWithFallback(user)),
		AbilityDuelsRating: utils.AllocPtr(getUserDefaultAbilityRatingWithFallback(user)),
		PuzzleRating:       utils.AllocPtr(getUserDefaultPuzzleRatingWithFallback(user)),
	}
}

func GetPlayerRatingByGameType(user *models.User, gameType interface{}) int {
	switch gameType {
	case models.PuzzleGameTypeCrossMathPuzzleDuel:
		return getUserDefaultPuzzleRatingWithFallback(user)
	case models.GameTypeFlashAnzan:
		return getUserFlashAnzanRatingWithFallback(user)
	case models.GameTypeAbilityDuels:
		return getUserDefaultAbilityRatingWithFallback(user)
	default:
		return getUserDefaultGlobalRatingWithFallback(user)
	}
}

func GetRatingFieldByGameType(gameType interface{}) string {
	gameTypeStr := fmt.Sprintf("%v", gameType)
	if gameTypeStr == models.PuzzleGameTypeCrossMathPuzzleDuel.String() {
		return constants.PuzzleRatingField
	}
	switch gameType {
	case models.GameTypeFlashAnzan:
		return constants.MemoryRatingField
	case models.GameTypeAbilityDuels:
		return constants.AbilityRatingField
	default:
		return constants.GlobalRatingField
	}
}

func getUserDefaultGlobalRatingWithFallback(user *models.User) int {
	if user != nil && user.RatingV2 != nil && user.RatingV2.GlobalRating != nil {
		return *user.RatingV2.GlobalRating
	}
	if user != nil && user.Rating != nil {
		return *user.Rating
	}
	return constants.DefaultRating
}

func getUserFlashAnzanRatingWithFallback(user *models.User) int {
	if user != nil && user.RatingV2 != nil && user.RatingV2.FlashAnzanRating != nil {
		return *user.RatingV2.FlashAnzanRating
	}
	return constants.DefaultFlashAnzanRating
}

func getUserDefaultAbilityRatingWithFallback(user *models.User) int {
	if user != nil && user.RatingV2 != nil && user.RatingV2.AbilityDuelsRating != nil {
		return *user.RatingV2.AbilityDuelsRating
	}
	return int(math.Max(1000, float64(getUserDefaultGlobalRatingWithFallback(user))*abilityRatingMultiplier))
}

func getUserDefaultPuzzleRatingWithFallback(user *models.User) int {
	if user != nil && user.RatingV2 != nil && user.RatingV2.PuzzleRating != nil {
		return *user.RatingV2.PuzzleRating
	}
	return constants.DefaultPuzzleRating
}
