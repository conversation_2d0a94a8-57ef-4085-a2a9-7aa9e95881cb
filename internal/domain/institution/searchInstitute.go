package institution

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *service) SearchInstitute(ctx context.Context, query string, limit *int) ([]*models.Institution, error) {
	if limit == nil {
		limit = utils.AllocPtr(10)
	}

	if query == "" {
		return nil, nil
	}

	if len(query) < 3 {
		return nil, nil
	}

	filter := models.InstitutionFilter{
		Query: query,
		Limit: int64(*limit),
	}

	return s.instituteRepo.Search(ctx, filter)
}
