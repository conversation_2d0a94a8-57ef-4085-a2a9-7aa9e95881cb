package institution

import (
	"context"

	"go.uber.org/fx"
	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

type service struct {
	instituteRepo repository.InstitutionRepository
}

func NewInstitutionService(lc fx.Lifecycle, repo repository.InstitutionRepository) domain.InstitutionStore {
	s := &service{
		instituteRepo: repo,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting Institution service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down Institution service")
			return nil
		},
	})

	return s
}
