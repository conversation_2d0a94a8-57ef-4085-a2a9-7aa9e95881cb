package league

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) CreateLeague(ctx context.Context, input models.CreateLeagueInput) (*models.League, error) {
	var newRegistrationForm *models.RegistrationForm
	if input.RegistrationFormInput != nil {
		registrationForm, err := createRegistrationForm(*input.RegistrationFormInput)
		if err != nil {
			return nil, err
		}
		newRegistrationForm = registrationForm
	}

	var leagueDetails *models.LeagueDetails

	if input.Details != nil {
		leagueDetails = getLeagueDetailsFromInput(*input.Details)
	}
	chatRoomID := primitive.NewObjectID()
	leagueID := primitive.NewObjectID()

	members := make([]*primitive.ObjectID, 0)

	err := s.messageGroupRepo.Create(ctx, &models.MessageGroup{
		ID:            chatRoomID,
		GroupName:     &input.Name,
		GroupType:     models.GroupTypeLeague,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
		Alias:         fmt.Sprintf("%s_%s", chatRoomID, leagueID),
		Members:       members,
		DeepLinkRoute: utils.AllocPtr(fmt.Sprintf("%s/league/%s", constants.MatiksClientHost, leagueID.Hex())),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to Create Chat For Leagues")
	}

	leagueHostDetails := &models.HostDetails{
		Name: input.HostedBy,
		Logo: &input.HostLogo,
	}

	league := &models.League{
		ID:                leagueID,
		Name:              input.Name,
		HostedBy:          input.HostedBy,
		RegistrationStart: input.RegistrationStart,
		RegistrationEnd:   input.RegistrationEnd,
		LeagueStart:       input.LeagueStart,
		LeagueEnd:         input.LeagueEnd,
		RegistrationForm:  newRegistrationForm,
		Details:           leagueDetails,
		ChatRoomId:        chatRoomID,
		HostedByV2:        leagueHostDetails,
	}

	league, err = s.leaguesRepository.CreateLeague(ctx, league)

	return league, err
}

func createRegistrationForm(input models.RegistrationFormInput) (*models.RegistrationForm, error) {
	now := time.Now()
	form := &models.RegistrationForm{
		ID:        primitive.NewObjectID(),
		Fields:    make([]*models.FormField, len(input.Fields)),
		CreatedAt: now,
		UpdatedAt: now,
	}

	for i, field := range input.Fields {
		validation := &models.FieldValidation{
			Regex:                  &field.Validation.Regex,
			Min:                    &field.Validation.Min,
			Max:                    &field.Validation.Max,
			NeedManualVerification: &field.Validation.NeedManualVerification,
			EmailSuffix:            field.Validation.EmailSuffix,
			EmailSuffixes:          field.Validation.EmailSuffixes,
		}

		form.Fields[i] = &models.FormField{
			ID:         primitive.NewObjectID(),
			Name:       field.Name,
			Type:       field.Type,
			Label:      field.Label,
			Required:   field.Required,
			Options:    field.Options,
			Validation: validation,
		}
	}

	return form, nil
}

func getLeagueDetailsFromInput(input models.LeagueDetailsInput) *models.LeagueDetails {
	return &models.LeagueDetails{
		About:        input.About,
		Instructions: input.Instructions,
		Requirements: input.Requirements,
		Awards:       input.Awards,
	}
}
