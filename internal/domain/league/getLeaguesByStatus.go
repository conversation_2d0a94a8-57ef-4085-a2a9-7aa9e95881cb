package league

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *service) GetLeaguesByStatus(ctx context.Context, statuses []models.LeagueStatus, page, pageSize *int, sortDirection *string) (*models.PaginatedLeagues, error) {
	if page == nil {
		page = utils.AllocPtr(1)
	}

	if pageSize == nil {
		pageSize = utils.AllocPtr(50)
	}

	if sortDirection == nil {
		sortDirection = utils.AllocPtr("desc")
	}

	return s.leaguesRepository.GetLeaguesByStatus(ctx, statuses, *page, *pageSize, *sortDirection)
}
