package league

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) GetLeague(ctx context.Context, id primitive.ObjectID) (*models.League, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	league, err := s.leaguesRepository.GetLeagueByID(ctx, id)
	if err != nil {
		return nil, err
	}

	registrationCount := s.leaguesParticipantRepository.GetParticipantsCountInLeague(ctx, id)

	league.RegistrationCount = utils.AllocPtr(registrationCount)

	currentUserParticipation, err := s.leaguesParticipantRepository.GetParticipantByUserAndLeagueID(ctx, userID, id)
	league.CurrentUserParticipation = currentUserParticipation
	if err != nil {
		return league, nil
	}

	currentUserResult, err := s.leaguesRepository.GetUserLeagueResult(ctx, id, userID)
	league.CurrentUserResult = currentUserResult

	if err != nil {
		return league, nil
	}

	return league, nil
}
