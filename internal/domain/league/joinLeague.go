package league

import (
	"context"
	"fmt"
	"strings"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func isEmailField(fieldName string) bool {
	return strings.Contains(strings.To<PERSON><PERSON>er(fieldName), "email")
}

func (s *service) JoinLeague(ctx context.Context, joinLeagueInput *models.JoinLeagueInput) (bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	if joinLeagueInput == nil {
		return false, nil
	}

	for _, field := range joinLeagueInput.FormData {
		if field != nil && isEmailField(field.Name) {
			for _, value := range field.Values {
				if !utils.IsValidEmail(value) {
					return false, fmt.Errorf("Invalid email format: %s", value)
				}
				isRegistered, err := s.leaguesRepository.IsEmailRegisteredInLeague(ctx, joinLeagueInput.LeagueID, value)
				if err != nil {
					return false, err
				}
				if isRegistered {
					return false, fmt.Errorf("email %s is already registered in this league", value)
				}
			}
		}
	}

	registrationData := make([]*models.RegistrationFieldData, len(joinLeagueInput.FormData))
	for i, field := range joinLeagueInput.FormData {
		registrationData[i] = &models.RegistrationFieldData{
			Name:   field.Name,
			Values: field.Values,
		}
	}

	league, err := s.leaguesRepository.GetLeagueByID(ctx, joinLeagueInput.LeagueID)
	if err != nil {
		return false, err
	}

	err = s.messageGroupRepo.AddParticipant(ctx, league.ChatRoomId, &userID)
	if err != nil {
		return false, fmt.Errorf("Failed to add Participant in chat")
	}

	err = s.leaguesRepository.RegisterUserForLeague(ctx, joinLeagueInput.LeagueID, userID, registrationData)
	if err != nil {
		err = s.messageGroupRepo.RemoveParticipant(ctx, league.ChatRoomId, userID)
		if err != nil {
			return false, err
		}
		return false, err
	}

	return true, nil
}
