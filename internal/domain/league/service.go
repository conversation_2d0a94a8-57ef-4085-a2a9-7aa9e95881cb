package league

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type service struct {
	leaguesRepository            repository.LeagueRepository
	leaguesParticipantRepository repository.LeagueParticipantRepository
	messageGroupRepo             repository.MessageGroupRepository
}

func NewService(
	lc fx.Lifecycle, repositoryFactory *repository.RepositoryFactory,
) domain.LeagueStore {
	s := &service{
		leaguesRepository:            repositoryFactory.LeaguesRepository,
		leaguesParticipantRepository: repositoryFactory.LeagueParticipantRepository,
		messageGroupRepo:             repositoryFactory.MessageGroupRepository,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting league service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down league service")
			return nil
		},
	})

	return s
}
