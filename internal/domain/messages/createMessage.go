package messages

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

const MaxMessageTextLength = 250

var shouldSendPushNotificationForGroupType = map[models.GroupType]bool{
	models.GroupTypeLeague:     false,
	models.GroupTypeGame:       false,
	models.GroupTypeShowdown:   false,
	models.GroupTypeCommunity:  false,
	models.GroupTypeIndividual: true,
}

func (s *service) CreateMessage(ctx context.Context, messageInput models.CreateMessageInput) (*models.Message, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	messageInput.Sender = userId
	group, err := s.messageGroupRepo.GetByID(ctx, messageInput.GroupID)
	if err != nil {
		zlog.Error(ctx, "Error retrieving group:", err)
		return nil, err
	}

	if group == nil {
		zlog.Error(ctx, "Group not found", fmt.Errorf("group not found"))
		return nil, fmt.Errorf("group not found")
	}

	if len(group.Members) == 0 {
		return nil, fmt.Errorf("no members found in the group")
	}

	if !CheckIfPersonIsMemberOfGroup(messageInput.Sender, group.Members) {
		zlog.Warn(ctx, "User is not a member of the group", zap.String("userID", messageInput.Sender.Hex()))
		return nil, fmt.Errorf("user is not a member of the group")
	}

	senderInfo, err := s.userRepo.GetByID(ctx, messageInput.Sender)
	if err != nil {
		zlog.Error(ctx, "Error retrieving user:", err)
		return nil, err
	}

	if senderInfo == nil {
		zlog.Error(ctx, "Sender info not found", fmt.Errorf("sender info not found"), zap.String("userID", messageInput.Sender.Hex()))
		return nil, fmt.Errorf("sender info not found")
	}
	var members []primitive.ObjectID
	for _, member := range group.Members {
		if member == nil {
			zlog.Warn(ctx, "Member is nil")
			continue
		}
		if *member == messageInput.Sender {
			continue
		}
		members = append(members, *member)
	}

	if len(messageInput.Content) > MaxMessageTextLength {
		return nil, fmt.Errorf("message is too long")
	}

	minifiedSenderInfo := &models.UserDetailsForMessage{
		ID:              senderInfo.ID,
		Name:            senderInfo.Name,
		Username:        senderInfo.Username,
		ProfileImageURL: senderInfo.ProfileImageURL,
		Rating:          senderInfo.Rating,
	}

	message := &models.Message{
		ID:         primitive.NewObjectID(),
		GroupID:    messageInput.GroupID,
		Content:    messageInput.Content,
		Sender:     messageInput.Sender,
		Attachment: messageInput.Attachment,
		CreatedAt:  time.Now(),
	}

	messageId, err := s.messagesRepo.CreateMessage(ctx, message)
	if err != nil {
		zlog.Error(ctx, "Error creating message:", err)
		return nil, err
	}

	err = s.messageGroupRepo.UpdateLastMessage(ctx, message.GroupID, message)
	if err != nil {
		zlog.Error(ctx, "Error updating last message:", err)
		return nil, err
	}

	message.ID = messageId

	message.SenderInfo = minifiedSenderInfo

	for _, member := range group.Members {
		if member == nil || *member == messageInput.Sender {
			continue
		}
		err = s.coreService.PublishUserEvent(ctx, *member, &models.PublishMessagePayload{Message: *message})
		if err != nil {
			return nil, err
		}
	}

	deepLinkUrl := fmt.Sprintf("%s/chat?id=%s", constants.MatiksClientHost, message.GroupID.Hex())

	if group.DeepLinkRoute != nil {
		deepLinkUrl = *group.DeepLinkRoute
	}

	notificationData := map[string]string{
		"type":     "FRIEND_REQUEST",
		"senderID": senderInfo.ID.Hex(),
		"username": senderInfo.Username,
		"url":      deepLinkUrl,
	}

	var name string
	if group.GroupType == models.GroupTypeIndividual {
		if senderInfo.Name != nil {
			name = *senderInfo.Name
		} else {
			name = senderInfo.Username
		}
	} else {
		if group.GroupName != nil {
			name = *group.GroupName
		} else {
			name = group.ID.Hex()
		}
	}

	shouldSendPushNotification := shouldSendPushNotificationForGroupType[group.GroupType]

	title, body := getNotificationTitleAndBody(name, message.Content)

	if !shouldSendPushNotification {
		return message, nil
	}

	err = s.notificationService.SendPushNotificationToUsers(
		ctx,
		body,
		title,
		notificationData,
		members,
	)
	if err != nil {
		zlog.Error(ctx, "error sending push notification", err)
	}

	return message, nil
}

func getNotificationTitleAndBody(name, messageContent string) (string, string) {
	if len(messageContent) == 0 {
		return fmt.Sprintf("New Message from %s", name), ""
	}
	return fmt.Sprintf("New Message from %s", name), messageContent[:min(len(messageContent), 20)] + "..."
}
