package messages

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MapUserToUserDetailsForMessage(user *models.User) *models.UserDetailsForMessage {
	return &models.UserDetailsForMessage{
		ID:              user.ID,
		Name:            user.Name,
		Username:        user.Username,
		ProfileImageURL: user.ProfileImageURL,
		Rating:          user.Rating,
	}
}

func (s *service) GetAllMessageGroup(ctx context.Context, input *models.GetAllMessageGroupsInput) (paginatedMessageGroups *models.PaginatedMessageGroups, err error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	pageSize := 50
	if input.PageSize != nil {
		pageSize = *input.PageSize
	}
	messageGroups, err := s.messageGroupRepo.GetIndividualUsers(ctx, userID, input.Page, pageSize)
	if err != nil {
		return nil, err
	}

	if len(messageGroups) == 0 {
		return nil, nil
	}

	// TODO : Need to optimise (very unoptimised code)

	userIds := []primitive.ObjectID{}
	userIdMap := make(map[primitive.ObjectID]int)
	isRead := true
	for i := range messageGroups {
		if (messageGroups[i]) == nil {
			continue
		}
		for _, memberId := range messageGroups[i].Members {
			if memberId == nil || *memberId == userID {
				continue
			}
			userIds = append(userIds, *memberId)
			userIdMap[*memberId] = i
		}
		if messageGroups[i].LastMessageRead == nil {
			messageGroups[i].LastMessageRead = []*models.MessageRead{}
			continue
		}

		for _, lastMessageRead := range messageGroups[i].LastMessageRead {
			if lastMessageRead == nil {
				continue
			}
			if lastMessageRead.UserID == userID && lastMessageRead.LastMessageRead != messageGroups[i].LastMessage.ID {
				isRead = false
			}
		}
	}

	if len(userIds) != 0 {
		users, err := s.userRepo.GetUsersByIDs(ctx, userIds)
		if err != nil {
			return nil, err
		}
		for _, user := range users {
			if user != nil {
				userDetailsForMessage := MapUserToUserDetailsForMessage(user)
				messageGroups[userIdMap[user.ID]].UserInfoIfIndividual = userDetailsForMessage
			}
		}
	}

	paginatedMessageGroups = &models.PaginatedMessageGroups{
		Groups:   messageGroups,
		NextPage: utils.AllocPtr(input.Page + 1),
		HasMore:  len(messageGroups) == pageSize,
		IsRead:   isRead,
	}

	return paginatedMessageGroups, nil
}
