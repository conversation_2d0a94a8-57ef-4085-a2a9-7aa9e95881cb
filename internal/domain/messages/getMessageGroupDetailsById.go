package messages

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) GetMessageGroupDetailsByID(ctx context.Context, groupID primitive.ObjectID) (*models.MessageGroup, error) {
	group, err := s.messageGroupRepo.GetByID(ctx, groupID)
	if err != nil {
		return nil, err
	}
	if group == nil {
		return nil, fmt.Errorf("message group not found")
	}
	return group, nil
}
