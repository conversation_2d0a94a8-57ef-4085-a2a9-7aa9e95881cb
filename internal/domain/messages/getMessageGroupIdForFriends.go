package messages

import (
	"context"
	"errors"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) GetMessageGroupIdForFriends(ctx context.Context, friendID primitive.ObjectID) (primitive.ObjectID, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return primitive.NilObjectID, err
	}
	alias := userID.Hex() + "-" + friendID.Hex()
	if friendID.Hex() > userID.Hex() {
		alias = friendID.Hex() + "-" + userID.Hex()
	}

	messageGroup, err := s.messageGroupRepo.GetByAlias(ctx, alias)
	if err != nil {
		return primitive.NilObjectID, err
	}
	if messageGroup == nil {
		isFriends, _, err := s.friendsRepo.CheckIfAlreadyFriends(ctx, userID, friendID)
		if err != nil {
			return primitive.NilObjectID, err
		}
		if !isFriends {
			return primitive.NilObjectID, errors.New("users are not friends")
		}
		messageGroup = &models.MessageGroup{
			ID:    primitive.NewObjectID(),
			Alias: alias,
			Members: []*primitive.ObjectID{
				&userID,
				&friendID,
			},
			GroupType: models.GroupTypeIndividual,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		if err = s.messageGroupRepo.Create(ctx, messageGroup); err != nil {
			return primitive.NilObjectID, err
		}
		return messageGroup.ID, nil
	}

	return messageGroup.ID, nil
}
