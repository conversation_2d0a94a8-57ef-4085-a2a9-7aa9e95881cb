package messages

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) GetMessagesByGroupID(ctx context.Context, groupID primitive.ObjectID, lastMessageId *primitive.ObjectID, pageSize *int, defaultSortDirection *models.SortDirection) (paginatedMessages *models.PaginatedMessage, err error) {
	limit := int64(50)
	if pageSize != nil {
		limit = int64(*pageSize)
		// limit = 50
	}
	sortValue := -1
	if defaultSortDirection != nil {
		sortValue, err = defaultSortDirection.GetValue()
		if err != nil {
			return nil, err
		}
	}
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	group, err := s.messageGroupRepo.GetByID(ctx, groupID)
	if err != nil {
		return nil, err
	}
	if group == nil || !CheckIfPersonIsMemberOfGroup(userID, group.Members) {
		return nil, nil
	}
	messages, err := s.messagesRepo.GetMessagesByGroupID(ctx, groupID, lastMessageId, limit, sortValue)
	if err != nil {
		return nil, err
	}
	var nextLastMessageId *primitive.ObjectID
	if len(messages) > 0 {
		nextLastMessageId = &messages[len(messages)-1].ID
	}

	paginatedMessages = &models.PaginatedMessage{
		Messages:      messages,
		LastMessageID: nextLastMessageId,
		HasMore:       len(messages) == int(limit),
	}
	return paginatedMessages, nil
}
