package messages

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/infrastructure/websocket"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type service struct {
	messageGroupRepo    repository.MessageGroupRepository
	messagesRepo        repository.MessagesRepository
	ws                  websocket.Websocket
	userService         domain.UserStore
	coreService         domain.CoreLogicStore
	userRepo            repository.UserRepository
	notificationService domain.NotificationStore
	friendsRepo         repository.FriendsAndFollowersRepository
}

func NewMessageService(
	lc fx.Lifecycle, repositoryFactory *repository.RepositoryFactory,
	ws websocket.Websocket, userService domain.UserStore,
	notificationService domain.NotificationStore,
	coreService domain.CoreLogicStore,
) domain.MessageStore {
	s := &service{
		messageGroupRepo:    repositoryFactory.MessageGroupRepository,
		messagesRepo:        repositoryFactory.MessagesRepository,
		ws:                  ws,
		userService:         userService,
		userRepo:            repositoryFactory.UserRepository,
		notificationService: notificationService,
		friendsRepo:         repositoryFactory.FriendsAndFollowersRepository,
		coreService:         coreService,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting message service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down message service")
			return nil
		},
	})

	return s
}
