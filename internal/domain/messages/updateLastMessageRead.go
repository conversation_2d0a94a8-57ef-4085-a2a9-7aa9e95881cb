package messages

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) UpdateLastMessageRead(ctx context.Context, groupID, lastMessageRead primitive.ObjectID) (bool, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Error getting user from context: ", err)
		return false, err
	}
	group, err := s.messageGroupRepo.GetByID(ctx, groupID)
	if err != nil {
		zlog.Error(ctx, "Error getting group from repository: ", err)
		return false, err
	}
	if group == nil || !CheckIfPersonIsMemberOfGroup(userId, group.Members) {
		zlog.Error(ctx, "User is not a member of the group", nil)
		return false, nil
	}

	isUserAvailable := false
	if group.LastMessageRead == nil {
		group.LastMessageRead = []*models.MessageRead{}
	}
	for i, messageRead := range group.LastMessageRead {
		if messageRead.UserID == userId {
			messageRead.LastMessageRead = lastMessageRead
			group.LastMessageRead[i] = messageRead
			isUserAvailable = true
			break
		}
	}
	if !isUserAvailable {
		group.LastMessageRead = append(group.LastMessageRead, &models.MessageRead{
			UserID:          userId,
			LastMessageRead: lastMessageRead,
		})
	}
	err = s.messageGroupRepo.UpdateLastMessageRead(ctx, group)
	if err != nil {
		zlog.Error(ctx, "Error updating last message read: ", err)
		return false, err
	}
	return true, nil
}
