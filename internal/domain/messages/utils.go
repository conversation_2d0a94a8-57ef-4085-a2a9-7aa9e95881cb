package messages

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) PublishMessageEvent(ctx context.Context, message models.Message) error {
	channel := fmt.Sprintf("%s_%s", constants.SubscriptionPrefixEnum.MESSAGE_EVENT, message.GroupID.Hex())
	err := s.ws.Publish(ctx, channel, message)
	if err != nil {
		return err
	}
	return nil
}

func CheckIfPersonIsMemberOfGroup(userID primitive.ObjectID, members []*primitive.ObjectID) bool {
	if len(members) == 0 {
		return false
	}
	for _, member := range members {
		if member != nil && *member == userID {
			return true
		}
	}
	return false
}
