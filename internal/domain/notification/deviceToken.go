package notification

import (
	"context"

	slicesutils "matiksOfficial/matiks-server-go/utils/slicesustils"

	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func (s *service) RegisterDeviceToken(ctx context.Context, pushNotificationToken string, deviceID, platform *string) (*models.DeviceTokenRegistrationResponse, error) {
	zlog.Info(ctx, "Registering device token", zap.String("pushNotificationToken", pushNotificationToken))

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Warn(ctx, "Failed to get user ID from context", zap.Error(err))
		return &models.DeviceTokenRegistrationResponse{
			Success: false,
			Message: "User not authenticated",
		}, nil
	}

	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil || user == nil {
		zlog.Error(ctx, "failed to get user by id", err)
		return &models.DeviceTokenRegistrationResponse{
			Success: false,
			Message: "Failed to get user by id",
		}, nil
	}
	if user.Additional == nil || len(user.Additional.PushNotificationTokens) == 0 {
		pNTs := make([]models.PushNotificationToken, 0, 1)
		pNT := models.PushNotificationToken{
			PNT:      utils.AllocPtr(pushNotificationToken),
			DeviceID: deviceID,
			Platform: platform,
		}
		pNTs = append(pNTs, pNT)
		user.Additional = &models.UserAdditional{
			PushNotificationTokens: pNTs,
		}
	} else {
		set := false
		for i, pNT := range user.Additional.PushNotificationTokens {
			if pNT.DeviceID == deviceID {
				set = true
				user.Additional.PushNotificationTokens[i].PNT = utils.AllocPtr(pushNotificationToken)
				user.Additional.PushNotificationTokens[i].Platform = platform
			}
		}
		if !set {
			user.Additional.PushNotificationTokens = append(user.Additional.PushNotificationTokens, models.PushNotificationToken{
				PNT:      utils.AllocPtr(pushNotificationToken),
				DeviceID: deviceID,
				Platform: platform,
			})
		}
	}

	user.Additional.PushNotificationTokens = slicesutils.Filter(user.Additional.PushNotificationTokens, func(pNT models.PushNotificationToken) bool {
		return pNT.PNT != nil && *pNT.PNT != ""
	})

	user.Additional.PushNotificationTokens = slicesutils.UniqBy(user.Additional.PushNotificationTokens, func(pNT models.PushNotificationToken) string {
		return *pNT.PNT
	})

	update := bson.M{"$set": bson.M{"additional": user.Additional}}
	err = s.userRepo.UpdateOne(ctx, bson.M{"_id": userID}, update)
	if err != nil {
		zlog.Error(ctx, "failed to update user", err)
		return &models.DeviceTokenRegistrationResponse{
			Success: false,
			Message: "Failed to update user",
		}, nil
	}

	return &models.DeviceTokenRegistrationResponse{
		Success: true,
		Message: "Token registered successfully",
	}, nil
}

func (s *service) UnregisterDeviceToken(ctx context.Context, pushNotificationToken, deviceID *string) (*models.DeviceTokenRegistrationResponse, error) {
	zlog.Info(ctx, "Unregistering device token", zap.String("pushNotificationToken", *pushNotificationToken))

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Warn(ctx, "Failed to get user ID from context", zap.Error(err))
		return &models.DeviceTokenRegistrationResponse{
			Success: false,
			Message: "User not authenticated",
		}, nil
	}

	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil || user == nil {
		zlog.Error(ctx, "failed to get user by id", err)
		return &models.DeviceTokenRegistrationResponse{
			Success: false,
			Message: "Failed to get user by id",
		}, nil
	}

	if user.Additional == nil || len(user.Additional.PushNotificationTokens) == 0 {
		return &models.DeviceTokenRegistrationResponse{
			Success: false,
			Message: "Token does not exists",
		}, nil
	}

	pNTs := make([]models.PushNotificationToken, 0, len(user.Additional.PushNotificationTokens))
	for _, pNT := range user.Additional.PushNotificationTokens {
		if *pNT.DeviceID == *deviceID && *pNT.PNT == *pushNotificationToken {
			continue
		}
		pNTs = append(pNTs, pNT)
	}
	user.Additional.PushNotificationTokens = pNTs

	update := bson.M{"$set": bson.M{"additional": user.Additional}}
	err = s.userRepo.UpdateOne(ctx, bson.M{"_id": userID}, update)
	if err != nil {
		zlog.Error(ctx, "Failed to update user", err)
		return &models.DeviceTokenRegistrationResponse{
			Success: false,
			Message: "Failed to update user",
		}, nil
	}

	return &models.DeviceTokenRegistrationResponse{
		Success: true,
		Message: "Token unregistered successfully",
	}, nil
}
