package notification

import (
	"context"
	"fmt"
	"net/smtp"
)

func (s *service) SendEmailNotification(ctx context.Context, subject, body string, to []string) error {
	from := s.cfg.SMTPFromEmail
	password := s.cfg.SMTPPassword
	smtpHost := s.cfg.SMTPHost
	smtpPort := fmt.Sprintf("%d", s.cfg.SMTPPort)

	auth := smtp.PlainAuth("", from, password, smtpHost)

	// Compose the email
	mime := "MIME-version: 1.0;\nContent-Type: text/html; charset=\"UTF-8\";\n\n"
	message := fmt.Sprintf("From: %s\nSubject: %s\n%s\n%s", from, subject, mime, body)

	// Sending email
	err := smtp.SendMail(smtpHost+":"+smtpPort, auth, from, to, []byte(message))
	if err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}
	return nil
}
