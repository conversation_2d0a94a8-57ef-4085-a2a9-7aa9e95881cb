package notification

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) SendInAppPopup(ctx context.Context, message string, userIDs []primitive.ObjectID) error {
	for _, userID := range userIDs {
		_, err := s.userRepo.GetByID(ctx, userID)
		if err != nil {
			return fmt.Errorf("failed to get user: %w", err)
		}
		// TODO: Send in-app notification
	}
	return nil
}
