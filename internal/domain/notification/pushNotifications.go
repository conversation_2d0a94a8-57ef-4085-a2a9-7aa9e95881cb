package notification

import (
	"context"
	"encoding/base64"
	"fmt"

	"matiksOfficial/matiks-server-go/pkg/config"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"firebase.google.com/go/v4/messaging"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func (s *service) SendPushNotificationToUsers(ctx context.Context, body, title string, data map[string]string, userIDs []primitive.ObjectID) error {
	message := &messaging.Message{
		Notification: &messaging.Notification{
			Title: title,
			Body:  body,
		},
		Data: data,
	}

	if len(userIDs) == 0 {
		zlog.Info(ctx, "no user IDs provided, skipping push notifications")
		return nil
	}

	users, err := s.userRepo.GetUsersByIDs(ctx, userIDs)
	if err != nil {
		zlog.Error(ctx, "failed to get users", err)
		return err
	}

	for _, user := range users {
		if user == nil {
			zlog.Error(ctx, "user is nil", systemErrors.ErrUserNil)
			continue
		}

		if user.Additional == nil || len(user.Additional.PushNotificationTokens) == 0 {
			zlog.Info(ctx, "user has no push notification tokens", zap.String("userID", user.ID.Hex()))
			continue
		}

		for _, pNT := range user.Additional.PushNotificationTokens {
			if pNT.PNT == nil {
				zlog.Error(ctx, "push notification token is nil", fmt.Errorf("userID: %s", user.ID.Hex()), zap.String("userID", user.ID.Hex()))
				continue
			}
			message.Token = *pNT.PNT

			if err := s.firebaseClient.Send(ctx, message); err != nil {
				zlog.Error(ctx, "failed to send push notification", err)
				continue
			}
			zlog.Info(ctx, "Successfully sent message")

		}
	}
	return nil
}

func getServiceAccountKeyFromEnv(cfg *config.Config) ([]byte, error) {
	encodedKey := cfg.FirebasePushNotificationServiceKey
	if encodedKey == "" {
		return nil, fmt.Errorf("Firebase push notification service key not set")
	}

	decodedKey, err := base64.StdEncoding.DecodeString(encodedKey)
	if err != nil {
		return nil, fmt.Errorf("failed to decode service account key: %v", err)
	}

	return decodedKey, nil
}
