package notification

import (
	"context"
	"fmt"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *service) SendFeedback(ctx context.Context, feedback *models.Feedback) (*bool, error) {
	zlog.Info(ctx, "Sending Feedback")
	status := false

	err := s.feedBackRepo.Create(ctx, feedback)
	if err != nil {
		zlog.Error(ctx, "Failed to create feedback", err)
		return &status, err
	}

	htmlBody := fmt.Sprintf(`
		<!DOCTYPE html>
		<html>
		<head>
			<style>
				body {
					display: flex;
					justify-content: center;
					align-items: center;
					min-height: 100vh;
					margin: 0;
					font-family: Arial, sans-serif;
					background-color: #f4f4f4;
				}
				.container {
					max-width: 600px;
					width: 100%%;
					background-color: #ffffff;
					padding: 20px;
					box-shadow: 0 0 10px rgba(0,0,0,0.1);
				}
				.feedback-details {
					margin-top: 20px;
					border-top: 1px solid #e0e0e0;
					padding-top: 10px;
				}
				.feedback-details p {
					margin: 10px 0;
					text-align: left;
				}
				.feedback-details strong {
					display: inline-block;
					min-width: 100px;
					margin-right: 10px;
				}
			</style>
		</head>
		<body>
			<div class="container">
				<div class="feedback-details">
					<p><strong>Email:</strong>%s</p>
					<p><strong>Phone:</strong>%s</p>
					<p><strong>Message:</strong></p>
					<p>%s</p>
				</div>
			</div>
		</body>
		</html>
	`,
		feedback.Email,
		feedback.Phone,
		feedback.Message,
	)

	emails := []string{
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
	}

	err = s.SendEmailNotification(ctx, "⚡ Matiks Feedback", htmlBody, emails)
	if err != nil {
		zlog.Error(ctx, "Failed to send email notification", err)
		return &status, err
	}

	status = true
	return &status, nil
}
