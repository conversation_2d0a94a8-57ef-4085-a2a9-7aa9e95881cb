package notification

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) SendNotification(ctx context.Context, notification *models.UserNotification) error {
	if notification == nil {
		zlog.Error(ctx, "Notification is nil", systemErrors.ErrNotificationFoundNil)
		return systemErrors.ErrNotificationFoundNil
	}

	if notification.UserID == primitive.NilObjectID {
		zlog.Error(ctx, "User id is nil", systemErrors.ErrNotificationUserIDNil)
		return systemErrors.ErrNotificationUserIDNil
	}

	if notification.SentAt.IsZero() {
		notification.SentAt = time.Now()
	}

	notificationObj := *notification
	go func(parentCtx context.Context, _notification models.UserNotification) {
		ctx, cancel := context.WithTimeout(utils.DeriveContextWithoutCancel(ctx), 30*time.Second)
		defer cancel()
		if err := s.handleNotificationTask(ctx, _notification); err != nil {
			zlog.Error(ctx, "Failed to handle notification task", err)
		}
	}(ctx, notificationObj)
	return nil
}

func (s *service) handleNotificationTask(ctx context.Context, notification models.UserNotification) error {
	switch notification.Type {
	case models.NotificationTypePushNotification:
		return s.PublishPushNotification(ctx, &notification)
	case models.NotificationTypeFeed:
		return s.feedService.AddFeed(ctx, &notification)
	case models.NotificationTypePushNotificationAndFeed:
		if err := s.PublishPushNotification(ctx, &notification); err != nil {
			zlog.Error(ctx, "Failed to publish push notification", err)
		}
		return s.feedService.AddFeed(ctx, &notification)
	case models.NotificationTypeInAppPopupAndFeed:
		if err := s.PublishInAppNotification(ctx, &notification); err != nil {
			zlog.Error(ctx, "Failed to publish in app notification", err)
		}
		return s.feedService.AddFeed(ctx, &notification)
	case models.NotificationTypeInAppPopup:
		return s.PublishInAppNotification(ctx, &notification)
	default:
		return fmt.Errorf("unknown notification type: %s", notification.Type)
	}
}
