package notification

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/firebase"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/infrastructure/sortedset"
	"matiksOfficial/matiks-server-go/pkg/config"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type service struct {
	userRepo       repository.UserRepository
	cfg            *config.Config
	sortedSet      sortedset.SortedSet
	feedBackRepo   repository.FeedbackRepository
	coreService    domain.CoreLogicStore
	firebaseClient firebase.FirebaseClient
	feedService    domain.FeedStore

	// email client
	// push notification client
}

func NewNotificationService(
	lc fx.Lifecycle,
	cfg *config.Config,
	repositoryFactory *repository.RepositoryFactory,
	sortedSet sortedset.SortedSet,
	coreService domain.CoreLogicStore,
	firebaseClient firebase.FirebaseClient,
	feedService domain.FeedStore,
) domain.NotificationStore {
	s := &service{
		userRepo:       repositoryFactory.UserRepository,
		cfg:            cfg,
		sortedSet:      sortedSet,
		feedBackRepo:   repositoryFactory.FeedbackRepository,
		coreService:    coreService,
		firebaseClient: firebaseClient,
		feedService:    feedService,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting notification service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down notification service")
			return nil
		},
	})

	return s
}
