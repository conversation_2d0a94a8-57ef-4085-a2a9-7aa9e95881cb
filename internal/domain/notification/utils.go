package notification

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"firebase.google.com/go/v4/messaging"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) PublishPushNotification(ctx context.Context, notification *models.UserNotification) error {
	if notification == nil {
		zlog.Error(ctx, "Notification is nil", systemErrors.ErrNotificationFoundNil)
		return systemErrors.ErrNotificationFoundNil
	}

	if notification.UserID == primitive.NilObjectID {
		zlog.Error(ctx, "User id is nil", systemErrors.ErrNotificationUserIDNil)
		return systemErrors.ErrNotificationUserIDNil
	}

	user, err := s.coreService.GetUserByID(ctx, notification.UserID)
	if err != nil {
		zlog.Error(ctx, "Failed to get user", err)
		return err
	}

	if user == nil {
		zlog.Error(ctx, "User not found", systemErrors.ErrUserNotFound, zap.String("id", notification.UserID.Hex()))
		return systemErrors.ErrUserNotFound
	}
	var tokens []string
	if user.Additional != nil && user.Additional.PushNotificationTokens != nil {
		for _, token := range user.Additional.PushNotificationTokens {
			if token.PNT == nil {
				zlog.Error(ctx, "Push notification token is nil", fmt.Errorf("userID: %s", user.ID.Hex()), zap.String("userID", user.ID.Hex()))
				continue
			}
			tokens = append(tokens, *token.PNT)
		}
	}

	if len(tokens) == 0 {
		// Use Warn if absence of tokens is expected behavior
		zlog.Warn(ctx, "No push notification tokens found",
			zap.String("userID", user.ID.Hex()))
		return systemErrors.ErrNotificationNoPushTokens
	}
	messages := make([]*messaging.Message, 0, len(tokens))
	for _, token := range tokens {
		messages = append(messages, &messaging.Message{
			Notification: &messaging.Notification{
				Title: notification.Title,
				Body:  notification.Body,
				ImageURL: func() string {
					if notification.ImageUrl != nil {
						return *notification.ImageUrl
					}
					return ""
				}(),
			},
			Token: token,
			Data:  notification.Data,
		})
	}
	err = s.firebaseClient.SendBatch(ctx, messages)
	if err != nil {
		zlog.Error(ctx, "Failed to send push notification", err)
		return err
	}

	zlog.Info(ctx, "Successfully sent push notification")
	return nil
}

func (s *service) PublishInAppNotification(ctx context.Context, notification *models.UserNotification) error {
	if notification == nil {
		zlog.Error(ctx, "Notification is nil", systemErrors.ErrNotificationFoundNil)
		return systemErrors.ErrNotificationFoundNil
	}

	if notification.UserID.IsZero() {
		zlog.Error(ctx, "User id is nil", systemErrors.ErrNotificationUserIDNil)
		return systemErrors.ErrNotificationUserIDNil
	}

	inAppAdditionalInfo, err := s.getInAppAdditionalInfo(ctx, notification.AdditionalInfo)
	if err != nil {
		zlog.Error(ctx, "Failed to get in app additional info", err)
		return err
	}

	inAppNotification := models.InAppNotification{
		Title:               notification.Title,
		Body:                notification.Body,
		InAppAdditionalInfo: inAppAdditionalInfo,
		SentAt:              notification.SentAt,
		ImageUrl:            notification.ImageUrl,
	}

	if err := s.coreService.PublishUserEvent(ctx, notification.UserID, &models.InAppNotificationPayload{
		InAppNotification: inAppNotification,
	}); err != nil {
		zlog.Error(ctx, "Failed to publish in app notification", err)
		return systemErrors.ErrPublishUserEventFailed
	}

	return nil
}

func (s *service) getInAppAdditionalInfo(ctx context.Context, additionalInfo *models.AdditionalInfo) (*models.InAppAdditionalInfo, error) {
	if additionalInfo == nil {
		return nil, systemErrors.ErrNotificationAdditionalInfoNil
	}
	if additionalInfo.InAppType == nil {
		zlog.Error(ctx, "In app type is nil", systemErrors.ErrNotificationInAppTypeNil)
		return nil, systemErrors.ErrNotificationInAppTypeNil
	}
	switch *additionalInfo.InAppType {
	case models.InAppTypeConnection:
		if additionalInfo.ConnectionRequestSentBy == nil {
			zlog.Error(ctx, "Connection request sent by is nil", systemErrors.ErrNotificationFeedAdditionalInfoNil)
			return nil, systemErrors.ErrNotificationFeedAdditionalInfoNil
		}

		return &models.InAppAdditionalInfo{
			ConnectionRequest: &models.ConnectionRequest{
				SentBy: *additionalInfo.ConnectionRequestSentBy,
			},
		}, nil
	case models.InAppTypeChallenge:
		if additionalInfo.ChallengeGameId == nil {
			zlog.Error(ctx, "Challenge id is nil", systemErrors.ErrNotificationFeedAdditionalInfoNil)
			return nil, systemErrors.ErrNotificationFeedAdditionalInfoNil
		}

		return &models.InAppAdditionalInfo{
			ChallengeGameId: additionalInfo.ChallengeGameId,
		}, nil
	default:
		zlog.Error(ctx, "Unsupported InAppType", systemErrors.ErrUnsupportedInAppType)
		return nil, systemErrors.ErrUnsupportedInAppType
	}
}
