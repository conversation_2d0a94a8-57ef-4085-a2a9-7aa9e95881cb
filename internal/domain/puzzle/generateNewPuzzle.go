package puzzle

import (
	"context"
	"errors"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/domain/questions/questionsGenerator"
	"matiksOfficial/matiks-server-go/internal/models"

	kenken "matiksOfficial/matiks-server-go/internal/domain/questions/questionsGenerator"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

const (
	GridSize        = 7
	GridSizeKenKen  = 5
	GroupSizeKenKen = 3
)

func (s *service) GenerateDailyPuzzle(ctx context.Context, puzzleType models.PuzzleType) (*models.Puzzle, error) {
	now := time.Now()
	date := now.Format("2006-01-02")
	if puzzleType == models.PuzzleTypeKenKen {
		return s.generateNewKenKenPuzzle(ctx, date)
	} else if puzzleType == models.PuzzleTypeCrossMath {
		return s.generateNewCrossMathPuzzle(ctx, date)
	} else if puzzleType == models.PuzzleTypeHectoc {
		return s.generateNewHectocPuzzle(ctx, date)
	} else {
		return s.generateNewCrossMathPuzzle(ctx, date)
	}
}

func (s *service) GenerateNewPuzzle(ctx context.Context, dateStr string, puzzleType models.PuzzleType) (*models.Puzzle, error) {
	if puzzleType == models.PuzzleTypeKenKen {
		return s.generateNewKenKenPuzzle(ctx, dateStr)
	} else if puzzleType == models.PuzzleTypeCrossMath {
		return s.generateNewCrossMathPuzzle(ctx, dateStr)
	} else if puzzleType == models.PuzzleTypeHectoc {
		return s.generateNewHectocPuzzle(ctx, dateStr)
	} else {
		return s.generateNewCrossMathPuzzle(ctx, dateStr)
	}
}

func (s *service) generateNewCrossMathPuzzle(ctx context.Context, dateStr string) (*models.Puzzle, error) {
	_, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return nil, fmt.Errorf("generateNewPuzzle: invalid date format, expected YYYY-MM-DD or full ISO 8601: %w", err)
	}
	formattedDate := dateStr
	difficulty, err := s.GetPuzzleDifficultyByDate(dateStr)
	if err != nil {
		return nil, err
	}

	existingChallenge, err := s.puzzleRepo.FindOne(ctx, bson.M{
		"puzzleDate": formattedDate,
		"difficulty": difficulty,
		"$or": []bson.M{
			{"puzzleType": models.PuzzleTypeCrossMath},
			{"puzzleType": nil},
		},
	})
	if err == nil && existingChallenge != nil {
		zlog.Info(ctx, "Puzzle already exists. Skipping creation.", zap.String("formattedDate", formattedDate))
		return existingChallenge, nil
	}
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return nil, err
	} else {
		zlog.Info(ctx, "Generating new puzzle", zap.String("formattedDate", formattedDate), zap.String("difficulty", difficulty.String()))
	}

	puzzleID := primitive.NewObjectID()

	cells, availableNumbers := questionsGenerator.GeneratePuzzle(models.Difficulty(difficulty), GridSize)

	newPuzzle := &models.Puzzle{
		ID:               puzzleID,
		PuzzleType:       utils.AllocPtr(models.PuzzleTypeCrossMath),
		PuzzleDate:       utils.AllocPtr(formattedDate),
		Difficulty:       difficulty.String(),
		SolvedBy:         utils.AllocPtr(0),
		Cells:            convertCellsToPointers(cells),
		AvailableAnswers: availableNumbers,
	}

	err = s.puzzleRepo.Create(ctx, newPuzzle)
	if err != nil {
		zlog.Error(ctx, "error inserting new puzzle:", err)
		return nil, err
	}

	zlog.Info(ctx, "New puzzle generated", zap.String("start time", formattedDate))

	return newPuzzle, nil
}

func (s *service) generateNewKenKenPuzzle(ctx context.Context, dateStr string) (*models.Puzzle, error) {
	_, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return nil, fmt.Errorf("generateNewPuzzle: invalid date format, expected YYYY-MM-DD or full ISO 8601: %w", err)
	}
	formattedDate := dateStr
	difficulty, err := s.GetPuzzleDifficultyByDate(dateStr)
	if err != nil {
		return nil, err
	}

	existingChallenge, err := s.puzzleRepo.FindOne(ctx, bson.M{
		"puzzleDate": formattedDate,
		"difficulty": difficulty,
		"puzzleType": models.PuzzleTypeKenKen,
	})
	if err == nil && existingChallenge != nil {
		zlog.Info(ctx, "Puzzle already exists. Skipping creation.", zap.String("formattedDate", formattedDate))
		return existingChallenge, nil
	}
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return nil, err
	} else {
		zlog.Info(ctx, "Generating new puzzle", zap.String("formattedDate", formattedDate), zap.String("difficulty", difficulty.String()))
	}

	puzzleID := primitive.NewObjectID()
	maxGroupSize := GroupSizeKenKen
	gridSize := GridSizeKenKen

	if difficulty == models.Hard {
		maxGroupSize = 4
		gridSize = 6
	}
	if difficulty == models.Medium || difficulty == models.Easy {
		maxGroupSize = 3
		gridSize = 5
	}

	kenkenSettings := kenken.KenKenSettings{
		Size:         gridSize,
		MaxGroupSize: maxGroupSize,
		Operations: kenken.Operations{
			Addition:       true,
			Subtraction:    true,
			Multiplication: true,
			Division:       true,
		},
	}
	generatedKenKen := kenken.GenerateKenKen(kenkenSettings)
	puzzleString := generatedKenKen.String()

	newPuzzle := &models.Puzzle{
		ID:               puzzleID,
		PuzzleType:       utils.AllocPtr(models.PuzzleTypeKenKen),
		Cells:            [][]*models.Cell{},
		PuzzleDate:       utils.AllocPtr(formattedDate),
		Difficulty:       difficulty.String(),
		SolvedBy:         utils.AllocPtr(0),
		AvailableAnswers: []string{},
		TypeSpecific: &models.PuzzleTypeSpecificDetails{
			PuzzleType: models.PuzzleTypeKenKen,
			KenKen: &models.KenKenPuzzle{
				PuzzleString: utils.AllocPtr(puzzleString),
			},
			CrossMath: nil,
		},
	}

	err = s.puzzleRepo.Create(ctx, newPuzzle)
	if err != nil {
		zlog.Error(ctx, "error inserting new puzzle:", err)
		return nil, err
	}

	zlog.Info(ctx, "New puzzle generated", zap.String("start time", formattedDate))

	return newPuzzle, nil
}

func (s *service) generateNewHectocPuzzle(ctx context.Context, dateStr string) (*models.Puzzle, error) {
	parsedDate, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return nil, fmt.Errorf("generateNewHectocPuzzle: invalid date format, expected YYYY-MM-DD: %w", err)
	}
	formattedDate := dateStr
	difficulty, err := s.GetPuzzleDifficultyByDate(dateStr)
	if err != nil {
		return nil, err
	}

	existingChallenge, err := s.puzzleRepo.FindOne(ctx, bson.M{
		"puzzleDate": formattedDate,
		"difficulty": difficulty,
		"puzzleType": models.PuzzleTypeHectoc, // Strict check for Hectoc
	})
	if err == nil && existingChallenge != nil {
		zlog.Info(ctx, "Hectoc Puzzle already exists. Skipping creation.", zap.String("formattedDate", formattedDate))
		return existingChallenge, nil
	}
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return nil, err
	} else {
		zlog.Info(ctx, "Generating new Hectoc puzzle", zap.String("formattedDate", formattedDate), zap.String("difficulty", difficulty.String()))
	}

	puzzleID := primitive.NewObjectID()

	hectocPuzzleString := questionsGenerator.GenerateHectoc(parsedDate)

	newPuzzle := &models.Puzzle{
		ID:               puzzleID,
		PuzzleType:       utils.AllocPtr(models.PuzzleTypeHectoc),
		Cells:            [][]*models.Cell{},
		PuzzleDate:       utils.AllocPtr(formattedDate),
		Difficulty:       difficulty.String(),
		SolvedBy:         utils.AllocPtr(0),
		AvailableAnswers: []string{},
		TypeSpecific: &models.PuzzleTypeSpecificDetails{
			PuzzleType: models.PuzzleTypeHectoc,
			Hectoc: &models.HectocPuzzle{
				PuzzleString: utils.AllocPtr(hectocPuzzleString),
			},
			CrossMath: nil,
			KenKen:    nil,
		},
	}

	err = s.puzzleRepo.Create(ctx, newPuzzle)
	if err != nil {
		zlog.Error(ctx, "error inserting new Hectoc puzzle:", err)
		return nil, err
	}

	zlog.Info(ctx, "New Hectoc puzzle generated", zap.String("start time", formattedDate))

	return newPuzzle, nil
}

func convertCellsToPointers(cells [][]models.Cell) [][]*models.Cell {
	pointerCells := make([][]*models.Cell, len(cells))
	for i, row := range cells {
		pointerCells[i] = make([]*models.Cell, len(row))
		for j := range row {
			pointerCells[i][j] = &row[j]
		}
	}
	return pointerCells
}
