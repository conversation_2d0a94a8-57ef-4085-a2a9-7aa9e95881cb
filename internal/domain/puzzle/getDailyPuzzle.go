package puzzle

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
)

func (s *service) GetPuzzleDifficultyByDate(dateStr string) (models.Difficulty, error) {
	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return models.Medium, fmt.Errorf("invalid date format, expected YYYY-MM-DD or full ISO 8601: %w", err)
	}
	weekday := date.Weekday()
	switch weekday {
	case time.Monday, time.Tuesday, time.Wednesday, time.Thursday, time.Friday:
		return models.Medium, nil
	case time.Saturday, time.Sunday:
		return models.Hard, nil
	}
	return models.Medium, fmt.Errorf("invalid weekday")
}

func (s *service) GetDailyPuzzle(ctx context.Context, dateStr string) (*models.Puzzle, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	puzzleType := models.PuzzleTypeCrossMath

	var puzzle *models.Puzzle

	_, err = time.Parse("2006-01-02", dateStr)
	if err != nil {
		zlog.Error(ctx, "Invalid date format", err)
		return nil, fmt.Errorf("invalid date format, expected YYYY-MM-DD or full ISO 8601: %w", err)
	}

	puzzle, err = s.puzzleRepo.FindOne(ctx, bson.M{"puzzleDate": dateStr, "$or": []bson.M{
		{"puzzleType": puzzleType},
		{"puzzleType": nil},
	}})
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zlog.Info(ctx, "No puzzle found for the given date. Generating default daily puzzle.", zap.String("date", dateStr))
			puzzle, err = s.GenerateNewPuzzle(ctx, dateStr, models.PuzzleTypeCrossMath)
			if err != nil {
				zlog.Error(ctx, "Failed to generate new default puzzle", err)
				return nil, fmt.Errorf("failed to generate new puzzle: %w", err)
			}
		} else {
			zlog.Error(ctx, "Failed to fetch puzzle", err)
			return nil, fmt.Errorf("error fetching puzzle: %w", err)
		}
	}

	if puzzle == nil {
		return nil, fmt.Errorf("fetched or generated puzzle is nil")
	}

	existingResult, err := s.puzzleResultRepo.FindOne(ctx, bson.M{"userId": userId, "puzzleId": puzzle.ID})
	if err == nil && existingResult != nil {
		if existingResult.TimeSpent != nil && *existingResult.TimeSpent != 0 {
			puzzle.HasAttempted = utils.AllocPtr(true)
		}

		if puzzle.HasAttempted != nil && *puzzle.HasAttempted {
			puzzle.CurrentUserResult = existingResult
		}
	} else if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		zlog.Error(ctx, "Failed to fetch puzzle result", err)
		return nil, fmt.Errorf("error fetching puzzle: %w", err)
	}

	userPuzzleStat, err := s.puzzleUserStatRepo.FindByUserID(ctx, userId, models.PuzzleTypeCrossMath)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		zlog.Error(ctx, "Failed to fetch user puzzle stat", err)
		return nil, fmt.Errorf("error fetching user puzzle stat: %w", err)
	}
	puzzle.UserStat = userPuzzleStat

	zlog.Info(ctx, "Puzzle fetched successfully", zap.String("puzzleId", puzzle.ID.Hex()))
	return puzzle, nil
}
