package puzzle

import (
	"context"
	"errors"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils/encryption"

	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
)

func (s *service) GetDailyPuzzleByType(ctx context.Context, dateStr string, puzzleType models.PuzzleType) (*models.Puzzle, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	_, err = time.Parse("2006-01-02", dateStr)
	if err != nil {
		zlog.Error(ctx, "Invalid date format", err)
		return nil, fmt.Errorf("invalid date format, expected YYYY-MM-DD or full ISO 8601: %w", err)
	}

	filter := bson.M{
		"puzzleDate": dateStr,
	}
	if puzzleType == models.PuzzleTypeCrossMath {
		filter["$or"] = []bson.M{
			{"puzzleType": puzzleType},
			{"puzzleType": nil},
		}
	} else {
		filter["puzzleType"] = puzzleType
	}

	puzzle, err := s.puzzleRepo.FindOne(ctx, filter)

	if err != nil || puzzle == nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zlog.Info(ctx, "No puzzle found for the given date. Generating default daily puzzle.", zap.String("date", dateStr))
			puzzle, err = s.GenerateNewPuzzle(ctx, dateStr, puzzleType)
			if err != nil {
				zlog.Error(ctx, "Failed to generate new default puzzle", err)
				return nil, fmt.Errorf("failed to generate new puzzle: %w", err)
			}
		} else {
			zlog.Error(ctx, "Failed to fetch puzzle", err)
			return nil, fmt.Errorf("error fetching puzzle: %w", err)
		}
	}

	if puzzle == nil {
		return nil, fmt.Errorf("fetched or generated puzzle is nil")
	}

	existingResult, err := s.puzzleResultRepo.FindOne(ctx, bson.M{"userId": userId, "puzzleId": puzzle.ID})
	if err == nil && existingResult != nil {
		if existingResult.TimeSpent != nil && *existingResult.TimeSpent != 0 {
			puzzle.HasAttempted = utils.AllocPtr(true)
		}

		if puzzle.HasAttempted != nil && *puzzle.HasAttempted {
			puzzle.CurrentUserResult = existingResult
		}
	} else if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		zlog.Error(ctx, "Failed to fetch puzzle result", err)
		return nil, fmt.Errorf("error fetching puzzle: %w", err)
	}

	userPuzzleStat, err := s.puzzleUserStatRepo.FindByUserID(ctx, userId, puzzleType)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		zlog.Error(ctx, "Failed to fetch user puzzle stat", err)
		return nil, fmt.Errorf("error fetching user puzzle stat: %w", err)
	}
	puzzle.UserStat = userPuzzleStat

	zlog.Info(ctx, "Puzzle fetched successfully", zap.String("puzzleId", puzzle.ID.Hex()))

	err = s.addEncryptedKenKenPuzzle(puzzle)
	if err != nil {
		zlog.Error(ctx, "Failed to add encrypted kenken puzzle", err)
		return nil, fmt.Errorf("failed to add encrypted kenken puzzle: %w", err)
	}

	return puzzle, nil
}

func (s *service) addEncryptedKenKenPuzzle(puzzle *models.Puzzle) error {
	if puzzle == nil {
		return nil
	}
	if puzzle.PuzzleType == nil || *puzzle.PuzzleType != models.PuzzleTypeKenKen {
		return nil
	}
	if puzzle.TypeSpecific == nil || puzzle.TypeSpecific.KenKen == nil {
		return nil
	}
	kenKenPuzzleString := puzzle.TypeSpecific.KenKen.PuzzleString
	if kenKenPuzzleString == nil {
		return nil
	}

	encQ, err := encryption.EncryptObject(kenKenPuzzleString)
	if err != nil {
		return err
	}

	*puzzle.TypeSpecific.KenKen.PuzzleString = encQ

	return nil
}
