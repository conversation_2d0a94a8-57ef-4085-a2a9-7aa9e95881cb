package puzzle

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func (s *service) GetPuzzleSubmissionsByMonthByType(ctx context.Context, yearMonths []string, puzzleType models.PuzzleType) ([]*models.PuzzleMonthlySubmissionReport, error) {
	var results []*models.PuzzleMonthlySubmissionReport

	for _, yearMonth := range yearMonths {
		t, err := time.Parse("2006-01", yearMonth)
		if err != nil {
			return nil, fmt.Errorf("invalid yearMonth format: %s", yearMonth)
		}

		startOfMonth := time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, time.UTC)
		endOfMonth := startOfMonth.AddDate(0, 1, 0).Add(-time.Second)

		submissions, err := s.puzzleResultRepo.GetSubmissionsBetweenDatesByType(ctx, startOfMonth.Format("2006-01-02"), endOfMonth.Format("2006-01-02"), puzzleType)
		if err != nil {
			zlog.Error(ctx, "Failed to fetch puzzle submissions", err, zap.String("yearMonth", yearMonth))
			return nil, fmt.Errorf("failed to fetch puzzle submissions for %s: %w", yearMonth, err)
		}

		results = append(results, &models.PuzzleMonthlySubmissionReport{
			YearMonth:         yearMonth,
			PuzzleSubmissions: submissions,
		})
	}

	zlog.Info(ctx, "GetPuzzleSubmissionsByMonth completed")
	return results, nil
}
