package puzzle

import (
	"context"
	"errors"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) GetUserPuzzleStatsByType(ctx context.Context, puzzleType models.PuzzleType) (*models.PuzzleUserStats, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	userPuzzleStat, err := s.puzzleUserStatRepo.FindByUserID(ctx, userId, puzzleType)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return userPuzzleStat, nil
}
