package puzzle

import (
	"context"

	"go.uber.org/fx"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

type service struct {
	userService         domain.UserStore
	userRepo            repository.UserRepository
	puzzleRepo          repository.PuzzleRepository
	puzzleResultRepo    repository.PuzzleResultRepository
	puzzleUserStatRepo  repository.PuzzleUserStatRepository
	notificationService domain.NotificationStore
	coreService         domain.CoreLogicStore
	userStreakRepo      repository.UserStreakRepository
	botDetectionService domain.BotDetectionStore
}

func NewPuzzleService(
	lc fx.Lifecycle, notificationService domain.NotificationStore,
	userService domain.UserStore,
	repositoryFactory *repository.RepositoryFactory,
	coreService domain.CoreLogicStore,
	botDetectionService domain.BotDetectionStore,
) domain.PuzzleStore {
	s := &service{
		userService:         userService,
		userRepo:            repositoryFactory.UserRepository,
		puzzleRepo:          repositoryFactory.PuzzleRepository,
		puzzleResultRepo:    repositoryFactory.PuzzleResultRepository,
		puzzleUserStatRepo:  repositoryFactory.PuzzleUserStatRepository,
		notificationService: notificationService,
		coreService:         coreService,
		userStreakRepo:      repositoryFactory.UserStreakRepository,
		botDetectionService: botDetectionService,
	}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting puzzle service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down puzzle service")
			return nil
		},
	})

	return s
}
