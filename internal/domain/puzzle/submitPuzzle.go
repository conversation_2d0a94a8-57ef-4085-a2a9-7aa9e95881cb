package puzzle

import (
	"context"
	"errors"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/domain/userStreak"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

const StatikCoinsEarnedForPuzzle = 15

func (s *service) SubmitPuzzleSolution(ctx context.Context, puzzleId primitive.ObjectID, timeSpent int) (*models.PuzzleResult, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	score := timeSpent

	existingResult, err := s.puzzleResultRepo.FindOne(ctx, bson.M{"userId": userId, "puzzleId": puzzleId})
	if err == nil && existingResult != nil {
		zlog.Info(ctx, "User has already solved this puzzle",
			zap.String("puzzleId", puzzleId.Hex()))
		return existingResult, nil
	} else if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return nil, err
	}

	puzzlePtr, err := s.puzzleRepo.FindByID(ctx, puzzleId)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, fmt.Errorf("puzzle not found with id: %s", puzzleId.Hex())
		}
		return nil, fmt.Errorf("error fetching puzzle by ID: %w", err)
	}
	if puzzlePtr == nil {
		return nil, fmt.Errorf("puzzle not found or is nil with id: %s", puzzleId.Hex())
	}

	puzzle := *puzzlePtr

	var puzzleDate *string
	var puzzleType models.PuzzleType

	if puzzle.PuzzleType == nil {
		puzzleType = models.PuzzleTypeCrossMath
	} else {
		puzzleType = *puzzle.PuzzleType
	}

	puzzleDate = puzzle.PuzzleDate

	if puzzle.Stats == nil {
		puzzle.Stats = &models.PuzzleStats{}
	}

	if puzzle.Stats.NumOfSubmission == 0 {
		puzzle.Stats.AverageTime = timeSpent
	} else {
		if puzzle.Stats.NumOfSubmission >= 0 {
			puzzle.Stats.AverageTime = int(float64(puzzle.Stats.AverageTime*puzzle.Stats.NumOfSubmission+timeSpent) / float64(puzzle.Stats.NumOfSubmission+1.0))
		} else {
			puzzle.Stats.AverageTime = timeSpent
		}
	}
	puzzle.Stats.NumOfSubmission++
	if puzzle.Stats.BestTime == 0 || timeSpent < puzzle.Stats.BestTime {
		puzzle.Stats.BestTime = timeSpent
	}

	if puzzle.SolvedBy == nil {
		puzzle.SolvedBy = utils.AllocPtr(1)
	} else {
		puzzle.SolvedBy = utils.AllocPtr(*puzzle.SolvedBy + 1)
	}
	err = s.puzzleRepo.Update(ctx, &puzzle)
	if err != nil {
		zlog.Error(ctx, "Error while updating puzzle", err, zap.String("puzzleId", puzzleId.Hex())) // Pass error, add fields
		return nil, fmt.Errorf("error updating puzzle: %w", err)
	}

	if puzzleDate == nil {
		zlog.Error(ctx, "PuzzleDate is nil after fetching puzzle", fmt.Errorf("puzzle date missing"), zap.String("puzzleId", puzzleId.Hex())) // Pass error, add fields
		return nil, fmt.Errorf("internal error: puzzle date is missing for id %s", puzzleId.Hex())
	}

	puzzleResult := models.PuzzleResult{
		ID:                primitive.NewObjectID(),
		UserID:            &userId,
		PuzzleID:          &puzzleId,
		StatikCoinsEarned: utils.AllocPtr(StatikCoinsEarnedForPuzzle),
		TimeSpent:         &score,
		CompletedAt:       utils.AllocPtr(time.Now()),
		PuzzleDate:        *puzzleDate,
		PuzzleType:        &puzzleType,
	}

	err = s.puzzleResultRepo.Create(ctx, &puzzleResult)
	if err != nil {
		zlog.Error(ctx, "Error while creating puzzle Result", err,
			zap.String("puzzleId", puzzleId.Hex()))
		return nil, err
	}

	userPuzzleStat, err := s.puzzleUserStatRepo.FindByUserID(ctx, userId, puzzleType)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return nil, fmt.Errorf("error fetching user puzzle stat: %w", err)
	}

	if userPuzzleStat == nil {
		userPuzzleStat = &models.PuzzleUserStats{
			UserID:     userId,
			PuzzleType: &puzzleType,
		}
	}

	if userPuzzleStat.NumOfSubmission == nil {
		userPuzzleStat.NumOfSubmission = utils.AllocPtr(1)
		userPuzzleStat.AverageTime = &timeSpent
	} else {
		*userPuzzleStat.NumOfSubmission++
		newAverage := int(((*userPuzzleStat.AverageTime * (*userPuzzleStat.NumOfSubmission - 1)) + timeSpent) / *userPuzzleStat.NumOfSubmission)
		userPuzzleStat.AverageTime = &newAverage
	}

	if userPuzzleStat.BestTime == nil || timeSpent < *userPuzzleStat.BestTime {
		userPuzzleStat.BestTime = &timeSpent
	}

	if userPuzzleStat.ID == primitive.NilObjectID {
		err = s.puzzleUserStatRepo.Create(ctx, userPuzzleStat)
	} else {
		err = s.puzzleUserStatRepo.Update(ctx, userPuzzleStat)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to update user puzzle stat: %w", err)
	}

	go func() {
		if err := userStreak.UpdateUserStreak(utils.DeriveContextWithoutCancel(ctx), userId, s.userRepo, true, s.notificationService, s.coreService, s.userStreakRepo); err != nil {
			zlog.Error(ctx, "Error updating user streaks after solving Puzzle", err)
		}
	}()

	go func() {
		timeSpentVal := 0
		if puzzleResult.TimeSpent != nil {
			timeSpentVal = *puzzleResult.TimeSpent
		}
		activityType := constants.ActivityTypeDailyPuzzleCrossMath
		if puzzleType == models.PuzzleTypeKenKen {
			activityType = constants.ActivityTypeDailyPuzzleKenKen
		} else if puzzleType == models.PuzzleTypeHectoc {
			activityType = constants.ActivityTypeDailyPuzzleHectoc
		}
		if err := s.userService.UpdateUserStatikCoinsAndTimeSpent(utils.DeriveContextWithoutCancel(ctx), userId, activityType, StatikCoinsEarnedForPuzzle, int64(timeSpentVal), &puzzleId); err != nil {
			zlog.Error(ctx, "Failed to update user activity after solving Puzzle", err)
		}
	}()

	return &puzzleResult, nil
}
