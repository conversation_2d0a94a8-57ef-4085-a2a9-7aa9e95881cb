package puzzleGame

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/zap"
)

func (s *service) AbortSearchingForPuzzleGame(ctx context.Context) (*bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get user ID from context: %w", err)
	}

	zlog.Debug(ctx, "Abort searching process", zap.String("userID", userID.Hex()))

	success := false
	success, err = s.playersQueue.RemoveUser(ctx, userID.Hex())
	if err != nil {
		return &success, fmt.Errorf("failed to remove user from waiting queue: %w", err)
	}

	return &success, nil
}
