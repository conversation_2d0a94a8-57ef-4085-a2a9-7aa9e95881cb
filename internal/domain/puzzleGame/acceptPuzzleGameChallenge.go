package puzzleGame

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/game/utils/botutils"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/queue"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) AcceptChallengeOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Error retrieving user from context:", err)
		return nil, err
	}

	zlog.Debug(ctx, "Accepting challenge", zap.String("gameID", gameID.Hex()))

	game, err := s.puzzleGameRepo.GetPuzzleGameByID(ctx, gameID)
	if err != nil {
		zlog.Error(ctx, "Error retrieving game:", err)
		return nil, err
	}

	if game.GameStatus != models.PuzzleGameStatusCreated {
		return nil, fmt.Errorf("challenge is no longer valid")
	}

	if (len(game.Players) < 2) || (game.Players[0].UserID == primitive.NilObjectID) || (game.Players[1].UserID == primitive.NilObjectID) {
		return nil, fmt.Errorf("invalid game players")
	}

	user, err := s.userService.GetUserByID(ctx, game.Players[1].UserID)
	if err != nil {
		zlog.Error(ctx, "Error retrieving user:", err)
		return nil, err
	}

	isBot := false
	if (user.IsHumanBot != nil && *user.IsHumanBot) || (user.IsBot != nil && *user.IsBot) {
		isBot = true

		if user.IsHumanBot != nil && *user.IsHumanBot {
			if queue.PlayedInLastHour(game.Players[0].UserID, user) > 6 {
				zlog.Info(ctx, "Human bot has played a lot")
				return nil, fmt.Errorf("human bot has exceeded allowed puzzles in the last hour")
			}
			queue.UpdatePlayedInLastHour(game.Players[0].UserID, user)
			err := botutils.UpdateHumanBotCache(user, s.cache, true)
			if err != nil {
				return nil, err
			}
		}
	}

	if !isBot && game.Players[1].UserID != userID {
		return nil, fmt.Errorf("you are not the challenged player")
	}

	game.GameStatus = models.PuzzleGameStatusReady

	err = s.puzzleGameRepo.UpdatePuzzleGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Error updating game status:", err)
		return nil, err
	}

	err = s.puzzleGameCache.SetPuzzleGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Error updating game in Cache:", err)
		return nil, err
	}

	err = s.publishPuzzleGameEvent(ctx, game, constants.GameEventEnum.USER_JOINED.String())
	if err != nil {
		return nil, err
	}

	err = s.PublishChallengeEvent(ctx, game, string(models.ChallengeStatusChallengeAccepted))
	if err != nil {
		zlog.Error(ctx, "Error publishing challenge accepted event:", err)
	}

	return game, nil
}
