package puzzleGame

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/questions/questionsGenerator"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) AcceptRematchOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	zlog.Debug(ctx, "AcceptRematch", zap.String("gameID", gameID.Hex()))

	game, err := s.puzzleGameRepo.FindOne(ctx, bson.M{"_id": gameID})
	if err != nil || game == nil {
		return nil, fmt.Errorf("game not found %w", err)
	}

	if game.RematchRequestedBy == nil {
		return nil, fmt.Errorf("no rematch request for this game")
	}

	if *game.RematchRequestedBy == userID {
		return nil, fmt.Errorf("you cannot accept your own rematch request")
	}

	isValidPlayer := false
	for _, player := range game.Players {
		if player.UserID == userID {
			isValidPlayer = true
			break
		}
	}

	if !isValidPlayer {
		return nil, fmt.Errorf("only players from the original game can accept rematch requests")
	}

	var seriesID *primitive.ObjectID
	if game.SeriesID != nil {
		seriesID = game.SeriesID
	} else {
		// Create a new game series
		newSeries := &models.GameSeries{
			ID:        utils.AllocPtr(primitive.NewObjectID()),
			GameIds:   &[]primitive.ObjectID{gameID},
			PlayerIds: &[]primitive.ObjectID{userID, *game.RematchRequestedBy},
		}
		_, err = s.puzzleGameSeriesRepo.CreatePuzzleGameSeries(ctx, newSeries)
		if err != nil {
			return nil, fmt.Errorf("failed to create game series: %w", err)
		}
		seriesID = newSeries.ID
	}

	config := models.PuzzleGameConfig{
		TimeLimit:          game.Config.TimeLimit,
		NumPlayers:         game.Config.NumPlayers,
		GameType:           game.Config.GameType,
		NumOfQuestions:     game.Config.NumOfQuestions,
		DifficultyLevel:    game.Config.DifficultyLevel,
		MaxTimePerQuestion: game.Config.MaxTimePerQuestion,
	}

	opponentUserId := s.GetOpponentID(game.Players, userID)

	currentUser, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get current user: %w", err)
	}
	opponentUser, err := s.userService.GetUserByID(ctx, opponentUserId)
	if err != nil {
		return nil, fmt.Errorf("failed to get opponent user: %w", err)
	}

	updatedUsers := []*models.User{currentUser, opponentUser}

	updatedPlayers := s.getPlayersFromUsers(updatedUsers)
	newGame, err := s.createGameWithPlayers(ctx, updatedPlayers, config)
	if err != nil {
		return nil, err
	}

	// Update the game series document
	seriesUpdate := bson.M{
		"$addToSet": bson.M{
			"gameIds": newGame.ID,
		},
	}
	err = s.puzzleGameSeriesRepo.UpdateOne(ctx, bson.M{"_id": seriesID}, seriesUpdate)
	if err != nil {
		return nil, fmt.Errorf("failed to update game series: %w", err)
	}

	update := bson.M{
		"$unset": bson.M{
			"rematchRequestedBy": 1,
		},
		"$set": bson.M{"seriesId": seriesID},
	}

	filter := bson.M{"_id": gameID}
	err = s.puzzleGameRepo.UpdateOne(ctx, filter, update)
	if err != nil {
		zlog.Info(ctx, "Error deleting RematchRequestedBy field after auto-closing rematch request", zap.Error(err))

		return nil, err
	}
	newGameID := newGame.ID.Hex()

	// update game cache and set seriesId of game
	newGame.SeriesID = seriesID
	err = s.puzzleGameCache.SetPuzzleGame(ctx, newGame)
	if err != nil {
		return nil, err
	}

	err = s.publishRematchEventToBothUsers(ctx, game, *game.RematchRequestedBy, constants.RematchGameEnum.REMATCH_ACCEPTED.String(), &newGameID)
	if err != nil {
		return nil, err
	}

	s.scheduleEndPuzzleGame(ctx, newGame)

	return newGame, nil
}

func (s *service) getPlayersFromUsers(users []*models.User) []*models.Player {
	Players := make([]*models.Player, len(users))

	for i, user := range users {

		if user.RatingV2 == nil {
			user.RatingV2 = &models.UserRating{
				GlobalRating: user.Rating,
				PuzzleRating: utils.AllocPtr(constants.DefaultPuzzleRating),
			}
			err := s.userService.UpdateUserFromObject(context.Background(), user)
			if err != nil {
				return nil
			}
		}
		if user.RatingV2 != nil && user.RatingV2.PuzzleRating == nil {
			user.RatingV2.PuzzleRating = utils.AllocPtr(constants.DefaultPuzzleRating)
			err := s.userService.UpdateUserFromObject(context.Background(), user)
			if err != nil {
				return nil
			}
		}

		Players[i] = &models.Player{
			UserID:      user.ID,
			Status:      models.PlayerStatusAccepted,
			Rating:      user.RatingV2.PuzzleRating,
			StatikCoins: user.StatikCoins,
		}
	}

	return Players
}

func (s *service) createGameWithPlayers(ctx context.Context, players []*models.Player, config models.PuzzleGameConfig) (*models.PuzzleGame, error) {
	zlog.Debug(ctx, "Creating game with players", zap.Int("numPlayers", len(players)), zap.Any("players", players))
	puzzleGameType := models.PuzzleGameTypeCrossMathPuzzleDuel

	if config.GameType != nil {
		puzzleGameType = *config.GameType
	}

	game := &models.PuzzleGame{
		Config:      &config,
		GameType:    puzzleGameType,
		CreatedBy:   players[0].UserID,
		GameStatus:  models.PuzzleGameStatusStarted,
		Players:     players,
		LeaderBoard: make([]*models.PuzzleLeaderboardEntry, len(players)),
		Questions:   []*models.PuzzleGameQuestion{},
		StartTime:   utils.AllocPtr(time.Now().Add(6 * time.Second)),
	}

	if len(players) > 0 {
		avgRating := 0
		for _, player := range players {
			if player.Rating != nil {
				avgRating += *player.Rating
			}
		}
		avgRating = avgRating / len(players)
		game.Questions = questionsGenerator.GeneratePuzzlesGameQuestion(avgRating)
		game.LeaderBoard = make([]*models.PuzzleLeaderboardEntry, len(players))
		for i, player := range players {
			game.LeaderBoard[i] = &models.PuzzleLeaderboardEntry{
				UserID:      &player.UserID,
				Correct:     utils.AllocPtr(0),
				Incorrect:   utils.AllocPtr(0),
				TotalPoints: utils.AllocPtr(0.0),
			}
		}
	}
	createdGame, err := s.puzzleGameRepo.CreatePuzzleGame(ctx, game)
	if err != nil {
		return nil, fmt.Errorf("failed to create game: %w", err)
	}

	err = s.puzzleGameCache.SetPuzzleGame(ctx, createdGame)
	if err != nil {
		zlog.Error(ctx, "Failed to cache game", err)
	}

	return createdGame, nil
}

func (s *service) notifyMatchedPlayers(ctx context.Context, game *models.PuzzleGame, players []*models.User) {
	for _, player := range players {
		opponent := players[0]
		if opponent.ID == player.ID {
			opponent = players[1]
		}

		zlog.Debug(ctx, "Notifying matched players", zap.String("user", player.ID.Hex()), zap.String("opponent", opponent.ID.Hex()))

		userEvent := &models.PuzzleGameEventWithOpponentOutput{
			PuzzleGame: game,
			Event:      utils.AllocPtr(constants.UserEventEnum.USER_MATCHED.String()),
			Opponent:   opponent,
		}
		err := s.coreService.PublishUserEvent(ctx, player.ID, userEvent)
		if err != nil {
			zlog.Error(ctx, "Failed to publish USER_MATCHED event", err)
		}
	}
}
