package puzzleGame

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) CancelPuzzleGame(ctx context.Context, gameId primitive.ObjectID) (*bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return utils.AllocPtr(false), err
	}

	zlog.Debug(ctx, "Cancelling game", zap.String("gameID", gameId.Hex()))

	game, err := s.GetPuzzleGameByID(ctx, gameId)
	if err != nil {
		return utils.AllocPtr(false), fmt.Errorf("Failed to update get by id ")
	}

	var opponentId primitive.ObjectID

	for _, player := range game.Players {
		if player.UserID != userID {
			opponentId = player.UserID
			break
		}
	}

	cancelGameOutput := models.GameCanceledOutput{
		GameID:     utils.AllocPtr(gameId.Hex()),
		CreatorID:  utils.AllocPtr(game.CreatedBy.Hex()),
		OpponentID: utils.AllocPtr(opponentId.Hex()),
		Status:     utils.AllocPtr(models.ChallengeStatuCancelled),
	}

	game.GameStatus = models.PuzzleGameStatusCancelled

	err = s.puzzleGameRepo.UpdatePuzzleGame(ctx, game)
	if err != nil {
		return utils.AllocPtr(false), fmt.Errorf("Failed to update game status ")
	}

	err = s.puzzleGameCache.SetPuzzleGame(ctx, game)
	if err != nil {
		return utils.AllocPtr(false), fmt.Errorf("Failed to set game in cache ")
	}

	err = s.coreService.PublishUserEvent(ctx, userID, cancelGameOutput)
	if err != nil {
		return utils.AllocPtr(false), fmt.Errorf("failed to publish user event to curr user")
	}

	err = s.coreService.PublishUserEvent(ctx, opponentId, cancelGameOutput)
	if err != nil {
		return utils.AllocPtr(false), fmt.Errorf("failed to publish user event to opponent user")
	}

	return utils.AllocPtr(true), nil
}
