package puzzleGame

import (
	"context"
	"fmt"
	"math/rand/v2"
	"time"

	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/constants"
	puzzleGameUtils "matiksOfficial/matiks-server-go/internal/domain/puzzleGame/utils"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) ChallengeUserForPuzzleGame(ctx context.Context, challengeUserInput *models.ChallengeUserForPuzzleGameInput) (*models.PuzzleGame, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	zlog.Debug(ctx, "Challenging user", zap.String("userID", userID.Hex()))

	if err := puzzleGameUtils.ValidatePuzzleGameConfig(challengeUserInput.GameConfig); err != nil {
		return nil, fmt.Errorf("invalid game configuration: %w", err)
	}

	gameConfig := models.PuzzleGameConfig{
		TimeLimit:  challengeUserInput.GameConfig.TimeLimit,
		NumPlayers: challengeUserInput.GameConfig.NumPlayers,
		GameType:   challengeUserInput.GameConfig.GameType,
	}

	currentUser, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	if currentUser.RatingV2 == nil {
		currentUser.RatingV2 = &models.UserRating{
			GlobalRating: currentUser.Rating,
			PuzzleRating: utils.AllocPtr(constants.DefaultPuzzleRating),
		}
		err = s.userService.UpdateUserFromObject(ctx, currentUser)
		if err != nil {
			return nil, err
		}
	}

	if currentUser.RatingV2 != nil && currentUser.RatingV2.PuzzleRating == nil {
		currentUser.RatingV2.PuzzleRating = utils.AllocPtr(constants.DefaultPuzzleRating)
		err = s.userService.UpdateUserFromObject(ctx, currentUser)
		if err != nil {
			return nil, err
		}
	}

	currentUserRating := *currentUser.RatingV2.PuzzleRating

	puzzleGameType := models.PuzzleGameTypeCrossMathPuzzleDuel
	if challengeUserInput.GameConfig.GameType != nil {
		puzzleGameType = *challengeUserInput.GameConfig.GameType
	}

	game := &models.PuzzleGame{
		ID:         primitive.NewObjectID(),
		Players:    []*models.Player{{UserID: userID, Rating: &currentUserRating, Status: models.PlayerStatusInvited, StatikCoins: currentUser.StatikCoins}},
		Config:     &gameConfig,
		GameStatus: models.PuzzleGameStatusCreated,
		GameType:   puzzleGameType,
		CreatedBy:  userID,
	}

	if challengeUserInput.UserID.Hex() == userID.Hex() {
		return nil, fmt.Errorf("cannot challenge yourself")
	}

	if challengeUserInput.UserID != nil {
		opponentUser, err := s.userService.GetUserByID(ctx, *challengeUserInput.UserID)
		if err != nil {
			return nil, err
		}
		if opponentUser.RatingV2 == nil {
			opponentUser.RatingV2 = &models.UserRating{
				GlobalRating: opponentUser.Rating,
				PuzzleRating: utils.AllocPtr(constants.DefaultPuzzleRating),
			}
			err = s.userService.UpdateUserFromObject(ctx, opponentUser)
			if err != nil {
				return nil, err
			}
		}

		if opponentUser.RatingV2 != nil && opponentUser.RatingV2.PuzzleRating == nil {
			opponentUser.RatingV2.PuzzleRating = utils.AllocPtr(constants.DefaultPuzzleRating)
			err = s.userService.UpdateUserFromObject(ctx, opponentUser)
			if err != nil {
				return nil, err
			}
		}

		opponentUserRating := *opponentUser.RatingV2.PuzzleRating

		game = &models.PuzzleGame{
			ID: primitive.NewObjectID(),
			Players: []*models.Player{
				{UserID: userID, Rating: &currentUserRating, Status: models.PlayerStatusInvited, StatikCoins: currentUser.StatikCoins},
				{UserID: *challengeUserInput.UserID, Rating: &opponentUserRating, StatikCoins: opponentUser.StatikCoins},
			},
			Config:     &gameConfig,
			GameStatus: models.PuzzleGameStatusCreated,
			GameType:   puzzleGameType,
			CreatedBy:  userID,
			CreatedAt:  utils.AllocPtr(time.Now()),
			UpdatedAt:  utils.AllocPtr(time.Now()),
		}
	}

	savedGame, err := s.puzzleGameRepo.CreatePuzzleGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Error creating game:", err)
		return nil, err
	}

	err = s.PublishChallengeEvent(ctx, savedGame, string(models.ChallengeStatusChallengeSent))
	if err != nil {
		zlog.Error(ctx, "Error publishing challenge event:", err)
	}

	if challengeUserInput.UserID != nil {
		go s.acceptChallengeIfBot(utils.DeriveContextWithoutCancel(ctx), savedGame, *challengeUserInput.UserID)
		go s.handleChallengeExpiration(utils.DeriveContextWithoutCancel(ctx), savedGame.ID)
	}

	return savedGame, nil
}

func (s *service) handleChallengeExpiration(ctx context.Context, gameID primitive.ObjectID) {
	timer := time.NewTimer(20 * time.Second)
	defer timer.Stop()

	select {
	case <-timer.C:
		game, err := s.puzzleGameRepo.GetPuzzleGameByID(ctx, gameID)
		if err != nil {
			zlog.Error(ctx, "Error retrieving game for expiration check:", err)
			return
		}

		if game.GameStatus == models.PuzzleGameStatusCreated {
			game.GameStatus = models.PuzzleGameStatusCancelled
			err = s.puzzleGameRepo.UpdatePuzzleGame(ctx, game)
			if err != nil {
				zlog.Error(ctx, "Error updating expired game:", err)
				return
			}

			err = s.PublishChallengeEvent(ctx, game, string(models.ChallengeStatusChallengeExpired))
			if err != nil {
				zlog.Error(ctx, "Error publishing challenge expiration event:", err)
			}
		}
	case <-ctx.Done():
		return
	}
}

func (s *service) acceptChallengeIfBot(ctx context.Context, game *models.PuzzleGame, userID primitive.ObjectID) {
	gameID := game.ID
	user, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Error retrieving user:", err)
		return
	}
	if (user.IsBot == nil && user.IsHumanBot == nil) ||
		(user.IsBot == nil && user.IsHumanBot != nil && !*user.IsHumanBot) ||
		(user.IsBot != nil && !*user.IsBot && user.IsHumanBot == nil) ||
		(user.IsBot != nil && !*user.IsBot && user.IsHumanBot != nil && !*user.IsHumanBot) {
		return
	}

	if user.HumanBotConfig != nil && user.HumanBotConfig.InGame != nil && *user.HumanBotConfig.InGame {
		return
	}

	randT := 1.5 + 6*rand.Float64()
	timer := time.NewTimer(time.Duration(randT) * time.Second)
	defer timer.Stop()

	select {
	case <-timer.C:
		botCtx := context.WithValue(utils.DeriveContextWithoutCancel(ctx), constants.UserContextKey, user.ID.Hex())
		probAcceptance := rand.Float64()
		if probAcceptance < 0.4 && (user.HumanBotConfig == nil || (user.HumanBotConfig != nil && len(user.HumanBotConfig.LastOneHourOpponents[userID]) > 5)) {
			_, err := s.RejectChallengeOfPuzzleGame(botCtx, gameID)
			if err != nil {
				zlog.Error(ctx, "Error rejecting challenge if bot:", err)
				return
			}
			return
		}

		_, err := s.AcceptChallengeOfPuzzleGame(botCtx, gameID)
		if err != nil {
			zlog.Error(ctx, "Error accepting challenge if bot:", err)
			return
		}

	case <-ctx.Done():
		zlog.Info(ctx, "Accepting rematch if bot context cancelled", zap.String("gameID", gameID.Hex()), zap.String("userID", userID.Hex()))
	}
}
