package puzzleGame

import (
	"context"
	"fmt"

	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"

	"matiksOfficial/matiks-server-go/internal/constants"
	puzzleGameUtils "matiksOfficial/matiks-server-go/internal/domain/puzzleGame/utils"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func (s *service) CreatePuzzleGame(ctx context.Context, gameConfig *models.PuzzleGameConfigInput) (*models.PuzzleGame, error) {
	zlog.Info(ctx, "Creating Puzzle Game", zap.Any("gameConfigInput", gameConfig))

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Error retrieving active puzzle games:", err)
		return nil, err
	}

	if err := puzzleGameUtils.ValidatePuzzleGameConfig(gameConfig); err != nil {
		return nil, fmt.Errorf("invalid puzzle game configuration: %w", err)
	}

	zlog.Debug(ctx, "Create PuzzleGame", zap.String("userID", userID.Hex()))

	if gameConfig.GameType != nil && *gameConfig.GameType == models.PuzzleGameTypeCrossMathPuzzleDuel {
		zlog.Debug(ctx, "Game type play online, handled, should not reach here")
	}

	findFilter := bson.M{
		"gameStatus": bson.M{"$nin": []string{string(constants.GameStatusEnum.ENDED), string(constants.GameStatusEnum.CANCELLED)}},
		"players": bson.M{
			"$elemMatch": bson.M{
				"userId": userID,
				"status": constants.PlayerStatusEnum.ACCEPTED,
			},
		},
	}

	activeGames, err := s.puzzleGameRepo.FindMany(ctx, findFilter)
	if err != nil {
		zlog.Error(ctx, "Error finding games:", err)
		return nil, err
	}

	if len(activeGames) > 1 {
		for _, game := range activeGames {
			game.GameStatus = models.PuzzleGameStatusCancelled
			err := s.puzzleGameRepo.UpdatePuzzleGame(ctx, game)
			if err != nil {
				zlog.Error(ctx, "Error cancelling game:", err)
			}
		}
	}

	currentUser, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	puzzleGameConfig := &models.PuzzleGameConfig{
		TimeLimit:          gameConfig.TimeLimit,
		NumPlayers:         gameConfig.NumPlayers,
		GameType:           gameConfig.GameType,
		DifficultyLevel:    gameConfig.DifficultyLevel,
		MaxTimePerQuestion: gameConfig.MaxTimePerQuestion,
		NumOfQuestions:     gameConfig.NumOfQuestions,
	}

	if currentUser.RatingV2 == nil {
		currentUser.RatingV2 = &models.UserRating{
			GlobalRating: currentUser.Rating,
			PuzzleRating: utils.AllocPtr(constants.DefaultPuzzleRating),
		}
		err = s.userService.UpdateUserFromObject(ctx, currentUser)
		if err != nil {
			return nil, err
		}
	}

	currentUserRating := gameutils.GetPlayerRatingByGameType(currentUser, models.PuzzleGameTypeCrossMathPuzzleDuel)

	players := []*models.Player{
		{
			UserID:      userID,
			Status:      models.PlayerStatusAccepted,
			Rating:      &currentUserRating,
			StatikCoins: currentUser.StatikCoins,
			TimeLeft:    gameConfig.TimeLimit,
		},
	}

	newGame := models.PuzzleGame{
		CreatedBy:  userID,
		Config:     puzzleGameConfig,
		GameType:   *gameConfig.GameType,
		GameStatus: models.PuzzleGameStatusCreated,
		Players:    players,
	}

	createdGame, gameCreationError := s.puzzleGameRepo.CreatePuzzleGame(ctx, &newGame)
	if gameCreationError != nil {
		zlog.Error(ctx, "Error creating game:", gameCreationError)
		return nil, gameCreationError
	}

	err = s.puzzleGameCache.SetPuzzleGame(ctx, createdGame)
	if err != nil {
		zlog.Error(ctx, "Failed to save game in cache :", err)
		return nil, err
	}

	return createdGame, nil
}
