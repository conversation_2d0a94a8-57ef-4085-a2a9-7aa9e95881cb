package puzzleGame

import (
	"context"
	"fmt"
	"math"

	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/domain/userStreak"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/constants"
	puzzleGameUtils "matiksOfficial/matiks-server-go/internal/domain/puzzleGame/utils"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) EndPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	game, err := s.GetPuzzleGameByID(ctx, gameID)
	if err != nil {
		return nil, fmt.Errorf("failed to get game: %w", err)
	}

	zlog.Debug(ctx, "Ending game", zap.String("gameID", gameID.Hex()))

	if game == nil {
		return nil, fmt.Errorf("game not found")
	}

	zlog.Debug(ctx, "Game end event", zap.String("gameID", gameID.Hex()))

	minValueForLooserRatingChange, maxValueForWinnerRatingChange := puzzleGameUtils.GetMinMaxValueForRatingChange(game.GameType)

	gameStatus := game.GameStatus
	questions := game.Questions
	startTime := game.StartTime

	if gameStatus != models.PuzzleGameStatusStarted {
		if gameStatus == models.PuzzleGameStatusCancelled {
			game.GameStatus = models.PuzzleGameStatusEnded
			return game, nil
		}

		zlog.Info(ctx, "Game is not in a started state, skipping end game")
		return nil, nil
	}

	game.GameStatus = models.PuzzleGameStatusEnded
	err = s.puzzleGameCache.SetPuzzleGame(ctx, game)
	if err != nil {
		return nil, err
	}

	sortedLeaderboard := puzzleGameUtils.UpdateLeaderboardRanksInPuzzleGame(game)

	if len(sortedLeaderboard) < 2 {
		return nil, fmt.Errorf("not enough players")
	}

	hasSomeoneNotSubmittedAnyQuestion := *sortedLeaderboard[0].TotalPoints == 0 || *sortedLeaderboard[1].TotalPoints == 0
	isTie := *sortedLeaderboard[0].TotalPoints == *sortedLeaderboard[1].TotalPoints
	bothNotSubmittedAnyQuestion := hasSomeoneNotSubmittedAnyQuestion && isTie

	winnerID := sortedLeaderboard[0].UserID
	loserID := sortedLeaderboard[1].UserID

	if winnerID == nil || loserID == nil {
		return nil, fmt.Errorf("invalid player IDs")
	}

	winner, err := s.userService.GetUserByID(ctx, *winnerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get winner: %w", err)
	}

	loser, err := s.userService.GetUserByID(ctx, *loserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get loser: %w", err)
	}

	if winner == nil || loser == nil {
		return nil, fmt.Errorf("players not found")
	}

	if winner.RatingV2 == nil {
		winner.RatingV2 = &models.UserRating{
			GlobalRating: winner.Rating,
			PuzzleRating: utils.AllocPtr(constants.DefaultPuzzleRating),
		}
	}

	if winner.RatingV2 != nil && winner.RatingV2.PuzzleRating == nil {
		winner.RatingV2.PuzzleRating = utils.AllocPtr(constants.DefaultPuzzleRating)
	}

	if loser.RatingV2 == nil {
		loser.RatingV2 = &models.UserRating{
			GlobalRating: winner.Rating,
			PuzzleRating: utils.AllocPtr(constants.DefaultPuzzleRating),
		}
	}

	if loser.RatingV2 != nil && loser.RatingV2.PuzzleRating == nil {
		loser.RatingV2.PuzzleRating = utils.AllocPtr(constants.DefaultPuzzleRating)
	}

	expectedWinnerScore := 1 / (1 + math.Pow(10, (float64(*loser.RatingV2.PuzzleRating)-float64(*winner.RatingV2.PuzzleRating))/400))
	expectedLoserScore := 1 / (1 + math.Pow(10, (float64(*winner.RatingV2.PuzzleRating)-float64(*loser.RatingV2.PuzzleRating))/400))

	maxScoreDifference := 0
	for _, questionObj := range questions {
		if len(questionObj.Submissions) > 0 {
			maxScoreDifference += 4
		}
	}

	scoreDifference := math.Abs(*sortedLeaderboard[0].TotalPoints - *sortedLeaderboard[1].TotalPoints)
	normalizedScoreDifference := math.Min(math.Max(scoreDifference/math.Max(float64(maxScoreDifference), 1), 0), 1)
	adjustmentFactor := 0.2 + 1.6*normalizedScoreDifference
	KFactor := puzzleGameUtils.GetPuzzleGameKFactor(game.Config)

	var winnerRatingChange, loserRatingChange int
	if !hasSomeoneNotSubmittedAnyQuestion {
		if isTie {
			winnerRatingChange = int(math.Max(1, KFactor*(0.5-expectedWinnerScore)*adjustmentFactor))
			loserRatingChange = int(math.Min(-1, KFactor*(0.5-expectedLoserScore)*adjustmentFactor))
		} else {
			winnerRatingChange = int(math.Max(1, KFactor*(1-expectedWinnerScore)*adjustmentFactor))
			loserRatingChange = int(math.Min(-1, KFactor*(0-expectedLoserScore)*adjustmentFactor))
		}
	} else {
		winnerRatingChange = 0
		loserRatingChange = 0
		if bothNotSubmittedAnyQuestion {
			loserRatingChange = 0
		}
	}

	winnerRatingChange = min(winnerRatingChange, maxValueForWinnerRatingChange)
	loserRatingChange = max(loserRatingChange, minValueForLooserRatingChange)

	winnerHighestRating := int(math.Max(float64(*winner.Rating), float64(*winner.Rating+winnerRatingChange)))
	if winner.Stats != nil {
		winnerHighestRating = int(math.Max(float64(winnerHighestRating), float64(winner.Stats.Hr)))
	}

	loserHighestRating := int(math.Max(float64(*loser.Rating), float64(*loser.Rating+loserRatingChange)))
	if loser.Stats != nil {
		loserHighestRating = int(math.Max(float64(loserHighestRating), float64(loser.Stats.Hr)))
	}

	if *loser.RatingV2.PuzzleRating+loserRatingChange < 100 || *loser.RatingV2.PuzzleRating+loserRatingChange > 5000 {
		loserRatingChange = 0
	}
	if *winner.RatingV2.PuzzleRating+winnerRatingChange < 100 || *winner.RatingV2.PuzzleRating+winnerRatingChange > 5000 {
		winnerRatingChange = 0
	}

	winnerGame := &models.UserGame{
		ID:         utils.AllocPtr(gameID.Hex()),
		ST:         startTime,
		IsWinner:   utils.AllocPtr(true),
		OpponentID: loserID,
	}

	loserGame := &models.UserGame{
		ID:         utils.AllocPtr(gameID.Hex()),
		ST:         startTime,
		IsWinner:   utils.AllocPtr(false),
		OpponentID: winnerID,
	}

	gameutils.UpdateUserStats(winner, loser, winnerHighestRating, winnerGame)
	gameutils.UpdateUserStats(loser, winner, loserHighestRating, loserGame)

	*winner.RatingV2.PuzzleRating += winnerRatingChange
	*loser.RatingV2.PuzzleRating += loserRatingChange

	err = s.userService.UpdateUserFromObject(ctx, winner)
	if err != nil {
		return nil, fmt.Errorf("failed to update winner: %w", err)
	}

	err = s.userService.UpdateUserFromObject(ctx, loser)
	if err != nil {
		return nil, fmt.Errorf("failed to update loser: %w", err)
	}

	sortedLeaderboard[0].RatingChange = utils.AllocPtr(winnerRatingChange)
	sortedLeaderboard[1].RatingChange = utils.AllocPtr(loserRatingChange)

	sortedLeaderboard[0].StatikCoinsEarned = puzzleGameUtils.GetStatikCoinsEarnedForWinnerInPuzzleGame(sortedLeaderboard[0])
	sortedLeaderboard[1].StatikCoinsEarned = puzzleGameUtils.GetStatikCoinsEarnedForLoserInPuzzleGame(sortedLeaderboard[1])

	game.LeaderBoard = sortedLeaderboard
	game.GameStatus = models.PuzzleGameStatusEnded

	err = s.publishPuzzleGameEvent(ctx, game, constants.GameEventEnum.GAME_ENDED.String())
	if err != nil {
		zlog.Error(ctx, "Failed to publish game ended event", err)
	}

	game.RematchRequestedBy = nil

	err = s.puzzleGameRepo.UpdatePuzzleGame(ctx, game)
	if err != nil {
		return nil, err
	}

	err = s.puzzleGameCache.SetPuzzleGame(ctx, game)
	if err != nil {
		return nil, err
	}

	go func() {
		if err := s.puzzleGameCache.DeletePuzzleGame(utils.DeriveContextWithoutCancel(ctx), gameID.Hex()); err != nil {
			zlog.Error(ctx, "Failed to remove game from cache", err)
		}
	}()

	go func() {
		if err := userStreak.UpdateUserStreak(utils.DeriveContextWithoutCancel(ctx), winner.ID, s.userRepo, true, s.notificationService, s.coreService, s.userStreakRepo); err != nil {
			zlog.Error(ctx, "Failed to update streak", err)
		}
	}()
	go func() {
		if err := userStreak.UpdateUserStreak(utils.DeriveContextWithoutCancel(ctx), loser.ID, s.userRepo, true, s.notificationService, s.coreService, s.userStreakRepo); err != nil {
			zlog.Error(ctx, "Failed to update streak", err)
		}
	}()

	activityType := gameutils.GetActivityTypeFromPuzzleGameType(game.GameType)

	go func() {
		if err := s.userService.UpdateUserStatikCoinsAndTimeSpent(utils.DeriveContextWithoutCancel(ctx), loser.ID, activityType, *sortedLeaderboard[1].StatikCoinsEarned, int64(*game.Config.TimeLimit)*1000, &game.ID); err != nil {
			zlog.Error(ctx, "Failed to update user activity of loser", err)
		}
	}()

	go func() {
		if err := s.userService.UpdateUserStatikCoinsAndTimeSpent(utils.DeriveContextWithoutCancel(ctx), winner.ID, activityType, *sortedLeaderboard[0].StatikCoinsEarned, int64(*game.Config.TimeLimit)*1000, &game.ID); err != nil {
			zlog.Error(ctx, "Failed to update user activity of winner", err)
		}
	}()

	return game, nil
}
