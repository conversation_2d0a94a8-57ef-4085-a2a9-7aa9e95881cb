package puzzleGame

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *service) GetMyCrossMathPuzzleRushStats(ctx context.Context) (*models.CrossMathPuzzleRushStats, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	stats := &models.CrossMathPuzzleRushStats{
		GlobalRank:  nil,
		FriendsRank: nil,
		BestAllTime: nil,
	}

	userStats, err := s.crossMathPuzzleRushRepo.GetUserPuzzleRushStats(ctx, userId)
	if err != nil {
		return nil, err
	}

	stats.BestAllTime = userStats.BestAllTime

	bestAllTime := 0
	if userStats.BestAllTime != nil {
		bestAllTime = *userStats.BestAllTime
	}

	globalRank, err := s.crossMathPuzzleRushRepo.GetMyGlobalRankInPuzzleRush(ctx, userId, bestAllTime)
	if err != nil {
		return nil, err
	}
	stats.GlobalRank = utils.AllocPtr(globalRank)

	friendsRank, err := s.crossMathPuzzleRushRepo.GetMyRankRelativeToFriend(ctx, userId, bestAllTime)
	if err != nil {
		return nil, err
	}
	stats.FriendsRank = utils.AllocPtr(friendsRank)

	return stats, nil
}
