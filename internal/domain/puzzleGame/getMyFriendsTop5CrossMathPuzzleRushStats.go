package puzzleGame

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *service) GetFriendsTop5CrossMathPuzzleRushStats(ctx context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	friendsTop5, err := s.crossMathPuzzleRushRepo.GetFriendsTop5InPuzzleRush(ctx, userId)
	if err != nil {
		return nil, err
	}

	return friendsTop5, nil
}
