package puzzleGame

import (
	"context"
	"errors"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

func (s *service) GetPuzzleGameByID(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	zlog.Debug(ctx, "Getting game by ID", zap.String("gameID", gameID.Hex()))

	cachedGame, err := s.puzzleGameCache.GetPuzzleGame(ctx, gameID.Hex())
	if err == nil && cachedGame != nil {
		zlog.Debug(ctx, "Game found in cache", zap.String("gameID", gameID.Hex()))
		return cachedGame, nil
	}

	game, err := s.puzzleGameRepo.GetPuzzleGameByID(ctx, gameID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zlog.Error(ctx, "Game not found", err, zap.String("gameID", gameID.Hex()))
			return nil, fmt.Errorf("game not found")
		}
		zlog.Error(ctx, "Error fetching game", err, zap.String("gameID", gameID.Hex()))
		return nil, fmt.Errorf("error fetching game: %v", err)
	}

	err = s.puzzleGameCache.SetPuzzleGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Error caching game", err, zap.String("gameID", gameID.Hex()))
	}

	return game, nil
}
