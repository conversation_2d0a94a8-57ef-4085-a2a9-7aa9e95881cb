package puzzleGame

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) GetPuzzleGameSeriesByID(ctx context.Context, gameID *primitive.ObjectID) (*models.GameSeries, error) {
	if gameID == nil {
		return nil, fmt.Errorf("gameID is required")
	}

	zlog.Debug(ctx, "Getting game series by ID", zap.String("gameID", gameID.Hex()))

	return s.puzzleGameSeriesRepo.GetPuzzleGameSeriesByID(ctx, *gameID)
}
