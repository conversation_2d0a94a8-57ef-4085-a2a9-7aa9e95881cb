package puzzleGame

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/scheduler"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *service) UserRepo() repository.UserRepository {
	return s.userRepo
}

func (s *service) publishSubmitPuzzleGameAnswerEvent(ctx context.Context, game *models.PuzzleGame, eventType string) error {
	channel := fmt.Sprintf("%s_%s", constants.SubscriptionPrefixEnum.PUZZLE_GAME_EVENT, game.ID.Hex())
	message := &models.SubmitPuzzleGameAnswerEvent{
		Event:      utils.AllocPtr(eventType),
		PuzzleGame: game,
	}
	err := s.ws.Publish(ctx, channel, message)
	if err != nil {
		return err
	}

	return err
}

func (s *service) publishPuzzleGameEvent(ctx context.Context, game *models.PuzzleGame, eventType string) error {
	channel := fmt.Sprintf("%s_%s", constants.SubscriptionPrefixEnum.PUZZLE_GAME_EVENT, game.ID.Hex())
	message := &models.PuzzleGameEventOutput{
		Event:      utils.AllocPtr(eventType),
		PuzzleGame: game,
	}

	err := s.ws.Publish(ctx, channel, message)
	if err != nil {
		return err
	}

	channel = fmt.Sprintf("%s_%s", constants.SubscriptionPrefixEnum.PUZZLE_GAME_EVENT, game.ID.Hex())
	return s.ws.Publish(ctx, channel, message)
}

func (s *service) scheduleEndPuzzleGame(ctx context.Context, game *models.PuzzleGame) {
	if game.Config == nil {
		return
	}

	if game.Config.TimeLimit != nil {
		startTime := *game.StartTime
		gameEndTime := startTime.Add(time.Duration(*game.Config.TimeLimit)*time.Second + 250*time.Millisecond)
		actionType := models.EndPuzzleGame

		err := scheduler.Schedule(ctx, s.sortedSet, &models.SchedulerPayload{
			Type: actionType,
			Action: models.EndPuzzleGameActionPayload{
				GameID:     game.ID,
				StartTime:  startTime,
				GameConfig: game.Config,
			},
			ContextMap: utils.GetContextValuesMap(ctx),
		}, gameEndTime)
		if err != nil {
			zlog.Error(ctx, "Failed to schedule end game", err)
			return
		}
	}
}
