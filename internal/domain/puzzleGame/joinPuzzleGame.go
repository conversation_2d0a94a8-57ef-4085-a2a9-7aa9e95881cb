package puzzleGame

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) JoinPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Error retrieving active games:", err)
		return nil, err
	}

	if gameID == primitive.NilObjectID {
		return nil, fmt.Errorf("game id cannot be nil")
	}

	zlog.Debug(ctx, "JoinGame", zap.String("userID", userID.Hex()), zap.String("gameID", gameID.Hex()))

	game, err := s.GetPuzzleGameByID(ctx, gameID)
	if err != nil || game == nil {
		return nil, fmt.Errorf("game not found")
	}

	zlog.Info(ctx, "JoinGame", zap.String("userID", userID.Hex()), zap.String("gameID", game.ID.Hex()))
	if game.Config == nil || game.Config.NumPlayers == nil {
		return nil, fmt.Errorf("invalid game config")
	}

	numPlayers := game.Config.NumPlayers
	acceptedPlayers := filterAcceptedPlayers(game.Players)

	zlog.Info(ctx, "JoinGame Resolver", zap.Any("acceptedPlayers", acceptedPlayers))
	currentUser, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("current user not found: %v", err)
	}

	if currentUser.RatingV2 == nil {
		currentUser.RatingV2 = &models.UserRating{
			PuzzleRating: utils.AllocPtr(constants.DefaultPuzzleRating),
			GlobalRating: currentUser.Rating,
		}
		err = s.userService.UpdateUserFromObject(ctx, currentUser)
		if err != nil {
			return nil, fmt.Errorf("failed to update user rating in puzzle")
		}
	}

	if currentUser.RatingV2 != nil && currentUser.RatingV2.PuzzleRating == nil {
		currentUser.RatingV2.PuzzleRating = utils.AllocPtr(constants.DefaultPuzzleRating)
		err = s.userService.UpdateUserFromObject(ctx, currentUser)
		if err != nil {
			return nil, fmt.Errorf("failed to update user rating in puzzle")
		}
	}

	rating := currentUser.RatingV2.PuzzleRating

	currPlayer := findPlayerByID(game.Players, userID)

	if currPlayer == nil && (len(acceptedPlayers) >= *numPlayers) {
		zlog.Error(ctx, "JoinGame: ", fmt.Errorf("game is already full"))
		return game, nil
	}

	if currPlayer != nil && currPlayer.Status == models.PlayerStatusAccepted {
		zlog.Error(ctx, "JoinGame: ", fmt.Errorf("player already joined this game"))
		return game, nil
	}

	var updatedPlayers []*models.Player

	if currPlayer != nil {
		updatedPlayers = updatePlayerStatus(game.Players, userID, models.PlayerStatusAccepted)
	} else {
		player := models.Player{
			UserID:      userID,
			Status:      models.PlayerStatusAccepted,
			Rating:      rating,
			StatikCoins: currentUser.StatikCoins,
			TimeLeft:    game.Config.TimeLimit,
		}
		updatedPlayers = append(game.Players, &player)
	}

	game.Players = updatedPlayers

	if len(updatedPlayers) >= *numPlayers {
		game.GameStatus = models.PuzzleGameStatusReady
	}

	err = s.publishPuzzleGameEvent(ctx, game, constants.GameEventEnum.USER_JOINED.String())
	if err != nil {
		return nil, err
	}

	err = s.puzzleGameCache.SetPuzzleGame(ctx, game)
	if err != nil {
		return nil, err
	}

	return game, nil
}

func filterAcceptedPlayers(players []*models.Player) []models.Player {
	var accepted []models.Player
	for _, player := range players {
		if player.Status == models.PlayerStatusAccepted {
			accepted = append(accepted, *player)
		}
	}
	return accepted
}

func findPlayerByID(players []*models.Player, userID primitive.ObjectID) *models.Player {
	for _, player := range players {
		if player.UserID == userID {
			return player
		}
	}
	return nil
}

func updatePlayerStatus(players []*models.Player, userID primitive.ObjectID, status models.PlayerStatus) []*models.Player {
	for i, player := range players {
		if player.UserID == userID {
			players[i].Status = status
			return players
		}
	}
	return players
}
