package puzzleGame

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *service) PublishChallengeEvent(ctx context.Context, game *models.PuzzleGame, eventType string) error {
	for _, player := range game.Players {
		opponentID := s.GetOpponentID(game.Players, player.UserID)
		opponent, err := s.userService.GetUserByID(ctx, opponentID)
		if err != nil {
			return fmt.Errorf("failed to get opponent user details: %w", err)
		}
		opponentData := &models.User{
			ID:              opponentID,
			Username:        opponent.Username,
			Rating:          opponent.Rating,
			RatingV2:        opponent.RatingV2,
			ProfileImageURL: opponent.ProfileImageURL,
		}

		message := &models.ChallengeForPuzzleGameOutput{
			GameID:       game.ID,
			GameConfig:   game.Config,
			CreatedAt:    *game.CreatedAt,
			ChallengedBy: game.CreatedBy,
			Status:       utils.AllocPtr(models.ChallengeStatus(eventType)),
			Opponent:     opponentData,
		}

		err = s.coreService.PublishUserEvent(ctx, player.UserID, message)
		if err != nil {
			return fmt.Errorf("failed to publish challenge event: %w", err)
		}
	}

	return nil
}
