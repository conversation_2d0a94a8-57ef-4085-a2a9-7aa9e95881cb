package puzzleGame

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) publishRematchEventToBothUsers(ctx context.Context, game *models.PuzzleGame, requestedByUserID primitive.ObjectID, eventType string, newGameID *string) error {
	for _, player := range game.Players {
		opponentID := s.GetOpponentID(game.Players, player.UserID)
		opponent, err := s.userService.GetUserByID(ctx, opponentID)
		if err != nil {
			zlog.Info(ctx, "Failed to get opponent user details", zap.Error(err))
			return fmt.Errorf("failed to get opponent user details: %w", err)
		}

		channel := fmt.Sprintf("%s_%s", constants.SubscriptionPrefixEnum.USER_EVENT, player.UserID.Hex())

		zlog.Debug(ctx, "Publishing Rematch Request event",
			zap.String("channel", channel),
			zap.String("gameID", game.ID.Hex()),
			zap.String("userID", player.UserID.Hex()),
			zap.String("eventType", eventType))

		message := &models.RematchRequestOutput{
			GameID:      utils.AllocPtr(game.ID.Hex()),
			RequestedBy: utils.AllocPtr(requestedByUserID.Hex()),
			Status:      utils.AllocPtr(eventType),
			NewGameID:   newGameID,
			User:        opponent,
			WaitingTime: utils.AllocPtr(constants.AutoCloseRematchRequestDuration),
			GameType:    utils.AllocPtr(string(constants.RematchRequestGameTypePUZZLE)),
		}

		err = s.coreService.PublishUserEvent(ctx, player.UserID, message)
		if err != nil {
			zlog.Info(ctx, "Failed to publish Rematch Request event", zap.Error(err))
			return fmt.Errorf("failed to publish Rematch Request event: %w", err)
		}

		zlog.Info(ctx, "Rematch event published successfully",
			zap.String("channel", channel),
			zap.String("gameID", game.ID.Hex()),
			zap.String("userID", player.UserID.Hex()),
			zap.String("eventType", eventType))
	}

	return nil
}

func (s *service) GetOpponentID(players []*models.Player, currentUserID primitive.ObjectID) primitive.ObjectID {
	for _, player := range players {
		if player.UserID != currentUserID {
			return player.UserID
		}
	}
	return primitive.NilObjectID
}
