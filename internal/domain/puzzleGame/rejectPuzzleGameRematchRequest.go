package puzzleGame

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) RejectRematchOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	zlog.Debug(ctx, "Rejecting rematch request", zap.String("userID", userID.Hex()), zap.String("gameID", gameID.Hex()))

	game, err := s.puzzleGameRepo.FindOne(ctx, bson.M{"_id": gameID})
	if err != nil || game == nil {
		return false, fmt.Errorf("game not found")
	}

	if game.RematchRequestedBy == nil {
		return false, fmt.Errorf("no rematch request for this game")
	}

	if *game.RematchRequestedBy == userID {
		return false, fmt.Errorf("you cannot reject your own rematch request")
	}

	update := bson.M{
		"$unset": bson.M{
			"rematchRequestedBy": 1,
		},
	}

	filter := bson.M{"_id": gameID}
	err = s.puzzleGameRepo.UpdateOne(ctx, filter, update)
	if err != nil {
		zlog.Info(ctx, "Error deleting RematchRequestedBy field after auto-closing rematch request", zap.Error(err))
		return false, err
	}
	err = s.publishRematchEventToBothUsers(ctx, game, *game.RematchRequestedBy, constants.RematchGameEnum.REMATCH_REJECTED.String(), nil)
	if err != nil {
		return false, err
	}

	return true, nil
}
