package puzzleGame

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) RemovePlayerFromPuzzleGame(ctx context.Context, gameID, playerID primitive.ObjectID) (bool, error) {
	zlog.Info(ctx, "Removing player from game", zap.String("gameID", gameID.Hex()), zap.String("playerID", playerID.Hex()))

	creatorId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, fmt.Errorf("Failed to get User Details from Context ")
	}

	game, err := s.GetPuzzleGameByID(ctx, gameID)
	if err != nil {
		return false, fmt.Errorf("failed to get game: %w", err)
	}

	if game == nil {
		return false, fmt.Errorf("game not found")
	}

	if game.CreatedBy != creatorId {
		return false, fmt.Errorf("only Creator can remove players from the game")
	}

	if game.GameStatus != models.PuzzleGameStatusCreated {
		return false, fmt.Errorf("cannot remove player from a game that has already started or ended")
	}

	playerIndex := -1
	for i, player := range game.Players {
		if player.UserID == playerID {
			playerIndex = i
			break
		}
	}

	if playerIndex == -1 {
		return false, fmt.Errorf("player not found in the game")
	}

	game.Players = append(game.Players[:playerIndex], game.Players[playerIndex+1:]...)

	err = s.puzzleGameRepo.UpdatePuzzleGame(ctx, game)
	if err != nil {
		return false, fmt.Errorf("failed to update game: %w", err)
	}

	err = s.puzzleGameCache.SetPuzzleGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Failed to update cached game", err)
	}

	err = s.publishPuzzleGameEvent(ctx, game, constants.GameEventEnum.PLAYER_REMOVED.String())
	if err != nil {
		zlog.Error(ctx, "Failed to publish player removed event", err)
	}

	return true, nil
}
