package puzzleGame

import (
	"context"
	"fmt"
	"math/rand/v2"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/puzzleGame/utils/puzzleGameBotutils"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) RequestRematchForPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	zlog.Debug(ctx, "Requesting rematch", zap.String("userID", userID.Hex()), zap.String("gameID", gameID.Hex()))

	puzzleGame, err := s.puzzleGameRepo.FindOne(ctx, bson.M{"_id": gameID})
	if err != nil || puzzleGame == nil {
		return false, fmt.Errorf("puzzleGame not found")
	}

	if puzzleGame.GameStatus != models.PuzzleGameStatusEnded {
		return false, fmt.Errorf("rematch can only be requested for ended games")
	}

	if puzzleGame.RematchRequestedBy != nil {
		return false, fmt.Errorf("rematch already requested for this puzzleGame")
	}

	isPlayerInGame := false
	for _, player := range puzzleGame.Players {
		if player.UserID == userID {
			isPlayerInGame = true
			break
		}
	}

	if !isPlayerInGame {
		return false, fmt.Errorf("only players in the puzzleGame can request a rematch")
	}

	puzzleGame.RematchRequestedBy = &userID

	err = s.puzzleGameRepo.UpdateOne(ctx, bson.M{"_id": gameID}, bson.M{"$set": bson.M{"rematchRequestedBy": userID}})
	if err != nil {
		return false, err
	}

	err = s.publishRematchEventToBothUsers(ctx, puzzleGame, userID, constants.RematchGameEnum.REMATCH_REQUESTED.String(), nil)
	if err != nil {
		return false, err
	}

	go s.acceptRematchIfBot(utils.DeriveContextWithoutCancel(ctx), puzzleGame, userID)
	go s.autoCloseRematchRequest(utils.DeriveContextWithoutCancel(ctx), gameID, userID)

	zlog.Info(ctx, "Rematch requested", zap.String("gameID", gameID.Hex()), zap.String("requestedBy", userID.Hex()))
	return true, nil
}

func (s *service) autoCloseRematchRequest(ctx context.Context, gameID, requestedByUserID primitive.ObjectID) {
	timer := time.NewTimer(20 * time.Second)
	defer timer.Stop()

	select {
	case <-timer.C:
		puzzleGame, err := s.puzzleGameRepo.FindOne(ctx, bson.M{"_id": gameID})
		if err != nil || puzzleGame == nil {
			zlog.Info(ctx, "Error finding puzzleGame for auto-close", zap.Error(err))
			return
		}

		if puzzleGame.RematchRequestedBy != nil {
			puzzleGame.RematchRequestedBy = nil
			update := bson.M{
				"$unset": bson.M{
					"rematchRequestedBy": 1,
				},
			}

			filter := bson.M{"_id": gameID}
			err = s.puzzleGameRepo.UpdateOne(ctx, filter, update)
			if err != nil {
				zlog.Info(ctx, "Error deleting RematchRequestedBy field after auto-closing rematch request", zap.Error(err))
				return
			}

			err = s.publishRematchEventToBothUsers(ctx, puzzleGame, requestedByUserID, constants.RematchGameEnum.REMATCH_AUTO_CLOSED.String(), nil)
			if err != nil {
				zlog.Info(ctx, "Error publishing auto-close event", zap.Error(err))
			}
		}
	case <-ctx.Done():
		return
	}
}

func (s *service) acceptRematchIfBot(ctx context.Context, puzzleGame *models.PuzzleGame, userID primitive.ObjectID) {
	gameID := puzzleGame.ID
	opponentID := s.GetOpponentID(puzzleGame.Players, userID)
	user, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get user details", err)
		return
	}
	opponent, err := s.userService.GetUserByID(ctx, opponentID)
	if err != nil {
		zlog.Error(ctx, "Failed to get opponent user details", err)
		return
	}

	if (opponent.IsBot == nil && opponent.IsHumanBot == nil) ||
		(opponent.IsBot == nil && opponent.IsHumanBot != nil && !*opponent.IsHumanBot) ||
		(opponent.IsBot != nil && !*opponent.IsBot && opponent.IsHumanBot == nil) ||
		(opponent.IsBot != nil && !*opponent.IsBot && opponent.IsHumanBot != nil && !*opponent.IsHumanBot) {
		return
	}

	if opponent.HumanBotConfig != nil && opponent.HumanBotConfig.InGame != nil && *opponent.HumanBotConfig.InGame {
		return
	}

	probAcceptance := rand.Float64()
	if probAcceptance < 0.3 {
		return
	}

	randT := 1.5 + 3*rand.Float64()
	timer := time.NewTimer(time.Duration(randT) * time.Second)
	defer timer.Stop()

	select {
	case <-timer.C:
		zlog.Info(ctx, "Accepting rematch if bot", zap.String("gameID", gameID.Hex()), zap.String("userID", userID.Hex()))
		s.botRematch(ctx, puzzleGame, user, opponent)
	case <-ctx.Done():
		zlog.Info(ctx, "Accepting rematch if bot context cancelled", zap.String("gameID", gameID.Hex()), zap.String("userID", userID.Hex()))
	}
}

func (s *service) botRematch(ctx context.Context, puzzleGame *models.PuzzleGame, user, botUser *models.User) {
	gameID := puzzleGame.ID
	var seriesID *primitive.ObjectID
	if puzzleGame.SeriesID != nil {
		seriesID = puzzleGame.SeriesID
	} else {
		// Create a new puzzleGame series
		newSeries := &models.GameSeries{
			ID:        utils.AllocPtr(primitive.NewObjectID()),
			GameIds:   &[]primitive.ObjectID{gameID},
			PlayerIds: &[]primitive.ObjectID{user.ID, botUser.ID},
		}
		_, err := s.puzzleGameSeriesRepo.CreatePuzzleGameSeries(ctx, newSeries)
		if err != nil {
			return
		}
		seriesID = newSeries.ID
	}
	gameType := models.PuzzleGameTypeCrossMathPuzzleDuel
	if puzzleGame.Config != nil && puzzleGame.Config.GameType != nil {
		gameType = *puzzleGame.Config.GameType
	}

	config := models.PuzzleGameConfig{
		TimeLimit:  puzzleGame.Config.TimeLimit,
		NumPlayers: puzzleGame.Config.NumPlayers,
		GameType:   &gameType,
	}

	user, err := s.userService.GetUserByID(ctx, user.ID)
	if err != nil {
		zlog.Error(ctx, "Failed to get user details", err)
		return
	}
	botUser, err = s.userService.GetUserByID(ctx, botUser.ID)
	if err != nil {
		zlog.Error(ctx, "Failed to get bot user details", err)
		return
	}

	updatedUsers := []*models.User{user, botUser}
	updatedPlayers := s.getPlayersFromUsers(updatedUsers)

	newGame, err := s.createGameWithPlayers(ctx, updatedPlayers, config)
	if err != nil {
		return
	}

	seriesUpdate := bson.M{
		"$addToSet": bson.M{
			"gameIds": newGame.ID,
		},
	}
	err = s.puzzleGameSeriesRepo.UpdateOne(ctx, bson.M{"_id": seriesID}, seriesUpdate)
	if err != nil {
		return
	}

	update := bson.M{
		"$unset": bson.M{
			"rematchRequestedBy": 1,
		},
		"$set": bson.M{"seriesId": seriesID},
	}

	filter := bson.M{"_id": gameID}
	err = s.puzzleGameRepo.UpdateOne(ctx, filter, update)
	if err != nil {
		zlog.Error(ctx, "Error deleting RematchRequestedBy field after auto-closing rematch request", err)
		return
	}
	newGameID := newGame.ID.Hex()

	newGame.SeriesID = seriesID
	err = s.puzzleGameCache.SetPuzzleGame(ctx, newGame)
	if err != nil {
		return
	}

	err = s.publishRematchEventToBothUsers(ctx, puzzleGame, *puzzleGame.RematchRequestedBy, constants.RematchGameEnum.REMATCH_ACCEPTED.String(), &newGameID)
	if err != nil {
		return
	}

	s.scheduleEndPuzzleGame(ctx, newGame)

	go func() {
		err := puzzleGameBotutils.RunPuzzleGameBot(utils.DeriveContextWithoutCancel(ctx), newGame, botUser, s, s.cache)
		if err != nil {
			zlog.Error(ctx, "Error running bot after auto-close rematch request", err)
		}
	}()
}
