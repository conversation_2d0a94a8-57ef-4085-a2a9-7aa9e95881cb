package puzzleGame

import (
	"context"
	"sync"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/infrastructure/leaderboard"
	"matiksOfficial/matiks-server-go/internal/infrastructure/locks"
	"matiksOfficial/matiks-server-go/internal/infrastructure/queue"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/infrastructure/sortedset"
	"matiksOfficial/matiks-server-go/internal/infrastructure/websocket"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type service struct {
	ws                      websocket.Websocket
	puzzleGameCache         cache.PuzzleGameCache
	cache                   cache.Cache
	userService             domain.UserStore
	coreService             domain.CoreLogicStore
	userRepo                repository.UserRepository
	puzzleGameRepo          repository.PuzzleGameRepository
	puzzleGameSeriesRepo    repository.PuzzleGameSeriesRepository
	playersQueue            queue.Queue
	presetsService          domain.PresetsStore
	presetsRepo             repository.PresetsRepository
	crossMathPuzzleRushRepo repository.CrossMathPuzzleRushRepository
	globalLeaderboard       leaderboard.GlobalLeaderboard
	mx                      *sync.Map
	locks                   locks.Locks
	sortedSet               sortedset.SortedSet
	notificationService     domain.NotificationStore
	userStreakRepo          repository.UserStreakRepository
	botDetectionService     domain.BotDetectionStore
}

func NewPuzzleGameService(lc fx.Lifecycle, ws websocket.Websocket, userService domain.UserStore,
	repositoryFactory *repository.RepositoryFactory, cacheInstance cache.Cache, playersQueue queue.Queue,
	presetsService domain.PresetsStore, locks locks.Locks, sortedSet sortedset.SortedSet,
	globalLeaderboard leaderboard.GlobalLeaderboard, notificationService domain.NotificationStore,
	coreService domain.CoreLogicStore, botDetectionService domain.BotDetectionStore,
) domain.PuzzleGameStore {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting puzzle game service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down puzzle game service")
			return nil
		},
	})
	return &service{
		ws:                      ws,
		puzzleGameCache:         cache.NewPuzzleGameCacheWrapper(cacheInstance),
		cache:                   cacheInstance,
		userService:             userService,
		userRepo:                repositoryFactory.UserRepository,
		puzzleGameRepo:          repositoryFactory.PuzzleGameRepository,
		puzzleGameSeriesRepo:    repositoryFactory.PuzzleGameSeriesRepository,
		crossMathPuzzleRushRepo: repositoryFactory.CrossMathPuzzleRushRepository,
		playersQueue:            playersQueue,
		presetsService:          presetsService,
		presetsRepo:             repositoryFactory.PresetsRepository,
		mx:                      &sync.Map{},
		globalLeaderboard:       globalLeaderboard,
		locks:                   locks,
		sortedSet:               sortedSet,
		notificationService:     notificationService,
		coreService:             coreService,
		userStreakRepo:          repositoryFactory.UserStreakRepository,
		botDetectionService:     botDetectionService,
	}
}
