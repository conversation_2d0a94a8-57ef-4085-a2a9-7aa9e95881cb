package puzzleGame

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/puzzleGame/utils/puzzleGameBotutils"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/constants"
	puzzleGameUtils "matiksOfficial/matiks-server-go/internal/domain/puzzleGame/utils"
	"matiksOfficial/matiks-server-go/internal/domain/questions/questionsGenerator"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"go.uber.org/zap"
)

func (s *service) StartPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if gameID == primitive.NilObjectID {
		return nil, fmt.Errorf("gameID is nil")
	}

	var game *models.PuzzleGame
	game, err = s.GetPuzzleGameByID(ctx, gameID)
	if err != nil {
		return nil, fmt.Errorf("game not found")
	}

	if game.GameStatus == models.PuzzleGameStatusStarted {
		zlog.Debug(ctx, "PuzzleGame is already started", zap.String("gameID", gameID.Hex()))
		return game, nil
	}

	currPlayer := findPlayerByID(game.Players, userID)
	if currPlayer == nil {
		zlog.Debug(ctx, "current player found nil")
		return nil, fmt.Errorf("you cannot start the game as you are not a player of this game")
	}

	if game.GameStatus != models.PuzzleGameStatusReady {
		zlog.Info(ctx, "game cannot be started, current status is", zap.Any("gameStatus", game.GameStatus))
		return game, fmt.Errorf("game cannot be started, current status is %s", game.GameStatus)
	}

	game.GameStatus = models.PuzzleGameStatusStarted

	game.LeaderBoard = make([]*models.PuzzleLeaderboardEntry, len(game.Players))
	for i, player := range game.Players {
		leaderboardEntry := puzzleGameUtils.GetDefaultUserLeaderBoardStandInPuzzleGame(player.UserID)
		game.LeaderBoard[i] = &leaderboardEntry
	}

	players := game.Players

	if len(players) > 1 {
		avgRating := 0
		for _, player := range players {
			if player.Rating != nil {
				avgRating += *player.Rating
			}
		}
		avgRating = avgRating / len(players)
		questions := questionsGenerator.GeneratePuzzlesGameQuestion(avgRating)
		game.Questions = questions
	} else {
		return nil, fmt.Errorf("can not start game with single player")
	}

	timeAfter5sec := time.Now().Add(5 * time.Second)
	game.StartTime = &timeAfter5sec

	err = s.publishPuzzleGameEvent(ctx, game, constants.GameEventEnum.GAME_STARTED.String())
	if err != nil {
		return nil, err
	}

	ctx = context.WithValue(ctx, constants.GameIDKey, game.ID.Hex())

	s.scheduleEndPuzzleGame(ctx, game)

	err = s.puzzleGameCache.SetPuzzleGame(ctx, game)
	if err != nil {
		return nil, err
	}

	err = s.puzzleGameRepo.UpdatePuzzleGame(ctx, game)
	if err != nil {
		return nil, err
	}

	for _, player := range game.Players {
		user, err := s.userService.GetUserByID(ctx, player.UserID)
		if err != nil {
			return nil, err
		}
		if (user.IsBot != nil && *user.IsBot) || (user.IsHumanBot != nil && *user.IsHumanBot) {
			go func() {
				err := puzzleGameBotutils.RunPuzzleGameBot(utils.DeriveContextWithoutCancel(ctx), game, user, s, s.cache)
				if err != nil {
					zlog.Error(ctx, "Error running bot after Accept Challenge", err)
				}
			}()
		}
	}

	return game, nil
}
