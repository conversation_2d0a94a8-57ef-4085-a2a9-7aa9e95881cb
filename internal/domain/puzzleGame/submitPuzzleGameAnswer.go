package puzzleGame

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"matiksOfficial/matiks-server-go/internal/constants"
	puzzleGameUtils "matiksOfficial/matiks-server-go/internal/domain/puzzleGame/utils"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

const (
	POSITIVE_POINTS = 4
	NEGATIVE_POINTS = -1
)

func (s *service) SubmitPuzzleGameAnswer(ctx context.Context, answerInput *models.SubmitPuzzleGameAnswerInput) (*models.PuzzleGame, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("unauthorized: user not authenticated")
	}

	zlog.Debug(ctx, "SubmitPuzzleGameAnswer", zap.String("userID", userID.Hex()), zap.Any("answerInput", answerInput))

	if answerInput == nil {
		return nil, fmt.Errorf("answerInput cannot be nil")
	}

	gameID := answerInput.GameID

	if gameID == nil {
		return nil, fmt.Errorf("gameID cannot be nil")
	}

	if answerInput.QuestionID == nil {
		return nil, fmt.Errorf("questionID cannot be nil")
	}

	if answerInput.IsCorrect == nil {
		return nil, fmt.Errorf("isCorrect cannot be nil")
	}

	game, err := s.GetPuzzleGameByID(ctx, *answerInput.GameID)
	if err != nil {
		return nil, fmt.Errorf("failed to get game: %w", err)
	}

	if game == nil {
		return nil, fmt.Errorf("game not found")
	}

	zlog.Debug(ctx, "Game retrieved successfully",
		zap.String("gameID", gameID.Hex()),
		zap.String("gameStatus", string(game.GameStatus)))

	if game.GameStatus == models.PuzzleGameStatusCreated {
		zlog.Warn(ctx, "Attempted to submit answer for a game that hasn't started")
		return nil, systemErrors.ErrGameNotStarted
	}

	if game.GameStatus == models.PuzzleGameStatusEnded {
		zlog.Warn(ctx, "Attempted to submit answer for a game that is ended",
			zap.String("gameID", gameID.Hex()))
		return nil, systemErrors.ErrGameAlreadyEnded
	}

	if game.GameStatus == models.PuzzleGameStatusPaused {
		zlog.Info(ctx, "Resuming paused game",
			zap.String("gameID", gameID.Hex()))
		game.GameStatus = models.PuzzleGameStatusStarted
	}
	if game.StartTime == nil {
		zlog.Warn(ctx, "Game startTime is nil",
			zap.String("gameID", gameID.Hex()))
		return nil, fmt.Errorf("game startTime cannot be nil")
	}
	submissionTime := answerInput.TimeOfSubmission.Time()

	totalTimeOfLiveGame := int(submissionTime.Sub(*(game.StartTime)).Milliseconds())

	questionEntry := findQuestionByID(game.Questions, answerInput.QuestionID)
	if questionEntry == nil {
		zlog.Warn(ctx, "Question not found in game",
			zap.String("gameID", gameID.Hex()),
			zap.String("questionID", *answerInput.QuestionID))
		return game, nil
	}

	isCorrect := *answerInput.IsCorrect

	if game.Config.TimeLimit != nil && *game.Config.TimeLimit*1000 <= totalTimeOfLiveGame {
		zlog.Info(ctx, "Game time limit reached, ending game",
			zap.String("gameID", gameID.Hex()))
		game.EndTime = &submissionTime
		return game, nil
	}

	if hasUserSubmitted(questionEntry.Submissions, userID) {
		zlog.Warn(ctx, "User has already submitted an answer for this question",
			zap.String("gameID", gameID.Hex()))
		return game, nil
	}

	changeInMarks := calculateChangeInMarks(isCorrect)

	zlog.Debug(ctx, "Calculated change in marks", zap.Float64("changeInMarks", changeInMarks))

	puzzleGameUtils.UpdatePuzzleGameLeaderboard(game, userID, isCorrect, changeInMarks)

	points := NEGATIVE_POINTS
	if isCorrect {
		points = POSITIVE_POINTS
	}

	newSubmission := &models.PuzzleQuestionSubmission{
		UserID:            utils.AllocPtr(userID),
		TimeTaken:         &totalTimeOfLiveGame,
		Points:            utils.AllocPtr(int(changeInMarks)),
		SubmissionTime:    answerInput.TimeOfSubmission,
		IsCorrect:         &isCorrect,
		InCorrectAttempts: answerInput.InCorrectAttempts,
		SubmittedValues:   []*string{},
	}

	questionEntry.Submissions = append(questionEntry.Submissions, newSubmission)

	eventType := string(constants.GameEventEnum.INCORRECT_MOVE_MADE)
	if isCorrect {
		eventType = string(constants.GameEventEnum.CORRECT_MOVE_MADE)
	}

	err = s.publishSubmitPuzzleGameAnswerEvent(ctx, game, eventType)
	if err != nil {
		zlog.Error(ctx, "Failed to publish game ended event", err)
	}

	err = s.puzzleGameCache.SetPuzzleGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Failed to update cached game", err)
	}

	zlog.Info(ctx, "Answer submission completed successfully",
		zap.Bool("isCorrect", isCorrect),
		zap.Int("points", points),
		zap.String("eventType", eventType))

	return game, nil
}

func findQuestionByID(questions []*models.PuzzleGameQuestion, questionID *string) *models.PuzzleGameQuestion {
	if questionID == nil {
		return nil
	}

	for _, q := range questions {
		if q.Question == nil {
			continue
		}
		if q.Id.Hex() == *questionID {
			return q
		}
	}

	return nil
}

func hasUserSubmitted(submissions []*models.PuzzleQuestionSubmission, userID primitive.ObjectID) bool {
	for _, s := range submissions {
		if s.UserID != nil && *s.UserID == userID {
			return true
		}
	}
	return false
}

func calculateChangeInMarks(isCorrect bool) float64 {
	if !isCorrect {
		return float64(NEGATIVE_POINTS)
	}
	return POSITIVE_POINTS
}
