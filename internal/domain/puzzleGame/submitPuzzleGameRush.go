package puzzleGame

import (
	"context"
	"errors"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) SubmitPuzzleGameRush(ctx context.Context, input models.SubmitPuzzleRushGame) (*models.CrossMathPuzzleRush, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	now := time.Now()

	if input.Score == nil {
		return nil, nil
	}

	userPuzzleRushStats, err := s.crossMathPuzzleRushRepo.FindUserRushStatsById(ctx, userID)

	if err != nil || userPuzzleRushStats == nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			userPuzzleRushStats = &models.CrossMathPuzzleRush{
				ID:          primitive.NewObjectID(),
				UserID:      userID,
				BestAllTime: input.Score,
				CreatedAt:   utils.AllocPtr(now),
				UpdatedAt:   utils.AllocPtr(now),
			}
			err = s.crossMathPuzzleRushRepo.InsertOne(ctx, *userPuzzleRushStats)
			if err != nil {
				return nil, err
			}
			if input.Score != nil && input.TimeSpent != nil {
				coinsEarned := *input.Score * 2
				err = s.userService.UpdateUserStatikCoinsAndTimeSpent(ctx, userID, constants.ActivityTypeCrossMathPuzzleRush, coinsEarned, int64(*input.TimeSpent), &userPuzzleRushStats.ID)
				if err != nil {
					return nil, err
				}
			}
			userPuzzleRushStats.IsNewBestScore = utils.AllocPtr(true)
			return userPuzzleRushStats, nil
		}
		return nil, err
	}

	update := bson.M{
		"$set": bson.M{
			"updatedAt": now,
		},
	}
	userPuzzleRushStats.IsNewBestScore = utils.AllocPtr(false)
	if userPuzzleRushStats.BestAllTime == nil {
		userPuzzleRushStats.IsNewBestScore = utils.AllocPtr(true)
		userPuzzleRushStats.BestAllTime = input.Score
		update["$set"].(bson.M)["bestAllTime"] = input.Score
	}

	if userPuzzleRushStats.BestAllTime != nil && *input.Score > *userPuzzleRushStats.BestAllTime {
		update["$set"].(bson.M)["bestAllTime"] = input.Score
		userPuzzleRushStats.IsNewBestScore = utils.AllocPtr(true)
		userPuzzleRushStats.BestAllTime = input.Score
	}

	if len(update["$set"].(bson.M)) > 1 {
		err = s.crossMathPuzzleRushRepo.UpdateOne(
			ctx,
			bson.M{"_id": userPuzzleRushStats.ID},
			update,
		)
		if err != nil {
			return nil, err
		}
	}

	if input.Score != nil && input.TimeSpent != nil {
		coinsEarned := *input.Score * 3
		err = s.userService.UpdateUserStatikCoinsAndTimeSpent(ctx, userID, constants.ActivityTypeCrossMathPuzzleRush, coinsEarned, int64(*input.TimeSpent), &userPuzzleRushStats.ID)
		if err != nil {
			return nil, err
		}
	}

	return userPuzzleRushStats, nil
}
