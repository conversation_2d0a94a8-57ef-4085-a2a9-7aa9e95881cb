package puzzleGameBotutils

import (
	"context"
	"strconv"
	"strings"
	"time"

	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"

	"matiksOfficial/matiks-server-go/internal/domain/questions/questionsGenerator"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/middleware"
	"matiksOfficial/matiks-server-go/internal/models"
)

func RunPuzzleGameBot(ctx context.Context, puzzleGame *models.PuzzleGame, botUser *models.User, puzzleGameService domain.PuzzleGameStore, cache cache.Cache) error {
	ctx, span := middleware.WithSpan(ctx, "runBot")
	defer span.End()
	zlog.Debug(ctx, "Entering RunBot function", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", puzzleGame.ID.Hex()))

	if botUser.RatingV2 == nil {
		botUser.RatingV2 = &models.UserRating{
			GlobalRating: botUser.Rating,
			PuzzleRating: utils.AllocPtr(constants.DefaultPuzzleRating),
		}
	}

	botRating := gameutils.GetPlayerRatingByGameType(botUser, puzzleGame.GameType)
	startTime := *puzzleGame.StartTime
	maxGameDuration := time.Duration(*puzzleGame.Config.TimeLimit) * time.Second
	var elapsedTime time.Duration = 0

	zlog.Info(ctx, "Running Bot", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", puzzleGame.ID.Hex()))
	zlog.Debug(ctx, "Updating human bot cache", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", puzzleGame.ID.Hex()))

	err := UpdateHumanBotCache(botUser, cache, true)
	if err != nil {
		zlog.Error(ctx, "Failed to update human bot cache", err, zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", puzzleGame.ID.Hex()))
		return err
	}
	if botUser.IsHumanBot != nil && *botUser.IsHumanBot && botUser.HumanBotConfig != nil {
		zlog.Debug(ctx, "Using human bot configuration", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", puzzleGame.ID.Hex()), zap.Int("targetRating", botUser.HumanBotConfig.TargetRating))
		botRating = botUser.HumanBotConfig.TargetRating
	}

	zlog.Debug(ctx, "Starting question loop", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", puzzleGame.ID.Hex()), zap.Int("totalQuestions", len(puzzleGame.Questions)))
	for _, question := range puzzleGame.Questions {
		zlog.Debug(ctx, "Processing question", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", puzzleGame.ID.Hex()), zap.String("questionID", question.Id.Hex()))

		gridSize := 3 // default value
		numHints := 0 // default value

		// Extract gridSize and numHints from the question preset
		if question.Question != nil {
			parts := strings.Split(*question.Question, "|")
			if len(parts) > 0 {
				presetParts := strings.Split(parts[0], "_")
				if len(presetParts) == 3 && presetParts[0] == "CROSS-MATH" {
					if size, err := strconv.Atoi(presetParts[1]); err == nil {
						gridSize = size
					}
					if hints, err := strconv.Atoi(presetParts[2]); err == nil {
						numHints = hints
					}
				}
			}
		}

		expectedTime := questionsGenerator.GetExpectedTimeToSolveAPuzzleQuestion(botRating, gridSize, numHints)

		solveAfterTime := expectedTime - time.Since(startTime)

		if elapsedTime < maxGameDuration {
			if solveAfterTime > 0 {
				elapsedTime += solveAfterTime
				select {
				case <-time.After(solveAfterTime):
				case <-ctx.Done():
					return nil
				}
			}

			answerInput := models.SubmitPuzzleGameAnswerInput{
				GameID:            utils.AllocPtr(puzzleGame.ID),
				QuestionID:        utils.AllocPtr(question.Id.Hex()),
				IsCorrect:         utils.AllocPtr(true),
				TimeOfSubmission:  utils.AllocPtr(models.DateFromTime(time.Now())),
				InCorrectAttempts: utils.AllocPtr(0),
			}
			zlog.Debug(ctx, "Submitting answer by Bot", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", puzzleGame.ID.Hex()), zap.Any("answerInput", answerInput))
			botCtx := context.WithValue(ctx, constants.UserContextKey, botUser.ID.Hex())
			if _, err := puzzleGameService.SubmitPuzzleGameAnswer(botCtx, &answerInput); err != nil {
				zlog.Error(ctx, "Failed to submit answer by Bot", err, zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", puzzleGame.ID.Hex()))
				return err
			}
			zlog.Debug(ctx, "Answer submitted successfully", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", puzzleGame.ID.Hex()))

			startTime = time.Now()
		} else {
			zlog.Debug(ctx, "Max puzzleGame duration reached", zap.String("botUserID", botUser.ID.Hex()), zap.Duration("elapsedTime", elapsedTime), zap.String("gameID", puzzleGame.ID.Hex()))
			return nil
		}
	}
	zlog.Debug(ctx, "Exiting RunBot function", zap.String("botUserID", botUser.ID.Hex()), zap.String("gameID", puzzleGame.ID.Hex()))
	return nil
}
