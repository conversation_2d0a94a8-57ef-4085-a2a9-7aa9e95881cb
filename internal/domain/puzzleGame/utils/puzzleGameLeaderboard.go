package puzzleGameUtils

import (
	"sort"

	"matiksOfficial/matiks-server-go/internal/models"

	"github.com/thoas/go-funk"
)

func GetDefaultUserLeaderBoardStandInPuzzleGame(userID models.ObjectID) models.PuzzleLeaderboardEntry {
	correct := 0
	incorrect := 0
	totalPoints := 0.0
	rank := 0

	return models.PuzzleLeaderboardEntry{
		UserID:      &userID,
		Correct:     &correct,
		Incorrect:   &incorrect,
		TotalPoints: &totalPoints,
		Rank:        &rank,
	}
}

func UpdateLeaderboardRanksInPuzzleGame(game *models.PuzzleGame) []*models.PuzzleLeaderboardEntry {
	leaderboard := game.LeaderBoard
	players := game.Players

	sort.Slice(leaderboard, func(i, j int) bool {
		if *leaderboard[i].TotalPoints != *leaderboard[j].TotalPoints {
			return *leaderboard[i].TotalPoints > *leaderboard[j].TotalPoints
		}

		result := funk.Find(players, func(player *models.Player) bool {
			return player.UserID == *leaderboard[i].UserID
		})
		if result == nil {
			return false
		}
		playerI, ok := result.(*models.Player)
		if !ok {
			return false
		}
		playerJ, ok := funk.Find(players, func(player *models.Player) bool {
			return player.UserID == *leaderboard[j].UserID
		}).(*models.Player)
		if !ok {
			return false
		}

		return *playerI.Rating < *playerJ.Rating
	})

	for i := range leaderboard {
		rank := i + 1
		leaderboard[i].Rank = &rank
	}

	return leaderboard
}
