package puzzleGameUtils

import (
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"
)

func ValidatePuzzleGameConfig(config *models.PuzzleGameConfigInput) error {
	if config == nil {
		return fmt.Errorf("game configuration is required")
	}

	if config.TimeLimit == nil && config.NumOfQuestions == nil {
		return fmt.Errorf("time limit and number of questions cannot be nil together")
	}

	if config.NumOfQuestions != nil && (*config.NumOfQuestions < 1 || *config.NumOfQuestions > 100) {
		return fmt.Errorf("number of questions must be between 1 and 100")
	}

	if config.TimeLimit != nil && *config.TimeLimit < 30 {
		return fmt.Errorf("time limit must be at least 30 seconds")
	}
	if config.TimeLimit != nil && *config.TimeLimit > 600 {
		return fmt.Errorf("time limit must be less than 600 seconds")
	}

	if config.NumPlayers != nil && *config.NumPlayers > 50 {
		return fmt.Errorf("number of players must be between 1 and 50")
	}

	if (config.MaxTimePerQuestion != nil && config.TimeLimit != nil) && (*config.MaxTimePerQuestion < 1 || *config.MaxTimePerQuestion > *config.TimeLimit) {
		return fmt.Errorf("max time per question must be between 1 and time limit")
	}

	if config.GameType != nil && !config.GameType.IsValid() {
		return fmt.Errorf("invalid game type")
	}

	return nil
}
