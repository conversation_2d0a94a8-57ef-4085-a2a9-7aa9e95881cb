package puzzleGameUtils

import (
	"testing"

	"matiksOfficial/matiks-server-go/internal/models"
)

func TestValidatePuzzleGameConfig(t *testing.T) {
	tests := []struct {
		name    string
		config  *models.PuzzleGameConfigInput
		wantErr bool
		errMsg  string
	}{
		{
			name:    "nil config",
			config:  nil,
			wantErr: true,
			errMsg:  "game configuration is required",
		},
		{
			name: "nil time limit and num of questions",
			config: &models.PuzzleGameConfigInput{
				NumPlayers: intPtr(10),
			},
			wantErr: true,
			errMsg:  "time limit and number of questions cannot be nil together",
		},
		{
			name: "num of questions less than 1",
			config: &models.PuzzleGameConfigInput{
				NumOfQuestions: intPtr(0),
			},
			wantErr: true,
			errMsg:  "number of questions must be between 1 and 100",
		},
		{
			name: "num of questions greater than 100",
			config: &models.PuzzleGameConfigInput{
				NumOfQuestions: intPtr(101),
			},
			wantErr: true,
			errMsg:  "number of questions must be between 1 and 100",
		},
		{
			name: "time limit less than 30",
			config: &models.PuzzleGameConfigInput{
				TimeLimit: intPtr(29),
			},
			wantErr: true,
			errMsg:  "time limit must be at least 30 seconds",
		},
		{
			name: "time limit greater than 600",
			config: &models.PuzzleGameConfigInput{
				TimeLimit: intPtr(601),
			},
			wantErr: true,
			errMsg:  "time limit must be less than 600 seconds",
		},
		{
			name: "num of players greater than 50",
			config: &models.PuzzleGameConfigInput{
				NumPlayers:     intPtr(51),
				NumOfQuestions: intPtr(10),
			},
			wantErr: true,
			errMsg:  "number of players must be between 1 and 50",
		},
		{
			name: "max time per question less than 1",
			config: &models.PuzzleGameConfigInput{
				TimeLimit:          intPtr(60),
				MaxTimePerQuestion: intPtr(0),
			},
			wantErr: true,
			errMsg:  "max time per question must be between 1 and time limit",
		},
		{
			name: "max time per question greater than time limit",
			config: &models.PuzzleGameConfigInput{
				TimeLimit:          intPtr(60),
				MaxTimePerQuestion: intPtr(61),
			},
			wantErr: true,
			errMsg:  "max time per question must be between 1 and time limit",
		},
		{
			name: "valid config with num of questions",
			config: &models.PuzzleGameConfigInput{
				NumOfQuestions: intPtr(50),
				NumPlayers:     intPtr(10),
			},
			wantErr: false,
		},
		{
			name: "valid config with time limit",
			config: &models.PuzzleGameConfigInput{
				TimeLimit:  intPtr(300),
				NumPlayers: intPtr(10),
			},
			wantErr: false,
		},
		{
			name: "valid config with time limit and max time per question",
			config: &models.PuzzleGameConfigInput{
				TimeLimit:          intPtr(300),
				MaxTimePerQuestion: intPtr(100),
				NumPlayers:         intPtr(10),
			},
			wantErr: false,
		},
		{
			name: "valid config with time limit and max time per question equal to time limit",
			config: &models.PuzzleGameConfigInput{
				TimeLimit:          intPtr(300),
				MaxTimePerQuestion: intPtr(300),
				NumPlayers:         intPtr(10),
			},
			wantErr: false,
		},
		{
			name: "valid config with time limit and max time per question equal to 1",
			config: &models.PuzzleGameConfigInput{
				TimeLimit:          intPtr(300),
				MaxTimePerQuestion: intPtr(1),
				NumPlayers:         intPtr(10),
			},
			wantErr: false,
		},
		{
			name: "valid config with num of questions equal to 1",
			config: &models.PuzzleGameConfigInput{
				NumOfQuestions: intPtr(1),
				NumPlayers:     intPtr(10),
			},
			wantErr: false,
		},
		{
			name: "valid config with num of questions equal to 100",
			config: &models.PuzzleGameConfigInput{
				NumOfQuestions: intPtr(100),
				NumPlayers:     intPtr(10),
			},
			wantErr: false,
		},
		{
			name: "valid config with time limit equal to 30",
			config: &models.PuzzleGameConfigInput{
				TimeLimit:  intPtr(30),
				NumPlayers: intPtr(10),
			},
			wantErr: false,
		},
		{
			name: "valid config with time limit equal to 600",
			config: &models.PuzzleGameConfigInput{
				TimeLimit:  intPtr(600),
				NumPlayers: intPtr(10),
			},
			wantErr: false,
		},
		{
			name: "valid config with num of players equal to 1",
			config: &models.PuzzleGameConfigInput{
				NumOfQuestions: intPtr(10),
				NumPlayers:     intPtr(1),
			},
			wantErr: false,
		},
		{
			name: "valid config with num of players equal to 50",
			config: &models.PuzzleGameConfigInput{
				NumOfQuestions: intPtr(10),
				NumPlayers:     intPtr(50),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidatePuzzleGameConfig(tt.config)
			if tt.wantErr {
				if err == nil {
					t.Errorf("ValidatePuzzleGameConfig() expected error, got nil")
				}
				if err != nil && err.Error() != tt.errMsg {
					t.Errorf("ValidatePuzzleGameConfig() error message = %v, wantErr %v", err.Error(), tt.errMsg)
				}
			} else {
				if err != nil {
					t.Errorf("ValidatePuzzleGameConfig() unexpected error: %v", err)
				}
			}
		})
	}
}

func intPtr(i int) *int {
	return &i
}
