package presets

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) DeleteUserSavedPreset(ctx context.Context, presetId models.ObjectID) (*bool, error) {
	zlog.Info(ctx, "Deleting user saved preset", zap.String("presetId", presetId.Hex()))

	success := true
	if presetId == primitive.NilObjectID {
		zlog.Error(ctx, "Preset id is nil", fmt.Errorf("preset id is nil"))
		return nil, fmt.Errorf("preset id is nil")
	}

	err := s.presetsRepo.DeleteSavedPreset(ctx, presetId)
	if err != nil {
		zlog.Error(ctx, "Failed to delete saved preset", err, zap.String("presetId", presetId.Hex()))
		return nil, fmt.Errorf("failed to delete saved preset: %w", err)
	}
	return &success, nil
}
