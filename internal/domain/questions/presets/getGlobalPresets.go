package presets

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) GetGlobalPresets(ctx context.Context, page, pageSize *int) (*models.GlobalPresets, error) {
	p := 1
	ps := 10
	if page != nil {
		p = *page
	}
	if pageSize != nil {
		ps = *pageSize
	}

	return s.presetsRepo.GetGlobalPresets(ctx, p, ps)
}

func (s *service) GetGlobalPresetsByIdentifier(ctx context.Context, identifier *string) (*models.GlobalPreset, error) {
	if identifier == nil {
		return nil, fmt.Errorf("identifier is required")
	}

	preset, err := s.presetsRepo.GetGlobalPresetByIdentifier(ctx, *identifier)
	if err != nil {
		return nil, fmt.Errorf("failed to get global preset by identifier: %w", err)
	}

	return preset, nil
}

func (s *service) GetGlobalPresetByID(ctx context.Context, id primitive.ObjectID) (*models.GlobalPreset, error) {
	globalPreset, err := s.presetsRepo.GetGlobalPresetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get global preset by id: %w", err)
	}

	return globalPreset, nil
}
