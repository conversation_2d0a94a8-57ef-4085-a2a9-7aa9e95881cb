package presets

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

// Filter by preset identifier and date
func (s *service) GetUserPresetStatsByDate(ctx context.Context, username *string, durationFilter *int, identifier *string) ([]*models.UserPresetDayStats, error) {
	var user *models.User
	if username == nil {
		userID, err := utils.GetUserFromContext(ctx)
		if err != nil {
			return nil, err
		}
		u, err := s.userService.GetUserByID(ctx, userID)
		user = u
		if err != nil {
			return nil, err
		}

	} else {
		u, err := s.userRepo.GetByUsername(ctx, *username)
		if err != nil || u == nil {
			return nil, fmt.Errorf("error getting user by username: %w", err)
		}
		user = u
	}

	location := time.UTC
	if user.Timezone != nil {
		loc, err := time.LoadLocation(*user.Timezone)
		if err == nil && loc != nil {
			location = loc
		}
	}

	now := time.Now()
	startDate := time.Date(2000, 1, 1, 0, 0, 0, 0, location)
	endDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, location)

	if identifier == nil || *identifier == "" || durationFilter == nil {
		return nil, fmt.Errorf("identifier or durationFilter is required")
	}

	if *durationFilter != 0 {
		startDate = endDate.AddDate(0, 0, -*durationFilter)
	}

	stats, err := s.presetsRepo.GetUserPresetStatsByDates(ctx, startDate, endDate, *durationFilter, user.ID, *identifier)
	if err != nil {
		return nil, fmt.Errorf("failed to get user presets stats by date: %w", err)
	}

	return stats, nil
}
