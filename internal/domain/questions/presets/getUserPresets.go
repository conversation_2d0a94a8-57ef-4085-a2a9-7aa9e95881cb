package presets

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) GetUserPresetsByIdentifier(ctx context.Context, identifier *string) (*models.UserPreset, error) {
	if identifier == nil {
		return nil, fmt.Errorf("identifier is required")
	}

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	preset, err := s.presetsRepo.GetUserPresetByIdentifier(ctx, *identifier, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user preset by identifier: %w", err)
	}

	return preset, nil
}

func (s *service) GetUserRecentPresets(ctx context.Context, page, pageSize *int) (*models.UserPresets, error) {
	p := 1
	ps := 10
	if page != nil {
		p = *page
	}
	if pageSize != nil {
		ps = *pageSize
	}

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	presets, err := s.presetsRepo.GetUserPresets(ctx, userID, false, p, ps)
	if err != nil {
		return nil, fmt.Errorf("failed to get user recent presets: %w", err)
	}

	return presets, nil
}

func (s *service) GetUserPresetByIdentifier(ctx context.Context, identifier *string, userId primitive.ObjectID) (*models.UserPreset, error) {
	if identifier == nil {
		return nil, fmt.Errorf("identifier is required")
	}

	preset, err := s.presetsRepo.GetUserPresetByIdentifier(ctx, *identifier, userId)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get user preset by identifier: %w", err)
	}

	return preset, nil
}

func (s *service) GetUserSavedPresets(ctx context.Context, page, pageSize *int) (*models.UserPresets, error) {
	p := 1
	ps := 10
	if page != nil {
		p = *page
	}
	if pageSize != nil {
		ps = *pageSize
	}

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	presets, err := s.presetsRepo.GetUserPresets(ctx, userID, true, p, ps)
	if err != nil {
		return nil, fmt.Errorf("failed to get user saved presets: %w", err)
	}

	return presets, nil
}
