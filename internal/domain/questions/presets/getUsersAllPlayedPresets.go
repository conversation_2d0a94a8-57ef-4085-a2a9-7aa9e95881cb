package presets

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *service) GetUsersAllPlayedPresets(ctx context.Context, username *string) (*models.AllPlayedPresetsOutput, error) {
	if username == nil || *username == "" {
		userID, err := utils.GetUserFromContext(ctx)
		if err != nil {
			return nil, err
		}
		return s.presetsRepo.GetUserPresetCategories(ctx, userID)
	}
	user, err := s.userRepo.GetByUsername(ctx, *username)
	if err != nil || user == nil {
		return nil, fmt.Errorf("user not found %w", err)
	}
	return s.presetsRepo.GetUserPresetCategories(ctx, user.ID)
}
