package presets

import (
	"fmt"
	"sort"
	"strconv"
	"strings"

	"matiksOfficial/matiks-server-go/internal/models"
)

func abs(n int) int {
	if n < 0 {
		return -n
	}
	return n
}

func GeneratePresetForFractionQuestion(operation string, numbers []int) string {
	if len(numbers) != 5 {
		return "FRAC_INVALID"
	}

	maxDigits := 0
	for i := 0; i < len(numbers); i++ {
		numDigits := len(strconv.Itoa(abs(numbers[i])))
		if numDigits > maxDigits {
			maxDigits = numDigits
		}
	}

	switch operation {
	case "+":
		operation = "ADD"
	case "-":
		operation = "SUB"
	case "×":
		operation = "MULT"
	case "÷":
		operation = "DIV"
	default:
		operation = "INVALID"
	}

	return fmt.Sprintf("FRAC_%s_%d", operation, maxDigits)
}

func PresetIdentifierGenerator(presetCategory models.PresetCategory, numbers []int) string {
	digitCounts := make([]int, 0, len(numbers))
	for _, num := range numbers {
		numDigits := len(strconv.Itoa(num))
		digitCounts = append(digitCounts, numDigits)
	}

	sort.Sort(sort.Reverse(sort.IntSlice(digitCounts)))
	return presetCategory.String() + "_" + strings.Trim(strings.Join(strings.Fields(fmt.Sprint(digitCounts)), ","), "[]")
}
