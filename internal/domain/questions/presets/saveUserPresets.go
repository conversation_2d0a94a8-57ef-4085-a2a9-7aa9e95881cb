package presets

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) SaveUserPreset(ctx context.Context, identifier, name *string) (*models.UserPreset, error) {
	return s.saveUserPreset(ctx, identifier, name, true)
}

func (s *service) saveUserPreset(ctx context.Context, identifier, name *string, save bool) (*models.UserPreset, error) {
	if identifier == nil || *identifier == "" {
		return nil, fmt.Errorf("identifier is required")
	}

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get current user ID: %w", err)
	}

	globalPreset, err := s.presetsRepo.GetGlobalPresetByIdentifier(ctx, *identifier)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, fmt.Errorf("failed to get global preset: %w", err)
	}

	if globalPreset == nil || err == mongo.ErrNoDocuments {
		globalPreset = &models.GlobalPreset{
			ID:         primitive.NewObjectID(),
			Identifier: *identifier,
		}
		err = s.presetsRepo.CreateGlobalPreset(ctx, globalPreset)
		if err != nil {
			return nil, fmt.Errorf("failed to create global preset: %w", err)
		}
	}

	globalPresetID := globalPreset.ID
	preset, err := s.presetsRepo.GetUserPresetByIdentifier(ctx, *identifier, userID)

	if err != nil && err != mongo.ErrNoDocuments {
		zlog.Error(ctx, "Failed to get user preset by identifier", err)
	}

	if err == mongo.ErrNoDocuments || preset == nil {
		preset = &models.UserPreset{
			ID:             primitive.NewObjectID(),
			GlobalPresetID: globalPresetID,
			UserID:         userID,
			Identifier:     *identifier,
			Name:           name,
			Saved:          save,
		}
		err = s.presetsRepo.CreateUserPreset(ctx, preset)
		if err != nil {
			return nil, fmt.Errorf("failed to create user preset: %w", err)
		}
		return preset, nil
	}

	if name != nil {
		preset.Name = name
	}
	preset.Saved = save
	preset.UpdatedAt = utils.AllocPtr(time.Now())

	filter := bson.M{"_id": preset.ID}
	update := bson.M{"$set": preset}
	err = s.presetsRepo.UpdateUserPreset(ctx, filter, update)
	if err != nil {
		return nil, fmt.Errorf("failed to update user preset: %w", err)
	}
	return preset, nil
}
