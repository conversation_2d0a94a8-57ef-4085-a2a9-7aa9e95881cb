package presets

import (
	"context"

	"go.uber.org/fx"
	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

type service struct {
	userRepo     repository.UserRepository
	userService  domain.UserStore
	presetsRepo  repository.PresetsRepository
	presetsCache cache.PresetsCache
}

func NewPresetsService(lc fx.Lifecycle, repositoryFactory *repository.RepositoryFactory,
	cacheInstance cache.Cache, userService domain.UserStore,
) domain.PresetsStore {
	s := &service{
		userRepo:     repositoryFactory.UserRepository,
		presetsRepo:  repositoryFactory.PresetsRepository,
		presetsCache: cache.NewPresetsCacheWrapper(cacheInstance),
		userService:  userService,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting events service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down events service")
			return nil
		},
	})

	return s
}
