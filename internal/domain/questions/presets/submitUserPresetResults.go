package presets

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"time"

	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"github.com/thoas/go-funk"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) SubmitUserPresetResult(ctx context.Context, userPresetResultInput *models.UserPresetResultInput) (*bool, error) {
	if userPresetResultInput == nil || len(userPresetResultInput.UserPresetResults) == 0 {
		return nil, fmt.Errorf("userPresetResultInput is required")
	}

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get current user ID: %w", err)
	}

	date := utils.GetUserDate(ctx, time.Now())

	for _, presetResult := range userPresetResultInput.UserPresetResults {
		identifier := presetResult.Identifier

		if identifier == "" || presetResult.Date.IsZero() {
			continue
		}

		globalPreset, err := s.presetsCache.GetGlobalPreset(ctx, identifier, userID)
		if err != nil || globalPreset == nil {
			zlog.Info(ctx, "Failed to get global presets from cache", zap.String("identifier", identifier))
			globalPreset, err = s.presetsRepo.GetGlobalPresetByIdentifier(ctx, identifier)
			if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
				return nil, fmt.Errorf("failed to get global preset: %w", err)
			}
		}

		if globalPreset == nil || errors.Is(err, mongo.ErrNoDocuments) {
			globalPreset = &models.GlobalPreset{
				ID:             primitive.NewObjectID(),
				Identifier:     identifier,
				GlobalAccuracy: 1,
				CreatedAt:      utils.AllocPtr(time.Now()),
				UpdatedAt:      utils.AllocPtr(time.Now()),
			}
			err = s.presetsRepo.CreateGlobalPreset(ctx, globalPreset)
			if err != nil {
				return nil, fmt.Errorf("failed to create global preset: %w", err)
			}
		}

		userPreset, err := s.presetsCache.GetUserPreset(ctx, identifier, userID)
		if err != nil || userPreset == nil {
			zlog.Info(ctx, "Failed to get user preset from cache", zap.String("identifier", identifier))
			userPreset, err = s.presetsRepo.GetUserPresetByIdentifier(ctx, identifier, userID)
			if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
				return nil, fmt.Errorf("failed to get user preset: %w", err)
			}
		}

		if userPreset == nil || errors.Is(err, mongo.ErrNoDocuments) {
			userPreset = &models.UserPreset{
				ID:             primitive.NewObjectID(),
				UserID:         userID,
				GlobalPresetID: globalPreset.ID,
				Identifier:     identifier,
				Name:           nil,
				CurAvgAccuracy: 1,
				CreatedAt:      utils.AllocPtr(time.Now()),
				UpdatedAt:      utils.AllocPtr(time.Now()),
			}
		}

		userPresetSolvedStats, err := s.presetsCache.GetUserPresetStats(ctx, identifier, userID)
		if err != nil || userPresetSolvedStats == nil {
			zlog.Info(ctx, "Failed to get user preset stats from cache", zap.String("identifier", identifier))
			userPresetSolvedStats, err = s.presetsRepo.GetUserPresetStats(ctx, identifier, date, userID)
			if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
				return nil, fmt.Errorf("failed to get user preset stats: %w", err)
			}
		}

		if userPresetSolvedStats == nil || errors.Is(err, mongo.ErrNoDocuments) {
			userPresetSolvedStats = &models.UserPresetStats{
				ID:             primitive.NewObjectID(),
				Date:           date,
				UserPresetID:   userPreset.ID,
				GlobalPresetID: globalPreset.ID,
				Identifier:     identifier,
				UserID:         userID,
				AvgAccuracy:    1,
			}
		}

		if presetResult.NumOfQuestions < 1 {
			continue
		}

		userPresetSolvedStats.AvgTime = calculateNewAverage(userPresetSolvedStats.AvgTime, userPresetSolvedStats.QuestionsSolved, presetResult.SubmittedTimes)
		userPresetSolvedStats.QuestionsSolved += presetResult.NumOfQuestions
		userPresetSolvedStats.InaccuracyPerformanceTrend = append(userPresetSolvedStats.InaccuracyPerformanceTrend, presetResult.IncorrectAttempts...)
		userPresetSolvedStats.TimePerformanceTrend = append(userPresetSolvedStats.TimePerformanceTrend, presetResult.SubmittedTimes...)
		userPresetSolvedStats.BestStreak = max(userPresetSolvedStats.BestStreak, presetResult.BestStreak)
		userPresetSolvedStats.NumOfCorrectSubmissions += presetResult.NumOfCorrectSubmissions
		userPresetSolvedStats.IncorrectSubmissions += int(funk.Sum(presetResult.IncorrectAttempts))
		userPresetSolvedStats.AvgAccuracy = float64(userPresetSolvedStats.QuestionsSolved) / (float64(userPresetSolvedStats.IncorrectSubmissions) + float64(userPresetSolvedStats.QuestionsSolved))

		userPreset.CurAvgTime = calculateNewAverage(userPreset.CurAvgTime, userPreset.QuestionsSolved, presetResult.SubmittedTimes)
		userPreset.QuestionsSolved = userPreset.QuestionsSolved + presetResult.NumOfQuestions
		userPreset.BestTime = updateBestTime(userPreset.BestTime, presetResult.SubmittedTimes)
		userPreset.Last10Time = userPresetSolvedStats.TimePerformanceTrend[max(len(userPresetSolvedStats.TimePerformanceTrend)-10, 0):]
		userPreset.Last10IncorrectAttempts = userPresetSolvedStats.InaccuracyPerformanceTrend[max(len(userPresetSolvedStats.InaccuracyPerformanceTrend)-10, 0):]
		userPreset.BestStreak = max(userPreset.BestStreak, presetResult.BestStreak)
		userPreset.IncorrectSubmissions += int(funk.Sum(presetResult.IncorrectAttempts))
		userPreset.CurAvgAccuracy = float64(userPreset.QuestionsSolved) / (float64(userPreset.IncorrectSubmissions) + float64(userPreset.QuestionsSolved))
		userPreset.SavedConfig = presetResult.SavedConfig
		userPreset.UpdatedAt = utils.AllocPtr(time.Now())

		globalPreset.BestTime = updateBestTime(globalPreset.BestTime, presetResult.SubmittedTimes)
		globalPreset.UpdatedAt = utils.AllocPtr(time.Now())
		globalPreset.GlobalAverageTime = calculateNewAverage(globalPreset.GlobalAverageTime, globalPreset.TotalQuestionsSolved, presetResult.SubmittedTimes)
		globalPreset.TotalQuestionsSolved = globalPreset.TotalQuestionsSolved + presetResult.NumOfQuestions
		globalPreset.IncorrectSubmissions += int(funk.Sum(presetResult.IncorrectAttempts))
		globalPreset.GlobalAccuracy = float64(globalPreset.TotalQuestionsSolved) / (float64(globalPreset.IncorrectSubmissions) + float64(globalPreset.TotalQuestionsSolved))
		// TODO: Update global presets
		// globalPreset.Top10Mathletes = updateTop10Mathletes(globalPreset.Top10Mathletes, presetResult.SubmittedTimes)

		if err := s.presetsCache.SetGlobalPreset(ctx, globalPreset, userID); err != nil {
			return nil, err
		}
		if err := s.presetsCache.SetUserPreset(ctx, userPreset, userID); err != nil {
			return nil, err
		}
		if err := s.presetsCache.SetUserPresetStats(ctx, userPresetSolvedStats, userID); err != nil {
			return nil, err
		}

		totalTimeSpent := funk.SumInt(presetResult.SubmittedTimes)

		go func() {
			err := s.userService.UpdateUserStatikCoinsAndTimeSpent(utils.DeriveContextWithoutCancel(ctx), userID, constants.ActivityTypePracticeNets, int(presetResult.NumOfQuestions)/5, int64(totalTimeSpent), &globalPreset.ID)
			if err != nil {
				zlog.Error(ctx, "Failed to update user activity", err)
			}
		}()
	}

	globalPresets, err := s.presetsCache.GetAllGlobalPresets(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get global presets from cache", err)
	}
	userPresets, err := s.presetsCache.GetAllUserPresets(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get user presets from cache", err)
	}
	userPresetStats, err := s.presetsCache.GetAllUserPresetStats(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get user preset stats from cache", err)
	}

	if err := s.presetsRepo.BulkUpdateGlobalPresets(ctx, globalPresets); err != nil {
		return nil, fmt.Errorf("failed to bulk update global presets: %w", err)
	}

	if err := s.presetsRepo.BulkUpdateUserPresets(ctx, userPresets); err != nil {
		return nil, fmt.Errorf("failed to bulk update user presets: %w", err)
	}

	if err := s.presetsRepo.BulkUpdateUserPresetStats(ctx, userPresetStats); err != nil {
		return nil, fmt.Errorf("failed to bulk update user preset stats: %w", err)
	}

	success := true

	if err := s.presetsCache.DeleteAllGlobalPresets(ctx, userID); err != nil {
		return &success, err
	}
	if err := s.presetsCache.DeleteAllUserPresets(ctx, userID); err != nil {
		return &success, err
	}
	if err := s.presetsCache.DeleteAllUserPresetStats(ctx, userID); err != nil {
		return &success, err
	}

	return &success, nil
}

func calculateNewAverage(currentAvg float64, currentCount int64, newValues []int) float64 {
	newSum := funk.SumInt(newValues)
	totalCount := currentCount + int64(len(newValues))
	if totalCount == 0 {
		return currentAvg
	}
	newAvg := ((currentAvg * float64(currentCount)) + float64(newSum)) / float64(totalCount)
	return newAvg
}

func updateBestTime(currentBest float64, newTimes []int) float64 {
	if len(newTimes) == 0 {
		return currentBest
	}
	prevMin := float64(slices.Min(newTimes))
	if currentBest == 0 {
		return prevMin
	}
	return min(currentBest, prevMin)
}
