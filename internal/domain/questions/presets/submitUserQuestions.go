package presets

import (
	"context"
	"errors"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type question struct {
	Identifier              string
	NumOfQuestions          int64
	SubmittedTimes          []int
	IncorrectAttempts       int
	BestStreak              int
	NumOfCorrectSubmissions int
	Date                    time.Time
	SavedConfig             string
}

func handleGameQuestion(ctx context.Context, gameQuestions []*models.GameQuestion, userID primitive.ObjectID) []*question {
	questions := make([]*question, 0, len(gameQuestions))
	prevTime := 0
	for _, q := range gameQuestions {
		if len(q.Submissions) == 0 {
			break
		}
		var submission *models.Submission
		for _, sub := range q.Submissions {
			if *sub.UserID == userID {
				submission = sub
			}
		}
		if submission == nil {
			break
		}
		timeTaken := *submission.TimeTaken
		timeToSolve := timeTaken - prevTime
		prevTime = timeTaken
		date := utils.GetUserDate(ctx, time.Now())
		question := &question{
			Identifier:              q.Question.PresetIdentifier,
			NumOfQuestions:          1,
			SubmittedTimes:          []int{timeToSolve},
			IncorrectAttempts:       *submission.InCorrectAttempts,
			BestStreak:              0,
			NumOfCorrectSubmissions: 0,
			Date:                    date,
		}
		questions = append(questions, question)
	}
	return questions
}

func handleContestQuestion(ctx context.Context, contestQuestions []*models.ContestQuestion) []*question {
	return []*question{}
}

func (s *service) SubmitUserQuestions(ctx context.Context, questionsWrapper any, userID primitive.ObjectID) (*bool, error) {
	zlog.Info(ctx, "Submitting user questions", zap.String("userID", userID.Hex()))
	if questionsWrapper == nil {
		return nil, fmt.Errorf("questions is required")
	}

	date := utils.GetUserDate(ctx, time.Now())
	var questions []*question
	switch qw := questionsWrapper.(type) {
	case []*models.GameQuestion:
		questions = handleGameQuestion(ctx, qw, userID)
	case []*models.ContestQuestion:
		questions = handleContestQuestion(ctx, qw)
	default:
		zlog.Warn(ctx, "Invalid questions type")
		return nil, fmt.Errorf("invalid questions type")
	}

	for _, question := range questions {
		identifier := question.Identifier

		globalPreset, err := s.presetsCache.GetGlobalPreset(ctx, identifier, userID)
		if err != nil || globalPreset == nil {
			zlog.Info(ctx, "Failed to get global presets from cache", zap.String("identifier", identifier))
			globalPreset, err = s.presetsRepo.GetGlobalPresetByIdentifier(ctx, identifier)
			if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
				return nil, fmt.Errorf("failed to get global preset: %w", err)
			}
		}

		if globalPreset == nil || errors.Is(err, mongo.ErrNoDocuments) {
			globalPreset = &models.GlobalPreset{
				ID:             primitive.NewObjectID(),
				Identifier:     identifier,
				GlobalAccuracy: 1,
				CreatedAt:      utils.AllocPtr(time.Now()),
				UpdatedAt:      utils.AllocPtr(time.Now()),
			}
			err = s.presetsRepo.CreateGlobalPreset(ctx, globalPreset)
			if err != nil {
				return nil, fmt.Errorf("failed to create global preset: %w", err)
			}
		}

		userPreset, err := s.presetsCache.GetUserPreset(ctx, identifier, userID)
		if err != nil || userPreset == nil {
			zlog.Info(ctx, "Failed to get user preset from cache", zap.String("identifier", identifier))
			userPreset, err = s.presetsRepo.GetUserPresetByIdentifier(ctx, identifier, userID)
			if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
				return nil, fmt.Errorf("failed to get user preset: %w", err)
			}
		}

		if userPreset == nil || errors.Is(err, mongo.ErrNoDocuments) {
			userPreset = &models.UserPreset{
				ID:             primitive.NewObjectID(),
				UserID:         userID,
				GlobalPresetID: globalPreset.ID,
				Identifier:     identifier,
				Name:           nil,
				CurAvgAccuracy: 1,
				CreatedAt:      utils.AllocPtr(time.Now()),
				UpdatedAt:      utils.AllocPtr(time.Now()),
			}
		}

		userPresetSolvedStats, err := s.presetsCache.GetUserPresetStats(ctx, identifier, userID)
		if err != nil || userPresetSolvedStats == nil {
			zlog.Info(ctx, "Failed to get user preset stats from cache", zap.String("identifier", identifier))
			userPresetSolvedStats, err = s.presetsRepo.GetUserPresetStats(ctx, identifier, date, userID)
			if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
				return nil, fmt.Errorf("failed to get user preset stats: %w", err)
			}
		}

		if userPresetSolvedStats == nil || errors.Is(err, mongo.ErrNoDocuments) {
			userPresetSolvedStats = &models.UserPresetStats{
				ID:             primitive.NewObjectID(),
				Date:           date,
				UserPresetID:   userPreset.ID,
				GlobalPresetID: globalPreset.ID,
				Identifier:     identifier,
				UserID:         userID,
				AvgAccuracy:    1,
			}
		}

		if question.NumOfQuestions < 1 {
			continue
		}

		userPresetSolvedStats.AvgTime = calculateNewAverage(userPresetSolvedStats.AvgTime, userPresetSolvedStats.QuestionsSolved, question.SubmittedTimes)
		userPresetSolvedStats.QuestionsSolved += question.NumOfQuestions
		userPresetSolvedStats.InaccuracyPerformanceTrend = append(userPresetSolvedStats.InaccuracyPerformanceTrend, question.IncorrectAttempts)
		userPresetSolvedStats.TimePerformanceTrend = append(userPresetSolvedStats.TimePerformanceTrend, question.SubmittedTimes...)
		userPresetSolvedStats.BestStreak = max(userPresetSolvedStats.BestStreak, question.BestStreak)
		userPresetSolvedStats.NumOfCorrectSubmissions += question.NumOfCorrectSubmissions
		userPresetSolvedStats.IncorrectSubmissions += question.IncorrectAttempts
		userPresetSolvedStats.AvgAccuracy = float64(userPresetSolvedStats.QuestionsSolved) / (float64(userPresetSolvedStats.IncorrectSubmissions) + float64(userPresetSolvedStats.QuestionsSolved))

		userPreset.CurAvgTime = calculateNewAverage(userPreset.CurAvgTime, userPreset.QuestionsSolved, question.SubmittedTimes)
		userPreset.QuestionsSolved += question.NumOfQuestions
		userPreset.BestTime = updateBestTime(userPreset.BestTime, question.SubmittedTimes)
		userPreset.Last10Time = userPresetSolvedStats.TimePerformanceTrend[max(len(userPresetSolvedStats.TimePerformanceTrend)-10, 0):]
		userPreset.Last10IncorrectAttempts = userPresetSolvedStats.InaccuracyPerformanceTrend[max(len(userPresetSolvedStats.InaccuracyPerformanceTrend)-10, 0):]
		userPreset.BestStreak = max(userPreset.BestStreak, question.BestStreak)
		userPreset.NumOfCorrectSubmissions += question.NumOfCorrectSubmissions
		userPreset.IncorrectSubmissions += question.IncorrectAttempts
		userPreset.CurAvgAccuracy = float64(userPreset.QuestionsSolved) / (float64(userPreset.IncorrectSubmissions) + float64(userPreset.QuestionsSolved))
		userPreset.SavedConfig = question.SavedConfig
		userPreset.UpdatedAt = utils.AllocPtr(time.Now())

		globalPreset.BestTime = updateBestTime(globalPreset.BestTime, question.SubmittedTimes)
		globalPreset.UpdatedAt = utils.AllocPtr(time.Now())
		globalPreset.GlobalAverageTime = calculateNewAverage(globalPreset.GlobalAverageTime, globalPreset.TotalQuestionsSolved, question.SubmittedTimes)
		globalPreset.TotalQuestionsSolved += question.NumOfQuestions
		globalPreset.NumOfCorrectSubmissions += question.NumOfCorrectSubmissions
		globalPreset.BestStreak = max(globalPreset.BestStreak, question.BestStreak)
		globalPreset.IncorrectSubmissions += question.IncorrectAttempts
		globalPreset.GlobalAccuracy = float64(globalPreset.TotalQuestionsSolved) / (float64(globalPreset.IncorrectSubmissions) + float64(globalPreset.TotalQuestionsSolved))
		// TODO: Update global presets
		// globalPreset.Top10Mathletes = updateTop10Mathletes(globalPreset.Top10Mathletes, question.SubmittedTimes)

		if err := s.presetsCache.SetGlobalPreset(ctx, globalPreset, userID); err != nil {
			return nil, err
		}
		if err := s.presetsCache.SetUserPreset(ctx, userPreset, userID); err != nil {
			return nil, err
		}
		if err := s.presetsCache.SetUserPresetStats(ctx, userPresetSolvedStats, userID); err != nil {
			return nil, err
		}
	}

	globalPresets, err := s.presetsCache.GetAllGlobalPresets(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get global presets from cache", err)
	}
	userPresets, err := s.presetsCache.GetAllUserPresets(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get user presets from cache", err)
	}
	userPresetStats, err := s.presetsCache.GetAllUserPresetStats(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get user preset stats from cache", err)
	}

	if err := s.presetsRepo.BulkUpdateGlobalPresets(ctx, globalPresets); err != nil {
		return nil, fmt.Errorf("failed to bulk update global presets: %w", err)
	}

	if err := s.presetsRepo.BulkUpdateUserPresets(ctx, userPresets); err != nil {
		return nil, fmt.Errorf("failed to bulk update user presets: %w", err)
	}

	if err := s.presetsRepo.BulkUpdateUserPresetStats(ctx, userPresetStats); err != nil {
		return nil, fmt.Errorf("failed to bulk update user preset stats: %w", err)
	}

	success := true

	if err := s.presetsCache.DeleteAllGlobalPresets(ctx, userID); err != nil {
		return &success, err
	}
	if err := s.presetsCache.DeleteAllUserPresets(ctx, userID); err != nil {
		return &success, err
	}
	if err := s.presetsCache.DeleteAllUserPresetStats(ctx, userID); err != nil {
		return &success, err
	}

	return &success, nil
}
