package questionsGenerator

import (
	"fmt"
	"math/rand/v2"

	"matiksOfficial/matiks-server-go/internal/domain/questions"
	"matiksOfficial/matiks-server-go/internal/domain/questions/presets"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func GetAdditionQuestion(numbers []int, rating int, tags []string) *models.Question {
	expression := make([]string, 0, 2*len(numbers))
	answer := 0
	hasSubtraction := false

	for index, num := range numbers {
		if index == 0 {
			expression = append(expression, fmt.Sprintf("%d", num))
			answer = num
			continue
		}

		randomNum := rand.Float64()
		if randomNum >= 0.4 || answer-num <= 0 {
			expression = append(expression, "+")
			answer += num
		} else {
			expression = append(expression, "-")
			answer -= num
			hasSubtraction = true
		}
		expression = append(expression, fmt.Sprintf("%d", num))
	}
	var presetCategory models.PresetCategory
	if hasSubtraction {
		presetCategory = models.PresetCategoryAddsub
	} else {
		presetCategory = models.PresetCategoryAdd
	}

	presetIdentifier := presets.PresetIdentifierGenerator(presetCategory, numbers)

	return &models.Question{
		Expression:       expression,
		Answers:          []string{fmt.Sprintf("%d", answer)},
		QuestionType:     utils.AllocPtr(models.QuestionTypeFillInTheBlanks),
		Rating:           utils.AllocPtr(rating),
		Tags:             append([]string{QUESTION_TAG_ADDITION, QUESTION_TAG_ARITHMETIC}, tags...),
		PresetIdentifier: presetIdentifier,
		MaxTimeLimit:     utils.AllocPtr(int(questions.GetExpectedTimeToSolveQuestion(rating, rating, presetIdentifier).Seconds())),
	}
}

func shuffle(slice []int) []int {
	shuffled := make([]int, len(slice))
	copy(shuffled, slice)
	for i := range shuffled {
		j := rand.IntN(i + 1)
		shuffled[i], shuffled[j] = shuffled[j], shuffled[i]
	}
	return shuffled
}

func createAdditionQuestion(rating int) *models.Question {
	var numConfig []NumConfig

	switch {
	case rating <= 600:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 700:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 800:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
		}
	case rating <= 900:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1000:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 1100:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
		}
	case rating <= 1200:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1300:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 1400:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1500:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1600:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1700:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1800:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1900:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2000:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
		}
	case rating <= 2100:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2200:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2300:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2400:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2500:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2600:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2700:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2800:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 4, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2900:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 3000:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 3100:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
		}
	case rating <= 3200:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 4, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
		}
	case rating <= 3300:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
		}
	case rating <= 3400:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
		}
	case rating <= 3500:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
		}
	case rating <= 3600:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 3700:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
		}
	case rating <= 3800:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 3900:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 4000:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
		}
	case rating > 4000:
		numConfig = []NumConfig{
			{NumDigit: 5, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 5, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 5, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 5, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 5, Level: DigitDifficultyLevels.MEDIUM},
		}
	default:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
		}
	}

	numbers := make([]int, 0, len(numConfig))
	for _, config := range numConfig {
		num := getRandomNumber(config.NumDigit, config.Level)
		numbers = append(numbers, num)
	}
	shuffledArray := shuffle(numbers)
	return GetAdditionQuestion(shuffledArray, rating, []string{})
}
