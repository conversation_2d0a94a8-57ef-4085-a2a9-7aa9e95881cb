package questionsGenerator

import (
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/questions"
	"matiksOfficial/matiks-server-go/internal/domain/questions/presets"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

const (
	FILL_IN_THE_BLANKS = "FILL_IN_THE_BLANKS"
	DIVISION           = "DIVISION"
	ARITHMETIC         = "ARITHMETIC"
)

func getDivisionQuestion(product, divisor float64, rating int, tags []string) *models.Question {
	expression := []string{fmt.Sprintf("%.0f", product), "÷", fmt.Sprintf("%.0f", divisor)}
	answer := product / divisor
	presetIdentifier := presets.PresetIdentifierGenerator(models.PresetCategoryDiv, []int{int(product), int(divisor)})

	return &models.Question{
		Expression:       expression,
		Answers:          []string{fmt.Sprintf("%.0f", answer)},
		QuestionType:     utils.AllocPtr(models.QuestionTypeFillInTheBlanks),
		Rating:           utils.AllocPtr(rating),
		Tags:             append([]string{DIVISION, ARITHMETIC}, tags...),
		PresetIdentifier: presetIdentifier,
		MaxTimeLimit:     utils.AllocPtr(int(questions.GetExpectedTimeToSolveQuestion(rating, rating, presetIdentifier).Seconds())),
	}
}

type NumberType struct {
	DIVISOR  string
	QUOTIENT string
}

func createDivisionQuestion(rating int) *models.Question {
	var numConfig []map[string]interface{}

	numberType := NumberType{
		DIVISOR:  "DIVISOR",
		QUOTIENT: "QUOTIENT",
	}

	switch {
	case rating <= 800:
		numConfig = []map[string]interface{}{
			{"type": numberType.QUOTIENT, "range": [2]int{2, 7}},
			{"type": numberType.DIVISOR, "range": [2]int{2, 7}},
		}
	case rating <= 1000:
		numConfig = []map[string]interface{}{
			{"type": numberType.QUOTIENT, "range": [2]int{3, 9}},
			{"type": numberType.DIVISOR, "range": [2]int{6, 9}},
		}
	case rating <= 1100:
		numConfig = []map[string]interface{}{
			{"type": numberType.QUOTIENT, "range": [2]int{11, 29}},
			{"type": numberType.DIVISOR, "range": [2]int{6, 9}},
		}
	case rating <= 1200:
		numConfig = []map[string]interface{}{
			{"type": numberType.QUOTIENT, "range": [2]int{31, 59}},
			{"type": numberType.DIVISOR, "range": [2]int{6, 9}},
		}
	case rating <= 1300:
		numConfig = []map[string]interface{}{
			{"type": numberType.QUOTIENT, "range": [2]int{61, 99}},
			{"type": numberType.DIVISOR, "range": [2]int{6, 9}},
		}
	case rating <= 1400:
		numConfig = []map[string]interface{}{
			{"type": numberType.QUOTIENT, "range": [2]int{2, 9}},
			{"type": numberType.DIVISOR, "range": [2]int{11, 19}},
		}
	case rating <= 1500:
		numConfig = []map[string]interface{}{
			{"type": numberType.QUOTIENT, "range": [2]int{101, 499}},
			{"type": numberType.DIVISOR, "range": [2]int{6, 9}},
		}
	case rating <= 1600:
		numConfig = []map[string]interface{}{
			{"type": numberType.QUOTIENT, "range": [2]int{2, 9}},
			{"type": numberType.DIVISOR, "range": [2]int{21, 29}},
		}
	case rating <= 1700:
		numConfig = []map[string]interface{}{
			{"type": numberType.QUOTIENT, "range": [2]int{11, 14}},
			{"type": numberType.DIVISOR, "range": [2]int{21, 29}},
		}
	case rating <= 1800:
		numConfig = []map[string]interface{}{
			{"type": numberType.QUOTIENT, "range": [2]int{15, 19}},
			{"type": numberType.DIVISOR, "range": [2]int{21, 39}},
		}
	case rating <= 2000:
		numConfig = []map[string]interface{}{
			{"type": numberType.QUOTIENT, "range": [2]int{15, 29}},
			{"type": numberType.DIVISOR, "range": [2]int{29, 39}},
		}
	default:
		numConfig = []map[string]interface{}{
			{"type": numberType.QUOTIENT, "range": [2]int{11, 99}},
			{"type": numberType.DIVISOR, "range": [2]int{39, 99}},
		}
	}

	var numbers []map[string]interface{}

	for _, config := range numConfig {
		rangeVals := config["range"].([2]int)
		numType := config["type"].(string)
		num := getRandomInt(rangeVals[0], rangeVals[1])
		numbers = append(numbers, map[string]interface{}{
			"type": numType,
			"num":  num,
		})
	}

	product := 1.0
	var divisor float64

	for _, numObj := range numbers {
		num := numObj["num"].(int)
		product *= float64(num)
		if numObj["type"] == numberType.DIVISOR {
			divisor = float64(num)
		}
	}

	return getDivisionQuestion(product, divisor, rating, []string{})
}
