package questionsGenerator

import (
	"fmt"
	"math"

	"matiksOfficial/matiks-server-go/internal/domain/questions/presets"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func getExponentQuestion(numbers []int, rating int, tags []string) *models.Question {
	expression := make([]string, 0, 2*len(numbers))
	base := numbers[0]
	exponent := numbers[1]

	expression = append(expression, fmt.Sprintf("%d", base))
	expression = append(expression, "^")
	expression = append(expression, fmt.Sprintf("%d", exponent))

	answer := int64(math.Round(math.Pow(float64(base), float64(exponent))*100)) / 100

	presetIdentifier := presets.PresetIdentifierGenerator(models.PresetCategoryExpo, numbers)

	return &models.Question{
		Expression:       expression,
		Answers:          []string{fmt.Sprintf("%d", answer)},
		QuestionType:     utils.AllocPtr(models.QuestionTypeFillInTheBlanks),
		Rating:           utils.AllocPtr(rating),
		Tags:             append([]string{QUESTION_TAG_EXPONENT, QUESTION_TAG_ARITHMETIC}, tags...),
		PresetIdentifier: presetIdentifier,
	}
}

func CreateExponentQuestion(rating int) *models.Question {
	var numConfig []NumConfig

	switch {
	case rating <= 600:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
		}
	case rating <= 700:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
		}
	case rating <= 800:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 900:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1000:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1100:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 1200:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 1300:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1400:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1500:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 1600:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 1700:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1800:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1900:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2000:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2100:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2200:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2300:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2400:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2500:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2600:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2700:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2800:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2900:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 3000:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 3100:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 3200:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 3300:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	default:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
		}
	}

	numbers := make([]int, 0, len(numConfig))

	num := getRandomNumber(numConfig[0].NumDigit, numConfig[0].Level)
	numbers = append(numbers, num)
	numbers = append(numbers, numConfig[1].NumDigit)

	return getExponentQuestion(numbers, rating, []string{})
}
