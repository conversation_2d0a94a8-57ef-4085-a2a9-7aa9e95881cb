package questionsGenerator

import (
	"fmt"
	"math"
	"strconv"
	"strings"
	"unicode"
)

type Token struct {
	Type  string
	Value string
}

var precedence = map[string]int{
	"+": 1,
	"-": 1,
	"*": 2,
	"/": 2,
	"×": 2,
	"÷": 2,
	"^": 3,
}

func EvaluateExpression(expr string) (string, error) {
	expr = strings.ReplaceAll(expr, " ", "")
	expr = strings.TrimSuffix(expr, "$")
	expr = strings.ReplaceAll(expr, "×", "*")
	expr = strings.ReplaceAll(expr, "÷", "/")

	tokens, err := tokenize(expr)
	if err != nil {
		return "", fmt.Errorf("tokenization error: %v", err)
	}

	postfix, err := toPostfix(tokens)
	if err != nil {
		return "", fmt.Errorf("conversion to postfix error: %v", err)
	}
	return evaluatePostfix(postfix)
}

func tokenize(expr string) ([]Token, error) {
	var tokens []Token
	var numBuilder strings.Builder

	for i := 0; i < len(expr); i++ {
		char := expr[i]

		switch {
		case unicode.IsDigit(rune(char)):
			numBuilder.WriteByte(char)
			for i+1 < len(expr) && (unicode.IsDigit(rune(expr[i+1])) || expr[i+1] == '.') {
				i++
				numBuilder.WriteByte(expr[i])
			}
			tokens = append(tokens, Token{"number", numBuilder.String()})
			numBuilder.Reset()

		case char == '+' || char == '-' || char == '*' || char == '/' || char == '^':
			if char == '-' && (i == 0 || expr[i-1] == '(' || expr[i-1] == '*') {
				tokens = append(tokens, Token{"number", "-1"})
				tokens = append(tokens, Token{"operator", "*"})
				continue
			}
			tokens = append(tokens, Token{"operator", string(char)})

		case char == '(' || char == '[':
			tokens = append(tokens, Token{"leftParen", "("})

		case char == ')' || char == ']':
			tokens = append(tokens, Token{"rightParen", ")"})

		default:
			if !unicode.IsSpace(rune(char)) {
				return nil, fmt.Errorf("invalid character: %c (hex: %x)", char, char)
			}
		}
	}

	return tokens, nil
}

func toPostfix(tokens []Token) ([]Token, error) {
	var output []Token
	var operators []Token

	for _, token := range tokens {
		switch token.Type {
		case "number":
			output = append(output, token)

		case "operator":
			for len(operators) > 0 {
				top := operators[len(operators)-1]
				if top.Type == "leftParen" {
					break
				}
				if precedence[token.Value] <= precedence[top.Value] {
					output = append(output, operators[len(operators)-1])
					operators = operators[:len(operators)-1]
				} else {
					break
				}
			}
			operators = append(operators, token)

		case "leftParen":
			operators = append(operators, token)
		case "rightParen":
			for len(operators) > 0 && operators[len(operators)-1].Type != "leftParen" {
				output = append(output, operators[len(operators)-1])
				operators = operators[:len(operators)-1]
			}
			if len(operators) == 0 {
				return nil, fmt.Errorf("mismatched parentheses")
			}
			operators = operators[:len(operators)-1]
		}
	}

	for len(operators) > 0 {
		if operators[len(operators)-1].Type == "leftParen" {
			return nil, fmt.Errorf("mismatched parentheses")
		}
		output = append(output, operators[len(operators)-1])
		operators = operators[:len(operators)-1]
	}

	return output, nil
}

func evaluatePostfix(tokens []Token) (string, error) {
	var stack []float64

	for _, token := range tokens {
		switch token.Type {
		case "number":
			num, err := strconv.ParseFloat(token.Value, 64)
			if err != nil {
				return "", fmt.Errorf("invalid number: %s", token.Value)
			}
			stack = append(stack, num)

		case "operator":
			if len(stack) < 2 {
				return "", fmt.Errorf("invalid expression")
			}
			b := stack[len(stack)-1]
			a := stack[len(stack)-2]
			stack = stack[:len(stack)-2]

			var result float64
			switch token.Value {
			case "+":
				result = a + b
			case "-":
				result = a - b
			case "*", "×":
				result = a * b
			case "/", "÷":
				if b == 0 {
					return "", fmt.Errorf("division by zero")
				}
				result = a / b
			case "^":
				result = math.Pow(a, b)
			}
			stack = append(stack, result)
		}
	}

	if len(stack) != 1 {
		return "", fmt.Errorf("invalid expression: stack size %d", len(stack))
	}

	answer := FormatDecimalNumber(stack[0])

	return answer, nil
}
