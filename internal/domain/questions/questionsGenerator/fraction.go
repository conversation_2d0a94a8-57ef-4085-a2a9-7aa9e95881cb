package questionsGenerator

import (
	"fmt"
	"math/rand/v2"

	"matiksOfficial/matiks-server-go/internal/domain/questions"
	"matiksOfficial/matiks-server-go/internal/domain/questions/presets"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

const (
	QUESTION_TAG_FRACTION = "FRACTION"
)

type Fraction struct {
	Numerator   int
	Denominator int
}

// Simplify reduces the fraction to its lowest terms
func (f *Fraction) Simplify() {
	if f.Numerator == 0 {
		f.Denominator = 1
		return
	}

	divisor := gcd(abs(f.Numerator), abs(f.Denominator))
	f.Numerator /= divisor
	f.Denominator /= divisor

	// Ensure denominator is positive
	if f.Denominator < 0 {
		f.Numerator = -f.Numerator
		f.Denominator = -f.Denominator
	}
}

func (f Fraction) String() string {
	return fmt.Sprintf("%d/%d", f.Numerator, f.Denominator)
}

func abs(n int) int {
	if n < 0 {
		return -n
	}
	return n
}

func generateFraction(maxNum int) Fraction {
	numerator := 1 + rand.IntN(maxNum)
	denominator := 1 + rand.IntN(maxNum)
	return Fraction{Numerator: numerator, Denominator: denominator}
}

func addFractions(f1, f2 Fraction) Fraction {
	result := Fraction{
		Numerator:   f1.Numerator*f2.Denominator + f2.Numerator*f1.Denominator,
		Denominator: f1.Denominator * f2.Denominator,
	}
	result.Simplify()
	return result
}

func subtractFractions(f1, f2 Fraction) Fraction {
	result := Fraction{
		Numerator:   f1.Numerator*f2.Denominator - f2.Numerator*f1.Denominator,
		Denominator: f1.Denominator * f2.Denominator,
	}
	result.Simplify()
	return result
}

func multiplyFractions(f1, f2 Fraction) Fraction {
	result := Fraction{
		Numerator:   f1.Numerator * f2.Numerator,
		Denominator: f1.Denominator * f2.Denominator,
	}
	result.Simplify()
	return result
}

func divideFractions(f1, f2 Fraction) Fraction {
	result := Fraction{
		Numerator:   f1.Numerator * f2.Denominator,
		Denominator: f1.Denominator * f2.Numerator,
	}
	result.Simplify()
	return result
}

func getFractionQuestion(f1, f2 Fraction, operator string, rating int, tags []string) *models.Question {
	var result Fraction
	switch operator {
	case "+":
		result = addFractions(f1, f2)
	case "-":
		result = subtractFractions(f1, f2)
		if result.Numerator < 0 {
			result = addFractions(f1, f2)
			operator = "+"
		}
	case "×":
		result = multiplyFractions(f1, f2)
	case "÷":
		result = divideFractions(f1, f2)
	}

	expression := []string{f1.String(), operator, f2.String()}
	presetIdentifier := presets.GeneratePresetForFractionQuestion(operator, []int{f1.Numerator, f1.Denominator, f2.Numerator, f2.Denominator})

	return &models.Question{
		Expression:       expression,
		Answers:          []string{result.String()},
		QuestionType:     utils.AllocPtr(models.QuestionTypeFillInTheBlanks),
		Rating:           utils.AllocPtr(rating),
		Tags:             append([]string{QUESTION_TAG_FRACTION, QUESTION_TAG_ARITHMETIC}, tags...),
		PresetIdentifier: presetIdentifier,
		MaxTimeLimit:     utils.AllocPtr(int(questions.GetExpectedTimeToSolveQuestion(rating, rating, presetIdentifier).Seconds())),
	}
}

func createFractionQuestion(rating int) *models.Question {
	var maxNum int
	var operators []string

	switch {
	case rating <= 800:
		maxNum = 10
		operators = []string{"+", "-"}
	case rating <= 1200:
		maxNum = 10
		operators = []string{"+", "-", "×", "÷"}
	case rating <= 1600:
		maxNum = 15
		operators = []string{"+", "-", "×", "÷"}
	case rating <= 2000:
		maxNum = 20
		operators = []string{"+", "-", "×", "÷"}
	case rating <= 2500:
		maxNum = 50
		operators = []string{"+", "-", "×", "÷"}
	default:
		maxNum = 200
		operators = []string{"+", "-", "×", "÷"}
	}

	f1 := generateFraction(maxNum)
	f2 := generateFraction(maxNum)

	// For division, ensure denominator won't be zero
	operator := operators[rand.IntN(len(operators))]
	if operator == "÷" {
		for f2.Numerator == 0 {
			f2 = generateFraction(maxNum)
		}
	}

	return getFractionQuestion(f1, f2, operator, rating, []string{})
}
