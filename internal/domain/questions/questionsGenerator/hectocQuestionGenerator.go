package questionsGenerator

import (
	"fmt"
	"math"
	"math/rand"
	"time"
)

type HectocPuzzle struct {
	Numbers []int
	Target  int
}

const HectocDefaultDifficulty = 0

func GenerateHectoc(date time.Time) string {
	difficulty := HectocDefaultDifficulty
	year, month, day := date.Date()
	dailySeed := int64(year*10000 + int(month)*100 + day)
	rand.Seed(dailySeed)

	numbers := make([]int, 6)
	for i := 0; i < 6; i++ {

		maxNum := 10 + (difficulty * 3)
		numbers[i] = rand.Intn(maxNum) + 1
	}

	target := 100

	return fmt.Sprintf("%d,%d,%d,%d,%d,%d:%d",
		numbers[0], numbers[1], numbers[2],
		numbers[3], numbers[4], numbers[5], target)
}

func EnsureSolvable(numbers []int, target int) bool {
	n := len(numbers)
	if n == 0 {
		return false
	}
	if n == 1 {
		return numbers[0] == target
	}

	for i := 0; i < n-1; i++ {
		a := numbers[i]
		b := numbers[i+1]

		operations := []func(int, int) (int, bool){
			func(x, y int) (int, bool) { return x + y, true },
			func(x, y int) (int, bool) { return x - y, true },
			func(x, y int) (int, bool) { return y - x, true },
			func(x, y int) (int, bool) { return x * y, true },
			func(x, y int) (int, bool) {
				if y != 0 && x%y == 0 {
					return x / y, true
				}
				return 0, false
			},
			func(x, y int) (int, bool) {
				if x != 0 && y%x == 0 {
					return y / x, true
				}
				return 0, false
			},
			func(x, y int) (int, bool) {
				if y != 0 {
					return x % y, true
				}
				return 0, false
			},
			func(x, y int) (int, bool) {
				if x != 0 {
					return y % x, true
				}
				return 0, false
			},
			func(x, y int) (int, bool) {
				if x >= 0 && y >= 0 && y < 10 {
					res := int(math.Pow(float64(x), float64(y)))
					return res, true
				}
				return 0, false
			},
			func(x, y int) (int, bool) {
				if y >= 0 && x >= 0 && x < 10 {
					res := int(math.Pow(float64(y), float64(x)))
					return res, true
				}
				return 0, false
			},
		}

		for _, op := range operations {
			res, possible := op(a, b)
			if possible {
				newNumbers := make([]int, 0, n-1)
				newNumbers = append(newNumbers, numbers[:i]...)
				newNumbers = append(newNumbers, res)
				newNumbers = append(newNumbers, numbers[i+2:]...)

				if EnsureSolvable(newNumbers, target) {
					return true
				}
			}
		}
	}

	return false
}
