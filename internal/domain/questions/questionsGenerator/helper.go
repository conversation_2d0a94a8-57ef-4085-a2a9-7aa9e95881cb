package questionsGenerator

import (
	"fmt"
	"math"
	"math/rand/v2"
	"strings"
)

var WEIGHTED_NON_ZERO_INTEGERS = []int{1, 2, 3, 4, 5, 6, 7, 7, 8, 8, 9, 9}

func getRandomWeightedInt() int {
	maxIndex := len(WEIGHTED_NON_ZERO_INTEGERS) - 1
	index := rand.IntN(maxIndex)
	return WEIGHTED_NON_ZERO_INTEGERS[index]
}

func getQuestionTimeLimit(rating int) int {
	if rating <= 1000 {
		return 10
	} else if rating <= 1500 {
		return 15
	}
	return 20
}

func getDifficultyRatingBasedOnQuestionNumber(questionNumber, initialRating int) int {
	if questionNumber < 2 {
		return initialRating
	} else if questionNumber <= 4 {
		return initialRating + 100
	}
	return initialRating + 30*questionNumber
}

func getRandomInt(min, max int) int {
	return rand.IntN(max-min+1) + min
}

func getRandomDigit(difficultyLevel DigitDifficultyLevel, nonZero bool) int {
	switch difficultyLevel {
	case DigitDifficultyLevels.EASY:
		if nonZero {
			return getRandomInt(1, 4)
		}
		return getRandomInt(0, 4)
	case DigitDifficultyLevels.MEDIUM:
		return getRandomInt(3, 6)
	case DigitDifficultyLevels.HARD:
		return getRandomInt(6, 9)
	default:
		if nonZero {
			return getRandomInt(1, 9)
		}
		return getRandomInt(0, 9)
	}
}

func getRandomNumber(numDigits int, difficultyLevel DigitDifficultyLevel) int {
	result := 0
	for i := 0; i < numDigits; i++ {
		digit := getRandomDigit(difficultyLevel, i == 0)
		result = result*10 + digit
	}
	return result
}

func randomOperator(rating int) string {
	operators := []string{"+", "-"}
	if rating > 1500 {
		operators = append(operators, "÷")
	}
	if rating > 2000 {
		operators = append(operators, "×")
	}
	return operators[rand.IntN(len(operators))]
}

func FormatDecimalNumber(num float64) string {
	rounded := math.Round(num*100) / 100
	str := fmt.Sprintf("%.2f", rounded)

	parts := strings.Split(str, ".")
	if len(parts) != 2 {
		return str
	}

	integerPart := parts[0]
	decimalPart := parts[1]

	decimalPart = strings.TrimRight(decimalPart, "0")

	if decimalPart == "" {
		return integerPart
	}
	return fmt.Sprintf("%s.%s", integerPart, decimalPart)
}
