package questionsGenerator

import (
	"context"
	"fmt"
	"math/rand/v2"
	"strings"

	"go.uber.org/zap"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

// KenKenSettings defines the configuration for a KenKen puzzle
type KenKenSettings struct {
	Size         int
	MaxGroupSize int
	Torus        bool
	Operations   Operations
}

// Operations defines which operations are allowed in the puzzle
type Operations struct {
	Addition       bool
	Subtraction    bool
	Multiplication bool
	Division       bool
	Max            bool
	Min            bool
	Range          bool
	Mod            bool
	Avg            bool
	Par            bool
	Gcd            bool
}

// KenKenCell represents a single cell in the puzzle
type KenKenCell struct {
	kenken    *Kenken
	X         int
	Y         int
	CellGroup *KenKenCellGroup
	Value     int
}

// KenKenCellGroup represents a group of cells with an operation
type KenKenCellGroup struct {
	kenken               *Kenken
	GroupID              int
	Cells                []*KenKenCell
	CurrentSize          int
	OperationDescription string
}

// Operation interface for all mathematical operations
type Operation interface {
	MinCells() int
	MaxCells() int
	Symbol() string
	Operation(arrayOfNumbers []int) (int, bool)
}

// Base structs for operations
type (
	SingleCell     struct{}
	Addition       struct{}
	Subtraction    struct{}
	Multiplication struct{}
	Division       struct{}
	Maximum        struct{}
	Minimum        struct{}
	Range          struct{}
	Mod            struct{}
	Average        struct{}
	Parity         struct{}
	Gcd            struct{}
)

// Kenken represents the entire puzzle
type Kenken struct {
	Size                int
	Settings            KenKenSettings
	Board               [][]*KenKenCell
	MinGroupSize        int
	DefaultMaxGroupSize int
	MaxGroupSize        int
	CellGroups          []*KenKenCellGroup
	Operations          []Operation
}

// String implements the Stringer interface for Kenken
func (k *Kenken) String() string {
	var sb strings.Builder

	// Header information
	sb.WriteString(fmt.Sprintf("KenKen Puzzle (Size: %d)\n", k.Size))
	sb.WriteString("Groups:\n")

	// Display cell groups and their operations
	for _, group := range k.CellGroups {
		sb.WriteString(fmt.Sprintf("Group %d: %s Cells: ",
			group.GroupID,
			group.OperationDescription))

		for i, cell := range group.Cells {
			if i > 0 {
				sb.WriteString(", ")
			}
			sb.WriteString(fmt.Sprintf("(%d,%d)", cell.X, cell.Y))
		}
		sb.WriteString("\n")
	}

	// Display the board
	sb.WriteString("\nBoard:\n")
	sb.WriteString("  ")
	for x := 0; x < k.Size; x++ {
		sb.WriteString(fmt.Sprintf(" %d ", x))
	}
	sb.WriteString("\n")

	for y := 0; y < k.Size; y++ {
		sb.WriteString(fmt.Sprintf("%d ", y))
		for x := 0; x < k.Size; x++ {
			cell := k.Board[x][y]
			sb.WriteString(fmt.Sprintf("[%d]", cell.Value))
		}
		sb.WriteString("\n")
	}

	// Display solution
	sb.WriteString("\nSolution:\n")
	for y := 0; y < k.Size; y++ {
		for x := 0; x < k.Size; x++ {
			sb.WriteString(fmt.Sprintf("%d ", k.Board[x][y].Value))
		}
		sb.WriteString("\n")
	}

	return sb.String()
}

// SetCellGroup adds a cell to a cell group
func (cell *KenKenCell) SetCellGroup(cellGroup *KenKenCellGroup) {
	cell.CellGroup = cellGroup
}

// GetNeighbors returns all neighboring cells
func (cell *KenKenCell) GetNeighbors() []*KenKenCell {
	var neighbors []*KenKenCell

	if cell.X < 0 || cell.Y < 0 {
		return neighbors
	}

	if cell.kenken.Settings.Torus {
		if cell.X > 0 {
			neighbors = append(neighbors, cell.kenken.Board[cell.X-1][cell.Y])
		} else {
			neighbors = append(neighbors, cell.kenken.Board[cell.kenken.Size-1][cell.Y])
		}

		if cell.Y > 0 {
			neighbors = append(neighbors, cell.kenken.Board[cell.X][cell.Y-1])
		} else {
			neighbors = append(neighbors, cell.kenken.Board[cell.X][cell.kenken.Size-1])
		}

		if cell.X < cell.kenken.Size-1 {
			neighbors = append(neighbors, cell.kenken.Board[cell.X+1][cell.Y])
		} else {
			neighbors = append(neighbors, cell.kenken.Board[0][cell.Y])
		}

		if cell.Y < cell.kenken.Size-1 {
			neighbors = append(neighbors, cell.kenken.Board[cell.X][cell.Y+1])
		} else {
			neighbors = append(neighbors, cell.kenken.Board[cell.X][0])
		}
	} else {
		if cell.X > 0 {
			neighbors = append(neighbors, cell.kenken.Board[cell.X-1][cell.Y])
		}
		if cell.Y > 0 {
			neighbors = append(neighbors, cell.kenken.Board[cell.X][cell.Y-1])
		}
		if cell.X < cell.kenken.Size-1 {
			neighbors = append(neighbors, cell.kenken.Board[cell.X+1][cell.Y])
		}
		if cell.Y < cell.kenken.Size-1 {
			neighbors = append(neighbors, cell.kenken.Board[cell.X][cell.Y+1])
		}
	}

	return neighbors
}

// GetNeighborsOriented returns neighboring cells by direction
func (cell *KenKenCell) GetNeighborsOriented() map[string]*KenKenCell {
	neighbors := make(map[string]*KenKenCell)

	if cell.X < 0 || cell.Y < 0 {
		return neighbors
	}

	if cell.kenken.Settings.Torus {
		if cell.X > 0 {
			neighbors["left"] = cell.kenken.Board[cell.X-1][cell.Y]
		} else {
			neighbors["left"] = cell.kenken.Board[cell.kenken.Size-1][cell.Y]
		}

		if cell.Y > 0 {
			neighbors["up"] = cell.kenken.Board[cell.X][cell.Y-1]
		} else {
			neighbors["up"] = cell.kenken.Board[cell.X][cell.kenken.Size-1]
		}

		if cell.X < cell.kenken.Size-1 {
			neighbors["right"] = cell.kenken.Board[cell.X+1][cell.Y]
		} else {
			neighbors["right"] = cell.kenken.Board[0][cell.Y]
		}

		if cell.Y < cell.kenken.Size-1 {
			neighbors["down"] = cell.kenken.Board[cell.X][cell.Y+1]
		} else {
			neighbors["down"] = cell.kenken.Board[cell.X][0]
		}
	} else {
		if cell.X > 0 {
			neighbors["left"] = cell.kenken.Board[cell.X-1][cell.Y]
		}
		if cell.Y > 0 {
			neighbors["up"] = cell.kenken.Board[cell.X][cell.Y-1]
		}
		if cell.X < cell.kenken.Size-1 {
			neighbors["right"] = cell.kenken.Board[cell.X+1][cell.Y]
		}
		if cell.Y < cell.kenken.Size-1 {
			neighbors["down"] = cell.kenken.Board[cell.X][cell.Y+1]
		}
	}

	return neighbors
}

// GetAllValues returns all values in the cell group
func (group *KenKenCellGroup) GetAllValues() []int {
	returnArray := make([]int, len(group.Cells))
	for i, cell := range group.Cells {
		returnArray[i] = cell.Value
	}
	return returnArray
}

// Grow tries to grow the cell group by adding a neighboring cell
func (group *KenKenCellGroup) Grow() bool {
	startingCellNumber := int(float64(len(group.Cells)) * rand.Float64())
	cellNum := startingCellNumber

	for {
		cellToGrowFrom := group.Cells[cellNum]
		cellNeighbors := cellToGrowFrom.GetNeighbors()
		neighborCellNum := int(float64(len(cellNeighbors)) * rand.Float64())

		for i := 0; i < len(cellNeighbors); i++ {
			neighborCell := cellNeighbors[(i+neighborCellNum)%len(cellNeighbors)]
			if neighborCell.CellGroup == nil {
				group.Cells = append(group.Cells, neighborCell)
				neighborCell.SetCellGroup(group)
				group.CurrentSize++
				return true
			}
		}

		cellNum = (cellNum + 1) % len(group.Cells)
		if cellNum == startingCellNumber {
			return false
		}
	}
}

// GetTopLeft returns the top-left cell in the group
func (group *KenKenCellGroup) GetTopLeft() *KenKenCell {
	cells := group.Cells
	topLeftCell := cells[0]

	for i := 1; i < len(cells); i++ {
		if cells[i].X <= topLeftCell.X {
			if cells[i].Y < topLeftCell.Y {
				topLeftCell = cells[i]
			}
		}
	}

	return topLeftCell
}

// Operation method implementations for each operation type
func (op SingleCell) MinCells() int  { return 1 }
func (op SingleCell) MaxCells() int  { return 1 }
func (op SingleCell) Symbol() string { return "" }
func (op SingleCell) Operation(arrayOfNumbers []int) (int, bool) {
	if len(arrayOfNumbers) > op.MaxCells() || len(arrayOfNumbers) < op.MinCells() {
		return 0, false
	}
	return arrayOfNumbers[0], true
}

func (op Addition) MinCells() int  { return 2 }
func (op Addition) MaxCells() int  { return -1 } // No max limit
func (op Addition) Symbol() string { return "+" }

func (op Addition) Operation(arrayOfNumbers []int) (int, bool) {
	if len(arrayOfNumbers) < op.MinCells() {
		return 0, false
	}

	resultOfOperation := 0
	for _, num := range arrayOfNumbers {
		resultOfOperation += num
	}
	return resultOfOperation, true
}

func (op Subtraction) MinCells() int  { return 2 }
func (op Subtraction) MaxCells() int  { return 2 }
func (op Subtraction) Symbol() string { return "-" }
func (op Subtraction) Operation(arrayOfNumbers []int) (int, bool) {
	if len(arrayOfNumbers) > op.MaxCells() || len(arrayOfNumbers) < op.MinCells() {
		return 0, false
	}

	if arrayOfNumbers[0] > arrayOfNumbers[1] {
		return arrayOfNumbers[0] - arrayOfNumbers[1], true
	}
	return arrayOfNumbers[1] - arrayOfNumbers[0], true
}

func (op Multiplication) MinCells() int  { return 2 }
func (op Multiplication) MaxCells() int  { return -1 } // No max limit
func (op Multiplication) Symbol() string { return "×" }

func (op Multiplication) Operation(arrayOfNumbers []int) (int, bool) {
	if len(arrayOfNumbers) < op.MinCells() {
		return 0, false
	}

	resultOfOperation := 1
	for _, num := range arrayOfNumbers {
		resultOfOperation *= num
	}
	return resultOfOperation, true
}

func (op Division) MinCells() int  { return 2 }
func (op Division) MaxCells() int  { return 2 }
func (op Division) Symbol() string { return "÷" }
func (op Division) Operation(arrayOfNumbers []int) (int, bool) {
	if len(arrayOfNumbers) > op.MaxCells() || len(arrayOfNumbers) < op.MinCells() {
		return 0, false
	}

	if arrayOfNumbers[0]%arrayOfNumbers[1] == 0 {
		return arrayOfNumbers[0] / arrayOfNumbers[1], true
	}
	if arrayOfNumbers[1]%arrayOfNumbers[0] == 0 {
		return arrayOfNumbers[1] / arrayOfNumbers[0], true
	}
	return 0, false
}

func (op Maximum) MinCells() int  { return 2 }
func (op Maximum) MaxCells() int  { return 3 }
func (op Maximum) Symbol() string { return "max" }
func (op Maximum) Operation(arrayOfNumbers []int) (int, bool) {
	if len(arrayOfNumbers) > op.MaxCells() || len(arrayOfNumbers) < op.MinCells() {
		return 0, false
	}

	resultOfOperation := arrayOfNumbers[0]
	for i := 1; i < len(arrayOfNumbers); i++ {
		if resultOfOperation < arrayOfNumbers[i] {
			resultOfOperation = arrayOfNumbers[i]
		}
	}
	return resultOfOperation, true
}

func (op Minimum) MinCells() int  { return 2 }
func (op Minimum) MaxCells() int  { return 3 }
func (op Minimum) Symbol() string { return "min" }
func (op Minimum) Operation(arrayOfNumbers []int) (int, bool) {
	if len(arrayOfNumbers) > op.MaxCells() || len(arrayOfNumbers) < op.MinCells() {
		return 0, false
	}

	resultOfOperation := arrayOfNumbers[0]
	for i := 1; i < len(arrayOfNumbers); i++ {
		if resultOfOperation > arrayOfNumbers[i] {
			resultOfOperation = arrayOfNumbers[i]
		}
	}
	return resultOfOperation, true
}

func (op Range) MinCells() int  { return 2 }
func (op Range) MaxCells() int  { return 4 }
func (op Range) Symbol() string { return "range" }
func (op Range) Operation(arrayOfNumbers []int) (int, bool) {
	if len(arrayOfNumbers) > op.MaxCells() || len(arrayOfNumbers) < op.MinCells() {
		return 0, false
	}

	minNum := arrayOfNumbers[0]
	maxNum := arrayOfNumbers[0]
	for i := 1; i < len(arrayOfNumbers); i++ {
		if maxNum < arrayOfNumbers[i] {
			maxNum = arrayOfNumbers[i]
		} else if minNum > arrayOfNumbers[i] {
			minNum = arrayOfNumbers[i]
		}
	}
	return maxNum - minNum, true
}

func (op Mod) MinCells() int  { return 2 }
func (op Mod) MaxCells() int  { return 2 }
func (op Mod) Symbol() string { return "%" }
func (op Mod) Operation(arrayOfNumbers []int) (int, bool) {
	if len(arrayOfNumbers) > op.MaxCells() || len(arrayOfNumbers) < op.MinCells() {
		return 0, false
	}

	if arrayOfNumbers[0] > arrayOfNumbers[1] {
		return arrayOfNumbers[0] % arrayOfNumbers[1], true
	}
	return arrayOfNumbers[1] % arrayOfNumbers[0], true
}

func (op Average) MinCells() int  { return 2 }
func (op Average) MaxCells() int  { return 4 }
func (op Average) Symbol() string { return "avg" }
func (op Average) Operation(arrayOfNumbers []int) (int, bool) {
	if len(arrayOfNumbers) > op.MaxCells() || len(arrayOfNumbers) < op.MinCells() {
		return 0, false
	}

	sum := 0
	for _, num := range arrayOfNumbers {
		sum += num
	}
	average := sum / len(arrayOfNumbers)
	if sum%len(arrayOfNumbers) == 0 {
		return average, true
	}
	return 0, false
}

func (op Parity) MinCells() int  { return 2 }
func (op Parity) MaxCells() int  { return 2 }
func (op Parity) Symbol() string { return "par" }
func (op Parity) Operation(arrayOfNumbers []int) (int, bool) {
	if len(arrayOfNumbers) > op.MaxCells() || len(arrayOfNumbers) < op.MinCells() {
		return 0, false
	}

	firstNum := arrayOfNumbers[0]
	secondNum := arrayOfNumbers[1]
	if (firstNum+secondNum)%2 == 0 {
		return 1, true
	}
	return 0, true
}

func (op Gcd) MinCells() int  { return 2 }
func (op Gcd) MaxCells() int  { return 3 }
func (op Gcd) Symbol() string { return "gcd" }
func (op Gcd) Operation(arrayOfNumbers []int) (int, bool) {
	if len(arrayOfNumbers) > op.MaxCells() || len(arrayOfNumbers) < op.MinCells() {
		return 0, false
	}

	// A recursive function for finding the gcd of two numbers
	var gcd func(a, b int) int
	gcd = func(a, b int) int {
		if b == 0 {
			return a
		}
		return gcd(b, a%b)
	}

	currentGCD := arrayOfNumbers[0]
	for i := 1; i < len(arrayOfNumbers); i++ {
		currentGCD = gcd(currentGCD, arrayOfNumbers[i])
	}
	return currentGCD, true
}

// NewKenKen creates a new KenKen puzzle
func NewKenKen(settings KenKenSettings) *Kenken {
	kenken := &Kenken{
		Size:                settings.Size,
		Settings:            settings,
		MinGroupSize:        1,
		DefaultMaxGroupSize: settings.MaxGroupSize,
		Operations:          []Operation{&SingleCell{}},
	}

	// Add operations based on settings
	if settings.Operations.Addition {
		kenken.Operations = append(kenken.Operations, &Addition{})
	}
	if settings.Operations.Subtraction {
		kenken.Operations = append(kenken.Operations, &Subtraction{})
	}
	if settings.Operations.Multiplication {
		kenken.Operations = append(kenken.Operations, &Multiplication{})
	}
	if settings.Operations.Division {
		kenken.Operations = append(kenken.Operations, &Division{})
	}
	if settings.Operations.Max {
		kenken.Operations = append(kenken.Operations, &Maximum{})
	}
	if settings.Operations.Min {
		kenken.Operations = append(kenken.Operations, &Minimum{})
	}
	if settings.Operations.Range {
		kenken.Operations = append(kenken.Operations, &Range{})
	}
	if settings.Operations.Mod {
		kenken.Operations = append(kenken.Operations, &Mod{})
	}
	if settings.Operations.Avg {
		kenken.Operations = append(kenken.Operations, &Average{})
	}
	if settings.Operations.Par {
		kenken.Operations = append(kenken.Operations, &Parity{})
	}
	if settings.Operations.Gcd {
		kenken.Operations = append(kenken.Operations, &Gcd{})
	}

	// Determine maxGroupSize based on operations
	for _, op := range kenken.Operations {
		maxOperationSize := op.MaxCells()
		if maxOperationSize < 0 {
			kenken.MaxGroupSize = kenken.DefaultMaxGroupSize
			break
		} else if kenken.MaxGroupSize == 0 {
			kenken.MaxGroupSize = maxOperationSize
		} else if maxOperationSize > kenken.MaxGroupSize {
			kenken.MaxGroupSize = maxOperationSize
		}
	}

	if kenken.MaxGroupSize > kenken.DefaultMaxGroupSize {
		kenken.MaxGroupSize = kenken.DefaultMaxGroupSize
	}

	builderArray := kenken.shuffledNumberArray(kenken.Size)

	// Initialize the board
	kenken.Board = make([][]*KenKenCell, kenken.Size)
	for x := 0; x < kenken.Size; x++ {
		kenken.Board[x] = make([]*KenKenCell, kenken.Size)
		for y := 0; y < kenken.Size; y++ {
			kenken.Board[x][y] = &KenKenCell{
				kenken: kenken,
				Value:  builderArray[(x+y)%kenken.Size],
			}
		}
	}

	// Shuffle the board
	kenken.shuffleBoard()

	// Assign coordinates to cells
	for x := 0; x < kenken.Size; x++ {
		for y := 0; y < kenken.Size; y++ {
			kenken.Board[x][y].X = x
			kenken.Board[x][y].Y = y
		}
	}

	// Create cell groups
	groupID := 1
	for x := 0; x < kenken.Size; x++ {
		for y := 0; y < kenken.Size; y++ {
			if kenken.Board[x][y].CellGroup == nil {
				maxSize := kenken.MaxGroupSize
				groupSize := int(rand.Float64()*float64(maxSize-kenken.MinGroupSize+1)) + kenken.MinGroupSize

				newCellGroup := &KenKenCellGroup{
					kenken:      kenken,
					GroupID:     groupID,
					Cells:       []*KenKenCell{kenken.Board[x][y]},
					CurrentSize: 1,
				}
				kenken.Board[x][y].SetCellGroup(newCellGroup)

				if groupSize != 1 {
					for m := 0; m < groupSize-1; m++ {
						newCellGroup.Grow()
					}
				}

				// Shuffle operations for better distribution
				kenken.shuffleArray(kenken.Operations)

				randomOperationStart := 0
				randomOperation := randomOperationStart
				foundOperation := false

				for !foundOperation {
					result, ok := kenken.Operations[randomOperation].Operation(newCellGroup.GetAllValues())
					if ok {
						newCellGroup.OperationDescription = kenken.Operations[randomOperation].Symbol() + fmt.Sprintf("%d", result)
						foundOperation = true
					} else {
						randomOperation = (randomOperation + 1) % len(kenken.Operations)
						if randomOperation == randomOperationStart {
							fmt.Println("No valid operation was found")
							break
						}
					}
				}

				kenken.CellGroups = append(kenken.CellGroups, newCellGroup)
				groupID++
			}
		}
	}

	return kenken
}

func (k *Kenken) shuffleBoard() {
	for i := 0; i < k.Size; i++ {
		// Swap columns
		column1 := int(rand.Float64() * float64(k.Size))
		column2 := int(rand.Float64() * float64(k.Size))

		for j := 0; j < k.Size; j++ {
			k.Board[j][column1], k.Board[j][column2] = k.Board[j][column2], k.Board[j][column1]
		}

		// Swap rows
		row1 := int(rand.Float64() * float64(k.Size))
		row2 := int(rand.Float64() * float64(k.Size))

		for j := 0; j < k.Size; j++ {
			k.Board[row1][j], k.Board[row2][j] = k.Board[row2][j], k.Board[row1][j]
		}
	}
}

func (k *Kenken) shuffleArray(array []Operation) {
	for i := 0; i < len(array)-1; i++ {
		randomNum := int(rand.Float64()*float64(len(array)-i)) + i
		array[i], array[randomNum] = array[randomNum], array[i]
	}
}

func (k *Kenken) shuffledNumberArray(n int) []int {
	numberArray := make([]int, n)
	for i := 0; i < n; i++ {
		numberArray[i] = i + 1
	}

	for i := 0; i < len(numberArray)-1; i++ {
		randomNum := int(rand.Float64()*float64(len(numberArray)-i)) + i
		numberArray[i], numberArray[randomNum] = numberArray[randomNum], numberArray[i]
	}

	return numberArray
}

func GenerateKenKen(settings KenKenSettings) *Kenken {
	// Generate the KenKen puzzle
	kenken := NewKenKen(settings)
	zlog.Info(context.Background(), "Generated KenKen puzzle", zap.Int("size", settings.Size), zap.Int("maxGroupSize", settings.MaxGroupSize))
	return kenken
}
