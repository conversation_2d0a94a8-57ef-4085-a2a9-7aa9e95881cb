package questionsGenerator

import (
	"fmt"
	"math"

	"matiksOfficial/matiks-server-go/internal/domain/questions/presets"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func getLCMQuestion(numbers []int, rating int, tags []string) *models.Question {
	expression := make([]string, 0, 2*len(numbers))

	expression = append(expression, fmt.Sprintf("%d", numbers[0]))
	expression = append(expression, "LCM")
	expression = append(expression, fmt.Sprintf("%d", numbers[1]))

	answer := LCM(numbers[0], numbers[1])

	presetIdentifier := presets.PresetIdentifierGenerator(models.PresetCategoryLcm, numbers)

	return &models.Question{
		Expression:       expression,
		Answers:          []string{fmt.Sprintf("%d", answer)},
		QuestionType:     utils.AllocPtr(models.QuestionTypeFillInTheBlanks),
		Rating:           utils.AllocPtr(rating),
		Tags:             append([]string{QUESTION_TAG_LCM, QUESTION_TAG_ARITHMETIC}, tags...),
		PresetIdentifier: presetIdentifier,
	}
}

func LCM(a, b int) int {
	if a == 0 || b == 0 {
		return 0
	}
	// Using the formula: LCM(a, b) = |a * b| / HCF(a, b)
	return int(math.Abs(float64(a*b))) / HCF(a, b)
}

func CreateLCMQuestion(rating int) *models.Question {
	var numConfig []NumConfig

	switch {

	case rating <= 600:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
		}
	case rating <= 700:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 800:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
		}
	case rating <= 900:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1000:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1100:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
		}
	case rating <= 1200:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1300:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 1, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 1400:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 1500:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1600:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 1700:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1800:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1900:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2000:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2100:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2200:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2300:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2400:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2500:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2600:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 5, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2700:
		numConfig = []NumConfig{
			{NumDigit: 5, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 5, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2800:
		numConfig = []NumConfig{
			{NumDigit: 5, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 5, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2900:
		numConfig = []NumConfig{
			{NumDigit: 5, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 6, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 3000:
		numConfig = []NumConfig{
			{NumDigit: 5, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 6, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 3100:
		numConfig = []NumConfig{
			{NumDigit: 6, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 6, Level: DigitDifficultyLevels.HARD},
		}
	default:
		numConfig = []NumConfig{
			{NumDigit: 6, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 6, Level: DigitDifficultyLevels.HARD},
		}
	}

	numbers := make([]int, 0, len(numConfig))

	num1 := getRandomNumber(numConfig[0].NumDigit, numConfig[0].Level)
	num2 := getRandomNumber(numConfig[1].NumDigit, numConfig[1].Level)

	numbers = append(numbers, num1)
	numbers = append(numbers, num2)

	return getLCMQuestion(numbers, rating, []string{})
}
