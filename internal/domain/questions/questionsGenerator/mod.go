package questionsGenerator

import (
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/questions/presets"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func GetModQuestion(numbers []int, rating int, tags []string) *models.Question {
	if len(numbers) < 2 {
		return nil
	}
	if numbers[0] == 0 {
		return nil
	}

	expression := make([]string, 0, 2*len(numbers))
	answer := numbers[1] % numbers[0]

	expression = append(expression, fmt.Sprintf("%d", numbers[1]))
	expression = append(expression, "MOD")
	expression = append(expression, fmt.Sprintf("%d", numbers[0]))

	presetIdentifier := presets.PresetIdentifierGenerator(models.PresetCategoryMod, numbers)

	return &models.Question{
		Expression:       expression,
		Answers:          []string{fmt.Sprintf("%d", answer)},
		QuestionType:     utils.AllocPtr(models.QuestionTypeFillInTheBlanks),
		Rating:           utils.AllocPtr(rating),
		Tags:             append([]string{QUESTION_TAG_MOD, QUESTION_TAG_ARITHMETIC}, tags...),
		PresetIdentifier: presetIdentifier,
	}
}

func CreateModQuestion(rating int) *models.Question {
	var numConfig []NumConfig

	switch {
	case rating <= 600:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
		}
	case rating <= 700:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 800:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 900:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 1000:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 1100:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1200:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1300:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 1400:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1500:
		numConfig = []NumConfig{
			{NumDigit: 1, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1600:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1700:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1800:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1900:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2000:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2100:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2200:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2300:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2400:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2500:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2600:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2700:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2800:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2900:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 3000:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 5, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 3100:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 5, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 3200:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 6, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 3300:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 6, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 3400:
		numConfig = []NumConfig{
			{NumDigit: 5, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 7, Level: DigitDifficultyLevels.MEDIUM},
		}
	default:
		numConfig = []NumConfig{
			{NumDigit: 5, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 7, Level: DigitDifficultyLevels.MEDIUM},
		}

	}

	numbers := make([]int, 0, len(numConfig)*2)
	for _, config := range numConfig {
		num := getRandomNumber(config.NumDigit, config.Level)
		numbers = append(numbers, num)
	}

	return GetModQuestion(numbers, rating, []string{})
}
