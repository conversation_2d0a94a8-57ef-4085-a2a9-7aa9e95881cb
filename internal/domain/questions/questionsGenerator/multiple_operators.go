package questionsGenerator

import (
	"fmt"
	"math/rand/v2"
	"strconv"
	"strings"

	"matiksOfficial/matiks-server-go/internal/domain/questions/presets"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

// Constants for supported operators
const (
	OPERATOR_ADDITION       = "+"
	OPERATOR_SUBTRACTION    = "-"
	OPERATOR_MULTIPLICATION = "*"
	OPERATOR_DIVISION       = "/"
	OPERATOR_EXPONENT       = "^"
	QUESTION_TAG_BODMAS     = "BODMAS"
)

// getRatingBasedConfig returns the configuration based on rating
func getRatingBasedConfig(rating int) MultiOpNumConfig {
	switch {
	case rating <= 600:
		return MultiOpNumConfig{
			NumConfig: []NumConfig{
				{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
				{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
			},
			Operators: 1,
			Brackets:  0,
		}
	case rating <= 800:
		return MultiOpNumConfig{
			NumConfig: []NumConfig{
				{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
				{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
				{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
			},
			Operators: 2,
			Brackets:  1,
		}
	case rating <= 1000:
		return MultiOpNumConfig{
			NumConfig: []NumConfig{
				{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
				{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
				{NumDigit: 1, Level: DigitDifficultyLevels.EASY},
			},
			Operators: 2,
			Brackets:  1,
		}
	case rating <= 1200:
		return MultiOpNumConfig{
			NumConfig: []NumConfig{
				{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
				{NumDigit: 1, Level: DigitDifficultyLevels.HARD},
				{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			},
			Operators: 2,
			Brackets:  2,
		}
	case rating <= 1600:
		return MultiOpNumConfig{
			NumConfig: []NumConfig{
				{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
				{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
				{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM},
				{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			},
			Operators: 3,
			Brackets:  2,
		}
	case rating <= 2000:
		return MultiOpNumConfig{
			NumConfig: []NumConfig{
				{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
				{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
				{NumDigit: 3, Level: DigitDifficultyLevels.EASY},
				{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
				{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			},
			Operators: 4,
			Brackets:  3,
		}
	default:
		return MultiOpNumConfig{
			NumConfig: []NumConfig{
				{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
				{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
				{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
				{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
				{NumDigit: 3, Level: DigitDifficultyLevels.HARD},
				{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			},
			Operators: 5,
			Brackets:  4,
		}
	}
}

func getRandomNumberMultiOp(numDigits int, level DigitDifficultyLevel) int {
	if numDigits <= 0 {
		return 0
	}

	minVal := 1
	maxVal := 9

	switch level {
	case DigitDifficultyLevels.EASY:
		if numDigits == 1 {
			return rand.IntN(5) + 1
		}
		minVal = int(pow10(numDigits - 1))
		maxVal = minVal*5 - 1
	case DigitDifficultyLevels.MEDIUM:
		minVal = int(pow10(numDigits - 1))
		maxVal = int(pow10(numDigits)) - 1
	case DigitDifficultyLevels.HARD:
		if numDigits == 1 {
			return rand.IntN(4) + 6
		}
		minVal = int(pow10(numDigits-1)) * 5
		maxVal = int(pow10(numDigits)) - 1
	}

	return rand.IntN(maxVal-minVal+1) + minVal
}

func pow10(n int) int {
	result := 1
	for i := 0; i < n; i++ {
		result *= 10
	}
	return result
}

func getRandomOperatorMultiOp(excludeDivision bool, rating int) string {
	if excludeDivision {
		operators := []string{OPERATOR_ADDITION, OPERATOR_SUBTRACTION, OPERATOR_MULTIPLICATION}

		if rating > 1200 {
			operators = append(operators, OPERATOR_EXPONENT)
		}
		return operators[rand.IntN(len(operators))]
	}

	return randomOperator(rating)
}

func resultIsWholeNumber(expr string) bool {
	if !strings.HasSuffix(expr, " $") {
		expr = expr + " $"
	}

	result, err := EvaluateExpression(expr)
	if err != nil {
		return false
	}

	num, err := strconv.ParseFloat(result, 64)
	if err != nil {
		return false
	}

	return num == float64(int(num))
}

func findDivisiblePairs(numbers []int) map[int][]int {
	divisorMap := make(map[int][]int)

	for i := 0; i < len(numbers); i++ {
		if numbers[i] == 0 {
			continue
		}

		for j := 0; j < len(numbers); j++ {
			if i == j {
				continue
			}

			if numbers[j]%numbers[i] == 0 {
				divisorMap[numbers[i]] = append(divisorMap[numbers[i]], numbers[j])
			}
		}
	}

	return divisorMap
}

func hasDivisor(num, divisor int) bool {
	return divisor != 0 && num%divisor == 0
}

func buildExpressionTree(numbers []int, operatorCount, rating int) string {
	if len(numbers) <= 1 {
		return fmt.Sprintf("%d", numbers[0])
	}

	var builder strings.Builder
	builder.WriteString(fmt.Sprintf("%d", numbers[0]))

	divisorMap := findDivisiblePairs(numbers)

	usedNumbers := map[int]bool{numbers[0]: true}
	operatorsUsed := 0
	lastNum := numbers[0]

	// Add remaining numbers with appropriate operators
	for i := 1; i < len(numbers) && operatorsUsed < operatorCount; i++ {
		if usedNumbers[numbers[i]] {
			continue
		}

		// Decide on operator
		var op string

		// For higher ratings, allow negative numbers occasionally
		if rating > 1500 && rand.IntN(5) == 0 {
			// Instead of a straight number, use a negative
			numbers[i] = -numbers[i]
		}

		// Possibly use exponents for higher ratings
		if rating > 1200 && rand.IntN(8) == 0 {
			op = OPERATOR_EXPONENT
			// For exponents, keep them small (usually 2 or 3) to avoid too large results
			if numbers[i] > 5 {
				numbers[i] = rand.IntN(3) + 2 // 2, 3, or 4
			}
		} else if op == "" && rand.IntN(4) > 0 && hasDivisor(lastNum, numbers[i]) {
			// Check if the last number is divisible by the current number
			if lastNum%numbers[i] == 0 && numbers[i] != 0 {
				op = OPERATOR_DIVISION
			}
		}

		// If division or exponent wasn't used, try another operator
		if op == "" {
			// Check if we can use division with a multiple from our divisor map
			divisibleBy := divisorMap[numbers[i]]
			if len(divisibleBy) > 0 && rand.IntN(4) > 0 { // 75% chance to use division with a multiple
				// Find an unused multiple
				for _, multiple := range divisibleBy {
					if !usedNumbers[multiple] {
						// Replace the current number with the multiple and use division
						op = OPERATOR_DIVISION
						// Update the builder to use the multiple instead of lastNum
						builderStr := builder.String()
						lastSpaceIdx := strings.LastIndex(builderStr, " ")

						if lastSpaceIdx == -1 {
							builder = strings.Builder{}
							builder.WriteString(fmt.Sprintf("%d", multiple))
						} else {
							// Replace the last number with the multiple
							newStr := builderStr[:lastSpaceIdx+1] + fmt.Sprintf("%d", multiple)
							builder = strings.Builder{}
							builder.WriteString(newStr)
						}
						lastNum = multiple
						usedNumbers[multiple] = true
						usedNumbers[numbers[i-1]] = false
						break
					}
				}
			}

			if op == "" {
				op = getRandomOperatorMultiOp(true, rating)
			}
		}

		builder.WriteString(" " + op + " ")
		builder.WriteString(fmt.Sprintf("%d", numbers[i]))
		lastNum = numbers[i]
		usedNumbers[numbers[i]] = true
		operatorsUsed++
	}

	for i := 0; i < len(numbers) && operatorsUsed < operatorCount; i++ {
		if !usedNumbers[numbers[i]] {
			op := getRandomOperatorMultiOp(false, rating)
			builder.WriteString(" " + op + " ")
			builder.WriteString(fmt.Sprintf("%d", numbers[i]))
			usedNumbers[numbers[i]] = true
			operatorsUsed++
		}
	}

	return builder.String()
}

func containsBracket(token string) bool {
	return strings.ContainsAny(token, "()[]")
}

// addBrackets inserts bracket pairs into the expression with more complex nesting support
func addBrackets(expr string, count int) string {
	tokens := strings.Fields(expr)
	bracketTypes := [][2]string{{"(", ")"}, {"[", "]"}}

	// Calculate maximum possible bracket pairs
	maxPairs := (len(tokens) - 1) / 2
	if count > maxPairs {
		count = maxPairs
	}

	for i := 0; i < count; i++ {
		var validPositions [][2]int

		// Try to find positions that would create more complex expressions
		// like "(a + b) * c" or "a * (b + c)"
		for start := 0; start < len(tokens)-2; start += 2 {
			// Skip if token already has brackets
			if containsBracket(tokens[start]) {
				continue
			}

			// Look for valid end positions
			for end := start + 2; end < len(tokens); end += 2 {
				// Skip if token already has brackets
				if containsBracket(tokens[end]) {
					continue
				}

				// If there's an operator in between, this is a good bracket candidate
				hasMixedOperators := false
				for j := start + 1; j < end; j += 2 {
					if j+2 < end && tokens[j] != tokens[j+2] {
						hasMixedOperators = true
						break
					}
				}

				// Prioritize brackets around addition/subtraction followed by multiplication/division
				// to enforce BODMAS rule
				if hasMixedOperators {
					// Higher weight for brackets that enforce BODMAS
					validPositions = append(validPositions, [2]int{start, end})
					validPositions = append(validPositions, [2]int{start, end})
				} else {
					validPositions = append(validPositions, [2]int{start, end})
				}
			}
		}

		if len(validPositions) == 0 {
			for start := 0; start < len(tokens)-2; start += 2 {
				if containsBracket(tokens[start]) {
					continue
				}

				for end := start + 2; end < len(tokens); end += 2 {
					if containsBracket(tokens[end]) {
						continue
					}

					validPositions = append(validPositions, [2]int{start, end})
				}
			}
		}

		if len(validPositions) == 0 {
			break
		}

		pos := validPositions[rand.IntN(len(validPositions))]
		bracket := bracketTypes[rand.IntN(len(bracketTypes))]

		tokens[pos[0]] = bracket[0] + " " + tokens[pos[0]]
		tokens[pos[1]] = tokens[pos[1]] + " " + bracket[1]
	}

	if len(tokens) > 6 && count > 1 {
		for i := 0; i < len(tokens); i++ {
			if strings.HasPrefix(tokens[i], "(") || strings.HasPrefix(tokens[i], "[") {
				openType := tokens[i][0:1]
				closeType := ")"
				if openType == "[" {
					closeType = "]"
				}

				level := 1
				endPos := -1

				for j := i + 1; j < len(tokens); j++ {
					if strings.HasPrefix(tokens[j], openType) {
						level++
					} else if strings.HasSuffix(tokens[j], closeType) {
						level--
						if level == 0 {
							endPos = j
							break
						}
					}
				}

				// If we found a matching pair with enough tokens between them
				if endPos > i+4 {
					// Find a subexpression to bracket inside the existing brackets
					var subValidPos [][2]int
					for start := i + 1; start < endPos-2; start += 2 {
						if containsBracket(tokens[start]) {
							continue
						}

						for end := start + 2; end < endPos; end += 2 {
							if containsBracket(tokens[end]) {
								continue
							}

							subValidPos = append(subValidPos, [2]int{start, end})
						}
					}

					if len(subValidPos) > 0 {
						pos := subValidPos[rand.IntN(len(subValidPos))]
						bracket := bracketTypes[rand.IntN(len(bracketTypes))]

						tokens[pos[0]] = bracket[0] + " " + tokens[pos[0]]
						tokens[pos[1]] = tokens[pos[1]] + " " + bracket[1]

						break
					}
				}
			}
		}
	}

	return strings.Join(tokens, " ")
}

func buildExpression(numbers []int, operatorCount, bracketCount, rating int) (string, int, error) {
	if len(numbers) < 2 || len(numbers) <= operatorCount {
		return "", 0, fmt.Errorf("invalid numbers and operators configuration")
	}

	shuffledNumbers := make([]int, len(numbers))
	copy(shuffledNumbers, numbers)
	rand.Shuffle(len(shuffledNumbers), func(i, j int) {
		shuffledNumbers[i], shuffledNumbers[j] = shuffledNumbers[j], shuffledNumbers[i]
	})

	maxAttempts := 10
	for attempt := 0; attempt < maxAttempts; attempt++ {
		expr := buildExpressionTree(shuffledNumbers, operatorCount, rating)

		if bracketCount > 0 {
			expr = addBrackets(expr, bracketCount)
		}

		if resultIsWholeNumber(expr) {
			// Get the value
			result, err := EvaluateExpression(expr + " $")
			if err != nil {
				continue
			}

			value, err := strconv.Atoi(result)
			if err != nil {
				continue
			}

			return expr, value, nil
		}

		// Shuffle numbers again for next attempt
		rand.Shuffle(len(shuffledNumbers), func(i, j int) {
			shuffledNumbers[i], shuffledNumbers[j] = shuffledNumbers[j], shuffledNumbers[i]
		})
	}

	return buildExpressionWithoutDivision(numbers, operatorCount, bracketCount, rating)
}

func buildExpressionWithoutDivision(numbers []int, operatorCount, bracketCount, rating int) (string, int, error) {
	if len(numbers) < 2 || len(numbers) <= operatorCount {
		return "", 0, fmt.Errorf("invalid numbers and operators configuration")
	}

	shuffledNumbers := make([]int, len(numbers))
	copy(shuffledNumbers, numbers)
	rand.Shuffle(len(shuffledNumbers), func(i, j int) {
		shuffledNumbers[i], shuffledNumbers[j] = shuffledNumbers[j], shuffledNumbers[i]
	})

	var builder strings.Builder
	builder.WriteString(fmt.Sprintf("%d", shuffledNumbers[0]))

	usedNumbers := map[int]bool{shuffledNumbers[0]: true}
	operatorsUsed := 0

	for i := 1; i < len(shuffledNumbers) && operatorsUsed < operatorCount; i++ {
		if usedNumbers[shuffledNumbers[i]] {
			continue
		}

		if rating > 1500 && rand.IntN(5) == 0 {
			shuffledNumbers[i] = -shuffledNumbers[i]
		}

		var op string
		if rating > 1200 && rand.IntN(8) == 0 {
			op = OPERATOR_EXPONENT
			if shuffledNumbers[i] > 5 {
				shuffledNumbers[i] = rand.IntN(3) + 2
			}
		} else {
			ops := []string{OPERATOR_ADDITION, OPERATOR_SUBTRACTION, OPERATOR_MULTIPLICATION}
			op = ops[rand.IntN(len(ops))]
		}

		builder.WriteString(" " + op + " ")
		builder.WriteString(fmt.Sprintf("%d", shuffledNumbers[i]))
		usedNumbers[shuffledNumbers[i]] = true
		operatorsUsed++
	}

	for i := 0; i < len(shuffledNumbers) && operatorsUsed < operatorCount; i++ {
		if !usedNumbers[shuffledNumbers[i]] {
			ops := []string{OPERATOR_ADDITION, OPERATOR_SUBTRACTION, OPERATOR_MULTIPLICATION}
			if rating > 1200 && rand.IntN(8) == 0 {
				ops = append(ops, OPERATOR_EXPONENT)
				if shuffledNumbers[i] > 5 {
					shuffledNumbers[i] = rand.IntN(3) + 2
				}
			}

			op := ops[rand.IntN(len(ops))]
			builder.WriteString(" " + op + " ")
			builder.WriteString(fmt.Sprintf("%d", shuffledNumbers[i]))
			usedNumbers[shuffledNumbers[i]] = true
			operatorsUsed++
		}
	}

	expr := builder.String()

	if bracketCount > 0 {
		expr = addBrackets(expr, bracketCount)
	}

	result, err := EvaluateExpression(expr + " $")
	if err != nil {
		return "", 0, err
	}

	value, err := strconv.Atoi(result)
	if err != nil {
		return "", 0, fmt.Errorf("expression result is not a whole number")
	}

	return expr, value, nil
}

func GenerateBODMASExpression(rating int) (string, int, error) {
	config := getRatingBasedConfig(rating)

	var numbers []int
	for _, numConfig := range config.NumConfig {
		num := getRandomNumberMultiOp(numConfig.NumDigit, numConfig.Level)
		numbers = append(numbers, num)
	}

	for attempt := 0; attempt < 10; attempt++ {
		expr, value, err := buildExpression(numbers, config.Operators, config.Brackets, rating)
		if err == nil {
			return expr, value, nil
		}
	}

	expr, value, err := buildExpressionWithoutDivision(numbers, config.Operators, config.Brackets, rating)
	return expr, value, err
}

func extractNumbersFromExpression(expr string) []int {
	tokens := strings.Fields(expr)
	var numbers []int

	for _, token := range tokens {
		token = strings.Trim(token, "()[]")

		if num, err := strconv.Atoi(token); err == nil {
			numbers = append(numbers, num)
		}
	}

	return numbers
}

func CreateMultipleOperatorsQuestions(rating int) *models.Question {
	expr, value, err := GenerateBODMASExpression(rating)
	if err != nil {
		return nil
	}

	if !strings.HasSuffix(expr, " $") {
		expr = expr + " $"
	}

	exprTokens := strings.Fields(expr)
	answer := fmt.Sprintf("%d", value)

	numbers := extractNumbersFromExpression(expr)
	presetIdentifier := presets.PresetIdentifierGenerator(models.PresetCategoryMulOp, numbers)

	tags := []string{QUESTION_TAG_MULTIPLE_OPERATORS, QUESTION_TAG_ARITHMETIC, QUESTION_TAG_BODMAS}

	if strings.Contains(expr, OPERATOR_DIVISION) {
		tags = append(tags, QUESTION_TAG_DIVISION)
	}
	if strings.Contains(expr, OPERATOR_MULTIPLICATION) {
		tags = append(tags, QUESTION_TAG_MULTIPLICATION)
	}
	if strings.Contains(expr, OPERATOR_ADDITION) {
		tags = append(tags, QUESTION_TAG_ADDITION)
	}
	if strings.Contains(expr, OPERATOR_EXPONENT) {
		tags = append(tags, QUESTION_TAG_EXPONENT)
	}

	return &models.Question{
		Expression:       exprTokens,
		Answers:          []string{answer},
		QuestionType:     utils.AllocPtr(models.QuestionTypeFillInTheBlanks),
		Rating:           utils.AllocPtr(rating),
		Tags:             tags,
		PresetIdentifier: presetIdentifier,
	}
}
