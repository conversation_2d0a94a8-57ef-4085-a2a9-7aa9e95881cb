package questionsGenerator

import (
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/questions"
	"matiksOfficial/matiks-server-go/internal/domain/questions/presets"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func getMultiplicationQuestion(numbers []int, rating int, tags []string) *models.Question {
	expression := make([]string, 0, len(numbers)*2)
	for index, number := range numbers {
		if index != 0 {
			expression = append(expression, "×")
		}
		expression = append(expression, fmt.Sprintf("%d", number))
	}
	answers := []string{fmt.Sprintf("%d", product(numbers))}
	presetIdentifier := presets.PresetIdentifierGenerator(models.PresetCategoryMult, numbers)

	return &models.Question{
		Expression:       expression,
		Answers:          answers,
		QuestionType:     utils.AllocPtr(models.QuestionTypeFillInTheBlanks),
		Rating:           utils.AllocPtr(rating),
		Tags:             append([]string{QUESTION_TAG_MULTIPLICATION, QUESTION_TAG_ARITHMETIC}, tags...),
		PresetIdentifier: presetIdentifier,
		MaxTimeLimit:     utils.AllocPtr(int(questions.GetExpectedTimeToSolveQuestion(rating, rating, presetIdentifier).Seconds())),
	}
}

func product(numbers []int) int {
	result := 1
	for _, num := range numbers {
		result *= num
	}
	return result
}

func createMultiplicationQuestion(rating int) *models.Question {
	var numConfig []NumConfig

	switch {
	case rating <= 700:
		numConfig = []NumConfig{{1, DigitDifficultyLevels.EASY}, {1, DigitDifficultyLevels.MEDIUM}}
	case rating <= 800:
		numConfig = []NumConfig{{1, DigitDifficultyLevels.MEDIUM}, {1, DigitDifficultyLevels.MEDIUM}}
	case rating <= 900:
		numConfig = []NumConfig{{1, DigitDifficultyLevels.MEDIUM}, {1, DigitDifficultyLevels.HARD}}
	case rating <= 1000:
		numConfig = []NumConfig{{1, DigitDifficultyLevels.HARD}, {1, DigitDifficultyLevels.HARD}}
	case rating <= 1100:
		numConfig = []NumConfig{{2, DigitDifficultyLevels.EASY}, {1, DigitDifficultyLevels.MEDIUM}}
	case rating <= 1150:
		numConfig = []NumConfig{{2, DigitDifficultyLevels.MEDIUM}, {1, DigitDifficultyLevels.MEDIUM}}
	case rating <= 1200:
		numConfig = []NumConfig{{2, DigitDifficultyLevels.MEDIUM}, {1, DigitDifficultyLevels.HARD}}
	case rating <= 1250:
		numConfig = []NumConfig{{2, DigitDifficultyLevels.HARD}, {1, DigitDifficultyLevels.MEDIUM}}
	case rating <= 1300:
		numConfig = []NumConfig{{2, DigitDifficultyLevels.HARD}, {1, DigitDifficultyLevels.HARD}}
	case rating <= 1400:
		numConfig = []NumConfig{{3, DigitDifficultyLevels.MEDIUM}, {1, DigitDifficultyLevels.MEDIUM}}
	case rating <= 1500:
		numConfig = []NumConfig{{3, DigitDifficultyLevels.MEDIUM}, {1, DigitDifficultyLevels.HARD}}
	case rating <= 1600:
		numConfig = []NumConfig{{3, DigitDifficultyLevels.HARD}, {1, DigitDifficultyLevels.MEDIUM}}
	case rating <= 1700:
		numConfig = []NumConfig{{2, DigitDifficultyLevels.EASY}, {2, DigitDifficultyLevels.EASY}}
	case rating <= 1800:
		numConfig = []NumConfig{{2, DigitDifficultyLevels.MEDIUM}, {2, DigitDifficultyLevels.EASY}}
	case rating <= 1900:
		numConfig = []NumConfig{{4, DigitDifficultyLevels.MEDIUM}, {1, DigitDifficultyLevels.MEDIUM}}
	case rating <= 2000:
		numConfig = []NumConfig{{2, DigitDifficultyLevels.MEDIUM}, {2, DigitDifficultyLevels.MEDIUM}}
	case rating <= 2100:
		numConfig = []NumConfig{{2, DigitDifficultyLevels.HARD}, {2, DigitDifficultyLevels.MEDIUM}}
	case rating <= 2200:
		numConfig = []NumConfig{{3, DigitDifficultyLevels.EASY}, {2, DigitDifficultyLevels.MEDIUM}}
	case rating <= 2300:
		numConfig = []NumConfig{{3, DigitDifficultyLevels.EASY}, {2, DigitDifficultyLevels.HARD}}
	case rating <= 2400:
		numConfig = []NumConfig{{2, DigitDifficultyLevels.HARD}, {2, DigitDifficultyLevels.HARD}}
	case rating <= 2500:
		numConfig = []NumConfig{{3, DigitDifficultyLevels.MEDIUM}, {2, DigitDifficultyLevels.MEDIUM}}
	case rating <= 2600:
		numConfig = []NumConfig{{3, DigitDifficultyLevels.MEDIUM}, {2, DigitDifficultyLevels.HARD}}
	case rating <= 2700:
		numConfig = []NumConfig{{4, DigitDifficultyLevels.MEDIUM}, {2, DigitDifficultyLevels.MEDIUM}}
	case rating <= 2800:
		numConfig = []NumConfig{{4, DigitDifficultyLevels.HARD}, {2, DigitDifficultyLevels.HARD}}

	case rating <= 2900:
		numConfig = []NumConfig{{3, DigitDifficultyLevels.MEDIUM}, {3, DigitDifficultyLevels.MEDIUM}}

	case rating <= 3000:
		numConfig = []NumConfig{{3, DigitDifficultyLevels.HARD}, {3, DigitDifficultyLevels.MEDIUM}}

	case rating <= 3100:
		numConfig = []NumConfig{{5, DigitDifficultyLevels.MEDIUM}, {2, DigitDifficultyLevels.HARD}}

	case rating <= 3200:
		numConfig = []NumConfig{{5, DigitDifficultyLevels.HARD}, {2, DigitDifficultyLevels.HARD}}

	case rating <= 3300:
		numConfig = []NumConfig{{5, DigitDifficultyLevels.EASY}, {3, DigitDifficultyLevels.EASY}}

	case rating <= 3400:
		numConfig = []NumConfig{{5, DigitDifficultyLevels.EASY}, {3, DigitDifficultyLevels.MEDIUM}}

	case rating <= 3500:
		numConfig = []NumConfig{{5, DigitDifficultyLevels.EASY}, {3, DigitDifficultyLevels.HARD}}

	case rating <= 3600:
		numConfig = []NumConfig{{5, DigitDifficultyLevels.MEDIUM}, {3, DigitDifficultyLevels.HARD}}

	case rating <= 3700:
		numConfig = []NumConfig{{5, DigitDifficultyLevels.HARD}, {3, DigitDifficultyLevels.HARD}}

	case rating <= 3800:
		numConfig = []NumConfig{{5, DigitDifficultyLevels.MEDIUM}, {4, DigitDifficultyLevels.MEDIUM}}

	case rating <= 3900:
		numConfig = []NumConfig{{5, DigitDifficultyLevels.HARD}, {4, DigitDifficultyLevels.MEDIUM}}

	case rating <= 4000:
		numConfig = []NumConfig{{5, DigitDifficultyLevels.HARD}, {4, DigitDifficultyLevels.HARD}}

	default:
		numConfig = []NumConfig{{5, DigitDifficultyLevels.MEDIUM}, {5, DigitDifficultyLevels.MEDIUM}}
	}

	numbers := make([]int, len(numConfig))
	for i, config := range numConfig {
		numbers[i] = getRandomNumber(config.NumDigit, config.Level)
	}

	return getMultiplicationQuestion(numbers, rating, nil)
}
