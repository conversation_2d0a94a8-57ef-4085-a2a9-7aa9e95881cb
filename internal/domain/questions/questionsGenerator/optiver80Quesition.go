package questionsGenerator

import (
	"fmt"
	"math/rand/v2"
	"strings"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/models"
)

func GenerateOptiver80Questions() []*models.ContestQuestion {
	mediumRatings := GenerateUniformRatings(1000, 1500, 50)
	hardRatings := GenerateUniformRatings(1500, 2000, 30)
	ratings := append(mediumRatings, hardRatings...)
	rand.Shuffle(len(ratings), func(i, j int) { ratings[i], ratings[j] = ratings[j], ratings[i] })
	questions := make([]*models.ContestQuestion, 0, len(ratings))
	uniqueExpressions := make(map[string]struct{})

	for i, rating := range ratings {
		var question *models.Question
		unique := false
		attempts := 10
		question = GetRandomArithmeticQuestion(rating, nil)

		for !unique && attempts > 0 {
			expressionKey := strings.Join(question.Expression, " ")
			if _, exists := uniqueExpressions[expressionKey]; !exists {
				unique = true
				uniqueExpressions[expressionKey] = struct{}{}
			} else {
				attempts--
				question = GetRandomArithmeticQuestion(rating, nil)
			}
		}
		questionId := fmt.Sprintf("optiver80_%d", i+1)
		question.ID = &questionId
		question.Tags = append(question.Tags, constants.Optiver80Tag)
		questions = append(questions, &models.ContestQuestion{
			ID:       questionId,
			Question: question,
			Points:   rating,
		})
	}
	return questions
}
