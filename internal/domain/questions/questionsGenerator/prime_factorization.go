package questionsGenerator

import (
	"fmt"
	"math/rand/v2"
	"sort"

	"matiksOfficial/matiks-server-go/internal/domain/questions/presets"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

var validPrimeNumbers = []int{2, 3, 5, 7, 11, 13, 17, 19}

func formatPrimeFactorization(factors []int) []string {
	result := make([]string, len(factors))
	for i, factor := range factors {
		result[i] = fmt.Sprintf("%d", factor)
	}
	return result
}

func getPrimeFactorizationQuestion(primeFactors []int, question, rating int, tags []string) *models.Question {
	expression := []string{fmt.Sprintf("%d", question)}

	answer := formatPrimeFactorization(primeFactors)

	presetIdentifier := presets.PresetIdentifierGenerator(models.PresetCategoryPf, []int{question})

	return &models.Question{
		Expression:       expression,
		Answers:          answer,
		QuestionType:     utils.AllocPtr(models.QuestionTypeFillInTheBlanks),
		Rating:           utils.AllocPtr(rating),
		Tags:             append([]string{QUESTION_TAG_PRIME_FACTORIZATION, QUESTION_TAG_ARITHMETIC}, tags...),
		PresetIdentifier: presetIdentifier,
	}
}

func CreatePrimeFactorizationQuestion(rating int) *models.Question {
	var numConfig NumConfig

	switch {
	case rating <= 600:
		numConfig = NumConfig{NumDigit: 1, Level: DigitDifficultyLevels.EASY}
	case rating <= 700:
		numConfig = NumConfig{NumDigit: 1, Level: DigitDifficultyLevels.EASY}
	case rating <= 800:
		numConfig = NumConfig{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM}
	case rating <= 900:
		numConfig = NumConfig{NumDigit: 1, Level: DigitDifficultyLevels.MEDIUM}
	case rating <= 1000:
		numConfig = NumConfig{NumDigit: 1, Level: DigitDifficultyLevels.EASY}
	case rating <= 1100:
		numConfig = NumConfig{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM}
	case rating <= 1200:
		numConfig = NumConfig{NumDigit: 2, Level: DigitDifficultyLevels.HARD}
	case rating <= 1300:
		numConfig = NumConfig{NumDigit: 2, Level: DigitDifficultyLevels.EASY}
	case rating <= 1400:
		numConfig = NumConfig{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM}
	case rating <= 1500:
		numConfig = NumConfig{NumDigit: 3, Level: DigitDifficultyLevels.HARD}
	case rating <= 1600:
		numConfig = NumConfig{NumDigit: 3, Level: DigitDifficultyLevels.EASY}
	case rating <= 1700:
		numConfig = NumConfig{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM}
	case rating <= 1800:
		numConfig = NumConfig{NumDigit: 4, Level: DigitDifficultyLevels.HARD}
	case rating <= 1900:
		numConfig = NumConfig{NumDigit: 4, Level: DigitDifficultyLevels.EASY}
	case rating <= 2000:
		numConfig = NumConfig{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM}
	case rating <= 2100:
		numConfig = NumConfig{NumDigit: 5, Level: DigitDifficultyLevels.HARD}
	case rating <= 2200:
		numConfig = NumConfig{NumDigit: 5, Level: DigitDifficultyLevels.EASY}
	case rating <= 2300:
		numConfig = NumConfig{NumDigit: 5, Level: DigitDifficultyLevels.MEDIUM}
	case rating <= 2400:
		numConfig = NumConfig{NumDigit: 5, Level: DigitDifficultyLevels.HARD}
	case rating <= 2500:
		numConfig = NumConfig{NumDigit: 6, Level: DigitDifficultyLevels.EASY}
	case rating <= 2600:
		numConfig = NumConfig{NumDigit: 6, Level: DigitDifficultyLevels.MEDIUM}
	case rating <= 2700:
		numConfig = NumConfig{NumDigit: 6, Level: DigitDifficultyLevels.HARD}
	case rating <= 2800:
		numConfig = NumConfig{NumDigit: 6, Level: DigitDifficultyLevels.EASY}
	case rating <= 2900:
		numConfig = NumConfig{NumDigit: 6, Level: DigitDifficultyLevels.MEDIUM}
	case rating <= 3000:
		numConfig = NumConfig{NumDigit: 7, Level: DigitDifficultyLevels.HARD}
	default:
		numConfig = NumConfig{NumDigit: 5, Level: DigitDifficultyLevels.MEDIUM}
	}

	primeFactors, question := pickRandomNumbers(validPrimeNumbers, numConfig.NumDigit)
	return getPrimeFactorizationQuestion(primeFactors, question, rating, []string{})
}

func pickRandomNumbers(validPrimeNumbers []int, n int) ([]int, int) {
	result := make([]int, n)
	product := 1

	for i := 0; i < n; i++ {
		randomIndex := rand.IntN(len(validPrimeNumbers))
		result[i] = validPrimeNumbers[randomIndex]
		product *= validPrimeNumbers[randomIndex]
	}
	sort.Ints(result)

	return result, product
}
