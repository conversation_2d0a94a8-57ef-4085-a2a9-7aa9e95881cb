package questionsGenerator

import (
	"fmt"
	"math"
	"math/rand/v2"
	"strings"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetHideCellsDistributionAccToUserRating(gameRating int) map[int][]int {
	normalizedRating := math.Max(700, math.Min(3000, float64(gameRating)))

	bracket := int(math.Floor((normalizedRating - 700) / 200))

	distributions := []map[int][]int{
		// 700-899
		{
			5: {2, 2, 3, 4},
			7: {3, 3, 4, 4, 5, 6, 7, 8, 8, 9, 9},
		},
		// 900-1099
		{
			5: {2, 3, 3, 4},
			7: {3, 4, 5, 6, 6, 7, 7, 8, 8, 9, 9},
		},
		// 1100-1299
		{
			5: {2, 3, 4, 4},
			7: {3, 4, 5, 6, 6, 7, 7, 8, 8, 9, 9},
		},
		// 1300-1499
		{
			5: {3, 3, 4, 4},
			7: {4, 4, 5, 6, 6, 7, 7, 8, 8, 9, 9},
		},
		// 1500-1699 (original distribution)
		{
			5: {3, 4, 4, 4},
			7: {4, 5, 6, 6, 7, 7, 8, 8, 9, 9, 9},
		},
		// 1700-1899
		{
			5: {4, 4, 4, 4},
			7: {4, 5, 6, 6, 7, 7, 8, 8, 9, 9, 9},
		},
		// 1900-2099
		{
			5: {4, 4, 4, 4},
			7: {4, 5, 6, 6, 7, 7, 8, 8, 9, 9, 9},
		},
		// 2100-2299
		{
			5: {4, 4, 4},
			7: {4, 5, 6, 6, 7, 7, 8, 8, 9, 9, 9},
		},
		// 2300-2499
		{
			5: {4, 4},
			7: {6, 7, 7, 8, 8, 8, 9, 9, 9},
		},
		// 2500-2699
		{
			5: {4, 4},
			7: {6, 6, 7, 8, 8, 9, 8, 8, 9, 9, 9},
		},
		// 2700-2899
		{
			5: {4, 4},
			7: {6, 6, 7, 8, 8, 9, 8, 8, 9, 9, 9},
		},
		// 2900-3000
		{
			5: {4, 4},
			7: {6, 7, 5, 6, 7, 7, 8, 8, 9, 9, 9, 9},
		},
	}

	if bracket < 0 {
		bracket = 0
	} else if bracket >= len(distributions) {
		bracket = len(distributions) - 1
	}

	return distributions[bracket]
}

func GeneratePuzzlesGameQuestion(gameRating int) []*models.PuzzleGameQuestion {
	puzzles := make([]*models.PuzzleGameQuestion, 0)
	hiddenDistribution := GetHideCellsDistributionAccToUserRating(gameRating)

	for i := range hiddenDistribution[5] {
		grid, answers := GeneratePuzzleWithSize(5, hiddenDistribution[5][i])
		puzzles = append(puzzles, &models.PuzzleGameQuestion{
			Id:          primitive.NewObjectID(),
			Question:    utils.AllocPtr(MinifyPuzzleGrid(grid, answers, 5)),
			Submissions: []*models.PuzzleQuestionSubmission{},
		})
	}

	for i := range hiddenDistribution[7] {
		grid, answers := GeneratePuzzleWithSize(7, hiddenDistribution[7][i])
		puzzles = append(puzzles, &models.PuzzleGameQuestion{
			Id:          primitive.NewObjectID(),
			Question:    utils.AllocPtr(MinifyPuzzleGrid(grid, answers, 7)),
			Submissions: []*models.PuzzleQuestionSubmission{},
		})
	}

	return puzzles
}

func GeneratePuzzleWithSize(gridSize, hideCellsCount int) ([][]models.Cell, []string) {
	puzzle := generatePuzzleAttempt(gridSize, hideCellsCount)

	if puzzle == nil {
		return nil, nil
	}

	var availableAnswers []string
	for i := 0; i < gridSize; i++ {
		for j := 0; j < gridSize; j++ {
			cell := puzzle[i][j]
			if !cell.IsVisible {
				availableAnswers = append(availableAnswers, cell.Value)
			}
		}
	}

	return puzzle, availableAnswers
}

func MinifyPuzzleGrid(grid [][]models.Cell, availableAnswers []string, gridSize int) string {
	var cellStrings []string
	gridCell := gridSize / 2
	totalCells := gridCell * gridCell
	hiddenCells := len(availableAnswers)
	hints := totalCells - hiddenCells

	presetIdentifier := fmt.Sprintf("CROSS-MATH_%d_%d", gridCell, hints)
	cellStrings = append(cellStrings, presetIdentifier)

	for i := 0; i < gridSize; i++ {
		for j := 0; j < gridSize; j++ {
			cell := grid[i][j]

			typeCode := "O"
			if cell.Type == models.CellTypeOperator {
				typeCode = "P"
			} else if cell.Type == models.CellTypeEmptyBlock {
				typeCode = "E"
			}

			visibilityCode := "V"
			if !cell.IsVisible {
				visibilityCode = "H"
			}
			cellString := fmt.Sprintf("%d%d%s%s%s", i, j, typeCode, visibilityCode, cell.Value)
			cellStrings = append(cellStrings, cellString)
		}
	}
	answerString := "ANS:" + strings.Join(availableAnswers, ",")
	cellStrings = append(cellStrings, answerString)

	return strings.Join(cellStrings, "|")
}

func GetExpectedTimeToSolveAPuzzleQuestion(userRating, gridSize, hints int) time.Duration {
	baseDifficulty := map[int]float64{
		2: 20.0,  // Base difficulty for 2x2 grid with no hints
		3: 120.0, // Base difficulty for 3x3 grid with no hints
	}

	// Calculate total cells in the grid
	totalCells := gridSize * gridSize

	// Calculate base time for the grid size
	var baseTime float64
	if val, ok := baseDifficulty[gridSize]; ok {
		baseTime = val
	} else if gridSize > 3 {
		baseTime = baseDifficulty[3] * math.Pow(float64(gridSize)/3.0, 2.5)
	} else {
		baseTime = baseDifficulty[2]
	}

	// Hint adjustment - more sophisticated approach
	var hintFactor float64
	if gridSize == 3 {
		// For 3x3 grid, use a more explicit hint impact mapping
		// These values are calibrated to match your requirements
		hintImpact := map[int]float64{
			0: 1.0,  // 0 hints: 100% of base time (110-120s)
			1: 0.75, // 1 hint:  75% of base time (~90s)
			2: 0.45, // 2 hints: 45% of base time (~54s)
			3: 0.32, // 3 hints: 32% of base time (35-40s)
			4: 0.25, // 4 hints: 25% of base time (27-32s)
			5: 0.20, // 5 hints: 15% of base time (15-20s)
			6: 0.15, // 6 hints: 10% of base time (10-15s)
		}

		if factor, ok := hintImpact[hints]; ok {
			hintFactor = factor
		} else {
			// Use default formula for any value not explicitly defined
			hintPercentage := float64(hints) / float64(totalCells)
			hintFactor = math.Pow(0.4, hintPercentage*float64(gridSize))
		}
	} else {
		// For 2x2, use an adjusted formula
		hintPercentage := float64(hints) / float64(totalCells)
		hintFactor = math.Pow(0.6, hintPercentage*float64(gridSize+1))
	}

	// Rating adjustment factor
	// Reference rating is 1600 (from examples)
	ratingFactor := math.Pow(1600.0/float64(userRating), 0.8)

	// Combine all factors
	expectedTime := baseTime * hintFactor * ratingFactor

	// Round to two decimal places
	return time.Duration(math.Round(expectedTime*1000) * float64(time.Millisecond))
}

func addGaussianNoise(meanTime, minTime, meanNoise, stdDev float64) float64 {
	u1 := rand.Float64()
	u2 := rand.Float64()
	gaussianNoise := meanNoise + stdDev*math.Sqrt(-2*math.Log(u1))*math.Cos(2*math.Pi*u2)

	noisyTime := max(meanTime+gaussianNoise, minTime)

	return noisyTime
}
