package questionsGenerator

import (
	"fmt"
	"math/rand/v2"
	"strconv"

	"matiksOfficial/matiks-server-go/internal/models"
)

var EmptyBlockValue = models.Cell{
	Value:     "",
	Type:      models.CellTypeEmptyBlock,
	IsVisible: true,
}

func gcd(a, b int) int {
	if b == 0 {
		return a
	}
	return gcd(b, a%b)
}

func lcm(a, b int) int {
	return (a * b) / gcd(a, b)
}

func getRandomNumberForPuzzle(min, max int) int {
	return min + rand.IntN(max-min+1)
}

func getRandomOperator() string {
	operators := []string{"+", "+", "-", "×", "÷"}
	return operators[rand.IntN(len(operators))]
}

type PuzzleGrid [][]models.Cell

func evaluateExpression(arr []string) float64 {
	values := make([]float64, 0)
	operators := make([]string, 0)

	for _, v := range arr {
		switch v {
		case "+", "-", "×", "÷":
			operators = append(operators, v)
		default:
			val, _ := strconv.ParseFloat(v, 64)
			values = append(values, val)
		}
	}

	// First handle multiplication and division
	for i := 0; i < len(operators); i++ {
		if operators[i] == "×" || operators[i] == "÷" {
			var result float64
			if operators[i] == "×" {
				result = values[i] * values[i+1]
			} else {
				if values[i+1] == 0 {
					result = 0
				} else {
					result = values[i] / values[i+1]
				}
			}
			values = append(values[:i], append([]float64{result}, values[i+2:]...)...)
			operators = append(operators[:i], operators[i+1:]...)
			i--
		}
	}

	// Then handle addition and subtraction
	result := values[0]
	for i := 0; i < len(operators); i++ {
		if operators[i] == "+" {
			result += values[i+1]
		} else if operators[i] == "-" {
			result -= values[i+1]
		}
	}
	return result
}

func generatePuzzleAttempt(gridSize, hideCellsCount int) PuzzleGrid {
	grid := make(PuzzleGrid, gridSize)
	for i := range grid {
		grid[i] = make([]models.Cell, gridSize)
		for j := range grid[i] {
			grid[i][j] = models.Cell{Value: "_", Type: models.CellTypeOperand, IsVisible: true}
		}
	}

	// Set equals signs and empty blocks
	for row := 0; row < gridSize-1; row += 2 {
		grid[row][gridSize-2] = models.Cell{Value: "=", Type: models.CellTypeOperator, IsVisible: true}
		grid[row+1][gridSize-2] = EmptyBlockValue
		grid[row+1][gridSize-1] = EmptyBlockValue
	}

	for cell := 0; cell < gridSize-1; cell += 2 {
		grid[gridSize-2][cell] = models.Cell{Value: "=", Type: models.CellTypeOperator, IsVisible: true}
		grid[gridSize-2][cell+1] = EmptyBlockValue
		grid[gridSize-1][cell+1] = EmptyBlockValue
	}

	grid[gridSize-1][gridSize-1] = EmptyBlockValue

	// Set empty blocks in alternate positions
	for x := 1; x < gridSize; x += 2 {
		for y := 1; y < gridSize; y += 2 {
			grid[x][y] = EmptyBlockValue
		}
	}

	// Set operators with alternating pattern
	for row := 0; row < gridSize-2; row++ {
		isOddRow := row%2 == 1
		startCol := 0
		if !isOddRow {
			startCol = 1
		}
		for col := startCol; col < gridSize-2; col += 2 {
			grid[row][col] = models.Cell{
				Value:     getRandomOperator(),
				Type:      models.CellTypeOperator,
				IsVisible: true,
			}
		}
	}

	// Remove Alternate Divisions

	// Check and fix consecutive division operators in rows
	for row := 0; row < gridSize-2; row += 2 {
		colIndex := 1
		if colIndex+2 < gridSize-2 {
			if grid[row][colIndex].Value == "÷" &&
				grid[row][colIndex+2].Value == "÷" {
				grid[row][colIndex].Value = "+"
			}
			if grid[row][colIndex].Value == "×" &&
				grid[row+2][colIndex].Value == "×" {
				grid[row+2][colIndex].Value = "-"
			}
		}
	}

	// Check and fix consecutive division operators in columns
	for col := 0; col < gridSize-2; col += 2 {
		rowIndex := 1
		if rowIndex+2 < gridSize-2 {
			if grid[rowIndex][col].Value == "÷" &&
				grid[rowIndex+2][col].Value == "÷" {
				grid[rowIndex][col].Value = "+"
			}
			if grid[rowIndex][col].Value == "×" &&
				grid[rowIndex+2][col].Value == "×" {
				grid[rowIndex+2][col].Value = "-"
			}
		}
	}

	// Fill in numbers
	for row := gridSize - 3; row >= 0; row -= 2 {
		for col := gridSize - 3; col >= 0; col -= 2 {
			value := 0
			if row == gridSize-3 && col == gridSize-3 {
				value = getRandomNumberForPuzzle(5, 10)
			} else if row == gridSize-3 {
				nextVal, _ := strconv.Atoi(grid[row][col+2].Value)
				switch grid[row][col+1].Value {
				case "÷":
					value = getRandomNumberForPuzzle(5, 10) * nextVal
				case "-":
					value = getRandomNumberForPuzzle(nextVal+5, nextVal+50)
				case "×":
					value = getRandomNumberForPuzzle(2, 10)
				default:
					value = getRandomNumberForPuzzle(5, 20)
				}
			} else if col == gridSize-3 {
				nextVal, _ := strconv.Atoi(grid[row+2][col].Value)
				switch grid[row+1][col].Value {
				case "÷":
					value = getRandomNumberForPuzzle(5, 10) * nextVal
				case "-":
					value = getRandomNumberForPuzzle(nextVal+5, nextVal+50)
				case "×":
					value = getRandomNumberForPuzzle(2, 10)
				default:
					value = getRandomNumberForPuzzle(5, 20)
				}
			} else {
				val1, _ := strconv.Atoi(grid[row][col+2].Value)
				val2, _ := strconv.Atoi(grid[row+2][col].Value)
				if grid[row+1][col].Value == "÷" && grid[row][col+1].Value == "÷" {
					lcmVal := lcm(val1, val2)
					value = lcmVal * getRandomNumberForPuzzle(2, 5)
				} else if grid[row+1][col].Value == "÷" {
					value = val2 * getRandomNumberForPuzzle(2, 5)
				} else if grid[row][col+1].Value == "÷" {
					value = val1 * getRandomNumberForPuzzle(2, 5)
				} else if grid[row][col+1].Value == "×" || grid[row+1][col].Value == "×" {
					value = getRandomNumberForPuzzle(2, 5)
				} else {
					value = getRandomNumberForPuzzle(5, 20)
				}
			}
			grid[row][col] = models.Cell{
				Value:     fmt.Sprintf("%d", value),
				Type:      models.CellTypeOperand,
				IsVisible: true,
			}
		}
	}

	// Calculate results for rows
	for row := 0; row < gridSize-2; row += 2 {
		values := make([]string, 0)
		for col := 0; col < gridSize-2; col++ {
			values = append(values, grid[row][col].Value)
		}
		result := evaluateExpression(values)
		grid[row][gridSize-1] = models.Cell{
			Value:     fmt.Sprintf("%.0f", result),
			Type:      models.CellTypeOperand,
			IsVisible: true,
		}
	}

	// Calculate results for columns
	for col := 0; col < gridSize-2; col += 2 {
		values := make([]string, 0)
		for row := 0; row < gridSize-2; row++ {
			values = append(values, grid[row][col].Value)
		}
		result := evaluateExpression(values)
		grid[gridSize-1][col] = models.Cell{
			Value:     fmt.Sprintf("%.0f", result),
			Type:      models.CellTypeOperand,
			IsVisible: true,
		}
	}

	hideGridCells(grid, gridSize, hideCellsCount)

	return grid
}

func hideGridCells(grid PuzzleGrid, actualGridSize, hideCellsCount int) {
	totalHidden := 0
	gridSize := actualGridSize - 1

	for row := 0; row < gridSize && totalHidden < hideCellsCount; row++ {

		var operandCols []int
		for col := 0; col < gridSize; col++ {
			if grid[row][col].Type == models.CellTypeOperand {
				operandCols = append(operandCols, col)
			}
		}

		positions := rand.Perm(len(operandCols))

		if len(operandCols) > 0 {
			grid[row][operandCols[positions[0]]].IsVisible = false
			totalHidden++
		}
	}

	rowIndex := 0
	for totalHidden < hideCellsCount {
		row := rowIndex % gridSize
		var visibleOperandCols []int
		for col := 0; col < gridSize; col++ {
			if grid[row][col].Type == models.CellTypeOperand && grid[row][col].IsVisible {
				visibleOperandCols = append(visibleOperandCols, col)
			}
		}

		positions := rand.Perm(len(visibleOperandCols))

		if len(visibleOperandCols) > 0 {
			grid[row][visibleOperandCols[positions[0]]].IsVisible = false
			totalHidden++
		}

		rowIndex++
		if rowIndex >= gridSize*2 {
			break
		}
	}
}

func getPuzzleHintsCount(difficulty models.Difficulty, gridSize int) int {
	if gridSize == 5 {
		switch difficulty {
		case models.Easy:
			return 2
		case models.Medium:
			return 4
		case models.Hard:
			return 4
		}
	}

	switch difficulty {
	case models.Easy:
		return 3
	case models.Medium:
		return 6
	case models.Hard:
		return 9

	default:
		return 9
	}
}

func GeneratePuzzle(difficulty models.Difficulty, gridSize int) ([][]models.Cell, []string) {
	hideCellsCount := getPuzzleHintsCount(difficulty, gridSize)
	puzzle := generatePuzzleAttempt(gridSize, hideCellsCount)

	var availableAnswers []string
	for i := 0; i < gridSize; i++ {
		for j := 0; j < gridSize; j++ {
			cell := puzzle[i][j]
			if !cell.IsVisible {
				availableAnswers = append(availableAnswers, cell.Value)
			}
		}
	}

	return puzzle, availableAnswers
}
