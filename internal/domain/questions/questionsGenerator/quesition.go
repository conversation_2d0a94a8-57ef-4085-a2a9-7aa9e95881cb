package questionsGenerator

import (
	"math/rand/v2"

	"matiksOfficial/matiks-server-go/internal/models"
)

func GetRandomArithmeticQuestion(rating int, tags []string) *models.Question {
	if tags == nil || len(tags) == 0 {
		num := rand.Float64()

		if num <= 0.4 {
			return createAdditionQuestion(rating)
		} else if num <= 0.7 {
			return createMultiplicationQuestion(rating)
		} else {
			return createDivisionQuestion(rating)
		}
	}

	tagMap := make(map[string]bool)
	for _, tag := range tags {
		tagMap[tag] = true
	}

	type QuestionType struct {
		tag      string
		weight   float64
		createFn func(int) *models.Question
	}

	availableTypes := []QuestionType{}

	if tagMap["ADD"] {
		availableTypes = append(availableTypes, QuestionType{"ADD", 0.4, createAdditionQuestion})
	}
	if tagMap["MULT"] {
		availableTypes = append(availableTypes, QuestionType{"MULT", 0.3, createMultiplicationQuestion})
	}
	if tagMap["DIV"] {
		availableTypes = append(availableTypes, QuestionType{"DIV", 0.3, createDivisionQuestion})
	}

	totalWeight := 0.0
	for _, qt := range availableTypes {
		totalWeight += qt.weight
	}

	normalizedTypes := []QuestionType{}
	for _, qt := range availableTypes {
		qt.weight /= totalWeight
		normalizedTypes = append(normalizedTypes, qt)
	}

	num := rand.Float64()
	cumulativeWeight := 0.0
	for _, qt := range normalizedTypes {
		cumulativeWeight += qt.weight
		if num <= cumulativeWeight {
			return qt.createFn(rating)
		}
	}

	return nil
}

func GenerateUniformRatings(min, max, size int) []int {
	step := float64(max-min) / float64(size-1)
	ratings := make([]int, size)
	for i := 0; i < size; i++ {
		ratings[i] = int(float64(min) + step*float64(i))
	}
	return ratings
}
