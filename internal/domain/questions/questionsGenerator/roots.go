package questionsGenerator

import (
	"fmt"
	"math"
	"math/rand/v2"

	"matiksOfficial/matiks-server-go/internal/domain/questions/presets"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func getPerfectRootQuestion(numbers []int, rating int, tags []string, rootType int) *models.Question {
	expression := make([]string, 0, 2*len(numbers))
	base := numbers[0]
	radicant := numbers[1]
	expression = append(expression, fmt.Sprintf("%d", base))
	expression = append(expression, "✓")
	expression = append(expression, fmt.Sprintf("%d", radicant))

	var answer float64
	var formatAnswer []string
	isPerfectRoot := isPerfectRoot(radicant, rootType)
	answer = math.Pow(float64(radicant), 1.0/float64(rootType))

	if isPerfectRoot {
		formatAnswer = []string{fmt.Sprintf("%.0f", answer)}
	} else {
		formatAnswer = []string{fmt.Sprintf("%.1f", math.Trunc(answer*10)/10)}
	}

	presetsIdentifier := presets.PresetIdentifierGenerator(models.PresetCategoryRoot, numbers)

	return &models.Question{
		Expression:       expression,
		Answers:          formatAnswer,
		QuestionType:     utils.AllocPtr(models.QuestionTypeFillInTheBlanks),
		Rating:           utils.AllocPtr(rating),
		Tags:             append([]string{QUESTION_TAG_ROOT, QUESTION_TAG_ARITHMETIC}, tags...),
		PresetIdentifier: presetsIdentifier,
	}
}

func CreateRootQuestion(rating int) *models.Question {
	var numConfig []NumConfig

	switch {
	case rating <= 600:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
		}
	case rating <= 700:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 800:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
		}
	case rating <= 900:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
			{NumDigit: 2, Level: DigitDifficultyLevels.EASY},
		}
	case rating <= 1000:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1100:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1200:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1300:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1400:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1500:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1600:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1700:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1800:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 1900:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2000:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2100:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2200:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2300:
		numConfig = []NumConfig{
			{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2400:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 5, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2500:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 5, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2600:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 5, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 2700:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 5, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2800:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 6, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 2900:
		numConfig = []NumConfig{
			{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 6, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 3000:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 6, Level: DigitDifficultyLevels.MEDIUM},
		}
	case rating <= 3100:
		numConfig = []NumConfig{
			{NumDigit: 5, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 6, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 3200:
		numConfig = []NumConfig{
			{NumDigit: 5, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 8, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 3300:
		numConfig = []NumConfig{
			{NumDigit: 5, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 9, Level: DigitDifficultyLevels.HARD},
		}
	case rating <= 3400:
		numConfig = []NumConfig{
			{NumDigit: 5, Level: DigitDifficultyLevels.HARD},
			{NumDigit: 9, Level: DigitDifficultyLevels.HARD},
		}
	default:
		numConfig = []NumConfig{
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
			{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM},
		}

	}

	numbers := make([]int, 0, len(numConfig))
	numbers = append(numbers, numConfig[0].NumDigit)

	var perfectNum int
	if numConfig[0].NumDigit == 2 {
		perfectNum = generatePerfectSquare(numConfig[1].NumDigit)
	} else if numConfig[0].NumDigit == 3 {
		perfectNum = generatePerfectCube(numConfig[1].NumDigit)
	} else {
		perfectNum = generatePerfectSquare(numConfig[1].NumDigit)
	}
	numbers = append(numbers, perfectNum)

	return getPerfectRootQuestion(numbers, rating, []string{}, numConfig[0].NumDigit)
}

func generatePerfectSquare(numDigits int) int {
	maxNum := int(math.Pow(10, float64(numDigits))) - 1
	minNum := int(math.Pow(10, float64(numDigits-1)))

	minRoot := int(math.Ceil(math.Sqrt(float64(minNum))))
	maxRoot := int(math.Floor(math.Sqrt(float64(maxNum))))
	root := minRoot + rand.IntN(maxRoot-minRoot+1)
	return root * root
}

func generatePerfectCube(numDigits int) int {
	maxNum := int(math.Pow(10, float64(numDigits))) - 1
	minNum := int(math.Pow(10, float64(numDigits-1)))

	minRoot := int(math.Ceil(math.Pow(float64(minNum), 1.0/3.0)))
	maxRoot := int(math.Floor(math.Pow(float64(maxNum), 1.0/3.0)))

	root := minRoot + rand.IntN(maxRoot-minRoot+1)
	return root * root * root
}

func isPerfectRoot(num, rootType int) bool {
	if num < 0 || rootType < 2 {
		return false
	}
	root := math.Pow(float64(num), 1.0/float64(rootType))

	intRoot := int(root)
	return math.Pow(float64(intRoot), float64(rootType)) == float64(num)
}
