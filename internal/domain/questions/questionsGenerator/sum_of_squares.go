package questionsGenerator

import (
	"fmt"
	"math"
	"math/rand/v2"

	"matiksOfficial/matiks-server-go/internal/domain/questions/presets"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func generateSummands(numDigits int, level DigitDifficultyLevel) []int {
	var summands []int
	maxSummand := int(math.Pow(10, float64(numDigits/2+1))) - 1
	minSummand := 1

	switch level {
	case DigitDifficultyLevels.EASY:
		maxSummand = minSummand + (maxSummand-minSummand)/3
		summands = make([]int, 2)
	case DigitDifficultyLevels.MEDIUM:
		maxSummand = minSummand + (maxSummand-minSummand)/2
		summands = make([]int, 3)
	case DigitDifficultyLevels.HARD:
		summands = make([]int, 4)
	}

	for attempts := 0; attempts < 100; attempts++ {
		for i := range summands {
			summands[i] = minSummand + rand.IntN(maxSummand-minSummand+1)
		}
		sumOfSquares := 0
		for _, s := range summands {
			sumOfSquares += s * s
		}
		if sumOfSquares >= int(math.Pow(10, float64(numDigits-1))) &&
			sumOfSquares < int(math.Pow(10, float64(numDigits))) {
			return summands
		}
	}
	return []int{1, 2, 3}
}

func CreateSumOfSquaresQuestion(rating int) *models.Question {
	var numConfig NumConfig

	switch {
	case rating <= 600:
		numConfig = NumConfig{NumDigit: 2, Level: DigitDifficultyLevels.EASY}
	case rating <= 700:
		numConfig = NumConfig{NumDigit: 2, Level: DigitDifficultyLevels.EASY}
	case rating <= 800:
		numConfig = NumConfig{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM}
	case rating <= 900:
		numConfig = NumConfig{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM}
	case rating <= 1000:
		numConfig = NumConfig{NumDigit: 2, Level: DigitDifficultyLevels.EASY}
	case rating <= 1100:
		numConfig = NumConfig{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM}
	case rating <= 1200:
		numConfig = NumConfig{NumDigit: 2, Level: DigitDifficultyLevels.HARD}
	case rating <= 1300:
		numConfig = NumConfig{NumDigit: 2, Level: DigitDifficultyLevels.EASY}
	case rating <= 1400:
		numConfig = NumConfig{NumDigit: 2, Level: DigitDifficultyLevels.MEDIUM}
	case rating <= 1500:
		numConfig = NumConfig{NumDigit: 2, Level: DigitDifficultyLevels.HARD}
	case rating <= 1600:
		numConfig = NumConfig{NumDigit: 2, Level: DigitDifficultyLevels.EASY}
	case rating <= 1700:
		numConfig = NumConfig{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM}
	case rating <= 1800:
		numConfig = NumConfig{NumDigit: 3, Level: DigitDifficultyLevels.HARD}
	case rating <= 1900:
		numConfig = NumConfig{NumDigit: 3, Level: DigitDifficultyLevels.EASY}
	case rating <= 2000:
		numConfig = NumConfig{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM}
	case rating <= 2100:
		numConfig = NumConfig{NumDigit: 3, Level: DigitDifficultyLevels.HARD}
	case rating <= 2200:
		numConfig = NumConfig{NumDigit: 3, Level: DigitDifficultyLevels.EASY}
	case rating <= 2300:
		numConfig = NumConfig{NumDigit: 3, Level: DigitDifficultyLevels.MEDIUM}
	case rating <= 2400:
		numConfig = NumConfig{NumDigit: 3, Level: DigitDifficultyLevels.HARD}
	case rating <= 2500:
		numConfig = NumConfig{NumDigit: 3, Level: DigitDifficultyLevels.EASY}
	case rating <= 2600:
		numConfig = NumConfig{NumDigit: 4, Level: DigitDifficultyLevels.MEDIUM}
	case rating <= 2700:
		numConfig = NumConfig{NumDigit: 4, Level: DigitDifficultyLevels.HARD}
	case rating <= 2800:
		numConfig = NumConfig{NumDigit: 4, Level: DigitDifficultyLevels.EASY}
	case rating <= 2900:
		numConfig = NumConfig{NumDigit: 5, Level: DigitDifficultyLevels.MEDIUM}
	case rating <= 3000:
		numConfig = NumConfig{NumDigit: 5, Level: DigitDifficultyLevels.HARD}
	default:
		numConfig = NumConfig{NumDigit: 4, Level: DigitDifficultyLevels.HARD}
	}

	summands := generateSummands(numConfig.NumDigit, numConfig.Level)
	sumOfSquares := 0
	for _, s := range summands {
		sumOfSquares += s * s
	}
	expression := make([]string, 0)
	squaredTerms := make([]string, len(summands))
	for i, s := range summands {
		squaredTerms[i] = fmt.Sprintf("%d²", s)
	}

	expression = append(expression, fmt.Sprintf("%d", sumOfSquares))
	expression = append(expression, "=")

	answer := ""
	for i, s := range squaredTerms {
		if i > 0 {
			answer += " + "
		}
		answer += s
	}

	presetsIdentifier := presets.PresetIdentifierGenerator(models.PresetCategorySOS, summands)

	return &models.Question{
		Expression:       expression,
		Answers:          []string{answer},
		QuestionType:     utils.AllocPtr(models.QuestionTypeFillInTheBlanks),
		Rating:           utils.AllocPtr(rating),
		Tags:             []string{QUESTION_TAG_SUM_OF_SQUARES, QUESTION_TAG_ARITHMETIC},
		PresetIdentifier: presetsIdentifier,
	}
}
