package questions

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"matiksOfficial/matiks-server-go/internal/models"
)

func TestParseIdentifier(t *testing.T) {
	tests := []struct {
		name       string
		identifier string
		want       QuestionInfo
		wantErr    bool
	}{
		{
			name:       "valid addition identifier",
			identifier: "ADD_2,2",
			want: QuestionInfo{
				Type:      models.PresetCategoryAdd,
				DigitList: []int{2, 2},
			},
			wantErr: false,
		},
		{
			name:       "valid multiplication identifier",
			identifier: "MULT_3,2,2",
			want: QuestionInfo{
				Type:      models.PresetCategoryMult,
				DigitList: []int{3, 2, 2},
			},
			wantErr: false,
		},
		{
			name:       "invalid format",
			identifier: "ADD-2-2",
			wantErr:    true,
		},
		{
			name:       "invalid question type",
			identifier: "INVALID_2,2",
			wantErr:    true,
		},
		{
			name:       "invalid digit list",
			identifier: "ADD_a,b",
			wantErr:    true,
		},
		{
			name:       "empty identifier",
			identifier: "",
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseIdentifier(tt.identifier)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}

func TestCalculateDifficulty(t *testing.T) {
	tests := []struct {
		name     string
		question QuestionInfo
		want     float64
	}{
		{
			name: "simple addition",
			question: QuestionInfo{
				Type:      models.PresetCategoryAdd,
				DigitList: []int{2, 2},
			},
			want: 5, // 1.0 * (2^1.5 + 2^1.5) * 1.0
		},
		{
			name: "normal addition",
			question: QuestionInfo{
				Type:      models.PresetCategoryAdd,
				DigitList: []int{3, 3},
			},
			want: 7, // 1.0 * (2^1.5 + 2^1.5) * 1.0
		},
		{
			name: "difficult addition",
			question: QuestionInfo{
				Type:      models.PresetCategoryAdd,
				DigitList: []int{3, 3, 3, 3, 3},
			},
			want: 20, // 1.0 * (2^1.5 + 2^1.5) * 1.0
		},
		{
			name: "difficult addition",
			question: QuestionInfo{
				Type:      models.PresetCategoryAddsub,
				DigitList: []int{3, 3, 3, 3, 3},
			},
			want: 30, // 1.0 * (2^1.5 + 2^1.5) * 1.0
		},
		{
			name: "complex multiplication",
			question: QuestionInfo{
				Type:      models.PresetCategoryMult,
				DigitList: []int{4, 2},
			},
			want: 32.5, // 2.5 * (3^1.5 + 2^1.5 + 2^1.5) * 1.2
		},
		{
			name: "difficult multiplication",
			question: QuestionInfo{
				Type:      models.PresetCategoryMult,
				DigitList: []int{5, 5},
			},
			want: 90, // 2.5 * (3^1.5 + 2^1.5 + 2^1.5) * 1.2
		},
		{
			name: "prime factorization",
			question: QuestionInfo{
				Type:      models.PresetCategoryPf,
				DigitList: []int{4},
			},
			want: 40.0, // 5.0 * (4^1.5) * 1.0
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := CalculateDifficulty(tt.question)
			assert.InDelta(t, tt.want, got, 0.1)
		})
	}
}

func TestEstimateTimeToSolve(t *testing.T) {
	tests := []struct {
		name       string
		rating     int
		identifier string
		want       float64
		wantErr    bool
	}{
		{
			name:       "valid estimation",
			rating:     1000,
			identifier: "ADD_2,2",
			want:       5656.85,
			wantErr:    false,
		},
		{
			name:       "rating too low",
			rating:     50,
			identifier: "ADD_2,2",
			wantErr:    true,
		},
		{
			name:       "rating too high",
			rating:     6000,
			identifier: "ADD_2,2",
			wantErr:    true,
		},
		{
			name:       "invalid identifier",
			rating:     1000,
			identifier: "INVALID_2,2",
			wantErr:    true,
		},
		{
			name:       "minimum time threshold",
			rating:     5000,
			identifier: "ADD_1,1",
			want:       550.0,
			wantErr:    false,
		},
		{
			name:       "ADD_1,1 -- rating: 600",
			rating:     600,
			identifier: "ADD_1,1",
			want:       3000.0,
			wantErr:    false,
		},
		{
			name:       "ADD_1,1 -- rating: 1000",
			rating:     1000,
			identifier: "ADD_1,1",
			want:       2000.0,
			wantErr:    false,
		},
		{
			name:       "ADD_1,1 -- rating: 1400",
			rating:     1400,
			identifier: "ADD_1,1",
			want:       1530.0,
			wantErr:    false,
		},
		{
			name:       "ADD_1,1 -- rating: 1700",
			rating:     1700,
			identifier: "ADD_1,1",
			want:       1310.0,
			wantErr:    false,
		},
		{
			name:       "ADD_1,1 -- rating: 2200",
			rating:     2200,
			identifier: "ADD_1,1",
			want:       1060.0,
			wantErr:    false,
		},
		{
			name:       "ADD_1,1 -- rating: 2800",
			rating:     2800,
			identifier: "ADD_1,1",
			want:       870.0,
			wantErr:    false,
		},
		{
			name:       "ADD_1,1 -- rating: 3500",
			rating:     3500,
			identifier: "ADD_1,1",
			want:       740.0,
			wantErr:    false,
		},
		{
			name:       "ADD_2,2 -- rating: 600",
			rating:     600,
			identifier: "ADD_2,2",
			want:       8510.0,
			wantErr:    false,
		},
		{
			name:       "ADD_2,2 -- rating: 1000",
			rating:     1000,
			identifier: "ADD_2,2",
			want:       5000.0,
			wantErr:    false,
		},
		{
			name:       "ADD_2,2 -- rating: 1400",
			rating:     1400,
			identifier: "ADD_2,2",
			want:       3500.0,
			wantErr:    false,
		},
		{
			name:       "ADD_2,2 -- rating: 1800",
			rating:     1800,
			identifier: "ADD_2,2",
			want:       3000.0,
			wantErr:    false,
		},
		{
			name:       "ADD_2,2 -- rating: 2200",
			rating:     2200,
			identifier: "ADD_2,2",
			want:       2500.0,
			wantErr:    false,
		},
		{
			name:       "ADD_2,2 -- rating: 2600",
			rating:     2600,
			identifier: "ADD_2,2",
			want:       2000.0,
			wantErr:    false,
		},
		{
			name:       "ADD_2,2 -- rating: 3200",
			rating:     3200,
			identifier: "ADD_2,2",
			want:       1500.0,
			wantErr:    false,
		},
		{
			name:       "ADD_2,2 -- rating: 3800",
			rating:     3800,
			identifier: "ADD_2,2",
			want:       1000.0,
			wantErr:    false,
		},
		{
			name:       "ADD_3,3,3,3,3,3 -- rating: 600",
			rating:     600,
			identifier: "ADD_3,3,3,3,3,3",
			want:       30000.0,
			wantErr:    false,
		},
		{
			name:       "ADD_3,3,3,3,3,3 -- rating: 1000",
			rating:     1000,
			identifier: "ADD_3,3,3,3,3,3",
			want:       20000.0,
			wantErr:    false,
		},
		{
			name:       "ADD_3,3,3,3,3,3 -- rating: 1400",
			rating:     1400,
			identifier: "ADD_3,3,3,3,3,3",
			want:       15000.0,
			wantErr:    false,
		},
		{
			name:       "ADD_3,3,3,3,3,3 -- rating: 1700",
			rating:     1700,
			identifier: "ADD_3,3,3,3,3,3",
			want:       13000.0,
			wantErr:    false,
		},
		{
			name:       "ADD_3,3,3,3,3,3 -- rating: 2200",
			rating:     2200,
			identifier: "ADD_3,3,3,3,3,3",
			want:       9500.0,
			wantErr:    false,
		},
		{
			name:       "ADD_3,3,3,3,3,3 -- rating: 2800",
			rating:     2800,
			identifier: "ADD_3,3,3,3,3,3",
			want:       6000.0,
			wantErr:    false,
		},
		{
			name:       "ADD_3,3,3,3,3,3 -- rating: 3500",
			rating:     3500,
			identifier: "ADD_3,3,3,3,3,3",
			want:       5000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_6,6 -- rating: 600",
			rating:     600,
			identifier: "MULT_6,6",
			want:       300000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_6,6 -- rating: 1000",
			rating:     1000,
			identifier: "MULT_6,6",
			want:       200000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_6,6 -- rating: 1400",
			rating:     1400,
			identifier: "MULT_6,6",
			want:       120000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_6,6 -- rating: 1800",
			rating:     1800,
			identifier: "MULT_6,6",
			want:       75000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_6,6 -- rating: 2400",
			rating:     2400,
			identifier: "MULT_6,6",
			want:       55000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_6,6 -- rating: 2800",
			rating:     2800,
			identifier: "MULT_6,6",
			want:       45000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_6,6 -- rating: 3300",
			rating:     3300,
			identifier: "MULT_6,6",
			want:       35000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_6,6 -- rating: 3800",
			rating:     3800,
			identifier: "MULT_6,6",
			want:       20000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_1,1 -- rating: 600",
			rating:     600,
			identifier: "MULT_1,1",
			want:       5000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_1,1 -- rating: 1000",
			rating:     1000,
			identifier: "MULT_1,1",
			want:       4000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_1,1 -- rating: 1400",
			rating:     1400,
			identifier: "MULT_1,1",
			want:       3000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_1,1 -- rating: 1800",
			rating:     1800,
			identifier: "MULT_1,1",
			want:       2000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_1,1 -- rating: 2400",
			rating:     2400,
			identifier: "MULT_1,1",
			want:       1500.0,
			wantErr:    false,
		},
		{
			name:       "MULT_1,1 -- rating: 2800",
			rating:     2800,
			identifier: "MULT_1,1",
			want:       1200.0,
			wantErr:    false,
		},
		{
			name:       "MULT_1,1 -- rating: 3300",
			rating:     3300,
			identifier: "MULT_1,1",
			want:       1000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_1,1 -- rating: 3800",
			rating:     3800,
			identifier: "MULT_1,1",
			want:       900.0,
			wantErr:    false,
		},
		{
			name:       "MULT_2,2 -- rating: 600",
			rating:     600,
			identifier: "MULT_2,2",
			want:       25000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_2,2 -- rating: 1000",
			rating:     1000,
			identifier: "MULT_2,2",
			want:       15000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_2,2 -- rating: 1400",
			rating:     1400,
			identifier: "MULT_2,2",
			want:       11000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_2,2 -- rating: 1800",
			rating:     1800,
			identifier: "MULT_2,2",
			want:       8500.0,
			wantErr:    false,
		},
		{
			name:       "MULT_2,2 -- rating: 2200",
			rating:     2200,
			identifier: "MULT_2,2",
			want:       7000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_2,2 -- rating: 2600",
			rating:     2600,
			identifier: "MULT_2,2",
			want:       5500.0,
			wantErr:    false,
		},
		{
			name:       "MULT_2,2 -- rating: 3200",
			rating:     3200,
			identifier: "MULT_2,2",
			want:       4000.0,
			wantErr:    false,
		},
		{
			name:       "MULT_2,2 -- rating: 3800",
			rating:     3800,
			identifier: "MULT_2,2",
			want:       3000.0,
			wantErr:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := EstimateTimeToSolve(tt.rating, tt.identifier)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.InDelta(t, tt.want, got, 100)
			}
		})
	}
}

func TestGetFallbackMeanTime(t *testing.T) {
	tests := []struct {
		name           string
		userRating     int
		questionRating int
		want           float64
	}{
		{
			name:           "very low rated user easy question",
			userRating:     500,
			questionRating: 500,
			want:           6770,
		},
		{
			name:           "baseline user easy question",
			userRating:     1000,
			questionRating: 500,
			want:           2950,
		},
		{
			name:           "medium rated user easy question",
			userRating:     2000,
			questionRating: 500,
			want:           1280,
		},
		{
			name:           "high rated user easy question",
			userRating:     4000,
			questionRating: 500,
			want:           560,
		},
		{
			name:           "very low rated user",
			userRating:     500,
			questionRating: 1000,
			want:           12640,
		},
		{
			name:           "baseline user",
			userRating:     1000,
			questionRating: 1000,
			want:           5500,
		},
		{
			name:           "medium rated user",
			userRating:     2000,
			questionRating: 1000,
			want:           2400,
		},
		{
			name:           "high rated user",
			userRating:     4000,
			questionRating: 1000,
			want:           1040,
		},
		{
			name:           "very low rated user difficult question",
			userRating:     500,
			questionRating: 4000,
			want:           44000,
		},
		{
			name:           "baseline user difficult question",
			userRating:     1000,
			questionRating: 4000,
			want:           19150,
		},
		{
			name:           "medium rated user difficult question",
			userRating:     2000,
			questionRating: 4000,
			want:           8330,
		},
		{
			name:           "high rated user difficult question",
			userRating:     4000,
			questionRating: 4000,
			want:           3630,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getFallbackMeanTime(tt.userRating, tt.questionRating)
			assert.InDelta(t, tt.want, got, 10)
		})
	}
}

func TestGetPresetMeanTime(t *testing.T) {
	tests := []struct {
		name             string
		presetIdentifier string
		questionRating   int
		userRating       int
		want             float64
	}{
		{
			name:             "valid preset",
			presetIdentifier: "ADD_2,2",
			questionRating:   1000,
			userRating:       1000,
			want:             5656.85,
		},
		{
			name:             "invalid preset falls back",
			presetIdentifier: "INVALID_2,2",
			questionRating:   1000,
			userRating:       1000,
			want:             5500.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getPresetMeanTime(tt.presetIdentifier, tt.questionRating, tt.userRating)
			assert.InDelta(t, tt.want, got, 10)
		})
	}
}

func TestAddGaussianNoise(t *testing.T) {
	tests := []struct {
		name      string
		meanTime  float64
		minTime   float64
		meanNoise float64
		stdDev    float64
		samples   int
	}{
		{
			name:      "normal distribution",
			meanTime:  1000.0,
			minTime:   500.0,
			meanNoise: 0.0,
			stdDev:    200.0,
			samples:   1000,
		},
		{
			name:      "minimum time threshold",
			meanTime:  600.0,
			minTime:   500.0,
			meanNoise: 0.0,
			stdDev:    100.0,
			samples:   1000,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sum := 0.0
			for i := 0; i < tt.samples; i++ {
				got := addGaussianNoise(tt.meanTime, tt.minTime, tt.meanNoise, tt.stdDev)
				assert.GreaterOrEqual(t, got, tt.minTime)
				sum += got
			}

			// Check that mean is within 5% of expected
			mean := sum / float64(tt.samples)
			expectedMean := tt.meanTime + tt.meanNoise
			if expectedMean < tt.minTime {
				expectedMean = tt.minTime
			}
			assert.InDelta(t, expectedMean, mean, expectedMean*0.05)
		})
	}
}

func TestGetExpectedTimeToSolveQuestion(t *testing.T) {
	tests := []struct {
		name             string
		userRating       int
		questionRating   int
		presetIdentifier string
		minExpected      time.Duration
		maxExpected      time.Duration
	}{
		{
			name:             "normal calculation",
			userRating:       1000,
			questionRating:   1000,
			presetIdentifier: "ADD_2,2",
			minExpected:      4000 * time.Millisecond,
			maxExpected:      6500 * time.Millisecond,
		},
		{
			name:             "high rated user",
			userRating:       4000,
			questionRating:   1000,
			presetIdentifier: "ADD_2,2",
			minExpected:      500 * time.Millisecond,
			maxExpected:      2500 * time.Millisecond,
		},
		{
			name:             "high rated user mult",
			userRating:       3000,
			questionRating:   1000,
			presetIdentifier: "MULT_2,2",
			minExpected:      5000 * time.Millisecond,
			maxExpected:      7000 * time.Millisecond,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetExpectedTimeToSolveQuestion(tt.userRating, tt.questionRating, tt.presetIdentifier)
			assert.GreaterOrEqual(t, got, tt.minExpected)
			assert.LessOrEqual(t, got, tt.maxExpected)
		})
	}
}
