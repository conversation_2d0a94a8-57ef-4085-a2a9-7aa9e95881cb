package scheduler

import (
	"context"
	"strconv"
	"strings"
	"time"

	"github.com/robfig/cron/v3"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func (s *service) scheduleWeeklyContest() {
	c := cron.New()
	_, err := c.AddFunc("0 0 * * FRI", func() {
		latestWeeklyContest, err := s.contestRepo.GetLatestWeeklyContest(context.Background())
		if err != nil {
			zlog.Error(context.Background(), "Error fetching latest weekly contest: ", err)
			return
		}
		if time.Now().Before(latestWeeklyContest.StartTime) {
			zlog.Warn(context.Background(), "Contest already scheduled")
			return
		}

		contestNumber, err := strconv.Atoi(strings.Split(latestWeeklyContest.Name, "#")[1])
		if err != nil {
			zlog.Error(context.Background(), "Error parsing contest number: ", err)
			return
		}
		contestNumber += 1
		contestName := "Matiks Weekly Contest #" + strconv.Itoa(contestNumber)

		registrationFormInput := models.RegistrationFormInput{}
		if latestWeeklyContest.RegistrationForm != nil {
			if latestWeeklyContest.RegistrationForm.Fields != nil {
				registrationFormInput.Fields = make([]models.FormFieldInput, len(latestWeeklyContest.RegistrationForm.Fields))
				for i, field := range latestWeeklyContest.RegistrationForm.Fields {
					registrationFormInput.Fields[i] = models.FormFieldInput{
						Name:       field.Name,
						Type:       field.Type,
						Label:      field.Label,
						Required:   field.Required,
						Options:    field.Options,
						Validation: models.FieldValidationInput{},
					}
				}
			}
		}
		_, err = s.contestService.CreateContest(context.Background(), models.CreateContestInput{
			Name:        contestName,
			Description: *latestWeeklyContest.Description,
			Details: models.ContestDetailsInput{
				About:        *latestWeeklyContest.Details.About,
				Requirements: *latestWeeklyContest.Details.Requirements,
				Instructions: *latestWeeklyContest.Details.Instructions,
			},
			HostName:              *latestWeeklyContest.HostedByV2.Name,
			HostLogo:              *latestWeeklyContest.HostedByV2.Logo,
			StartTime:             latestWeeklyContest.StartTime.Add(time.Hour * 24 * 7),
			EndTime:               latestWeeklyContest.EndTime.Add(time.Hour * 24 * 7),
			ContestDuration:       latestWeeklyContest.ContestDuration,
			RegistrationStartTime: latestWeeklyContest.RegistrationStartTime.Add(time.Hour * 24 * 7),
			RegistrationEndTime:   latestWeeklyContest.RegistrationEndTime.Add(time.Hour * 24 * 7),
			RegistrationForm:      registrationFormInput,
		})
		if err != nil {
			zlog.Error(context.Background(), "Error creating new weekly contest: ", err)
			return
		}
	})
	if err != nil {
		zlog.Error(context.Background(), "Error scheduling cron job: ", err)
		return
	}

	c.Start()
}

func (s *service) scheduleDailyChallenges() {
	c := cron.New(cron.WithSeconds())

	_, err := c.AddFunc("0 0 0 * * *", func() {
		_, err := s.dailyChallengeService.GenerateNewChallenge(context.Background(), models.ChallengeDivisionOpen)
		if err != nil {
			zlog.Error(context.Background(), "Error generating new OPEN challenge: ", err)
		}
		_, err = s.dailyChallengeService.GenerateNewChallenge(context.Background(), models.ChallengeDivisionDiv1)
		if err != nil {
			zlog.Error(context.Background(), "Error generating new Div 1 challenge: ", err)
		}
		_, err = s.dailyChallengeService.GenerateNewChallenge(context.Background(), models.ChallengeDivisionDiv2)
		if err != nil {
			zlog.Error(context.Background(), "Error generating new Div 2 challenge: ", err)
		}
		_, err = s.dailyChallengeService.GenerateNewChallenge(context.Background(), models.ChallengeDivisionDiv3)
		if err != nil {
			zlog.Error(context.Background(), "Error generating new Div 3 challenge: ", err)
		}
	})
	if err != nil {
		zlog.Error(context.Background(), "Error scheduling cron job: ", err)
	}

	c.Start()
}

func (s *service) scheduleDailyPuzzle() {
	c := cron.New(cron.WithSeconds())

	_, err := c.AddFunc("0 0 0 * * *", func() {
		_, err := s.puzzleService.GenerateDailyPuzzle(context.Background(), models.PuzzleTypeCrossMath)
		if err != nil {
			zlog.Error(context.Background(), "Error generating new easy puzzle: ", err)
		}
	})
	if err != nil {
		zlog.Error(context.Background(), "Error scheduling cron job: ", err)
	}

	_, err = c.AddFunc("0 0 0 * * *", func() {
		_, err := s.puzzleService.GenerateDailyPuzzle(context.Background(), models.PuzzleTypeKenKen)
		if err != nil {
			zlog.Error(context.Background(), "Error generating new easy puzzle: ", err)
		}
	})
	if err != nil {
		zlog.Error(context.Background(), "Error scheduling cron job: ", err)
	}

	c.Start()
}

func (s *service) scheduleWeeklyLeagueProcess() {
	c := cron.New(cron.WithLocation(time.UTC))
	_, err := c.AddFunc("55 23 * * SUN", func() {
		err := s.weeklyLeagueService.RunWeeklyLeagueProcess(context.Background())
		if err != nil {
			zlog.Error(context.Background(), "Error running weekly league process: ", err)
		}
	})
	if err != nil {
		zlog.Error(context.Background(), "Error scheduling cron job: ", err)
	}

	c.Start()
}
