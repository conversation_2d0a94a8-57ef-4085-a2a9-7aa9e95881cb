package scheduler

import (
	"context"
	"encoding/json"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func (s *service) onEndGame(ctx context.Context, task models.SchedulerIntermediatePayload) error {
	var payload models.EndGameActionPayload
	if err := json.Unmarshal(task.Action, &payload); err != nil {
		zlog.Error(ctx, "Failed to unmarshal task: ", err)
		return err
	}
	_, err := s.gameService.EndGame(ctx, &payload.GameID)
	return err
}
