package scheduler

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func (s *service) handleTask(ctx context.Context, task *models.SchedulerIntermediatePayload) error {
	if task == nil {
		zlog.Error(ctx, "Task is nil", fmt.Erro<PERSON>("task is nil"))
		return fmt.Errorf("task is nil")
	}
	taskType := task.Type
	ctx = utils.InheritContextFromMap(ctx, task.ContextMap)

	switch taskType {
	case models.FixtureCreation:
		return s.onFixtureCreation(ctx, *task)

	case models.NotifyFixtureCreation:
		return s.onNotifyFixtureCreation(ctx, *task)

	case models.UpdateShowdownStatus:
		return s.onUpdateShowdownStatus(ctx, *task)

	case models.NotifyAsShowdownStarts:
		return s.onNotifyAsShowdownStarts(ctx, *task)

	case models.ScheduleShowdownRoundUpdate:
		return s.onScheduleShowdownRoundUpdate(ctx, *task)

	case models.JoinGameShowdownCheck:
		return s.onJoinGameShowdownCheck(ctx, *task)

	case models.ScheduleShowdownEnd:
		return s.onNotifyShowdownEnd(ctx, *task)

	case models.EndGameForShowdown:
		return s.onEndGameForShowdown(ctx, *task)

	case models.EndGame:
		return s.onEndGame(ctx, *task)

	case models.EndPuzzleGame:
		return s.onEndPuzzleGame(ctx, *task)

	case models.CheckShowdownRoundPlayerStatus:
		return s.onCheckShowdownRoundPlayerStatus(ctx, *task)

	// case models.SingleNotificationHandler:
	// 	return s.handleNotificationTask(ctx, task)

	default:
		return fmt.Errorf("unknown task type: %d", task.Type)
	}
}
