package scheduler

import (
	"context"
	"encoding/json"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func (s *service) onEndPuzzleGame(ctx context.Context, task models.SchedulerIntermediatePayload) error {
	var payload models.EndPuzzleGameActionPayload
	if err := json.Unmarshal(task.Action, &payload); err != nil {
		zlog.Error(ctx, "Failed to unmarshal task: ", err)
		return err
	}
	_, err := s.puzzleGameService.EndPuzzleGame(ctx, payload.GameID)
	return err
}
