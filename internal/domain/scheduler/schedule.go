package scheduler

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/infrastructure/sortedset"
	"matiksOfficial/matiks-server-go/internal/models"

	"github.com/redis/go-redis/v9"
)

func Schedule(ctx context.Context, sortedSet sortedset.SortedSet, payload *models.SchedulerPayload, time time.Time) error {
	actionData, err := json.Marshal(payload.Action)
	if err != nil {
		return fmt.Errorf("failed to marshal task: %w", err)
	}
	memberPayload := models.SchedulerIntermediatePayload{
		Type:       payload.Type,
		Action:     actionData,
		ContextMap: payload.ContextMap,
	}
	taskBytes, err := json.Marshal(memberPayload)
	if err != nil {
		return fmt.Errorf("failed to marshal task: %w", err)
	}
	err = sortedSet.ZAdd(ctx, scheduler_key, redis.Z{
		Score:  float64(time.UnixMilli()),
		Member: taskBytes,
	})
	if err != nil {
		return fmt.E<PERSON>rf("failed to add task to queue: %w", err)
	}

	return nil
}
