package scheduler

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) onFixtureCreation(ctx context.Context, task models.SchedulerIntermediatePayload) error {
	var payload models.ShowdownRoundActionPayload
	if err := json.Unmarshal(task.Action, &payload); err != nil {
		zlog.Error(ctx, "Failed to unmarshal task: ", err)
		return err
	}
	return s.showdownService.CreateFixtures(ctx, payload.ShowdownId, payload.Round)
}

func (s *service) onNotifyFixtureCreation(ctx context.Context, task models.SchedulerIntermediatePayload) error {
	var payload models.NotifyUsersForFixtureCreationActionPayload
	if err := json.Unmarshal(task.Action, &payload); err != nil {
		zlog.Error(ctx, "Failed to unmarshal task: ", err)
		return err
	}
	return s.showdownService.NotifyUsersAsFixturesCreated(ctx, payload.ShowdownId, payload.CurrentRound, &payload.ByeParticipantId)
}

func (s *service) onUpdateShowdownStatus(ctx context.Context, task models.SchedulerIntermediatePayload) error {
	var payload models.UpdateShowdownStatusActionPayload
	if err := json.Unmarshal(task.Action, &payload); err != nil {
		zlog.Error(ctx, "Failed to unmarshal task: ", err)
		return err
	}
	return s.showdownService.UpdateShowdownStatus(ctx, payload.ShowdownId, payload.Status)
}

func (s *service) onNotifyAsShowdownStarts(ctx context.Context, task models.SchedulerIntermediatePayload) error {
	var payload models.ShowdownRoundActionPayload
	if err := json.Unmarshal(task.Action, &payload); err != nil {
		zlog.Error(ctx, "Failed to unmarshal task: ", err)
		return err
	}
	return s.showdownService.NotifyUsersAsShowdownStarts(ctx, payload.ShowdownId, payload.Round)
}

func (s *service) onScheduleShowdownRoundUpdate(ctx context.Context, task models.SchedulerIntermediatePayload) error {
	var payload models.ShowdownRoundActionPayload
	if err := json.Unmarshal(task.Action, &payload); err != nil {
		zlog.Error(ctx, "Failed to unmarshal task: ", err)
		return err
	}
	return s.showdownService.UpdateShowdownCurrentRound(ctx, payload.ShowdownId, payload.Round)
}

func (s *service) onJoinGameShowdownCheck(ctx context.Context, task models.SchedulerIntermediatePayload) error {
	payload := string(task.Action)
	res := strings.Split(strings.Trim(payload, `"`), ":")
	if len(res) != 3 {
		return fmt.Errorf("payload of invalid format")
	}
	showdownIdStr := res[1]
	gameIdStr := res[2]
	showdownId, err := primitive.ObjectIDFromHex(showdownIdStr)
	if err != nil {
		return err
	}
	gameId, err := primitive.ObjectIDFromHex(gameIdStr)
	if err != nil {
		return err
	}
	return s.gameService.CheckIfOpponentNotJoinedForShowdown(ctx, showdownId, gameId)
}

func (s *service) onNotifyShowdownEnd(ctx context.Context, task models.SchedulerIntermediatePayload) error {
	var payload models.ShowdownRoundActionPayload
	if err := json.Unmarshal(task.Action, &payload); err != nil {
		zlog.Error(ctx, "Failed to unmarshal task: ", err)
		return err
	}
	return s.showdownService.OnShowdownEnd(ctx, payload.ShowdownId)
}

func (s *service) onEndGameForShowdown(ctx context.Context, task models.SchedulerIntermediatePayload) error {
	var payload models.EndGameActionPayload
	if err := json.Unmarshal(task.Action, &payload); err != nil {
		zlog.Error(ctx, "Failed to unmarshal task: ", err)
		return err
	}
	_, err := s.gameService.EndGameForShowdown(ctx, &payload.GameID)
	return err
}

func (s *service) onCheckShowdownRoundPlayerStatus(ctx context.Context, task models.SchedulerIntermediatePayload) error {
	var payload models.ShowdownRoundActionPayload
	if err := json.Unmarshal(task.Action, &payload); err != nil {
		zlog.Error(ctx, "Failed to unmarshal task: ", err)
		return err
	}
	return s.showdownService.CheckShowdownPlayersStatus(ctx, payload.ShowdownId, payload.Round)
}
