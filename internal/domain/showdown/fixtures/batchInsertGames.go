package showdownFixtures

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *ShowdownFixtureService) batchInsertGames(ctx context.Context, games []*models.Game) error {
	gamesAndPlayers := map[primitive.ObjectID]*models.ShowdownRoundDto{}

	err := s.gameRepo.CreateManyGames(ctx, games)
	if err != nil {
		zlog.Error(ctx, "Failed to get insert batch of users for create game", err)
		return err
	}
	for _, game := range games {
		if game == nil || game.ID == nil || len(game.Players) != 2 {
			return fmt.Errorf("game is invalid: %v", game)
		}
		if game.Players[0] == nil || game.Players[1] == nil {
			return fmt.Errorf("game is invalid: %v", game)
		}
		user1 := game.Players[0]
		user2 := game.Players[1]
		user1RoundData, ok := gamesAndPlayers[user1.UserID]
		if ok && user1RoundData != nil {
			user1RoundData.Round.Games = append(user1RoundData.Round.Games, *game.ID)
		} else {
			gamesAndPlayers[user1.UserID] = &models.ShowdownRoundDto{
				UserId: user1.UserID,
				Round: models.ShowdownRound{
					Opponent:     user2.UserID,
					PlayerStatus: models.RoundPlayerStatusPendingJoin,
					Round:        s.currentRound,
					Score:        0,
					Games:        []models.ObjectID{*game.ID},
				},
			}
		}
		user2RoundData, ok := gamesAndPlayers[user1.UserID]
		if ok && user1RoundData != nil {
			user2RoundData.Round.Games = append(user2RoundData.Round.Games, *game.ID)
		} else {
			gamesAndPlayers[user2.UserID] = &models.ShowdownRoundDto{
				UserId: user2.UserID,
				Round: models.ShowdownRound{
					Opponent:     user1.UserID,
					PlayerStatus: models.RoundPlayerStatusPendingJoin,
					Round:        s.currentRound,
					Score:        0,
					Games:        []models.ObjectID{*game.ID},
				},
			}
		}
	}
	err = s.updateShowdownRoundBatch(ctx, gamesAndPlayers, s.showdownID)
	if err != nil {
		return err
	}
	return nil
}
