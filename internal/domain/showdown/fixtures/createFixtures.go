package showdownFixtures

import (
	"context"
	"errors"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	ErrShowdownNotFound    = errors.New("showdown not found")
	ErrParticipantNotFound = errors.New("participant not found")
	ErrShowdownCannotBeNil = errors.New("showdown cannot be nil")
)

func (s *ShowdownFixtureService) CreateFixtures(
	ctx context.Context,
) (
	fixtures []*models.Fictures,
	byeParticipantId *primitive.ObjectID, err error,
) {
	currentPage := 1
	pageSize := constants.SHOWDOWN_PARTICIPANT_BATCH_SIZE
	var participantsLeft []*models.ShowdownParticipantDetail

	// 	get participants count
	totalParticipantsCount, err := s.showdownParticipantRepo.CountByShowdown(ctx, s.showdownID)
	if err != nil {
		return nil, nil, err
	}
	if totalParticipantsCount == 0 {
		return fixtures, byeParticipantId, nil
	}

	hasBye := totalParticipantsCount%2 == 1

	participantsCount := int64(0)
	shouldContinue := true

	for participantsCount < (totalParticipantsCount-int64(s.showdown.Rounds)) && shouldContinue {
		participants, err := s.getParticipantsForFixtures(ctx, currentPage, pageSize)
		if err != nil {
			return nil, nil, err
		}
		participants = append(participantsLeft, participants...)

		results, err := s.MatchParticipantsWithoutRepeat(ctx, ShowdownMatchingInput{
			participants: participants,
			showdown:     &s.showdown,
		})
		if err != nil {
			return nil, nil, err
		}

		fixtures = append(fixtures, results.Fixtures...)
		participantsLeft = results.Unmatched

		participantsCount += int64(len(participants))

		currentPage += 1
		shouldContinue = participantsCount < (totalParticipantsCount - int64(s.showdown.Rounds))
	}

	if totalParticipantsCount-participantsCount > 1 {
		participants, err := s.getParticipantsForFixtures(ctx, currentPage, pageSize)
		if err != nil {
			return nil, nil, err
		}
		participants = append(participantsLeft, participants...)
		if hasBye {
			byeParticipant := getByeParticipant(participants)
			if byeParticipant == nil {
				return nil, nil, ErrParticipantNotFound
			}
			byeParticipantId = &byeParticipant.ShowdownParticipant.UserID
		}

		results, err := s.MatchParticipantsWithoutRepeat(ctx, ShowdownMatchingInput{
			participants: participants,
			showdown:     &s.showdown,
		})
		if err != nil {
			return nil, nil, err
		}

		fixtures = append(fixtures, results.Fixtures...)
		participantsLeft = results.Unmatched
		if len(participantsLeft) != 0 {
			results, err = s.MatchRemainingParticipants(ShowdownMatchingInput{
				participants: participantsLeft,
				showdown:     &s.showdown,
			})
			if err != nil {
				return nil, nil, err
			}
			fixtures = append(fixtures, results.Fixtures...)
		}
	}

	return fixtures, byeParticipantId, nil
}
