package showdownFixtures

import (
	"math/rand/v2"

	"matiksOfficial/matiks-server-go/internal/models"
)

func getByeParticipant(participants []*models.ShowdownParticipantDetail) *models.ShowdownParticipantDetail {
	var byeParticipant *models.ShowdownParticipantDetail
	if len(participants) == 1 {
		byeParticipant = participants[0]
		participants = []*models.ShowdownParticipantDetail{}
		return byeParticipant
	}
	for i := len(participants) - 1; i >= 0; i-- {
		if !participants[i].ShowdownParticipant.HadABye {
			byeParticipant = participants[i]
			participants[i].ShowdownParticipant.HadABye = true
			return byeParticipant
		}
	}
	indexOfByeUser := rand.IntN(len(participants) - 1)

	byeParticipant = participants[indexOfByeUser]
	byeParticipant.ShowdownParticipant.HadABye = true
	if indexOfByeUser == 0 {
		participants = participants[1:]
	} else if (indexOfByeUser + 1) == len(participants) {
		participants = participants[:indexOfByeUser]
	} else {
		participants = append(participants[:indexOfByeUser], participants[indexOfByeUser+1:]...)
	}

	return byeParticipant
}
