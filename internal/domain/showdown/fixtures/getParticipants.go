package showdownFixtures

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *ShowdownFixtureService) getParticipantsForFixtures(ctx context.Context, currentPage, pageSize int) ([]*models.ShowdownParticipantDetail, error) {
	if s.currentRound == 1 {
		participantsInfo, err := s.showdownParticipantRepo.GetShowdownParticipantsPaginated(
			ctx,
			s.showdownID,
			currentPage,
			pageSize,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to get paginated participants: %v", err)
		}

		var participants []*models.ShowdownParticipantDetail
		for _, participantInfo := range participantsInfo {
			if participantInfo == nil {
				// TODO: ERROR HANDLING
				continue
			}
			participants = append(participants, &models.ShowdownParticipantDetail{ShowdownParticipant: *participantInfo})
		}
		return participants, nil
	} else {
		participants, err := s.getParticipantsFromLeaderboard(
			ctx,
			currentPage,
			pageSize,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to get paginated participants: %v", err)
		}
		return participants, nil
	}
}

func (s *ShowdownFixtureService) getParticipantsForLastPass(ctx context.Context, roundCount int) ([]*models.ShowdownParticipantDetail, error) {
	if s.currentRound == 1 {
		participantsInfo, err := s.showdownParticipantRepo.GetShowdownParticipantsOfLastPass(
			ctx,
			s.showdownID,
			roundCount,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to get paginated participants: %v", err)
		}

		var participants []*models.ShowdownParticipantDetail
		for _, participantInfo := range participantsInfo {
			if participantInfo == nil {
				// TODO: ERROR HANDLING
				continue
			}
			participants = append(participants, &models.ShowdownParticipantDetail{ShowdownParticipant: *participantInfo})
		}
		return participants, nil
	} else {
		participants, err := s.getLeaderboardParticipantsForLastPass(
			ctx,
			s.currentRound,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to get paginated participants: %v", err)
		}
		return participants, nil
	}
}
