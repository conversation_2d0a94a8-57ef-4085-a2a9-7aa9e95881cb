package showdownFixtures

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *ShowdownFixtureService) getParticipantsFromLeaderboard(ctx context.Context, currentPage, limit int) ([]*models.ShowdownParticipantDetail, error) {
	leaderboardEntities, err := s.showdownCache.GetPaginatedLeaderboard(ctx, s.showdownID, currentPage, limit)
	if err != nil {
		return nil, err
	}
	participants := make([]*models.ShowdownParticipantDetail, 0, len(leaderboardEntities))
	for _, v := range leaderboardEntities {
		participant, err := s.showdownCache.GetShowdownParticipant(ctx, s.showdownID, v.UserId)
		if err != nil {
			// TODO: Handle error
			continue
		}
		participants = append(participants, &models.ShowdownParticipantDetail{
			ShowdownParticipant: *participant,
		})
	}

	return participants, nil
}

func (s *ShowdownFixtureService) getLeaderboardParticipantsForLastPass(ctx context.Context, roundCount int) ([]*models.ShowdownParticipantDetail, error) {
	leaderboardEntities, err := s.showdownCache.GetPaginatedLeaderboardRev(ctx, s.showdownID, 1, roundCount)
	if err != nil {
		return nil, err
	}
	participants := make([]*models.ShowdownParticipantDetail, 0, len(leaderboardEntities))
	for _, v := range leaderboardEntities {
		participant, err := s.showdownCache.GetShowdownParticipant(ctx, s.showdownID, v.UserId)
		if err != nil {
			// TODO: Handle error
			continue
		}
		participants = append(participants, &models.ShowdownParticipantDetail{
			ShowdownParticipant: *participant,
		})
	}

	return participants, nil
}
