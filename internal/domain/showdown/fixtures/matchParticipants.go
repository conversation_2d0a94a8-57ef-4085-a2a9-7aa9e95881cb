package showdownFixtures

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

type ShowdownMatchingInput struct {
	participants []*models.ShowdownParticipantDetail
	showdown     *models.Showdown
}

type ShowdownMatchingResults struct {
	Fixtures  []*models.Fictures
	Unmatched []*models.ShowdownParticipantDetail
}

func (s *ShowdownFixtureService) MatchParticipantsWithoutRepeat(ctx context.Context, payload ShowdownMatchingInput) (results ShowdownMatchingResults, err error) {
	var fixtures []*models.Fictures
	var unmatchedParticipants []*models.ShowdownParticipantDetail

	matched := make(map[primitive.ObjectID]bool)

	if len(payload.participants) <= 1 {
		return results, nil
	}

	participantsLen := len(payload.participants)

	for i := 0; i < participantsLen; i++ {
		if payload.participants[i] == nil {
			zlog.Error(ctx, "participant found nil while matching", nil, zap.String("showdownID", s.showdownID.Hex()))
			return results, fmt.Errorf("participant found nil while matching")
		}
		if matched[payload.participants[i].ShowdownParticipant.UserID] {
			zlog.Error(ctx, "participant already matched", nil, zap.String("showdownID", s.showdownID.Hex()))
			continue
		}

		candidate1 := payload.participants[i]
		bestMatch := -1

		for j := i + 1; j < participantsLen; j++ {
			if matched[payload.participants[j].ShowdownParticipant.UserID] {
				zlog.Error(ctx, "participant already matched", nil, zap.String("showdownID", s.showdownID.Hex()))
				continue
			}

			candidate2 := payload.participants[j]
			if !hasRecentOpponent(candidate1, candidate2) {
				zlog.Error(ctx, "participants have no recent opponent", nil, zap.String("showdownID", s.showdownID.Hex()))
				bestMatch = j
				break
			}
		}
		if bestMatch != -1 {
			candidate2 := payload.participants[bestMatch]
			fixture, err := createFixture(candidate1, candidate2, s.showdownID, s.currentRound)
			if err != nil {
				zlog.Error(ctx, "failed to create fixture", err, zap.String("showdownID", s.showdownID.Hex()))
				return results, fmt.Errorf("failed to create fixture: %w", err)
			}

			fixtures = append(fixtures, &fixture)
			updateRecentOpponents(candidate1, candidate2)
			_, err = s.showdownCache.SetShowdownParticipant(ctx, &candidate1.ShowdownParticipant)
			if err != nil {
				return results, fmt.Errorf("failed to update showdown participant: %w", err)
			}
			_, err = s.showdownCache.SetShowdownParticipant(ctx, &candidate2.ShowdownParticipant)
			if err != nil {
				return results, fmt.Errorf("failed to update showdown participant: %w", err)
			}
			matched[candidate1.ShowdownParticipant.UserID] = true
			matched[candidate2.ShowdownParticipant.UserID] = true
		}
	}

	for _, p := range payload.participants {
		if !matched[p.ShowdownParticipant.UserID] {
			unmatchedParticipants = append(unmatchedParticipants, p)
		}
	}

	return ShowdownMatchingResults{
		Fixtures:  fixtures,
		Unmatched: unmatchedParticipants,
	}, nil
}

func (s *ShowdownFixtureService) MatchRemainingParticipants(payload ShowdownMatchingInput) (results ShowdownMatchingResults, err error) {
	var fixtures []*models.Fictures
	var unmatchedParticipants []*models.ShowdownParticipantDetail
	if len(payload.participants) == 0 {
		return results, nil
	}
	for i := 0; (i + 1) < len(payload.participants); i += 2 {
		candidate1 := payload.participants[i]
		candidate2 := payload.participants[i+1]
		fixture, err := createFixture(candidate1, candidate2, s.showdownID, s.currentRound)
		if err != nil {
			return results, fmt.Errorf("failed to create fixture: %w", err)
		}
		fixtures = append(fixtures, &fixture)
		updateRecentOpponents(candidate1, candidate2)
	}

	return ShowdownMatchingResults{
		Fixtures:  fixtures,
		Unmatched: unmatchedParticipants,
	}, nil
}

// func (s *ShowdownFixtureService) MatchRemainingParticipants(payload ShowdownMatchingInput) (results ShowdownMatchingResults, err error) {
