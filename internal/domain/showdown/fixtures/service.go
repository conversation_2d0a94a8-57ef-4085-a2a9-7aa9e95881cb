package showdownFixtures

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/models"
)

type ShowdownFixtureService struct {
	currentRound            int
	showdownID              primitive.ObjectID
	showdownCache           cache.ShowdownCache
	showdownParticipantRepo repository.ShowdownParticipantRepository
	gameRepo                repository.GameRepository
	gameService             domain.GameStore
	showdown                models.Showdown
}

func NewShowdownFixtureService(
	currentRound int, showdownID primitive.ObjectID,
	showdownCache cache.ShowdownCache, showdown *models.Showdown,
	showdownParticipantRepo repository.ShowdownParticipantRepository,
	gameRepo repository.GameRepository,
	gameService domain.GameStore,
) *ShowdownFixtureService {
	return &ShowdownFixtureService{
		currentRound:            currentRound,
		showdownID:              showdownID,
		showdownCache:           showdownCache,
		showdownParticipantRepo: showdownParticipantRepo,
		gameRepo:                gameRepo,
		gameService:             gameService,
		showdown:                *showdown,
	}
}
