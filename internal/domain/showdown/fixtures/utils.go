package showdownFixtures

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func updateRecentOpponents(p1, p2 *models.ShowdownParticipantDetail) {
	p1.ShowdownParticipant.RecentOpponents = append(
		p1.ShowdownParticipant.RecentOpponents,
		p2.ShowdownParticipant.UserID,
	)
	p2.ShowdownParticipant.RecentOpponents = append(
		p2.ShowdownParticipant.RecentOpponents,
		p1.ShowdownParticipant.UserID,
	)
}

func hasRecentOpponent(p1, p2 *models.ShowdownParticipantDetail) bool {
	for _, opponent := range p1.ShowdownParticipant.RecentOpponents {
		if opponent == p2.ShowdownParticipant.UserID {
			return true
		}
	}
	return false
}

func createFixture(p1, p2 *models.ShowdownParticipantDetail, showdownID primitive.ObjectID, round int) (fixture models.Fictures, err error) {
	if p1 == nil || p2 == nil {
		return models.Fictures{}, fmt.Errorf("participants found nil while creating fixture payload")
	}
	fixture = models.Fictures{
		ShowdownID:   showdownID,
		Users:        []*models.ShowdownParticipantDetail{p1, p2},
		Round:        round,
		Participants: []primitive.ObjectID{p1.ShowdownParticipant.UserID, p2.ShowdownParticipant.UserID},
	}
	return fixture, nil
}

func (s *ShowdownFixtureService) getShowdownParticipant(ctx context.Context, showdownId, userId primitive.ObjectID) (*models.ShowdownParticipant, error) {
	participant, err := s.showdownCache.GetShowdownParticipant(ctx, showdownId, userId.Hex())
	if err == nil && participant != nil {
		return participant, nil
	}
	participant, err = s.showdownParticipantRepo.GetByShowdownIDAndUserID(ctx, showdownId, userId)
	if err != nil {
		return nil, err
	}
	return participant, nil
}

func (s *ShowdownFixtureService) updateShowdownRoundBatch(ctx context.Context, roundDtos map[primitive.ObjectID]*models.ShowdownRoundDto, showdownID primitive.ObjectID) error {
	var participant *models.ShowdownParticipant
	var err error

	for _, dto := range roundDtos {
		if dto == nil {
			return fmt.Errorf("roundDTO is nil")
		}
		participant, err = s.getShowdownParticipant(ctx, showdownID, dto.UserId)
		if err != nil {
			return err
		}
		participant.Rounds = append(participant.Rounds, &dto.Round)
		_, err = s.showdownCache.SetShowdownParticipant(ctx, participant)
		if err != nil {
			return err
		}
	}
	err = s.showdownParticipantRepo.UpdateShowdownRoundsBatch(ctx, roundDtos, showdownID)
	return err
}
