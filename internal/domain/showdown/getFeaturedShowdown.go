package showdown

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *service) GetFeaturedShowdown(ctx context.Context) ([]*models.Showdown, error) {
	showdowns, err := s.showdownRepo.GetLatestShowdownByStartTime(ctx, 2)
	if err != nil {
		return nil, err
	}
	resp := make([]*models.Showdown, 0, len(showdowns))
	for _, showdown := range showdowns {
		if time.Now().After(showdown.StartTime.Add(-30 * time.Hour)) {
			resp = append(resp, showdown)
		}
	}
	return resp, nil
}
