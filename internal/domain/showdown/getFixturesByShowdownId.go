package showdown

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) GetFixturesByShowdownId(ctx context.Context, showdownID primitive.ObjectID) (*models.FicturesCollection, error) {
	fixtures := models.FicturesCollection{}
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	showdown, err := s.showdownCache.GetShowdown(ctx, showdownID)
	if err != nil {
		zlog.Error(ctx, "showdown not found", err, zap.String("showdownID", showdownID.Hex()))
		return nil, err
	}

	if showdown == nil {
		zlog.Error(ctx, "showdown not found", nil, zap.String("showdownID", showdownID.Hex()))
		return &fixtures, fmt.Errorf("showdown is nil")
	}

	currentUserFixture, err := s.fixtureRepo.GetByShowdownUserIDAndRound(ctx, showdownID, userID, showdown.CurrentRound)
	if err != nil {
		zlog.Error(ctx, "Failed to get fixture", err, zap.String("showdownID", showdownID.Hex()))
		return nil, err
	}

	if currentUserFixture == nil || currentUserFixture.ShowdownID.IsZero() {
		zlog.Error(ctx, "Failed to get fixture", nil, zap.String("showdownID", showdownID.Hex()))
		return nil, nil
	}

	fixtures.CurrentUserFicture = currentUserFixture

	for i, participantID := range fixtures.CurrentUserFicture.Participants {
		if participantID == userID && len(fixtures.CurrentUserFicture.Participants) == 2 && i == 1 {
			fixtures.CurrentUserFicture.Participants[0], fixtures.CurrentUserFicture.Participants[1] = fixtures.CurrentUserFicture.Participants[1], fixtures.CurrentUserFicture.Participants[0]
			fixtures.CurrentUserFicture.Users[0], fixtures.CurrentUserFicture.Users[1] = fixtures.CurrentUserFicture.Users[1], fixtures.CurrentUserFicture.Users[0]
		}
	}

	for i, participantID := range fixtures.CurrentUserFicture.Participants {
		participantData, err := s.getShowdownParticipant(ctx, showdownID, participantID)
		if err != nil {
			return nil, err
		}
		if participantData == nil {
			return nil, fmt.Errorf("participant is nil with id: %v", participantID.Hex())
		}
		fixtures.CurrentUserFicture.Users[i].ShowdownParticipant = *participantData
		for _, roundData := range participantData.Rounds {
			if roundData != nil && roundData.Round == showdown.CurrentRound {
				fixtures.CurrentUserFicture.Users[i].CurrentRound = *roundData
			}
		}
	}

	return &fixtures, nil
}
