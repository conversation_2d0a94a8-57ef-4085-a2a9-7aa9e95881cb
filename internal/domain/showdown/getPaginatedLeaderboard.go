package showdown

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) GetPaginatedLeaderboard(ctx context.Context, input *models.PaginatedLeaderboardInput) (*models.PaginatedLeaderboard, error) {
	showdown, err := s.getShowdownById(ctx, input.ShowdownID)
	if err != nil {
		return nil, err
	}
	var leaderboard *models.PaginatedLeaderboard
	if time.Now().Add(-5*time.Minute).Compare(showdown.EndTime) < 1 {
		leaderboard, err = s.getLeaderboardParticipantsFromCache(ctx, input.ShowdownID, input.Page)
		if err != nil {
			return nil, err
		}
		return leaderboard, nil
	}
	totalParticipants, err := s.showdownLeaderboardRepo.GetShowdownLeaderboardParticipantsCount(ctx, input.ShowdownID)
	if err != nil {
		return nil, err
	}
	leaderboardParticipants, err := s.showdownLeaderboardRepo.GetLeaderboardParticipantsPaginated(ctx, input.ShowdownID, input.Page, constants.LEADERBOARD_PAGE_SIZE)
	if err != nil {
		return nil, err
	}
	return &models.PaginatedLeaderboard{
		Participants: leaderboardParticipants,
		Count:        int(totalParticipants),
	}, nil
}

func (s *service) getLeaderboardParticipantsFromCache(ctx context.Context, showdownId primitive.ObjectID, page int) (*models.PaginatedLeaderboard, error) {
	totalCount, err := s.showdownCache.GetLeaderboardEntitiesCount(ctx, showdownId)
	if err != nil {
		return nil, err
	}
	leaderboardEntities, err := s.showdownCache.GetPaginatedLeaderboard(ctx, showdownId, page, constants.LEADERBOARD_PAGE_SIZE)
	if err != nil {
		return nil, err
	}
	participants := make([]*models.LeaderParticipantEntity, 0, len(leaderboardEntities))
	for _, v := range leaderboardEntities {
		participant, err := s.showdownCache.GetShowdownParticipant(ctx, showdownId, v.UserId)
		if err != nil {
			return nil, err
		}
		if participant == nil {
			return nil, fmt.Errorf("participant is nil")
		}
		participants = append(participants, &models.LeaderParticipantEntity{
			ShowdownId: showdownId,
			Score:      v.Score,
			Rank:       int(v.Rank),
			Participant: models.ParticipantBasicInfo{
				ID:         participant.ID,
				UserID:     participant.UserID,
				ShowdownID: participant.ShowdownID,
				Rounds:     participant.Rounds,
				UserInfo:   participant.UserInfo,
			},
			UserId: participant.UserID,
		})
	}
	return &models.PaginatedLeaderboard{
		Participants: participants,
		Count:        int(totalCount),
	}, nil
}
