package showdown

import (
	"context"
	"errors"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// TODO: implement cache setter 5 min ago only
func (s *service) GetShowdownByID(ctx context.Context, showdownID primitive.ObjectID) (*models.Showdown, error) {
	zlog.Debug(ctx, "Fetching showdown", zap.String("showdownID", showdownID.Hex()))

	showdown, err := s.getShowdownById(ctx, showdownID)
	if err != nil || showdown == nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zlog.Warn(ctx, "showdown not found", zap.String("showdownID", showdownID.Hex()))
			return nil, fmt.Errorf("showdown not found")
		}
		zlog.Error(ctx, "Failed to fetch showdown", err, zap.String("showdownID", showdownID.Hex()), zap.Error(err))
		return nil, fmt.Errorf("failed to fetch showdown: %w", err)
	}

	recentParticipants, err := s.getRecentParticipants(ctx, showdownID)
	if err != nil {
		zlog.Warn(ctx, "Failed to get recent participants", zap.String("showdownID", showdownID.Hex()), zap.Error(err))
		return nil, fmt.Errorf("failed to get recent participants: %w", err)
	}
	showdown.RecentParticipants = make([]*models.UserPublicDetails, len(recentParticipants))
	for i, participant := range recentParticipants {
		showdown.RecentParticipants[i] = utils.GetUserPublicDetails(participant)
	}

	err = s.showdownCache.SetShowdown(ctx, showdown)
	if err != nil {
		return nil, err
	}

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Warn(ctx, "User not authenticated", zap.Error(err))
		return nil, err
	}

	participant, err := s.getShowdownParticipant(ctx, showdownID, userID)
	if err != nil {
		if !errors.Is(err, mongo.ErrNoDocuments) {
			zlog.Error(ctx, "Failed to fetch participant", err, zap.String("showdownID", showdownID.Hex()), zap.String("userID", userID.Hex()))
			return nil, fmt.Errorf("failed to get participant: %w", err)
		}
	}
	if participant != nil {
		var (
			currentGame  primitive.ObjectID
			currentRound *models.ShowdownRound
		)
		for _, round := range participant.Rounds {
			if round.Round == showdown.CurrentRound {
				if len(round.Games) >= 1 {
					currentGame = round.Games[len(round.Games)-1]
				}
				currentRound = round
				break
			}
		}

		showdown.CurrentUserParticipation = &models.CurrentShowdonParticipant{
			ShowdownID:       &showdownID,
			UserID:           &userID,
			RegistrationData: participant.RegistrationData,
			Rounds:           participant.Rounds,
			CurrentGame:      &currentGame,
			CurrentRound:     currentRound,
		}
		zlog.Debug(ctx, "Participant found", zap.String("showdownID", showdownID.Hex()), zap.String("userID", userID.Hex()))
	}

	zlog.Debug(ctx, "showdown fetched successfully", zap.String("showdownID", showdownID.Hex()))
	return showdown, nil
}

func (s *service) getShowdownById(ctx context.Context, showdownId primitive.ObjectID) (*models.Showdown, error) {
	showdown, err := s.showdownCache.GetShowdown(ctx, showdownId)
	if err != nil {
		showdown, err = s.showdownRepo.FindByID(ctx, showdownId)
		if err != nil {
			return nil, err
		}
	}

	return showdown, err
}
