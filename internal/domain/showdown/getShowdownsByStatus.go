package showdown

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) GetShowdownsByStatus(ctx context.Context, statuses []models.ShowdownContestStatus, page, pageSize *int, sortDirection *string) (*models.PaginatedShowdowns, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	paginatedShowdowns, err := s.showdownRepo.GetShowdownsByStatus(ctx, statuses, page, pageSize, sortDirection)
	if err != nil {
		zlog.Error(ctx, "Failed to get showdowns", err)
		return nil, fmt.Errorf("failed to decode showdowns: %w", err)
	}

	var showdownIds []*primitive.ObjectID
	for _, showdown := range paginatedShowdowns.Showdowns {
		showdownIds = append(showdownIds, showdown.ID)
	}
	if len(showdownIds) == 0 {
		return paginatedShowdowns, nil
	}
	participants, err := s.showdownParticipantRepo.GetParticipantsByUserAndShowdowns(ctx, showdownIds, userId)
	if err != nil {
		zlog.Error(ctx, "Failed to get participants", err)
		return nil, fmt.Errorf("failed to get participants: %w", err)
	}

	if len(participants) == 0 {
		return paginatedShowdowns, nil
	}

	participantsMap := map[primitive.ObjectID]*models.ShowdownParticipant{}
	for _, participant := range participants {
		if participant == nil {
			zlog.Error(ctx, "participant found nil", nil)
			continue
		}
		participantsMap[participant.ShowdownID] = participant
	}

	for _, showdown := range paginatedShowdowns.Showdowns {
		if showdown == nil || showdown.ID == nil {
			zlog.Error(ctx, "showdown found nil", nil)
			continue
		}
		if participant, ok := participantsMap[*showdown.ID]; ok && participant != nil {
			var currentUserParticipant models.CurrentShowdonParticipant
			err := MapShowdownParticipantToCurrentParticipant(participant, &currentUserParticipant, showdown.CurrentRound)
			if err != nil {
				zlog.Error(ctx, "Failed to map participant to currentUserParticipant", err)
			}
			showdown.CurrentUserParticipation = &currentUserParticipant
		}
	}

	zlog.Debug(ctx, "Showdowns fetched successfully", zap.Int("count", len(paginatedShowdowns.Showdowns)))
	return paginatedShowdowns, nil
}
