package showdown

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func MapShowdownParticipantToCurrentParticipant(participant *models.ShowdownParticipant, currentParticipant *models.CurrentShowdonParticipant, currentRound int) error {
	if participant == nil {
		return fmt.Errorf("participant is nil")
	}
	if currentParticipant == nil {
		return fmt.Errorf("current participant is nil")
	}
	currentParticipant.ShowdownID = &participant.ShowdownID
	currentParticipant.UserID = &participant.UserID
	currentParticipant.Rounds = participant.Rounds
	currentParticipant.TotalScore = participant.TotalScore
	for _, roundData := range participant.Rounds {
		if roundData.Round == currentRound {
			currentParticipant.CurrentRound = roundData
			gamesLen := len(roundData.Games)
			if gamesLen > 0 {
				currentParticipant.CurrentGame = &roundData.Games[gamesLen-1]
			}
			break
		}
	}
	return nil
}

func (s *service) NotifyUsersAsFixturesCreated(ctx context.Context, showdownId primitive.ObjectID, round int, byeParticipantId *primitive.ObjectID) error {
	// TODO: handle errors
	showdown, err := s.getShowdownById(ctx, showdownId)
	if err != nil || showdown == nil {
		return fmt.Errorf("showdow not found %w", err)
	}
	showdownNotificationPayload := models.ShowdownMinifiedNotificationPayload{
		ID:                showdown.ID,
		Name:              showdown.Name,
		Description:       showdown.Description,
		CurrentRound:      showdown.CurrentRound,
		StartTime:         showdown.StartTime,
		RegistrationCount: showdown.RegistrationCount,
	}

	fixtures, err := s.fixtureRepo.GetFixturesByShowdownIDAndRound(ctx, showdownId, round)
	if err != nil {
		return err
	}
	if fixtures == nil {
		return fmt.Errorf("fixtures not found")
	}
	for _, fixture := range fixtures {
		if fixture == nil {
			zlog.Error(ctx, "fixture found nil while notifying users", nil, zap.String("showdownID", showdownId.Hex()))
			continue
		}
		for _, userId := range fixture.Participants {
			zlog.Debug(ctx, "Notifying matched players", zap.String("user", userId.Hex()))

			participant, err := s.getShowdownParticipant(ctx, showdownId, userId)
			if err != nil {
				zlog.Debug(ctx, "Failed to participant from cache", zap.String("user", userId.Hex()))
				continue
			}
			var currentUserParticipantDto models.CurrentShowdonParticipant
			err = MapShowdownParticipantToCurrentParticipant(participant, &currentUserParticipantDto, round)
			if err != nil {
				zlog.Debug(ctx, "Failed to map participant to currentUserParticipantDto", zap.String("user", userId.Hex()))
				continue
			}
			data := models.ShowdownFicturesCreatedEvent{
				Showdown:       &showdownNotificationPayload,
				CurrentFixture: fixture,
				Participant:    &currentUserParticipantDto,
			}

			err = s.coreService.PublishUserEvent(ctx, userId, &data)
			if err != nil {
				zlog.Error(ctx, "failed to publish fixture creation event", err)
				return err
			}
		}
	}

	if byeParticipantId != nil {
		zlog.Debug(ctx, "Notifying bye participant", zap.String("user", (*byeParticipantId).Hex()))
		participant, err := s.getShowdownParticipant(ctx, showdownId, *byeParticipantId)
		if err != nil {
			zlog.Debug(ctx, "Failed to get bye participant", zap.String("user", (*byeParticipantId).Hex()))
		}
		var currentUserParticipantDto models.CurrentShowdonParticipant
		err = MapShowdownParticipantToCurrentParticipant(participant, &currentUserParticipantDto, round)
		if err != nil {
			zlog.Debug(ctx, "Failed to map participant to currentUserParticipantDto", zap.String("user", (*byeParticipantId).Hex()))
			return err
		}
		data := models.ShowdownFicturesCreatedEvent{
			Showdown:    &showdownNotificationPayload,
			Participant: &currentUserParticipantDto,
		}
		err = s.coreService.PublishUserEvent(ctx, *byeParticipantId, &data)
		if err != nil {
			zlog.Error(ctx, "Failed to publish Ficture creation event", err)
			return err
		}
	}

	return nil
}

// TODO: add error logs
// TODO: add condition for reschedule of sumday showdown
func (s *service) NotifyUsersAsShowdownStarts(ctx context.Context, showdownId primitive.ObjectID, round int) error {
	showdown, err := s.getShowdownById(ctx, showdownId)
	if err != nil || showdown == nil {
		return fmt.Errorf("showdow not found %w", err)
	}
	showdownNotificationPayload := models.ShowdownMinifiedNotificationPayload{
		ID:           showdown.ID,
		Name:         showdown.Name,
		Description:  showdown.Description,
		CurrentRound: showdown.CurrentRound,
		StartTime:    showdown.StartTime,
	}
	fixtures, err := s.fixtureRepo.GetFixturesByShowdownIDAndRound(ctx, showdownId, round)
	if err != nil {
		return err
	}
	if fixtures == nil {
		return fmt.Errorf("fixtures not found")
	}
	for _, fixture := range fixtures {
		if fixture == nil {
			zlog.Error(ctx, "fixture found nil while notifying users", nil, zap.String("showdownID", showdownId.Hex()))
			continue
		}
		players := make([]models.ShowdownUserDetails, 0, 2)
		for _, user := range fixture.Users {
			if user == nil {
				zlog.Error(ctx, "user found nil while notifying users", nil, zap.String("showdownID", showdownId.Hex()))
				continue
			}
			players = append(players, *user.ShowdownParticipant.UserInfo)
		}
		// TODO: handle next round events
		data := models.ShowdownToStartEvent{
			Showdown:   &showdownNotificationPayload,
			ShowdownID: showdownNotificationPayload.ID,
		}
		err := s.notifyShowdownPlayers(ctx, &data, players)
		if err != nil {
			return err
		}
	}

	// nextRound := round + 1
	// var nextRoundAt time.Time
	// if round == 1 {
	// 	nextRoundAt = showdown.StartTime.Add(-5 * time.Second)
	// } else {
	// 	currentRoundStartTime := showdown.StartTime.Add(time.Duration((showdown.RoundTime*(nextRound-1))+showdown.GapBwRounds*(nextRound-1)-5) * time.Second)
	// 	nextRoundAt = currentRoundStartTime
	// }

	// scheduler.Schedule(ctx, s.sortedset, &models.SchedulerPayload{
	// 	Type: models.NotifyAsShowdownStarts,
	// 	Action: models.ShowdownRoundActionPayload{
	// 		ShowdownId: showdownId,
	// 		Round:      nextRound,
	// 	},
	// }, nextRoundAt)

	return nil
}
