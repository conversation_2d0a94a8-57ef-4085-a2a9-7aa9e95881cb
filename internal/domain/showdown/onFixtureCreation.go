package showdown

import (
	"context"
	"fmt"

	showdownFixtures "matiksOfficial/matiks-server-go/internal/domain/showdown/fixtures"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) OnFixtureCreation(ctx context.Context, showdownId primitive.ObjectID, currentRound int) error {
	zlog.Info(ctx, "Creating Fixtures for", zap.String("showdownID", showdownId.Hex()), zap.Any("round", currentRound))

	showdown, err := s.getShowdownById(ctx, showdownId)
	if err != nil {
		zlog.Error(ctx, "Failed to get showdown", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}
	if showdown == nil {
		zlog.Error(ctx, "Failed to create fixtures registration count", nil, zap.String("showdownID", showdownId.Hex()))
		return fmt.Errorf("showdown null")
	}

	if currentRound < 1 {
		currentRound = 1
	}

	if currentRound == 1 {
		err = s.UpdateShowdownStatus(ctx, showdownId, models.ShowdownContestStatusFicturesCreated)
		if err != nil {
			return err
		}
	}
	fixtureService := showdownFixtures.NewShowdownFixtureService(currentRound, showdownId, s.showdownCache, showdown, s.showdownParticipantRepo, s.gameRepo, s.gameService)
	fixtures, byeParticipantId, err := fixtureService.CreateFixtures(ctx)
	if err != nil {
		zlog.Error(ctx, "Failed to create fixtures", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}

	if err = fixtureService.CreateGamesForShowdown(ctx, fixtures); err != nil {
		return err
	}

	if err = s.fixtureRepo.BulkInsert(ctx, fixtures); err != nil {
		zlog.Error(ctx, "Failed to insert fixtures", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}

	if err = s.scheduleNextRoundAndNotifications(ctx, showdown, currentRound, showdownId, byeParticipantId); err != nil {
		zlog.Error(ctx, "Failed to schedule next round and notification updates", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}

	return nil
}
