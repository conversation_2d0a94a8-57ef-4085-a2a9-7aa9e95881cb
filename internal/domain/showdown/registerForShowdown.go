package showdown

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) RegisterForShowdown(ctx context.Context, input models.ShowdownRegistrationFormValuesInput) (bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Failed to get user from context", err)
		return false, fmt.Errorf("unauthorized: %w", err)
	}

	zlog.Info(ctx, "Registering for showdown", zap.String("showdownID", input.ShowdownID.Hex()), zap.String("userID", userID.Hex()))

	showdownID := input.ShowdownID

	showdown, err := s.GetShowdownByID(ctx, showdownID)
	if err != nil {
		zlog.Error(ctx, "Failed to fetch showdown", err, zap.String("showdownID", showdownID.Hex()))
		return false, fmt.Errorf("failed to fetch contest: %w", err)
	}

	if showdown.ClubID != nil {
		clubMembershipInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, userID, *showdown.ClubID)
		if err != nil {
			zlog.Error(ctx, "Failed to fetch club membership info", err, zap.String("showDownId", showdownID.Hex()))
			return false, fmt.Errorf("failed to fetch club membership info: %w", err)
		}
		if clubMembershipInfo == nil {
			zlog.Warn(ctx, "User is not a member of the club", zap.String("showDownId", showdownID.Hex()))
			return false, fmt.Errorf("user is not a member of the club")
		}
	}

	currentTime := time.Now()
	if (showdown.RegistrationStartTime != nil && currentTime.Before(*showdown.RegistrationStartTime)) || currentTime.After(showdown.EndTime) {
		zlog.Warn(ctx, "Registration is not open", zap.String("showdownID", showdownID.Hex()))
		return false, fmt.Errorf("registration is not open for this contest")
	}

	zlog.Info(ctx, "Checking for showdown status", zap.String("showdownID", showdownID.Hex()), zap.String("showdownStatus", showdown.Status.String()), zap.String("userID", userID.Hex()))

	if showdown.Status != models.ShowdownContestStatusRegistrationOpen {
		zlog.Warn(ctx, "Registration is not open", zap.String("showdownID", showdownID.Hex()))
		return false, fmt.Errorf("registration is not open for this contest")
	}

	existingParticipant, err := s.showdownParticipantRepo.GetByShowdownIDAndUserID(ctx, showdownID, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to check for existing participant", err, zap.String("showdownID", showdownID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("failed to check for existing participant: %w", err)
	}
	if existingParticipant != nil {
		zlog.Warn(ctx, "Participant already registered", zap.String("showdownID", showdownID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("you have already registered for this contest")
	}

	userData, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		zlog.Warn(ctx, "Failed to get user info from DB", zap.String("showdownID", showdownID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("failed to get user info")
	}

	newParticipant := &models.ShowdownParticipant{
		ID:         utils.AllocPtr(primitive.NewObjectID()),
		UserID:     userID,
		ShowdownID: showdownID,
		// RegistrationData: registrationData,
		CreatedAt: currentTime,
		UpdatedAt: currentTime,
		UserInfo: &models.ShowdownUserDetails{
			Username:        userData.Username,
			Name:            userData.Name,
			ProfileImageURL: userData.ProfileImageURL,
			Rating:          userData.Rating,
		},
	}

	err = s.showdownParticipantRepo.Create(ctx, newParticipant)
	if err != nil {
		zlog.Error(ctx, "Failed to create participant", err, zap.String("showdownID", showdownID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("failed to create participant: %w", err)
	}

	showdown, err = s.showdownRepo.IncrementRegistrationCount(ctx, showdownID)
	if err != nil {
		zlog.Error(ctx, "Failed to update showdown registration count", err, zap.String("showdownID", showdownID.Hex()))
		return false, fmt.Errorf("failed to update showdown registration count: %w", err)
	}

	if showdown.ClubID != nil {
		err = s.clubEventRepo.IncrementEventParticipationCount(ctx, *showdown.ClubID, showdownID)
		if err != nil {
			zlog.Error(ctx, "Failed to update club event participation count", err, zap.String("showdownID", showdownID.Hex()))
			return false, fmt.Errorf("failed to update club event participation count: %w", err)
		}
	}

	err = s.showdownCache.SetShowdown(ctx, showdown)
	if err != nil {
		zlog.Error(ctx, "Failed to update showdown registration count in cache", err, zap.String("showdownID", showdownID.Hex()))
	}

	zlog.Info(ctx, "Successfully registered participant", zap.String("showdownID", showdownID.Hex()), zap.String("userID", userID.Hex()))
	return true, nil
}

func validateFormData(formData []*models.RegistrationFormFieldValueInput, formFields []*models.FormField) bool {
	for _, field := range formFields {
		formFieldSubmission := findFormFieldSubmission(formData, field.Name)
		if formFieldSubmission == nil {
			if field.Required {
				return false
			}
			continue
		}
		// TODO: Add more validation rules based on field type and validation properties
	}
	return true
}

func findFormFieldSubmission(formData []*models.RegistrationFormFieldValueInput, fieldName string) *models.RegistrationFormFieldValueInput {
	for _, field := range formData {
		if field.Name == fieldName {
			return field
		}
	}
	return nil
}
