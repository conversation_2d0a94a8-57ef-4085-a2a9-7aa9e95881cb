package showdown

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"

	"matiksOfficial/matiks-server-go/internal/domain/scheduler"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) scheduleNextRoundAndNotifications(ctx context.Context, showdown *models.Showdown, currentRound int, showdownId primitive.ObjectID, byeParticipantId *primitive.ObjectID) error {
	nextRound := currentRound + 1
	if showdown.Rounds >= nextRound {
		nextRoundTime := s.calculateNextRoundTime(showdown, currentRound)

		if err := scheduler.Schedule(ctx, s.sortedSet, &models.SchedulerPayload{
			Type: models.FixtureCreation,
			Action: models.ShowdownRoundActionPayload{
				ShowdownId: showdownId,
				Round:      nextRound,
			},
			ContextMap: utils.GetContextValuesMap(ctx),
		}, nextRoundTime); err != nil {
			return err
		}
	}

	// currentRoundStartTime := showdown.StartTime.Add(time.Duration((showdown.RoundTime*(currentRound-1))+showdown.GapBwRounds*(currentRound-2)) * time.Second)
	currentRoundStartsAt := showdown.StartTime
	if currentRound > 1 {
		currentRoundStartsAt = s.calculateCurrentRoundTime(showdown, currentRound)
	}

	if err := scheduler.Schedule(ctx, s.sortedSet, &models.SchedulerPayload{
		Type: models.ScheduleShowdownRoundUpdate,
		Action: models.ShowdownRoundActionPayload{
			ShowdownId: showdownId,
			Round:      currentRound,
		},
		ContextMap: utils.GetContextValuesMap(ctx),
	}, currentRoundStartsAt); err != nil {
		return err
	}
	// TODO: add round update notification to users add it next week

	// currentRoundWaitEndsAt := currentRoundStartsAt.Add(time.Duration(showdown.RoundConfig.MaxWaitTime+showdown.GapBwRounds) * time.Second)
	// if err := scheduler.Schedule(ctx, s.sortedset, &models.SchedulerPayload{
	// 	Type: models.CheckShowdownRoundPlayerStatus,
	// 	Action: models.ShowdownRoundActionPayload{
	// 		ShowdownId: showdownId,
	// 		Round:      currentRound,
	// 	},
	// }, currentRoundWaitEndsAt); err != nil {
	// 	return err
	// }

	if currentRound == showdown.Rounds {
		currentRoundEndsAt := s.calculateCurrentRoundEndTime(showdown, currentRoundStartsAt).Add(60 * time.Second)

		// For showdown round end
		if err := scheduler.Schedule(ctx, s.sortedSet, &models.SchedulerPayload{
			Type: models.ScheduleShowdownEnd,
			Action: models.ShowdownRoundActionPayload{
				ShowdownId: showdownId,
				Round:      currentRound,
			},
			ContextMap: utils.GetContextValuesMap(ctx),
		}, currentRoundEndsAt); err != nil {
			return err
		}
	}

	// Schedule notifications
	var byeParticipant primitive.ObjectID
	if byeParticipantId != nil {
		byeParticipant = *byeParticipantId
	}

	return scheduler.Schedule(ctx, s.sortedSet, &models.SchedulerPayload{
		Type: models.NotifyFixtureCreation,
		Action: models.NotifyUsersForFixtureCreationActionPayload{
			ShowdownId:       showdownId,
			CurrentRound:     currentRound,
			ByeParticipantId: byeParticipant,
		},
		ContextMap: utils.GetContextValuesMap(ctx),
	}, time.Now().Add(time.Second*10))
}

func (s *service) calculateCurrentRoundTime(showdown *models.Showdown, currentRound int) time.Time {
	return showdown.StartTime.Add(time.Duration(
		(currentRound-1)*showdown.RoundTime+
			(currentRound-2)*showdown.GapBwRounds,
	) * time.Second)
}

func (s *service) calculateCurrentRoundEndTime(showdown *models.Showdown, currentRoundStartTime time.Time) time.Time {
	return currentRoundStartTime.Add(time.Duration(
		showdown.RoundTime,
	) * time.Second)
}

func (s *service) calculateNextRoundTime(showdown *models.Showdown, currentRound int) time.Time {
	return showdown.StartTime.Add(time.Duration(
		currentRound*showdown.RoundTime+
			(currentRound-1)*showdown.GapBwRounds+
			15,
	) * time.Second)
}
