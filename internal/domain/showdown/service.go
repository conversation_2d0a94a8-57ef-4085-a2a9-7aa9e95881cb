package showdown

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/infrastructure/sortedset"
	"matiksOfficial/matiks-server-go/internal/infrastructure/websocket"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type service struct {
	ws                      websocket.Websocket
	userService             domain.UserStore
	coreService             domain.CoreLogicStore
	gameService             domain.GameStore
	showdownRepo            repository.ShowdownRepository
	gameRepo                repository.GameRepository
	clubMemberRepo          repository.ClubMemberRepository
	clubEventRepo           repository.ClubEventRepository
	showdownCache           cache.ShowdownCache
	showdownParticipantRepo repository.ShowdownParticipantRepository
	showdownLeaderboardRepo repository.ShowdownLeaderboardRepository
	fixtureRepo             repository.FixturesRepository
	sortedSet               sortedset.SortedSet
}

func NewShowdownService(
	lc fx.Lifecycle,
	ws websocket.Websocket,
	repositoryFactory *repository.RepositoryFactory,
	gameService domain.GameStore,
	cacheInstance cache.Cache,
	leaderboardCacheInstance cache.LeaderboardCache,
	userService domain.UserStore,
	sortedSet sortedset.SortedSet,
	coreService domain.CoreLogicStore,
) domain.ShowdownStore {
	s := &service{
		ws:                      ws,
		showdownRepo:            repositoryFactory.ShowdownRepository,
		gameService:             gameService,
		showdownCache:           cache.NewShowdownCacheWrapper(cacheInstance, leaderboardCacheInstance),
		gameRepo:                repositoryFactory.GameRepository,
		fixtureRepo:             repositoryFactory.FixtureRepository,
		showdownLeaderboardRepo: repositoryFactory.ShowdownLeaderboardRepository,
		showdownParticipantRepo: repositoryFactory.ShowdownParticipantRepository,
		clubMemberRepo:          repositoryFactory.ClubMemberRepository,
		clubEventRepo:           repositoryFactory.ClubEventRepository,
		userService:             userService,
		sortedSet:               sortedSet,
		coreService:             coreService,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting showdown service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down showdown service")
			return nil
		},
	})

	return s
}
