package showdown

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) UnregisterFromShowdown(ctx context.Context, showdownID primitive.ObjectID) (bool, error) {
	zlog.Debug(ctx, "Unregistering from showdown", zap.String("showdownID", showdownID.Hex()))

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	showdown, err := s.showdownRepo.FindByID(ctx, showdownID)
	if err != nil {
		zlog.Error(ctx, "Failed to fetch showdown", err, zap.String("showdownID", showdownID.Hex()))
		return false, fmt.Errorf("failed to fetch showdown: %w", err)
	}

	if showdown.ClubID != nil {
		clubMembershipInfo, err := s.clubMemberRepo.GetMemberInfo(ctx, userID, *showdown.ClubID)
		if err != nil {
			zlog.Error(ctx, "Failed to fetch club membership info", err, zap.String("showdownID", showdownID.Hex()))
			return false, fmt.Errorf("failed to fetch club membership info: %w", err)
		}
		if clubMembershipInfo == nil {
			zlog.Warn(ctx, "User is not a member of the club", zap.String("showdownID", showdownID.Hex()))
			return false, fmt.Errorf("user is not a member of the club")
		}
	}

	currentTime := time.Now()
	if currentTime.After(showdown.StartTime) {
		zlog.Warn(ctx, "Attempted to unregister after showdown start", zap.String("showdownID", showdownID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("showdown has already started. You cannot unregister at this time")
	}

	if showdown.Status != models.ShowdownContestStatusRegistrationOpen {
		zlog.Warn(ctx, "Attempted to unregister after showdown start", zap.String("showdownID", showdownID.Hex()))
		return false, fmt.Errorf("attempted to unregister after showdown start")
	}

	deleted, err := s.showdownParticipantRepo.DeleteByShowdownAndUser(ctx, showdownID, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to delete participant", err, zap.String("showdownID", showdownID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("failed to unregister from showdown: %w", err)
	}
	if !deleted {
		zlog.Warn(ctx, "Participant not found", zap.String("showdownID", showdownID.Hex()), zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("you are not registered for this showdown")
	}

	showdown, err = s.showdownRepo.DecrementRegistrationCount(ctx, showdownID)
	if err != nil {
		zlog.Error(ctx, "Failed to update showdown registration count", err, zap.String("showdownID", showdownID.Hex()))
		return false, fmt.Errorf("failed to update showdown registration count: %w", err)
	}

	if showdown.ClubID != nil {
		err = s.clubEventRepo.DecrementEventParticipationCount(ctx, *showdown.ClubID, showdownID)
		if err != nil {
			zlog.Error(ctx, "Failed to update club event participation count", err, zap.String("showdownID", showdownID.Hex()))
			return false, fmt.Errorf("failed to update club event participation count: %w", err)
		}
	}

	err = s.showdownCache.SetShowdown(ctx, showdown)
	if err != nil {
		zlog.Error(ctx, "Failed to update showdown registration count in cache", err, zap.String("showdownID", showdownID.Hex()))
	}

	zlog.Info(ctx, "Successfully unregistered from showdown", zap.String("showdownID", showdownID.Hex()), zap.String("userID", userID.Hex()))
	return true, nil
}
