package streakShieldTransaction

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

// GetUserStreakShieldTransactions retrieves streak shield transactions for the current user
func (s *service) GetUserStreakShieldTransactions(ctx context.Context, page, pageSize int) (*models.StreakShieldTransactionPage, error) {
	// Get current user ID from context
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Failed to get user from context", err)
		return nil, fmt.Errorf("unauthorized: %w", err)
	}

	zlog.Info(ctx, "Getting streak shield transactions for user", zap.String("userID", userID.Hex()))

	// Get transactions
	transactions, total, err := s.transactionRepo.FindByUserID(ctx, userID, page, pageSize)
	if err != nil {
		zlog.Error(ctx, "Failed to get streak shield transactions", err, zap.String("userID", userID.Hex()))
		return nil, fmt.Errorf("failed to get streak shield transactions: %w", err)
	}

	// Transform transactions to include referral details when applicable
	outputs := make([]*models.StreakShieldTransactionOutput, 0, len(transactions))
	for _, transaction := range transactions {
		output := &models.StreakShieldTransactionOutput{
			Transaction: transaction,
		}

		// If this is a referral transaction, fetch the referral details
		if transaction.EarnVia != nil && *transaction.EarnVia == models.EarnViaReferral && transaction.ReferralID != nil {
			// Get the referral
			referral, err := s.getReferralDetails(ctx, *transaction.ReferralID)
			if err == nil && referral != nil {
				output.ReferralDetails = referral
			}
		}

		outputs = append(outputs, output)
	}

	// Calculate hasMore
	hasMore := (page * pageSize) < total

	// Return the page
	return &models.StreakShieldTransactionPage{
		Results:      outputs,
		PageNumber:   page,
		PageSize:     pageSize,
		HasMore:      &hasMore,
		TotalResults: &total,
	}, nil
}

// getReferralDetails fetches the referral and referred user details
func (s *service) getReferralDetails(ctx context.Context, referralID primitive.ObjectID) (*models.ReferralDetails, error) {
	// Get the referral from the database
	referral, err := s.referralRepo.FindByID(ctx, referralID)
	if err != nil {
		zlog.Error(ctx, "Failed to get referral", err, zap.String("referralID", referralID.Hex()))
		return nil, err
	}

	// Get the referred user details
	referredUser, err := s.userRepo.GetByID(ctx, referral.ReferredTo)
	if err != nil {
		zlog.Error(ctx, "Failed to get referred user", err, zap.String("referredUserID", referral.ReferredTo.Hex()))
		return nil, err
	}

	if referredUser == nil {
		zlog.Error(ctx, "Referred user not found", err, zap.String("referredUserID", referral.ReferredTo.Hex()))
		return nil, fmt.Errorf("referred user not found")
	}

	// Convert to UserPublicDetails
	userPublicDetails := utils.GetUserPublicDetails(referredUser)

	return &models.ReferralDetails{
		Referral:     referral,
		ReferredUser: userPublicDetails,
	}, nil
}
