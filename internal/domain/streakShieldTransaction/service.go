package streakShieldTransaction

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type service struct {
	transactionRepo repository.StreakShieldTransactionRepository
	userRepo        repository.UserRepository
	referralRepo    repository.ReferralsRepository
}

func NewStreakShieldTransactionService(
	lc fx.Lifecycle,
	transactionRepo repository.StreakShieldTransactionRepository,
	userRepo repository.UserRepository,
	referralRepo repository.ReferralsRepository,
) domain.StreakShieldTransactionStore {
	s := &service{
		transactionRepo: transactionRepo,
		userRepo:        userRepo,
		referralRepo:    referralRepo,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting Streak Shield Transaction service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down Streak Shield Transaction service")
			return nil
		},
	})

	return s
}
