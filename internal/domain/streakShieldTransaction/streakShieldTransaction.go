package streakShieldTransaction

import (
	"context"
	"errors"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

// StreakShieldTransactionService implements the StreakShieldTransactionStore interface
type StreakShieldTransactionService struct {
	transactionRepo repository.StreakShieldTransactionRepository
	userRepo        repository.UserRepository
	referralRepo    repository.ReferralsRepository
}

// CreateTransaction creates a new streak shield transaction
func (s *service) CreateTransaction(
	ctx context.Context,
	userID primitive.ObjectID,
	quantity int,
	transactionType models.TransactionType,
	earnVia *models.EarnVia,
	referralID *primitive.ObjectID,
	transactionID *string,
) (*models.StreakShieldTransaction, error) {
	// Validate input
	if quantity <= 0 {
		return nil, errors.New("quantity must be greater than 0")
	}

	// Validate transaction type and related fields
	if transactionType == models.TransactionTypeCredited {
		if earnVia == nil {
			return nil, errors.New("earnVia is required for credited transactions")
		}

		// Validate referral ID for referral transactions
		if *earnVia == models.EarnViaReferral && referralID == nil {
			return nil, errors.New("referralId is required for referral transactions")
		}

		// Validate transaction ID for purchased transactions
		if *earnVia == models.EarnViaPurchased && transactionID == nil {
			return nil, errors.New("transactionId is required for purchased transactions")
		}
	}

	// Create transaction
	transaction := &models.StreakShieldTransaction{
		ID:              primitive.NewObjectID(),
		UserID:          userID,
		Quantity:        quantity,
		TransactionType: transactionType,
		EarnVia:         earnVia,
		ReferralID:      referralID,
		TransactionID:   transactionID,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// For debited transactions, initialize the redeemedOn array
	if transactionType == models.TransactionTypeDebited {
		now := time.Now()
		transaction.RedeemedOn = []time.Time{now}
	}

	// Save transaction
	err := s.transactionRepo.Create(ctx, transaction)
	if err != nil {
		zlog.Error(ctx, "Failed to create streak shield transaction", err)
		return nil, err
	}

	// Update user's streak freezers count
	update := bson.M{}
	if transactionType == models.TransactionTypeCredited {
		update = bson.M{"$inc": bson.M{"userStreaks.streakFreezers": quantity}}
	} else {
		update = bson.M{"$inc": bson.M{"userStreaks.streakFreezers": -quantity}}
	}

	err = s.userRepo.UpdateOne(ctx, bson.M{"_id": userID}, update)
	if err != nil {
		zlog.Error(ctx, "Failed to update user's streak freezers", err, zap.String("userId", userID.Hex()))
		return nil, fmt.Errorf("failed to update user's streak freezers: %w", err)
	}

	return transaction, nil
}

// GetUserTransactions retrieves streak shield transactions for a user
func (s *service) GetUserTransactions(
	ctx context.Context,
	userID primitive.ObjectID,
	page, pageSize int,
) ([]*models.StreakShieldTransaction, int, error) {
	return s.transactionRepo.FindByUserID(ctx, userID, page, pageSize)
}

// GetAllTransactions retrieves all streak shield transactions with pagination
func (s *service) GetAllTransactions(
	ctx context.Context,
	page, pageSize int,
) ([]*models.StreakShieldTransaction, int, error) {
	return s.transactionRepo.FindAll(ctx, page, pageSize)
}

// AddRedemptionDate adds a redemption date to a debited transaction
func (s *service) AddRedemptionDate(
	ctx context.Context,
	transactionID primitive.ObjectID,
	redemptionDate time.Time,
) error {
	transaction, err := s.transactionRepo.FindByID(ctx, transactionID)
	if err != nil {
		return err
	}

	if transaction == nil {
		return errors.New("transaction not found")
	}

	if transaction.TransactionType != models.TransactionTypeDebited {
		return errors.New("can only add redemption dates to debited transactions")
	}

	update := bson.M{"$push": bson.M{"redeemedOn": redemptionDate}}
	return s.transactionRepo.UpdateOne(ctx, bson.M{"_id": transactionID}, update)
}
