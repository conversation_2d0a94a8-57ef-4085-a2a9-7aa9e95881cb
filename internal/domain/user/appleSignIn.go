package user

import (
	"context"
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"math/big"
	"net/http"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func (s *service) SignInWithApple(ctx context.Context, input *models.AppleSignInInput) (*models.User, error) {
	zlog.Info(ctx, "Starting Apple Sign In process")

	payload, err := s.VerifyAppleIdentityToken(input.IdentityToken)
	if err != nil {
		zlog.Error(ctx, "Failed to verify Apple identity token", err)
		return nil, fmt.Errorf("failed to verify Apple identity token: %w", err)
	}

	// Extract email from the payload
	email, ok := payload["email"].(string)
	if !ok {
		zlog.Error(ctx, "Email not found in Apple identity token", fmt.Errorf("invalid email"))
		return nil, fmt.Errorf("email not found in Apple identity token")
	}

	// Check if user exists
	user, err := s.GetUserByEmail(ctx, email)
	if err != nil && err != mongo.ErrNoDocuments {
		zlog.Error(ctx, "Error fetching user by email", err, zap.String("email", email))
		return nil, fmt.Errorf("error fetching user by email: %w", err)
	}

	isSignup := false
	if user == nil {
		isSignup = true
		// User doesn't exist, create a new one
		zlog.Info(ctx, "Creating new user for Apple Sign In", zap.String("email", email))

		name := s.generateNameFromAppleInput(input.FullName)
		username, err := s.GenerateUserName(ctx, name)
		if err != nil {
			zlog.Error(ctx, "Error generating username", err)
			return nil, fmt.Errorf("error generating username: %w", err)
		}

		user = &models.User{
			ID:       primitive.NewObjectID(),
			Email:    &email,
			Name:     &name,
			Username: username,
			Rating:   utils.AllocPtr(constants.DefaultRating),
			RatingV2: &models.UserRating{
				GlobalRating: utils.AllocPtr(constants.DefaultRating),
			},
			IsGuest:   utils.AllocPtr(false),
			CreatedAt: utils.AllocPtr(time.Now().UTC()),
			UpdatedAt: utils.AllocPtr(time.Now().UTC()),
			IsSignup:  isSignup,
		}

		err = s.userRepo.Create(ctx, user)
		if err != nil {
			zlog.Error(ctx, "Failed to create new user", err, zap.String("email", email))
			return nil, fmt.Errorf("failed to create new user: %w", err)
		}
	} else {
		zlog.Info(ctx, "Existing user found for Apple Sign In", zap.String("email", email))
	}

	if user == nil {
		return nil, fmt.Errorf("something went wrong")
	}

	// Generate token
	token, err := s.authService.GenerateToken(user.ID)
	if err != nil {
		zlog.Error(ctx, "Failed to generate token", err)
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}
	user.Token = &token

	zlog.Info(ctx, "Apple Sign In successful", zap.String("email", email), zap.String("userId", user.ID.Hex()))
	return user, nil
}

func (s *service) generateNameFromAppleInput(fullName *models.AppleSignInFullNameInput) string {
	if fullName == nil {
		return "Apple User"
	}

	var nameParts []string
	if fullName.GivenName != nil {
		nameParts = append(nameParts, *fullName.GivenName)
	}
	if fullName.MiddleName != nil {
		nameParts = append(nameParts, *fullName.MiddleName)
	}
	if fullName.FamilyName != nil {
		nameParts = append(nameParts, *fullName.FamilyName)
	}

	if len(nameParts) == 0 {
		return "Apple User"
	}

	return strings.Join(nameParts, " ")
}

const (
	applePublicKeysURL = "https://appleid.apple.com/auth/keys"
	appClientId        = "com.matiks.app"
)

type ApplePublicKey struct {
	Kty string `json:"kty"`
	Kid string `json:"kid"`
	Use string `json:"use"`
	Alg string `json:"alg"`
	N   string `json:"n"`
	E   string `json:"e"`
}

type ApplePublicKeys struct {
	Keys []ApplePublicKey `json:"keys"`
}

func (s *service) VerifyAppleIdentityToken(identityToken string) (map[string]interface{}, error) {
	// Parse the token
	token, err := jwt.Parse(identityToken, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		kid, ok := token.Header["kid"].(string)
		if !ok {
			return nil, fmt.Errorf("kid header not found")
		}

		publicKey, err := getApplePublicKey(kid)
		if err != nil {
			return nil, fmt.Errorf("Error while getting apple public key: %w", err)
		}

		return publicKey, nil
	})
	if err != nil {
		return nil, fmt.Errorf("failed to parse Apple identity token: %w", err)
	}

	// Verify the token
	if !token.Valid {
		return nil, fmt.Errorf("invalid Apple identity token")
	}

	// Validate claims
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("invalid token claims")
	}

	if err := validateTokenClaims(claims); err != nil {
		return nil, err
	}

	return claims, nil
}

func getApplePublicKey(kid string) (*rsa.PublicKey, error) {
	resp, err := http.Get(applePublicKeysURL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch Apple public keys: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to fetch keys, status: %v", resp.StatusCode)
	}

	var keys ApplePublicKeys
	if err := json.NewDecoder(resp.Body).Decode(&keys); err != nil {
		return nil, fmt.Errorf("failed to decode Apple public keys: %w", err)
	}

	var publicKey *rsa.PublicKey
	for _, key := range keys.Keys {
		if key.Kid == kid {
			publicKey, err = jwkToRSAPublicKey(key.N, key.E)
			if err != nil {
				return nil, fmt.Errorf("error converting JWK to RSA public key: %w", err)
			}
			return publicKey, nil
			// return jwt.ParseRSAPublicKeyFromPEM([]byte(fmt.Sprintf("-----BEGIN PUBLIC KEY-----\n%s\n-----END PUBLIC KEY-----", key.N)))
		}
	}

	return nil, fmt.Errorf("public key not found for kid: %s", kid)
}

func validateTokenClaims(claims jwt.MapClaims) error {
	// Validate issuer
	iss, ok := claims["iss"].(string)
	if !ok || iss != "https://appleid.apple.com" {
		return fmt.Errorf("invalid issuer")
	}

	// Validate audience (your app's client ID)
	aud, ok := claims["aud"].(string)
	if !ok || aud != appClientId {
		return fmt.Errorf("invalid audience")
	}

	// Validate expiration time
	exp, ok := claims["exp"].(float64)
	if !ok || float64(time.Now().Unix()) > exp {
		return fmt.Errorf("token has expired")
	}

	return nil
}

func jwkToRSAPublicKey(nStr, eStr string) (*rsa.PublicKey, error) {
	// Decode the modulus (n) from Base64URL
	nBytes, err := base64.RawURLEncoding.DecodeString(nStr)
	if err != nil {
		return nil, fmt.Errorf("failed to decode modulus: %w", err)
	}
	n := new(big.Int).SetBytes(nBytes)

	// Decode the exponent (e) from Base64URL
	eBytes, err := base64.RawURLEncoding.DecodeString(eStr)
	if err != nil {
		return nil, fmt.Errorf("failed to decode exponent: %w", err)
	}
	if len(eBytes) > 4 {
		return nil, errors.New("exponent too large")
	}
	e := 0
	for _, b := range eBytes {
		e = e<<8 + int(b)
	}

	// Create the RSA public key
	return &rsa.PublicKey{N: n, E: e}, nil
}
