package user

import (
	"context"
	"fmt"
	"math"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

const WeeklyLeagueMaxGroupSize = 30

func (s *service) AssignUserToLeagueGroup(ctx context.Context, userLeague models.LeagueInfo) error {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return err
	}

	if userLeague.League == nil {
		return fmt.Errorf("league type not specified")
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"league.league": *userLeague.League,
		}}},
		{{Key: "$project", Value: bson.M{
			"_id":           1,
			"leagueGroupID": "$league.groupId",
		}}},
	}

	cursor, err := s.userRepo.AggregateProjected(ctx, pipeline, options.Aggregate().SetAllowDiskUse(true))
	if err != nil {
		return fmt.Errorf("failed to query users in league: %w", err)
	}
	defer cursor.Close(ctx)

	var leagueUsers []struct {
		UserID  primitive.ObjectID `bson:"_id"`
		GroupID *int               `bson:"leagueGroupID"`
	}

	if err := cursor.All(ctx, &leagueUsers); err != nil {
		return fmt.Errorf("failed to decode league users: %w", err)
	}

	leagueSize := len(leagueUsers)
	numGroups := int(math.Ceil(float64(leagueSize) / float64(WeeklyLeagueMaxGroupSize)))
	if numGroups <= 0 {
		numGroups = 1
	}

	groupCounts := make([]int, numGroups)
	for _, u := range leagueUsers {
		if u.GroupID != nil && *u.GroupID > 0 && *u.GroupID <= numGroups {
			groupCounts[*u.GroupID-1]++
		}
	}

	minGroup := 0
	for g := 1; g < numGroups; g++ {
		if groupCounts[g] < groupCounts[minGroup] {
			minGroup = g
		}
	}
	groupID := minGroup + 1

	if groupCounts[minGroup] >= WeeklyLeagueMaxGroupSize {
		groupID = len(groupCounts) + 1
	}

	userLeague.GroupID = utils.AllocPtr(groupID)
	userLeague.UpdatedAt = utils.AllocPtr(time.Now().UTC())

	update := bson.M{
		"$set": bson.M{
			"league": userLeague,
		},
	}

	err = s.userRepo.UpdateOne(ctx, bson.M{"_id": userID}, update)
	if err != nil {
		return fmt.Errorf("failed to update user's league group: %w", err)
	}

	return nil
}
