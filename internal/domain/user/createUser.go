package user

import (
	"context"
	"errors"
	"fmt"

	"go.mongodb.org/mongo-driver/mongo"
	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) CreateUser(ctx context.Context, input *models.UserInput) (*models.User, error) {
	zlog.Info(ctx, "Starting user creation process", zap.String("email", input.Email))

	if input.Password != input.Confirm {
		zlog.Warn(ctx, "Password mismatch during user creation", zap.String("email", input.Email))
		return nil, fmt.Errorf("passwords do not match")
	}

	existingUser, err := s.GetUserByEmail(ctx, input.Email)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		zlog.Error(ctx, "Error checking for existing user", err, zap.String("email", input.Email))
		return nil, err
	}
	if existingUser != nil {
		return nil, fmt.Errorf("email already in use")
	}

	hashedPassword, err := s.authService.HashPassword(input.Password)
	if err != nil {
		zlog.Error(ctx, "Failed to hash password", err)
		return nil, err
	}

	user := &models.User{
		ID:       primitive.NewObjectID(),
		Email:    &input.Email,
		Name:     &input.Name,
		Password: &hashedPassword,
		Rating:   utils.AllocPtr(constants.DefaultRating),
	}

	user.RatingV2 = gameutils.GetUserDefaultRating(user)

	err = s.userRepo.Create(ctx, user)
	if err != nil {
		zlog.Error(ctx, "Failed to insert user", err)
		return nil, err
	}

	token, err := s.authService.GenerateToken(user.ID)
	if err != nil {
		zlog.Error(ctx, "Failed to generate token", err)
		return nil, err
	}

	user.Token = &token
	zlog.Info(ctx, "User created successfully", zap.String("email", input.Email), zap.String("userId", user.ID.Hex()))
	return user, nil
}
