package user

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
)

func (s *service) DeleteUser(ctx context.Context) (bool, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	userInfo, err := s.userRepo.GetByID(ctx, userId)
	if err != nil || userInfo == nil {
		return false, err
	}

	_, err = s.deletedUserRepo.InsertOne(ctx, userInfo)
	if err != nil {
		return false, err
	}

	// Instead of Deleting user I am setting their email as nil
	err = s.userRepo.UpdateOne(ctx, bson.M{"_id": userId}, bson.M{"$set": bson.M{
		"email":           nil,
		"name":            nil,
		"profileImageUrl": nil,
		"isDeleted":       true,
	}})
	if err != nil {
		return false, err
	}

	return true, nil
}
