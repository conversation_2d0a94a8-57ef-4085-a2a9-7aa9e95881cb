package user

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"time"

	userUtils "matiksOfficial/matiks-server-go/internal/domain/user/utils"

	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/user/humanBots"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/infrastructure/list"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) OnlineUsers(ctx context.Context, page, pageSize int) (*models.OnlineUsersPage, error) {
	if page < 1 {
		page = 1
	}

	if pageSize < 1 || pageSize > 200 {
		pageSize = DefaultPageSize
	}

	users, totalCount, err := s.getOnlineUsers(ctx, page, pageSize)
	if err != nil {
		return nil, err
	}

	hasMore := (page * pageSize) < totalCount

	return &models.OnlineUsersPage{
		Users:        users,
		PageNumber:   page,
		PageSize:     pageSize,
		HasMore:      &hasMore,
		TotalResults: &totalCount,
	}, nil
}

func (s *service) getOnlineUsers(ctx context.Context, page, pageSize int) ([]*models.UserDetailWithActivity, int, error) {
	users, countUsers, err := s.getOnlineUsersFromRedis(ctx, page, pageSize)
	if err != nil {
		return nil, 0, err
	}
	var humanBots []*models.UserDetailWithActivity
	var countBots int
	if page*pageSize > countUsers {
		remaining := page*pageSize - countUsers
		if (remaining / pageSize) == 0 {
			humanBots, countBots, err = s.getHumanBots(ctx, 0, remaining)
			if err != nil {
				return nil, 0, err
			}
		} else {
			skip := (remaining - 1) / pageSize
			humanBots, countBots, err = s.getHumanBots(ctx, skip*pageSize, pageSize)
			if err != nil {
				return nil, 0, err
			}
		}
	}

	users = append(users, humanBots...)
	return users, countUsers + countBots, nil
}

func (s *service) getOnlineUsersFromRedis(ctx context.Context, page, pageSize int) ([]*models.UserDetailWithActivity, int, error) {
	currentUserID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, 0, err
	}

	key := "online_users"
	now := time.Now().Unix()

	totalOnlineCount, err := s.redisCache.ZCount(ctx, key, fmt.Sprintf("%d", now-30), fmt.Sprintf("%d", now))
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, 0, err
	}

	if totalOnlineCount == 0 {
		return []*models.UserDetailWithActivity{}, 0, nil
	}

	bufferMultiplier := 2
	bufferedPageSize := pageSize * bufferMultiplier

	var eligibleUsers []*models.UserDetailWithActivity
	var processedCount int64 = 0
	var currentOffset int64 = 0

	for len(eligibleUsers) < pageSize && processedCount < totalOnlineCount {
		results, err := s.redisCache.ZRangeByScore(ctx, key, &redis.ZRangeBy{
			Min:    fmt.Sprintf("%d", now-30),
			Max:    fmt.Sprintf("%d", math.MaxInt64),
			Offset: currentOffset,
			Count:  int64(bufferedPageSize),
		})
		if err != nil && !errors.Is(err, redis.Nil) {
			return nil, 0, err
		}

		if len(results) == 0 {
			break
		}

		processedCount += int64(len(results))
		currentOffset += int64(len(results))

		for _, z := range results {
			var activityData struct {
				UserID       string                  `json:"userId"`
				LastActivity models.UserActivityType `json:"lastActivity"`
				Timestamp    int64                   `json:"timestamp"`
			}

			if err := json.Unmarshal([]byte(z), &activityData); err != nil {
				zlog.Error(ctx, "getOnlineUsersFromRedis: Error unmarshalling user activity data", err, zap.String("userID", activityData.UserID))
				continue
			}

			userId, err := primitive.ObjectIDFromHex(activityData.UserID)
			if err != nil {
				zlog.Error(ctx, "getOnlineUsersFromRedis: Invalid user ID", err, zap.String("userID", activityData.UserID))
				continue
			}

			if userId == currentUserID {
				continue
			}

			userDetails, err := s.GetUserByID(ctx, userId)
			if err != nil {
				zlog.Error(ctx, "getOnlineUsersFromRedis: error Getting User By user ID", err, zap.String("userID", activityData.UserID))
				continue
			}

			if !userUtils.IsEligibleToViewOnlineUsers(*userDetails) {
				continue
			}

			eligibleUsers = append(eligibleUsers, &models.UserDetailWithActivity{
				UserInfo:     utils.GetMinifiedUserDetails(userDetails),
				CurrActivity: utils.AllocPtr(activityData.LastActivity),
			})
		}
	}

	var estimatedTotalEligible int
	if processedCount > 0 {
		eligibleRatio := float64(len(eligibleUsers)) / float64(processedCount)
		estimatedTotalEligible = int(math.Ceil(float64(totalOnlineCount) * eligibleRatio))
	} else {
		estimatedTotalEligible = 0
	}

	start := (page - 1) * pageSize
	end := start + pageSize

	if start >= len(eligibleUsers) {
		return []*models.UserDetailWithActivity{}, estimatedTotalEligible, nil
	}

	if end > len(eligibleUsers) {
		end = len(eligibleUsers)
	}

	return eligibleUsers[start:end], estimatedTotalEligible, nil
}

func (s *service) getHumanBots(ctx context.Context, skip, limit int) ([]*models.UserDetailWithActivity, int, error) {
	bots := make([]*models.UserDetailWithActivity, 0, limit)
	_, err := s.list.LLen(ctx, constants.ActiveBotsKey)
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			return nil, 0, err
		} else {
			err := s.list.LTrim(ctx, constants.ActiveBotsKey, -1, 0)
			if err != nil {
				return nil, 0, err
			}

			fetchedBotIDs, err := fetchActiveBotIDs(ctx, s.userRepo, s.redisCache)
			if err != nil {
				return nil, 0, err
			}

			if len(fetchedBotIDs) == 0 {
				return nil, 0, nil
			}

			err = s.list.LPush(ctx, constants.ActiveBotsKey, utils.ToInterfaceSlice(fetchedBotIDs)...)
			if err != nil {
				return nil, 0, err
			}
		}
	}

	ok, err := checkBotsStatusInCache(ctx, s.list, s.redisCache)
	if err != nil {
		return nil, 0, err
	}

	if !ok {
		err := s.list.LTrim(ctx, constants.ActiveBotsKey, -1, 0)
		if err != nil {
			return nil, 0, err
		}

		fetchedBotIDs, err := fetchActiveBotIDs(ctx, s.userRepo, s.redisCache)
		if err != nil {
			return nil, 0, err
		}

		if len(fetchedBotIDs) == 0 {
			return nil, 0, nil
		}

		err = s.list.LPush(ctx, constants.ActiveBotsKey, utils.ToInterfaceSlice(fetchedBotIDs)...)
		if err != nil {
			return nil, 0, err
		}
	}
	count, err := s.list.LLen(ctx, constants.ActiveBotsKey)
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, 0, err
	}

	data, err := s.list.LRange(ctx, constants.ActiveBotsKey, int64(skip), int64(skip+limit))
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, 0, err
	}

	for _, z := range data {
		var botID primitive.ObjectID
		if err := json.Unmarshal([]byte(z), &botID); err != nil {
			return nil, 0, err
		}

		botData, err := s.redisCache.Get(ctx, constants.HumanBotKey+botID.Hex())
		if err != nil && !errors.Is(err, redis.Nil) {
			return nil, 0, err
		}

		var bot *models.User
		if err := json.Unmarshal(botData, &bot); err != nil {
			return nil, 0, err
		}

		currActivity := utils.AllocPtr(models.Exploring)
		if bot.HumanBotConfig.InGame != nil && *bot.HumanBotConfig.InGame {
			currActivity = utils.AllocPtr(models.InGame)
		}

		bots = append(bots, &models.UserDetailWithActivity{
			UserInfo:     utils.GetMinifiedUserDetails(bot),
			CurrActivity: currActivity,
		})
	}

	return bots, int(count), nil
}

func fetchActiveBotIDs(ctx context.Context, userRepo repository.UserRepository, cache cache.Cache) ([]primitive.ObjectID, error) {
	botIDs := make([]primitive.ObjectID, 0, 20)
	groupedHumanBots, err := humanBots.GetHumanBotsGrouped(userRepo, cache)
	if err != nil {
		return nil, err
	}

	currentHour := time.Now().Hour()
	for _, humanBotsGroup := range groupedHumanBots {
		for _, bot := range humanBotsGroup.HumanBots {
			if bot.HumanBotConfig != nil &&
				bot.HumanBotConfig.ActiveStartTime < currentHour &&
				(bot.HumanBotConfig.ActiveEndTime >= currentHour || bot.HumanBotConfig.ActiveEndTime == 0) {
				botIDs = append(botIDs, bot.ID)
			}
		}
	}

	return botIDs, nil
}

func checkBotsStatusInCache(ctx context.Context, list list.List, cache cache.Cache) (bool, error) {
	dataStr, err := list.LIndex(ctx, constants.ActiveBotsKey, 0)
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return false, nil
		}
		return false, err
	}
	var botID primitive.ObjectID
	err = json.Unmarshal([]byte(dataStr), &botID)
	if err != nil {
		return false, err
	}

	data, err := cache.Get(ctx, constants.HumanBotKey+botID.Hex())
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return false, nil
		}
		return false, err
	}

	var bot *models.User
	err = json.Unmarshal(data, &bot)
	if err != nil {
		return false, err
	}

	currentHour := time.Now().Hour()
	if bot.HumanBotConfig != nil &&
		bot.HumanBotConfig.ActiveStartTime < currentHour &&
		(bot.HumanBotConfig.ActiveEndTime >= currentHour || bot.HumanBotConfig.ActiveEndTime == 0) {
		return true, nil
	}
	return false, nil
}
