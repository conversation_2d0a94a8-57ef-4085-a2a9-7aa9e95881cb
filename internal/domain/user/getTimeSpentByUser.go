package user

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
)

func (s *service) GetTimeSpentByUser(ctx context.Context, date *string) (int, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return 0, err
	}

	if date == nil {
		return 0, fmt.Errorf("Date Can't be empty")
	}

	// Try to get time spent from the new userDailyActivity repository first
	timeSpent, err := s.userDailyActivityRepo.GetTimeSpentByUser(ctx, userId, *date)

	// If not found in the new repository, try the old one
	if err != nil || timeSpent == nil {
		return 0, err
	}

	return int(*timeSpent), nil
}
