package user

import (
	"context"
	"fmt"
	"math"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	weeklyLeagueUtils "matiksOfficial/matiks-server-go/internal/domain/weeklyLeague/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (s *service) GetUserLeagueGroupLeaderboard(ctx context.Context, page, pageSize *int) (*models.WeeklyLeagueLeaderboardPage, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	user, err := s.GetUserByID(ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("no user found for ID %v", userId)
	}

	if user == nil || user.League == nil || user.League.GroupID == nil {
		return nil, fmt.Errorf("user has not in any league group yet")
	}

	if user.League.League == nil {
		return nil, fmt.Errorf("user has not joined any league group yet")
	}

	if page == nil {
		page = utils.AllocPtr(1)
	}
	if pageSize == nil {
		pageSize = utils.AllocPtr(30)
	}

	groupID := *user.League.GroupID
	league := *user.League.League

	skip := (*page - 1) * *pageSize

	currentWeekStart := weeklyLeagueUtils.GetWeekStart(time.Now().UTC())
	currentWeekEnd := weeklyLeagueUtils.GetWeekEnd(currentWeekStart)

	promotionRate, demotionRate := weeklyLeagueUtils.GetPromotionDemotionRates(league)

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"league.league":  league,
			"league.groupId": groupID,
		}}},

		{{Key: "$project", Value: bson.M{
			"_id":             1,
			"name":            1,
			"username":        1,
			"profileImageUrl": 1,
			"rating":          1,
			"badge":           1,
			"globalRank":      1,
		}}},

		{{Key: "$lookup", Value: bson.M{
			"from": "user_activities",
			"let":  bson.M{"userId": "$_id"},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{
					"$expr": bson.M{
						"$and": bson.A{
							bson.M{"$eq": bson.A{"$userId", "$$userId"}},
							bson.M{"$gte": bson.A{"$timestamp", currentWeekStart}},
							bson.M{"$lte": bson.A{"$timestamp", currentWeekEnd}},
						},
					},
				}},
				bson.M{"$group": bson.M{
					"_id":         "$userId",
					"statikCoins": bson.M{"$sum": "$xpEarned"},
				}},
			},
			"as": "weeklyCoins",
		}}},

		{{Key: "$unwind", Value: bson.M{
			"path":                       "$weeklyCoins",
			"preserveNullAndEmptyArrays": true,
		}}},

		{{Key: "$addFields", Value: bson.M{
			"statikCoins": bson.M{
				"$ifNull": bson.A{"$weeklyCoins.statikCoins", 0},
			},
		}}},

		{{Key: "$sort", Value: bson.M{"statikCoins": -1}}},

		{{Key: "$facet", Value: bson.M{
			"metadata": bson.A{bson.M{"$count": "total"}},
			"data": bson.A{
				bson.M{"$skip": skip},
				bson.M{"$limit": *pageSize},
			},
		}}},
		{{Key: "$project", Value: bson.M{
			"results": bson.M{
				"$map": bson.M{
					"input": "$data",
					"as":    "result",
					"in": bson.M{
						"user": bson.M{
							"_id":             "$$result._id",
							"name":            "$$result.name",
							"username":        "$$result.username",
							"profileImageUrl": "$$result.profileImageUrl",
							"rating":          "$$result.rating",
							"badge":           "$$result.badge",
							"globalRank":      "$$result.globalRank",
						},
						"statikCoins": "$$result.statikCoins",
						"rank": bson.M{
							"$add": bson.A{skip, bson.M{"$indexOfArray": bson.A{"$data", "$$result"}}, 1},
						},
					},
				},
			},
			"pageNumber": bson.M{"$literal": page},
			"pageSize":   bson.M{"$literal": *pageSize},
			"totalResults": bson.M{"$ifNull": bson.A{
				bson.M{"$arrayElemAt": bson.A{"$metadata.total", 0}},
				0,
			}},
		}}},
	}

	cursor, err := s.userRepo.AggregateProjected(
		ctx,
		pipeline,
		options.Aggregate().
			SetMaxTime(30*time.Second).
			SetAllowDiskUse(true),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to execute league leaderboard pipeline: %w", err)
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err := cursor.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("failed to decode league leaderboard results: %w", err)
	}

	if len(results) == 0 || results[0]["results"] == nil {
		return &models.WeeklyLeagueLeaderboardPage{
			Results:           []*models.WeeklyLeagueLeaderboardEntry{},
			PageNumber:        *page,
			PageSize:          *pageSize,
			HasMore:           utils.AllocPtr(false),
			TotalResults:      utils.AllocPtr(0),
			CurrentUserLeague: user.League,
		}, nil
	}

	resultData := results[0]
	totalResults := utils.ToInt(resultData["totalResults"])
	hasMore := totalResults > (skip + *pageSize)

	leaderboardEntries := make([]*models.WeeklyLeagueLeaderboardEntry, 0)
	if resultsArr, ok := resultData["results"].(bson.A); ok {
		for _, result := range resultsArr {
			if resultDoc, ok := result.(bson.M); ok {
				if user, ok := resultDoc["user"].(bson.M); ok {
					entry := &models.WeeklyLeagueLeaderboardEntry{
						User:        utils.ExtractUserPublicDetails(user),
						StatikCoins: utils.AllocPtr(utils.ToInt(resultDoc["statikCoins"])),
						Rank:        utils.AllocPtr(utils.ToInt(resultDoc["rank"])),
					}
					leaderboardEntries = append(leaderboardEntries, entry)
				}
			}
		}
	}

	promotionCount := int(math.Ceil(float64(totalResults) * promotionRate))
	if promotionCount < 1 {
		promotionCount = 1
	}

	demotionThresold := int(math.Floor(float64(totalResults) * demotionRate))
	if demotionThresold < 1 {
		demotionThresold = 1
	}

	for _, entry := range leaderboardEntries {
		if entry == nil {
			continue
		}
		entry.ProgressState = utils.AllocPtr(models.WeeklyLeagueProgressStateNoChange)
		if entry.Rank != nil && *entry.Rank <= promotionCount {
			entry.ProgressState = utils.AllocPtr(models.WeeklyLeagueProgressStatePromotion)
			continue
		}
		if entry.Rank != nil && *entry.Rank > demotionThresold {
			entry.ProgressState = utils.AllocPtr(models.WeeklyLeagueProgressStateDemotion)
			continue
		}
	}

	return &models.WeeklyLeagueLeaderboardPage{
		Results:           leaderboardEntries,
		PageNumber:        *page,
		PageSize:          *pageSize,
		HasMore:           utils.AllocPtr(hasMore),
		TotalResults:      utils.AllocPtr(totalResults),
		CurrentUserLeague: user.League,
	}, nil
}
