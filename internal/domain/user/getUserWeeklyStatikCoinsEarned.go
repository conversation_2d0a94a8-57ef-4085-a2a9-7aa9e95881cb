package user

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	weeklyLeagueUtils "matiksOfficial/matiks-server-go/internal/domain/weeklyLeague/utils"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (s *service) GetUsersWeeklyStatikCoins(ctx context.Context) (int, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return 0, nil
	}

	currentWeekStart := weeklyLeagueUtils.GetWeekStart(time.Now().UTC())
	currentWeekEnd := weeklyLeagueUtils.GetWeekEnd(currentWeekStart)

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"userId": userId,
			"date": bson.M{
				"$gte": currentWeekStart,
				"$lte": currentWeekEnd,
			},
			"statikCoinsEarned": bson.M{"$gt": 0},
		}}},
	}

	// Try to get data from the new userDailyActivity repository first
	activities, err := s.userDailyActivityRepo.Aggregate(
		ctx,
		pipeline,
		options.Aggregate().SetMaxTime(10*time.Second),
	)
	if err != nil {
		return 0, fmt.Errorf("failed to execute weekly coins aggregation pipeline: %w", err)
	}

	if len(activities) == 0 {
		return 0, nil
	}

	totalCoins := 0
	for _, activity := range activities {
		if activity != nil {
			totalCoins += activity.StatikCoinsEarned
		}
	}

	return totalCoins, nil
}
