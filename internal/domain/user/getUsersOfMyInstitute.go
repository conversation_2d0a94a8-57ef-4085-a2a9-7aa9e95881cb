package user

import (
	"context"
	"errors"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

func (s *service) GetUsersOfMyInstitute(ctx context.Context, page, pageSize *int) (*models.MyInstituteUsersPage, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	currentUser, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get current user details", err, zap.String("userID", userID.Hex()))
		return nil, errors.New("failed to retrieve user information")
	}
	if currentUser == nil {
		return nil, errors.New("user not found")
	}

	if currentUser.InstitutionID == nil {
		return &models.MyInstituteUsersPage{
			Results:      []*models.UserPublicDetails{},
			PageNumber:   1,
			PageSize:     0,
			HasMore:      false,
			TotalResults: 0,
		}, nil
	}

	filter := bson.M{
		"institutionId": currentUser.InstitutionID,
		"_id":           bson.M{"$ne": userID},
		"isGuest":       false,
	}

	var currentPage int64 = 1
	var currentPageSize int64 = DefaultPageSize

	if page != nil && *page > 0 {
		currentPage = int64(*page)
	}
	if pageSize != nil && *pageSize > 0 {
		currentPageSize = int64(*pageSize)
	}

	skip := (currentPage - 1) * currentPageSize
	limit := currentPageSize

	opts := options.Find().
		SetSort(bson.D{{Key: "username", Value: 1}}).
		SetSkip(skip).
		SetLimit(limit)

	users, err := s.userRepo.Find(ctx, filter, opts)
	if err != nil {
		zlog.Error(ctx, "Failed to find paginated users by institute ID", err,
			zap.String("institutionID", currentUser.InstitutionID.Hex()),
			zap.String("userID", userID.Hex()),
		)
		return nil, errors.New("failed to retrieve users from your institute")
	}

	publicDetails := make([]*models.UserPublicDetails, len(users))
	for i, user := range users {
		publicDetails[i] = utils.GetUserPublicDetails(user)
	}

	totalCount, countErr := s.userRepo.CountDocuments(ctx, filter)
	if countErr != nil {
		zlog.Error(ctx, "Failed to count users by institute ID", countErr,
			zap.String("institutionID", currentUser.InstitutionID.Hex()),
			zap.String("userID", userID.Hex()),
		)

		totalCount = 0
	}

	hasMore := false
	if totalCount > 0 {
		hasMore = (skip + limit) < totalCount
	}

	return &models.MyInstituteUsersPage{
		Results:      publicDetails,
		PageNumber:   currentPage,
		PageSize:     currentPageSize,
		HasMore:      hasMore,
		TotalResults: totalCount,
	}, nil
}
