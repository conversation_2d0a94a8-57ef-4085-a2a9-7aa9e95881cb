package user

import (
	"context"
	"fmt"
	"time"

	weeklyLeagueUtils "matiksOfficial/matiks-server-go/internal/domain/weeklyLeague/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

func (s *service) GetUsersWeeklyStatikCoinsV2(ctx context.Context, userId primitive.ObjectID) (*models.UsersWeeklyStatikCoinsOutput, error) {
	zlog.Info(ctx, "Getting User Weekly Statik Coins for", zap.String("userID: ", userId.Hex()))
	currentWeekStart := weeklyLeagueUtils.GetWeekStart(time.Now().UTC())
	currentWeekEnd := weeklyLeagueUtils.GetWeekEnd(currentWeekStart)

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"userId": userId,
			"date": bson.M{
				"$gte": currentWeekStart,
				"$lte": currentWeekEnd,
			},
			"statikCoinsEarned": bson.M{"$gt": 0},
		}}},
	}

	activities, err := s.userDailyActivityRepo.Aggregate(
		ctx,
		pipeline,
		options.Aggregate().SetMaxTime(10*time.Second),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to execute weekly coins aggregation pipeline: %w", err)
	}

	totalCoins := 0
	dailyCoins := make([]int, 7)
	activityMap := make(map[string]int)

	for _, activity := range activities {
		if activity != nil {
			if !activity.Date.IsZero() {
				dateStr := activity.Date.Format("2006-01-02")
				activityMap[dateStr] += activity.StatikCoinsEarned
				totalCoins += activity.StatikCoinsEarned
			}
		}
	}

	for i := 0; i < 7; i++ {
		currentDay := currentWeekStart.AddDate(0, 0, i)
		dateStr := currentDay.Format("2006-01-02")
		dailyCoins[i] = activityMap[dateStr]
	}

	response := &models.UsersWeeklyStatikCoinsOutput{
		TotalCoins: totalCoins,
		DailyCoins: dailyCoins,
	}

	return response, nil
}
