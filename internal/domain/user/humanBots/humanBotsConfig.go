package humanBots

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"strconv"
	"strings"
	"time"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/models"
)

type humanBotsConfig struct {
	Name         string `json:"name"`
	TargetRating string `json:"targetRating"`
	Rating       string `json:"rating"`
}

func GetHumanBotsFromConfig(userService domain.UserStore, userRepo repository.UserRepository) ([]*models.User, error) {
	file, err := os.Open("internal/domain/user/humanBots/humanBotsConfig.json")
	if err != nil {
		zlog.Error(context.Background(), "Error opening file", err)
		return nil, err
	}
	defer file.Close()

	byteValue, err := io.ReadAll(file)
	if err != nil {
		zlog.Error(context.Background(), "Error reading file", err)
		return nil, err
	}

	var humanBotsData []humanBotsConfig
	err = json.Unmarshal(byteValue, &humanBotsData)
	if err != nil {
		zlog.Error(context.Background(), "Error unmarshaling JSON", err)
		return nil, err
	}

	var humanBots []*models.User

	for _, humanBot := range humanBotsData {
		var u models.User
		targetRating, err := strconv.Atoi(humanBot.TargetRating)
		if err != nil {
			return nil, err
		}
		rating, err := strconv.Atoi(humanBot.Rating)
		if err != nil {
			return nil, err
		}
		u.ID = primitive.NewObjectID()
		u.IsHumanBot = utils.AllocPtr(true)
		u.Name = utils.AllocPtr(humanBot.Name)
		u.HumanBotConfig = &models.HumanBotConfig{
			TargetRating: targetRating,
			InGame:       utils.AllocPtr(false),
		}
		u.Rating = utils.AllocPtr(rating)
		u.Username, err = userService.GenerateUserName(context.Background(), humanBot.Name)
		if err != nil {
			return nil, err
		}
		initial := strings.ToUpper(u.Username[0:1])
		u.ProfileImageURL = utils.AllocPtr(fmt.Sprintf("https://storage.googleapis.com/matiks-go/%s.png", initial))
		u.IsBot = utils.AllocPtr(false)
		u.IsGuest = utils.AllocPtr(false)
		u.CreatedAt = utils.AllocPtr(time.Now())
		u.UpdatedAt = utils.AllocPtr(time.Now())

		higherRankedPlayers, err := userService.CountUsersWithHigherRating(context.TODO(), userRepo, constants.DefaultRating)
		if err != nil {
			return nil, fmt.Errorf("failed to count higher ranked players: %w", err)
		}
		globalRank := higherRankedPlayers + 1
		u.GlobalRank = utils.AllocPtr(globalRank)

		humanBots = append(humanBots, &u)
	}
	assignTimesToHumanBots(humanBots)

	for _, bot := range humanBots {
		err := userService.UpdateUserFromObject(context.TODO(), bot)
		if err != nil {
			return nil, err
		}
	}

	return humanBots, nil
}

func assignTimesToHumanBots(bots []*models.User) {
	targetRatingGroups := make(map[int][]*models.User)
	for _, bot := range bots {
		if bot.HumanBotConfig != nil {
			targetRating := bot.HumanBotConfig.TargetRating
			targetRatingGroups[targetRating] = append(targetRatingGroups[targetRating], bot)
		}
	}

	for _, bots := range targetRatingGroups {
		activeTime := 0
		for _, bot := range bots {
			bot.HumanBotConfig.ActiveStartTime = activeTime
			activeTime = (activeTime + 3) % 24

			bot.HumanBotConfig.ActiveEndTime = activeTime
		}
	}
}

type GroupedHumanBotIDs struct {
	TargetRating int               `json:"targetRating"`
	HumanBotIDs  []models.ObjectID `json:"humanBotIDs"`
}

func GetHumanBotsGrouped(userRepo repository.UserRepository, cache cache.Cache) ([]*models.GroupedHumanBots, error) {
	var humanBotIDsData []GroupedHumanBotIDs
	var groupedHumanBots []*models.GroupedHumanBots
	data, err := cache.Get(context.TODO(), constants.GroupedHumanBotsKey)
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}
	if err == nil && data != nil {
		err := json.Unmarshal(data, &humanBotIDsData)
		if err != nil {
			return nil, err
		}
	}

	if humanBotIDsData == nil {
		humanBotIDsData, err = getGroupedHumanBotsIDsFromDB(userRepo)
		if err != nil {
			return nil, err
		}

		byteData, err := json.Marshal(humanBotIDsData)
		if err != nil {
			return nil, err
		}
		err = cache.Set(context.TODO(), constants.GroupedHumanBotsKey, byteData, 24*time.Hour)
		if err != nil {
			return nil, err
		}
	}

	humanBotsFromCache := make(map[string]*models.User, 200)
	byteValues, err := cache.GetAll(context.TODO(), constants.HumanBotKey+"*")
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}
	for key, value := range byteValues {
		id := strings.Split(key, ":")[1]
		var u *models.User
		err = json.Unmarshal(value, &u)
		humanBotsFromCache[id] = u
	}

	for _, group := range humanBotIDsData {
		groupedHumanBot := &models.GroupedHumanBots{
			TargetRating: group.TargetRating,
			HumanBots:    make([]*models.User, 0, len(group.HumanBotIDs)),
		}

		for _, id := range group.HumanBotIDs {
			if humanBotsFromCache[id.Hex()] == nil {
				continue
			}
			groupedHumanBot.HumanBots = append(groupedHumanBot.HumanBots, humanBotsFromCache[id.Hex()])
		}

		if len(groupedHumanBot.HumanBots) != len(group.HumanBotIDs) {
			groupedHumanBot.HumanBots = make([]*models.User, 0, len(group.HumanBotIDs))

			filter := bson.M{"_id": bson.M{"$in": group.HumanBotIDs}}
			users, err := userRepo.Find(context.TODO(), filter)
			if err != nil {
				zlog.Error(context.Background(), "Error finding users", err)
			}
			groupedHumanBot.HumanBots = users
			go setHumanBotsInCache(users, cache)
		}
		groupedHumanBots = append(groupedHumanBots, groupedHumanBot)
	}
	return groupedHumanBots, nil
}

func setHumanBotsInCache(users []*models.User, cache cache.Cache) {
	for _, user := range users {
		byteData, _ := json.Marshal(user)
		cache.Set(context.TODO(), constants.HumanBotKey+user.ID.Hex(), byteData, 3*time.Hour)
	}
}

func getGroupedHumanBotsIDsFromDB(userRepo repository.UserRepository) ([]GroupedHumanBotIDs, error) {
	filter := bson.M{"isHumanBot": true}
	humanBots, err := userRepo.Find(context.TODO(), filter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return nil, err
	}
	var groupedHumanBotIDs []GroupedHumanBotIDs
	ratingHumanBotMap := make(map[int][]primitive.ObjectID)
	for _, humanBot := range humanBots {
		ratingHumanBotMap[humanBot.HumanBotConfig.TargetRating] = append(ratingHumanBotMap[humanBot.HumanBotConfig.TargetRating], humanBot.ID)
	}

	for k, v := range ratingHumanBotMap {
		var group GroupedHumanBotIDs
		group.TargetRating = k
		group.HumanBotIDs = v
		groupedHumanBotIDs = append(groupedHumanBotIDs, group)
	}
	return groupedHumanBotIDs, nil
}
