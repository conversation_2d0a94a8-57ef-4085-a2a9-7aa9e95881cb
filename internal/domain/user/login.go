package user

import (
	"context"
	"errors"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"matiksOfficial/matiks-server-go/internal/constants"
	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/domain/userStreak"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/domain/utils/rankingutils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"google.golang.org/api/idtoken"

	"go.mongodb.org/mongo-driver/bson"
)

func (s *service) Login(ctx context.Context, email, password string) (*models.User, error) {
	zlog.Info(ctx, "Logging in user", zap.String("email", email))

	user, err := s.GetUserByEmail(ctx, email)
	if err != nil {
		zlog.Error(ctx, "Error fetching user", err)
		return nil, fmt.Errorf("error fetching user: %w", err)
	}

	if user == nil {
		zlog.Warn(ctx, "User not found", zap.String("email", email))
		return nil, fmt.Errorf("user not found")
	}

	if user.Password == nil {
		zlog.Warn(ctx, "User has no password set", zap.String("email", email))
		return nil, fmt.Errorf("invalid login method")
	}

	if !s.authService.ComparePasswords(*user.Password, password) {
		zlog.Warn(ctx, "Invalid password", zap.String("email", email))
		return nil, fmt.Errorf("invalid password")
	}

	token, err := s.authService.GenerateToken(user.ID)
	if err != nil {
		zlog.Error(ctx, "failed to generate token", err)
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}
	user.Token = &token
	zlog.Info(ctx, "User logged in successfully", zap.String("userID", user.ID.Hex()))
	return user, nil
}

func (s *service) LoginAsGuest(ctx context.Context, guestID models.ObjectID) (*models.User, error) {
	zlog.Info(ctx, "Logging in guest user", zap.String("guestId", guestID.Hex()))

	guestUser, err := s.GetUserByID(ctx, guestID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) && !errors.Is(err, systemErrors.ErrUserNotFound) {
		zlog.Error(ctx, "Error fetching user", err)
		return nil, fmt.Errorf("error fetching user: %w", err)
	}

	isSignup := false
	// If guest user is found and let user login with new guest id
	if guestUser != nil && guestUser.IsGuest != nil && !*guestUser.IsGuest {
		guestID = primitive.NewObjectID()
	}
	if guestUser == nil || !*guestUser.IsGuest {
		isSignup = true
		totalUsersCount, err := s.userRepo.Count(ctx, bson.M{})
		if err != nil {
			zlog.Error(ctx, "Failed to count users", err)
			return nil, fmt.Errorf("failed to count users: %w", err)
		}
		guestUserName := fmt.Sprintf("guest%d", totalUsersCount+1)
		username, err := s.GenerateUserName(ctx, guestUserName)
		if err != nil || username == "" {
			zlog.Error(ctx, "Failed to generate username", err)
			return nil, fmt.Errorf("failed to generate username: %w", err)
		}
		guestUser = &models.User{
			ID:        guestID,
			Name:      &guestUserName,
			Username:  username,
			IsGuest:   utils.AllocPtr(true),
			Rating:    utils.AllocPtr(constants.DefaultRating),
			CreatedAt: utils.AllocPtr(time.Now()),
			UpdatedAt: utils.AllocPtr(time.Now()),
			IsSignup:  isSignup,
		}

		guestUser.RatingV2 = gameutils.GetUserDefaultRating(guestUser)

		err = s.userRepo.Create(ctx, guestUser)
		if err != nil {
			zlog.Error(ctx, "Failed to create guest user", err)
			return nil, fmt.Errorf("failed to create guest user: %w", err)
		}
	}

	token, err := s.authService.GenerateToken(guestUser.ID)
	if err != nil {
		zlog.Error(ctx, "Failed to generate token", err)
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	guestUser.Token = &token
	zlog.Info(ctx, "Guest user logged in successfully", zap.String("userID", guestUser.ID.Hex()))
	return guestUser, nil
}

func (s *service) VerifyToken(ctx context.Context, token string) (*models.User, error) {
	claims, err := s.authService.VerifyToken(token)
	if err != nil {
		zlog.Error(ctx, "Failed to verify token", err)
		return nil, fmt.Errorf("failed to verify token: %w", err)
	}

	decodedID := claims["id"].(string)

	ID, err := primitive.ObjectIDFromHex(decodedID)
	if err != nil {
		zlog.Error(ctx, "Invalid user ID in token", err)
		return nil, fmt.Errorf("invalid user ID in token: %w", err)
	}

	user, err := s.GetUserByID(ctx, ID)
	if err != nil {
		zlog.Error(ctx, "Error fetching user", err)
		return nil, fmt.Errorf("error fetching user: %w", err)
	}
	if user == nil {
		zlog.Warn(ctx, "User not found", zap.String("userID", ID.Hex()))
		return nil, fmt.Errorf("user not found")
	}

	if user.RatingV2 == nil {
		defaultRating := gameutils.GetUserDefaultRating(user)
		user.RatingV2 = defaultRating
		err := s.UpdateUserFromObject(ctx, user)
		if err != nil {
			return user, fmt.Errorf("failed to update user: %w", err)
		}
	}

	user.Token = utils.AllocPtr(token)

	go func() {
		err := userStreak.UpdateUserStreak(utils.DeriveContextWithoutCancel(ctx), user.ID, s.userRepo, false, s.notificationService, s.coreService, s.userStreakRepo)
		if err != nil {
			zlog.Error(ctx, "Failed to update streak", err)
		}
	}()

	return user, nil
}

func (s *service) LegacyGoogleLogin(ctx context.Context, idToken string, guestID *models.ObjectID) (*models.User, error) {
	if err := rankingutils.UpdateRank(false, s.userRepo); err != nil {
		return nil, err
	}
	user, err := s.signInUserWithIDToken(ctx, idToken, guestID)
	if err != nil {
		zlog.Error(ctx, "Failed to sign in user with ID token", err)
		return nil, fmt.Errorf("failed to sign in user with ID token: %w", err)
	}
	return user, nil
}

func (s *service) GoogleLogin(ctx context.Context, authCode string, tokenType, expiresIn *string, guestID *primitive.ObjectID) (*models.User, error) {
	token, err := s.authService.OAuthClient().Exchange(ctx, authCode)
	if err != nil {
		zlog.Error(ctx, "Failed to exchange auth code for token", err)
		return nil, fmt.Errorf("failed to exchange auth code for token: %w", err)
	}

	idToken, ok := token.Extra("id_token").(string)
	if !ok {
		zlog.Warn(ctx, "ID token not found in OAuth response")
		return nil, fmt.Errorf("ID token not found in OAuth response")
	}

	user, err := s.signInUserWithIDToken(ctx, idToken, guestID)
	if err != nil {
		zlog.Error(ctx, "Failed to sign in user with ID token", err)
		return nil, fmt.Errorf("failed to sign in user with ID token: %w", err)
	}

	return user, nil
}

func (s *service) signInUserWithIDToken(ctx context.Context, idToken string, guestID *models.ObjectID) (*models.User, error) {
	payload, err := idtoken.Validate(ctx, idToken, s.authService.OAuthClient().ClientID)
	if err != nil {
		zlog.Error(ctx, "Failed to validate ID token", err)
		return nil, fmt.Errorf("failed to validate ID token: %w", err)
	}

	user, err := s.findOrCreateUser(ctx, payload, guestID)
	if err != nil {
		zlog.Error(ctx, "Failed to find or create user", err)
		return nil, fmt.Errorf("failed to find or create user: %w", err)
	}

	token, err := s.authService.GenerateToken(user.ID)
	if err != nil {
		zlog.Error(ctx, "Failed to generate token", err)
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}
	user.Token = utils.AllocPtr(token)
	zlog.Info(ctx, "User logged in successfully", zap.String("userID", user.ID.Hex()))
	return user, nil
}

func (s *service) findOrCreateUser(ctx context.Context, payload *idtoken.Payload, guestID *models.ObjectID) (*models.User, error) {
	email, _ := payload.Claims["email"].(string)
	name, _ := payload.Claims["name"].(string)
	picture, _ := payload.Claims["picture"].(string)
	isGuest := false

	zlog.Info(ctx, "Finding or creating user", zap.String("email", email))

	user, err := s.GetUserByEmail(ctx, email)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		zlog.Error(ctx, "Error fetching user by email", err, zap.String("email", email))
		return nil, fmt.Errorf("error fetching user by email: %w", err)
	}

	isSignup := false
	if user == nil {
		// User doesn't exist, create a new one
		higherRankedPlayers, err := s.CountUsersWithHigherRating(ctx, s.userRepo, constants.DefaultRating)
		if err != nil {
			zlog.Error(ctx, "Failed to count higher ranked players", err)
			return nil, fmt.Errorf("failed to count higher ranked players: %w", err)
		}

		globalRank := higherRankedPlayers + 1

		if guestID != nil {
			// Convert guest user to regular user
			guestUser, err := s.GetUserByID(ctx, *guestID)
			if err != nil && !errors.Is(err, mongo.ErrNoDocuments) && !errors.Is(err, systemErrors.ErrUserNotFound) {
				zlog.Error(ctx, "Error fetching guest user", err, zap.String("guestID", guestID.Hex()))
				return nil, fmt.Errorf("error fetching guest user: %w", err)
			}
			username, err := s.GenerateUserName(ctx, name)
			if err != nil {
				zlog.Error(ctx, "Error generating username", err)
			}
			if guestUser != nil {
				user = guestUser
				user.Email = &email
				user.Name = &name
				user.Username = username
				user.ProfileImageURL = &picture
				user.IsGuest = &isGuest
				user.IsSignup = isSignup
				user.GlobalRank = &globalRank

				user.UpdatedAt = utils.AllocPtr(time.Now().UTC())

				if user.RatingV2 == nil {
					user.RatingV2 = gameutils.GetUserDefaultRating(user)
				}

				// Update the existing guest user
				err = s.userRepo.UpdateOne(
					ctx,
					bson.M{"_id": user.ID},
					bson.M{"$set": user},
				)
				if err != nil {
					zlog.Error(ctx, "Failed to update guest user", err, zap.String("guestID", guestID.Hex()))
					return nil, fmt.Errorf("failed to update guest user: %w", err)
				}
				err = s.globalLeaderboard.UpdateUserRating(ctx, nil, *user.Rating)
				if err != nil {
					zlog.Error(ctx, "Failed to update leaderboard", err, zap.Any("payload", map[string]interface{}{
						"userId":        user.ID,
						"updatedRating": *user.Rating,
					}))
					// return nil, err
				}
				if err := rankingutils.UpdateRank(true, s.userRepo); err != nil {
					return nil, err
				}
				zlog.Info(ctx, "Guest user converted to regular user", zap.String("email", email), zap.String("userID", user.ID.Hex()))
				return user, nil
			}
		}

		// Create a new user
		isSignup = true
		username, err := s.GenerateUserName(ctx, name)
		if err != nil {
			zlog.Error(ctx, "Error generating username", err)
		}
		// Generate a new unique referral code
		referralCode, err := s.GenerateUniqueReferralCode(ctx)
		if err != nil {
			zlog.Error(ctx, "Failed to generate unique referral code", err)
			// Fallback to non-unique code if there's an error
			referralCode = GenerateReferralCode()
		}
		newID := primitive.NewObjectID()

		user = &models.User{
			ID:              newID,
			Email:           &email,
			Name:            &name,
			Username:        username,
			ProfileImageURL: &picture,
			Rating:          utils.AllocPtr(constants.DefaultRating),
			IsGuest:         &isGuest,
			GlobalRank:      &globalRank,
			CreatedAt:       utils.AllocPtr(time.Now().UTC()),
			UpdatedAt:       utils.AllocPtr(time.Now().UTC()),
			IsSignup:        isSignup,
			ReferralCode:    &referralCode,
			IsReferred:      utils.AllocPtr(false),
		}

		user.RatingV2 = gameutils.GetUserDefaultRating(user)

		err = s.userRepo.Create(ctx, user)
		if err != nil {
			zlog.Error(ctx, "Failed to insert new user", err, zap.String("email", email))
			return nil, fmt.Errorf("failed to insert new user: %w", err)
		}

		err = s.globalLeaderboard.UpdateUserRating(ctx, nil, *user.Rating)
		if err != nil {
			zlog.Error(ctx, "Failed to update leaderboard", err, zap.Any("payload", map[string]interface{}{
				"userId":        user.ID,
				"updatedRating": *user.Rating,
			}))
			// return nil, err
		}

		if err := rankingutils.UpdateRank(true, s.userRepo); err != nil {
			return nil, err
		}

		zlog.Info(ctx, "New user created", zap.String("email", email), zap.String("userID", user.ID.Hex()))
	} else {
		// User exists, update their information
		username, err := s.GenerateUserName(ctx, name)
		if err != nil {
			zlog.Error(ctx, "Error generating username", err)
		}

		// Initialize referralCode variable
		var referralCodeString string

		// Check if user already has a referral code
		if user.ReferralCode != nil {
			referralCodeString = *user.ReferralCode
		} else {
			// Generate a new unique referral code
			var err error
			referralCodeString, err = s.GenerateUniqueReferralCode(ctx)
			if err != nil {
				zlog.Error(ctx, "Failed to generate unique referral code", err)
				// Fallback to non-unique code if there's an error
				referralCodeString = GenerateReferralCode()
			}
		}

		if user.Email == nil || *user.Email != email {
			update := bson.M{
				"$set": bson.M{
					"name":            name,
					"profileImageUrl": picture,
					"email":           email,
					"username":        username,
					"isGuest":         isGuest,
					"referralCode":    referralCodeString,
					"isReferred":      false,
					"updatedAt":       time.Now().UTC(),
				},
			}

			err = s.userRepo.UpdateOne(ctx, bson.M{"_id": user.ID}, update)
			if err != nil {
				zlog.Error(ctx, "Failed to update existing user", err, zap.String("email", email))
				return nil, fmt.Errorf("failed to update existing user: %w", err)
			}
		}
		if err := rankingutils.UpdateRank(false, s.userRepo); err != nil {
			return nil, err
		}
		zlog.Info(ctx, "Existing user updated", zap.String("email", email), zap.String("userID", user.ID.Hex()))
	}

	return user, nil
}

func (s *service) CountUsersWithHigherRating(ctx context.Context, userRepo repository.UserRepository, rating int) (int, error) {
	count, err := userRepo.Count(ctx, bson.M{"rating": bson.M{"$gt": rating}, "isGuest": false})
	if err != nil {
		return 0, err
	}
	return int(count), nil
}
