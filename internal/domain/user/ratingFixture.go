package user

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/thoas/go-funk"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/domain/questions/questionsGenerator"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/domain/utils/rankingutils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

const RatingFixtureQuestions = 20

func (s *service) GetRatingFixtureQuestions(ctx context.Context) ([]*models.Question, error) {
	ratings := questionsGenerator.GenerateUniformRatings(800, 1400, RatingFixtureQuestions)

	questions := make([]*models.Question, RatingFixtureQuestions)
	for i := 0; i < len(ratings); i++ {
		questions[i] = questionsGenerator.GetRandomArithmeticQuestion(ratings[i], nil)
		questionId := strconv.Itoa(i + 1)
		questions[i].ID = &questionId
	}

	return questions, nil
}

func (s *service) SubmitRatingFixtureResponses(ctx context.Context, submission []*int, timeTaken int) (*models.UserRatingFixtureSubmission, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	// Get user's information
	user, err := s.GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %v", err)
	}

	var userHasFixedRating bool
	if user != nil && user.HasFixedRating != nil {
		userHasFixedRating = *user.HasFixedRating
	} else {
		userHasFixedRating = false
	}

	// Check if user has fixed rating
	if userHasFixedRating {
		return nil, errors.New("user has a fixed rating and cannot submit rating fixture responses")
	}

	// Check if user has already submitted a fixture response
	existingSubmission, err := s.userRatingFixtureSubmissionRepo.FindByUserID(ctx, userID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return nil, fmt.Errorf("failed to check for existing submissions: %v", err)
	}
	if existingSubmission != nil {
		return nil, errors.New("user has already submitted a rating fixture response")
	}

	now := time.Now()
	// Calculate user score
	userScore := len(funk.Compact(submission).([]*int))

	currentRating := *user.Rating

	// proposed rating
	proposedRating := utils.GetRatingBasedOnFixtureTest(timeTaken, userScore)

	ratingFixtureSubmission := &models.UserRatingFixtureSubmission{
		ID:             primitive.NewObjectID(),
		UserID:         userID,
		Submissions:    submission,
		UserScore:      userScore,
		TimeTaken:      timeTaken,
		CreatedAt:      now,
		UpdatedAt:      now,
		CurrentRating:  currentRating,
		ProposedRating: proposedRating,
		UserStance:     models.UserStancePending,
	}

	// Insert the document into the database
	_, err = s.userRatingFixtureSubmissionRepo.InsertOne(ctx, ratingFixtureSubmission)
	if err != nil {
		return nil, err
	}

	return ratingFixtureSubmission, nil
}

func (s *service) UpdateRatingBasedOnFixtureResponse(ctx context.Context, userStance models.UserStance) (*models.User, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	// Get user's information
	user, err := s.GetUserByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Check if user already has a fixed rating
	if user.HasFixedRating != nil && *user.HasFixedRating {
		return nil, errors.New("user already has a fixed rating")
	}

	// Get the user's rating fixture submission
	submission, err := s.userRatingFixtureSubmissionRepo.FindByUserID(ctx, userID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errors.New("user does not have a rating fixture submission")
		}
		return nil, err
	}

	// Update the user's rating fixture submission
	submission.UserStance = userStance
	_, err = s.userRatingFixtureSubmissionRepo.UpdateOne(ctx, submission)
	if err != nil {
		return nil, err
	}

	// Update user's HasFixedRating field
	hasFixedRating := true
	user.HasFixedRating = &hasFixedRating
	oldRating := user.Rating
	// If the user accepted the proposed rating, update their rating
	if userStance == models.UserStanceAccepted {
		user.Rating = utils.AllocPtr(submission.ProposedRating)
		if user.RatingV2 == nil {
			user.RatingV2 = &models.UserRating{
				GlobalRating: utils.AllocPtr(submission.ProposedRating),
			}
		}
		user.RatingV2.GlobalRating = utils.AllocPtr(submission.ProposedRating)
		user.Badge = utils.AllocPtr(utils.GetInitialBadge(submission.ProposedRating))
		go s.notifyBadgeAssigned(utils.DeriveContextWithoutCancel(ctx), user)
	}

	// Update the user in the database
	err = s.UpdateUserFromObject(ctx, user)
	if err != nil {
		return nil, err
	}
	if user.Rating == nil {
		return nil, fmt.Errorf("user rating found nil")
	}

	go func() {
		err := rankingutils.UpdateRank(false, s.userRepo)
		if err != nil {
			zlog.Error(ctx, "Failed to update ranking", err)
		}
	}()
	err = s.globalLeaderboard.UpdateUserRating(ctx, oldRating, *user.Rating)
	if err != nil {
		zlog.Error(ctx, "Failed to update leaderboard", err, zap.Any("payload", map[string]interface{}{
			"userId":        user.ID,
			"prevRating":    oldRating,
			"updatedRating": *user.Rating,
		}))
		// return nil, err
	}
	return user, nil
}

func (s *service) GetRatingFixtureSubmission(ctx context.Context) (*models.UserRatingFixtureSubmission, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	// Get the user's rating fixture submission
	submission, err := s.userRatingFixtureSubmissionRepo.FindByUserID(ctx, userID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errors.New("used does not have submitted rating fixture submissions")
		}
		return nil, err
	}

	return submission, nil
}

func (s *service) notifyBadgeAssigned(ctx context.Context, user *models.User) {
	if user.IsGuest != nil && *user.IsGuest {
		return
	}

	err := s.coreService.PublishUserEvent(ctx, user.ID, &models.BadgeAssignedEvent{
		InitialBadge: nil,
		NewBadge:     user.Badge,
	})
	if err != nil {
		zlog.Error(ctx, "Failed to publish badge assigned event", err)
	}
}
