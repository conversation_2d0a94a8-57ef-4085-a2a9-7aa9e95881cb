package user

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
)

func (s *service) SendOtp(ctx context.Context, email string) (bool, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	isValidEmail := utils.IsValidEmail(email)
	if !isValidEmail {
		return false, fmt.Errorf("invalid email address")
	}

	OtpStoreCacheKey := fmt.Sprintf("%s_%s", constants.OtpStoreCacheKey, userId)
	existingOTP, err := s.redisCache.Get(ctx, OtpStoreCacheKey)
	if err == nil && existingOTP != nil {
		return true, nil
	}

	otp, err := utils.GenerateOTP(4)
	if err != nil {
		return false, fmt.Errorf("failed to generate otp: %w", err)
	}

	hashedOTP, err := s.authService.HashPassword(otp)
	if err != nil {
		return false, fmt.Errorf("failed to hash otp: %w", err)
	}

	err = s.redisCache.Set(ctx, OtpStoreCacheKey, []byte(hashedOTP), constants.OtpExpiryDuration)
	if err != nil {
		return false, fmt.Errorf("failed to set otp to redis: %w", err)
	}

	emails := make([]string, 1)
	emails[0] = email

	htmlBody := fmt.Sprintf(`
		<!DOCTYPE html>
		<html>
		<head>
			<style>
				.container {
					max-width: 600px;
					margin: 0 auto;
					padding: 20px;
					font-family: Arial, sans-serif;
				}
				.header {
					background-color: #4CAF50;
					color: white;
					padding: 20px;
					text-align: center;
					border-radius: 5px 5px 0 0;
				}
				.content {
					background-color: #f8f9fa;
					padding: 20px;
					border-radius: 0 0 5px 5px;
				}
				.otp-code {
					font-size: 32px;
					font-weight: bold;
					text-align: center;
					color: #2c3e50;
					margin: 20px 0;
					letter-spacing: 5px;
				}
				.footer {
					text-align: center;
					margin-top: 20px;
					font-size: 12px;
					color: #666;
				}
			</style>
		</head>
		<body>
			<div class="container">
				<div class="header">
					<h2>Email Verification</h2>
				</div>
				<div class="content">
					<p>Hello Mathelete,</p>
					<p>Thank you for registering with us. Please use the following OTP to verify your email address:</p>
					<div class="otp-code">%s</div>
					<p>If you didn't request this verification, please ignore this email.</p>
				</div>
				<div class="footer">
					<p>This is an automated message, please do not reply to this email.</p>
					<p>&copy; 2025 Matiks. All rights reserved.</p>
				</div>
			</div>
		</body>
		</html>
	`, otp)

	err = s.notificationService.SendEmailNotification(
		ctx,
		"Matiks : Email Verification Code",
		htmlBody,
		emails,
	)
	if err != nil {
		return false, err
	}

	return true, nil
}
