package user

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/auth"
	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cdn"
	"matiksOfficial/matiks-server-go/internal/infrastructure/leaderboard"
	"matiksOfficial/matiks-server-go/internal/infrastructure/list"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/infrastructure/storage"
	"matiksOfficial/matiks-server-go/internal/infrastructure/websocket"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type service struct {
	authService                     auth.AuthStore
	coreService                     domain.CoreLogicStore
	ws                              websocket.Websocket
	userRatingFixtureSubmissionRepo repository.UserRatingFixtureSubmissionRepository
	userRepo                        repository.UserRepository
	deletedUserRepo                 repository.DeletedUserRepository
	userDailyActivityRepo           repository.UserDailyActivityRepository
	userActivitiesRepo              repository.UserActivitiesRepository
	friendsAndFollowersRepo         repository.FriendsAndFollowersRepository
	storage                         *storage.Storage
	redisCache                      cache.Cache
	globalLeaderboard               leaderboard.GlobalLeaderboard
	userSettingsRepo                repository.UserSettingsRepository
	list                            list.List
	notificationService             domain.NotificationStore
	institutionRepo                 repository.InstitutionRepository
	referralRepo                    repository.ReferralsRepository
	userStreakRepo                  repository.UserStreakRepository
	streakShieldTransactionService  domain.StreakShieldTransactionStore
	counterRepo                     repository.CounterRepository
	cdnClient                       cdn.CDNClient
}

func NewUserService(lc fx.Lifecycle, ws websocket.Websocket, authService auth.AuthStore,
	repositoryFactory *repository.RepositoryFactory, storage *storage.Storage, redisCache cache.Cache, list list.List,
	globalLeaderboard leaderboard.GlobalLeaderboard, notificationService domain.NotificationStore, coreService domain.CoreLogicStore,
	streakShieldTransactionService domain.StreakShieldTransactionStore, cdnClient cdn.CDNClient,
) domain.UserStore {
	s := &service{
		authService:                     authService,
		ws:                              ws,
		userRepo:                        repositoryFactory.UserRepository,
		deletedUserRepo:                 repositoryFactory.DeletedUserRepository,
		userRatingFixtureSubmissionRepo: repositoryFactory.UserRatingFixtureSubmissionRepository,
		storage:                         storage,
		friendsAndFollowersRepo:         repositoryFactory.FriendsAndFollowersRepository,
		redisCache:                      redisCache,
		userDailyActivityRepo:           repositoryFactory.UserDailyActivityRepository,
		userActivitiesRepo:              repositoryFactory.UserActivitiesRepository,
		globalLeaderboard:               globalLeaderboard,
		list:                            list,
		userSettingsRepo:                repositoryFactory.UserSettingsRepository,
		notificationService:             notificationService,
		coreService:                     coreService,
		institutionRepo:                 repositoryFactory.InstitutionRepository,
		referralRepo:                    repositoryFactory.ReferralsRepository,
		userStreakRepo:                  repositoryFactory.UserStreakRepository,
		streakShieldTransactionService:  streakShieldTransactionService,
		counterRepo:                     repositoryFactory.CounterRepository,
		cdnClient:                       cdnClient,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting user service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down user service")
			return nil
		},
	})

	return s
}
