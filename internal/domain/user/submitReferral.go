package user

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	stringUtils "matiksOfficial/matiks-server-go/utils/string"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) SubmitReferral(ctx context.Context, referralCode string) (bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	zlog.Debug(ctx,
		"SubmitReferral",
		zap.String("userID", userID.Hex()),
		zap.String("referralCode", referralCode),
	)

	if err != nil {
		zlog.Error(ctx, "Failed to get user from context", err)
		return false, fmt.Errorf("unauthorized: %w", err)
	}
	user, err := s.GetUserByID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get user by ID", err)
		return false, err
	}

	if user.IsReferred != nil && *user.IsReferred {
		return false, fmt.Errorf("user is already referred")
	}

	referringUser, err := s.referralRepo.GetUserByReferralCode(ctx, referralCode)
	if referringUser == nil {
		return false, fmt.Errorf("invalid referral code")
	}
	if err != nil {
		return false, fmt.Errorf("referral Code is Invalid, %v", err)
	}

	referral := models.Referral{
		ID:         primitive.NewObjectID(),
		Referrer:   referringUser.ID,
		ReferredTo: userID,
	}
	err = s.referralRepo.Create(ctx, &referral)
	if err != nil {
		zlog.Error(ctx, "Failed to upsert referral", err)
		return false, err
	}

	err = s.userRepo.UpdateOne(ctx, bson.M{"_id": userID}, bson.M{"$set": bson.M{"isReferred": true}})
	if err != nil {
		zlog.Error(ctx, "Failed to update user", err)
		return false, err
	}

	// Create streak shield transactions for both users
	creditedType := models.TransactionTypeCredited
	referralEarnVia := models.EarnViaReferral

	// Create transaction for the referred user
	_, err = s.streakShieldTransactionService.CreateTransaction(
		ctx,
		userID,
		1, // quantity of shields earned
		creditedType,
		&referralEarnVia,
		&referral.ID, // referralId
		nil,          // no transactionId for referral transactions
	)
	if err != nil {
		zlog.Error(ctx, "Failed to create streak shield transaction for referred user", err)
		// Continue even if transaction creation fails, as this is a secondary operation
	}

	// Create transaction for the referrer
	_, err = s.streakShieldTransactionService.CreateTransaction(
		ctx,
		referringUser.ID,
		1, // quantity of shields earned
		creditedType,
		&referralEarnVia,
		&referral.ID, // referralId
		nil,          // no transactionId for referral transactions
	)
	if err != nil {
		zlog.Error(ctx, "Failed to create streak shield transaction for referrer", err)
		// Continue even if transaction creation fails, as this is a secondary operation
	}

	notificationData := map[string]string{
		"type":     "REFERRAL_SUCCESSFULL",
		"senderID": user.ID.Hex(),
		"username": user.Username,
	}

	err = s.notificationService.SendNotification(
		ctx,
		&models.UserNotification{
			Title:  "Your Referral Was Successfull 🎉",
			Body:   stringUtils.Concat("Your Referral was Successfull to ", user.Username),
			Data:   notificationData,
			Type:   models.NotificationTypePushNotification,
			UserID: referringUser.ID,
		},
	)
	if err != nil {
		zlog.Error(ctx, "error sending push notification", err)
	}

	return true, nil
}
