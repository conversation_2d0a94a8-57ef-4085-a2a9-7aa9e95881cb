package user

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) UpdateUserStatikCoinsAndTimeSpent(ctx context.Context, userID primitive.ObjectID, activityType constants.ActivityType, coins int, duration int64, activityID *primitive.ObjectID) error {
	user, err := s.GetUserByID(ctx, userID)
	if err != nil {
		return err
	}

	if user == nil {
		return fmt.Errorf("user not found")
	}

	if (user.IsBot != nil && *user.IsBot) || (user.IsHumanBot != nil && *user.IsHumanBot) {
		return nil
	}

	userDate := utils.GetUserDate(ctx, time.Now(), user.Timezone)

	err = s.userRepo.UpdateUserStatikCoins(ctx, userID, coins)
	if err != nil {
		return fmt.Errorf("error upserting users statikCoins: %w", err)
	}

	// Update the userDailyActivity repository for backward compatibility
	err = s.userDailyActivityRepo.UpsertUserActivity(ctx, userID, userDate, activityType.String(), duration, coins)
	if err != nil {
		return fmt.Errorf("error updating user daily activity: %w", err)
	}

	// Add record to the new user_activities collection
	activityTypeEnum := activityType
	userActivity := &models.UserActivity{
		UserID:       userID,
		ActivityType: activityTypeEnum,
		XPEarned:     coins,
		ActivityID:   activityID,
		Timestamp:    time.Now(),
		TimeSpent:    duration,
	}

	err = s.userActivitiesRepo.Create(ctx, userActivity)
	if err != nil {
		return fmt.Errorf("error creating user activity record: %w", err)
	}

	err = s.coreService.PublishUserEvent(ctx, userID, &models.StatikCoinsEarnedEvent{
		StatikCoinsEarned: &coins,
		ActivityType:      utils.AllocPtr(activityType.String()),
	})
	if err != nil {
		return err
	}

	userUpdatedStatikCoins := user.StatikCoins + coins

	if user.IsGuest == nil || *user.IsGuest {
		return nil
	}

	// TODO: @mohan this is very unoptimized, being called on each activity. need to
	if userUpdatedStatikCoins > user.StatikCoins {
		user.StatikCoins = userUpdatedStatikCoins
		err = s.UpdateUserParticipationInMatiksWeeklyLeague(ctx, *user)
		if err != nil {
			zlog.Error(ctx, "Failed to update user participation in Matiks Weekly League", err)
		}
	}

	return nil
}
