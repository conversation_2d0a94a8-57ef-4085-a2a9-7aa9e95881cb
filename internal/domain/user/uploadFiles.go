package user

import (
	"context"
	"fmt"
	"io"
	"time"

	"go.uber.org/zap"

	"cloud.google.com/go/storage"
	"github.com/99designs/gqlgen/graphql"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func (s *service) UploadFiles(ctx context.Context, files []*graphql.Upload) ([]*models.File, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if len(files) == 0 {
		return []*models.File{}, nil
	}

	bucket := s.storage.Storage.Bucket(constants.StorageBucket)
	resp := make([]*models.File, 0, len(files))

	for _, file := range files {
		objectName := "files/" + userID.Hex() + "_" + file.Filename
		object := bucket.Object(objectName)

		content, err := io.ReadAll(file.File)
		if err != nil {
			return nil, fmt.Errorf("failed to read file content: %w", err)
		}

		writer := object.NewWriter(ctx)
		writer.ObjectAttrs.ContentType = file.ContentType
		writer.ObjectAttrs.ACL = []storage.ACLRule{{Entity: storage.AllUsers, Role: storage.RoleReader}}
		writer.ObjectAttrs.CacheControl = "public, max-age=31536000, immutable"

		if _, err := writer.Write(content); err != nil {
			return nil, fmt.Errorf("failed to write to GCS: %w", err)
		}

		if err := writer.Close(); err != nil {
			return nil, fmt.Errorf("failed to close GCS writer: %w", err)
		}

		timestamp := time.Now().Unix()
		url := fmt.Sprintf("https://cdn.matiks.com/%s?timestamp=%d", objectName, timestamp)
		// Extract the path for later cache invalidation
		urlPath := s.cdnClient.ExtractPathFromURL(url)

		// Try to invalidate the specific URL path immediately
		err = s.cdnClient.InvalidateCache(ctx, urlPath)
		if err != nil {
			// Log the error but don't fail the upload
			zlog.Error(ctx, "Failed to invalidate CDN cache for specific file URL", err,
				zap.String("path", urlPath))
		}

		resp = append(resp, &models.File{
			Name:        file.Filename,
			Content:     string(content),
			ContentType: file.ContentType,
			URL:         url,
		})
	}

	// Also invalidate the pattern for all user's files
	specificPath := fmt.Sprintf("/files/%s_*", userID.Hex())
	err = s.cdnClient.InvalidateCache(ctx, specificPath)
	if err != nil {
		// Log the error but don't fail the upload
		zlog.Error(ctx, "Failed to invalidate CDN cache for files pattern", err,
			zap.String("path", specificPath))
	}

	return resp, nil
}
