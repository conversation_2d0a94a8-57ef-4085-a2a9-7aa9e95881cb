package user

import (
	"context"
	"fmt"
	"io"
	"time"

	"go.uber.org/zap"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"cloud.google.com/go/storage"
	"github.com/99designs/gqlgen/graphql"
	"go.mongodb.org/mongo-driver/bson"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *service) UploadProfilePicture(ctx context.Context, file graphql.Upload) (*models.File, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get user ID: %w", err)
	}

	bucket := s.storage.Storage.Bucket(constants.StorageBucket)
	objectName := "profile_picture/" + userID.Hex() + "_profile-picture.jpeg"
	object := bucket.Object(objectName)

	content, err := io.ReadAll(file.File)
	if err != nil {
		return nil, fmt.Errorf("failed to read file content: %w", err)
	}

	writer := object.NewWriter(ctx)
	writer.ObjectAttrs.ContentType = file.ContentType
	writer.ObjectAttrs.ACL = []storage.ACLRule{{Entity: storage.AllUsers, Role: storage.RoleReader}}
	writer.ObjectAttrs.CacheControl = "public, max-age=31536000, immutable"

	if _, err := writer.Write(content); err != nil {
		return nil, fmt.Errorf("failed to write to GCS: %w", err)
	}

	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("failed to close GCS writer: %w", err)
	}

	timestamp := time.Now().Unix()
	url := fmt.Sprintf("https://cdn.matiks.com/%s?timestamp=%d", objectName, timestamp)

	// Update user profile image URL in database
	err = s.userRepo.UpdateOne(ctx, bson.M{"_id": userID}, bson.M{"$set": bson.M{"profileImageUrl": url}})
	if err != nil {
		return nil, fmt.Errorf("failed to update user profile image URL in database: %w", err)
	}

	// Extract the path from the URL and invalidate the CDN cache
	urlPath := s.cdnClient.ExtractPathFromURL(url)

	// Try to invalidate the specific URL path first
	err = s.cdnClient.InvalidateCache(ctx, urlPath)
	if err != nil {
		// Log the error but don't fail the upload
		zlog.Error(ctx, "Failed to invalidate CDN cache for specific profile picture URL", err,
			zap.String("path", urlPath))
	}

	return &models.File{
		Name:        file.Filename,
		Content:     string(content),
		ContentType: file.ContentType,
		URL:         url,
	}, nil
}
