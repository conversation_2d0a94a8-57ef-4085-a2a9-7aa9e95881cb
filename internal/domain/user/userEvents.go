package user

import (
	"context"
	"encoding/json"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) UserEventsSubscription(ctx context.Context, userID *primitive.ObjectID) (<-chan models.UserEvent, error) {
	if userID == nil {
		return nil, fmt.Errorf("userID is nil")
	}

	channel := fmt.Sprintf("%s_%s", constants.SubscriptionPrefixEnum.USER_EVENT, userID.Hex())

	subscription, err := s.ws.Subscribe(ctx, channel)
	if err != nil {
		zlog.Info(ctx, "Failed to start UserEvents subscription", zap.Error(err))
		return nil, fmt.Errorf("failed to start UserEvents subscription: %w", err)
	}

	ch := make(chan models.UserEvent)
	go func() {
		defer close(ch)
		defer zlog.Info(ctx, "UserEvents subscription ended")

		for {
			select {
			case <-ctx.Done():
				zlog.Info(ctx, "UserEvents subscription context done", zap.String("userID", userID.Hex()))
				return
			case message := <-subscription:
				eventData := []byte(message.(string))
				processedEvent, err := s.parseUserEvent(ctx, eventData)
				if err != nil {
					zlog.Info(ctx, "Error handling user event", zap.Error(err))
					continue
				}

				ch <- processedEvent
			}
		}
	}()

	return ch, nil
}

func (s *service) parseUserEvent(ctx context.Context, eventData []byte) (models.UserEvent, error) {
	var baseEvent struct {
		Type  string          `json:"type"`
		Event json.RawMessage `json:"event"`
	}

	err := json.Unmarshal(eventData, &baseEvent)
	if err != nil {
		zlog.Error(ctx, "Failed to unmarshal event", err, zap.ByteString("eventData", eventData))
		return nil, fmt.Errorf("failed to unmarshal event: %w", err)
	}
	return s.processEvent(ctx, baseEvent.Type, baseEvent.Event)
}

func (s *service) processEvent(ctx context.Context, eventType string, eventData json.RawMessage) (models.UserEvent, error) {
	switch eventType {
	case constants.UserEventRematchRequested.String():
		var event models.RematchRequestOutput
		if err := json.Unmarshal(eventData, &event); err != nil {
			zlog.Error(ctx, "Failed to unmarshal RematchRequestOutput", err, zap.ByteString("eventData", eventData))
			return nil, fmt.Errorf("failed to unmarshal RematchRequestOutput: %w", err)
		}
		return &event, nil
	case constants.UserEventBadgeAssigned.String():
		var event models.BadgeAssignedEvent
		if err := json.Unmarshal(eventData, &event); err != nil {
			zlog.Error(ctx, "Failed to unmarshal BadgeAssignedEvent", err, zap.ByteString("eventData", eventData))
			return nil, fmt.Errorf("failed to unmarshal BadgeAssignedEvent: %w", err)
		}
		return &event, nil
	case constants.UserEventUserMatched.String():
		var event models.SearchSubscriptionOutput
		if err := json.Unmarshal(eventData, &event); err != nil {
			zlog.Error(ctx, "Failed to unmarshal SearchSubscriptionOutput", err, zap.ByteString("eventData", eventData))
			return nil, fmt.Errorf("failed to unmarshal SearchSubscriptionOutput: %w", err)
		}
		return &event, nil
	case constants.ChallengeUserEnumStruct.String():
		var event models.ChallengeOutput
		if err := json.Unmarshal(eventData, &event); err != nil {
			zlog.Error(ctx, "Failed to unmarshal ChallengeOutput", err, zap.ByteString("eventData", eventData))
			return nil, fmt.Errorf("failed to unmarshal ChallengeOutput: %w", err)
		}
		return &event, nil
	default:
		zlog.Warn(ctx, "Unknown event type", zap.String("eventType", eventType))
		return nil, fmt.Errorf("unknown event type: %s", eventType)
	}
}
