package user

import (
	"context"
	"errors"
	"fmt"
	"math/rand/v2"
	"regexp"
	"strconv"
	"strings"

	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.mongodb.org/mongo-driver/mongo"

	"go.mongodb.org/mongo-driver/bson"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func (s *service) IsUsernameAvailable(ctx context.Context, username *string) (bool, error) {
	if username == nil {
		return false, fmt.Errorf("username is nil")
	}
	*username = strings.ToLower(strings.TrimSpace(*username))
	if len(*username) < 4 {
		*username = "mathlete_" + addRandomSuffix(*username)
	}

	if !s.isValidUsername(*username) {
		*username = "mathlete_" + addRandomSuffix(*username)
	}

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil && !errors.Is(err, systemErrors.ErrUserNotAuthorized) {
		return false, fmt.Errorf("failed to get user from context: %w", err)
	}
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return false, fmt.Errorf("failed to get user: %w", err)
	}
	if user != nil && user.Username != "" && *username == user.Username {
		return true, nil
	}
	count, err := s.userRepo.Count(ctx, bson.M{"username": *username})
	if err != nil {
		return false, fmt.Errorf("failed to get user count: %w", err)
	}
	return count == 0, nil
}

func (s *service) GenerateUserName(ctx context.Context, name string) (string, error) {
	username := cleanUsername(name)
	if strings.HasPrefix(name, "guest") {
		for {
			isAvailable, err := s.IsUsernameAvailable(ctx, &username)
			if err != nil {
				return "", fmt.Errorf("failed to check user availability: %w", err)
			}
			if isAvailable {
				return username, nil
			}
			username = addRandomSuffix(username)
		}
	}
	for {
		isAvailable, err := s.IsUsernameAvailable(ctx, &username)
		if err != nil {
			return "", fmt.Errorf("failed to check user availability: %w", err)
		}
		if isAvailable {
			return username, nil
		}
		username = addRandomSuffix(username)
	}
}

func addRandomSuffix(name string) string {
	randSuffix := rand.Int() % 10000
	return name + strconv.Itoa(randSuffix)
}

func (s *service) isValidUsername(username string) bool {
	if len(username) < 4 {
		return false
	}

	// Check if the username starts with a letter (a-z)
	if !regexp.MustCompile(`^[a-z]`).MatchString(username) {
		return false
	}

	// Check if the username contains only valid characters (a-z, 0-9, -, _, .)
	if !regexp.MustCompile(`^[a-z0-9-_.]+$`).MatchString(username) {
		return false
	}

	// If all conditions are met, return true
	return true
}

func cleanUsername(name string) string {
	// Convert to lowercase and remove spaces
	name = strings.ToLower(strings.ReplaceAll(name, " ", ""))

	// Remove invalid characters
	name = regexp.MustCompile(`[^a-z0-9._-]`).ReplaceAllString(name, "")

	// Remove leading and trailing dots, hyphens, or underscores
	name = regexp.MustCompile(`^[._-]+|[._-]+$`).ReplaceAllString(name, "")

	return name
}

// Constants for referral code generation
const (
	ReferralCodeCounterName = "referral_code_counter"
	ReferralCodeLength      = 8 // Total length of the referral code
)

// GenerateReferralCode creates a referral code with random characters
// This is a fallback method when the sequential method is not available
func GenerateReferralCode() string {
	// Use a wider character set including both letters and numbers
	chars := "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789" // Removed confusing chars like O,0,I,l,1

	// Create a prefix to make codes more readable and categorizable
	prefixes := []string{"MT", "ST", "MK", "SK"} // Matiks-related prefixes
	prefix := prefixes[rand.IntN(len(prefixes))]

	// Generate the random part (6 characters)
	randomPart := ""
	for range 6 {
		randomPart += string(chars[rand.IntN(len(chars))])
	}

	// Combine prefix and random part
	return prefix + randomPart
}

// GenerateUniqueReferralCode creates a guaranteed unique referral code using a counter
// Format: 2-letter prefix + 6-digit sequential counter (padded with zeros)
func (s *service) GenerateUniqueReferralCode(ctx context.Context) (string, error) {
	// Get the next sequence number from the counter collection
	seq, err := s.counterRepo.GetNextSequence(ctx, ReferralCodeCounterName)
	if err != nil {
		// If there's an error with the counter, fall back to the random method
		zlog.Error(ctx, "Failed to get next sequence for referral code", err)
		return s.fallbackToRandomReferralCode(ctx)
	}

	// Create a prefix to make codes more readable and categorizable
	prefixes := []string{"MT", "ST", "MK", "SK"} // Matiks-related prefixes

	// Use the sequence number to determine which prefix to use (for variety)
	prefixIndex := int(seq % int64(len(prefixes)))
	prefix := prefixes[prefixIndex]

	// Format the sequence number as a 6-digit string with leading zeros
	sequentialPart := fmt.Sprintf("%06d", seq)

	// If the sequential part is longer than 6 digits, truncate it to the last 6 digits
	if len(sequentialPart) > 6 {
		sequentialPart = sequentialPart[len(sequentialPart)-6:]
	}

	// Combine prefix and sequential part
	code := prefix + sequentialPart

	// Double-check that the code is unique (should always be true with a counter)
	count, err := s.userRepo.Count(ctx, bson.M{"referralCode": code})
	if err != nil {
		zlog.Error(ctx, "Failed to check referral code uniqueness", err)
		return s.fallbackToRandomReferralCode(ctx)
	}

	if count > 0 {
		// This should never happen with a properly functioning counter
		zlog.Error(ctx, "Unexpected duplicate referral code detected", fmt.Errorf("duplicate code: %s", code))
		return s.fallbackToRandomReferralCode(ctx)
	}

	return code, nil
}

// fallbackToRandomReferralCode is a helper method to generate a unique random code
// when the sequential method fails
func (s *service) fallbackToRandomReferralCode(ctx context.Context) (string, error) {
	for i := 0; i < 10; i++ { // Try up to 10 times to generate a unique code
		code := GenerateReferralCode()

		// Check if code already exists
		count, err := s.userRepo.Count(ctx, bson.M{"referralCode": code})
		if err != nil {
			return "", fmt.Errorf("failed to check referral code uniqueness: %w", err)
		}

		if count == 0 {
			// Code is unique, return it
			return code, nil
		}
	}

	// If we couldn't generate a unique code after several attempts
	return "", fmt.Errorf("failed to generate unique referral code after multiple attempts")
}
