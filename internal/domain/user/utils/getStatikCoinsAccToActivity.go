package userUtils

import "matiksOfficial/matiks-server-go/internal/constants"

func GetStatikCoinsAccToActivityType(activityType string, duration int64) int {
	if duration < 60000 && activityType == string(constants.ActivityTypeDailyChallenge) {
		return 30
	}

	if duration >= 60000 && activityType == string(constants.ActivityTypeDailyChallenge) {
		return 25
	}

	if activityType == string(constants.ActivityTypeFlashAnzan) {
		return 10
	}

	return 5
}
