package userUtils

import "matiksOfficial/matiks-server-go/internal/models"

func IsEligibleToViewOnlineUsers(user models.User) bool {
	isUserShadowBanned := IsUserShadowBanned(user)
	if isUserShadowBanned {
		return false
	}

	if user.Stats != nil && user.Stats.Ngp >= 3 {
		return true
	}

	return false
}

func IsUserShadowBanned(user models.User) bool {
	if user.IsShadowBanned != nil && *user.IsShadowBanned {
		return true
	}

	if user.IsBanned != nil && *user.IsBanned {
		return true
	}

	return false
}
