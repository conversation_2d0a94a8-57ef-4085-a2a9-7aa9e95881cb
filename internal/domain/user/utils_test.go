package user

import (
	"context"
	"fmt"
	"testing"

	"matiksOfficial/matiks-server-go/mocks/internal_/auth"
	"matiksOfficial/matiks-server-go/mocks/internal_/domain"
	"matiksOfficial/matiks-server-go/mocks/internal_/infrastructure/cache"
	"matiksOfficial/matiks-server-go/mocks/internal_/infrastructure/leaderboard"
	"matiksOfficial/matiks-server-go/mocks/internal_/infrastructure/list"
	"matiksOfficial/matiks-server-go/mocks/internal_/infrastructure/repository"
	"matiksOfficial/matiks-server-go/mocks/internal_/infrastructure/websocket"

	"github.com/stretchr/testify/mock"
)

func Test_service_GenerateUserName(t *testing.T) {
	type fields struct {
		authService                     *auth.MockAuthStore
		ws                              *websocket.MockWebsocket
		userRatingFixtureSubmissionRepo *repository.MockUserRatingFixtureSubmissionRepository
		userRepo                        *repository.MockUserRepository
		userDailyActivityRepo           *repository.MockUserDailyActivityRepository
		friendsAndFollowersRepo         *repository.MockFriendsAndFollowersRepository
		redisCache                      *cache.MockCache
		globalLeaderboard               *leaderboard.MockGlobalLeaderboard
		userSettingsRepo                *repository.MockUserSettingsRepository
		list                            *list.MockList
		notificationService             *domain.MockNotificationStore
	}
	type args struct {
		ctx  context.Context
		name string
	}
	defFields := fields{
		authService:                     auth.NewMockAuthStore(t),
		ws:                              websocket.NewMockWebsocket(t),
		userRatingFixtureSubmissionRepo: repository.NewMockUserRatingFixtureSubmissionRepository(t),
		userRepo:                        repository.NewMockUserRepository(t),
		userDailyActivityRepo:           repository.NewMockUserDailyActivityRepository(t),
		friendsAndFollowersRepo:         repository.NewMockFriendsAndFollowersRepository(t),
		redisCache:                      cache.NewMockCache(t),
		globalLeaderboard:               leaderboard.NewMockGlobalLeaderboard(t),
		userSettingsRepo:                repository.NewMockUserSettingsRepository(t),
		list:                            list.NewMockList(t),
		notificationService:             domain.NewMockNotificationStore(t),
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		{
			name:   "test",
			fields: defFields,
			args: args{
				ctx:  context.Background(),
				name: "test",
			},
			want:    "test",
			wantErr: false,
		},
		{
			name:   "blank",
			fields: defFields,
			args: args{
				ctx:  context.Background(),
				name: "",
			},
			want:    "test",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{
				authService:                     tt.fields.authService,
				ws:                              tt.fields.ws,
				userRatingFixtureSubmissionRepo: tt.fields.userRatingFixtureSubmissionRepo,
				userRepo:                        tt.fields.userRepo,
				userDailyActivityRepo:           tt.fields.userDailyActivityRepo,
				friendsAndFollowersRepo:         tt.fields.friendsAndFollowersRepo,
				redisCache:                      tt.fields.redisCache,
				globalLeaderboard:               tt.fields.globalLeaderboard,
				userSettingsRepo:                tt.fields.userSettingsRepo,
				list:                            tt.fields.list,
				notificationService:             tt.fields.notificationService,
			}
			tt.fields.userRepo.On("GetByID", mock.Anything, mock.Anything).Return(nil, nil)
			tt.fields.userRepo.On("Count", mock.Anything, mock.Anything).Return(int64(0), nil)
			got, err := s.GenerateUserName(tt.args.ctx, tt.args.name)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateUserName() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GenerateUserName() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGenerateReferralCode(t *testing.T) {
	// Test that the function generates codes of the expected format
	for i := 0; i < 5; i++ {
		code := GenerateReferralCode()

		// Check length (should be 8 characters: 2 for prefix + 6 random)
		if len(code) != 8 {
			t.Errorf("Expected code length to be 8, got %d: %s", len(code), code)
		}

		// Check that the prefix is one of the expected values
		prefix := code[0:2]
		validPrefixes := map[string]bool{"MT": true, "ST": true, "MK": true, "SK": true}
		if !validPrefixes[prefix] {
			t.Errorf("Expected prefix to be one of MT, ST, MK, SK, got %s", prefix)
		}

		t.Logf("Generated code: %s", code)
	}

	// Test that multiple calls generate different codes
	codes := make(map[string]bool)
	for i := 0; i < 100; i++ {
		code := GenerateReferralCode()
		codes[code] = true
	}

	// Check that we have close to 100 unique codes (allowing for a small chance of collision)
	if len(codes) < 95 {
		t.Errorf("Expected close to 100 unique codes, got %d", len(codes))
	}
}

func TestGenerateUniqueReferralCode(t *testing.T) {
	// Setup mocks
	userRepo := repository.NewMockUserRepository(t)
	counterRepo := repository.NewMockCounterRepository(t)

	// Mock the GetNextSequence method to return a sequence number
	counterRepo.On("GetNextSequence", mock.Anything, ReferralCodeCounterName).Return(int64(12345), nil)

	// Mock the Count method to simulate no existing codes
	userRepo.On("Count", mock.Anything, mock.Anything).Return(int64(0), nil)

	s := &service{
		userRepo:    userRepo,
		counterRepo: counterRepo,
	}

	// Test the unique code generation
	code, err := s.GenerateUniqueReferralCode(context.Background())
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	// Verify the code format
	if len(code) != 8 {
		t.Errorf("Expected code length to be 8, got %d: %s", len(code), code)
	}

	// Verify the code structure: 2-letter prefix + 6-digit number
	prefix := code[0:2]
	sequentialPart := code[2:]

	// Check that the prefix is one of the expected values
	validPrefixes := map[string]bool{"MT": true, "ST": true, "MK": true, "SK": true}
	if !validPrefixes[prefix] {
		t.Errorf("Expected prefix to be one of MT, ST, MK, SK, got %s", prefix)
	}

	// Check that the sequential part is the expected value (12345 formatted as 012345)
	expectedSequentialPart := "012345"
	if sequentialPart != expectedSequentialPart {
		t.Errorf("Expected sequential part to be %s, got %s", expectedSequentialPart, sequentialPart)
	}

	// Verify that the counter method was called
	counterRepo.AssertCalled(t, "GetNextSequence", mock.Anything, ReferralCodeCounterName)

	// Verify that Count was called
	userRepo.AssertCalled(t, "Count", mock.Anything, mock.Anything)
}

func TestGenerateUniqueReferralCode_FallbackToRandom(t *testing.T) {
	// Setup mocks
	userRepo := repository.NewMockUserRepository(t)
	counterRepo := repository.NewMockCounterRepository(t)

	// Mock the GetNextSequence method to return an error
	counterRepo.On("GetNextSequence", mock.Anything, ReferralCodeCounterName).Return(int64(0), fmt.Errorf("counter error"))

	// Mock the Count method to simulate no existing codes
	userRepo.On("Count", mock.Anything, mock.Anything).Return(int64(0), nil)

	s := &service{
		userRepo:    userRepo,
		counterRepo: counterRepo,
	}

	// Test the unique code generation with fallback
	code, err := s.GenerateUniqueReferralCode(context.Background())
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	// Verify the code format
	if len(code) != 8 {
		t.Errorf("Expected code length to be 8, got %d: %s", len(code), code)
	}

	// Verify that the counter method was called
	counterRepo.AssertCalled(t, "GetNextSequence", mock.Anything, ReferralCodeCounterName)

	// Verify that Count was called
	userRepo.AssertCalled(t, "Count", mock.Anything, mock.Anything)
}
