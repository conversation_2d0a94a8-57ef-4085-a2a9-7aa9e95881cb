package userResolution

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"

	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) CheckIfPledgeTaken(ctx context.Context) (*bool, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return utils.AllocPtr(false), fmt.Errorf("Failed to Get User ID from context ")
	}

	_, err = s.userResolutionRepository.FindByUserID(ctx, userId)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return utils.AllocPtr(false), nil
		}

		return utils.AllocPtr(false), err
	}

	return utils.AllocPtr(true), nil
}
