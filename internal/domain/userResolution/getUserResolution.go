package userResolution

import (
	"context"
	"errors"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) GetUserResolution(ctx context.Context) (*models.UserResolution, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Failed to Get User ID from context ")
	}

	usersResolution, err := s.userResolutionRepository.FindByUserID(ctx, userId)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return usersResolution, nil
}
