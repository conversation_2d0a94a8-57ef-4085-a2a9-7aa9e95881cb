package userResolution

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type service struct {
	userResolutionRepository repository.UserResolutionRepository
}

func NewUserResolutionService(lc fx.Lifecycle, repositoryFactory *repository.RepositoryFactory,
) domain.UserResolutionStore {
	s := &service{
		userResolutionRepository: repositoryFactory.UserResolutionRepository,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting user resolution service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down user resolution service")
			return nil
		},
	})

	return s
}
