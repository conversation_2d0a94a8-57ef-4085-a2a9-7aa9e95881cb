package userResolution

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) TakePledge(ctx context.Context, duration *int) (*bool, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return utils.AllocPtr(false), fmt.Errorf("Failed to Get User ID from context ")
	}

	savedUserResolution, err := s.userResolutionRepository.FindByUserID(ctx, userId)

	if err != nil && err != mongo.ErrNoDocuments {
		return utils.AllocPtr(false), err
	}

	if savedUserResolution != nil {
		return utils.AllocPtr(false), nil
	}

	userResolution := &models.UserResolution{
		ID:        primitive.NewObjectID(),
		Duration:  *duration,
		UserID:    userId,
		CreatedAt: utils.AllocPtr(time.Now()),
	}

	_, err = s.userResolutionRepository.InsertOne(ctx, userResolution)
	if err != nil {
		return utils.AllocPtr(false), err
	}

	return utils.AllocPtr(true), nil
}
