package userStreak

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/zap"
)

func (s *service) CheckUserStreakStatus(ctx context.Context) (*models.StreakStatusResponse, error) {
	userID, err := utils.GetUserFromContext(ctx)
	zlog.Info(ctx, "Checking User Streak Status", zap.String("userID", userID.Hex()))
	if err != nil {
		zlog.Error(ctx, "cannot get userID from context", err)
		return nil, err
	}

	user, err := s.coreService.GetUserByID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "cannot get user by ID", err)
		return nil, err
	}

	response := &models.StreakStatusResponse{
		HasStreak:       false,
		StreakFreezers:  0,
		MissedDays:      0,
		CanSaveStreak:   false,
		LostStreakCount: 0,
		LastSevenDays:   getDefaultLastSevenDays(ctx),
	}

	if user.UserStreaks == nil {
		user.UserStreaks = &models.UserStreaks{}
	}

	streakFreezers := user.UserStreaks.StreakFreezers
	response.StreakFreezers = streakFreezers

	userTime := utils.GetUserTime(ctx, time.Now())
	normalizedDate := utils.GetUserDate(ctx, time.Now())

	lastPlayedDate := utils.GetUserDate(ctx, user.UserStreaks.LastPlayedDate)

	userStreak, err := s.userStreakRepo.GetStreakHistoryByUserID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "failed to get user streak history", err)
		return nil, err
	}

	if userStreak == nil {
		return response, nil
	}

	yesterdayDate := utils.GetUserDate(ctx, normalizedDate.AddDate(0, 0, -1))

	yesterdayExists := containsDate(ctx, userStreak.StreakHistoryObj, yesterdayDate)

	daysSinceLastPlayed := int(userTime.Sub(lastPlayedDate).Hours() / 24)

	hasStreak := daysSinceLastPlayed <= 1
	response.HasStreak = hasStreak

	var missedDays int
	if daysSinceLastPlayed > 1 {
		missedDays = daysSinceLastPlayed - 1
	} else {
		missedDays = 0
	}
	response.MissedDays = missedDays

	canSaveStreak := streakFreezers >= missedDays && missedDays > 0 && missedDays <= 2
	response.CanSaveStreak = canSaveStreak

	if canSaveStreak {
		response.LostStreakCount = calculateCurrentStreak(ctx, userStreak.StreakHistoryObj, true)
	}

	lastSevenDays := make([]models.StreakDay, 7)

	if user.UserStreaks.LastSevenDays == nil || len(user.UserStreaks.LastSevenDays) < 7 {
		user.UserStreaks.LastSevenDays = make([]bool, 7)
	}

	for i := 0; i < 7; i++ {
		date := normalizedDate.AddDate(0, 0, -i)
		dateCopy := date
		activity := false
		if i < len(user.UserStreaks.LastSevenDays) {
			activity = user.UserStreaks.LastSevenDays[6-i]
		}

		lastSevenDays[i] = models.StreakDay{
			Date:     dateCopy,
			Activity: activity,
		}
	}
	for i, j := 0, len(lastSevenDays)-1; i < j; i, j = i+1, j-1 {
		lastSevenDays[i], lastSevenDays[j] = lastSevenDays[j], lastSevenDays[i]
	}
	response.LastSevenDays = lastSevenDays

	if yesterdayExists {
		response.CanSaveStreak = false
		return response, nil
	}

	return response, nil
}

func getDefaultLastSevenDays(ctx context.Context) []models.StreakDay {
	normalizedDate := utils.GetUserDate(ctx, time.Now())
	days := make([]models.StreakDay, 7)

	for i := 0; i < 7; i++ {
		date := normalizedDate.AddDate(0, 0, -(6 - i))
		days[i] = models.StreakDay{
			Date:         date,
			Activity:     false,
			IsShieldUsed: false,
		}
	}
	return days
}
