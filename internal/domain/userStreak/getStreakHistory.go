package userStreak

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/zap"
)

func (s *service) GetUserStreakHistoryByMonth(ctx context.Context, yearMonths []string) ([]*models.StreakEntry, error) {
	userID, err := utils.GetUserFromContext(ctx)
	zlog.Info(ctx, "GetUserStreakHistoryByMonth", zap.String("userID", userID.Hex()))
	if err != nil {
		zlog.Info(ctx, "failed to get user from context")
		return nil, err
	}

	userStreak, err := s.userStreakRepo.GetStreakHistoryByUserID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "failed to get user streak", err)
		return nil, err
	}

	streaks, err := getStreakHistoryByMonth(ctx, userStreak, yearMonths)
	if err != nil {
		zlog.Error(ctx, "failed to get user streak history", err)
		return nil, err
	}

	return streaks, nil
}

func getStreakHistoryByMonth(ctx context.Context, streakHistory *models.StreakHistory, yearMonths []string) ([]*models.StreakEntry, error) {
	yearMonthMap := make(map[string]bool)
	for _, ym := range yearMonths {
		yearMonthMap[ym] = true
	}

	var filteredHistory []*models.StreakEntry

	for _, date := range streakHistory.StreakHistoryObj {
		yearMonth := date.Date.Format("2006-01")
		if yearMonthMap[yearMonth] {
			filteredHistory = append(filteredHistory, &models.StreakEntry{
				Date:         date.Date,
				IsShieldUsed: date.IsShieldUsed,
			})
		}
	}

	return filteredHistory, nil
}
