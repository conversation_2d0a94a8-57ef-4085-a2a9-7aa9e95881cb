package userStreak

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/zap"
)

func (s *service) GetUserUpdatedStreak(ctx context.Context) (*models.UserStreaks, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Failed to get user ID", err)
		return nil, fmt.Errorf("unauthorized: user not authenticated")
	}
	err = UpdateUserStreak(ctx, userId, s.userRepo, false, s.notificationService, s.coreService, s.userStreakRepo)
	if err != nil {
		zlog.Error(ctx, "Failed to update user streak", err)
		return nil, err
	}

	user, err := s.userRepo.GetByID(ctx, userId)
	if err != nil {
		zlog.Error(ctx, "Failed to get user by ID", err)
		return nil, err
	}
	if user == nil {
		zlog.Error(ctx, "User not found", nil)
		return nil, fmt.Errorf("user not found")
	}
	if user.UserStreaks == nil {
		zlog.Error(ctx, "User streaks not found", fmt.Errorf("user streaks not initialized"))
		return nil, fmt.Errorf("user streaks not found")
	}

	zlog.Info(ctx, "User streaks updated successfully", zap.String("userID", userId.Hex()))
	return user.UserStreaks, nil
}
