package userStreak

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type service struct {
	userRepo                       repository.UserRepository
	userStreakRepo                 repository.UserStreakRepository
	notificationService            domain.NotificationStore
	coreService                    domain.CoreLogicStore
	streakShieldTransactionService domain.StreakShieldTransactionStore
}

func NewUserStreakService(lc fx.Lifecycle, repositoryFactory *repository.RepositoryFactory,
	notificationService domain.NotificationStore, coreService domain.CoreLogicStore,
	streakShieldTransactionService domain.StreakShieldTransactionStore,
) domain.UserStreakStore {
	s := &service{
		userRepo:                       repositoryFactory.UserRepository,
		userStreakRepo:                 repositoryFactory.UserStreakRepository,
		notificationService:            notificationService,
		coreService:                    coreService,
		streakShieldTransactionService: streakShieldTransactionService,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting user streak service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down user streak service")
			return nil
		},
	})

	return s
}
