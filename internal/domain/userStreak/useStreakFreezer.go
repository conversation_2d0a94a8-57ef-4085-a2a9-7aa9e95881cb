package userStreak

import (
	"context"
	"fmt"
	"slices"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func (s *service) UseStreakFreezer(ctx context.Context) (bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, nil
	}
	zlog.Info(ctx, "Using Streak Freezer for User", zap.String("userID", userID.Hex()))

	user, err := s.coreService.GetUserByID(ctx, userID)
	if err != nil || user == nil {
		zlog.Error(ctx, "Failed to get user by ID", err)
		return false, err
	}

	if user.IsBot != nil && *user.IsBot {
		zlog.Info(ctx, "User is a bot, skipping streak freezer usage", zap.String("userID", userID.Hex()))
		return false, nil
	}

	if user.UserStreaks.StreakFreezers == 0 {
		return false, fmt.Errorf("no streak freezers")
	}

	userStreak, err := s.userStreakRepo.GetStreakHistoryByUserID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get user streakHisoryObj by ID", err)
		return false, err
	}

	if user.UserStreaks == nil {
		user.UserStreaks = &models.UserStreaks{}
	}
	if user.UserStreaks.StreakHistory == nil {
		user.UserStreaks.StreakHistory = make([]*time.Time, 0)
	}
	if user.UserStreaks.LastSevenDays == nil {
		user.UserStreaks.LastSevenDays = make([]bool, 7)
	}

	todayDate := utils.GetUserDate(ctx, time.Now())

	yesterdayDate := utils.GetUserDate(ctx, todayDate.AddDate(0, 0, -1))

	yesterdayExists := containsDate(ctx, userStreak.StreakHistoryObj, yesterdayDate)
	if yesterdayExists {
		zlog.Info(ctx, "User already has a streak", zap.String("userID", userID.Hex()))
		return false, fmt.Errorf("user already has a streak")
	}

	lastPlayedDate := user.UserStreaks.LastPlayedDate
	daysSinceLastPlayed := int(todayDate.Sub(lastPlayedDate).Hours() / 24)

	if daysSinceLastPlayed < 1 {
		return false, fmt.Errorf("user has a streak")
	}

	if daysSinceLastPlayed > 3 {
		return false, fmt.Errorf("cannot apply streak for 3 consecutive days")
	}

	if daysSinceLastPlayed > 0 {
		daysToFreeze := min(daysSinceLastPlayed, 2)

		// if daysToFreeze > user.UserStreaks.StreakFreezers {
		// 	return false, fmt.Errorf("not enough streak Freezers")
		// }

		daysToFreeze = min(daysToFreeze, user.UserStreaks.StreakFreezers)

		if daysToFreeze <= 0 {
			return false, fmt.Errorf("no days to freeze")
		}

		tempDates := make([]*models.StreakEntry, 0, daysToFreeze)

		for i := 1; i <= daysToFreeze; i++ {
			missedDate := utils.GetUserDate(ctx, todayDate.AddDate(0, 0, -i))

			if containsDate(ctx, userStreak.StreakHistoryObj, missedDate) {
				continue
			}

			streakEntry := &models.StreakEntry{
				Date:         missedDate,
				IsShieldUsed: true,
			}
			tempDates = append(tempDates, streakEntry)

			user.UserStreaks.StreakFreezers--
		}

		// Create a streak shield transaction to record the usage
		debitedType := models.TransactionTypeDebited
		_, err = s.streakShieldTransactionService.CreateTransaction(
			ctx,
			userID,
			daysToFreeze,
			debitedType,
			nil,
			nil,
			nil,
		)
		if err != nil {
			zlog.Error(ctx, "Failed to create streak shield transaction", err)
			// Continue even if transaction creation fails, as this is a secondary operation
		}

		for i, j := 0, len(tempDates)-1; i < j; i, j = i+1, j-1 {
			tempDates[i], tempDates[j] = tempDates[j], tempDates[i]
		}

		userStreak.StreakHistoryObj = append(userStreak.StreakHistoryObj, tempDates...)

		user.UserStreaks.CurrentStreak = calculateCurrentStreak(ctx, userStreak.StreakHistoryObj, false)

		if user.UserStreaks.CurrentStreak > user.UserStreaks.LongestStreak {
			user.UserStreaks.LongestStreak = user.UserStreaks.CurrentStreak
		}

		newLastSevenDays := make([]bool, 7)
		for i := 0; i < 7; i++ {
			checkDate := todayDate.AddDate(0, 0, -i)
			newLastSevenDays[6-i] = containsDate(ctx, userStreak.StreakHistoryObj, checkDate)
		}
		user.UserStreaks.LastSevenDays = newLastSevenDays

		if daysToFreeze > 0 {
			err = s.userRepo.UpdateOne(ctx, bson.M{"_id": userID}, bson.M{"$set": bson.M{"userStreaks": user.UserStreaks}})
			if err != nil {
				zlog.Error(ctx, "Failed to update user streak object", err)
				return false, err
			}

			err = s.userStreakRepo.UpsertStreakHistoryEntries(ctx, userID, userStreak.StreakHistoryObj)
			if err != nil {
				zlog.Error(ctx, "Failed to upsert streak history entries", err)
				return false, err
			}
		}
	}

	return true, nil
}

func calculateCurrentStreak(ctx context.Context, history []*models.StreakEntry, countToday bool) int {
	if len(history) == 0 {
		return 0
	}

	sortedHistory := make([]*models.StreakEntry, len(history))
	copy(sortedHistory, history)

	slices.SortFunc(sortedHistory, func(a, b *models.StreakEntry) int {
		return b.Date.Compare(a.Date)
	})

	streak := 0
	if countToday {
		streak++
	}
	if len(sortedHistory) > 0 {
		currentDate := utils.GetUserDate(ctx, sortedHistory[0].Date)
		for i := 1; i < len(sortedHistory); i++ {
			prevDate := utils.GetUserDate(ctx, sortedHistory[i].Date)
			diff := currentDate.Sub(prevDate).Hours() / 24
			if sortedHistory[i].IsShieldUsed {
				currentDate = prevDate
				continue
			}
			if diff == 1 {
				streak++
			} else {
				break
			}
			currentDate = prevDate
		}
	}

	return streak
}
