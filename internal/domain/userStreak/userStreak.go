package userStreak

import (
	"context"
	"fmt"
	"math/rand/v2"
	"slices"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/models"
	stringUtils "matiksOfficial/matiks-server-go/utils/string"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func updateUserStreakObject(ctx context.Context, userStreaks *models.UserStreaks, userStreakHistory *[]*models.StreakEntry, playedToday bool) bool {
	if *userStreakHistory == nil {
		*userStreakHistory = make([]*models.StreakEntry, 0)
	}
	if userStreaks.LastSevenDays == nil {
		userStreaks.LastSevenDays = make([]bool, 7)
	}

	normalizedDate := utils.GetUserDate(ctx, time.Now())
	userTime := utils.GetUserTime(ctx, time.Now())

	lastPlayedDate := utils.GetUserDate(ctx, userStreaks.LastPlayedDate)

	daysSinceLastPlayed := int(userTime.Sub(lastPlayedDate).Hours() / 24)

	zlog.Debug(ctx, "Updating User Streak", zap.Int("daysSinceLastPlayed", daysSinceLastPlayed),
		zap.String("lastPlayedDate", lastPlayedDate.String()),
		zap.String("normalizedDate", normalizedDate.String()),
		zap.String("userTime", userTime.String()),
		zap.String("playedToday", fmt.Sprintf("%v", playedToday)),
		zap.String("user timezone", utils.GetUserTimezoneFromContext(ctx)))

	newLastSevenDays := make([]bool, 7)
	for i := 0; i < 7; i++ {
		checkDate := utils.GetUserDate(ctx, normalizedDate.AddDate(0, 0, -i))
		newLastSevenDays[6-i] = containsDate(ctx, *userStreakHistory, checkDate)
	}
	userStreaks.LastSevenDays = newLastSevenDays

	zlog.Debug(ctx, "Updated Last Seven Days", zap.Any("lastSevenDays", userStreaks.LastSevenDays))

	isStreakMaintained := checkStreakMaintained(ctx, *userStreakHistory, userTime, daysSinceLastPlayed)

	if playedToday {
		userStreaks.LastSevenDays[6] = true
		if daysSinceLastPlayed == 0 {
			zlog.Debug(ctx, "Played today but no days since last played")
			return false
		} else if isStreakMaintained {
			userStreaks.CurrentStreak++
		} else {
			userStreaks.CurrentStreak = 1
		}
		userStreaks.LastPlayedDate = normalizedDate
		streakHistory := models.StreakEntry{
			Date:         normalizedDate,
			IsShieldUsed: false,
		}
		*userStreakHistory = append(*userStreakHistory, &streakHistory)
		if userStreaks.CurrentStreak > userStreaks.LongestStreak {
			userStreaks.LongestStreak = userStreaks.CurrentStreak
		}
		return true
	} else {
		if !isStreakMaintained {
			userStreaks.CurrentStreak = 0
			return true
		}
		if daysSinceLastPlayed == 0 {
			return false
		}
		return true
	}
}

func containsDate(ctx context.Context, history []*models.StreakEntry, date time.Time) bool {
	if history == nil || len(history) == 0 {
		return false
	}
	for _, t := range slices.Backward(history[max(0, len(history)-7):]) {
		if t.Date.Equal(date) {
			return true
		}
	}
	return false
}

func UpdateUserStreak(
	ctx context.Context, userID primitive.ObjectID,
	userRepo repository.UserRepository, playedToday bool,
	notificationService domain.NotificationStore,
	coreService domain.CoreLogicStore,
	userStreakRepo repository.UserStreakRepository,
) error {
	user, err := coreService.GetUserByID(ctx, userID)
	zlog.Info(ctx, "Updating user streak for user ID", zap.String("userID", userID.Hex()))
	if err != nil || user == nil {
		return err
	}

	if user.IsBot != nil && *user.IsBot {
		zlog.Info(ctx, "User is a bot, skipping streak update")
		return nil
	}

	if user.UserStreaks == nil {
		user.UserStreaks = &models.UserStreaks{}
	}

	var streakHistory []*models.StreakEntry

	userStreak, err := userStreakRepo.GetStreakHistoryByUserID(ctx, userID)
	if err == nil && userStreak != nil {
		streakHistory = userStreak.StreakHistoryObj
	}
	if streakHistory == nil {
		streakHistory = make([]*models.StreakEntry, 0)
	}

	update := updateUserStreakObject(ctx, user.UserStreaks, &streakHistory, playedToday)

	if update {
		err = userRepo.UpdateOne(ctx, bson.M{"_id": userID}, bson.M{"$set": bson.M{"userStreaks": user.UserStreaks}})
		if err != nil {
			zlog.Error(ctx, "Failed to update user streak", err)
			return err
		}

		err = userStreakRepo.UpsertStreakHistoryEntries(ctx, userID, streakHistory)
		if err != nil {
			zlog.Error(ctx, "Failed to upsert streak history entries", err)
			return err
		}

		// Publishing User Event for Streak Maintain
		if playedToday {
			zlog.Info(ctx, "Publishing User Event for Streak Maintain", zap.String("userID", userID.Hex()))

			if err := SendFeedEvent(ctx, notificationService, userID, user); err != nil {
				zlog.Error(ctx, "Failed to send feed event", err)
			}

			err = coreService.PublishUserEvent(ctx, userID, models.StreakMaintained{
				IsPlayedToday: playedToday,
			})
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func checkStreakMaintained(ctx context.Context, streakHistory []*models.StreakEntry, userTime time.Time, daysSinceLastPlayed int) bool {
	yesterdayDate := utils.GetUserDate(ctx, userTime.AddDate(0, 0, -1))

	// checking if the streak is maintained by streak shield
	hasYesterdayDate := containsDate(ctx, streakHistory, yesterdayDate)

	if daysSinceLastPlayed <= 1 || daysSinceLastPlayed == 2 && hasYesterdayDate || daysSinceLastPlayed == 3 && hasYesterdayDate {
		return true
	}
	return false
}

func SendFeedEvent(ctx context.Context, notificationService domain.NotificationStore, userID primitive.ObjectID, user *models.User) error {
	if user == nil {
		zlog.Error(ctx, "User not found", systemErrors.ErrUserNil)
		return nil
	}
	if user.UserStreaks == nil {
		zlog.Error(ctx, "User streaks not found", systemErrors.ErrUserStreaksNil)
		return nil
	}

	if !slices.Contains([]int{7, 21, 50, 100, 150, 200, 500}, user.UserStreaks.CurrentStreak) {
		return nil
	}
	randomCongratulate := []string{"Hurrah!", "Congratulations!", "Amazing!", "Fantastic!"}
	i := rand.IntN(len(randomCongratulate))
	word := randomCongratulate[i]

	zlog.Info(ctx, "Publishing User Event for Streak Maintain", zap.String("userID", userID.Hex()))
	err := notificationService.SendNotification(ctx, &models.UserNotification{
		UserID: userID,
		Type:   models.NotificationTypeFeed,
		Data: map[string]string{
			"type": "streak_maintained",
		},
		SentAt:   time.Now(),
		ImageUrl: user.ProfileImageURL,
		Title:    "@Matiks",
		Body:     fmt.Sprintf("%s You have maintained a %d day streak!", word, user.UserStreaks.CurrentStreak),
		Feed: &models.FeedNotificationParams{
			FeedType: models.FeedTypeCelebration,
			FeedForFriends: &models.FeedForFriends{
				Title: stringUtils.Concat("@", user.Username),
				Body:  fmt.Sprintf("%s Your friend has maintained a %d day streak!", word, user.UserStreaks.CurrentStreak),
			},
		},
	})
	if err != nil {
		zlog.Error(ctx, "Failed to send feed event", err)
		return err
	}
	return nil
}
