package usersettings

import (
	"context"
	"errors"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

var DefaultUserSettings = models.UserSettings{
	PlaySound:      true,
	HapticFeedback: true,
}

func (s *service) GetUserSettings(ctx context.Context) (*models.UserSettings, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	zlog.Info(ctx, "Getting user settings", zap.String("userID", userID.Hex()))

	settings, err := s.userSettingsRepo.GetUserSettings(ctx, userID)
	if err != nil {
		if !errors.Is(err, mongo.ErrNoDocuments) {
			return nil, err
		}
		settings = &models.UserSettings{
			UserID:         userID,
			PlaySound:      DefaultUserSettings.PlaySound,
			HapticFeedback: DefaultUserSettings.HapticFeedback,
		}
		return settings, nil
	}

	return settings, nil
}
