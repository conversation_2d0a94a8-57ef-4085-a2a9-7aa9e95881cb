package usersettings

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type service struct {
	userSettingsRepo repository.UserSettingsRepository
}

func NewUserSettingsService(lc fx.Lifecycle, repositoryFactory *repository.RepositoryFactory,
) domain.UserSettingsStore {
	s := &service{
		userSettingsRepo: repositoryFactory.UserSettingsRepository,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting user settings service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down user settings service")
			return nil
		},
	})

	return s
}
