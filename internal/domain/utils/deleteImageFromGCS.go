package utils

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strings"

	"matiksOfficial/matiks-server-go/internal/constants"

	"cloud.google.com/go/storage"
)

func DeleteImageFromGCS(ctx context.Context, imageURL string, storageClient *storage.Client) error {
	parsedURL, err := url.Parse(imageURL)
	if err != nil {
		return fmt.Errorf("failed to parse image URL: %w", err)
	}

	objectName := strings.TrimPrefix(parsedURL.Path, "/")
	objectName = strings.Split(objectName, "?")[0]

	bucket := storageClient.Bucket(constants.StorageBucket)
	object := bucket.Object(objectName)

	if err := object.Delete(ctx); err != nil {
		if errors.Is(err, storage.ErrObjectNotExist) {
			return fmt.Errorf("object %s not found in bucket %s", objectName, constants.StorageBucket)
		}
		return fmt.Errorf("failed to delete object %s from GCS: %w", objectName, err)
	}

	return nil
}
