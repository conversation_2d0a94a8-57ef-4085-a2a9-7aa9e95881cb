package encryption

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
)

// EncryptObject encrypts a JSON-serializable object using AES-256-CBC
// in a format compatible with CryptoJS decryption
func EncryptObject(jsonObject interface{}) (string, error) {
	secretKey := []byte("S1E9C5R@E@T*K)E(YS1E9C5R^E@T*K)E")

	// Convert object to JSON string
	jsonString, err := json.Marshal(jsonObject)
	if err != nil {
		return "", fmt.Errorf("failed to marshal JSON: %w", err)
	}

	// Create cipher block
	block, err := aes.NewCipher(secretKey)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %w", err)
	}

	// Generate random IV
	iv := make([]byte, aes.BlockSize)
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("failed to generate IV: %w", err)
	}

	// Pad the plaintext to a multiple of the block size (PKCS7)
	padding := aes.BlockSize - (len(jsonString) % aes.BlockSize)
	paddedText := make([]byte, len(jsonString)+padding)
	copy(paddedText, jsonString)
	for i := len(jsonString); i < len(paddedText); i++ {
		paddedText[i] = byte(padding)
	}

	// Create CBC encrypter
	mode := cipher.NewCBCEncrypter(block, iv)

	// Encrypt the padded text
	encrypted := make([]byte, len(paddedText))
	mode.CryptBlocks(encrypted, paddedText)

	// Format as IV:encrypted in hex format
	return fmt.Sprintf("%s:%s", hex.EncodeToString(iv), hex.EncodeToString(encrypted)), nil
}
