package utils

import (
	"context"
	"fmt"

	"go.opentelemetry.io/otel/trace"

	"matiksOfficial/matiks-server-go/internal/constants"
)

func GetServiceNameFromContext(ctx context.Context) string {
	value, ok := ctx.Value(constants.ServiceName).(string)
	if !ok {
		return ""
	}
	return value
}

func GetSpanFromContext(ctx context.Context) (trace.Span, error) {
	value, ok := ctx.Value(constants.SpanContextKey).(trace.Span)
	if !ok {
		return nil, fmt.Errorf("span not found in context")
	}
	return value, nil
}
