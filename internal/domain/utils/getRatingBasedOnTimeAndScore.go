package utils

func GetRatingBasedOnFixtureTest(timeTaken, score int) int {
	if score >= 20 {
		if timeTaken <= 30 {
			return 2000
		} else if timeTaken <= 35 {
			return 1900
		} else if timeTaken <= 40 {
			return 1750
		} else if timeTaken <= 50 {
			return 1600
		} else if timeTaken <= 60 {
			return 1450
		} else if timeTaken <= 70 {
			return 1300
		} else if timeTaken <= 80 {
			return 1250
		} else if timeTaken <= 90 {
			return 1200
		} else {
			return 1000
		}
	} else {
		if score >= 18 {
			return 1500
		} else if score >= 15 {
			return 1400
		} else if score >= 13 {
			return 1300
		} else if score >= 11 {
			return 1200
		} else if score >= 10 {
			return 1100
		} else if score >= 8 {
			return 1000
		} else if score >= 6 {
			return 900
		} else if score >= 4 {
			return 800
		} else if score >= 3 {
			return 700
		} else {
			return 600
		}
	}
}
