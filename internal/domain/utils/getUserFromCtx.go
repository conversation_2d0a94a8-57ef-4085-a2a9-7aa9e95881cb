package utils

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"matiksOfficial/matiks-server-go/internal/constants"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetUserFromContext(ctx context.Context) (primitive.ObjectID, error) {
	val := ctx.Value(constants.UserContextKey)
	if val == nil {
		return primitive.NilObjectID, systemErrors.ErrUserNotAuthorized
	}
	userIdStr := ctx.Value(constants.UserContextKey).(string)

	if userIdStr == "" {
		return primitive.NilObjectID, fmt.Errorf("unauthorized: userID is empty")
	}

	userID, err := primitive.ObjectIDFromHex(userIdStr)
	if err != nil {
		return primitive.NilObjectID, fmt.Errorf("unauthorized: invalid userID: %w", err)
	}
	return userID, nil
}

func GetUserTimezoneFromContext(ctx context.Context) string {
	value, ok := ctx.Value(constants.UserTimezoneKey).(string)
	if !ok {
		return ""
	}
	return value
}

func GetUserDate(ctx context.Context, qtime time.Time, fallbackTimezone ...*string) time.Time {
	location := time.UTC
	timezone := GetUserTimezoneFromContext(ctx)
	if timezone == "" && len(fallbackTimezone) > 0 && fallbackTimezone[0] != nil {
		timezone = *fallbackTimezone[0]
	}
	loc, err := time.LoadLocation(timezone)
	if err == nil && loc != nil {
		location = loc
	}
	userTime := qtime.In(location)
	return time.Date(userTime.Year(), userTime.Month(), userTime.Day(), 0, 0, 0, 0, location)
}

func GetUserTime(ctx context.Context, qtime time.Time, fallbackTimezone ...*string) time.Time {
	location := time.UTC
	timezone := GetUserTimezoneFromContext(ctx)
	if timezone == "" && len(fallbackTimezone) > 0 && fallbackTimezone[0] != nil {
		timezone = *fallbackTimezone[0]
	}

	if timezone != "" {
		loc, err := time.LoadLocation(timezone)
		// If loading the location is successful, use the loaded location
		if err == nil && loc != nil {
			location = loc
		} else {
			// If an error occurs, fallback to UTC
			location = time.UTC
		}
	}
	return qtime.In(location)
}
