package utils

import (
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetUserPublicDetails(user *models.User) *models.UserPublicDetails {
	if user == nil {
		return nil
	}

	return &models.UserPublicDetails{
		ID:                  user.ID,
		Name:                user.Name,
		Username:            user.Username,
		ProfileImageURL:     user.ProfileImageURL,
		Rating:              user.Rating,
		Badge:               user.Badge,
		CountryCode:         user.CountryCode,
		IsGuest:             user.IsGuest,
		GlobalRank:          user.GlobalRank,
		CountryRank:         user.CountryRank,
		PreviousGlobalRank:  user.PreviousGlobalRank,
		PreviousCountryRank: user.PreviousCountryRank,
		Stats:               user.Stats,
		UserStreaks:         user.UserStreaks,
		RatingV2:            user.RatingV2,
	}
}

func GetMinifiedUserDetails(user *models.User) *models.UserPublicDetails {
	return &models.UserPublicDetails{
		ID:              user.ID,
		Username:        user.Username,
		ProfileImageURL: user.ProfileImageURL,
		Rating:          user.Rating,
	}
}

func ExtractUserPublicDetails(user bson.M) *models.UserPublicDetails {
	return &models.UserPublicDetails{
		ID:              user["_id"].(primitive.ObjectID),
		Name:            GetStringPtr(user, "name"),
		Username:        user["username"].(string),
		ProfileImageURL: GetStringPtr(user, "profileImageUrl"),
		Rating:          GetIntPtr(user, "rating"),
		Badge:           GetBadgeTypePtr(user, "badge"),
		GlobalRank:      GetIntPtr(user, "globalRank"),
		Stats:           ExtractUserStats(user["stats"]),
		UserStreaks:     ExtractUserStreaks(user["userStreaks"]),
		RatingV2:        ExtractRatingV2Details(user["ratingV2"]),
	}
}

func ExtractUserStats(stats interface{}) *models.UserStats {
	if stats == nil {
		return nil
	}
	statsMap := stats.(bson.M)
	return &models.UserStats{
		Ngp:             GetIntValue(statsMap, "ngp"),
		Hr:              GetIntValue(statsMap, "hr"),
		FollowersCount:  GetIntValue(statsMap, "followersCount"),
		FollowingsCount: GetIntValue(statsMap, "followingsCount"),
		FriendsCount:    GetIntValue(statsMap, "friendsCount"),
	}
}

func ExtractUserStreaks(streaks interface{}) *models.UserStreaks {
	if streaks == nil {
		return nil
	}
	streaksMap := streaks.(bson.M)
	return &models.UserStreaks{
		CurrentStreak:  GetIntValue(streaksMap, "currentStreak"),
		LongestStreak:  GetIntValue(streaksMap, "longestStreak"),
		LastPlayedDate: GetTimeValue(streaksMap, "lastPlayedDate"),
		LastSevenDays:  GetBoolSlice(streaksMap, "lastSevenDays"),
		StreakHistory:  GetTimeSlice(streaksMap, "streakHistory"),
	}
}

func ExtractRatingV2Details(data interface{}) *models.UserRating {
	if data == nil {
		return nil
	}

	ratingV2Data, ok := data.(bson.M)
	if !ok {
		return nil
	}

	return &models.UserRating{
		GlobalRating:       GetIntPtr(ratingV2Data, "globalRating"),
		FlashAnzanRating:   GetIntPtr(ratingV2Data, "flashAnzanRating"),
		AbilityDuelsRating: GetIntPtr(ratingV2Data, "abilityDuelsRating"),
	}
}
