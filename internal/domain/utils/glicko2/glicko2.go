package glicko2

import (
	"math"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	// System constants
	DefaultVolatility = 0.06     // Default volatility for new players
	DefaultRating     = 1000     // Default rating for new players
	DefaultRD         = 150      // Default rating deviation for new players
	MinRD             = 30       // Minimum rating deviation allowed
	MaxRD             = 250      // Maximum rating deviation allowed
	DefaultTau        = 0.5      // System constraint for volatility (0.3 to 1.2 recommended)
	Scale             = 173.7178 // Used to convert between Glicko-2 and original scale
	Convergence       = 0.000001 // Convergence criterion for volatility iteration

	// Margin of victory constants
	MaxSkewFactor          = 2.4  // Maximum multiplier for very decisive victories
	MinSkewFactor          = 0.1  // Minimum multiplier for extremely close matches
	MaxMargin              = 40.0 // The margin value that represents a completely one-sided match
	MarginSigmoidSteepness = 5.0  // Controls how steep the sigmoid curve is for margin calculation

	// RD adjustment constants
	RDDecayPerDay        = 5.0  // How much RD increases per day of inactivity
	MaxRDIncreaseDays    = 60.0 // Cap RD increase after this many days of inactivity
	RDDecreaseMultiplier = 1.2  // Multiplier to accelerate RD decrease for live updates

	// Volatility adjustment constants
	VolatilityAdjustmentFactor = 0.9  // Factor to adjust volatility for live updates
	MaxVolatility              = 0.1  // Maximum allowed volatility
	MinVolatility              = 0.04 // Minimum allowed volatility
)

// GlickoPlayer represents a player in the Glicko-2 rating system
type GlickoPlayer struct {
	// Public attributes - ALL of these must be persisted in your database
	ID          primitive.ObjectID // Unique identifier for the player
	Rating      float64            // Rating on the original Glicko scale
	RD          float64            // Rating deviation on the original Glicko scale
	Volatility  float64            // Volatility (sigma)
	LastUpdated int64              // Timestamp of last update (for RD calculation) - IMPORTANT: Must be persisted
	NumGames    int                // Number of games played - helps with RD calculation

	// Internal attributes - calculated during runtime, don't need to be stored
	mu  float64 // Rating on the Glicko-2 scale
	phi float64 // RD on the Glicko-2 scale
}

// MatchResult represents the outcome of a match
type MatchResult struct {
	Opponent *GlickoPlayer // Opponent in the match
	Score    float64       // 1.0 for win, 0.5 for draw, 0.0 for loss
	Margin   float64       // 0 for close match, up to MaxMargin for one-sided match
}

// NewPlayer creates a new player with default or specified ratings
func NewPlayer(id primitive.ObjectID, rating, rd, volatility float64, lastUpdated int64) *GlickoPlayer {
	if rating == 0 {
		rating = DefaultRating
	}
	if rd == 0 {
		rd = DefaultRD
	}
	if volatility == 0 {
		volatility = DefaultVolatility
	}
	if lastUpdated == 0 {
		lastUpdated = time.Now().UnixMilli()
	}

	// Convert to Glicko-2 scale
	mu := (rating - DefaultRating) / Scale
	phi := rd / Scale

	return &GlickoPlayer{
		ID:          id,
		Rating:      rating,
		RD:          rd,
		Volatility:  volatility,
		LastUpdated: lastUpdated,
		NumGames:    0,
		mu:          mu,
		phi:         phi,
	}
}

// LoadPlayer recreates a player object from stored data
func LoadPlayer(id primitive.ObjectID, rating, rd, volatility float64, lastUpdated int64) *GlickoPlayer {
	return NewPlayer(id, rating, rd, volatility, lastUpdated)
}

// g calculates the g function for a given RD
func g(phi float64) float64 {
	return 1.0 / math.Sqrt(1.0+3.0*phi*phi/math.Pi*math.Pi)
}

// E calculates the expected score
func E(mu, muJ, phi float64) float64 {
	return 1.0 / (1.0 + math.Exp(-g(phi)*(mu-muJ)))
}

// v calculates the estimated variance of the player's rating based on match outcomes
func v(g, E float64) float64 {
	return 1.0 / (g * g * E * (1 - E))
}

// delta calculates the estimated improvement in rating
func delta(v, g, E, score float64) float64 {
	return v * g * (score - E)
}

// calculateSkewFactor determines how much to amplify or reduce the rating change based on margin
// This is a custom extension to the Glicko2 algorithm to account for margin of victory
func calculateSkewFactor(margin, score float64) float64 {
	// Normalize margin to be between 0 and 1
	normalizedMargin := math.Min(margin, MaxMargin) / MaxMargin

	// For draws, don't apply skew factor
	if score == 0.5 {
		return 1.0
	}

	// Calculate skew factor: ranges from MinSkewFactor to MaxSkewFactor
	// Use a sigmoid function for a more natural curve that emphasizes mid-range margins
	// and reduces the impact of very small or very large margins
	// This creates a more balanced adjustment than linear interpolation

	// Center the sigmoid at 0.5 and adjust steepness with MarginSigmoidSteepness
	x := (normalizedMargin - 0.5) * MarginSigmoidSteepness
	sigmoid := 1.0 / (1.0 + math.Exp(-x))

	// Scale the sigmoid output to our desired range
	skewFactor := MinSkewFactor + sigmoid*(MaxSkewFactor-MinSkewFactor)

	// For losses (score=0), we want to reduce the rating change for large margins
	// This prevents excessive rating drops when losing badly to a much stronger player
	if score < 0.5 {
		// Invert the skew factor for losses, but keep it within reasonable bounds
		// This makes losses with large margins less punishing
		skewFactor = math.Max(MinSkewFactor, 1.0/skewFactor)
	}

	return skewFactor
}

// UpdateRating updates a player's rating after a single match
// This implementation is optimized for live rating updates without rating periods
func (p *GlickoPlayer) UpdateRating(result MatchResult, currentTime int64) {
	// First, adjust RD for time passed since last update
	if p.LastUpdated > 0 {
		// Convert milliseconds to days
		daysSinceLastUpdate := float64(currentTime-p.LastUpdated) / float64(24*60*60*1000)

		// Cap the days since last update to avoid excessive RD increase
		daysSinceLastUpdate = math.Min(daysSinceLastUpdate, MaxRDIncreaseDays)

		// Calculate RD increase based on time passed and player experience
		// More experienced players (more games) have slower RD increase
		rdIncreaseFactor := 1.0
		if p.NumGames > 50 {
			rdIncreaseFactor = 0.4 // Very established players' RD increases very slowly
		} else if p.NumGames > 30 {
			rdIncreaseFactor = 0.5 // Established players' RD increases more slowly
		} else if p.NumGames > 10 {
			rdIncreaseFactor = 0.75 // Somewhat established players
		}

		// Calculate RD increase: more days = more increase
		rdIncrease := RDDecayPerDay * daysSinceLastUpdate * rdIncreaseFactor

		// Apply the increase, but don't exceed MaxRD
		p.RD = math.Min(MaxRD, p.RD+rdIncrease)

		// Convert to Glicko-2 scale
		p.phi = p.RD / Scale
	}

	// Increment game count
	p.NumGames++

	// Convert opponent to Glicko-2 scale
	oppMu := (result.Opponent.Rating - DefaultRating) / Scale
	oppPhi := result.Opponent.RD / Scale

	// Calculate intermediate values
	gOpp := g(oppPhi)
	expScore := E(p.mu, oppMu, oppPhi)
	vValue := v(gOpp, expScore)

	// Calculate delta with skew factor based on margin
	skewFactor := calculateSkewFactor(result.Margin, result.Score)
	deltaValue := delta(vValue, gOpp, expScore, result.Score) * skewFactor

	// Calculate rating difference to determine if this is a match against a much stronger/weaker player
	ratingDiff := math.Abs(p.Rating - result.Opponent.Rating)
	ratingDiffFactor := math.Min(1.0, 400.0/math.Max(1.0, ratingDiff))

	// Adjust delta based on rating difference - reduce impact of matches against much stronger/weaker players
	deltaValue *= ratingDiffFactor

	// Update volatility using an optimized algorithm for live updates
	// This is a more efficient implementation of the iterative algorithm from Glickman's paper
	a := math.Log(p.Volatility * p.Volatility)
	deltaSquaredOverV := deltaValue * deltaValue / vValue
	phiSquared := p.phi * p.phi

	// Initial values for iteration
	A := a
	B := 0.0

	// Set initial bounds for the algorithm
	if deltaSquaredOverV > phiSquared+vValue {
		B = math.Log(deltaSquaredOverV - phiSquared - vValue)
	} else {
		k := 1.0
		for {
			B = a - k*math.Abs(DefaultTau)
			denom := phiSquared + vValue + math.Exp(B)
			if deltaSquaredOverV/denom < 1.0 {
				break
			}
			k++
		}
	}

	// Use the Illinois algorithm for root finding (more efficient than bisection)
	fA := volatilityFunction(A, p.phi, vValue, deltaValue, DefaultTau, a)
	fB := volatilityFunction(B, p.phi, vValue, deltaValue, DefaultTau, a)

	// Use a standard bisection method for root finding - more reliable than Illinois
	maxIterations := 100 // Prevent infinite loops
	iterations := 0

	// Ensure initial bounds have opposite signs
	if fA*fB > 0 {
		// If both functions have the same sign, adjust B until we find a bracket
		k := 1.0
		for fA*fB > 0 && k < 50 { // Limit iterations to prevent infinite loop
			B = a - k*math.Abs(DefaultTau)
			fB = volatilityFunction(B, p.phi, vValue, deltaValue, DefaultTau, a)
			k++
		}
	}

	// Standard bisection method
	for math.Abs(B-A) > Convergence && iterations < maxIterations {
		iterations++

		// Calculate midpoint
		C := (A + B) / 2
		fC := volatilityFunction(C, p.phi, vValue, deltaValue, DefaultTau, a)

		// Update bounds based on function values
		if fC*fB <= 0 {
			A = B
			fA = fB
		} else {
			fA = fA / 2 // Illinois method adjustment for faster convergence
		}

		B = C
		fB = fC
	}

	// New volatility with adjustment for live updates
	newVolatility := math.Exp(A/2) * VolatilityAdjustmentFactor

	// Ensure volatility stays within reasonable bounds
	newVolatility = math.Max(MinVolatility, math.Min(MaxVolatility, newVolatility))

	// Calculate new phi
	phiStar := math.Sqrt(phiSquared + newVolatility*newVolatility)

	// Calculate new phi' and mu'
	newPhi := 1.0 / math.Sqrt(1.0/phiStar/phiStar+1.0/vValue)

	// Apply skew factor to the final rating update
	newMu := p.mu + newPhi*newPhi*gOpp*(result.Score-expScore)*skewFactor

	// Update player's rating values
	p.Volatility = newVolatility
	p.mu = newMu

	// Enhanced RD update calculation optimized for live updates
	// This accelerates RD decrease for active players

	// Base RD update from Glicko2
	standardNewPhi := newPhi

	// Calculate a dynamic RD decrease factor based on current RD, number of games, and match outcome
	// Higher RD and fewer games = faster decrease
	rdDecreaseFactor := 0.0
	if p.RD > 300 {
		rdDecreaseFactor = 0.30 // 30% faster decrease for very high RD
	} else if p.RD > 250 {
		rdDecreaseFactor = 0.25 // 25% faster decrease for high RD
	} else if p.RD > 200 {
		rdDecreaseFactor = 0.20 // 20% faster decrease for medium-high RD
	} else if p.RD > 150 {
		rdDecreaseFactor = 0.15 // 15% faster decrease for medium RD
	} else if p.RD > 100 {
		rdDecreaseFactor = 0.10 // 10% faster decrease for medium-low RD
	} else if p.RD > 50 {
		rdDecreaseFactor = 0.05 // 5% faster decrease for low RD
	} else {
		rdDecreaseFactor = 0.0 // No adjustment for very low RD
	}

	// Adjust RD decrease based on match outcome
	// Winning against stronger players or losing against weaker players provides more information
	// about a player's true skill, so we decrease RD more in these cases
	expectedOutcome := expScore > 0.5
	actualOutcome := result.Score > 0.5
	if expectedOutcome != actualOutcome {
		// Unexpected outcome - increase RD decrease
		rdDecreaseFactor += 0.05
	}

	// Apply the RD decrease factor with the live update multiplier
	adjustedPhi := standardNewPhi * (1.0 - rdDecreaseFactor*RDDecreaseMultiplier)

	// Ensure RD doesn't go below MinRD
	minPhi := MinRD / Scale

	// Use the adjusted phi value
	p.phi = math.Max(minPhi, adjustedPhi)

	// Convert back to original Glicko scale
	p.Rating = p.mu*Scale + DefaultRating
	p.RD = p.phi * Scale

	// Update timestamp - IMPORTANT: This must be saved to the database
	p.LastUpdated = currentTime
}

// volatilityFunction is the function that needs to be solved to find new volatility
// This implements the function f(x) from step 5.1 in Glickman's paper
// Optimized for numerical stability and performance
func volatilityFunction(x, phi, v, delta, tau, a float64) float64 {
	// Implementation directly from Glickman's paper
	ex := math.Exp(x)
	phiSquared := phi * phi
	deltaSquared := delta * delta

	// First term: (e^x * (delta^2 - phi^2 - v - e^x)) / (2 * (phi^2 + v + e^x)^2)
	term1Numerator := ex * (deltaSquared - phiSquared - v - ex)
	term1Denominator := 2.0 * math.Pow(phiSquared+v+ex, 2)

	// Prevent division by zero or very small numbers
	if math.Abs(term1Denominator) < 1e-10 {
		term1Denominator = 1e-10 * math.Copysign(1, term1Denominator)
	}

	term1 := term1Numerator / term1Denominator

	// Second term: (x - a) / tau^2
	term2 := (x - a) / (tau * tau)

	// The function is term1 - term2
	return term1 - term2
}

// NewGlickoPlayer creates a GlickoPlayer from a User model
// If ratingType is specified, it will use the appropriate rating from the user's RatingV2 field
func NewGlickoPlayer(user *models.User, ratingType string) *GlickoPlayer {
	// If user has RatingV2 with GlobalRating and attributes, use those
	if user.RatingV2 != nil && user.RatingV2.GlobalRating != nil && user.RatingV2.GlobalRatingAttributes != nil {
		// Create player with existing rating data
		player := NewPlayer(
			user.ID,
			float64(*user.RatingV2.GlobalRating),
			user.RatingV2.GlobalRatingAttributes.RD,
			user.RatingV2.GlobalRatingAttributes.Volatility,
			user.RatingV2.GlobalRatingAttributes.LastUpdated,
		)

		// Estimate number of games based on RD (lower RD = more games)
		if user.RatingV2.GlobalRatingAttributes.RD < 100 {
			player.NumGames = 50
		} else if user.RatingV2.GlobalRatingAttributes.RD < 150 {
			player.NumGames = 30
		} else if user.RatingV2.GlobalRatingAttributes.RD < 200 {
			player.NumGames = 15
		} else if user.RatingV2.GlobalRatingAttributes.RD < 250 {
			player.NumGames = 5
		} else {
			player.NumGames = 0
		}

		return player
	}

	// Fallback to legacy rating or default
	if user.Rating == nil {
		user.Rating = utils.AllocPtr(DefaultRating)
	}

	// Create new player with default values
	return NewPlayer(user.ID, float64(*user.Rating), DefaultRD, DefaultVolatility, time.Now().AddDate(-1, 0, 0).UnixMilli())
}

// UpdateUserFromGlickoPlayer updates a User model with data from a GlickoPlayer
// If ratingType is specified, it will update the appropriate rating in the user's RatingV2 field
func UpdateUserFromGlickoPlayer(player *GlickoPlayer, user *models.User, ratingType string) *models.User {
	if user.RatingV2 == nil {
		user.RatingV2 = &models.UserRating{}
	}

	user.RatingV2.GlobalRatingAttributes = &models.RatingAttributes{
		Rating:      player.Rating,
		RD:          player.RD,
		Volatility:  player.Volatility,
		LastUpdated: player.LastUpdated,
	}

	return user
}
