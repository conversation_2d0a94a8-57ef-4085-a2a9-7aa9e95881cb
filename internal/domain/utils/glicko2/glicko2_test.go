package glicko2

import (
	"math"
	"reflect"
	"testing"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func TestCalculateSkewFactor(t *testing.T) {
	// Test draw case
	skew := calculateSkewFactor(20.0, 0.5)
	if skew != 1.0 {
		t.<PERSON><PERSON><PERSON>("Expected skew factor for draw to be 1.0, got %f", skew)
	}

	// Test zero margin
	skew = calculateSkewFactor(0.0, 1.0)
	if skew <= MinSkewFactor || skew >= MaxSkewFactor {
		t.<PERSON><PERSON><PERSON>("Expected skew factor for zero margin to be between %f and %f, got %f",
			MinSkewFactor, MaxSkewFactor, skew)
	}

	// Test max margin
	skew = calculateSkewFactor(MaxMargin, 1.0)
	if math.Abs(skew-MaxSkewFactor) > 0.01 {
		t.Errorf("Expected skew factor for max margin to be close to %f, got %f",
			MaxSkewFactor, skew)
	}

	// Test that skew increases with margin
	skew1 := calculateSkewFactor(10.0, 1.0)
	skew2 := calculateSkewFactor(30.0, 1.0)
	if skew2 <= skew1 {
		t.Errorf("Expected skew factor to increase with margin, got %f for margin 10 and %f for margin 30",
			skew1, skew2)
	}
}

func TestRDLimits(t *testing.T) {
	// Create a player with minimum RD
	player := NewPlayer(primitive.NewObjectID(), 1500, MinRD, DefaultVolatility, time.Now().UnixMilli())

	// Create a strong opponent
	opponent := NewPlayer(primitive.NewObjectID(), 2000, 50, DefaultVolatility, time.Now().UnixMilli())

	// Player loses to much stronger opponent
	result := MatchResult{
		Opponent: opponent,
		Score:    0.0,  // Loss
		Margin:   40.0, // Maximum margin
	}

	// Update player's rating
	currentTime := time.Now().UnixMilli()
	player.UpdateRating(result, currentTime)

	// Check that RD doesn't go below minimum
	if player.RD < float64(MinRD) {
		t.Errorf("Expected RD to not go below minimum %v, got %f", MinRD, player.RD)
	}

	// Now test maximum RD
	// Create a player with very high RD and long inactivity
	player = NewPlayer(primitive.NewObjectID(), 1500, 300, DefaultVolatility, time.Now().AddDate(0, -3, 0).UnixMilli())

	// Update player's rating
	player.UpdateRating(result, currentTime)

	// Check that RD doesn't exceed maximum
	if player.RD > float64(MaxRD) {
		t.Errorf("Expected RD to not exceed maximum %v, got %f", MaxRD, player.RD)
	}
}

func TestMarginImpact(t *testing.T) {
	// Create two identical players
	player1 := NewPlayer(primitive.NewObjectID(), 1500, 200, DefaultVolatility, time.Now().UnixMilli())
	player2 := NewPlayer(primitive.NewObjectID(), 1500, 200, DefaultVolatility, time.Now().UnixMilli())

	// Clone player1 for two different scenarios
	player1Small := *player1
	player1Large := *player1

	// Small margin win
	smallResult := MatchResult{
		Opponent: player2,
		Score:    1.0, // Win
		Margin:   5.0, // Small margin
	}

	// Large margin win
	largeResult := MatchResult{
		Opponent: player2,
		Score:    1.0,  // Win
		Margin:   35.0, // Large margin
	}

	// Update ratings
	currentTime := time.Now().UnixMilli()
	player1Small.UpdateRating(smallResult, currentTime)
	player1Large.UpdateRating(largeResult, currentTime)

	// Check that larger margin resulted in larger rating increase
	if player1Large.Rating <= player1Small.Rating {
		t.Errorf("Expected larger margin to result in larger rating increase, small margin: %f, large margin: %f",
			player1Small.Rating, player1Large.Rating)
	}
}

func TestGlickoPlayer_UpdateRating(t *testing.T) {
	type fields struct {
		ID          primitive.ObjectID
		Rating      float64
		RD          float64
		Volatility  float64
		LastUpdated int64
		NumGames    int
	}
	type args struct {
		result      MatchResult
		currentTime int64
	}
	type want struct {
		Rating     float64
		RD         float64
		Volatility float64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   want
	}{
		{
			name: "GlickoPlayer wins against higher rated opponent low margin",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          50,
				Volatility:  0.06,
				LastUpdated: 0,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1600, 250, 0.06, 0),
					Score:    1.0,
					Margin:   1,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer wins against higher rated opponent high margin",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          50,
				Volatility:  0.06,
				LastUpdated: 0,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1600, 250, 0.06, 0),
					Score:    1.0,
					Margin:   40,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer loses against lower rated opponent low margin",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          250,
				Volatility:  0.06,
				LastUpdated: 0,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1400, 250, 0.06, 0),
					Score:    0.0,
					Margin:   1,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer loses against lower rated opponent high margin",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          250,
				Volatility:  0.06,
				LastUpdated: 0,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1400, 250, 0.06, 0),
					Score:    0.0,
					Margin:   40,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer draws against equally rated opponent",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          250,
				Volatility:  0.06,
				LastUpdated: time.Now().UnixMilli() - 10,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1500, 250, 0.06, 0),
					Score:    0.5,
					Margin:   0,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer wins against equally rated opponent",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          250,
				Volatility:  0.06,
				LastUpdated: time.Now().UnixMilli() - 10,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1500, 250, 0.06, 0),
					Score:    1,
					Margin:   15,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer loses against equally rated opponent",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          250,
				Volatility:  0.06,
				LastUpdated: 0,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1500, 250, 0.06, 0),
					Score:    0,
					Margin:   15,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer wins against higher rated opponent low margin non zero last updated",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          250,
				Volatility:  0.06,
				LastUpdated: time.Now().UnixMilli() - 10,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1600, 250, 0.06, 0),
					Score:    1.0,
					Margin:   1,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer wins against higher rated opponent high margin non zero last updated",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          250,
				Volatility:  0.06,
				LastUpdated: time.Now().UnixMilli() - 10,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1600, 250, 0.06, 0),
					Score:    1.0,
					Margin:   40,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer loses against lower rated opponent low margin non zero last updated",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          250,
				Volatility:  0.06,
				LastUpdated: time.Now().UnixMilli() - 10,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1400, 250, 0.06, 0),
					Score:    0.0,
					Margin:   1,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer loses against lower rated opponent high margin non zero last updated",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          250,
				Volatility:  0.06,
				LastUpdated: time.Now().UnixMilli() - 10,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1400, 250, 0.06, 0),
					Score:    0.0,
					Margin:   40,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer draws against equally rated opponent non zero last updated",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          250,
				Volatility:  0.06,
				LastUpdated: time.Now().UnixMilli() - 10,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1500, 250, 0.06, 0),
					Score:    0.5,
					Margin:   0,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer wins against equally rated opponent non zero last updated",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          250,
				Volatility:  0.06,
				LastUpdated: time.Now().UnixMilli() - 10,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1500, 250, 0.06, 0),
					Score:    1,
					Margin:   15,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer loses against equally rated opponent non zero last updated",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          250,
				Volatility:  0.06,
				LastUpdated: time.Now().UnixMilli() - 10,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1500, 250, 0.06, 0),
					Score:    0,
					Margin:   15,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer wins against higher rated opponent low margin played a few games",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          150,
				Volatility:  0.01,
				LastUpdated: time.Now().UnixMilli() - 10,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1600, 150, 0.01, 0),
					Score:    1.0,
					Margin:   1,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer wins against higher rated opponent high margin played a few games",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          150,
				Volatility:  0.01,
				LastUpdated: time.Now().UnixMilli() - 10,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1600, 150, 0.01, 0),
					Score:    1.0,
					Margin:   40,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer loses against lower rated opponent low margin played a few games",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          150,
				Volatility:  0.01,
				LastUpdated: time.Now().UnixMilli() - 10,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1400, 150, 0.01, 0),
					Score:    0.0,
					Margin:   1,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer loses against lower rated opponent high margin played a few games",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          150,
				Volatility:  0.01,
				LastUpdated: time.Now().UnixMilli() - 10,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1400, 150, 0.01, 0),
					Score:    0.0,
					Margin:   40,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer draws against equally rated opponent played a few games",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          150,
				Volatility:  0.01,
				LastUpdated: time.Now().UnixMilli() - 10,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1500, 150, 0.01, 0),
					Score:    0.5,
					Margin:   0,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer wins against equally rated opponent played a few games",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          150,
				Volatility:  0.01,
				LastUpdated: time.Now().UnixMilli() - 10,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1500, 150, 0.01, 0),
					Score:    1,
					Margin:   15,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer loses against equally rated opponent played a few games",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          150,
				Volatility:  0.01,
				LastUpdated: time.Now().UnixMilli() - 10,
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1500, 150, 0.01, 0),
					Score:    0,
					Margin:   15,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
		{
			name: "GlickoPlayer's RD increases over time",
			fields: fields{
				ID:          primitive.NewObjectID(),
				Rating:      1500,
				RD:          200,
				Volatility:  0.06,
				LastUpdated: 0, // 2023-01-01
			},
			args: args{
				result: MatchResult{
					Opponent: NewPlayer(primitive.NewObjectID(), 1500, 100, 0.06, 0),
					Score:    0.5,
					Margin:   0,
				},
				currentTime: time.Now().UnixMilli(),
			},
			want: want{
				Rating:     0.0,
				RD:         0.0,
				Volatility: 0.0,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewPlayer(tt.fields.ID, tt.fields.Rating, tt.fields.RD, tt.fields.Volatility, tt.fields.LastUpdated)
			p.NumGames = tt.fields.NumGames
			p.UpdateRating(tt.args.result, tt.args.currentTime)

			if !reflect.DeepEqual(math.Round(p.Rating*10000000000)/10000000000, math.Round(tt.want.Rating*10000000000)/10000000000) {
				t.Errorf("UpdateRating() Rating = %v, want %v", p.Rating, tt.want.Rating)
			}
			if !reflect.DeepEqual(math.Round(p.RD*10000000000)/10000000000, math.Round(tt.want.RD*10000000000)/10000000000) {
				t.Errorf("UpdateRating() RD = %v, want %v", p.RD, tt.want.RD)
			}
			if !reflect.DeepEqual(math.Round(p.Volatility*10000000000)/10000000000, math.Round(tt.want.Volatility*10000000000)/10000000000) {
				t.Errorf("UpdateRating() Volatility = %v, want %v", p.Volatility, tt.want.Volatility)
			}
		})
	}
}
