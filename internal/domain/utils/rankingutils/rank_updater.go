package rankingutils

import (
	"context"
	"sync"
	"time"

	"go.uber.org/zap"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
)

// TODO: bucketed rank updates

const RankUpdateInterval = 1 * time.Hour // 1 hour

type updateRequest struct {
	shouldUpdate bool
	userRepo     repository.UserRepository
	countryCodes []string
	responseChan chan error
}

var (
	updateChan      chan updateRequest
	lastUpdatedTime time.Time
	mutex           sync.Mutex
)

func init() {
	updateChan = make(chan updateRequest)
	go handleUpdates()
}

// UpdateRank initiates a rank update request
func UpdateRank(shouldUpdate bool, userRepo repository.UserRepository, countryCodes ...string) error {
	responseChan := make(chan error)
	updateChan <- updateRequest{
		shouldUpdate: shouldUpdate,
		userRepo:     userRepo,
		countryCodes: countryCodes,
		responseChan: responseChan,
	}
	return <-response<PERSON>han
}

func handleUpdates() {
	for req := range updateChan {
		err := processUpdate(req)
		req.responseChan <- err
	}
}

func processUpdate(req updateRequest) error {
	mutex.Lock()
	defer mutex.Unlock()

	timeSinceLastUpdate := time.Since(lastUpdatedTime)

	if !req.shouldUpdate && timeSinceLastUpdate < RankUpdateInterval {
		zlog.Info(context.Background(), "Rank update not required: last update was ago, next update in %v",
			zap.Duration("time since last update", timeSinceLastUpdate), zap.Duration("next update time", RankUpdateInterval-timeSinceLastUpdate))
		return nil
	}

	go func() {
		err := UpdateGlobalRankingsV2(context.Background(), req.userRepo)
		if err != nil {
			zlog.Error(context.Background(), "Failed to update global rankings:", err)
		}
	}()

	if len(req.countryCodes) > 0 {
		for _, code := range req.countryCodes {
			go func() {
				err := UpdateCountryRankings(context.Background(), req.userRepo, code)
				if err != nil {
					zlog.Error(context.Background(), "Failed to update country rankings:", err)
				}
			}()
		}
	}

	lastUpdatedTime = time.Now()
	return nil
}
