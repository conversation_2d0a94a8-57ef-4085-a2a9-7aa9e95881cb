// File: internal/utils/ranking/update_country_rank.go

package rankingutils

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func UpdateCountryRankings(ctx context.Context, userRepo repository.UserRepository, countryCode string) error {
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"countryCode": countryCode, "isGuest": false}}},
		{{Key: "$sort", Value: bson.M{"rating": -1}}},
		{{
			Key: "$group", Value: bson.M{
				"_id": "$rating",
				"users": bson.M{
					"$push": bson.M{
						"_id":         "$_id",
						"countryRank": "$countryRank",
					},
				},
				"count": bson.M{"$sum": 1},
			},
		}},
		{{Key: "$sort", Value: bson.M{"_id": -1}}},
		{{
			Key: "$setWindowFields", Value: bson.M{
				"sortBy": bson.M{"_id": -1},
				"output": bson.M{
					"cumulativeRank": bson.M{
						"$sum": "$count",
						"window": bson.M{
							"documents": []string{"unbounded", "current"},
						},
					},
				},
			},
		}},
		{{Key: "$unwind", Value: "$users"}},
		{{
			Key: "$project", Value: bson.M{
				"userId":       "$users._id",
				"rating":       "$_id",
				"previousRank": "$users.countryRank",
				"rank": bson.M{
					"$subtract": []string{"$cumulativeRank", "$count"},
				},
			},
		}},
	}

	cursor, err := userRepo.AggregateProjected(ctx, pipeline)
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	var bulkWrites []mongo.WriteModel
	for cursor.Next(ctx) {
		var result struct {
			UserID       string `bson:"userId"`
			Rating       int    `bson:"rating"`
			PreviousRank int    `bson:"previousRank"`
			Rank         int    `bson:"rank"`
		}
		if err := cursor.Decode(&result); err != nil {
			return err
		}

		if result.Rank+1 == result.PreviousRank {
			continue
		}

		update := mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": result.UserID}).
			SetUpdate(bson.M{
				"$set": bson.M{
					"countryRank":         result.Rank + 1,
					"previousCountryRank": result.PreviousRank,
				},
			})
		bulkWrites = append(bulkWrites, update)
	}

	if len(bulkWrites) > 0 {
		_, err = userRepo.BulkWrite(ctx, bulkWrites)
		if err != nil {
			return err
		}
	}

	return nil
}
