// File: internal/utils/ranking/update_global_rank.go

package rankingutils

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func UpdateGlobalRankingsV0(ctx context.Context, userCollection *mongo.Collection) error {
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"isGuest": false}}},
		{{Key: "$sort", Value: bson.M{"rating": -1}}},
		{{
			Key: "$group", Value: bson.M{
				"_id": "$rating",
				"users": bson.M{
					"$push": bson.M{
						"_id":        "$_id",
						"globalRank": "$globalRank",
					},
				},
				"count": bson.M{"$sum": 1},
			},
		}},
		{{Key: "$sort", Value: bson.M{"_id": -1}}},
		{{
			Key: "$setWindowFields", Value: bson.M{
				"sortBy": bson.M{"_id": -1},
				"output": bson.M{
					"cumulativeRank": bson.M{
						"$sum": "$count",
						"window": bson.M{
							"documents": []string{"unbounded", "current"},
						},
					},
				},
			},
		}},
		{{Key: "$unwind", Value: "$users"}},
		{{
			Key: "$project", Value: bson.M{
				"userID":       "$users._id",
				"rating":       "$_id",
				"previousRank": "$users.globalRank",
				"rank": bson.M{
					"$subtract": []string{"$cumulativeRank", "$count"},
				},
			},
		}},
	}

	cursor, err := userCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	var bulkWrites []mongo.WriteModel
	for cursor.Next(ctx) {
		var result struct {
			UserID       string `bson:"userID"`
			Rating       int    `bson:"rating"`
			PreviousRank int    `bson:"previousRank"`
			Rank         int    `bson:"rank"`
		}
		if err := cursor.Decode(&result); err != nil {
			return err
		}

		if result.Rank+1 == result.PreviousRank {
			continue
		}

		update := mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": result.UserID}).
			SetUpdate(bson.M{
				"$set": bson.M{
					"globalRank":         result.Rank + 1,
					"previousGlobalRank": result.PreviousRank,
				},
			})
		bulkWrites = append(bulkWrites, update)
	}

	if len(bulkWrites) > 0 {
		_, err = userCollection.BulkWrite(ctx, bulkWrites)
		if err != nil {
			return err
		}
	}

	return nil
}

func UpdateGlobalRankingsV1(ctx context.Context, userCollection *mongo.Collection) error {
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"isGuest": false}}},
		{{Key: "$sort", Value: bson.D{{Key: "rating", Value: -1}, {Key: "_id", Value: 1}}}},
		{{Key: "$group", Value: bson.D{
			{Key: "_id", Value: nil},
			{Key: "users", Value: bson.M{
				"$push": bson.M{
					"_id":        "$_id",
					"rating":     "$rating",
					"globalRank": "$globalRank",
				},
			}},
		}}},
		{{Key: "$unwind", Value: bson.M{
			"path":              "$users",
			"includeArrayIndex": "rank",
		}}},
		{{Key: "$project", Value: bson.M{
			"_id":          "$users._id",
			"rating":       "$users.rating",
			"previousRank": "$users.globalRank",
			"newRank":      bson.M{"$add": []interface{}{"$rank", 1}},
		}}},
		{{Key: "$match", Value: bson.M{
			"$expr": bson.M{"$ne": []string{"$previousRank", "$newRank"}},
		}}},
	}

	cursor, err := userCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return fmt.Errorf("aggregation failed: %w", err)
	}
	defer cursor.Close(ctx)

	var bulkWrites []mongo.WriteModel
	for cursor.Next(ctx) {
		var result struct {
			ID           primitive.ObjectID `bson:"_id"`
			Rating       int                `bson:"rating"`
			PreviousRank int                `bson:"previousRank"`
			NewRank      int                `bson:"newRank"`
		}
		if err := cursor.Decode(&result); err != nil {
			return fmt.Errorf("failed to decode result: %w", err)
		}

		update := mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": result.ID}).
			SetUpdate(bson.M{
				"$set": bson.M{
					"globalRank":         result.NewRank,
					"previousGlobalRank": result.PreviousRank,
				},
			})
		bulkWrites = append(bulkWrites, update)
	}

	if err := cursor.Err(); err != nil {
		return fmt.Errorf("cursor iteration failed: %w", err)
	}

	if len(bulkWrites) > 0 {
		_, err = userCollection.BulkWrite(ctx, bulkWrites, options.BulkWrite().SetOrdered(false))
		if err != nil {
			return fmt.Errorf("bulk write failed: %w", err)
		}
	}

	return nil
}

func UpdateGlobalRankingsV2(ctx context.Context, userRepo repository.UserRepository) error {
	users, err := userRepo.Find(
		ctx,
		bson.M{"isGuest": false},
		options.Find().SetSort(bson.D{{Key: "rating", Value: -1}, {Key: "_id", Value: 1}}),
	)
	if err != nil {
		return err
	}

	var bulkWrites []mongo.WriteModel
	currentRank := 1
	prevRating := 0
	prevRankCount := 0
	for _, user := range users {
		if prevRating != *user.Rating {
			currentRank += prevRankCount
			prevRankCount = 0
		}
		update := mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": user.ID}).
			SetUpdate(bson.M{
				"$set": bson.M{
					"globalRank":         currentRank,
					"previousGlobalRank": user.GlobalRank,
				},
			})
		prevRankCount++
		prevRating = *user.Rating
		bulkWrites = append(bulkWrites, update)
	}
	if len(bulkWrites) > 0 {
		_, err = userRepo.BulkWrite(ctx, bulkWrites, options.BulkWrite().SetOrdered(false))
		if err != nil {
			return err
		}
	}
	return nil
}
