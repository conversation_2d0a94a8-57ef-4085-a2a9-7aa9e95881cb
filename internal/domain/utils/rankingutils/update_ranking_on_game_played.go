// File: internal/utils/ranking/update_ranking_on_game_played.go

package rankingutils

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"

	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
)

// UpdateRankingOnGamePlayed updates the ranking of players after a game has been played
func UpdateRankingOnGamePlayed(ctx context.Context, userRepo repository.UserRepository, users []*models.User) error {
	K := 50
	var shouldUpdateTopKPlayersRank bool

	for _, user := range users {
		if user.IsGuest == nil || *user.IsGuest {
			continue
		}

		filter := bson.M{
			"rating":  bson.M{"$gt": user.Rating},
			"_id":     bson.M{"$ne": user.ID},
			"isGuest": false,
		}

		higherRankedPlayers, err := userRepo.CountDocuments(ctx, filter)
		if err != nil {
			continue
		}

		globalRank := int(higherRankedPlayers) + 1

		if *user.GlobalRank == globalRank {
			continue
		}

		shouldUpdateTopKPlayersRank = *user.GlobalRank <= K || globalRank <= K

		update := bson.M{
			"$set": bson.M{
				"globalRank": globalRank,
			},
		}

		err = userRepo.UpdateOne(ctx, bson.M{"_id": user.ID}, update)
		if err != nil {
			continue
		}

		if shouldUpdateTopKPlayersRank {
			continue
		}
	}

	return nil
}
