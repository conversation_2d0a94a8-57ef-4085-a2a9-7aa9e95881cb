package userStreaks

import (
	"context"
	"fmt"
	"math/rand"
	"slices"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	stringUtils "matiksOfficial/matiks-server-go/utils/string"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func updateUserStreakObject(userStreaks *models.UserStreaks, playedToday bool, timezone *string) bool {
	if userStreaks.StreakHistory == nil {
		userStreaks.StreakHistory = make([]*time.Time, 0)
	}
	if userStreaks.LastSevenDays == nil {
		userStreaks.LastSevenDays = make([]bool, 7)
	}

	todayDate := time.Now()

	location := time.UTC
	if timezone != nil {
		loc, err := time.LoadLocation(*timezone)
		if err == nil {
			location = loc
		}
	}

	userDate := todayDate.In(location)
	normalizedDate := time.Date(userDate.Year(), userDate.Month(), userDate.Day(), 0, 0, 0, 0, location)

	lastPlayedDate := userStreaks.LastPlayedDate

	daysSinceLastPlayed := int(userDate.Sub(lastPlayedDate).Hours() / 24)

	newLastSevenDays := make([]bool, 7)
	for i := 0; i < 7; i++ {
		checkDate := normalizedDate.AddDate(0, 0, -i)
		newLastSevenDays[6-i] = containsDate(userStreaks.StreakHistory, checkDate)
	}
	userStreaks.LastSevenDays = newLastSevenDays

	if playedToday {
		userStreaks.LastSevenDays[6] = true
		if daysSinceLastPlayed == 0 {
			return false
		} else if daysSinceLastPlayed == 1 {
			userStreaks.CurrentStreak++
		} else {
			userStreaks.CurrentStreak = 1
		}
		userStreaks.LastPlayedDate = normalizedDate

		userStreaks.StreakHistory = append(userStreaks.StreakHistory, &normalizedDate)
		if userStreaks.CurrentStreak > userStreaks.LongestStreak {
			userStreaks.LongestStreak = userStreaks.CurrentStreak
		}
	} else {
		if daysSinceLastPlayed > 1 {
			userStreaks.CurrentStreak = 0
			return true
		}
		return false
	}

	return true
}

func containsDate(history []*time.Time, date time.Time) bool {
	for _, t := range slices.Backward(history[max(0, len(history)-7):]) {
		if t.Equal(date) {
			return true
		}
	}
	return false
}

func UpdateUserStreak(
	ctx context.Context, userID primitive.ObjectID,
	userRepo repository.UserRepository, playedToday bool,
	notificationService domain.NotificationStore,
	coreService domain.CoreLogicStore,
) error {
	user, err := coreService.GetUserByID(ctx, userID)
	if err != nil || user == nil {
		return err
	}

	if user.IsBot != nil && *user.IsBot {
		return nil
	}

	if user.UserStreaks == nil {
		user.UserStreaks = &models.UserStreaks{}
	}

	update := updateUserStreakObject(user.UserStreaks, playedToday, user.Timezone)

	if update {
		err = userRepo.UpdateOne(ctx, bson.M{"_id": userID}, bson.M{"$set": bson.M{"userStreaks": user.UserStreaks}})
		if err != nil {
			return err
		}

		// Publishing User Event for Streak Maintain
		if playedToday {
			zlog.Info(ctx, "Publishing User Event for Streak Maintain", zap.String("userID", userID.Hex()))

			if err := SendFeedEvent(ctx, notificationService, userID, user); err != nil {
				zlog.Error(ctx, "Failed to send feed event", err)
			}

			err = coreService.PublishUserEvent(ctx, userID, models.StreakMaintained{
				IsPlayedToday: playedToday,
			})
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func SendFeedEvent(ctx context.Context, notificationService domain.NotificationStore, userID primitive.ObjectID, user *models.User) error {
	if user == nil {
		zlog.Error(ctx, "User not found", systemErrors.ErrUserNil)
		return nil
	}
	if user.UserStreaks == nil {
		zlog.Error(ctx, "User streaks not found", systemErrors.ErrUserStreaksNil)
		return nil
	}

	if !slices.Contains([]int{7, 21, 50, 100, 150, 200, 500}, user.UserStreaks.CurrentStreak) {
		return nil
	}
	randomCongratulate := []string{"Hurrah!", "Congratulations!", "Amazing!", "Fantastic!"}
	i := rand.Intn(len(randomCongratulate))
	word := randomCongratulate[i]

	zlog.Info(ctx, "Publishing User Event for Streak Maintain", zap.String("userID", userID.Hex()))
	err := notificationService.SendNotification(ctx, &models.UserNotification{
		UserID: userID,
		Type:   models.NotificationTypeFeed,
		Data: map[string]string{
			"type": "streak_maintained",
		},
		SentAt:   time.Now(),
		ImageUrl: user.ProfileImageURL,
		Title:    "@Matiks",
		Body:     fmt.Sprintf("%s You have maintained a %d day streak!", word, user.UserStreaks.CurrentStreak),
		Feed: &models.FeedNotificationParams{
			FeedType: models.FeedTypeCelebration,
			FeedForFriends: &models.FeedForFriends{
				Title: stringUtils.Concat("@", user.Username),
				Body:  fmt.Sprintf("%s Your friend has maintained a %d day streak!", word, user.UserStreaks.CurrentStreak),
			},
		},
	})
	if err != nil {
		zlog.Error(ctx, "Failed to send feed event", err)
		return err
	}
	return nil
}
