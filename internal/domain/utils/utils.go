package utils

import (
	"context"
	"encoding/json"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func AllocPtr[T any](v T) *T {
	return &v
}

func RemoveElement[T any](s []T, i int) []T {
	s[i] = s[len(s)-1]
	return s[:len(s)-1]
}

func GetDefaultValue[T comparable](value *T, defaultValue T) T {
	if value == nil {
		return defaultValue
	}
	return *value
}

func ToFloat64(v interface{}) float64 {
	switch val := v.(type) {
	case float64:
		return val
	case float32:
		return float64(val)
	case int:
		return float64(val)
	case int32:
		return float64(val)
	case int64:
		return float64(val)
	default:
		return 0
	}
}

func ToInt(v interface{}) int {
	switch val := v.(type) {
	case int:
		return val
	case int32:
		return int(val)
	case int64:
		return int(val)
	case float64:
		return int(val)
	case float32:
		return int(val)
	default:
		return 0
	}
}

func GetStringPtr(m bson.M, key string) *string {
	if val, ok := m[key].(string); ok {
		return &val
	}
	return nil
}

func GetBadgeTypePtr(m bson.M, key string) *models.BadgeType {
	switch val := m[key].(type) {
	case models.BadgeType:
		return &val
	case string:
		convertedVal := models.BadgeType(val) // adjust as needed
		return &convertedVal
	default:
		return nil
	}
}

func GetInitialBadge(rating int) models.BadgeType {
	if rating < 1000 {
		return models.BadgeTypeRookie
	} else if rating < 1200 {
		return models.BadgeTypeNovice
	} else if rating < 1600 {
		return models.BadgeTypeAmateur
	} else if rating < 2000 {
		return models.BadgeTypeExpert
	} else if rating < 2500 {
		return models.BadgeTypeCandidateMaster
	} else if rating < 3000 {
		return models.BadgeTypeMaster
	} else if rating < 3500 {
		return models.BadgeTypeGrandmaster
	} else if rating < 4000 {
		return models.BadgeTypeLegendaryGrandmaster
	} else {
		return models.BadgeTypeGoat
	}
}

func GetIntPtr(m bson.M, key string) *int {
	if val, ok := m[key].(int32); ok {
		intVal := int(val)
		return &intVal
	}
	return nil
}

func DeriveContextWithoutCancel(ctx context.Context) context.Context {
	derivedCtx := context.Background()

	for _, v := range constants.AllKeys {
		derivedCtx = context.WithValue(derivedCtx, v, ctx.Value(v))
	}
	return derivedCtx
}

func GetContextValuesMap(ctx context.Context) map[string]interface{} {
	valuesMap := make(map[string]interface{})
	for _, v := range constants.AllKeys {
		valuesMap[v.String()] = ctx.Value(v)
	}
	return valuesMap
}

func InheritContextFromMap(ctx context.Context, valuesMap map[string]interface{}) context.Context {
	derivedCtx := DeriveContextWithoutCancel(ctx)

	for _, v := range constants.AllKeys {
		if value, ok := valuesMap[v.String()]; ok {
			derivedCtx = context.WithValue(derivedCtx, v, value)
		}
	}
	return derivedCtx
}

func DeriveContextWithCancel(ctx context.Context, timeout time.Duration) context.Context {
	derivedCtx := context.Background()

	for _, v := range constants.AllKeys {
		derivedCtx = context.WithValue(derivedCtx, v, ctx.Value(v))
	}

	derivedCtx, cancel := context.WithTimeout(derivedCtx, timeout)
	defer cancel()

	return derivedCtx
}

func GetIntValue(m bson.M, key string) int {
	if value, ok := m[key]; ok {
		if intValue, ok := value.(int32); ok {
			return int(intValue)
		}
		if intValue, ok := value.(int64); ok {
			return int(intValue)
		}
		if intValue, ok := value.(int); ok {
			return intValue
		}
	}
	return 0
}

func GetTimeValue(m bson.M, key string) time.Time {
	if value, ok := m[key]; ok {
		if timeValue, ok := value.(time.Time); ok {
			return timeValue
		}
	}
	return time.Time{}
}

func GetBoolSlice(m bson.M, key string) []bool {
	if value, ok := m[key]; ok {
		if boolSlice, ok := value.(primitive.A); ok {
			result := make([]bool, len(boolSlice))
			for i, v := range boolSlice {
				if boolValue, ok := v.(bool); ok {
					result[i] = boolValue
				}
			}
			return result
		}
	}
	return nil
}

func GetTimeSlice(m bson.M, key string) []*time.Time {
	if value, ok := m[key]; ok {
		if timeSlice, ok := value.(primitive.A); ok {
			result := make([]*time.Time, len(timeSlice))
			for i, v := range timeSlice {
				if timeValue, ok := v.(time.Time); ok {
					result[i] = &timeValue
				}
			}
			return result
		}
	}
	return nil
}

func ToInterfaceSlice[T any](slice []T) []interface{} {
	converted := make([]interface{}, len(slice))
	for i, v := range slice {
		data, _ := json.Marshal(v)
		converted[i] = data
	}
	return converted
}

func UniqueObjectIDs(ids []primitive.ObjectID) []primitive.ObjectID {
	if len(ids) == 0 {
		return []primitive.ObjectID{}
	}
	seen := make(map[primitive.ObjectID]struct{}, len(ids))
	unique := make([]primitive.ObjectID, 0, len(ids))
	for _, id := range ids {
		if _, ok := seen[id]; !ok {
			seen[id] = struct{}{}
			unique = append(unique, id)
		}
	}
	return unique
}
