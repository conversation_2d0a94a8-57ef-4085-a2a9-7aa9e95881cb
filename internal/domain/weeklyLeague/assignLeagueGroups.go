package weeklyLeague

import (
	"context"
	"fmt"
	"math"
	"math/rand/v2"
	"time"

	"go.uber.org/zap"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const MaxGroupSize = 30

func (s *service) AssignLeagueGroups(ctx context.Context) error {
	zlog.Info(ctx, "Assigning league groups")
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"league": bson.M{"$ne": nil},
		}}},
		{{Key: "$project", Value: bson.M{
			"_id":           1,
			"leagueType":    "$league.league",
			"leagueGroupID": "$league.groupId",
			"leagueUpdated": "$league.updatedAt",
		}}},
	}

	cursor, err := s.userRepo.AggregateProjected(ctx, pipeline, options.Aggregate().SetAllowDiskUse(true))
	if err != nil {
		return fmt.Errorf("failed to query users for group assignment: %w", err)
	}
	defer cursor.Close(ctx)

	leagueGroups := make(map[models.LeagueType][]struct {
		UserID  primitive.ObjectID `bson:"_id"`
		GroupID *int               `bson:"leagueGroupID"`
		Updated *time.Time         `bson:"leagueUpdated"`
	})

	for cursor.Next(ctx) {
		var user struct {
			UserID     primitive.ObjectID `bson:"_id"`
			LeagueType *models.LeagueType `bson:"leagueType"`
			GroupID    *int               `bson:"leagueGroupID"`
			Updated    *time.Time         `bson:"leagueUpdated"`
		}
		if err := cursor.Decode(&user); err != nil {
			zlog.Error(ctx, "Failed to decode user for group assignment", err)
			continue
		}

		if user.LeagueType == nil {
			continue
		}

		if _, exists := leagueGroups[*user.LeagueType]; !exists {
			leagueGroups[*user.LeagueType] = make([]struct {
				UserID  primitive.ObjectID `bson:"_id"`
				GroupID *int               `bson:"leagueGroupID"`
				Updated *time.Time         `bson:"leagueUpdated"`
			}, 0)
		}

		leagueGroups[*user.LeagueType] = append(leagueGroups[*user.LeagueType], struct {
			UserID  primitive.ObjectID `bson:"_id"`
			GroupID *int               `bson:"leagueGroupID"`
			Updated *time.Time         `bson:"leagueUpdated"`
		}{
			UserID:  user.UserID,
			GroupID: user.GroupID,
			Updated: user.Updated,
		})
	}

	if err := cursor.Err(); err != nil {
		return fmt.Errorf("cursor error during group assignment: %w", err)
	}

	updates := make([]mongo.WriteModel, 0)

	for league, users := range leagueGroups {
		leagueSize := len(users)

		zlog.Info(ctx, "Processing league group assignment", zap.String("league", league.String()), zap.Int("leagueSize", leagueSize))

		if leagueSize <= 0 {
			continue
		}

		numGroups := int(math.Ceil(float64(leagueSize) / float64(MaxGroupSize)))
		if numGroups <= 0 {
			numGroups = 1
		}

		indexes := make([]int, leagueSize)
		for i := range indexes {
			indexes[i] = i
		}

		for i := leagueSize - 1; i > 0; i-- {
			j := rand.IntN(i + 1)
			indexes[i], indexes[j] = indexes[j], indexes[i]
		}

		groupCounts := make([]int, numGroups)

		for _, idx := range indexes {
			minGroup := 0
			for g := 1; g < numGroups; g++ {
				if groupCounts[g] < groupCounts[minGroup] {
					minGroup = g
				}
			}

			if groupCounts[minGroup] >= MaxGroupSize {
				zlog.Warn(ctx, fmt.Sprintf("Group %d is already at max capacity (%d users)", minGroup+1, MaxGroupSize))
				continue
			}

			groupID := minGroup + 1
			userID := users[idx].UserID
			groupCounts[minGroup]++

			updates = append(updates, mongo.NewUpdateOneModel().
				SetFilter(bson.M{"_id": userID}).
				SetUpdate(bson.M{"$set": bson.M{
					"league.groupId":   utils.AllocPtr(groupID),
					"league.updatedAt": utils.AllocPtr(time.Now().UTC()),
				}}),
			)
		}
	}

	if len(updates) == 0 {
		return nil
	}

	zlog.Info(ctx, "Starting bulk write for group assignments", zap.Int("totalUpdates", len(updates)))

	const updateBatchSize = 1000
	for i := 0; i < len(updates); i += updateBatchSize {
		end := min(i+updateBatchSize, len(updates))

		_, err := s.userRepo.BulkWrite(
			ctx,
			updates[i:end],
			options.BulkWrite().SetOrdered(false),
		)
		if err != nil {
			zlog.Error(ctx, "Error updating group assignments batch", err)
		}
	}

	zlog.Info(ctx, "Finished bulk writing group assignments")

	return nil
}
