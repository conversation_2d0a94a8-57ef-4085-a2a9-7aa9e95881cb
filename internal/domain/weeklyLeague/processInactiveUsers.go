package weeklyLeague

import (
	"context"
	"time"

	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func ProcessInactiveUsers(ctx context.Context, s *service, inactiveUserIDs []primitive.ObjectID) []mongo.WriteModel {
	zlog.Info(ctx, "Processing inactive users", zap.Int("totalUsers", len(inactiveUserIDs)))
	if len(inactiveUserIDs) == 0 {
		return nil
	}

	inactiveUsersPipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"_id": bson.M{"$in": inactiveUserIDs},
		}}},
		{{Key: "$project", Value: bson.M{
			"_id":         1,
			"league":      1,
			"statikCoins": 1,
		}}},
	}

	cursor, err := s.userRepo.AggregateProjected(ctx, inactiveUsersPipeline, options.Aggregate())
	if err != nil {
		zlog.Error(ctx, "Failed to query inactive users", err)
		return nil
	}
	defer cursor.Close(ctx)

	newAssignments := make([]mongo.WriteModel, 0, len(inactiveUserIDs))

	for cursor.Next(ctx) {
		var userDoc struct {
			ID          primitive.ObjectID `bson:"_id"`
			League      *models.LeagueInfo `bson:"league"`
			StatikCoins int                `bson:"statikCoins"`
		}
		if err := cursor.Decode(&userDoc); err != nil {
			zlog.Error(ctx, "Failed to decode inactive user", err)
			continue
		}
		var currentLeague models.LeagueType

		if userDoc.League == nil || userDoc.League.League == nil {
			currentLeague = models.LeagueTypeBronze
		} else {
			currentLeague = *userDoc.League.League
		}

		newAssignments = append(newAssignments, mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": userDoc.ID}).
			SetUpdate(bson.M{"$set": bson.M{
				"league": &models.LeagueInfo{
					League:            utils.AllocPtr(currentLeague),
					GroupID:           utils.AllocPtr(1), // Will be reassigned in group allocation
					UpdatedAt:         utils.AllocPtr(time.Now().UTC()),
					HasParticipated:   utils.AllocPtr(false),
					CoinsTillLastWeek: utils.AllocPtr(userDoc.StatikCoins),
					ProgressState:     utils.AllocPtr(models.WeeklyLeagueProgressStateNoChange),
				},
			}}),
		)
	}

	if err := cursor.Err(); err != nil {
		zlog.Error(ctx, "Cursor error during inactive users processing", err)
		return nil
	}

	zlog.Info(ctx, "Finished processing inactive users", zap.Int("totalUsers", len(newAssignments)))

	return newAssignments
}
