package weeklyLeague

import (
	"context"
	"math"
	"time"

	"go.uber.org/zap"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	weeklyLeagueUtils "matiksOfficial/matiks-server-go/internal/domain/weeklyLeague/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type GroupedUserLeagueStat struct {
	League  models.LeagueType
	GroupID int
	Users   []models.UserLeagueStat
}

func ProcessLeaguePromotionsDemotionsAndRetentions(ctx context.Context, leagueUsers map[models.LeagueType][]models.UserLeagueStat) []mongo.WriteModel {
	totalUsers := 0
	for _, users := range leagueUsers {
		totalUsers += len(users)
	}
	zlog.Info(ctx, "Processing league promotions and demotions", zap.Int("totalUsers", totalUsers))
	newAssignments := make([]mongo.WriteModel, 0)

	// Reorganize users by both league type and group ID
	groupedUsers := make(map[models.LeagueType]map[int][]models.UserLeagueStat)
	for league, users := range leagueUsers {
		if len(users) == 0 {
			continue
		}

		groupedUsers[league] = make(map[int][]models.UserLeagueStat)
		for _, user := range users {
			groupID := 1
			if user.User.League != nil && user.User.League.GroupID != nil {
				groupID = *user.User.League.GroupID
			}

			if _, exists := groupedUsers[league][groupID]; !exists {
				groupedUsers[league][groupID] = make([]models.UserLeagueStat, 0)
			}
			groupedUsers[league][groupID] = append(groupedUsers[league][groupID], user)
		}
	}

	for league, groupMap := range groupedUsers {
		for groupID, users := range groupMap {
			if len(users) == 0 {
				continue
			}

			zlog.Debug(ctx, "Processing group",
				zap.String("league", string(league)),
				zap.Int("groupID", groupID),
				zap.Int("userCount", len(users)))

			weeklyLeagueUtils.SortUsersByCoins(users)
			promotionRate, demotionRate := weeklyLeagueUtils.GetPromotionDemotionRates(league)
			promotionThreshold := int(math.Ceil(float64(len(users)) * promotionRate))
			demotionThreshold := int(math.Floor(float64(len(users)) * demotionRate))

			if promotionThreshold <= 0 {
				promotionThreshold = 1
			}

			if demotionThreshold >= len(users) {
				demotionThreshold = len(users) - 1
			}

			if promotionThreshold > demotionThreshold {
				promotionThreshold = demotionThreshold
			}

			for i := 0; i < promotionThreshold && i < len(users); i++ {
				userInfo := users[i].User
				if userInfo.League == nil {
					continue
				}
				hasParticipated := userInfo.League.HasParticipated
				if hasParticipated == nil {
					continue
				}
				if !*hasParticipated {
					continue
				}

				coinsTillLastWeek := 0
				if userInfo.League != nil && userInfo.League.CoinsTillLastWeek != nil {
					coinsTillLastWeek = *userInfo.League.CoinsTillLastWeek
				}

				nextLeague := weeklyLeagueUtils.GetNextLeague(league)
				newAssignments = append(newAssignments, mongo.NewUpdateOneModel().
					SetFilter(bson.M{"_id": users[i].UserID}).
					SetUpdate(bson.M{"$set": bson.M{
						"league": &models.LeagueInfo{
							League:            utils.AllocPtr(nextLeague),
							GroupID:           utils.AllocPtr(1),
							UpdatedAt:         utils.AllocPtr(time.Now().UTC()),
							HasParticipated:   utils.AllocPtr(false),
							CoinsTillLastWeek: utils.AllocPtr(coinsTillLastWeek),
							ProgressState:     utils.AllocPtr(models.WeeklyLeagueProgressStatePromotion),
						},
					}}),
				)
			}

			for i := demotionThreshold; i < len(users); i++ {
				userInfo := users[i].User
				if userInfo.League == nil {
					continue
				}
				hasParticipated := userInfo.League.HasParticipated
				if hasParticipated == nil {
					continue
				}
				if !*hasParticipated {
					continue
				}
				coinsTillLastWeek := 0
				if userInfo.League != nil && userInfo.League.CoinsTillLastWeek != nil {
					coinsTillLastWeek = *userInfo.League.CoinsTillLastWeek
				}
				prevLeague := weeklyLeagueUtils.GetPreviousLeague(league)
				newAssignments = append(newAssignments, mongo.NewUpdateOneModel().
					SetFilter(bson.M{"_id": users[i].UserID}).
					SetUpdate(bson.M{"$set": bson.M{
						"league": &models.LeagueInfo{
							League:            utils.AllocPtr(prevLeague),
							GroupID:           utils.AllocPtr(1),
							UpdatedAt:         utils.AllocPtr(time.Now().UTC()),
							HasParticipated:   utils.AllocPtr(false),
							CoinsTillLastWeek: utils.AllocPtr(coinsTillLastWeek),
							ProgressState:     utils.AllocPtr(models.WeeklyLeagueProgressStateDemotion),
						},
					}}),
				)
			}

			for i := promotionThreshold; i < demotionThreshold; i++ {
				userInfo := users[i].User
				if userInfo.League == nil {
					continue
				}
				hasParticipated := userInfo.League.HasParticipated
				if hasParticipated == nil {
					continue
				}
				if !*hasParticipated {
					continue
				}
				coinsTillLastWeek := 0
				if userInfo.League != nil && userInfo.League.CoinsTillLastWeek != nil {
					coinsTillLastWeek = *userInfo.League.CoinsTillLastWeek
				}

				newAssignments = append(newAssignments, mongo.NewUpdateOneModel().
					SetFilter(bson.M{"_id": users[i].UserID}).
					SetUpdate(bson.M{"$set": bson.M{
						"league": &models.LeagueInfo{
							League:            utils.AllocPtr(league),
							GroupID:           utils.AllocPtr(1),
							UpdatedAt:         utils.AllocPtr(time.Now().UTC()),
							HasParticipated:   utils.AllocPtr(false),
							CoinsTillLastWeek: utils.AllocPtr(coinsTillLastWeek),
							ProgressState:     utils.AllocPtr(models.WeeklyLeagueProgressStateNoChange),
						},
					}}),
				)
			}
		}
	}

	zlog.Info(ctx, "Finished processing league promotions and demotions", zap.Int("totalAssignments", len(newAssignments)))

	return newAssignments
}
