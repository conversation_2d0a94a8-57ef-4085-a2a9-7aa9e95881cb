package weeklyLeague

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	weeklyLeagueUtils "matiksOfficial/matiks-server-go/internal/domain/weeklyLeague/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap" // Import zap for structured fields
)

func (s *service) ProcessWeeklyLeagueAssignments(ctx context.Context) error {
	zlog.Debug(ctx, "Starting ProcessWeeklyLeagueAssignments")
	defer zlog.Debug(ctx, "Finished ProcessWeeklyLeagueAssignments") // Will log even if errors occur before explicit return

	currentWeekStart := weeklyLeagueUtils.GetCurrentWeekStart()
	currentWeekEnd := weeklyLeagueUtils.GetWeekEnd(currentWeekStart)

	now := time.Now().UTC()
	zlog.Debug(ctx, "Calculated week boundaries",
		zap.Time("currentWeekStart", currentWeekStart),
		zap.Time("currentWeekEnd", currentWeekEnd),
		zap.Time("currentTimeUTC", now),
	)

	combinedPipeline := mongo.Pipeline{
		{
			{Key: "$match", Value: bson.M{
				"date": bson.M{
					"$gte": currentWeekStart,
					"$lte": currentWeekEnd,
				},
				"statikCoinsEarned": bson.M{"$gt": 0},
			}},
		},
		{
			{Key: "$group", Value: bson.M{
				"_id":              "$userId",
				"prevWeekCoins":    bson.M{"$sum": "$statikCoinsEarned"},
				"lastActivityDate": bson.M{"$max": "$date"},
				"hasActivity":      bson.M{"$first": true},
			}},
		},
		{
			{Key: "$lookup", Value: bson.M{
				"from":         "users",
				"localField":   "_id",
				"foreignField": "_id",
				"as":           "user",
				"pipeline": bson.A{
					bson.M{"$match": bson.M{
						"$and": bson.A{
							bson.M{"$or": bson.A{
								bson.M{"isGuest": false},
								bson.M{"isGuest": bson.M{"$exists": false}},
							}},
							bson.M{"$or": bson.A{
								bson.M{"isBot": false},
								bson.M{"isBot": bson.M{"$exists": false}},
							}},
							bson.M{"$or": bson.A{
								bson.M{"isHumanBot": false},
								bson.M{"isHumanBot": bson.M{"$exists": false}},
							}},
						},
					}},
					bson.M{"$project": bson.M{
						"_id":         1,
						"league":      1,
						"statikCoins": 1,
					}},
				},
			}},
		},
		{
			{Key: "$unwind", Value: bson.M{
				"path":                       "$user",
				"preserveNullAndEmptyArrays": false,
			}},
		},
		{
			{Key: "$lookup", Value: bson.M{
				"from": "userDailyActivity",
				"let":  bson.M{"userId": "$_id"},
				"pipeline": bson.A{
					bson.M{"$match": bson.M{
						"$expr": bson.M{"$and": bson.A{
							bson.M{"$eq": bson.A{"$userId", "$$userId"}},
							bson.M{"$gte": bson.A{"$date", currentWeekStart}},
							bson.M{"$lte": bson.A{"$date", now}},
							bson.M{"$gt": bson.A{"$statikCoinsEarned", 0}},
						}},
					}},
					bson.M{"$group": bson.M{
						"_id":           "$userId",
						"currWeekCoins": bson.M{"$sum": "$statikCoinsEarned"},
					}},
				},
				"as": "currentWeekActivity",
			}},
		},
		{
			{Key: "$addFields", Value: bson.M{
				"isActiveThisWeek": bson.M{"$cond": bson.M{
					"if":   bson.M{"$gt": bson.A{bson.M{"$size": "$currentWeekActivity"}, 0}},
					"then": true,
					"else": false,
				}},
				"currWeekCoins": bson.M{"$cond": bson.M{
					"if":   bson.M{"$gt": bson.A{bson.M{"$size": "$currentWeekActivity"}, 0}},
					"then": bson.M{"$arrayElemAt": bson.A{"$currentWeekActivity.currWeekCoins", 0}},
					"else": 0,
				}},
			}},
		},
		{
			{Key: "$project", Value: bson.M{
				"_id":              1,
				"prevWeekCoins":    1,
				"currWeekCoins":    1,
				"isActiveThisWeek": 1,
				"user":             1,
			}},
		},
	}

	zlog.Info(ctx, "Executing combined user activity aggregation pipeline")
	cursor, err := s.userDailyActivityRepo.AggregateProjected(ctx, combinedPipeline, options.Aggregate().SetAllowDiskUse(true))
	if err != nil {
		// Pass the error directly as the third argument, then zap fields
		zlog.Error(ctx, "Failed to execute combined pipeline", err, zap.Any("pipeline", combinedPipeline))
		return fmt.Errorf("failed to execute combined pipeline: %w", err)
	}
	defer func() {
		zlog.Debug(ctx, "Closing combined pipeline cursor")
		if closeErr := cursor.Close(ctx); closeErr != nil {
			zlog.Warn(ctx, "Failed to close combined pipeline cursor", zap.Error(closeErr)) // Wrap error
		}
	}()

	leagueUsers := make(map[models.LeagueType][]models.UserLeagueStat)
	inactiveUsers := make([]primitive.ObjectID, 0)
	activeUserIds := make(map[primitive.ObjectID]bool)

	zlog.Debug(ctx, "Starting processing of combined pipeline results")
	processedCount := 0
	for cursor.Next(ctx) {
		processedCount++
		if processedCount%100 == 0 { // Log progress periodically for large datasets
			zlog.Debug(ctx, "Processing combined pipeline results batch", zap.Int("processedCount", processedCount))
		}
		var result struct {
			ID               primitive.ObjectID `bson:"_id"`
			PrevWeekCoins    int                `bson:"prevWeekCoins"`
			CurrWeekCoins    int                `bson:"currWeekCoins"`
			IsActiveThisWeek bool               `bson:"isActiveThisWeek"`
			User             struct {
				ID          primitive.ObjectID `bson:"_id"`
				League      *models.LeagueInfo `bson:"league"`
				StatikCoins int                `bson:"statikCoins"`
			} `bson:"user"`
		}

		if err := cursor.Decode(&result); err != nil {
			// Use Warn as we might be able to continue processing other users
			zlog.Error(ctx, "Failed to decode user activity data from combined pipeline", err)
			continue // Skip this user
		}

		userID := result.ID
		// Use Hex() for ObjectID for better readability in logs
		if processedCount%100 == 0 {
			zlog.Debug(ctx, "Processing user from combined pipeline",
				zap.String("userID", userID.Hex()),
				zap.Bool("isActiveThisWeek", result.IsActiveThisWeek),
				zap.Int("prevWeekCoins", result.PrevWeekCoins),
				zap.Int("currWeekCoins", result.CurrWeekCoins))
		}

		if result.IsActiveThisWeek {
			activeUserIds[userID] = true
		}

		var userLeague models.LeagueType
		if result.User.League == nil || result.User.League.League == nil {
			userLeague = models.LeagueTypeBronze
		} else {
			userLeague = *result.User.League.League
		}

		if !result.IsActiveThisWeek {
			inactiveUsers = append(inactiveUsers, userID)
			continue // Skip adding to leagueUsers map
		}
		userLeagueInfo := result.User.League
		if userLeagueInfo == nil {
			continue
		}

		userLeagueInfo.CoinsTillLastWeek = utils.AllocPtr(result.User.StatikCoins)

		userStat := models.UserLeagueStat{
			UserID:      result.ID,
			StatikCoins: result.CurrWeekCoins,
			User: models.UserIdWithLeagueInfo{
				ID:     result.ID,
				League: userLeagueInfo,
			},
		}

		if _, exists := leagueUsers[userLeague]; !exists {
			leagueUsers[userLeague] = make([]models.UserLeagueStat, 0)
		}
		leagueUsers[userLeague] = append(leagueUsers[userLeague], userStat)
		if processedCount%100 == 0 {
			zlog.Debug(ctx, "Added user stat to league map",
				zap.String("userID", userID.Hex()),
				zap.String("league", string(userLeague)),
				zap.Int("currentWeekCoins", userStat.StatikCoins))
		}
	}
	zlog.Info(ctx, "Finished processing combined pipeline results",
		zap.Int("totalProcessed", processedCount),
		zap.Int("activeUsersCount", len(activeUserIds)),
		zap.Int("inactiveUsersCount", len(inactiveUsers)))

	if err := cursor.Err(); err != nil {
		// Pass the error directly as the third argument
		zlog.Error(ctx, "Cursor error after processing combined pipeline results", err)
		return fmt.Errorf("cursor error during combined pipeline processing: %w", err)
	}

	newUsersPipeline := mongo.Pipeline{
		{
			{Key: "$match", Value: bson.M{
				"date": bson.M{
					"$gte": currentWeekStart,
					"$lte": now,
				},
				"statikCoinsEarned": bson.M{"$gt": 0},
			}},
		},
		{
			{Key: "$group", Value: bson.M{
				"_id":         "$userId",
				"statikCoins": bson.M{"$sum": "$statikCoinsEarned"},
			}},
		},
		{
			{Key: "$lookup", Value: bson.M{
				"from":         "users",
				"localField":   "_id",
				"foreignField": "_id",
				"as":           "user",
				"pipeline": bson.A{
					bson.M{"$match": bson.M{
						"$and": bson.A{
							bson.M{"$or": bson.A{
								bson.M{"isGuest": false},
								bson.M{"isGuest": bson.M{"$exists": false}},
							}},
							bson.M{"$or": bson.A{
								bson.M{"isBot": false},
								bson.M{"isBot": bson.M{"$exists": false}},
							}},
							bson.M{"$or": bson.A{
								bson.M{"isHumanBot": false},
								bson.M{"isHumanBot": bson.M{"$exists": false}},
							}},
						},
					}},
					bson.M{"$project": bson.M{
						"_id":         1,
						"league":      1,
						"statikCoins": 1,
					}},
				},
			}},
		},
		{
			{Key: "$unwind", Value: bson.M{
				"path":                       "$user",
				"preserveNullAndEmptyArrays": false,
			}},
		},
	}

	var existingUserIDs []primitive.ObjectID
	for _, users := range leagueUsers {
		for _, u := range users {
			existingUserIDs = append(existingUserIDs, u.UserID)
		}
	}

	existingUserIDs = append(existingUserIDs, inactiveUsers...)

	if len(existingUserIDs) > 0 {
		newUsersPipeline = append(newUsersPipeline, bson.D{
			{Key: "$match", Value: bson.M{
				"_id": bson.M{"$nin": existingUserIDs},
			}},
		})
	}

	newUsersCursor, err := s.userDailyActivityRepo.AggregateProjected(ctx, newUsersPipeline, options.Aggregate().SetAllowDiskUse(true))
	if err != nil {
		return fmt.Errorf("failed to query new users: %w", err)
	}
	defer newUsersCursor.Close(ctx)

	newUsersLeagueAssignments := make([]mongo.WriteModel, 0)
	for newUsersCursor.Next(ctx) {
		var result struct {
			ID          primitive.ObjectID `bson:"_id"`
			StatikCoins int                `bson:"statikCoins"`
			User        struct {
				ID          primitive.ObjectID `bson:"_id"`
				League      *models.LeagueInfo `bson:"league"`
				StatikCoins int                `bson:"statikCoins"`
			} `bson:"user"`
		}

		if err := newUsersCursor.Decode(&result); err != nil {
			zlog.Error(ctx, "Failed to decode new user", err)
			continue
		}

		userLeagueInfo := result.User.League

		if userLeagueInfo == nil {
			newUsersLeagueAssignments = append(newUsersLeagueAssignments, mongo.NewUpdateOneModel().
				SetFilter(bson.M{"_id": result.User.ID}).
				SetUpdate(bson.M{"$set": bson.M{
					"league": &models.LeagueInfo{
						League:            utils.AllocPtr(models.LeagueTypeBronze),
						GroupID:           utils.AllocPtr(1), // Will be reassigned in group allocation
						UpdatedAt:         utils.AllocPtr(time.Now().UTC()),
						CoinsTillLastWeek: utils.AllocPtr(result.User.StatikCoins),
						HasParticipated:   utils.AllocPtr(false),
						ProgressState:     utils.AllocPtr(models.WeeklyLeagueProgressStateNoChange),
					},
				}}),
			)
			continue
		}
	}

	if err := newUsersCursor.Err(); err != nil {
		return fmt.Errorf("cursor error during new users processing: %w", err)
	}

	// Log counts before combining
	promoDemotions := ProcessLeaguePromotionsDemotionsAndRetentions(ctx, leagueUsers)
	// Assuming ProcessInactiveUsers needs ctx and s based on previous thought, adjust if incorrect
	inactiveUpdates := ProcessInactiveUsers(ctx, s, inactiveUsers)
	zlog.Info(ctx, "Calculated assignment updates",
		zap.Int("newUserAssignments", len(newUsersLeagueAssignments)),
		zap.Int("promoDemotionAssignments", len(promoDemotions)),
		zap.Int("inactiveUserUpdates", len(inactiveUpdates)),
	)

	newAssignments := make([]mongo.WriteModel, 0, len(newUsersLeagueAssignments)+len(promoDemotions)+len(inactiveUpdates)) // Pre-allocate slice capacity
	newAssignments = append(newAssignments, newUsersLeagueAssignments...)
	newAssignments = append(newAssignments, promoDemotions...)
	newAssignments = append(newAssignments, inactiveUpdates...)

	const updateBatchSize = 1000
	totalAssignments := len(newAssignments)
	if totalAssignments == 0 {
		zlog.Info(ctx, "No new assignments to process")
		return nil
	}

	zlog.Info(ctx, "Starting bulk write for user league assignments", zap.Int("totalAssignments", totalAssignments), zap.Int("batchSize", updateBatchSize))

	for i := 0; i < totalAssignments; i += updateBatchSize {
		start := i
		end := min(start+updateBatchSize, totalAssignments)
		batch := newAssignments[start:end]
		batchNumber := (i / updateBatchSize) + 1
		zlog.Debug(ctx, "Processing bulk write batch",
			zap.Int("batchNumber", batchNumber),
			zap.Int("batchSize", len(batch)),
			zap.Int("startIndex", start),
			zap.Int("endIndex", end-1), // end is exclusive
		)

		_, err := s.userRepo.BulkWrite(
			ctx,
			batch,
			options.BulkWrite().SetOrdered(false),
		)
		if err != nil {
			// Log error but continue processing other batches as BulkWrite is unordered
			zlog.Error(ctx, "Error updating league assignments batch", err,
				zap.Int("batchNumber", batchNumber),
				zap.Int("batchSize", len(batch)),
			)
			// Potentially add more detailed logging here if needed, e.g., logging specific IDs in the failed batch if possible
		} else {
			zlog.Debug(ctx, "Successfully processed bulk write batch", zap.Int("batchNumber", batchNumber))
		}
	}
	zlog.Info(ctx, "Finished bulk writing user league assignments", zap.Int("totalAssignmentsProcessed", totalAssignments))

	return nil
}
