package weeklyLeague

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type service struct {
	userRepo              repository.UserRepository
	userDailyActivityRepo repository.UserDailyActivityRepository
}

func NewWeeklyLeagueService(lc fx.Lifecycle, repositoryFactory *repository.RepositoryFactory) domain.WeeklyLeagueStore {
	s := &service{
		userRepo:              repositoryFactory.UserRepository,
		userDailyActivityRepo: repositoryFactory.UserDailyActivityRepository,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting weekly league service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down weekly league service")
			return nil
		},
	})

	return s
}
