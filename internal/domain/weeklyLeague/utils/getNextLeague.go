package weeklyLeagueUtils

import "matiksOfficial/matiks-server-go/internal/models"

func GetNextLeague(current models.LeagueType) models.LeagueType {
	switch current {
	case models.LeagueTypeBronze:
		return models.LeagueTypeSilver
	case models.LeagueTypeSilver:
		return models.LeagueTypeGold
	case models.LeagueTypeGold:
		return models.LeagueTypeDiamond
	case models.LeagueTypeDiamond:
		return models.LeagueTypeRuby
	case models.LeagueTypeRuby:
		return models.LeagueTypeMatikan
	case models.LeagueTypeMatikan:
		return models.LeagueTypeMatikan
	default:
		return current
	}
}
