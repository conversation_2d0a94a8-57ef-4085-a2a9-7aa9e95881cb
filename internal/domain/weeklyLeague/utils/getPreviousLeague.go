package weeklyLeagueUtils

import "matiksOfficial/matiks-server-go/internal/models"

func GetPreviousLeague(current models.LeagueType) models.LeagueType {
	switch current {
	// Already at lowest
	case models.LeagueTypeBronze:
		return models.LeagueTypeBronze // Already at lowest
	case models.LeagueTypeSilver:
		return models.LeagueTypeBronze
	case models.LeagueTypeGold:
		return models.LeagueTypeSilver
	case models.LeagueTypeDiamond:
		return models.LeagueTypeGold
	case models.LeagueTypeRuby:
		return models.LeagueTypeDiamond
	case models.LeagueTypeMatikan:
		return models.LeagueTypeRuby
	default:
		return current
	}
}
