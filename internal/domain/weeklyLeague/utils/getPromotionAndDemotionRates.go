package weeklyLeagueUtils

import "matiksOfficial/matiks-server-go/internal/models"

func GetPromotionDemotionRates(leagueType models.LeagueType) (promotionRate, demotionRate float64) {
	switch leagueType {
	case models.LeagueTypeBronze:
		return 0.50, 1 // Higher promotion rate for Iron
	case models.LeagueTypeSilver:
		return 0.33, 0.75 // Standard rates for Silver
	case models.LeagueTypeGold:
		return 0.15, 0.77 // Lower promotion rate for Gold
	case models.LeagueTypeDiamond:
		return 0.15, 0.77 // Extremely selective promotion for Diamond
	case models.LeagueTypeRuby:
		return 0.15, 0.80 // Extremely selective promotion for Ruby
	case models.LeagueTypeMatikan:
		return 0, 0.80 // Extremely selective promotion for Matikan
	default:
		return 0.20, 0.80 // Default rates
	}
}
