package weeklyLeagueUtils

import "time"

func GetCurrentWeekStart() time.Time {
	t := time.Now()
	weekday := int(t.Weekday())

	weekday = (weekday + 6) % 7

	return time.Date(t.Year(), t.Month(), t.Day()-weekday, 0, 0, 0, 0, time.UTC)
}

func GetWeekStart(t time.Time) time.Time {
	weekday := int(t.Weekday())
	weekday = (weekday + 6) % 7

	return time.Date(t.Year(), t.Month(), t.Day()-weekday, 0, 0, 0, 0, time.UTC)
}

func GetWeekEnd(start time.Time) time.Time {
	return start.AddDate(0, 0, 7).Add(-1 * time.Second)
}

func GetPreviousWeekStart() time.Time {
	now := time.Now().UTC()
	currentWeekStart := GetWeekStart(now)
	return currentWeekStart.AddDate(0, 0, -7)
}
