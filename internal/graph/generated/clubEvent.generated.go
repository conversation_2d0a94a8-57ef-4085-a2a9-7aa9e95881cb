// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"context"
	"errors"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/ast"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// region    ************************** generated!.gotpl **************************

// endregion ************************** generated!.gotpl **************************

// region    ***************************** args.gotpl *****************************

// endregion ***************************** args.gotpl *****************************

// region    ************************** directives.gotpl **************************

// endregion ************************** directives.gotpl **************************

// region    **************************** field.gotpl *****************************

func (ec *executionContext) _ClubEvent_id(ctx context.Context, field graphql.CollectedField, obj *models.ClubEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEvent_id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEvent_id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEvent_clubEventPlayId(ctx context.Context, field graphql.CollectedField, obj *models.ClubEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEvent_clubEventPlayId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ClubEventPlayID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEvent_clubEventPlayId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEvent_participationCount(ctx context.Context, field graphql.CollectedField, obj *models.ClubEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEvent_participationCount(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ParticipationCount, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEvent_participationCount(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEvent_clubId(ctx context.Context, field graphql.CollectedField, obj *models.ClubEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEvent_clubId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ClubID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEvent_clubId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEvent_title(ctx context.Context, field graphql.CollectedField, obj *models.ClubEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEvent_title(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Title, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEvent_title(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEvent_visibility(ctx context.Context, field graphql.CollectedField, obj *models.ClubEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEvent_visibility(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Visibility, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.Visibility)
	fc.Result = res
	return ec.marshalOVisibility2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐVisibility(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEvent_visibility(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Visibility does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEvent_description(ctx context.Context, field graphql.CollectedField, obj *models.ClubEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEvent_description(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Description, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEvent_description(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEvent_clubEventType(ctx context.Context, field graphql.CollectedField, obj *models.ClubEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEvent_clubEventType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ClubEventType, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.ClubEventType)
	fc.Result = res
	return ec.marshalNClubEventType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐClubEventType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEvent_clubEventType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ClubEventType does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEvent_startTime(ctx context.Context, field graphql.CollectedField, obj *models.ClubEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEvent_startTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StartTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEvent_startTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEvent_gameConfig(ctx context.Context, field graphql.CollectedField, obj *models.ClubEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEvent_gameConfig(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GameConfig, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.GameConfig)
	fc.Result = res
	return ec.marshalNGameConfig2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameConfig(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEvent_gameConfig(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "timeLimit":
				return ec.fieldContext_GameConfig_timeLimit(ctx, field)
			case "numPlayers":
				return ec.fieldContext_GameConfig_numPlayers(ctx, field)
			case "gameType":
				return ec.fieldContext_GameConfig_gameType(ctx, field)
			case "questionTags":
				return ec.fieldContext_GameConfig_questionTags(ctx, field)
			case "difficultyLevel":
				return ec.fieldContext_GameConfig_difficultyLevel(ctx, field)
			case "maxTimePerQuestion":
				return ec.fieldContext_GameConfig_maxTimePerQuestion(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type GameConfig", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEvent_ratedEvent(ctx context.Context, field graphql.CollectedField, obj *models.ClubEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEvent_ratedEvent(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RatedEvent, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEvent_ratedEvent(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEvent_openToAll(ctx context.Context, field graphql.CollectedField, obj *models.ClubEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEvent_openToAll(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.OpenToAll, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEvent_openToAll(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEvent_playerSetting(ctx context.Context, field graphql.CollectedField, obj *models.ClubEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEvent_playerSetting(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PlayerSetting, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.PlayerSetting)
	fc.Result = res
	return ec.marshalOPlayerSetting2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPlayerSetting(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEvent_playerSetting(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "minRating":
				return ec.fieldContext_PlayerSetting_minRating(ctx, field)
			case "maxRating":
				return ec.fieldContext_PlayerSetting_maxRating(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PlayerSetting", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEvent_createdBy(ctx context.Context, field graphql.CollectedField, obj *models.ClubEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEvent_createdBy(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatedBy, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEvent_createdBy(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEvent_createdAt(ctx context.Context, field graphql.CollectedField, obj *models.ClubEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEvent_createdAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEvent_createdAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEvent_updatedAt(ctx context.Context, field graphql.CollectedField, obj *models.ClubEvent) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEvent_updatedAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UpdatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEvent_updatedAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEvent",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEventParticipant_id(ctx context.Context, field graphql.CollectedField, obj *models.ClubEventParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEventParticipant_id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEventParticipant_id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEventParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEventParticipant_clubEventId(ctx context.Context, field graphql.CollectedField, obj *models.ClubEventParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEventParticipant_clubEventId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ClubEventID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEventParticipant_clubEventId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEventParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEventParticipant_userId(ctx context.Context, field graphql.CollectedField, obj *models.ClubEventParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEventParticipant_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEventParticipant_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEventParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEventParticipant_joinedAt(ctx context.Context, field graphql.CollectedField, obj *models.ClubEventParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEventParticipant_joinedAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.JoinedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEventParticipant_joinedAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEventParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEventsPage_results(ctx context.Context, field graphql.CollectedField, obj *models.ClubEventsPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEventsPage_results(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Results, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.ClubEvent)
	fc.Result = res
	return ec.marshalNClubEvent2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐClubEventᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEventsPage_results(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEventsPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_ClubEvent_id(ctx, field)
			case "clubEventPlayId":
				return ec.fieldContext_ClubEvent_clubEventPlayId(ctx, field)
			case "participationCount":
				return ec.fieldContext_ClubEvent_participationCount(ctx, field)
			case "clubId":
				return ec.fieldContext_ClubEvent_clubId(ctx, field)
			case "title":
				return ec.fieldContext_ClubEvent_title(ctx, field)
			case "visibility":
				return ec.fieldContext_ClubEvent_visibility(ctx, field)
			case "description":
				return ec.fieldContext_ClubEvent_description(ctx, field)
			case "clubEventType":
				return ec.fieldContext_ClubEvent_clubEventType(ctx, field)
			case "startTime":
				return ec.fieldContext_ClubEvent_startTime(ctx, field)
			case "gameConfig":
				return ec.fieldContext_ClubEvent_gameConfig(ctx, field)
			case "ratedEvent":
				return ec.fieldContext_ClubEvent_ratedEvent(ctx, field)
			case "openToAll":
				return ec.fieldContext_ClubEvent_openToAll(ctx, field)
			case "playerSetting":
				return ec.fieldContext_ClubEvent_playerSetting(ctx, field)
			case "createdBy":
				return ec.fieldContext_ClubEvent_createdBy(ctx, field)
			case "createdAt":
				return ec.fieldContext_ClubEvent_createdAt(ctx, field)
			case "updatedAt":
				return ec.fieldContext_ClubEvent_updatedAt(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ClubEvent", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEventsPage_pageNumber(ctx context.Context, field graphql.CollectedField, obj *models.ClubEventsPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEventsPage_pageNumber(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PageNumber, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEventsPage_pageNumber(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEventsPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEventsPage_pageSize(ctx context.Context, field graphql.CollectedField, obj *models.ClubEventsPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEventsPage_pageSize(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PageSize, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEventsPage_pageSize(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEventsPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEventsPage_hasMore(ctx context.Context, field graphql.CollectedField, obj *models.ClubEventsPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEventsPage_hasMore(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasMore, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEventsPage_hasMore(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEventsPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ClubEventsPage_totalResults(ctx context.Context, field graphql.CollectedField, obj *models.ClubEventsPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ClubEventsPage_totalResults(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalResults, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ClubEventsPage_totalResults(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ClubEventsPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PlayerSetting_minRating(ctx context.Context, field graphql.CollectedField, obj *models.PlayerSetting) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PlayerSetting_minRating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.MinRating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PlayerSetting_minRating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PlayerSetting",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PlayerSetting_maxRating(ctx context.Context, field graphql.CollectedField, obj *models.PlayerSetting) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PlayerSetting_maxRating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.MaxRating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PlayerSetting_maxRating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PlayerSetting",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

// endregion **************************** field.gotpl *****************************

// region    **************************** input.gotpl *****************************

func (ec *executionContext) unmarshalInputCreateClubEventInput(ctx context.Context, obj any) (models.CreateClubEventInput, error) {
	var it models.CreateClubEventInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"clubId", "title", "description", "clubEventType", "startTime", "gameConfig", "openToAll", "playerSetting", "ratedEvent", "visibility"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "clubId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("clubId"))
			data, err := ec.unmarshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.ClubID = data
		case "title":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("title"))
			data, err := ec.unmarshalNString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Title = data
		case "description":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("description"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Description = data
		case "clubEventType":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("clubEventType"))
			data, err := ec.unmarshalNClubEventType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐClubEventType(ctx, v)
			if err != nil {
				return it, err
			}
			it.ClubEventType = data
		case "startTime":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("startTime"))
			data, err := ec.unmarshalNTime2timeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.StartTime = data
		case "gameConfig":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("gameConfig"))
			data, err := ec.unmarshalOGameConfigInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameConfigInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.GameConfig = data
		case "openToAll":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("openToAll"))
			data, err := ec.unmarshalOBoolean2ᚖbool(ctx, v)
			if err != nil {
				return it, err
			}
			it.OpenToAll = data
		case "playerSetting":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("playerSetting"))
			data, err := ec.unmarshalOPlayerSettingInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPlayerSettingInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.PlayerSetting = data
		case "ratedEvent":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("ratedEvent"))
			data, err := ec.unmarshalOBoolean2ᚖbool(ctx, v)
			if err != nil {
				return it, err
			}
			it.RatedEvent = data
		case "visibility":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("visibility"))
			data, err := ec.unmarshalOVisibility2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐVisibility(ctx, v)
			if err != nil {
				return it, err
			}
			it.Visibility = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputPlayerSettingInput(ctx context.Context, obj any) (models.PlayerSettingInput, error) {
	var it models.PlayerSettingInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"minRating", "maxRating"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "minRating":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("minRating"))
			data, err := ec.unmarshalNInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.MinRating = data
		case "maxRating":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("maxRating"))
			data, err := ec.unmarshalNInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.MaxRating = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputUpdateClubEventInput(ctx context.Context, obj any) (models.UpdateClubEventInput, error) {
	var it models.UpdateClubEventInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"eventId", "title", "description", "startTime", "endTime"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "eventId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("eventId"))
			data, err := ec.unmarshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.EventID = data
		case "title":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("title"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Title = data
		case "description":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("description"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Description = data
		case "startTime":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("startTime"))
			data, err := ec.unmarshalOTime2ᚖtimeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.StartTime = data
		case "endTime":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("endTime"))
			data, err := ec.unmarshalOTime2ᚖtimeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.EndTime = data
		}
	}

	return it, nil
}

// endregion **************************** input.gotpl *****************************

// region    ************************** interface.gotpl ***************************

// endregion ************************** interface.gotpl ***************************

// region    **************************** object.gotpl ****************************

var clubEventImplementors = []string{"ClubEvent"}

func (ec *executionContext) _ClubEvent(ctx context.Context, sel ast.SelectionSet, obj *models.ClubEvent) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, clubEventImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ClubEvent")
		case "id":
			out.Values[i] = ec._ClubEvent_id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "clubEventPlayId":
			out.Values[i] = ec._ClubEvent_clubEventPlayId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "participationCount":
			out.Values[i] = ec._ClubEvent_participationCount(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "clubId":
			out.Values[i] = ec._ClubEvent_clubId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "title":
			out.Values[i] = ec._ClubEvent_title(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "visibility":
			out.Values[i] = ec._ClubEvent_visibility(ctx, field, obj)
		case "description":
			out.Values[i] = ec._ClubEvent_description(ctx, field, obj)
		case "clubEventType":
			out.Values[i] = ec._ClubEvent_clubEventType(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "startTime":
			out.Values[i] = ec._ClubEvent_startTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "gameConfig":
			out.Values[i] = ec._ClubEvent_gameConfig(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "ratedEvent":
			out.Values[i] = ec._ClubEvent_ratedEvent(ctx, field, obj)
		case "openToAll":
			out.Values[i] = ec._ClubEvent_openToAll(ctx, field, obj)
		case "playerSetting":
			out.Values[i] = ec._ClubEvent_playerSetting(ctx, field, obj)
		case "createdBy":
			out.Values[i] = ec._ClubEvent_createdBy(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "createdAt":
			out.Values[i] = ec._ClubEvent_createdAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "updatedAt":
			out.Values[i] = ec._ClubEvent_updatedAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var clubEventParticipantImplementors = []string{"ClubEventParticipant"}

func (ec *executionContext) _ClubEventParticipant(ctx context.Context, sel ast.SelectionSet, obj *models.ClubEventParticipant) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, clubEventParticipantImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ClubEventParticipant")
		case "id":
			out.Values[i] = ec._ClubEventParticipant_id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "clubEventId":
			out.Values[i] = ec._ClubEventParticipant_clubEventId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userId":
			out.Values[i] = ec._ClubEventParticipant_userId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "joinedAt":
			out.Values[i] = ec._ClubEventParticipant_joinedAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var clubEventsPageImplementors = []string{"ClubEventsPage"}

func (ec *executionContext) _ClubEventsPage(ctx context.Context, sel ast.SelectionSet, obj *models.ClubEventsPage) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, clubEventsPageImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ClubEventsPage")
		case "results":
			out.Values[i] = ec._ClubEventsPage_results(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "pageNumber":
			out.Values[i] = ec._ClubEventsPage_pageNumber(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "pageSize":
			out.Values[i] = ec._ClubEventsPage_pageSize(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "hasMore":
			out.Values[i] = ec._ClubEventsPage_hasMore(ctx, field, obj)
		case "totalResults":
			out.Values[i] = ec._ClubEventsPage_totalResults(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var playerSettingImplementors = []string{"PlayerSetting"}

func (ec *executionContext) _PlayerSetting(ctx context.Context, sel ast.SelectionSet, obj *models.PlayerSetting) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, playerSettingImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PlayerSetting")
		case "minRating":
			out.Values[i] = ec._PlayerSetting_minRating(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "maxRating":
			out.Values[i] = ec._PlayerSetting_maxRating(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

// endregion **************************** object.gotpl ****************************

// region    ***************************** type.gotpl *****************************

func (ec *executionContext) marshalNClubEvent2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐClubEvent(ctx context.Context, sel ast.SelectionSet, v models.ClubEvent) graphql.Marshaler {
	return ec._ClubEvent(ctx, sel, &v)
}

func (ec *executionContext) marshalNClubEvent2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐClubEventᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.ClubEvent) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNClubEvent2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐClubEvent(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNClubEvent2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐClubEvent(ctx context.Context, sel ast.SelectionSet, v *models.ClubEvent) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._ClubEvent(ctx, sel, v)
}

func (ec *executionContext) marshalNClubEventParticipant2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐClubEventParticipant(ctx context.Context, sel ast.SelectionSet, v models.ClubEventParticipant) graphql.Marshaler {
	return ec._ClubEventParticipant(ctx, sel, &v)
}

func (ec *executionContext) marshalNClubEventParticipant2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐClubEventParticipant(ctx context.Context, sel ast.SelectionSet, v *models.ClubEventParticipant) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._ClubEventParticipant(ctx, sel, v)
}

func (ec *executionContext) unmarshalNClubEventType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐClubEventType(ctx context.Context, v any) (models.ClubEventType, error) {
	var res models.ClubEventType
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNClubEventType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐClubEventType(ctx context.Context, sel ast.SelectionSet, v models.ClubEventType) graphql.Marshaler {
	return v
}

func (ec *executionContext) marshalNClubEventsPage2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐClubEventsPage(ctx context.Context, sel ast.SelectionSet, v models.ClubEventsPage) graphql.Marshaler {
	return ec._ClubEventsPage(ctx, sel, &v)
}

func (ec *executionContext) marshalNClubEventsPage2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐClubEventsPage(ctx context.Context, sel ast.SelectionSet, v *models.ClubEventsPage) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._ClubEventsPage(ctx, sel, v)
}

func (ec *executionContext) unmarshalNCreateClubEventInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCreateClubEventInput(ctx context.Context, v any) (models.CreateClubEventInput, error) {
	res, err := ec.unmarshalInputCreateClubEventInput(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) unmarshalNUpdateClubEventInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUpdateClubEventInput(ctx context.Context, v any) (models.UpdateClubEventInput, error) {
	res, err := ec.unmarshalInputUpdateClubEventInput(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOClubEvent2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐClubEvent(ctx context.Context, sel ast.SelectionSet, v *models.ClubEvent) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._ClubEvent(ctx, sel, v)
}

func (ec *executionContext) unmarshalOClubEventType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐClubEventType(ctx context.Context, v any) (*models.ClubEventType, error) {
	if v == nil {
		return nil, nil
	}
	var res = new(models.ClubEventType)
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOClubEventType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐClubEventType(ctx context.Context, sel ast.SelectionSet, v *models.ClubEventType) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return v
}

func (ec *executionContext) marshalOPlayerSetting2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPlayerSetting(ctx context.Context, sel ast.SelectionSet, v *models.PlayerSetting) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._PlayerSetting(ctx, sel, v)
}

func (ec *executionContext) unmarshalOPlayerSettingInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPlayerSettingInput(ctx context.Context, v any) (*models.PlayerSettingInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputPlayerSettingInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

// endregion ***************************** type.gotpl *****************************
