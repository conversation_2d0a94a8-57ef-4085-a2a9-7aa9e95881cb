// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"context"
	"errors"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/ast"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// region    ************************** generated!.gotpl **************************

// endregion ************************** generated!.gotpl **************************

// region    ***************************** args.gotpl *****************************

// endregion ***************************** args.gotpl *****************************

// region    ************************** directives.gotpl **************************

// endregion ************************** directives.gotpl **************************

// region    **************************** field.gotpl *****************************

func (ec *executionContext) _Contest__id(ctx context.Context, field graphql.CollectedField, obj *models.Contest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Contest__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Contest__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Contest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Contest_clubId(ctx context.Context, field graphql.CollectedField, obj *models.Contest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Contest_clubId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ClubID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Contest_clubId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Contest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Contest_name(ctx context.Context, field graphql.CollectedField, obj *models.Contest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Contest_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Contest_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Contest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Contest_description(ctx context.Context, field graphql.CollectedField, obj *models.Contest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Contest_description(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Description, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Contest_description(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Contest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Contest_hostedBy(ctx context.Context, field graphql.CollectedField, obj *models.Contest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Contest_hostedBy(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HostedBy, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Contest_hostedBy(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Contest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Contest_hostedByV2(ctx context.Context, field graphql.CollectedField, obj *models.Contest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Contest_hostedByV2(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HostedByV2, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.HostDetails)
	fc.Result = res
	return ec.marshalOHostDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐHostDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Contest_hostedByV2(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Contest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "name":
				return ec.fieldContext_HostDetails_name(ctx, field)
			case "logo":
				return ec.fieldContext_HostDetails_logo(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type HostDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Contest_details(ctx context.Context, field graphql.CollectedField, obj *models.Contest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Contest_details(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Details, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.ContestDetails)
	fc.Result = res
	return ec.marshalOContestDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Contest_details(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Contest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "about":
				return ec.fieldContext_ContestDetails_about(ctx, field)
			case "requirements":
				return ec.fieldContext_ContestDetails_requirements(ctx, field)
			case "instructions":
				return ec.fieldContext_ContestDetails_instructions(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ContestDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Contest_startTime(ctx context.Context, field graphql.CollectedField, obj *models.Contest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Contest_startTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StartTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Contest_startTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Contest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Contest_endTime(ctx context.Context, field graphql.CollectedField, obj *models.Contest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Contest_endTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EndTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Contest_endTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Contest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Contest_registrationCount(ctx context.Context, field graphql.CollectedField, obj *models.Contest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Contest_registrationCount(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RegistrationCount, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Contest_registrationCount(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Contest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Contest_contestDuration(ctx context.Context, field graphql.CollectedField, obj *models.Contest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Contest_contestDuration(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ContestDuration, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Contest_contestDuration(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Contest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Contest_registrationStartTime(ctx context.Context, field graphql.CollectedField, obj *models.Contest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Contest_registrationStartTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RegistrationStartTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Contest_registrationStartTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Contest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Contest_registrationEndTime(ctx context.Context, field graphql.CollectedField, obj *models.Contest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Contest_registrationEndTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RegistrationEndTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Contest_registrationEndTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Contest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Contest_encryptedQuestions(ctx context.Context, field graphql.CollectedField, obj *models.Contest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Contest_encryptedQuestions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EncryptedQuestions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*string)
	fc.Result = res
	return ec.marshalOString2ᚕᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Contest_encryptedQuestions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Contest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Contest_registrationForm(ctx context.Context, field graphql.CollectedField, obj *models.Contest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Contest_registrationForm(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RegistrationForm, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.RegistrationForm)
	fc.Result = res
	return ec.marshalORegistrationForm2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationForm(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Contest_registrationForm(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Contest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_RegistrationForm__id(ctx, field)
			case "fields":
				return ec.fieldContext_RegistrationForm_fields(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type RegistrationForm", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Contest_status(ctx context.Context, field graphql.CollectedField, obj *models.Contest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Contest_status(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Status, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.ContestStatus)
	fc.Result = res
	return ec.marshalNContestStatus2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestStatus(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Contest_status(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Contest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ContestStatus does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Contest_currentUserParticipation(ctx context.Context, field graphql.CollectedField, obj *models.Contest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Contest_currentUserParticipation(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CurrentUserParticipation, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.CurrentUserParticipation)
	fc.Result = res
	return ec.marshalOCurrentUserParticipation2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCurrentUserParticipation(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Contest_currentUserParticipation(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Contest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "contestId":
				return ec.fieldContext_CurrentUserParticipation_contestId(ctx, field)
			case "userId":
				return ec.fieldContext_CurrentUserParticipation_userId(ctx, field)
			case "registrationData":
				return ec.fieldContext_CurrentUserParticipation_registrationData(ctx, field)
			case "score":
				return ec.fieldContext_CurrentUserParticipation_score(ctx, field)
			case "lastSubmissionTime":
				return ec.fieldContext_CurrentUserParticipation_lastSubmissionTime(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type CurrentUserParticipation", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Contest_recentParticipants(ctx context.Context, field graphql.CollectedField, obj *models.Contest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Contest_recentParticipants(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RecentParticipants, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.UserPublicDetails)
	fc.Result = res
	return ec.marshalOUserPublicDetails2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetailsᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Contest_recentParticipants(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Contest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPublicDetails__id(ctx, field)
			case "name":
				return ec.fieldContext_UserPublicDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_UserPublicDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserPublicDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserPublicDetails_rating(ctx, field)
			case "badge":
				return ec.fieldContext_UserPublicDetails_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_UserPublicDetails_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_UserPublicDetails_isGuest(ctx, field)
			case "globalRank":
				return ec.fieldContext_UserPublicDetails_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_UserPublicDetails_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_UserPublicDetails_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_UserPublicDetails_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_UserPublicDetails_stats(ctx, field)
			case "userStreaks":
				return ec.fieldContext_UserPublicDetails_userStreaks(ctx, field)
			case "ratingV2":
				return ec.fieldContext_UserPublicDetails_ratingV2(ctx, field)
			case "institutionId":
				return ec.fieldContext_UserPublicDetails_institutionId(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPublicDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestDetails_about(ctx context.Context, field graphql.CollectedField, obj *models.ContestDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestDetails_about(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.About, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestDetails_about(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestDetails_requirements(ctx context.Context, field graphql.CollectedField, obj *models.ContestDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestDetails_requirements(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Requirements, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestDetails_requirements(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestDetails_instructions(ctx context.Context, field graphql.CollectedField, obj *models.ContestDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestDetails_instructions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Instructions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestDetails_instructions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestLeaderboard_participants(ctx context.Context, field graphql.CollectedField, obj *models.ContestLeaderboard) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestLeaderboard_participants(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Participants, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.ContestParticipant)
	fc.Result = res
	return ec.marshalNContestParticipant2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestParticipantᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestLeaderboard_participants(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestLeaderboard",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "user":
				return ec.fieldContext_ContestParticipant_user(ctx, field)
			case "registrationData":
				return ec.fieldContext_ContestParticipant_registrationData(ctx, field)
			case "score":
				return ec.fieldContext_ContestParticipant_score(ctx, field)
			case "startTime":
				return ec.fieldContext_ContestParticipant_startTime(ctx, field)
			case "correctSubmission":
				return ec.fieldContext_ContestParticipant_correctSubmission(ctx, field)
			case "incorrectSubmission":
				return ec.fieldContext_ContestParticipant_incorrectSubmission(ctx, field)
			case "rank":
				return ec.fieldContext_ContestParticipant_rank(ctx, field)
			case "lastSubmissionTime":
				return ec.fieldContext_ContestParticipant_lastSubmissionTime(ctx, field)
			case "isVirtualParticipant":
				return ec.fieldContext_ContestParticipant_isVirtualParticipant(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ContestParticipant", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestLeaderboard_totalParticipants(ctx context.Context, field graphql.CollectedField, obj *models.ContestLeaderboard) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestLeaderboard_totalParticipants(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalParticipants, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestLeaderboard_totalParticipants(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestLeaderboard",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestParticipant_user(ctx context.Context, field graphql.CollectedField, obj *models.ContestParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestParticipant_user(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.User, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.UserPublicDetails)
	fc.Result = res
	return ec.marshalNUserPublicDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestParticipant_user(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPublicDetails__id(ctx, field)
			case "name":
				return ec.fieldContext_UserPublicDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_UserPublicDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserPublicDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserPublicDetails_rating(ctx, field)
			case "badge":
				return ec.fieldContext_UserPublicDetails_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_UserPublicDetails_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_UserPublicDetails_isGuest(ctx, field)
			case "globalRank":
				return ec.fieldContext_UserPublicDetails_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_UserPublicDetails_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_UserPublicDetails_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_UserPublicDetails_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_UserPublicDetails_stats(ctx, field)
			case "userStreaks":
				return ec.fieldContext_UserPublicDetails_userStreaks(ctx, field)
			case "ratingV2":
				return ec.fieldContext_UserPublicDetails_ratingV2(ctx, field)
			case "institutionId":
				return ec.fieldContext_UserPublicDetails_institutionId(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPublicDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestParticipant_registrationData(ctx context.Context, field graphql.CollectedField, obj *models.ContestParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestParticipant_registrationData(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RegistrationData, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.RegistrationFieldData)
	fc.Result = res
	return ec.marshalORegistrationFieldData2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFieldData(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestParticipant_registrationData(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "name":
				return ec.fieldContext_RegistrationFieldData_name(ctx, field)
			case "values":
				return ec.fieldContext_RegistrationFieldData_values(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type RegistrationFieldData", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestParticipant_score(ctx context.Context, field graphql.CollectedField, obj *models.ContestParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestParticipant_score(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Score, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestParticipant_score(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestParticipant_startTime(ctx context.Context, field graphql.CollectedField, obj *models.ContestParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestParticipant_startTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StartTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestParticipant_startTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestParticipant_correctSubmission(ctx context.Context, field graphql.CollectedField, obj *models.ContestParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestParticipant_correctSubmission(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CorrectSubmission, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestParticipant_correctSubmission(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestParticipant_incorrectSubmission(ctx context.Context, field graphql.CollectedField, obj *models.ContestParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestParticipant_incorrectSubmission(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IncorrectSubmission, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestParticipant_incorrectSubmission(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestParticipant_rank(ctx context.Context, field graphql.CollectedField, obj *models.ContestParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestParticipant_rank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestParticipant_rank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestParticipant_lastSubmissionTime(ctx context.Context, field graphql.CollectedField, obj *models.ContestParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestParticipant_lastSubmissionTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LastSubmissionTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestParticipant_lastSubmissionTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestParticipant_isVirtualParticipant(ctx context.Context, field graphql.CollectedField, obj *models.ContestParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestParticipant_isVirtualParticipant(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsVirtualParticipant, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestParticipant_isVirtualParticipant(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestQuestion__id(ctx context.Context, field graphql.CollectedField, obj *models.ContestQuestion) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestQuestion__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestQuestion__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestQuestion",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestQuestion_question(ctx context.Context, field graphql.CollectedField, obj *models.ContestQuestion) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestQuestion_question(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Question, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.Question)
	fc.Result = res
	return ec.marshalNQuestion2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐQuestion(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestQuestion_question(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestQuestion",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_Question_id(ctx, field)
			case "expression":
				return ec.fieldContext_Question_expression(ctx, field)
			case "description":
				return ec.fieldContext_Question_description(ctx, field)
			case "options":
				return ec.fieldContext_Question_options(ctx, field)
			case "answers":
				return ec.fieldContext_Question_answers(ctx, field)
			case "questionType":
				return ec.fieldContext_Question_questionType(ctx, field)
			case "rating":
				return ec.fieldContext_Question_rating(ctx, field)
			case "maxTimeLimit":
				return ec.fieldContext_Question_maxTimeLimit(ctx, field)
			case "tags":
				return ec.fieldContext_Question_tags(ctx, field)
			case "fastestTimeTaken":
				return ec.fieldContext_Question_fastestTimeTaken(ctx, field)
			case "presetIdentifier":
				return ec.fieldContext_Question_presetIdentifier(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Question", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestQuestion_points(ctx context.Context, field graphql.CollectedField, obj *models.ContestQuestion) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestQuestion_points(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Points, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestQuestion_points(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestQuestion",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestSubmission_questionId(ctx context.Context, field graphql.CollectedField, obj *models.ContestSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestSubmission_questionId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.QuestionID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestSubmission_questionId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestSubmission_answer(ctx context.Context, field graphql.CollectedField, obj *models.ContestSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestSubmission_answer(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Answer, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestSubmission_answer(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestSubmission_isCorrect(ctx context.Context, field graphql.CollectedField, obj *models.ContestSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestSubmission_isCorrect(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsCorrect, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestSubmission_isCorrect(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestSubmission_submissionTime(ctx context.Context, field graphql.CollectedField, obj *models.ContestSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestSubmission_submissionTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.SubmissionTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestSubmission_submissionTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ContestSubmission_points(ctx context.Context, field graphql.CollectedField, obj *models.ContestSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ContestSubmission_points(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Points, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ContestSubmission_points(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ContestSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CurrentUserParticipation_contestId(ctx context.Context, field graphql.CollectedField, obj *models.CurrentUserParticipation) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CurrentUserParticipation_contestId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ContestID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CurrentUserParticipation_contestId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CurrentUserParticipation",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CurrentUserParticipation_userId(ctx context.Context, field graphql.CollectedField, obj *models.CurrentUserParticipation) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CurrentUserParticipation_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CurrentUserParticipation_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CurrentUserParticipation",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CurrentUserParticipation_registrationData(ctx context.Context, field graphql.CollectedField, obj *models.CurrentUserParticipation) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CurrentUserParticipation_registrationData(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RegistrationData, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.RegistrationFieldData)
	fc.Result = res
	return ec.marshalORegistrationFieldData2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFieldData(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CurrentUserParticipation_registrationData(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CurrentUserParticipation",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "name":
				return ec.fieldContext_RegistrationFieldData_name(ctx, field)
			case "values":
				return ec.fieldContext_RegistrationFieldData_values(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type RegistrationFieldData", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _CurrentUserParticipation_score(ctx context.Context, field graphql.CollectedField, obj *models.CurrentUserParticipation) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CurrentUserParticipation_score(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Score, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CurrentUserParticipation_score(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CurrentUserParticipation",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CurrentUserParticipation_lastSubmissionTime(ctx context.Context, field graphql.CollectedField, obj *models.CurrentUserParticipation) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CurrentUserParticipation_lastSubmissionTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LastSubmissionTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CurrentUserParticipation_lastSubmissionTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CurrentUserParticipation",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _HostDetails_name(ctx context.Context, field graphql.CollectedField, obj *models.HostDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_HostDetails_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_HostDetails_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "HostDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _HostDetails_logo(ctx context.Context, field graphql.CollectedField, obj *models.HostDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_HostDetails_logo(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Logo, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_HostDetails_logo(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "HostDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PaginatedContests_contests(ctx context.Context, field graphql.CollectedField, obj *models.PaginatedContests) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PaginatedContests_contests(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Contests, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.Contest)
	fc.Result = res
	return ec.marshalNContest2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PaginatedContests_contests(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PaginatedContests",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_Contest__id(ctx, field)
			case "clubId":
				return ec.fieldContext_Contest_clubId(ctx, field)
			case "name":
				return ec.fieldContext_Contest_name(ctx, field)
			case "description":
				return ec.fieldContext_Contest_description(ctx, field)
			case "hostedBy":
				return ec.fieldContext_Contest_hostedBy(ctx, field)
			case "hostedByV2":
				return ec.fieldContext_Contest_hostedByV2(ctx, field)
			case "details":
				return ec.fieldContext_Contest_details(ctx, field)
			case "startTime":
				return ec.fieldContext_Contest_startTime(ctx, field)
			case "endTime":
				return ec.fieldContext_Contest_endTime(ctx, field)
			case "registrationCount":
				return ec.fieldContext_Contest_registrationCount(ctx, field)
			case "contestDuration":
				return ec.fieldContext_Contest_contestDuration(ctx, field)
			case "registrationStartTime":
				return ec.fieldContext_Contest_registrationStartTime(ctx, field)
			case "registrationEndTime":
				return ec.fieldContext_Contest_registrationEndTime(ctx, field)
			case "encryptedQuestions":
				return ec.fieldContext_Contest_encryptedQuestions(ctx, field)
			case "registrationForm":
				return ec.fieldContext_Contest_registrationForm(ctx, field)
			case "status":
				return ec.fieldContext_Contest_status(ctx, field)
			case "currentUserParticipation":
				return ec.fieldContext_Contest_currentUserParticipation(ctx, field)
			case "recentParticipants":
				return ec.fieldContext_Contest_recentParticipants(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Contest", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PaginatedContests_totalCount(ctx context.Context, field graphql.CollectedField, obj *models.PaginatedContests) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PaginatedContests_totalCount(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalCount, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PaginatedContests_totalCount(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PaginatedContests",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _RegistrationFieldData_name(ctx context.Context, field graphql.CollectedField, obj *models.RegistrationFieldData) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RegistrationFieldData_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RegistrationFieldData_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RegistrationFieldData",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _RegistrationFieldData_values(ctx context.Context, field graphql.CollectedField, obj *models.RegistrationFieldData) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RegistrationFieldData_values(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Values, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]string)
	fc.Result = res
	return ec.marshalOString2ᚕstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RegistrationFieldData_values(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RegistrationFieldData",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserContestResult_totalScore(ctx context.Context, field graphql.CollectedField, obj *models.UserContestResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserContestResult_totalScore(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalScore, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserContestResult_totalScore(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserContestResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserContestResult_startTime(ctx context.Context, field graphql.CollectedField, obj *models.UserContestResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserContestResult_startTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StartTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserContestResult_startTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserContestResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserContestResult_lastSubmissionTime(ctx context.Context, field graphql.CollectedField, obj *models.UserContestResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserContestResult_lastSubmissionTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LastSubmissionTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserContestResult_lastSubmissionTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserContestResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserContestResult_correctSubmission(ctx context.Context, field graphql.CollectedField, obj *models.UserContestResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserContestResult_correctSubmission(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CorrectSubmission, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserContestResult_correctSubmission(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserContestResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserContestResult_incorrectSubmission(ctx context.Context, field graphql.CollectedField, obj *models.UserContestResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserContestResult_incorrectSubmission(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IncorrectSubmission, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserContestResult_incorrectSubmission(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserContestResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserContestResult_user(ctx context.Context, field graphql.CollectedField, obj *models.UserContestResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserContestResult_user(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.User, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.UserPublicDetails)
	fc.Result = res
	return ec.marshalOUserPublicDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserContestResult_user(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserContestResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPublicDetails__id(ctx, field)
			case "name":
				return ec.fieldContext_UserPublicDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_UserPublicDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserPublicDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserPublicDetails_rating(ctx, field)
			case "badge":
				return ec.fieldContext_UserPublicDetails_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_UserPublicDetails_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_UserPublicDetails_isGuest(ctx, field)
			case "globalRank":
				return ec.fieldContext_UserPublicDetails_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_UserPublicDetails_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_UserPublicDetails_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_UserPublicDetails_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_UserPublicDetails_stats(ctx, field)
			case "userStreaks":
				return ec.fieldContext_UserPublicDetails_userStreaks(ctx, field)
			case "ratingV2":
				return ec.fieldContext_UserPublicDetails_ratingV2(ctx, field)
			case "institutionId":
				return ec.fieldContext_UserPublicDetails_institutionId(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPublicDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserContestResult_rank(ctx context.Context, field graphql.CollectedField, obj *models.UserContestResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserContestResult_rank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserContestResult_rank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserContestResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserContestResult_questionsSolved(ctx context.Context, field graphql.CollectedField, obj *models.UserContestResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserContestResult_questionsSolved(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.QuestionsSolved, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserContestResult_questionsSolved(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserContestResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserContestResult_totalParticipants(ctx context.Context, field graphql.CollectedField, obj *models.UserContestResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserContestResult_totalParticipants(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalParticipants, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserContestResult_totalParticipants(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserContestResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserContestResult_isVirtualParticipant(ctx context.Context, field graphql.CollectedField, obj *models.UserContestResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserContestResult_isVirtualParticipant(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsVirtualParticipant, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserContestResult_isVirtualParticipant(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserContestResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserContestSubmissions_totalScore(ctx context.Context, field graphql.CollectedField, obj *models.UserContestSubmissions) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserContestSubmissions_totalScore(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalScore, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserContestSubmissions_totalScore(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserContestSubmissions",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserContestSubmissions_startTime(ctx context.Context, field graphql.CollectedField, obj *models.UserContestSubmissions) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserContestSubmissions_startTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StartTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserContestSubmissions_startTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserContestSubmissions",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserContestSubmissions_lastSubmissionTime(ctx context.Context, field graphql.CollectedField, obj *models.UserContestSubmissions) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserContestSubmissions_lastSubmissionTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LastSubmissionTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserContestSubmissions_lastSubmissionTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserContestSubmissions",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserContestSubmissions_correctSubmission(ctx context.Context, field graphql.CollectedField, obj *models.UserContestSubmissions) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserContestSubmissions_correctSubmission(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CorrectSubmission, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserContestSubmissions_correctSubmission(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserContestSubmissions",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserContestSubmissions_incorrectSubmission(ctx context.Context, field graphql.CollectedField, obj *models.UserContestSubmissions) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserContestSubmissions_incorrectSubmission(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IncorrectSubmission, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserContestSubmissions_incorrectSubmission(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserContestSubmissions",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserContestSubmissions_submissions(ctx context.Context, field graphql.CollectedField, obj *models.UserContestSubmissions) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserContestSubmissions_submissions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Submissions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.ContestSubmission)
	fc.Result = res
	return ec.marshalNContestSubmission2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestSubmissionᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserContestSubmissions_submissions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserContestSubmissions",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "questionId":
				return ec.fieldContext_ContestSubmission_questionId(ctx, field)
			case "answer":
				return ec.fieldContext_ContestSubmission_answer(ctx, field)
			case "isCorrect":
				return ec.fieldContext_ContestSubmission_isCorrect(ctx, field)
			case "submissionTime":
				return ec.fieldContext_ContestSubmission_submissionTime(ctx, field)
			case "points":
				return ec.fieldContext_ContestSubmission_points(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ContestSubmission", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserContestSubmissions_user(ctx context.Context, field graphql.CollectedField, obj *models.UserContestSubmissions) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserContestSubmissions_user(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.User, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.UserPublicDetails)
	fc.Result = res
	return ec.marshalOUserPublicDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserContestSubmissions_user(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserContestSubmissions",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPublicDetails__id(ctx, field)
			case "name":
				return ec.fieldContext_UserPublicDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_UserPublicDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserPublicDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserPublicDetails_rating(ctx, field)
			case "badge":
				return ec.fieldContext_UserPublicDetails_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_UserPublicDetails_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_UserPublicDetails_isGuest(ctx, field)
			case "globalRank":
				return ec.fieldContext_UserPublicDetails_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_UserPublicDetails_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_UserPublicDetails_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_UserPublicDetails_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_UserPublicDetails_stats(ctx, field)
			case "userStreaks":
				return ec.fieldContext_UserPublicDetails_userStreaks(ctx, field)
			case "ratingV2":
				return ec.fieldContext_UserPublicDetails_ratingV2(ctx, field)
			case "institutionId":
				return ec.fieldContext_UserPublicDetails_institutionId(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPublicDetails", field.Name)
		},
	}
	return fc, nil
}

// endregion **************************** field.gotpl *****************************

// region    **************************** input.gotpl *****************************

func (ec *executionContext) unmarshalInputContestDetailsInput(ctx context.Context, obj any) (models.ContestDetailsInput, error) {
	var it models.ContestDetailsInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"about", "requirements", "instructions"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "about":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("about"))
			data, err := ec.unmarshalOString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.About = data
		case "requirements":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("requirements"))
			data, err := ec.unmarshalOString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Requirements = data
		case "instructions":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("instructions"))
			data, err := ec.unmarshalOString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Instructions = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputContestQuestionInput(ctx context.Context, obj any) (models.ContestQuestionInput, error) {
	var it models.ContestQuestionInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"questionId", "points"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "questionId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("questionId"))
			data, err := ec.unmarshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.QuestionID = data
		case "points":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("points"))
			data, err := ec.unmarshalNInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.Points = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputCreateContestInput(ctx context.Context, obj any) (models.CreateContestInput, error) {
	var it models.CreateContestInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"name", "description", "hostName", "hostLogo", "startTime", "endTime", "contestDuration", "registrationStartTime", "details", "registrationEndTime", "registrationForm"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "name":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("name"))
			data, err := ec.unmarshalNString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Name = data
		case "description":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("description"))
			data, err := ec.unmarshalOString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Description = data
		case "hostName":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("hostName"))
			data, err := ec.unmarshalOString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.HostName = data
		case "hostLogo":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("hostLogo"))
			data, err := ec.unmarshalOString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.HostLogo = data
		case "startTime":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("startTime"))
			data, err := ec.unmarshalNTime2timeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.StartTime = data
		case "endTime":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("endTime"))
			data, err := ec.unmarshalNTime2timeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.EndTime = data
		case "contestDuration":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("contestDuration"))
			data, err := ec.unmarshalNInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.ContestDuration = data
		case "registrationStartTime":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("registrationStartTime"))
			data, err := ec.unmarshalOTime2timeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.RegistrationStartTime = data
		case "details":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("details"))
			data, err := ec.unmarshalOContestDetailsInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestDetailsInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.Details = data
		case "registrationEndTime":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("registrationEndTime"))
			data, err := ec.unmarshalOTime2timeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.RegistrationEndTime = data
		case "registrationForm":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("registrationForm"))
			data, err := ec.unmarshalNRegistrationFormInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFormInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.RegistrationForm = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputRegistrationFormFieldValueInput(ctx context.Context, obj any) (models.RegistrationFormFieldValueInput, error) {
	var it models.RegistrationFormFieldValueInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"name", "values"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "name":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("name"))
			data, err := ec.unmarshalNString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Name = data
		case "values":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("values"))
			data, err := ec.unmarshalOString2ᚕstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Values = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputRegistrationFormValuesInput(ctx context.Context, obj any) (models.RegistrationFormValuesInput, error) {
	var it models.RegistrationFormValuesInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"contestId", "formData"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "contestId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("contestId"))
			data, err := ec.unmarshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.ContestID = data
		case "formData":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("formData"))
			data, err := ec.unmarshalNRegistrationFormFieldValueInput2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFormFieldValueInputᚄ(ctx, v)
			if err != nil {
				return it, err
			}
			it.FormData = data
		}
	}

	return it, nil
}

// endregion **************************** input.gotpl *****************************

// region    ************************** interface.gotpl ***************************

// endregion ************************** interface.gotpl ***************************

// region    **************************** object.gotpl ****************************

var contestImplementors = []string{"Contest"}

func (ec *executionContext) _Contest(ctx context.Context, sel ast.SelectionSet, obj *models.Contest) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, contestImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("Contest")
		case "_id":
			out.Values[i] = ec._Contest__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "clubId":
			out.Values[i] = ec._Contest_clubId(ctx, field, obj)
		case "name":
			out.Values[i] = ec._Contest_name(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "description":
			out.Values[i] = ec._Contest_description(ctx, field, obj)
		case "hostedBy":
			out.Values[i] = ec._Contest_hostedBy(ctx, field, obj)
		case "hostedByV2":
			out.Values[i] = ec._Contest_hostedByV2(ctx, field, obj)
		case "details":
			out.Values[i] = ec._Contest_details(ctx, field, obj)
		case "startTime":
			out.Values[i] = ec._Contest_startTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "endTime":
			out.Values[i] = ec._Contest_endTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "registrationCount":
			out.Values[i] = ec._Contest_registrationCount(ctx, field, obj)
		case "contestDuration":
			out.Values[i] = ec._Contest_contestDuration(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "registrationStartTime":
			out.Values[i] = ec._Contest_registrationStartTime(ctx, field, obj)
		case "registrationEndTime":
			out.Values[i] = ec._Contest_registrationEndTime(ctx, field, obj)
		case "encryptedQuestions":
			out.Values[i] = ec._Contest_encryptedQuestions(ctx, field, obj)
		case "registrationForm":
			out.Values[i] = ec._Contest_registrationForm(ctx, field, obj)
		case "status":
			out.Values[i] = ec._Contest_status(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "currentUserParticipation":
			out.Values[i] = ec._Contest_currentUserParticipation(ctx, field, obj)
		case "recentParticipants":
			out.Values[i] = ec._Contest_recentParticipants(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var contestDetailsImplementors = []string{"ContestDetails"}

func (ec *executionContext) _ContestDetails(ctx context.Context, sel ast.SelectionSet, obj *models.ContestDetails) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, contestDetailsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ContestDetails")
		case "about":
			out.Values[i] = ec._ContestDetails_about(ctx, field, obj)
		case "requirements":
			out.Values[i] = ec._ContestDetails_requirements(ctx, field, obj)
		case "instructions":
			out.Values[i] = ec._ContestDetails_instructions(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var contestLeaderboardImplementors = []string{"ContestLeaderboard"}

func (ec *executionContext) _ContestLeaderboard(ctx context.Context, sel ast.SelectionSet, obj *models.ContestLeaderboard) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, contestLeaderboardImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ContestLeaderboard")
		case "participants":
			out.Values[i] = ec._ContestLeaderboard_participants(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "totalParticipants":
			out.Values[i] = ec._ContestLeaderboard_totalParticipants(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var contestParticipantImplementors = []string{"ContestParticipant"}

func (ec *executionContext) _ContestParticipant(ctx context.Context, sel ast.SelectionSet, obj *models.ContestParticipant) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, contestParticipantImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ContestParticipant")
		case "user":
			out.Values[i] = ec._ContestParticipant_user(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "registrationData":
			out.Values[i] = ec._ContestParticipant_registrationData(ctx, field, obj)
		case "score":
			out.Values[i] = ec._ContestParticipant_score(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "startTime":
			out.Values[i] = ec._ContestParticipant_startTime(ctx, field, obj)
		case "correctSubmission":
			out.Values[i] = ec._ContestParticipant_correctSubmission(ctx, field, obj)
		case "incorrectSubmission":
			out.Values[i] = ec._ContestParticipant_incorrectSubmission(ctx, field, obj)
		case "rank":
			out.Values[i] = ec._ContestParticipant_rank(ctx, field, obj)
		case "lastSubmissionTime":
			out.Values[i] = ec._ContestParticipant_lastSubmissionTime(ctx, field, obj)
		case "isVirtualParticipant":
			out.Values[i] = ec._ContestParticipant_isVirtualParticipant(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var contestQuestionImplementors = []string{"ContestQuestion"}

func (ec *executionContext) _ContestQuestion(ctx context.Context, sel ast.SelectionSet, obj *models.ContestQuestion) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, contestQuestionImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ContestQuestion")
		case "_id":
			out.Values[i] = ec._ContestQuestion__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "question":
			out.Values[i] = ec._ContestQuestion_question(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "points":
			out.Values[i] = ec._ContestQuestion_points(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var contestSubmissionImplementors = []string{"ContestSubmission"}

func (ec *executionContext) _ContestSubmission(ctx context.Context, sel ast.SelectionSet, obj *models.ContestSubmission) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, contestSubmissionImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ContestSubmission")
		case "questionId":
			out.Values[i] = ec._ContestSubmission_questionId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "answer":
			out.Values[i] = ec._ContestSubmission_answer(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "isCorrect":
			out.Values[i] = ec._ContestSubmission_isCorrect(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "submissionTime":
			out.Values[i] = ec._ContestSubmission_submissionTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "points":
			out.Values[i] = ec._ContestSubmission_points(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var currentUserParticipationImplementors = []string{"CurrentUserParticipation"}

func (ec *executionContext) _CurrentUserParticipation(ctx context.Context, sel ast.SelectionSet, obj *models.CurrentUserParticipation) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, currentUserParticipationImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("CurrentUserParticipation")
		case "contestId":
			out.Values[i] = ec._CurrentUserParticipation_contestId(ctx, field, obj)
		case "userId":
			out.Values[i] = ec._CurrentUserParticipation_userId(ctx, field, obj)
		case "registrationData":
			out.Values[i] = ec._CurrentUserParticipation_registrationData(ctx, field, obj)
		case "score":
			out.Values[i] = ec._CurrentUserParticipation_score(ctx, field, obj)
		case "lastSubmissionTime":
			out.Values[i] = ec._CurrentUserParticipation_lastSubmissionTime(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var hostDetailsImplementors = []string{"HostDetails"}

func (ec *executionContext) _HostDetails(ctx context.Context, sel ast.SelectionSet, obj *models.HostDetails) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, hostDetailsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("HostDetails")
		case "name":
			out.Values[i] = ec._HostDetails_name(ctx, field, obj)
		case "logo":
			out.Values[i] = ec._HostDetails_logo(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var paginatedContestsImplementors = []string{"PaginatedContests"}

func (ec *executionContext) _PaginatedContests(ctx context.Context, sel ast.SelectionSet, obj *models.PaginatedContests) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, paginatedContestsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PaginatedContests")
		case "contests":
			out.Values[i] = ec._PaginatedContests_contests(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "totalCount":
			out.Values[i] = ec._PaginatedContests_totalCount(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var registrationFieldDataImplementors = []string{"RegistrationFieldData"}

func (ec *executionContext) _RegistrationFieldData(ctx context.Context, sel ast.SelectionSet, obj *models.RegistrationFieldData) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, registrationFieldDataImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("RegistrationFieldData")
		case "name":
			out.Values[i] = ec._RegistrationFieldData_name(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "values":
			out.Values[i] = ec._RegistrationFieldData_values(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userContestResultImplementors = []string{"UserContestResult"}

func (ec *executionContext) _UserContestResult(ctx context.Context, sel ast.SelectionSet, obj *models.UserContestResult) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userContestResultImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserContestResult")
		case "totalScore":
			out.Values[i] = ec._UserContestResult_totalScore(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "startTime":
			out.Values[i] = ec._UserContestResult_startTime(ctx, field, obj)
		case "lastSubmissionTime":
			out.Values[i] = ec._UserContestResult_lastSubmissionTime(ctx, field, obj)
		case "correctSubmission":
			out.Values[i] = ec._UserContestResult_correctSubmission(ctx, field, obj)
		case "incorrectSubmission":
			out.Values[i] = ec._UserContestResult_incorrectSubmission(ctx, field, obj)
		case "user":
			out.Values[i] = ec._UserContestResult_user(ctx, field, obj)
		case "rank":
			out.Values[i] = ec._UserContestResult_rank(ctx, field, obj)
		case "questionsSolved":
			out.Values[i] = ec._UserContestResult_questionsSolved(ctx, field, obj)
		case "totalParticipants":
			out.Values[i] = ec._UserContestResult_totalParticipants(ctx, field, obj)
		case "isVirtualParticipant":
			out.Values[i] = ec._UserContestResult_isVirtualParticipant(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userContestSubmissionsImplementors = []string{"UserContestSubmissions"}

func (ec *executionContext) _UserContestSubmissions(ctx context.Context, sel ast.SelectionSet, obj *models.UserContestSubmissions) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userContestSubmissionsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserContestSubmissions")
		case "totalScore":
			out.Values[i] = ec._UserContestSubmissions_totalScore(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "startTime":
			out.Values[i] = ec._UserContestSubmissions_startTime(ctx, field, obj)
		case "lastSubmissionTime":
			out.Values[i] = ec._UserContestSubmissions_lastSubmissionTime(ctx, field, obj)
		case "correctSubmission":
			out.Values[i] = ec._UserContestSubmissions_correctSubmission(ctx, field, obj)
		case "incorrectSubmission":
			out.Values[i] = ec._UserContestSubmissions_incorrectSubmission(ctx, field, obj)
		case "submissions":
			out.Values[i] = ec._UserContestSubmissions_submissions(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "user":
			out.Values[i] = ec._UserContestSubmissions_user(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

// endregion **************************** object.gotpl ****************************

// region    ***************************** type.gotpl *****************************

func (ec *executionContext) marshalNContest2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContest(ctx context.Context, sel ast.SelectionSet, v models.Contest) graphql.Marshaler {
	return ec._Contest(ctx, sel, &v)
}

func (ec *executionContext) marshalNContest2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.Contest) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNContest2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContest(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNContest2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContest(ctx context.Context, sel ast.SelectionSet, v *models.Contest) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._Contest(ctx, sel, v)
}

func (ec *executionContext) marshalNContestLeaderboard2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestLeaderboard(ctx context.Context, sel ast.SelectionSet, v models.ContestLeaderboard) graphql.Marshaler {
	return ec._ContestLeaderboard(ctx, sel, &v)
}

func (ec *executionContext) marshalNContestLeaderboard2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestLeaderboard(ctx context.Context, sel ast.SelectionSet, v *models.ContestLeaderboard) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._ContestLeaderboard(ctx, sel, v)
}

func (ec *executionContext) marshalNContestParticipant2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestParticipantᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.ContestParticipant) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNContestParticipant2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestParticipant(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNContestParticipant2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestParticipant(ctx context.Context, sel ast.SelectionSet, v *models.ContestParticipant) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._ContestParticipant(ctx, sel, v)
}

func (ec *executionContext) unmarshalNContestStatus2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestStatus(ctx context.Context, v any) (models.ContestStatus, error) {
	var res models.ContestStatus
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNContestStatus2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestStatus(ctx context.Context, sel ast.SelectionSet, v models.ContestStatus) graphql.Marshaler {
	return v
}

func (ec *executionContext) unmarshalNContestStatus2ᚕmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestStatusᚄ(ctx context.Context, v any) ([]models.ContestStatus, error) {
	var vSlice []any
	vSlice = graphql.CoerceList(v)
	var err error
	res := make([]models.ContestStatus, len(vSlice))
	for i := range vSlice {
		ctx := graphql.WithPathContext(ctx, graphql.NewPathWithIndex(i))
		res[i], err = ec.unmarshalNContestStatus2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestStatus(ctx, vSlice[i])
		if err != nil {
			return nil, err
		}
	}
	return res, nil
}

func (ec *executionContext) marshalNContestStatus2ᚕmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestStatusᚄ(ctx context.Context, sel ast.SelectionSet, v []models.ContestStatus) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNContestStatus2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestStatus(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNContestSubmission2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestSubmissionᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.ContestSubmission) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNContestSubmission2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestSubmission(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNContestSubmission2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestSubmission(ctx context.Context, sel ast.SelectionSet, v *models.ContestSubmission) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._ContestSubmission(ctx, sel, v)
}

func (ec *executionContext) unmarshalNCreateContestInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCreateContestInput(ctx context.Context, v any) (models.CreateContestInput, error) {
	res, err := ec.unmarshalInputCreateContestInput(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNPaginatedContests2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPaginatedContests(ctx context.Context, sel ast.SelectionSet, v models.PaginatedContests) graphql.Marshaler {
	return ec._PaginatedContests(ctx, sel, &v)
}

func (ec *executionContext) marshalNPaginatedContests2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPaginatedContests(ctx context.Context, sel ast.SelectionSet, v *models.PaginatedContests) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._PaginatedContests(ctx, sel, v)
}

func (ec *executionContext) unmarshalNRegistrationFormFieldValueInput2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFormFieldValueInputᚄ(ctx context.Context, v any) ([]*models.RegistrationFormFieldValueInput, error) {
	var vSlice []any
	vSlice = graphql.CoerceList(v)
	var err error
	res := make([]*models.RegistrationFormFieldValueInput, len(vSlice))
	for i := range vSlice {
		ctx := graphql.WithPathContext(ctx, graphql.NewPathWithIndex(i))
		res[i], err = ec.unmarshalNRegistrationFormFieldValueInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFormFieldValueInput(ctx, vSlice[i])
		if err != nil {
			return nil, err
		}
	}
	return res, nil
}

func (ec *executionContext) unmarshalNRegistrationFormFieldValueInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFormFieldValueInput(ctx context.Context, v any) (*models.RegistrationFormFieldValueInput, error) {
	res, err := ec.unmarshalInputRegistrationFormFieldValueInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) unmarshalNRegistrationFormValuesInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFormValuesInput(ctx context.Context, v any) (models.RegistrationFormValuesInput, error) {
	res, err := ec.unmarshalInputRegistrationFormValuesInput(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNUserContestResult2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserContestResult(ctx context.Context, sel ast.SelectionSet, v models.UserContestResult) graphql.Marshaler {
	return ec._UserContestResult(ctx, sel, &v)
}

func (ec *executionContext) marshalNUserContestResult2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserContestResult(ctx context.Context, sel ast.SelectionSet, v *models.UserContestResult) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._UserContestResult(ctx, sel, v)
}

func (ec *executionContext) marshalNUserContestSubmissions2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserContestSubmissions(ctx context.Context, sel ast.SelectionSet, v models.UserContestSubmissions) graphql.Marshaler {
	return ec._UserContestSubmissions(ctx, sel, &v)
}

func (ec *executionContext) marshalNUserContestSubmissions2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserContestSubmissions(ctx context.Context, sel ast.SelectionSet, v *models.UserContestSubmissions) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._UserContestSubmissions(ctx, sel, v)
}

func (ec *executionContext) marshalOContestDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestDetails(ctx context.Context, sel ast.SelectionSet, v *models.ContestDetails) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._ContestDetails(ctx, sel, v)
}

func (ec *executionContext) unmarshalOContestDetailsInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐContestDetailsInput(ctx context.Context, v any) (models.ContestDetailsInput, error) {
	res, err := ec.unmarshalInputContestDetailsInput(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOCurrentUserParticipation2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCurrentUserParticipation(ctx context.Context, sel ast.SelectionSet, v *models.CurrentUserParticipation) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._CurrentUserParticipation(ctx, sel, v)
}

func (ec *executionContext) marshalOHostDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐHostDetails(ctx context.Context, sel ast.SelectionSet, v *models.HostDetails) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._HostDetails(ctx, sel, v)
}

func (ec *executionContext) marshalORegistrationFieldData2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFieldData(ctx context.Context, sel ast.SelectionSet, v []*models.RegistrationFieldData) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalORegistrationFieldData2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFieldData(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalORegistrationFieldData2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFieldData(ctx context.Context, sel ast.SelectionSet, v *models.RegistrationFieldData) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._RegistrationFieldData(ctx, sel, v)
}

// endregion ***************************** type.gotpl *****************************
