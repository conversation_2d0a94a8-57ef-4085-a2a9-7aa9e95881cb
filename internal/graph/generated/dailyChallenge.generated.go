// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"context"
	"errors"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/ast"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// region    ************************** generated!.gotpl **************************

// endregion ************************** generated!.gotpl **************************

// region    ***************************** args.gotpl *****************************

// endregion ***************************** args.gotpl *****************************

// region    ************************** directives.gotpl **************************

// endregion ************************** directives.gotpl **************************

// region    **************************** field.gotpl *****************************

func (ec *executionContext) _BotDetectionResult_isBotBehavior(ctx context.Context, field graphql.CollectedField, obj *models.BotDetectionResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_BotDetectionResult_isBotBehavior(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsBotBehavior, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_BotDetectionResult_isBotBehavior(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "BotDetectionResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _BotDetectionResult_userId(ctx context.Context, field graphql.CollectedField, obj *models.BotDetectionResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_BotDetectionResult_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_BotDetectionResult_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "BotDetectionResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _BotDetectionResult_challengeId(ctx context.Context, field graphql.CollectedField, obj *models.BotDetectionResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_BotDetectionResult_challengeId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ChallengeID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_BotDetectionResult_challengeId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "BotDetectionResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ChallengeResult_user(ctx context.Context, field graphql.CollectedField, obj *models.ChallengeResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ChallengeResult_user(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.User, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.UserPublicDetails)
	fc.Result = res
	return ec.marshalNUserPublicDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ChallengeResult_user(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ChallengeResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPublicDetails__id(ctx, field)
			case "name":
				return ec.fieldContext_UserPublicDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_UserPublicDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserPublicDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserPublicDetails_rating(ctx, field)
			case "badge":
				return ec.fieldContext_UserPublicDetails_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_UserPublicDetails_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_UserPublicDetails_isGuest(ctx, field)
			case "globalRank":
				return ec.fieldContext_UserPublicDetails_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_UserPublicDetails_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_UserPublicDetails_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_UserPublicDetails_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_UserPublicDetails_stats(ctx, field)
			case "userStreaks":
				return ec.fieldContext_UserPublicDetails_userStreaks(ctx, field)
			case "ratingV2":
				return ec.fieldContext_UserPublicDetails_ratingV2(ctx, field)
			case "institutionId":
				return ec.fieldContext_UserPublicDetails_institutionId(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPublicDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ChallengeResult_rank(ctx context.Context, field graphql.CollectedField, obj *models.ChallengeResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ChallengeResult_rank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ChallengeResult_rank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ChallengeResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ChallengeResult_score(ctx context.Context, field graphql.CollectedField, obj *models.ChallengeResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ChallengeResult_score(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Score, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ChallengeResult_score(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ChallengeResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallenge__id(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallenge) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallenge__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallenge__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallenge",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallenge_challengeStatus(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallenge) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallenge_challengeStatus(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ChallengeStatus, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallenge_challengeStatus(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallenge",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallenge_questions(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallenge) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallenge_questions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Questions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.GameQuestion)
	fc.Result = res
	return ec.marshalNGameQuestion2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameQuestionᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallenge_questions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallenge",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "question":
				return ec.fieldContext_GameQuestion_question(ctx, field)
			case "submissions":
				return ec.fieldContext_GameQuestion_submissions(ctx, field)
			case "stats":
				return ec.fieldContext_GameQuestion_stats(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type GameQuestion", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallenge_startTime(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallenge) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallenge_startTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StartTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallenge_startTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallenge",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallenge_endTime(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallenge) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallenge_endTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EndTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallenge_endTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallenge",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallenge_hasAttempted(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallenge) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallenge_hasAttempted(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasAttempted, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallenge_hasAttempted(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallenge",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallenge_encryptedQuestions(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallenge) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallenge_encryptedQuestions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EncryptedQuestions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*string)
	fc.Result = res
	return ec.marshalOString2ᚕᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallenge_encryptedQuestions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallenge",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallenge_challengeNumber(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallenge) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallenge_challengeNumber(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ChallengeNumber, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallenge_challengeNumber(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallenge",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallenge_division(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallenge) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallenge_division(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Division, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(models.ChallengeDivision)
	fc.Result = res
	return ec.marshalOCHALLENGE_DIVISION2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐChallengeDivision(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallenge_division(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallenge",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type CHALLENGE_DIVISION does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallenge_stats(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallenge) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallenge_stats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Stats, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.DailyChallengeStat)
	fc.Result = res
	return ec.marshalODailyChallengeStat2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐDailyChallengeStat(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallenge_stats(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallenge",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "totalAttempts":
				return ec.fieldContext_DailyChallengeStat_totalAttempts(ctx, field)
			case "totalSubmission":
				return ec.fieldContext_DailyChallengeStat_totalSubmission(ctx, field)
			case "averageTime":
				return ec.fieldContext_DailyChallengeStat_averageTime(ctx, field)
			case "bestTime":
				return ec.fieldContext_DailyChallengeStat_bestTime(ctx, field)
			case "averageAccuracy":
				return ec.fieldContext_DailyChallengeStat_averageAccuracy(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type DailyChallengeStat", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallengeResult_userId(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallengeResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallengeResult_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallengeResult_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallengeResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallengeResult_challengeId(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallengeResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallengeResult_challengeId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ChallengeID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallengeResult_challengeId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallengeResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallengeResult_score(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallengeResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallengeResult_score(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Score, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallengeResult_score(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallengeResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallengeResult_completedAt(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallengeResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallengeResult_completedAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CompletedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallengeResult_completedAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallengeResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallengeResult_rank(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallengeResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallengeResult_rank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallengeResult_rank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallengeResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallengeResult_statikCoinsEarned(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallengeResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallengeResult_statikCoinsEarned(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StatikCoinsEarned, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallengeResult_statikCoinsEarned(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallengeResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallengeResult_resultStatus(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallengeResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallengeResult_resultStatus(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ResultStatus, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(models.ResultStatus)
	fc.Result = res
	return ec.marshalORESULT_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐResultStatus(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallengeResult_resultStatus(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallengeResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type RESULT_STATUS does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallengeResultWithStats_result(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallengeResultWithStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallengeResultWithStats_result(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Result, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.DailyChallengeResult)
	fc.Result = res
	return ec.marshalODailyChallengeResult2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐDailyChallengeResult(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallengeResultWithStats_result(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallengeResultWithStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "userId":
				return ec.fieldContext_DailyChallengeResult_userId(ctx, field)
			case "challengeId":
				return ec.fieldContext_DailyChallengeResult_challengeId(ctx, field)
			case "score":
				return ec.fieldContext_DailyChallengeResult_score(ctx, field)
			case "completedAt":
				return ec.fieldContext_DailyChallengeResult_completedAt(ctx, field)
			case "rank":
				return ec.fieldContext_DailyChallengeResult_rank(ctx, field)
			case "statikCoinsEarned":
				return ec.fieldContext_DailyChallengeResult_statikCoinsEarned(ctx, field)
			case "resultStatus":
				return ec.fieldContext_DailyChallengeResult_resultStatus(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type DailyChallengeResult", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallengeResultWithStats_stats(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallengeResultWithStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallengeResultWithStats_stats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Stats, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.UserDailyChallengeStats)
	fc.Result = res
	return ec.marshalOUserDailyChallengeStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserDailyChallengeStats(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallengeResultWithStats_stats(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallengeResultWithStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserDailyChallengeStats__id(ctx, field)
			case "userId":
				return ec.fieldContext_UserDailyChallengeStats_userId(ctx, field)
			case "division":
				return ec.fieldContext_UserDailyChallengeStats_division(ctx, field)
			case "totalAttempts":
				return ec.fieldContext_UserDailyChallengeStats_totalAttempts(ctx, field)
			case "totalSubmission":
				return ec.fieldContext_UserDailyChallengeStats_totalSubmission(ctx, field)
			case "averageTime":
				return ec.fieldContext_UserDailyChallengeStats_averageTime(ctx, field)
			case "bestTime":
				return ec.fieldContext_UserDailyChallengeStats_bestTime(ctx, field)
			case "streaks":
				return ec.fieldContext_UserDailyChallengeStats_streaks(ctx, field)
			case "averageAccuracy":
				return ec.fieldContext_UserDailyChallengeStats_averageAccuracy(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserDailyChallengeStats", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallengeStat_totalAttempts(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallengeStat) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallengeStat_totalAttempts(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalAttempts, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallengeStat_totalAttempts(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallengeStat",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallengeStat_totalSubmission(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallengeStat) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallengeStat_totalSubmission(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalSubmission, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallengeStat_totalSubmission(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallengeStat",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallengeStat_averageTime(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallengeStat) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallengeStat_averageTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.AverageTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallengeStat_averageTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallengeStat",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallengeStat_bestTime(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallengeStat) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallengeStat_bestTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.BestTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallengeStat_bestTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallengeStat",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _DailyChallengeStat_averageAccuracy(ctx context.Context, field graphql.CollectedField, obj *models.DailyChallengeStat) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_DailyChallengeStat_averageAccuracy(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.AverageAccuracy, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*float64)
	fc.Result = res
	return ec.marshalOFloat2ᚖfloat64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_DailyChallengeStat_averageAccuracy(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "DailyChallengeStat",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderboardPage_results(ctx context.Context, field graphql.CollectedField, obj *models.LeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderboardPage_results(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Results, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]models.ChallengeResult)
	fc.Result = res
	return ec.marshalNChallengeResult2ᚕmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐChallengeResultᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderboardPage_results(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "user":
				return ec.fieldContext_ChallengeResult_user(ctx, field)
			case "rank":
				return ec.fieldContext_ChallengeResult_rank(ctx, field)
			case "score":
				return ec.fieldContext_ChallengeResult_score(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ChallengeResult", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderboardPage_pageNumber(ctx context.Context, field graphql.CollectedField, obj *models.LeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderboardPage_pageNumber(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PageNumber, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderboardPage_pageNumber(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderboardPage_pageSize(ctx context.Context, field graphql.CollectedField, obj *models.LeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderboardPage_pageSize(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PageSize, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderboardPage_pageSize(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderboardPage_hasMore(ctx context.Context, field graphql.CollectedField, obj *models.LeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderboardPage_hasMore(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasMore, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalOBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderboardPage_hasMore(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderboardPage_totalResults(ctx context.Context, field graphql.CollectedField, obj *models.LeaderboardPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderboardPage_totalResults(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalResults, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderboardPage_totalResults(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderboardPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Result_userId(ctx context.Context, field graphql.CollectedField, obj *models.Result) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Result_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Result_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Result",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Result_challengeId(ctx context.Context, field graphql.CollectedField, obj *models.Result) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Result_challengeId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ChallengeID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Result_challengeId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Result",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Result_submittedTimes(ctx context.Context, field graphql.CollectedField, obj *models.Result) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Result_submittedTimes(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.SubmittedTimes, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*int)
	fc.Result = res
	return ec.marshalOInt2ᚕᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Result_submittedTimes(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Result",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Result_score(ctx context.Context, field graphql.CollectedField, obj *models.Result) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Result_score(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Score, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Result_score(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Result",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Result_completedAt(ctx context.Context, field graphql.CollectedField, obj *models.Result) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Result_completedAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CompletedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Result_completedAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Result",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _SubmitChallengeResult_success(ctx context.Context, field graphql.CollectedField, obj *models.SubmitChallengeResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_SubmitChallengeResult_success(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Success, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalOBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_SubmitChallengeResult_success(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "SubmitChallengeResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _SubmitChallengeResult_message(ctx context.Context, field graphql.CollectedField, obj *models.SubmitChallengeResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_SubmitChallengeResult_message(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Message, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalOString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_SubmitChallengeResult_message(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "SubmitChallengeResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _SubmitChallengeResult_result(ctx context.Context, field graphql.CollectedField, obj *models.SubmitChallengeResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_SubmitChallengeResult_result(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Result, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.Result)
	fc.Result = res
	return ec.marshalOResult2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐResult(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_SubmitChallengeResult_result(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "SubmitChallengeResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "userId":
				return ec.fieldContext_Result_userId(ctx, field)
			case "challengeId":
				return ec.fieldContext_Result_challengeId(ctx, field)
			case "submittedTimes":
				return ec.fieldContext_Result_submittedTimes(ctx, field)
			case "score":
				return ec.fieldContext_Result_score(ctx, field)
			case "completedAt":
				return ec.fieldContext_Result_completedAt(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Result", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDailyChallengeResultWithStats_success(ctx context.Context, field graphql.CollectedField, obj *models.UserDailyChallengeResultWithStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDailyChallengeResultWithStats_success(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Success, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDailyChallengeResultWithStats_success(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDailyChallengeResultWithStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDailyChallengeResultWithStats_error(ctx context.Context, field graphql.CollectedField, obj *models.UserDailyChallengeResultWithStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDailyChallengeResultWithStats_error(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Error, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDailyChallengeResultWithStats_error(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDailyChallengeResultWithStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDailyChallengeResultWithStats_result(ctx context.Context, field graphql.CollectedField, obj *models.UserDailyChallengeResultWithStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDailyChallengeResultWithStats_result(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Result, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.DailyChallengeResultWithStats)
	fc.Result = res
	return ec.marshalODailyChallengeResultWithStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐDailyChallengeResultWithStats(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDailyChallengeResultWithStats_result(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDailyChallengeResultWithStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "result":
				return ec.fieldContext_DailyChallengeResultWithStats_result(ctx, field)
			case "stats":
				return ec.fieldContext_DailyChallengeResultWithStats_stats(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type DailyChallengeResultWithStats", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDailyChallengeStats__id(ctx context.Context, field graphql.CollectedField, obj *models.UserDailyChallengeStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDailyChallengeStats__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDailyChallengeStats__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDailyChallengeStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDailyChallengeStats_userId(ctx context.Context, field graphql.CollectedField, obj *models.UserDailyChallengeStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDailyChallengeStats_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDailyChallengeStats_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDailyChallengeStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDailyChallengeStats_division(ctx context.Context, field graphql.CollectedField, obj *models.UserDailyChallengeStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDailyChallengeStats_division(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Division, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.ChallengeDivision)
	fc.Result = res
	return ec.marshalNCHALLENGE_DIVISION2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐChallengeDivision(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDailyChallengeStats_division(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDailyChallengeStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type CHALLENGE_DIVISION does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDailyChallengeStats_totalAttempts(ctx context.Context, field graphql.CollectedField, obj *models.UserDailyChallengeStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDailyChallengeStats_totalAttempts(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalAttempts, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDailyChallengeStats_totalAttempts(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDailyChallengeStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDailyChallengeStats_totalSubmission(ctx context.Context, field graphql.CollectedField, obj *models.UserDailyChallengeStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDailyChallengeStats_totalSubmission(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalSubmission, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDailyChallengeStats_totalSubmission(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDailyChallengeStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDailyChallengeStats_averageTime(ctx context.Context, field graphql.CollectedField, obj *models.UserDailyChallengeStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDailyChallengeStats_averageTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.AverageTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDailyChallengeStats_averageTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDailyChallengeStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDailyChallengeStats_bestTime(ctx context.Context, field graphql.CollectedField, obj *models.UserDailyChallengeStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDailyChallengeStats_bestTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.BestTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDailyChallengeStats_bestTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDailyChallengeStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDailyChallengeStats_streaks(ctx context.Context, field graphql.CollectedField, obj *models.UserDailyChallengeStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDailyChallengeStats_streaks(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Streaks, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.UserDailyChallengeStreaks)
	fc.Result = res
	return ec.marshalNUserDailyChallengeStreaks2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserDailyChallengeStreaks(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDailyChallengeStats_streaks(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDailyChallengeStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "current":
				return ec.fieldContext_UserDailyChallengeStreaks_current(ctx, field)
			case "highest":
				return ec.fieldContext_UserDailyChallengeStreaks_highest(ctx, field)
			case "lastPlayedDate":
				return ec.fieldContext_UserDailyChallengeStreaks_lastPlayedDate(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserDailyChallengeStreaks", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDailyChallengeStats_averageAccuracy(ctx context.Context, field graphql.CollectedField, obj *models.UserDailyChallengeStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDailyChallengeStats_averageAccuracy(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.AverageAccuracy, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*float64)
	fc.Result = res
	return ec.marshalOFloat2ᚖfloat64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDailyChallengeStats_averageAccuracy(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDailyChallengeStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDailyChallengeStreaks_current(ctx context.Context, field graphql.CollectedField, obj *models.UserDailyChallengeStreaks) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDailyChallengeStreaks_current(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Current, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDailyChallengeStreaks_current(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDailyChallengeStreaks",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDailyChallengeStreaks_highest(ctx context.Context, field graphql.CollectedField, obj *models.UserDailyChallengeStreaks) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDailyChallengeStreaks_highest(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Highest, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDailyChallengeStreaks_highest(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDailyChallengeStreaks",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDailyChallengeStreaks_lastPlayedDate(ctx context.Context, field graphql.CollectedField, obj *models.UserDailyChallengeStreaks) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDailyChallengeStreaks_lastPlayedDate(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LastPlayedDate, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDailyChallengeStreaks_lastPlayedDate(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDailyChallengeStreaks",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserResult_success(ctx context.Context, field graphql.CollectedField, obj *models.UserResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserResult_success(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Success, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalOBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserResult_success(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserResult_error(ctx context.Context, field graphql.CollectedField, obj *models.UserResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserResult_error(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Error, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalOString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserResult_error(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserResult_result(ctx context.Context, field graphql.CollectedField, obj *models.UserResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserResult_result(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Result, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.DailyChallengeResult)
	fc.Result = res
	return ec.marshalODailyChallengeResult2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐDailyChallengeResult(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserResult_result(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "userId":
				return ec.fieldContext_DailyChallengeResult_userId(ctx, field)
			case "challengeId":
				return ec.fieldContext_DailyChallengeResult_challengeId(ctx, field)
			case "score":
				return ec.fieldContext_DailyChallengeResult_score(ctx, field)
			case "completedAt":
				return ec.fieldContext_DailyChallengeResult_completedAt(ctx, field)
			case "rank":
				return ec.fieldContext_DailyChallengeResult_rank(ctx, field)
			case "statikCoinsEarned":
				return ec.fieldContext_DailyChallengeResult_statikCoinsEarned(ctx, field)
			case "resultStatus":
				return ec.fieldContext_DailyChallengeResult_resultStatus(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type DailyChallengeResult", field.Name)
		},
	}
	return fc, nil
}

// endregion **************************** field.gotpl *****************************

// region    **************************** input.gotpl *****************************

func (ec *executionContext) unmarshalInputSubmitSolutionInput(ctx context.Context, obj any) (models.SubmitSolutionInput, error) {
	var it models.SubmitSolutionInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"challengeId", "submittedTimes", "challengeNumber"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "challengeId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("challengeId"))
			data, err := ec.unmarshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.ChallengeID = data
		case "submittedTimes":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("submittedTimes"))
			data, err := ec.unmarshalOInt2ᚕᚖint(ctx, v)
			if err != nil {
				return it, err
			}
			it.SubmittedTimes = data
		case "challengeNumber":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("challengeNumber"))
			data, err := ec.unmarshalOInt2ᚖint(ctx, v)
			if err != nil {
				return it, err
			}
			it.ChallengeNumber = data
		}
	}

	return it, nil
}

// endregion **************************** input.gotpl *****************************

// region    ************************** interface.gotpl ***************************

// endregion ************************** interface.gotpl ***************************

// region    **************************** object.gotpl ****************************

var botDetectionResultImplementors = []string{"BotDetectionResult"}

func (ec *executionContext) _BotDetectionResult(ctx context.Context, sel ast.SelectionSet, obj *models.BotDetectionResult) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, botDetectionResultImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("BotDetectionResult")
		case "isBotBehavior":
			out.Values[i] = ec._BotDetectionResult_isBotBehavior(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userId":
			out.Values[i] = ec._BotDetectionResult_userId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "challengeId":
			out.Values[i] = ec._BotDetectionResult_challengeId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var challengeResultImplementors = []string{"ChallengeResult"}

func (ec *executionContext) _ChallengeResult(ctx context.Context, sel ast.SelectionSet, obj *models.ChallengeResult) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, challengeResultImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ChallengeResult")
		case "user":
			out.Values[i] = ec._ChallengeResult_user(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "rank":
			out.Values[i] = ec._ChallengeResult_rank(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "score":
			out.Values[i] = ec._ChallengeResult_score(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var dailyChallengeImplementors = []string{"DailyChallenge"}

func (ec *executionContext) _DailyChallenge(ctx context.Context, sel ast.SelectionSet, obj *models.DailyChallenge) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, dailyChallengeImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("DailyChallenge")
		case "_id":
			out.Values[i] = ec._DailyChallenge__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "challengeStatus":
			out.Values[i] = ec._DailyChallenge_challengeStatus(ctx, field, obj)
		case "questions":
			out.Values[i] = ec._DailyChallenge_questions(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "startTime":
			out.Values[i] = ec._DailyChallenge_startTime(ctx, field, obj)
		case "endTime":
			out.Values[i] = ec._DailyChallenge_endTime(ctx, field, obj)
		case "hasAttempted":
			out.Values[i] = ec._DailyChallenge_hasAttempted(ctx, field, obj)
		case "encryptedQuestions":
			out.Values[i] = ec._DailyChallenge_encryptedQuestions(ctx, field, obj)
		case "challengeNumber":
			out.Values[i] = ec._DailyChallenge_challengeNumber(ctx, field, obj)
		case "division":
			out.Values[i] = ec._DailyChallenge_division(ctx, field, obj)
		case "stats":
			out.Values[i] = ec._DailyChallenge_stats(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var dailyChallengeResultImplementors = []string{"DailyChallengeResult"}

func (ec *executionContext) _DailyChallengeResult(ctx context.Context, sel ast.SelectionSet, obj *models.DailyChallengeResult) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, dailyChallengeResultImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("DailyChallengeResult")
		case "userId":
			out.Values[i] = ec._DailyChallengeResult_userId(ctx, field, obj)
		case "challengeId":
			out.Values[i] = ec._DailyChallengeResult_challengeId(ctx, field, obj)
		case "score":
			out.Values[i] = ec._DailyChallengeResult_score(ctx, field, obj)
		case "completedAt":
			out.Values[i] = ec._DailyChallengeResult_completedAt(ctx, field, obj)
		case "rank":
			out.Values[i] = ec._DailyChallengeResult_rank(ctx, field, obj)
		case "statikCoinsEarned":
			out.Values[i] = ec._DailyChallengeResult_statikCoinsEarned(ctx, field, obj)
		case "resultStatus":
			out.Values[i] = ec._DailyChallengeResult_resultStatus(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var dailyChallengeResultWithStatsImplementors = []string{"DailyChallengeResultWithStats"}

func (ec *executionContext) _DailyChallengeResultWithStats(ctx context.Context, sel ast.SelectionSet, obj *models.DailyChallengeResultWithStats) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, dailyChallengeResultWithStatsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("DailyChallengeResultWithStats")
		case "result":
			out.Values[i] = ec._DailyChallengeResultWithStats_result(ctx, field, obj)
		case "stats":
			out.Values[i] = ec._DailyChallengeResultWithStats_stats(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var dailyChallengeStatImplementors = []string{"DailyChallengeStat"}

func (ec *executionContext) _DailyChallengeStat(ctx context.Context, sel ast.SelectionSet, obj *models.DailyChallengeStat) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, dailyChallengeStatImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("DailyChallengeStat")
		case "totalAttempts":
			out.Values[i] = ec._DailyChallengeStat_totalAttempts(ctx, field, obj)
		case "totalSubmission":
			out.Values[i] = ec._DailyChallengeStat_totalSubmission(ctx, field, obj)
		case "averageTime":
			out.Values[i] = ec._DailyChallengeStat_averageTime(ctx, field, obj)
		case "bestTime":
			out.Values[i] = ec._DailyChallengeStat_bestTime(ctx, field, obj)
		case "averageAccuracy":
			out.Values[i] = ec._DailyChallengeStat_averageAccuracy(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var leaderboardPageImplementors = []string{"LeaderboardPage"}

func (ec *executionContext) _LeaderboardPage(ctx context.Context, sel ast.SelectionSet, obj *models.LeaderboardPage) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, leaderboardPageImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("LeaderboardPage")
		case "results":
			out.Values[i] = ec._LeaderboardPage_results(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "pageNumber":
			out.Values[i] = ec._LeaderboardPage_pageNumber(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "pageSize":
			out.Values[i] = ec._LeaderboardPage_pageSize(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "hasMore":
			out.Values[i] = ec._LeaderboardPage_hasMore(ctx, field, obj)
		case "totalResults":
			out.Values[i] = ec._LeaderboardPage_totalResults(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var resultImplementors = []string{"Result"}

func (ec *executionContext) _Result(ctx context.Context, sel ast.SelectionSet, obj *models.Result) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, resultImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("Result")
		case "userId":
			out.Values[i] = ec._Result_userId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "challengeId":
			out.Values[i] = ec._Result_challengeId(ctx, field, obj)
		case "submittedTimes":
			out.Values[i] = ec._Result_submittedTimes(ctx, field, obj)
		case "score":
			out.Values[i] = ec._Result_score(ctx, field, obj)
		case "completedAt":
			out.Values[i] = ec._Result_completedAt(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var submitChallengeResultImplementors = []string{"SubmitChallengeResult"}

func (ec *executionContext) _SubmitChallengeResult(ctx context.Context, sel ast.SelectionSet, obj *models.SubmitChallengeResult) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, submitChallengeResultImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("SubmitChallengeResult")
		case "success":
			out.Values[i] = ec._SubmitChallengeResult_success(ctx, field, obj)
		case "message":
			out.Values[i] = ec._SubmitChallengeResult_message(ctx, field, obj)
		case "result":
			out.Values[i] = ec._SubmitChallengeResult_result(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userDailyChallengeResultWithStatsImplementors = []string{"UserDailyChallengeResultWithStats"}

func (ec *executionContext) _UserDailyChallengeResultWithStats(ctx context.Context, sel ast.SelectionSet, obj *models.UserDailyChallengeResultWithStats) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userDailyChallengeResultWithStatsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserDailyChallengeResultWithStats")
		case "success":
			out.Values[i] = ec._UserDailyChallengeResultWithStats_success(ctx, field, obj)
		case "error":
			out.Values[i] = ec._UserDailyChallengeResultWithStats_error(ctx, field, obj)
		case "result":
			out.Values[i] = ec._UserDailyChallengeResultWithStats_result(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userDailyChallengeStatsImplementors = []string{"UserDailyChallengeStats"}

func (ec *executionContext) _UserDailyChallengeStats(ctx context.Context, sel ast.SelectionSet, obj *models.UserDailyChallengeStats) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userDailyChallengeStatsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserDailyChallengeStats")
		case "_id":
			out.Values[i] = ec._UserDailyChallengeStats__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userId":
			out.Values[i] = ec._UserDailyChallengeStats_userId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "division":
			out.Values[i] = ec._UserDailyChallengeStats_division(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "totalAttempts":
			out.Values[i] = ec._UserDailyChallengeStats_totalAttempts(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "totalSubmission":
			out.Values[i] = ec._UserDailyChallengeStats_totalSubmission(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "averageTime":
			out.Values[i] = ec._UserDailyChallengeStats_averageTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "bestTime":
			out.Values[i] = ec._UserDailyChallengeStats_bestTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "streaks":
			out.Values[i] = ec._UserDailyChallengeStats_streaks(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "averageAccuracy":
			out.Values[i] = ec._UserDailyChallengeStats_averageAccuracy(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userDailyChallengeStreaksImplementors = []string{"UserDailyChallengeStreaks"}

func (ec *executionContext) _UserDailyChallengeStreaks(ctx context.Context, sel ast.SelectionSet, obj *models.UserDailyChallengeStreaks) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userDailyChallengeStreaksImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserDailyChallengeStreaks")
		case "current":
			out.Values[i] = ec._UserDailyChallengeStreaks_current(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "highest":
			out.Values[i] = ec._UserDailyChallengeStreaks_highest(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "lastPlayedDate":
			out.Values[i] = ec._UserDailyChallengeStreaks_lastPlayedDate(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userResultImplementors = []string{"UserResult"}

func (ec *executionContext) _UserResult(ctx context.Context, sel ast.SelectionSet, obj *models.UserResult) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userResultImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserResult")
		case "success":
			out.Values[i] = ec._UserResult_success(ctx, field, obj)
		case "error":
			out.Values[i] = ec._UserResult_error(ctx, field, obj)
		case "result":
			out.Values[i] = ec._UserResult_result(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

// endregion **************************** object.gotpl ****************************

// region    ***************************** type.gotpl *****************************

func (ec *executionContext) marshalNBotDetectionResult2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐBotDetectionResult(ctx context.Context, sel ast.SelectionSet, v models.BotDetectionResult) graphql.Marshaler {
	return ec._BotDetectionResult(ctx, sel, &v)
}

func (ec *executionContext) marshalNBotDetectionResult2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐBotDetectionResult(ctx context.Context, sel ast.SelectionSet, v *models.BotDetectionResult) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._BotDetectionResult(ctx, sel, v)
}

func (ec *executionContext) unmarshalNCHALLENGE_DIVISION2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐChallengeDivision(ctx context.Context, v any) (models.ChallengeDivision, error) {
	var res models.ChallengeDivision
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNCHALLENGE_DIVISION2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐChallengeDivision(ctx context.Context, sel ast.SelectionSet, v models.ChallengeDivision) graphql.Marshaler {
	return v
}

func (ec *executionContext) marshalNChallengeResult2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐChallengeResult(ctx context.Context, sel ast.SelectionSet, v models.ChallengeResult) graphql.Marshaler {
	return ec._ChallengeResult(ctx, sel, &v)
}

func (ec *executionContext) marshalNChallengeResult2ᚕmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐChallengeResultᚄ(ctx context.Context, sel ast.SelectionSet, v []models.ChallengeResult) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNChallengeResult2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐChallengeResult(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) unmarshalNSubmitSolutionInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSubmitSolutionInput(ctx context.Context, v any) (models.SubmitSolutionInput, error) {
	res, err := ec.unmarshalInputSubmitSolutionInput(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNUserDailyChallengeStreaks2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserDailyChallengeStreaks(ctx context.Context, sel ast.SelectionSet, v *models.UserDailyChallengeStreaks) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._UserDailyChallengeStreaks(ctx, sel, v)
}

func (ec *executionContext) unmarshalOCHALLENGE_DIVISION2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐChallengeDivision(ctx context.Context, v any) (models.ChallengeDivision, error) {
	var res models.ChallengeDivision
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOCHALLENGE_DIVISION2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐChallengeDivision(ctx context.Context, sel ast.SelectionSet, v models.ChallengeDivision) graphql.Marshaler {
	return v
}

func (ec *executionContext) unmarshalOCHALLENGE_DIVISION2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐChallengeDivision(ctx context.Context, v any) (*models.ChallengeDivision, error) {
	if v == nil {
		return nil, nil
	}
	var res = new(models.ChallengeDivision)
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOCHALLENGE_DIVISION2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐChallengeDivision(ctx context.Context, sel ast.SelectionSet, v *models.ChallengeDivision) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return v
}

func (ec *executionContext) marshalODailyChallenge2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐDailyChallenge(ctx context.Context, sel ast.SelectionSet, v []*models.DailyChallenge) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalODailyChallenge2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐDailyChallenge(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalODailyChallenge2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐDailyChallenge(ctx context.Context, sel ast.SelectionSet, v *models.DailyChallenge) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._DailyChallenge(ctx, sel, v)
}

func (ec *executionContext) marshalODailyChallengeResult2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐDailyChallengeResult(ctx context.Context, sel ast.SelectionSet, v *models.DailyChallengeResult) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._DailyChallengeResult(ctx, sel, v)
}

func (ec *executionContext) marshalODailyChallengeResultWithStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐDailyChallengeResultWithStats(ctx context.Context, sel ast.SelectionSet, v *models.DailyChallengeResultWithStats) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._DailyChallengeResultWithStats(ctx, sel, v)
}

func (ec *executionContext) marshalODailyChallengeStat2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐDailyChallengeStat(ctx context.Context, sel ast.SelectionSet, v *models.DailyChallengeStat) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._DailyChallengeStat(ctx, sel, v)
}

func (ec *executionContext) marshalOLeaderboardPage2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeaderboardPage(ctx context.Context, sel ast.SelectionSet, v *models.LeaderboardPage) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._LeaderboardPage(ctx, sel, v)
}

func (ec *executionContext) unmarshalORESULT_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐResultStatus(ctx context.Context, v any) (models.ResultStatus, error) {
	tmp, err := graphql.UnmarshalString(v)
	res := models.ResultStatus(tmp)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalORESULT_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐResultStatus(ctx context.Context, sel ast.SelectionSet, v models.ResultStatus) graphql.Marshaler {
	_ = sel
	_ = ctx
	res := graphql.MarshalString(string(v))
	return res
}

func (ec *executionContext) marshalOResult2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐResult(ctx context.Context, sel ast.SelectionSet, v *models.Result) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._Result(ctx, sel, v)
}

func (ec *executionContext) marshalOSubmitChallengeResult2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSubmitChallengeResult(ctx context.Context, sel ast.SelectionSet, v *models.SubmitChallengeResult) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._SubmitChallengeResult(ctx, sel, v)
}

func (ec *executionContext) marshalOUserDailyChallengeResultWithStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserDailyChallengeResultWithStats(ctx context.Context, sel ast.SelectionSet, v *models.UserDailyChallengeResultWithStats) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._UserDailyChallengeResultWithStats(ctx, sel, v)
}

func (ec *executionContext) marshalOUserDailyChallengeStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserDailyChallengeStats(ctx context.Context, sel ast.SelectionSet, v *models.UserDailyChallengeStats) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._UserDailyChallengeStats(ctx, sel, v)
}

func (ec *executionContext) marshalOUserResult2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserResult(ctx context.Context, sel ast.SelectionSet, v *models.UserResult) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._UserResult(ctx, sel, v)
}

// endregion ***************************** type.gotpl *****************************
