// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"context"
	"errors"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/ast"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// region    ************************** generated!.gotpl **************************

// endregion ************************** generated!.gotpl **************************

// region    ***************************** args.gotpl *****************************

// endregion ***************************** args.gotpl *****************************

// region    ************************** directives.gotpl **************************

// endregion ************************** directives.gotpl **************************

// region    **************************** field.gotpl *****************************

func (ec *executionContext) _ConnectionRequest_sentBy(ctx context.Context, field graphql.CollectedField, obj *models.ConnectionRequest) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ConnectionRequest_sentBy(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.SentBy, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ConnectionRequest_sentBy(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ConnectionRequest",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedAdditionalInfo_connectionRequest(ctx context.Context, field graphql.CollectedField, obj *models.FeedAdditionalInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedAdditionalInfo_connectionRequest(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ConnectionRequest, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.ConnectionRequest)
	fc.Result = res
	return ec.marshalOConnectionRequest2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐConnectionRequest(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedAdditionalInfo_connectionRequest(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedAdditionalInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "sentBy":
				return ec.fieldContext_ConnectionRequest_sentBy(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ConnectionRequest", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedData__id(ctx context.Context, field graphql.CollectedField, obj *models.FeedData) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedData__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedData__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedData",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedData_sentAt(ctx context.Context, field graphql.CollectedField, obj *models.FeedData) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedData_sentAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.SentAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedData_sentAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedData",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedData_title(ctx context.Context, field graphql.CollectedField, obj *models.FeedData) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedData_title(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Title, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedData_title(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedData",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedData_description(ctx context.Context, field graphql.CollectedField, obj *models.FeedData) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedData_description(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Description, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedData_description(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedData",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedData_expirationTime(ctx context.Context, field graphql.CollectedField, obj *models.FeedData) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedData_expirationTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ExpirationTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedData_expirationTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedData",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedData_sentFor(ctx context.Context, field graphql.CollectedField, obj *models.FeedData) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedData_sentFor(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.SentFor, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedData_sentFor(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedData",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedData_lastLikedByUserName(ctx context.Context, field graphql.CollectedField, obj *models.FeedData) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedData_lastLikedByUserName(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LastLikedByUserName, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedData_lastLikedByUserName(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedData",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedData_likesCount(ctx context.Context, field graphql.CollectedField, obj *models.FeedData) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedData_likesCount(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LikesCount, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedData_likesCount(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedData",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedData_additionalInfo(ctx context.Context, field graphql.CollectedField, obj *models.FeedData) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedData_additionalInfo(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.AdditionalInfo, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.FeedAdditionalInfo)
	fc.Result = res
	return ec.marshalOFeedAdditionalInfo2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFeedAdditionalInfo(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedData_additionalInfo(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedData",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "connectionRequest":
				return ec.fieldContext_FeedAdditionalInfo_connectionRequest(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type FeedAdditionalInfo", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedData_feedForFriends(ctx context.Context, field graphql.CollectedField, obj *models.FeedData) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedData_feedForFriends(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.FeedForFriends, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.FeedForFriends)
	fc.Result = res
	return ec.marshalOFeedForFriends2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFeedForFriends(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedData_feedForFriends(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedData",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "title":
				return ec.fieldContext_FeedForFriends_title(ctx, field)
			case "body":
				return ec.fieldContext_FeedForFriends_body(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type FeedForFriends", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedData_createdAt(ctx context.Context, field graphql.CollectedField, obj *models.FeedData) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedData_createdAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedData_createdAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedData",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedData_updatedAt(ctx context.Context, field graphql.CollectedField, obj *models.FeedData) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedData_updatedAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UpdatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedData_updatedAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedData",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedForFriends_title(ctx context.Context, field graphql.CollectedField, obj *models.FeedForFriends) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedForFriends_title(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Title, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedForFriends_title(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedForFriends",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedForFriends_body(ctx context.Context, field graphql.CollectedField, obj *models.FeedForFriends) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedForFriends_body(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Body, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedForFriends_body(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedForFriends",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedResponse_feeds(ctx context.Context, field graphql.CollectedField, obj *models.FeedResponse) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedResponse_feeds(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Feeds, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.UserFeed)
	fc.Result = res
	return ec.marshalNUserFeed2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserFeedᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedResponse_feeds(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedResponse",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserFeed__id(ctx, field)
			case "userId":
				return ec.fieldContext_UserFeed_userId(ctx, field)
			case "feedType":
				return ec.fieldContext_UserFeed_feedType(ctx, field)
			case "feedReferenceId":
				return ec.fieldContext_UserFeed_feedReferenceId(ctx, field)
			case "isLiked":
				return ec.fieldContext_UserFeed_isLiked(ctx, field)
			case "expirationTime":
				return ec.fieldContext_UserFeed_expirationTime(ctx, field)
			case "feedData":
				return ec.fieldContext_UserFeed_feedData(ctx, field)
			case "createdAt":
				return ec.fieldContext_UserFeed_createdAt(ctx, field)
			case "imageUrl":
				return ec.fieldContext_UserFeed_imageUrl(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserFeed", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedResponse_lastId(ctx context.Context, field graphql.CollectedField, obj *models.FeedResponse) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedResponse_lastId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LastID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedResponse_lastId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedResponse",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedResponse_hasMore(ctx context.Context, field graphql.CollectedField, obj *models.FeedResponse) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedResponse_hasMore(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasMore, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedResponse_hasMore(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedResponse",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedResponse_userDetails(ctx context.Context, field graphql.CollectedField, obj *models.FeedResponse) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedResponse_userDetails(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserDetails, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.UserPublicDetails)
	fc.Result = res
	return ec.marshalNUserPublicDetails2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetailsᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedResponse_userDetails(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedResponse",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPublicDetails__id(ctx, field)
			case "name":
				return ec.fieldContext_UserPublicDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_UserPublicDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserPublicDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserPublicDetails_rating(ctx, field)
			case "badge":
				return ec.fieldContext_UserPublicDetails_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_UserPublicDetails_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_UserPublicDetails_isGuest(ctx, field)
			case "globalRank":
				return ec.fieldContext_UserPublicDetails_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_UserPublicDetails_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_UserPublicDetails_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_UserPublicDetails_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_UserPublicDetails_stats(ctx, field)
			case "userStreaks":
				return ec.fieldContext_UserPublicDetails_userStreaks(ctx, field)
			case "ratingV2":
				return ec.fieldContext_UserPublicDetails_ratingV2(ctx, field)
			case "institutionId":
				return ec.fieldContext_UserPublicDetails_institutionId(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPublicDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _FeedResponse_isRead(ctx context.Context, field graphql.CollectedField, obj *models.FeedResponse) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FeedResponse_isRead(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsRead, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FeedResponse_isRead(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FeedResponse",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserFeed__id(ctx context.Context, field graphql.CollectedField, obj *models.UserFeed) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserFeed__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserFeed__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserFeed",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserFeed_userId(ctx context.Context, field graphql.CollectedField, obj *models.UserFeed) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserFeed_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserFeed_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserFeed",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserFeed_feedType(ctx context.Context, field graphql.CollectedField, obj *models.UserFeed) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserFeed_feedType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.FeedType, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.FeedType)
	fc.Result = res
	return ec.marshalNFEED_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFeedType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserFeed_feedType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserFeed",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type FEED_TYPE does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserFeed_feedReferenceId(ctx context.Context, field graphql.CollectedField, obj *models.UserFeed) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserFeed_feedReferenceId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.FeedReferenceID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserFeed_feedReferenceId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserFeed",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserFeed_isLiked(ctx context.Context, field graphql.CollectedField, obj *models.UserFeed) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserFeed_isLiked(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsLiked, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserFeed_isLiked(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserFeed",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserFeed_expirationTime(ctx context.Context, field graphql.CollectedField, obj *models.UserFeed) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserFeed_expirationTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ExpirationTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserFeed_expirationTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserFeed",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserFeed_feedData(ctx context.Context, field graphql.CollectedField, obj *models.UserFeed) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserFeed_feedData(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.FeedData, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.FeedData)
	fc.Result = res
	return ec.marshalNFeedData2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFeedData(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserFeed_feedData(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserFeed",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_FeedData__id(ctx, field)
			case "sentAt":
				return ec.fieldContext_FeedData_sentAt(ctx, field)
			case "title":
				return ec.fieldContext_FeedData_title(ctx, field)
			case "description":
				return ec.fieldContext_FeedData_description(ctx, field)
			case "expirationTime":
				return ec.fieldContext_FeedData_expirationTime(ctx, field)
			case "sentFor":
				return ec.fieldContext_FeedData_sentFor(ctx, field)
			case "lastLikedByUserName":
				return ec.fieldContext_FeedData_lastLikedByUserName(ctx, field)
			case "likesCount":
				return ec.fieldContext_FeedData_likesCount(ctx, field)
			case "additionalInfo":
				return ec.fieldContext_FeedData_additionalInfo(ctx, field)
			case "feedForFriends":
				return ec.fieldContext_FeedData_feedForFriends(ctx, field)
			case "createdAt":
				return ec.fieldContext_FeedData_createdAt(ctx, field)
			case "updatedAt":
				return ec.fieldContext_FeedData_updatedAt(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type FeedData", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserFeed_createdAt(ctx context.Context, field graphql.CollectedField, obj *models.UserFeed) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserFeed_createdAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserFeed_createdAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserFeed",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserFeed_imageUrl(ctx context.Context, field graphql.CollectedField, obj *models.UserFeed) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserFeed_imageUrl(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ImageURL, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserFeed_imageUrl(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserFeed",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

// endregion **************************** field.gotpl *****************************

// region    **************************** input.gotpl *****************************

// endregion **************************** input.gotpl *****************************

// region    ************************** interface.gotpl ***************************

// endregion ************************** interface.gotpl ***************************

// region    **************************** object.gotpl ****************************

var connectionRequestImplementors = []string{"ConnectionRequest"}

func (ec *executionContext) _ConnectionRequest(ctx context.Context, sel ast.SelectionSet, obj *models.ConnectionRequest) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, connectionRequestImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ConnectionRequest")
		case "sentBy":
			out.Values[i] = ec._ConnectionRequest_sentBy(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var feedAdditionalInfoImplementors = []string{"FeedAdditionalInfo"}

func (ec *executionContext) _FeedAdditionalInfo(ctx context.Context, sel ast.SelectionSet, obj *models.FeedAdditionalInfo) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, feedAdditionalInfoImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("FeedAdditionalInfo")
		case "connectionRequest":
			out.Values[i] = ec._FeedAdditionalInfo_connectionRequest(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var feedDataImplementors = []string{"FeedData"}

func (ec *executionContext) _FeedData(ctx context.Context, sel ast.SelectionSet, obj *models.FeedData) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, feedDataImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("FeedData")
		case "_id":
			out.Values[i] = ec._FeedData__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "sentAt":
			out.Values[i] = ec._FeedData_sentAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "title":
			out.Values[i] = ec._FeedData_title(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "description":
			out.Values[i] = ec._FeedData_description(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "expirationTime":
			out.Values[i] = ec._FeedData_expirationTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "sentFor":
			out.Values[i] = ec._FeedData_sentFor(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "lastLikedByUserName":
			out.Values[i] = ec._FeedData_lastLikedByUserName(ctx, field, obj)
		case "likesCount":
			out.Values[i] = ec._FeedData_likesCount(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "additionalInfo":
			out.Values[i] = ec._FeedData_additionalInfo(ctx, field, obj)
		case "feedForFriends":
			out.Values[i] = ec._FeedData_feedForFriends(ctx, field, obj)
		case "createdAt":
			out.Values[i] = ec._FeedData_createdAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "updatedAt":
			out.Values[i] = ec._FeedData_updatedAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var feedForFriendsImplementors = []string{"FeedForFriends"}

func (ec *executionContext) _FeedForFriends(ctx context.Context, sel ast.SelectionSet, obj *models.FeedForFriends) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, feedForFriendsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("FeedForFriends")
		case "title":
			out.Values[i] = ec._FeedForFriends_title(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "body":
			out.Values[i] = ec._FeedForFriends_body(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var feedResponseImplementors = []string{"FeedResponse"}

func (ec *executionContext) _FeedResponse(ctx context.Context, sel ast.SelectionSet, obj *models.FeedResponse) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, feedResponseImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("FeedResponse")
		case "feeds":
			out.Values[i] = ec._FeedResponse_feeds(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "lastId":
			out.Values[i] = ec._FeedResponse_lastId(ctx, field, obj)
		case "hasMore":
			out.Values[i] = ec._FeedResponse_hasMore(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userDetails":
			out.Values[i] = ec._FeedResponse_userDetails(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "isRead":
			out.Values[i] = ec._FeedResponse_isRead(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userFeedImplementors = []string{"UserFeed"}

func (ec *executionContext) _UserFeed(ctx context.Context, sel ast.SelectionSet, obj *models.UserFeed) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userFeedImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserFeed")
		case "_id":
			out.Values[i] = ec._UserFeed__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userId":
			out.Values[i] = ec._UserFeed_userId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "feedType":
			out.Values[i] = ec._UserFeed_feedType(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "feedReferenceId":
			out.Values[i] = ec._UserFeed_feedReferenceId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "isLiked":
			out.Values[i] = ec._UserFeed_isLiked(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "expirationTime":
			out.Values[i] = ec._UserFeed_expirationTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "feedData":
			out.Values[i] = ec._UserFeed_feedData(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "createdAt":
			out.Values[i] = ec._UserFeed_createdAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "imageUrl":
			out.Values[i] = ec._UserFeed_imageUrl(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

// endregion **************************** object.gotpl ****************************

// region    ***************************** type.gotpl *****************************

func (ec *executionContext) unmarshalNFEED_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFeedType(ctx context.Context, v any) (models.FeedType, error) {
	var res models.FeedType
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNFEED_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFeedType(ctx context.Context, sel ast.SelectionSet, v models.FeedType) graphql.Marshaler {
	return v
}

func (ec *executionContext) marshalNFeedData2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFeedData(ctx context.Context, sel ast.SelectionSet, v *models.FeedData) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._FeedData(ctx, sel, v)
}

func (ec *executionContext) marshalNFeedResponse2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFeedResponse(ctx context.Context, sel ast.SelectionSet, v models.FeedResponse) graphql.Marshaler {
	return ec._FeedResponse(ctx, sel, &v)
}

func (ec *executionContext) marshalNFeedResponse2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFeedResponse(ctx context.Context, sel ast.SelectionSet, v *models.FeedResponse) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._FeedResponse(ctx, sel, v)
}

func (ec *executionContext) marshalNUserFeed2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserFeedᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.UserFeed) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNUserFeed2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserFeed(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNUserFeed2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserFeed(ctx context.Context, sel ast.SelectionSet, v *models.UserFeed) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._UserFeed(ctx, sel, v)
}

func (ec *executionContext) marshalOConnectionRequest2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐConnectionRequest(ctx context.Context, sel ast.SelectionSet, v *models.ConnectionRequest) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._ConnectionRequest(ctx, sel, v)
}

func (ec *executionContext) marshalOFeedAdditionalInfo2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFeedAdditionalInfo(ctx context.Context, sel ast.SelectionSet, v *models.FeedAdditionalInfo) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._FeedAdditionalInfo(ctx, sel, v)
}

func (ec *executionContext) marshalOFeedForFriends2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFeedForFriends(ctx context.Context, sel ast.SelectionSet, v *models.FeedForFriends) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._FeedForFriends(ctx, sel, v)
}

// endregion ***************************** type.gotpl *****************************
