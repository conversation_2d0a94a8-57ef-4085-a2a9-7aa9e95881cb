// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"context"
	"errors"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/ast"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// region    ************************** generated!.gotpl **************************

// endregion ************************** generated!.gotpl **************************

// region    ***************************** args.gotpl *****************************

// endregion ***************************** args.gotpl *****************************

// region    ************************** directives.gotpl **************************

// endregion ************************** directives.gotpl **************************

// region    **************************** field.gotpl *****************************

func (ec *executionContext) _ChallengeOutput_gameId(ctx context.Context, field graphql.CollectedField, obj *models.ChallengeOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ChallengeOutput_gameId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GameID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ChallengeOutput_gameId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ChallengeOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ChallengeOutput_challengedBy(ctx context.Context, field graphql.CollectedField, obj *models.ChallengeOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ChallengeOutput_challengedBy(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ChallengedBy, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ChallengeOutput_challengedBy(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ChallengeOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ChallengeOutput_gameConfig(ctx context.Context, field graphql.CollectedField, obj *models.ChallengeOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ChallengeOutput_gameConfig(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GameConfig, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.GameConfig)
	fc.Result = res
	return ec.marshalNGameConfig2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameConfig(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ChallengeOutput_gameConfig(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ChallengeOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "timeLimit":
				return ec.fieldContext_GameConfig_timeLimit(ctx, field)
			case "numPlayers":
				return ec.fieldContext_GameConfig_numPlayers(ctx, field)
			case "gameType":
				return ec.fieldContext_GameConfig_gameType(ctx, field)
			case "questionTags":
				return ec.fieldContext_GameConfig_questionTags(ctx, field)
			case "difficultyLevel":
				return ec.fieldContext_GameConfig_difficultyLevel(ctx, field)
			case "maxTimePerQuestion":
				return ec.fieldContext_GameConfig_maxTimePerQuestion(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type GameConfig", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ChallengeOutput_createdAt(ctx context.Context, field graphql.CollectedField, obj *models.ChallengeOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ChallengeOutput_createdAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ChallengeOutput_createdAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ChallengeOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ChallengeOutput_status(ctx context.Context, field graphql.CollectedField, obj *models.ChallengeOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ChallengeOutput_status(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Status, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.ChallengeStatus)
	fc.Result = res
	return ec.marshalOChallengeStatus2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐChallengeStatus(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ChallengeOutput_status(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ChallengeOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ChallengeStatus does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ChallengeOutput_opponent(ctx context.Context, field graphql.CollectedField, obj *models.ChallengeOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ChallengeOutput_opponent(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Opponent, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.User)
	fc.Result = res
	return ec.marshalOUser2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUser(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ChallengeOutput_opponent(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ChallengeOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_User__id(ctx, field)
			case "email":
				return ec.fieldContext_User_email(ctx, field)
			case "token":
				return ec.fieldContext_User_token(ctx, field)
			case "name":
				return ec.fieldContext_User_name(ctx, field)
			case "username":
				return ec.fieldContext_User_username(ctx, field)
			case "bio":
				return ec.fieldContext_User_bio(ctx, field)
			case "country":
				return ec.fieldContext_User_country(ctx, field)
			case "links":
				return ec.fieldContext_User_links(ctx, field)
			case "awardsAndAchievements":
				return ec.fieldContext_User_awardsAndAchievements(ctx, field)
			case "phoneNumber":
				return ec.fieldContext_User_phoneNumber(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_User_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_User_rating(ctx, field)
			case "ratingV2":
				return ec.fieldContext_User_ratingV2(ctx, field)
			case "statikCoins":
				return ec.fieldContext_User_statikCoins(ctx, field)
			case "badge":
				return ec.fieldContext_User_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_User_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_User_isGuest(ctx, field)
			case "isBot":
				return ec.fieldContext_User_isBot(ctx, field)
			case "isShadowBanned":
				return ec.fieldContext_User_isShadowBanned(ctx, field)
			case "shadowBanStatus":
				return ec.fieldContext_User_shadowBanStatus(ctx, field)
			case "suspiciousActivity":
				return ec.fieldContext_User_suspiciousActivity(ctx, field)
			case "globalRank":
				return ec.fieldContext_User_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_User_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_User_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_User_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_User_stats(ctx, field)
			case "hasFixedRating":
				return ec.fieldContext_User_hasFixedRating(ctx, field)
			case "isSignup":
				return ec.fieldContext_User_isSignup(ctx, field)
			case "userStreaks":
				return ec.fieldContext_User_userStreaks(ctx, field)
			case "timezone":
				return ec.fieldContext_User_timezone(ctx, field)
			case "additional":
				return ec.fieldContext_User_additional(ctx, field)
			case "league":
				return ec.fieldContext_User_league(ctx, field)
			case "institutionId":
				return ec.fieldContext_User_institutionId(ctx, field)
			case "lastReadFeedId":
				return ec.fieldContext_User_lastReadFeedId(ctx, field)
			case "referralCode":
				return ec.fieldContext_User_referralCode(ctx, field)
			case "isReferred":
				return ec.fieldContext_User_isReferred(ctx, field)
			case "isDeleted":
				return ec.fieldContext_User_isDeleted(ctx, field)
			case "accountStatus":
				return ec.fieldContext_User_accountStatus(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type User", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game__id(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_players(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_players(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Players, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.Player)
	fc.Result = res
	return ec.marshalNPlayer2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPlayerᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_players(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "userId":
				return ec.fieldContext_Player_userId(ctx, field)
			case "rating":
				return ec.fieldContext_Player_rating(ctx, field)
			case "statikCoins":
				return ec.fieldContext_Player_statikCoins(ctx, field)
			case "status":
				return ec.fieldContext_Player_status(ctx, field)
			case "timeLeft":
				return ec.fieldContext_Player_timeLeft(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Player", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_gameStatus(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_gameStatus(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GameStatus, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.GameStatus)
	fc.Result = res
	return ec.marshalNGAME_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameStatus(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_gameStatus(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type GAME_STATUS does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_rematchRequestedBy(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_rematchRequestedBy(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RematchRequestedBy, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_rematchRequestedBy(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_gameType(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_gameType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GameType, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.GameType)
	fc.Result = res
	return ec.marshalNGAME_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_gameType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type GAME_TYPE does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_createdBy(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_createdBy(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatedBy, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_createdBy(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_config(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_config(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Config, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.GameConfig)
	fc.Result = res
	return ec.marshalOGameConfig2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameConfig(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_config(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "timeLimit":
				return ec.fieldContext_GameConfig_timeLimit(ctx, field)
			case "numPlayers":
				return ec.fieldContext_GameConfig_numPlayers(ctx, field)
			case "gameType":
				return ec.fieldContext_GameConfig_gameType(ctx, field)
			case "questionTags":
				return ec.fieldContext_GameConfig_questionTags(ctx, field)
			case "difficultyLevel":
				return ec.fieldContext_GameConfig_difficultyLevel(ctx, field)
			case "maxTimePerQuestion":
				return ec.fieldContext_GameConfig_maxTimePerQuestion(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type GameConfig", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_questions(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_questions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Questions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.GameQuestion)
	fc.Result = res
	return ec.marshalOGameQuestion2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameQuestion(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_questions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "question":
				return ec.fieldContext_GameQuestion_question(ctx, field)
			case "submissions":
				return ec.fieldContext_GameQuestion_submissions(ctx, field)
			case "stats":
				return ec.fieldContext_GameQuestion_stats(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type GameQuestion", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_minifiedQuestions(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_minifiedQuestions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.MinifiedQuestions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*string)
	fc.Result = res
	return ec.marshalOString2ᚕᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_minifiedQuestions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_encryptedQuestions(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_encryptedQuestions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EncryptedQuestions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*string)
	fc.Result = res
	return ec.marshalOString2ᚕᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_encryptedQuestions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_leaderBoard(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_leaderBoard(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LeaderBoard, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.LeaderBoardEntry)
	fc.Result = res
	return ec.marshalOLeaderBoardEntry2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeaderBoardEntry(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_leaderBoard(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "userId":
				return ec.fieldContext_LeaderBoardEntry_userId(ctx, field)
			case "correct":
				return ec.fieldContext_LeaderBoardEntry_correct(ctx, field)
			case "incorrect":
				return ec.fieldContext_LeaderBoardEntry_incorrect(ctx, field)
			case "totalPoints":
				return ec.fieldContext_LeaderBoardEntry_totalPoints(ctx, field)
			case "ratingChange":
				return ec.fieldContext_LeaderBoardEntry_ratingChange(ctx, field)
			case "statikCoinsEarned":
				return ec.fieldContext_LeaderBoardEntry_statikCoinsEarned(ctx, field)
			case "rank":
				return ec.fieldContext_LeaderBoardEntry_rank(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type LeaderBoardEntry", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_startTime(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_startTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StartTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_startTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_endTime(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_endTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EndTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_endTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_seriesId(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_seriesId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.SeriesID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_seriesId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_showdownId(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_showdownId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ShowdownId, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_showdownId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_showdownGameConfig(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_showdownGameConfig(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ShowdownGameConfig, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.ShowdownGameConfig)
	fc.Result = res
	return ec.marshalOShowdownGameConfig2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownGameConfig(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_showdownGameConfig(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "isRoundEnded":
				return ec.fieldContext_ShowdownGameConfig_isRoundEnded(ctx, field)
			case "hasOpponentNotShown":
				return ec.fieldContext_ShowdownGameConfig_hasOpponentNotShown(ctx, field)
			case "nextGameId":
				return ec.fieldContext_ShowdownGameConfig_nextGameId(ctx, field)
			case "round":
				return ec.fieldContext_ShowdownGameConfig_round(ctx, field)
			case "nextGameStartsAt":
				return ec.fieldContext_ShowdownGameConfig_nextGameStartsAt(ctx, field)
			case "totalGamesPlayed":
				return ec.fieldContext_ShowdownGameConfig_totalGamesPlayed(ctx, field)
			case "showdownGamePlayer":
				return ec.fieldContext_ShowdownGameConfig_showdownGamePlayer(ctx, field)
			case "numOfGames":
				return ec.fieldContext_ShowdownGameConfig_numOfGames(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ShowdownGameConfig", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameConfig_timeLimit(ctx context.Context, field graphql.CollectedField, obj *models.GameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameConfig_timeLimit(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TimeLimit, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameConfig_timeLimit(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameConfig_numPlayers(ctx context.Context, field graphql.CollectedField, obj *models.GameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameConfig_numPlayers(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NumPlayers, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameConfig_numPlayers(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameConfig_gameType(ctx context.Context, field graphql.CollectedField, obj *models.GameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameConfig_gameType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GameType, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(models.GameType)
	fc.Result = res
	return ec.marshalOGAME_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameConfig_gameType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type GAME_TYPE does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameConfig_questionTags(ctx context.Context, field graphql.CollectedField, obj *models.GameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameConfig_questionTags(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.QuestionTags, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]string)
	fc.Result = res
	return ec.marshalOString2ᚕstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameConfig_questionTags(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameConfig_difficultyLevel(ctx context.Context, field graphql.CollectedField, obj *models.GameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameConfig_difficultyLevel(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.DifficultyLevel, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]int)
	fc.Result = res
	return ec.marshalOInt2ᚕintᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameConfig_difficultyLevel(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameConfig_maxTimePerQuestion(ctx context.Context, field graphql.CollectedField, obj *models.GameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameConfig_maxTimePerQuestion(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.MaxTimePerQuestion, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameConfig_maxTimePerQuestion(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameDetailedAnalysis_game(ctx context.Context, field graphql.CollectedField, obj *models.GameDetailedAnalysis) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameDetailedAnalysis_game(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Game, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.Game)
	fc.Result = res
	return ec.marshalNGame2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGame(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameDetailedAnalysis_game(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameDetailedAnalysis",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_Game__id(ctx, field)
			case "players":
				return ec.fieldContext_Game_players(ctx, field)
			case "gameStatus":
				return ec.fieldContext_Game_gameStatus(ctx, field)
			case "rematchRequestedBy":
				return ec.fieldContext_Game_rematchRequestedBy(ctx, field)
			case "gameType":
				return ec.fieldContext_Game_gameType(ctx, field)
			case "createdBy":
				return ec.fieldContext_Game_createdBy(ctx, field)
			case "config":
				return ec.fieldContext_Game_config(ctx, field)
			case "questions":
				return ec.fieldContext_Game_questions(ctx, field)
			case "minifiedQuestions":
				return ec.fieldContext_Game_minifiedQuestions(ctx, field)
			case "encryptedQuestions":
				return ec.fieldContext_Game_encryptedQuestions(ctx, field)
			case "leaderBoard":
				return ec.fieldContext_Game_leaderBoard(ctx, field)
			case "startTime":
				return ec.fieldContext_Game_startTime(ctx, field)
			case "endTime":
				return ec.fieldContext_Game_endTime(ctx, field)
			case "seriesId":
				return ec.fieldContext_Game_seriesId(ctx, field)
			case "showdownId":
				return ec.fieldContext_Game_showdownId(ctx, field)
			case "showdownGameConfig":
				return ec.fieldContext_Game_showdownGameConfig(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Game", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameDetailedAnalysis_questions(ctx context.Context, field graphql.CollectedField, obj *models.GameDetailedAnalysis) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameDetailedAnalysis_questions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Questions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.GameQuestionAnalysis)
	fc.Result = res
	return ec.marshalNGameQuestionAnalysis2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameQuestionAnalysisᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameDetailedAnalysis_questions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameDetailedAnalysis",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "question":
				return ec.fieldContext_GameQuestionAnalysis_question(ctx, field)
			case "avgTimes":
				return ec.fieldContext_GameQuestionAnalysis_avgTimes(ctx, field)
			case "globalAvgTime":
				return ec.fieldContext_GameQuestionAnalysis_globalAvgTime(ctx, field)
			case "globalBestTime":
				return ec.fieldContext_GameQuestionAnalysis_globalBestTime(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type GameQuestionAnalysis", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameQuestion_question(ctx context.Context, field graphql.CollectedField, obj *models.GameQuestion) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameQuestion_question(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Question, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.Question)
	fc.Result = res
	return ec.marshalOQuestion2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐQuestion(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameQuestion_question(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameQuestion",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_Question_id(ctx, field)
			case "expression":
				return ec.fieldContext_Question_expression(ctx, field)
			case "description":
				return ec.fieldContext_Question_description(ctx, field)
			case "options":
				return ec.fieldContext_Question_options(ctx, field)
			case "answers":
				return ec.fieldContext_Question_answers(ctx, field)
			case "questionType":
				return ec.fieldContext_Question_questionType(ctx, field)
			case "rating":
				return ec.fieldContext_Question_rating(ctx, field)
			case "maxTimeLimit":
				return ec.fieldContext_Question_maxTimeLimit(ctx, field)
			case "tags":
				return ec.fieldContext_Question_tags(ctx, field)
			case "fastestTimeTaken":
				return ec.fieldContext_Question_fastestTimeTaken(ctx, field)
			case "presetIdentifier":
				return ec.fieldContext_Question_presetIdentifier(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Question", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameQuestion_submissions(ctx context.Context, field graphql.CollectedField, obj *models.GameQuestion) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameQuestion_submissions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Submissions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.Submission)
	fc.Result = res
	return ec.marshalOSubmission2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSubmission(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameQuestion_submissions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameQuestion",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "userId":
				return ec.fieldContext_Submission_userId(ctx, field)
			case "timeTaken":
				return ec.fieldContext_Submission_timeTaken(ctx, field)
			case "points":
				return ec.fieldContext_Submission_points(ctx, field)
			case "submissionTime":
				return ec.fieldContext_Submission_submissionTime(ctx, field)
			case "isCorrect":
				return ec.fieldContext_Submission_isCorrect(ctx, field)
			case "inCorrectAttempts":
				return ec.fieldContext_Submission_inCorrectAttempts(ctx, field)
			case "submittedValues":
				return ec.fieldContext_Submission_submittedValues(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Submission", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameQuestion_stats(ctx context.Context, field graphql.CollectedField, obj *models.GameQuestion) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameQuestion_stats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Stats, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(models.GameQuestionStats)
	fc.Result = res
	return ec.marshalOGameQuestionStats2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameQuestionStats(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameQuestion_stats(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameQuestion",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "fastestTime":
				return ec.fieldContext_GameQuestionStats_fastestTime(ctx, field)
			case "userIds":
				return ec.fieldContext_GameQuestionStats_userIds(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type GameQuestionStats", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameQuestionAnalysis_question(ctx context.Context, field graphql.CollectedField, obj *models.GameQuestionAnalysis) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameQuestionAnalysis_question(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Question, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.Question)
	fc.Result = res
	return ec.marshalNQuestion2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐQuestion(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameQuestionAnalysis_question(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameQuestionAnalysis",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_Question_id(ctx, field)
			case "expression":
				return ec.fieldContext_Question_expression(ctx, field)
			case "description":
				return ec.fieldContext_Question_description(ctx, field)
			case "options":
				return ec.fieldContext_Question_options(ctx, field)
			case "answers":
				return ec.fieldContext_Question_answers(ctx, field)
			case "questionType":
				return ec.fieldContext_Question_questionType(ctx, field)
			case "rating":
				return ec.fieldContext_Question_rating(ctx, field)
			case "maxTimeLimit":
				return ec.fieldContext_Question_maxTimeLimit(ctx, field)
			case "tags":
				return ec.fieldContext_Question_tags(ctx, field)
			case "fastestTimeTaken":
				return ec.fieldContext_Question_fastestTimeTaken(ctx, field)
			case "presetIdentifier":
				return ec.fieldContext_Question_presetIdentifier(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Question", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameQuestionAnalysis_avgTimes(ctx context.Context, field graphql.CollectedField, obj *models.GameQuestionAnalysis) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameQuestionAnalysis_avgTimes(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.AvgTimes, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.UserAvgTime)
	fc.Result = res
	return ec.marshalNUserAvgTime2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserAvgTimeᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameQuestionAnalysis_avgTimes(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameQuestionAnalysis",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "userId":
				return ec.fieldContext_UserAvgTime_userId(ctx, field)
			case "questionAvgTime":
				return ec.fieldContext_UserAvgTime_questionAvgTime(ctx, field)
			case "presetAvgTime":
				return ec.fieldContext_UserAvgTime_presetAvgTime(ctx, field)
			case "presetBestTime":
				return ec.fieldContext_UserAvgTime_presetBestTime(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserAvgTime", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameQuestionAnalysis_globalAvgTime(ctx context.Context, field graphql.CollectedField, obj *models.GameQuestionAnalysis) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameQuestionAnalysis_globalAvgTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GlobalAvgTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameQuestionAnalysis_globalAvgTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameQuestionAnalysis",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameQuestionAnalysis_globalBestTime(ctx context.Context, field graphql.CollectedField, obj *models.GameQuestionAnalysis) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameQuestionAnalysis_globalBestTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GlobalBestTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameQuestionAnalysis_globalBestTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameQuestionAnalysis",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameQuestionStats_fastestTime(ctx context.Context, field graphql.CollectedField, obj *models.GameQuestionStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameQuestionStats_fastestTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.FastestTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameQuestionStats_fastestTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameQuestionStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameQuestionStats_userIds(ctx context.Context, field graphql.CollectedField, obj *models.GameQuestionStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameQuestionStats_userIds(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserIds, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚕᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameQuestionStats_userIds(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameQuestionStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GetGamesByRatingOutput_games(ctx context.Context, field graphql.CollectedField, obj *models.GetGamesByRatingOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GetGamesByRatingOutput_games(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Games, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.MinifiedGame)
	fc.Result = res
	return ec.marshalOMinifiedGame2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMinifiedGame(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GetGamesByRatingOutput_games(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GetGamesByRatingOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_MinifiedGame__id(ctx, field)
			case "players":
				return ec.fieldContext_MinifiedGame_players(ctx, field)
			case "config":
				return ec.fieldContext_MinifiedGame_config(ctx, field)
			case "leaderBoard":
				return ec.fieldContext_MinifiedGame_leaderBoard(ctx, field)
			case "startTime":
				return ec.fieldContext_MinifiedGame_startTime(ctx, field)
			case "endTime":
				return ec.fieldContext_MinifiedGame_endTime(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type MinifiedGame", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GetGamesByRatingOutput_puzzleGames(ctx context.Context, field graphql.CollectedField, obj *models.GetGamesByRatingOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GetGamesByRatingOutput_puzzleGames(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PuzzleGames, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.MinifiedPuzzleGame)
	fc.Result = res
	return ec.marshalOMinifiedPuzzleGame2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMinifiedPuzzleGame(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GetGamesByRatingOutput_puzzleGames(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GetGamesByRatingOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_MinifiedPuzzleGame__id(ctx, field)
			case "players":
				return ec.fieldContext_MinifiedPuzzleGame_players(ctx, field)
			case "config":
				return ec.fieldContext_MinifiedPuzzleGame_config(ctx, field)
			case "leaderBoard":
				return ec.fieldContext_MinifiedPuzzleGame_leaderBoard(ctx, field)
			case "startTime":
				return ec.fieldContext_MinifiedPuzzleGame_startTime(ctx, field)
			case "endTime":
				return ec.fieldContext_MinifiedPuzzleGame_endTime(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type MinifiedPuzzleGame", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GetGamesByRatingOutput_users(ctx context.Context, field graphql.CollectedField, obj *models.GetGamesByRatingOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GetGamesByRatingOutput_users(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Users, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.UserPublicDetails)
	fc.Result = res
	return ec.marshalOUserPublicDetails2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GetGamesByRatingOutput_users(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GetGamesByRatingOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPublicDetails__id(ctx, field)
			case "name":
				return ec.fieldContext_UserPublicDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_UserPublicDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserPublicDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserPublicDetails_rating(ctx, field)
			case "badge":
				return ec.fieldContext_UserPublicDetails_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_UserPublicDetails_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_UserPublicDetails_isGuest(ctx, field)
			case "globalRank":
				return ec.fieldContext_UserPublicDetails_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_UserPublicDetails_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_UserPublicDetails_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_UserPublicDetails_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_UserPublicDetails_stats(ctx, field)
			case "userStreaks":
				return ec.fieldContext_UserPublicDetails_userStreaks(ctx, field)
			case "ratingV2":
				return ec.fieldContext_UserPublicDetails_ratingV2(ctx, field)
			case "institutionId":
				return ec.fieldContext_UserPublicDetails_institutionId(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPublicDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GetGamesByRatingOutput_totalCount(ctx context.Context, field graphql.CollectedField, obj *models.GetGamesByRatingOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GetGamesByRatingOutput_totalCount(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalCount, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GetGamesByRatingOutput_totalCount(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GetGamesByRatingOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GetGamesOutput_games(ctx context.Context, field graphql.CollectedField, obj *models.GetGamesOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GetGamesOutput_games(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Games, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.MinifiedGame)
	fc.Result = res
	return ec.marshalOMinifiedGame2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMinifiedGame(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GetGamesOutput_games(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GetGamesOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_MinifiedGame__id(ctx, field)
			case "players":
				return ec.fieldContext_MinifiedGame_players(ctx, field)
			case "config":
				return ec.fieldContext_MinifiedGame_config(ctx, field)
			case "leaderBoard":
				return ec.fieldContext_MinifiedGame_leaderBoard(ctx, field)
			case "startTime":
				return ec.fieldContext_MinifiedGame_startTime(ctx, field)
			case "endTime":
				return ec.fieldContext_MinifiedGame_endTime(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type MinifiedGame", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GetGamesOutput_users(ctx context.Context, field graphql.CollectedField, obj *models.GetGamesOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GetGamesOutput_users(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Users, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.UserPublicDetails)
	fc.Result = res
	return ec.marshalOUserPublicDetails2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GetGamesOutput_users(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GetGamesOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPublicDetails__id(ctx, field)
			case "name":
				return ec.fieldContext_UserPublicDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_UserPublicDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserPublicDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserPublicDetails_rating(ctx, field)
			case "badge":
				return ec.fieldContext_UserPublicDetails_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_UserPublicDetails_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_UserPublicDetails_isGuest(ctx, field)
			case "globalRank":
				return ec.fieldContext_UserPublicDetails_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_UserPublicDetails_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_UserPublicDetails_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_UserPublicDetails_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_UserPublicDetails_stats(ctx, field)
			case "userStreaks":
				return ec.fieldContext_UserPublicDetails_userStreaks(ctx, field)
			case "ratingV2":
				return ec.fieldContext_UserPublicDetails_ratingV2(ctx, field)
			case "institutionId":
				return ec.fieldContext_UserPublicDetails_institutionId(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPublicDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderBoardEntry_userId(ctx context.Context, field graphql.CollectedField, obj *models.LeaderBoardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderBoardEntry_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderBoardEntry_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderBoardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderBoardEntry_correct(ctx context.Context, field graphql.CollectedField, obj *models.LeaderBoardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderBoardEntry_correct(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Correct, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderBoardEntry_correct(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderBoardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderBoardEntry_incorrect(ctx context.Context, field graphql.CollectedField, obj *models.LeaderBoardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderBoardEntry_incorrect(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Incorrect, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderBoardEntry_incorrect(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderBoardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderBoardEntry_totalPoints(ctx context.Context, field graphql.CollectedField, obj *models.LeaderBoardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderBoardEntry_totalPoints(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalPoints, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*float64)
	fc.Result = res
	return ec.marshalOFloat2ᚖfloat64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderBoardEntry_totalPoints(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderBoardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderBoardEntry_ratingChange(ctx context.Context, field graphql.CollectedField, obj *models.LeaderBoardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderBoardEntry_ratingChange(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RatingChange, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderBoardEntry_ratingChange(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderBoardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderBoardEntry_statikCoinsEarned(ctx context.Context, field graphql.CollectedField, obj *models.LeaderBoardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderBoardEntry_statikCoinsEarned(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StatikCoinsEarned, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderBoardEntry_statikCoinsEarned(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderBoardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderBoardEntry_rank(ctx context.Context, field graphql.CollectedField, obj *models.LeaderBoardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderBoardEntry_rank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderBoardEntry_rank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderBoardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MinifiedGame__id(ctx context.Context, field graphql.CollectedField, obj *models.MinifiedGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MinifiedGame__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MinifiedGame__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MinifiedGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MinifiedGame_players(ctx context.Context, field graphql.CollectedField, obj *models.MinifiedGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MinifiedGame_players(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Players, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.Player)
	fc.Result = res
	return ec.marshalNPlayer2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPlayerᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MinifiedGame_players(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MinifiedGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "userId":
				return ec.fieldContext_Player_userId(ctx, field)
			case "rating":
				return ec.fieldContext_Player_rating(ctx, field)
			case "statikCoins":
				return ec.fieldContext_Player_statikCoins(ctx, field)
			case "status":
				return ec.fieldContext_Player_status(ctx, field)
			case "timeLeft":
				return ec.fieldContext_Player_timeLeft(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Player", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _MinifiedGame_config(ctx context.Context, field graphql.CollectedField, obj *models.MinifiedGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MinifiedGame_config(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Config, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.GameConfig)
	fc.Result = res
	return ec.marshalOGameConfig2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameConfig(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MinifiedGame_config(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MinifiedGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "timeLimit":
				return ec.fieldContext_GameConfig_timeLimit(ctx, field)
			case "numPlayers":
				return ec.fieldContext_GameConfig_numPlayers(ctx, field)
			case "gameType":
				return ec.fieldContext_GameConfig_gameType(ctx, field)
			case "questionTags":
				return ec.fieldContext_GameConfig_questionTags(ctx, field)
			case "difficultyLevel":
				return ec.fieldContext_GameConfig_difficultyLevel(ctx, field)
			case "maxTimePerQuestion":
				return ec.fieldContext_GameConfig_maxTimePerQuestion(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type GameConfig", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _MinifiedGame_leaderBoard(ctx context.Context, field graphql.CollectedField, obj *models.MinifiedGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MinifiedGame_leaderBoard(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LeaderBoard, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.LeaderBoardEntry)
	fc.Result = res
	return ec.marshalOLeaderBoardEntry2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeaderBoardEntry(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MinifiedGame_leaderBoard(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MinifiedGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "userId":
				return ec.fieldContext_LeaderBoardEntry_userId(ctx, field)
			case "correct":
				return ec.fieldContext_LeaderBoardEntry_correct(ctx, field)
			case "incorrect":
				return ec.fieldContext_LeaderBoardEntry_incorrect(ctx, field)
			case "totalPoints":
				return ec.fieldContext_LeaderBoardEntry_totalPoints(ctx, field)
			case "ratingChange":
				return ec.fieldContext_LeaderBoardEntry_ratingChange(ctx, field)
			case "statikCoinsEarned":
				return ec.fieldContext_LeaderBoardEntry_statikCoinsEarned(ctx, field)
			case "rank":
				return ec.fieldContext_LeaderBoardEntry_rank(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type LeaderBoardEntry", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _MinifiedGame_startTime(ctx context.Context, field graphql.CollectedField, obj *models.MinifiedGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MinifiedGame_startTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StartTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MinifiedGame_startTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MinifiedGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MinifiedGame_endTime(ctx context.Context, field graphql.CollectedField, obj *models.MinifiedGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MinifiedGame_endTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EndTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MinifiedGame_endTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MinifiedGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Player_userId(ctx context.Context, field graphql.CollectedField, obj *models.Player) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Player_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Player_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Player",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Player_rating(ctx context.Context, field graphql.CollectedField, obj *models.Player) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Player_rating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Player_rating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Player",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Player_statikCoins(ctx context.Context, field graphql.CollectedField, obj *models.Player) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Player_statikCoins(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StatikCoins, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Player_statikCoins(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Player",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Player_status(ctx context.Context, field graphql.CollectedField, obj *models.Player) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Player_status(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Status, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(models.PlayerStatus)
	fc.Result = res
	return ec.marshalOPLAYER_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPlayerStatus(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Player_status(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Player",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type PLAYER_STATUS does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Player_timeLeft(ctx context.Context, field graphql.CollectedField, obj *models.Player) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Player_timeLeft(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TimeLeft, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Player_timeLeft(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Player",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _SearchSubscriptionOutput_game(ctx context.Context, field graphql.CollectedField, obj *models.SearchSubscriptionOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_SearchSubscriptionOutput_game(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Game, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.Game)
	fc.Result = res
	return ec.marshalOGame2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGame(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_SearchSubscriptionOutput_game(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "SearchSubscriptionOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_Game__id(ctx, field)
			case "players":
				return ec.fieldContext_Game_players(ctx, field)
			case "gameStatus":
				return ec.fieldContext_Game_gameStatus(ctx, field)
			case "rematchRequestedBy":
				return ec.fieldContext_Game_rematchRequestedBy(ctx, field)
			case "gameType":
				return ec.fieldContext_Game_gameType(ctx, field)
			case "createdBy":
				return ec.fieldContext_Game_createdBy(ctx, field)
			case "config":
				return ec.fieldContext_Game_config(ctx, field)
			case "questions":
				return ec.fieldContext_Game_questions(ctx, field)
			case "minifiedQuestions":
				return ec.fieldContext_Game_minifiedQuestions(ctx, field)
			case "encryptedQuestions":
				return ec.fieldContext_Game_encryptedQuestions(ctx, field)
			case "leaderBoard":
				return ec.fieldContext_Game_leaderBoard(ctx, field)
			case "startTime":
				return ec.fieldContext_Game_startTime(ctx, field)
			case "endTime":
				return ec.fieldContext_Game_endTime(ctx, field)
			case "seriesId":
				return ec.fieldContext_Game_seriesId(ctx, field)
			case "showdownId":
				return ec.fieldContext_Game_showdownId(ctx, field)
			case "showdownGameConfig":
				return ec.fieldContext_Game_showdownGameConfig(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Game", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _SearchSubscriptionOutput_event(ctx context.Context, field graphql.CollectedField, obj *models.SearchSubscriptionOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_SearchSubscriptionOutput_event(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Event, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_SearchSubscriptionOutput_event(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "SearchSubscriptionOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _SearchSubscriptionOutput_opponent(ctx context.Context, field graphql.CollectedField, obj *models.SearchSubscriptionOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_SearchSubscriptionOutput_opponent(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Opponent, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.User)
	fc.Result = res
	return ec.marshalOUser2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUser(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_SearchSubscriptionOutput_opponent(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "SearchSubscriptionOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_User__id(ctx, field)
			case "email":
				return ec.fieldContext_User_email(ctx, field)
			case "token":
				return ec.fieldContext_User_token(ctx, field)
			case "name":
				return ec.fieldContext_User_name(ctx, field)
			case "username":
				return ec.fieldContext_User_username(ctx, field)
			case "bio":
				return ec.fieldContext_User_bio(ctx, field)
			case "country":
				return ec.fieldContext_User_country(ctx, field)
			case "links":
				return ec.fieldContext_User_links(ctx, field)
			case "awardsAndAchievements":
				return ec.fieldContext_User_awardsAndAchievements(ctx, field)
			case "phoneNumber":
				return ec.fieldContext_User_phoneNumber(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_User_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_User_rating(ctx, field)
			case "ratingV2":
				return ec.fieldContext_User_ratingV2(ctx, field)
			case "statikCoins":
				return ec.fieldContext_User_statikCoins(ctx, field)
			case "badge":
				return ec.fieldContext_User_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_User_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_User_isGuest(ctx, field)
			case "isBot":
				return ec.fieldContext_User_isBot(ctx, field)
			case "isShadowBanned":
				return ec.fieldContext_User_isShadowBanned(ctx, field)
			case "shadowBanStatus":
				return ec.fieldContext_User_shadowBanStatus(ctx, field)
			case "suspiciousActivity":
				return ec.fieldContext_User_suspiciousActivity(ctx, field)
			case "globalRank":
				return ec.fieldContext_User_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_User_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_User_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_User_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_User_stats(ctx, field)
			case "hasFixedRating":
				return ec.fieldContext_User_hasFixedRating(ctx, field)
			case "isSignup":
				return ec.fieldContext_User_isSignup(ctx, field)
			case "userStreaks":
				return ec.fieldContext_User_userStreaks(ctx, field)
			case "timezone":
				return ec.fieldContext_User_timezone(ctx, field)
			case "additional":
				return ec.fieldContext_User_additional(ctx, field)
			case "league":
				return ec.fieldContext_User_league(ctx, field)
			case "institutionId":
				return ec.fieldContext_User_institutionId(ctx, field)
			case "lastReadFeedId":
				return ec.fieldContext_User_lastReadFeedId(ctx, field)
			case "referralCode":
				return ec.fieldContext_User_referralCode(ctx, field)
			case "isReferred":
				return ec.fieldContext_User_isReferred(ctx, field)
			case "isDeleted":
				return ec.fieldContext_User_isDeleted(ctx, field)
			case "accountStatus":
				return ec.fieldContext_User_accountStatus(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type User", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Submission_userId(ctx context.Context, field graphql.CollectedField, obj *models.Submission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Submission_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Submission_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Submission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Submission_timeTaken(ctx context.Context, field graphql.CollectedField, obj *models.Submission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Submission_timeTaken(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TimeTaken, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Submission_timeTaken(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Submission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Submission_points(ctx context.Context, field graphql.CollectedField, obj *models.Submission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Submission_points(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Points, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Submission_points(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Submission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Submission_submissionTime(ctx context.Context, field graphql.CollectedField, obj *models.Submission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Submission_submissionTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.SubmissionTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.Date)
	fc.Result = res
	return ec.marshalODate2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐDate(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Submission_submissionTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Submission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Date does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Submission_isCorrect(ctx context.Context, field graphql.CollectedField, obj *models.Submission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Submission_isCorrect(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsCorrect, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Submission_isCorrect(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Submission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Submission_inCorrectAttempts(ctx context.Context, field graphql.CollectedField, obj *models.Submission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Submission_inCorrectAttempts(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.InCorrectAttempts, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Submission_inCorrectAttempts(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Submission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Submission_submittedValues(ctx context.Context, field graphql.CollectedField, obj *models.Submission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Submission_submittedValues(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.SubmittedValues, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*string)
	fc.Result = res
	return ec.marshalOString2ᚕᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Submission_submittedValues(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Submission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _SubscriptionOutput_game(ctx context.Context, field graphql.CollectedField, obj *models.SubscriptionOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_SubscriptionOutput_game(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Game, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.Game)
	fc.Result = res
	return ec.marshalOGame2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGame(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_SubscriptionOutput_game(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "SubscriptionOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_Game__id(ctx, field)
			case "players":
				return ec.fieldContext_Game_players(ctx, field)
			case "gameStatus":
				return ec.fieldContext_Game_gameStatus(ctx, field)
			case "rematchRequestedBy":
				return ec.fieldContext_Game_rematchRequestedBy(ctx, field)
			case "gameType":
				return ec.fieldContext_Game_gameType(ctx, field)
			case "createdBy":
				return ec.fieldContext_Game_createdBy(ctx, field)
			case "config":
				return ec.fieldContext_Game_config(ctx, field)
			case "questions":
				return ec.fieldContext_Game_questions(ctx, field)
			case "minifiedQuestions":
				return ec.fieldContext_Game_minifiedQuestions(ctx, field)
			case "encryptedQuestions":
				return ec.fieldContext_Game_encryptedQuestions(ctx, field)
			case "leaderBoard":
				return ec.fieldContext_Game_leaderBoard(ctx, field)
			case "startTime":
				return ec.fieldContext_Game_startTime(ctx, field)
			case "endTime":
				return ec.fieldContext_Game_endTime(ctx, field)
			case "seriesId":
				return ec.fieldContext_Game_seriesId(ctx, field)
			case "showdownId":
				return ec.fieldContext_Game_showdownId(ctx, field)
			case "showdownGameConfig":
				return ec.fieldContext_Game_showdownGameConfig(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Game", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _SubscriptionOutput_event(ctx context.Context, field graphql.CollectedField, obj *models.SubscriptionOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_SubscriptionOutput_event(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Event, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_SubscriptionOutput_event(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "SubscriptionOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _SubscriptionOutput_question(ctx context.Context, field graphql.CollectedField, obj *models.SubscriptionOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_SubscriptionOutput_question(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Question, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.Question)
	fc.Result = res
	return ec.marshalOQuestion2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐQuestion(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_SubscriptionOutput_question(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "SubscriptionOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_Question_id(ctx, field)
			case "expression":
				return ec.fieldContext_Question_expression(ctx, field)
			case "description":
				return ec.fieldContext_Question_description(ctx, field)
			case "options":
				return ec.fieldContext_Question_options(ctx, field)
			case "answers":
				return ec.fieldContext_Question_answers(ctx, field)
			case "questionType":
				return ec.fieldContext_Question_questionType(ctx, field)
			case "rating":
				return ec.fieldContext_Question_rating(ctx, field)
			case "maxTimeLimit":
				return ec.fieldContext_Question_maxTimeLimit(ctx, field)
			case "tags":
				return ec.fieldContext_Question_tags(ctx, field)
			case "fastestTimeTaken":
				return ec.fieldContext_Question_fastestTimeTaken(ctx, field)
			case "presetIdentifier":
				return ec.fieldContext_Question_presetIdentifier(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Question", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserAvgTime_userId(ctx context.Context, field graphql.CollectedField, obj *models.UserAvgTime) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserAvgTime_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserAvgTime_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserAvgTime",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserAvgTime_questionAvgTime(ctx context.Context, field graphql.CollectedField, obj *models.UserAvgTime) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserAvgTime_questionAvgTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.QuestionAvgTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserAvgTime_questionAvgTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserAvgTime",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserAvgTime_presetAvgTime(ctx context.Context, field graphql.CollectedField, obj *models.UserAvgTime) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserAvgTime_presetAvgTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PresetAvgTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserAvgTime_presetAvgTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserAvgTime",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserAvgTime_presetBestTime(ctx context.Context, field graphql.CollectedField, obj *models.UserAvgTime) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserAvgTime_presetBestTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PresetBestTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserAvgTime_presetBestTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserAvgTime",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

// endregion **************************** field.gotpl *****************************

// region    **************************** input.gotpl *****************************

func (ec *executionContext) unmarshalInputChallengeUserInput(ctx context.Context, obj any) (models.ChallengeUserInput, error) {
	var it models.ChallengeUserInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"userId", "gameConfig"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "userId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("userId"))
			data, err := ec.unmarshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.UserID = data
		case "gameConfig":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("gameConfig"))
			data, err := ec.unmarshalOGameConfigInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameConfigInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.GameConfig = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputGameConfigInput(ctx context.Context, obj any) (models.GameConfigInput, error) {
	var it models.GameConfigInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"timeLimit", "numPlayers", "gameType", "questionTags", "difficultyLevel", "maxTimePerQuestion"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "timeLimit":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("timeLimit"))
			data, err := ec.unmarshalOInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.TimeLimit = data
		case "numPlayers":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("numPlayers"))
			data, err := ec.unmarshalOInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.NumPlayers = data
		case "gameType":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("gameType"))
			data, err := ec.unmarshalOGAME_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameType(ctx, v)
			if err != nil {
				return it, err
			}
			it.GameType = data
		case "questionTags":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("questionTags"))
			data, err := ec.unmarshalOString2ᚕstringᚄ(ctx, v)
			if err != nil {
				return it, err
			}
			it.QuestionTags = data
		case "difficultyLevel":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("difficultyLevel"))
			data, err := ec.unmarshalOInt2ᚕintᚄ(ctx, v)
			if err != nil {
				return it, err
			}
			it.DifficultyLevel = data
		case "maxTimePerQuestion":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("maxTimePerQuestion"))
			data, err := ec.unmarshalOInt2ᚖint(ctx, v)
			if err != nil {
				return it, err
			}
			it.MaxTimePerQuestion = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputGetGamesByRatingInput(ctx context.Context, obj any) (models.GetGamesByRatingInput, error) {
	var it models.GetGamesByRatingInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"userId", "pageInfo", "ratingType"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "userId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("userId"))
			data, err := ec.unmarshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.UserID = data
		case "pageInfo":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("pageInfo"))
			data, err := ec.unmarshalOPageInfoInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPageInfoInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.PageInfo = data
		case "ratingType":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("ratingType"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.RatingType = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputGetGamesInput(ctx context.Context, obj any) (models.GetGamesInput, error) {
	var it models.GetGamesInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"userId", "timeRange", "pageInfo"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "userId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("userId"))
			data, err := ec.unmarshalOID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.UserID = data
		case "timeRange":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("timeRange"))
			data, err := ec.unmarshalOTimeRangeInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐTimeRangeInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.TimeRange = data
		case "pageInfo":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("pageInfo"))
			data, err := ec.unmarshalOPageInfoInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPageInfoInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.PageInfo = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputJoinGameInput(ctx context.Context, obj any) (models.JoinGameInput, error) {
	var it models.JoinGameInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"gameId"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "gameId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("gameId"))
			data, err := ec.unmarshalOID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.GameID = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputPageInfoInput(ctx context.Context, obj any) (models.PageInfoInput, error) {
	var it models.PageInfoInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"pageNumber", "rows"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "pageNumber":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("pageNumber"))
			data, err := ec.unmarshalOInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.PageNumber = data
		case "rows":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("rows"))
			data, err := ec.unmarshalOInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.Rows = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputStartGameInput(ctx context.Context, obj any) (models.StartGameInput, error) {
	var it models.StartGameInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"gameId"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "gameId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("gameId"))
			data, err := ec.unmarshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.GameID = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputSubmitAnswerInput(ctx context.Context, obj any) (models.SubmitAnswerInput, error) {
	var it models.SubmitAnswerInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"gameId", "questionId", "submittedValue", "isCorrect", "inCorrectAttempts", "timeOfSubmission"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "gameId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("gameId"))
			data, err := ec.unmarshalOID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.GameID = data
		case "questionId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("questionId"))
			data, err := ec.unmarshalOString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.QuestionID = data
		case "submittedValue":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("submittedValue"))
			data, err := ec.unmarshalOString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.SubmittedValue = data
		case "isCorrect":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("isCorrect"))
			data, err := ec.unmarshalOBoolean2bool(ctx, v)
			if err != nil {
				return it, err
			}
			it.IsCorrect = data
		case "inCorrectAttempts":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("inCorrectAttempts"))
			data, err := ec.unmarshalOInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.IncorrectAttempts = data
		case "timeOfSubmission":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("timeOfSubmission"))
			data, err := ec.unmarshalODate2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐDate(ctx, v)
			if err != nil {
				return it, err
			}
			it.TimeOfSubmission = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputSubmitFlashAnzanAnswerInput(ctx context.Context, obj any) (models.SubmitFlashAnzanAnswerInput, error) {
	var it models.SubmitFlashAnzanAnswerInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"gameId", "questionIdentifier", "submittedValue", "isCorrect", "timeOfSubmission", "maxScore"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "gameId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("gameId"))
			data, err := ec.unmarshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.GameID = data
		case "questionIdentifier":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("questionIdentifier"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.QuestionIdentifier = data
		case "submittedValue":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("submittedValue"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.SubmittedValue = data
		case "isCorrect":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("isCorrect"))
			data, err := ec.unmarshalOBoolean2ᚖbool(ctx, v)
			if err != nil {
				return it, err
			}
			it.IsCorrect = data
		case "timeOfSubmission":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("timeOfSubmission"))
			data, err := ec.unmarshalODate2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐDate(ctx, v)
			if err != nil {
				return it, err
			}
			it.TimeOfSubmission = data
		case "maxScore":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("maxScore"))
			data, err := ec.unmarshalOInt2ᚖint(ctx, v)
			if err != nil {
				return it, err
			}
			it.MaxScore = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputTimeRangeInput(ctx context.Context, obj any) (models.TimeRangeInput, error) {
	var it models.TimeRangeInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"startTime", "endTime"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "startTime":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("startTime"))
			data, err := ec.unmarshalOTime2timeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.StartTime = data
		case "endTime":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("endTime"))
			data, err := ec.unmarshalOTime2timeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.EndTime = data
		}
	}

	return it, nil
}

// endregion **************************** input.gotpl *****************************

// region    ************************** interface.gotpl ***************************

// endregion ************************** interface.gotpl ***************************

// region    **************************** object.gotpl ****************************

var challengeOutputImplementors = []string{"ChallengeOutput", "UserEvent"}

func (ec *executionContext) _ChallengeOutput(ctx context.Context, sel ast.SelectionSet, obj *models.ChallengeOutput) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, challengeOutputImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ChallengeOutput")
		case "gameId":
			out.Values[i] = ec._ChallengeOutput_gameId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "challengedBy":
			out.Values[i] = ec._ChallengeOutput_challengedBy(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "gameConfig":
			out.Values[i] = ec._ChallengeOutput_gameConfig(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "createdAt":
			out.Values[i] = ec._ChallengeOutput_createdAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "status":
			out.Values[i] = ec._ChallengeOutput_status(ctx, field, obj)
		case "opponent":
			out.Values[i] = ec._ChallengeOutput_opponent(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var gameImplementors = []string{"Game"}

func (ec *executionContext) _Game(ctx context.Context, sel ast.SelectionSet, obj *models.Game) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, gameImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("Game")
		case "_id":
			out.Values[i] = ec._Game__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "players":
			out.Values[i] = ec._Game_players(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "gameStatus":
			out.Values[i] = ec._Game_gameStatus(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "rematchRequestedBy":
			out.Values[i] = ec._Game_rematchRequestedBy(ctx, field, obj)
		case "gameType":
			out.Values[i] = ec._Game_gameType(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "createdBy":
			out.Values[i] = ec._Game_createdBy(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "config":
			out.Values[i] = ec._Game_config(ctx, field, obj)
		case "questions":
			out.Values[i] = ec._Game_questions(ctx, field, obj)
		case "minifiedQuestions":
			out.Values[i] = ec._Game_minifiedQuestions(ctx, field, obj)
		case "encryptedQuestions":
			out.Values[i] = ec._Game_encryptedQuestions(ctx, field, obj)
		case "leaderBoard":
			out.Values[i] = ec._Game_leaderBoard(ctx, field, obj)
		case "startTime":
			out.Values[i] = ec._Game_startTime(ctx, field, obj)
		case "endTime":
			out.Values[i] = ec._Game_endTime(ctx, field, obj)
		case "seriesId":
			out.Values[i] = ec._Game_seriesId(ctx, field, obj)
		case "showdownId":
			out.Values[i] = ec._Game_showdownId(ctx, field, obj)
		case "showdownGameConfig":
			out.Values[i] = ec._Game_showdownGameConfig(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var gameConfigImplementors = []string{"GameConfig"}

func (ec *executionContext) _GameConfig(ctx context.Context, sel ast.SelectionSet, obj *models.GameConfig) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, gameConfigImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GameConfig")
		case "timeLimit":
			out.Values[i] = ec._GameConfig_timeLimit(ctx, field, obj)
		case "numPlayers":
			out.Values[i] = ec._GameConfig_numPlayers(ctx, field, obj)
		case "gameType":
			out.Values[i] = ec._GameConfig_gameType(ctx, field, obj)
		case "questionTags":
			out.Values[i] = ec._GameConfig_questionTags(ctx, field, obj)
		case "difficultyLevel":
			out.Values[i] = ec._GameConfig_difficultyLevel(ctx, field, obj)
		case "maxTimePerQuestion":
			out.Values[i] = ec._GameConfig_maxTimePerQuestion(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var gameDetailedAnalysisImplementors = []string{"GameDetailedAnalysis"}

func (ec *executionContext) _GameDetailedAnalysis(ctx context.Context, sel ast.SelectionSet, obj *models.GameDetailedAnalysis) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, gameDetailedAnalysisImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GameDetailedAnalysis")
		case "game":
			out.Values[i] = ec._GameDetailedAnalysis_game(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "questions":
			out.Values[i] = ec._GameDetailedAnalysis_questions(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var gameQuestionImplementors = []string{"GameQuestion"}

func (ec *executionContext) _GameQuestion(ctx context.Context, sel ast.SelectionSet, obj *models.GameQuestion) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, gameQuestionImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GameQuestion")
		case "question":
			out.Values[i] = ec._GameQuestion_question(ctx, field, obj)
		case "submissions":
			out.Values[i] = ec._GameQuestion_submissions(ctx, field, obj)
		case "stats":
			out.Values[i] = ec._GameQuestion_stats(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var gameQuestionAnalysisImplementors = []string{"GameQuestionAnalysis"}

func (ec *executionContext) _GameQuestionAnalysis(ctx context.Context, sel ast.SelectionSet, obj *models.GameQuestionAnalysis) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, gameQuestionAnalysisImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GameQuestionAnalysis")
		case "question":
			out.Values[i] = ec._GameQuestionAnalysis_question(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "avgTimes":
			out.Values[i] = ec._GameQuestionAnalysis_avgTimes(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "globalAvgTime":
			out.Values[i] = ec._GameQuestionAnalysis_globalAvgTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "globalBestTime":
			out.Values[i] = ec._GameQuestionAnalysis_globalBestTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var gameQuestionStatsImplementors = []string{"GameQuestionStats"}

func (ec *executionContext) _GameQuestionStats(ctx context.Context, sel ast.SelectionSet, obj *models.GameQuestionStats) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, gameQuestionStatsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GameQuestionStats")
		case "fastestTime":
			out.Values[i] = ec._GameQuestionStats_fastestTime(ctx, field, obj)
		case "userIds":
			out.Values[i] = ec._GameQuestionStats_userIds(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var getGamesByRatingOutputImplementors = []string{"GetGamesByRatingOutput"}

func (ec *executionContext) _GetGamesByRatingOutput(ctx context.Context, sel ast.SelectionSet, obj *models.GetGamesByRatingOutput) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, getGamesByRatingOutputImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GetGamesByRatingOutput")
		case "games":
			out.Values[i] = ec._GetGamesByRatingOutput_games(ctx, field, obj)
		case "puzzleGames":
			out.Values[i] = ec._GetGamesByRatingOutput_puzzleGames(ctx, field, obj)
		case "users":
			out.Values[i] = ec._GetGamesByRatingOutput_users(ctx, field, obj)
		case "totalCount":
			out.Values[i] = ec._GetGamesByRatingOutput_totalCount(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var getGamesOutputImplementors = []string{"GetGamesOutput"}

func (ec *executionContext) _GetGamesOutput(ctx context.Context, sel ast.SelectionSet, obj *models.GetGamesOutput) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, getGamesOutputImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GetGamesOutput")
		case "games":
			out.Values[i] = ec._GetGamesOutput_games(ctx, field, obj)
		case "users":
			out.Values[i] = ec._GetGamesOutput_users(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var leaderBoardEntryImplementors = []string{"LeaderBoardEntry"}

func (ec *executionContext) _LeaderBoardEntry(ctx context.Context, sel ast.SelectionSet, obj *models.LeaderBoardEntry) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, leaderBoardEntryImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("LeaderBoardEntry")
		case "userId":
			out.Values[i] = ec._LeaderBoardEntry_userId(ctx, field, obj)
		case "correct":
			out.Values[i] = ec._LeaderBoardEntry_correct(ctx, field, obj)
		case "incorrect":
			out.Values[i] = ec._LeaderBoardEntry_incorrect(ctx, field, obj)
		case "totalPoints":
			out.Values[i] = ec._LeaderBoardEntry_totalPoints(ctx, field, obj)
		case "ratingChange":
			out.Values[i] = ec._LeaderBoardEntry_ratingChange(ctx, field, obj)
		case "statikCoinsEarned":
			out.Values[i] = ec._LeaderBoardEntry_statikCoinsEarned(ctx, field, obj)
		case "rank":
			out.Values[i] = ec._LeaderBoardEntry_rank(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var minifiedGameImplementors = []string{"MinifiedGame"}

func (ec *executionContext) _MinifiedGame(ctx context.Context, sel ast.SelectionSet, obj *models.MinifiedGame) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, minifiedGameImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("MinifiedGame")
		case "_id":
			out.Values[i] = ec._MinifiedGame__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "players":
			out.Values[i] = ec._MinifiedGame_players(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "config":
			out.Values[i] = ec._MinifiedGame_config(ctx, field, obj)
		case "leaderBoard":
			out.Values[i] = ec._MinifiedGame_leaderBoard(ctx, field, obj)
		case "startTime":
			out.Values[i] = ec._MinifiedGame_startTime(ctx, field, obj)
		case "endTime":
			out.Values[i] = ec._MinifiedGame_endTime(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var playerImplementors = []string{"Player"}

func (ec *executionContext) _Player(ctx context.Context, sel ast.SelectionSet, obj *models.Player) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, playerImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("Player")
		case "userId":
			out.Values[i] = ec._Player_userId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "rating":
			out.Values[i] = ec._Player_rating(ctx, field, obj)
		case "statikCoins":
			out.Values[i] = ec._Player_statikCoins(ctx, field, obj)
		case "status":
			out.Values[i] = ec._Player_status(ctx, field, obj)
		case "timeLeft":
			out.Values[i] = ec._Player_timeLeft(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var searchSubscriptionOutputImplementors = []string{"SearchSubscriptionOutput", "UserEvent"}

func (ec *executionContext) _SearchSubscriptionOutput(ctx context.Context, sel ast.SelectionSet, obj *models.SearchSubscriptionOutput) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, searchSubscriptionOutputImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("SearchSubscriptionOutput")
		case "game":
			out.Values[i] = ec._SearchSubscriptionOutput_game(ctx, field, obj)
		case "event":
			out.Values[i] = ec._SearchSubscriptionOutput_event(ctx, field, obj)
		case "opponent":
			out.Values[i] = ec._SearchSubscriptionOutput_opponent(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var submissionImplementors = []string{"Submission"}

func (ec *executionContext) _Submission(ctx context.Context, sel ast.SelectionSet, obj *models.Submission) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, submissionImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("Submission")
		case "userId":
			out.Values[i] = ec._Submission_userId(ctx, field, obj)
		case "timeTaken":
			out.Values[i] = ec._Submission_timeTaken(ctx, field, obj)
		case "points":
			out.Values[i] = ec._Submission_points(ctx, field, obj)
		case "submissionTime":
			out.Values[i] = ec._Submission_submissionTime(ctx, field, obj)
		case "isCorrect":
			out.Values[i] = ec._Submission_isCorrect(ctx, field, obj)
		case "inCorrectAttempts":
			out.Values[i] = ec._Submission_inCorrectAttempts(ctx, field, obj)
		case "submittedValues":
			out.Values[i] = ec._Submission_submittedValues(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var subscriptionOutputImplementors = []string{"SubscriptionOutput"}

func (ec *executionContext) _SubscriptionOutput(ctx context.Context, sel ast.SelectionSet, obj *models.SubscriptionOutput) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, subscriptionOutputImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("SubscriptionOutput")
		case "game":
			out.Values[i] = ec._SubscriptionOutput_game(ctx, field, obj)
		case "event":
			out.Values[i] = ec._SubscriptionOutput_event(ctx, field, obj)
		case "question":
			out.Values[i] = ec._SubscriptionOutput_question(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userAvgTimeImplementors = []string{"UserAvgTime"}

func (ec *executionContext) _UserAvgTime(ctx context.Context, sel ast.SelectionSet, obj *models.UserAvgTime) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userAvgTimeImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserAvgTime")
		case "userId":
			out.Values[i] = ec._UserAvgTime_userId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "questionAvgTime":
			out.Values[i] = ec._UserAvgTime_questionAvgTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "presetAvgTime":
			out.Values[i] = ec._UserAvgTime_presetAvgTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "presetBestTime":
			out.Values[i] = ec._UserAvgTime_presetBestTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

// endregion **************************** object.gotpl ****************************

// region    ***************************** type.gotpl *****************************

func (ec *executionContext) unmarshalNGAME_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameStatus(ctx context.Context, v any) (models.GameStatus, error) {
	var res models.GameStatus
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNGAME_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameStatus(ctx context.Context, sel ast.SelectionSet, v models.GameStatus) graphql.Marshaler {
	return v
}

func (ec *executionContext) unmarshalNGAME_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameType(ctx context.Context, v any) (models.GameType, error) {
	var res models.GameType
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNGAME_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameType(ctx context.Context, sel ast.SelectionSet, v models.GameType) graphql.Marshaler {
	return v
}

func (ec *executionContext) marshalNGame2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGame(ctx context.Context, sel ast.SelectionSet, v models.Game) graphql.Marshaler {
	return ec._Game(ctx, sel, &v)
}

func (ec *executionContext) marshalNGame2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGame(ctx context.Context, sel ast.SelectionSet, v *models.Game) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._Game(ctx, sel, v)
}

func (ec *executionContext) marshalNGameConfig2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameConfig(ctx context.Context, sel ast.SelectionSet, v *models.GameConfig) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._GameConfig(ctx, sel, v)
}

func (ec *executionContext) marshalNGameQuestion2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameQuestionᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.GameQuestion) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNGameQuestion2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameQuestion(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNGameQuestion2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameQuestion(ctx context.Context, sel ast.SelectionSet, v *models.GameQuestion) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._GameQuestion(ctx, sel, v)
}

func (ec *executionContext) marshalNGameQuestionAnalysis2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameQuestionAnalysisᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.GameQuestionAnalysis) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNGameQuestionAnalysis2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameQuestionAnalysis(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNGameQuestionAnalysis2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameQuestionAnalysis(ctx context.Context, sel ast.SelectionSet, v *models.GameQuestionAnalysis) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._GameQuestionAnalysis(ctx, sel, v)
}

func (ec *executionContext) marshalNPlayer2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPlayerᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.Player) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNPlayer2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPlayer(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNPlayer2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPlayer(ctx context.Context, sel ast.SelectionSet, v *models.Player) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._Player(ctx, sel, v)
}

func (ec *executionContext) marshalNUserAvgTime2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserAvgTimeᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.UserAvgTime) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNUserAvgTime2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserAvgTime(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNUserAvgTime2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserAvgTime(ctx context.Context, sel ast.SelectionSet, v *models.UserAvgTime) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._UserAvgTime(ctx, sel, v)
}

func (ec *executionContext) unmarshalOChallengeStatus2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐChallengeStatus(ctx context.Context, v any) (*models.ChallengeStatus, error) {
	if v == nil {
		return nil, nil
	}
	var res = new(models.ChallengeStatus)
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOChallengeStatus2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐChallengeStatus(ctx context.Context, sel ast.SelectionSet, v *models.ChallengeStatus) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return v
}

func (ec *executionContext) unmarshalOChallengeUserInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐChallengeUserInput(ctx context.Context, v any) (*models.ChallengeUserInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputChallengeUserInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) unmarshalOGAME_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameType(ctx context.Context, v any) (models.GameType, error) {
	var res models.GameType
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOGAME_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameType(ctx context.Context, sel ast.SelectionSet, v models.GameType) graphql.Marshaler {
	return v
}

func (ec *executionContext) marshalOGame2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGame(ctx context.Context, sel ast.SelectionSet, v *models.Game) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._Game(ctx, sel, v)
}

func (ec *executionContext) marshalOGameConfig2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameConfig(ctx context.Context, sel ast.SelectionSet, v *models.GameConfig) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._GameConfig(ctx, sel, v)
}

func (ec *executionContext) unmarshalOGameConfigInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameConfigInput(ctx context.Context, v any) (*models.GameConfigInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputGameConfigInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOGameDetailedAnalysis2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameDetailedAnalysis(ctx context.Context, sel ast.SelectionSet, v *models.GameDetailedAnalysis) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._GameDetailedAnalysis(ctx, sel, v)
}

func (ec *executionContext) marshalOGameQuestion2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameQuestion(ctx context.Context, sel ast.SelectionSet, v []*models.GameQuestion) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOGameQuestion2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameQuestion(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOGameQuestion2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameQuestion(ctx context.Context, sel ast.SelectionSet, v *models.GameQuestion) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._GameQuestion(ctx, sel, v)
}

func (ec *executionContext) marshalOGameQuestionStats2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameQuestionStats(ctx context.Context, sel ast.SelectionSet, v models.GameQuestionStats) graphql.Marshaler {
	return ec._GameQuestionStats(ctx, sel, &v)
}

func (ec *executionContext) unmarshalOGetGamesByRatingInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGetGamesByRatingInput(ctx context.Context, v any) (*models.GetGamesByRatingInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputGetGamesByRatingInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOGetGamesByRatingOutput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGetGamesByRatingOutput(ctx context.Context, sel ast.SelectionSet, v *models.GetGamesByRatingOutput) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._GetGamesByRatingOutput(ctx, sel, v)
}

func (ec *executionContext) unmarshalOGetGamesInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGetGamesInput(ctx context.Context, v any) (*models.GetGamesInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputGetGamesInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOGetGamesOutput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGetGamesOutput(ctx context.Context, sel ast.SelectionSet, v *models.GetGamesOutput) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._GetGamesOutput(ctx, sel, v)
}

func (ec *executionContext) unmarshalOJoinGameInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐJoinGameInput(ctx context.Context, v any) (*models.JoinGameInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputJoinGameInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOLeaderBoardEntry2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeaderBoardEntry(ctx context.Context, sel ast.SelectionSet, v []*models.LeaderBoardEntry) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOLeaderBoardEntry2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeaderBoardEntry(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOLeaderBoardEntry2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeaderBoardEntry(ctx context.Context, sel ast.SelectionSet, v *models.LeaderBoardEntry) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._LeaderBoardEntry(ctx, sel, v)
}

func (ec *executionContext) marshalOMinifiedGame2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMinifiedGame(ctx context.Context, sel ast.SelectionSet, v []*models.MinifiedGame) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOMinifiedGame2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMinifiedGame(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOMinifiedGame2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMinifiedGame(ctx context.Context, sel ast.SelectionSet, v *models.MinifiedGame) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._MinifiedGame(ctx, sel, v)
}

func (ec *executionContext) unmarshalOPLAYER_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPlayerStatus(ctx context.Context, v any) (models.PlayerStatus, error) {
	var res models.PlayerStatus
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOPLAYER_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPlayerStatus(ctx context.Context, sel ast.SelectionSet, v models.PlayerStatus) graphql.Marshaler {
	return v
}

func (ec *executionContext) unmarshalOPageInfoInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPageInfoInput(ctx context.Context, v any) (models.PageInfoInput, error) {
	res, err := ec.unmarshalInputPageInfoInput(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) unmarshalOPageInfoInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPageInfoInput(ctx context.Context, v any) (*models.PageInfoInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputPageInfoInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOSearchSubscriptionOutput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSearchSubscriptionOutput(ctx context.Context, sel ast.SelectionSet, v *models.SearchSubscriptionOutput) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._SearchSubscriptionOutput(ctx, sel, v)
}

func (ec *executionContext) unmarshalOStartGameInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐStartGameInput(ctx context.Context, v any) (*models.StartGameInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputStartGameInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOSubmission2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSubmission(ctx context.Context, sel ast.SelectionSet, v []*models.Submission) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOSubmission2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSubmission(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOSubmission2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSubmission(ctx context.Context, sel ast.SelectionSet, v *models.Submission) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._Submission(ctx, sel, v)
}

func (ec *executionContext) unmarshalOSubmitAnswerInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSubmitAnswerInput(ctx context.Context, v any) (*models.SubmitAnswerInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputSubmitAnswerInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) unmarshalOSubmitFlashAnzanAnswerInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSubmitFlashAnzanAnswerInput(ctx context.Context, v any) (*models.SubmitFlashAnzanAnswerInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputSubmitFlashAnzanAnswerInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOSubscriptionOutput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSubscriptionOutput(ctx context.Context, sel ast.SelectionSet, v *models.SubscriptionOutput) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._SubscriptionOutput(ctx, sel, v)
}

func (ec *executionContext) unmarshalOTimeRangeInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐTimeRangeInput(ctx context.Context, v any) (models.TimeRangeInput, error) {
	res, err := ec.unmarshalInputTimeRangeInput(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) unmarshalOTimeRangeInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐTimeRangeInput(ctx context.Context, v any) (*models.TimeRangeInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputTimeRangeInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

// endregion ***************************** type.gotpl *****************************
