// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"context"
	"errors"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/ast"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// region    ************************** generated!.gotpl **************************

// endregion ************************** generated!.gotpl **************************

// region    ***************************** args.gotpl *****************************

// endregion ***************************** args.gotpl *****************************

// region    ************************** directives.gotpl **************************

// endregion ************************** directives.gotpl **************************

// region    **************************** field.gotpl *****************************

func (ec *executionContext) _Attachment_type(ctx context.Context, field graphql.CollectedField, obj *models.Attachment) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Attachment_type(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Type, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.AttachmentType)
	fc.Result = res
	return ec.marshalNAttachmentType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAttachmentType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Attachment_type(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Attachment",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type AttachmentType does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Attachment_url(ctx context.Context, field graphql.CollectedField, obj *models.Attachment) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Attachment_url(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.URL, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Attachment_url(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Attachment",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CreateMessageInput_groupId(ctx context.Context, field graphql.CollectedField, obj *models.CreateMessageInput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CreateMessageInput_groupId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GroupID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CreateMessageInput_groupId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CreateMessageInput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CreateMessageInput_content(ctx context.Context, field graphql.CollectedField, obj *models.CreateMessageInput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CreateMessageInput_content(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Content, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CreateMessageInput_content(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CreateMessageInput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CreateMessageInput_attachment(ctx context.Context, field graphql.CollectedField, obj *models.CreateMessageInput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CreateMessageInput_attachment(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Attachment, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.Attachment)
	fc.Result = res
	return ec.marshalOAttachment2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAttachment(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CreateMessageInput_attachment(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CreateMessageInput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "type":
				return ec.fieldContext_Attachment_type(ctx, field)
			case "url":
				return ec.fieldContext_Attachment_url(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Attachment", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _CreateMessageInput_sender(ctx context.Context, field graphql.CollectedField, obj *models.CreateMessageInput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CreateMessageInput_sender(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Sender, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CreateMessageInput_sender(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CreateMessageInput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Message__id(ctx context.Context, field graphql.CollectedField, obj *models.Message) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Message__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Message__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Message",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Message_groupId(ctx context.Context, field graphql.CollectedField, obj *models.Message) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Message_groupId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GroupID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Message_groupId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Message",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Message_content(ctx context.Context, field graphql.CollectedField, obj *models.Message) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Message_content(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Content, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Message_content(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Message",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Message_attachment(ctx context.Context, field graphql.CollectedField, obj *models.Message) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Message_attachment(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Attachment, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.Attachment)
	fc.Result = res
	return ec.marshalOAttachment2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAttachment(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Message_attachment(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Message",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "type":
				return ec.fieldContext_Attachment_type(ctx, field)
			case "url":
				return ec.fieldContext_Attachment_url(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Attachment", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Message_sender(ctx context.Context, field graphql.CollectedField, obj *models.Message) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Message_sender(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Sender, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Message_sender(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Message",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Message_createdAt(ctx context.Context, field graphql.CollectedField, obj *models.Message) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Message_createdAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Message_createdAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Message",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Message_senderInfo(ctx context.Context, field graphql.CollectedField, obj *models.Message) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Message_senderInfo(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.SenderInfo, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.UserDetailsForMessage)
	fc.Result = res
	return ec.marshalOUserDetailsForMessage2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserDetailsForMessage(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Message_senderInfo(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Message",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserDetailsForMessage__id(ctx, field)
			case "name":
				return ec.fieldContext_UserDetailsForMessage_name(ctx, field)
			case "username":
				return ec.fieldContext_UserDetailsForMessage_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserDetailsForMessage_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserDetailsForMessage_rating(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserDetailsForMessage", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _MessageGroup__id(ctx context.Context, field graphql.CollectedField, obj *models.MessageGroup) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MessageGroup__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MessageGroup__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MessageGroup",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MessageGroup_groupName(ctx context.Context, field graphql.CollectedField, obj *models.MessageGroup) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MessageGroup_groupName(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GroupName, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MessageGroup_groupName(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MessageGroup",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MessageGroup_lastMessageRead(ctx context.Context, field graphql.CollectedField, obj *models.MessageGroup) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MessageGroup_lastMessageRead(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LastMessageRead, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.MessageRead)
	fc.Result = res
	return ec.marshalOMessageRead2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMessageRead(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MessageGroup_lastMessageRead(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MessageGroup",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "userId":
				return ec.fieldContext_MessageRead_userId(ctx, field)
			case "lastMessageRead":
				return ec.fieldContext_MessageRead_lastMessageRead(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type MessageRead", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _MessageGroup_members(ctx context.Context, field graphql.CollectedField, obj *models.MessageGroup) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MessageGroup_members(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Members, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2ᚕᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MessageGroup_members(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MessageGroup",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MessageGroup_alias(ctx context.Context, field graphql.CollectedField, obj *models.MessageGroup) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MessageGroup_alias(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Alias, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MessageGroup_alias(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MessageGroup",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MessageGroup_messages(ctx context.Context, field graphql.CollectedField, obj *models.MessageGroup) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MessageGroup_messages(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Messages, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.Message)
	fc.Result = res
	return ec.marshalOMessage2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMessage(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MessageGroup_messages(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MessageGroup",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_Message__id(ctx, field)
			case "groupId":
				return ec.fieldContext_Message_groupId(ctx, field)
			case "content":
				return ec.fieldContext_Message_content(ctx, field)
			case "attachment":
				return ec.fieldContext_Message_attachment(ctx, field)
			case "sender":
				return ec.fieldContext_Message_sender(ctx, field)
			case "createdAt":
				return ec.fieldContext_Message_createdAt(ctx, field)
			case "senderInfo":
				return ec.fieldContext_Message_senderInfo(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Message", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _MessageGroup_groupType(ctx context.Context, field graphql.CollectedField, obj *models.MessageGroup) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MessageGroup_groupType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GroupType, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.GroupType)
	fc.Result = res
	return ec.marshalNGroupType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGroupType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MessageGroup_groupType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MessageGroup",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type GroupType does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MessageGroup_createdAt(ctx context.Context, field graphql.CollectedField, obj *models.MessageGroup) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MessageGroup_createdAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MessageGroup_createdAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MessageGroup",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MessageGroup_updatedAt(ctx context.Context, field graphql.CollectedField, obj *models.MessageGroup) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MessageGroup_updatedAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UpdatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MessageGroup_updatedAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MessageGroup",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MessageGroup_lastMessage(ctx context.Context, field graphql.CollectedField, obj *models.MessageGroup) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MessageGroup_lastMessage(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LastMessage, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.Message)
	fc.Result = res
	return ec.marshalOMessage2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMessage(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MessageGroup_lastMessage(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MessageGroup",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_Message__id(ctx, field)
			case "groupId":
				return ec.fieldContext_Message_groupId(ctx, field)
			case "content":
				return ec.fieldContext_Message_content(ctx, field)
			case "attachment":
				return ec.fieldContext_Message_attachment(ctx, field)
			case "sender":
				return ec.fieldContext_Message_sender(ctx, field)
			case "createdAt":
				return ec.fieldContext_Message_createdAt(ctx, field)
			case "senderInfo":
				return ec.fieldContext_Message_senderInfo(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Message", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _MessageGroup_userInfoIfIndividual(ctx context.Context, field graphql.CollectedField, obj *models.MessageGroup) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MessageGroup_userInfoIfIndividual(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserInfoIfIndividual, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.UserDetailsForMessage)
	fc.Result = res
	return ec.marshalOUserDetailsForMessage2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserDetailsForMessage(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MessageGroup_userInfoIfIndividual(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MessageGroup",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserDetailsForMessage__id(ctx, field)
			case "name":
				return ec.fieldContext_UserDetailsForMessage_name(ctx, field)
			case "username":
				return ec.fieldContext_UserDetailsForMessage_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserDetailsForMessage_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserDetailsForMessage_rating(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserDetailsForMessage", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _MessageGroup_deepLinkRoute(ctx context.Context, field graphql.CollectedField, obj *models.MessageGroup) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MessageGroup_deepLinkRoute(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.DeepLinkRoute, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MessageGroup_deepLinkRoute(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MessageGroup",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MessageRead_userId(ctx context.Context, field graphql.CollectedField, obj *models.MessageRead) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MessageRead_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MessageRead_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MessageRead",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MessageRead_lastMessageRead(ctx context.Context, field graphql.CollectedField, obj *models.MessageRead) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MessageRead_lastMessageRead(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LastMessageRead, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MessageRead_lastMessageRead(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MessageRead",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PaginatedMessage_messages(ctx context.Context, field graphql.CollectedField, obj *models.PaginatedMessage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PaginatedMessage_messages(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Messages, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.Message)
	fc.Result = res
	return ec.marshalOMessage2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMessage(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PaginatedMessage_messages(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PaginatedMessage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_Message__id(ctx, field)
			case "groupId":
				return ec.fieldContext_Message_groupId(ctx, field)
			case "content":
				return ec.fieldContext_Message_content(ctx, field)
			case "attachment":
				return ec.fieldContext_Message_attachment(ctx, field)
			case "sender":
				return ec.fieldContext_Message_sender(ctx, field)
			case "createdAt":
				return ec.fieldContext_Message_createdAt(ctx, field)
			case "senderInfo":
				return ec.fieldContext_Message_senderInfo(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Message", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PaginatedMessage_lastMessageId(ctx context.Context, field graphql.CollectedField, obj *models.PaginatedMessage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PaginatedMessage_lastMessageId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LastMessageID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PaginatedMessage_lastMessageId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PaginatedMessage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PaginatedMessage_hasMore(ctx context.Context, field graphql.CollectedField, obj *models.PaginatedMessage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PaginatedMessage_hasMore(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasMore, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PaginatedMessage_hasMore(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PaginatedMessage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PaginatedMessageGroups_groups(ctx context.Context, field graphql.CollectedField, obj *models.PaginatedMessageGroups) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PaginatedMessageGroups_groups(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Groups, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.MessageGroup)
	fc.Result = res
	return ec.marshalOMessageGroup2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMessageGroup(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PaginatedMessageGroups_groups(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PaginatedMessageGroups",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_MessageGroup__id(ctx, field)
			case "groupName":
				return ec.fieldContext_MessageGroup_groupName(ctx, field)
			case "lastMessageRead":
				return ec.fieldContext_MessageGroup_lastMessageRead(ctx, field)
			case "members":
				return ec.fieldContext_MessageGroup_members(ctx, field)
			case "alias":
				return ec.fieldContext_MessageGroup_alias(ctx, field)
			case "messages":
				return ec.fieldContext_MessageGroup_messages(ctx, field)
			case "groupType":
				return ec.fieldContext_MessageGroup_groupType(ctx, field)
			case "createdAt":
				return ec.fieldContext_MessageGroup_createdAt(ctx, field)
			case "updatedAt":
				return ec.fieldContext_MessageGroup_updatedAt(ctx, field)
			case "lastMessage":
				return ec.fieldContext_MessageGroup_lastMessage(ctx, field)
			case "userInfoIfIndividual":
				return ec.fieldContext_MessageGroup_userInfoIfIndividual(ctx, field)
			case "deepLinkRoute":
				return ec.fieldContext_MessageGroup_deepLinkRoute(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type MessageGroup", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PaginatedMessageGroups_nextPage(ctx context.Context, field graphql.CollectedField, obj *models.PaginatedMessageGroups) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PaginatedMessageGroups_nextPage(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NextPage, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PaginatedMessageGroups_nextPage(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PaginatedMessageGroups",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PaginatedMessageGroups_hasMore(ctx context.Context, field graphql.CollectedField, obj *models.PaginatedMessageGroups) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PaginatedMessageGroups_hasMore(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasMore, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PaginatedMessageGroups_hasMore(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PaginatedMessageGroups",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PaginatedMessageGroups_isRead(ctx context.Context, field graphql.CollectedField, obj *models.PaginatedMessageGroups) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PaginatedMessageGroups_isRead(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsRead, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PaginatedMessageGroups_isRead(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PaginatedMessageGroups",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDetailsForMessage__id(ctx context.Context, field graphql.CollectedField, obj *models.UserDetailsForMessage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDetailsForMessage__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDetailsForMessage__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDetailsForMessage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDetailsForMessage_name(ctx context.Context, field graphql.CollectedField, obj *models.UserDetailsForMessage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDetailsForMessage_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDetailsForMessage_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDetailsForMessage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDetailsForMessage_username(ctx context.Context, field graphql.CollectedField, obj *models.UserDetailsForMessage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDetailsForMessage_username(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Username, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDetailsForMessage_username(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDetailsForMessage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDetailsForMessage_profileImageUrl(ctx context.Context, field graphql.CollectedField, obj *models.UserDetailsForMessage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDetailsForMessage_profileImageUrl(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ProfileImageURL, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDetailsForMessage_profileImageUrl(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDetailsForMessage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserDetailsForMessage_rating(ctx context.Context, field graphql.CollectedField, obj *models.UserDetailsForMessage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserDetailsForMessage_rating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserDetailsForMessage_rating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserDetailsForMessage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

// endregion **************************** field.gotpl *****************************

// region    **************************** input.gotpl *****************************

func (ec *executionContext) unmarshalInputGetAllMessageGroupsInput(ctx context.Context, obj any) (models.GetAllMessageGroupsInput, error) {
	var it models.GetAllMessageGroupsInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"page", "pageSize"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "page":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("page"))
			data, err := ec.unmarshalNInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.Page = data
		case "pageSize":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("pageSize"))
			data, err := ec.unmarshalOInt2ᚖint(ctx, v)
			if err != nil {
				return it, err
			}
			it.PageSize = data
		}
	}

	return it, nil
}

// endregion **************************** input.gotpl *****************************

// region    ************************** interface.gotpl ***************************

// endregion ************************** interface.gotpl ***************************

// region    **************************** object.gotpl ****************************

var attachmentImplementors = []string{"Attachment"}

func (ec *executionContext) _Attachment(ctx context.Context, sel ast.SelectionSet, obj *models.Attachment) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, attachmentImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("Attachment")
		case "type":
			out.Values[i] = ec._Attachment_type(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "url":
			out.Values[i] = ec._Attachment_url(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var createMessageInputImplementors = []string{"CreateMessageInput"}

func (ec *executionContext) _CreateMessageInput(ctx context.Context, sel ast.SelectionSet, obj *models.CreateMessageInput) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, createMessageInputImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("CreateMessageInput")
		case "groupId":
			out.Values[i] = ec._CreateMessageInput_groupId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "content":
			out.Values[i] = ec._CreateMessageInput_content(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "attachment":
			out.Values[i] = ec._CreateMessageInput_attachment(ctx, field, obj)
		case "sender":
			out.Values[i] = ec._CreateMessageInput_sender(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var messageImplementors = []string{"Message"}

func (ec *executionContext) _Message(ctx context.Context, sel ast.SelectionSet, obj *models.Message) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, messageImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("Message")
		case "_id":
			out.Values[i] = ec._Message__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "groupId":
			out.Values[i] = ec._Message_groupId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "content":
			out.Values[i] = ec._Message_content(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "attachment":
			out.Values[i] = ec._Message_attachment(ctx, field, obj)
		case "sender":
			out.Values[i] = ec._Message_sender(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "createdAt":
			out.Values[i] = ec._Message_createdAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "senderInfo":
			out.Values[i] = ec._Message_senderInfo(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var messageGroupImplementors = []string{"MessageGroup"}

func (ec *executionContext) _MessageGroup(ctx context.Context, sel ast.SelectionSet, obj *models.MessageGroup) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, messageGroupImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("MessageGroup")
		case "_id":
			out.Values[i] = ec._MessageGroup__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "groupName":
			out.Values[i] = ec._MessageGroup_groupName(ctx, field, obj)
		case "lastMessageRead":
			out.Values[i] = ec._MessageGroup_lastMessageRead(ctx, field, obj)
		case "members":
			out.Values[i] = ec._MessageGroup_members(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "alias":
			out.Values[i] = ec._MessageGroup_alias(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "messages":
			out.Values[i] = ec._MessageGroup_messages(ctx, field, obj)
		case "groupType":
			out.Values[i] = ec._MessageGroup_groupType(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "createdAt":
			out.Values[i] = ec._MessageGroup_createdAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "updatedAt":
			out.Values[i] = ec._MessageGroup_updatedAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "lastMessage":
			out.Values[i] = ec._MessageGroup_lastMessage(ctx, field, obj)
		case "userInfoIfIndividual":
			out.Values[i] = ec._MessageGroup_userInfoIfIndividual(ctx, field, obj)
		case "deepLinkRoute":
			out.Values[i] = ec._MessageGroup_deepLinkRoute(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var messageReadImplementors = []string{"MessageRead"}

func (ec *executionContext) _MessageRead(ctx context.Context, sel ast.SelectionSet, obj *models.MessageRead) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, messageReadImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("MessageRead")
		case "userId":
			out.Values[i] = ec._MessageRead_userId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "lastMessageRead":
			out.Values[i] = ec._MessageRead_lastMessageRead(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var paginatedMessageImplementors = []string{"PaginatedMessage"}

func (ec *executionContext) _PaginatedMessage(ctx context.Context, sel ast.SelectionSet, obj *models.PaginatedMessage) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, paginatedMessageImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PaginatedMessage")
		case "messages":
			out.Values[i] = ec._PaginatedMessage_messages(ctx, field, obj)
		case "lastMessageId":
			out.Values[i] = ec._PaginatedMessage_lastMessageId(ctx, field, obj)
		case "hasMore":
			out.Values[i] = ec._PaginatedMessage_hasMore(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var paginatedMessageGroupsImplementors = []string{"PaginatedMessageGroups"}

func (ec *executionContext) _PaginatedMessageGroups(ctx context.Context, sel ast.SelectionSet, obj *models.PaginatedMessageGroups) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, paginatedMessageGroupsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PaginatedMessageGroups")
		case "groups":
			out.Values[i] = ec._PaginatedMessageGroups_groups(ctx, field, obj)
		case "nextPage":
			out.Values[i] = ec._PaginatedMessageGroups_nextPage(ctx, field, obj)
		case "hasMore":
			out.Values[i] = ec._PaginatedMessageGroups_hasMore(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "isRead":
			out.Values[i] = ec._PaginatedMessageGroups_isRead(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userDetailsForMessageImplementors = []string{"UserDetailsForMessage"}

func (ec *executionContext) _UserDetailsForMessage(ctx context.Context, sel ast.SelectionSet, obj *models.UserDetailsForMessage) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userDetailsForMessageImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserDetailsForMessage")
		case "_id":
			out.Values[i] = ec._UserDetailsForMessage__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "name":
			out.Values[i] = ec._UserDetailsForMessage_name(ctx, field, obj)
		case "username":
			out.Values[i] = ec._UserDetailsForMessage_username(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "profileImageUrl":
			out.Values[i] = ec._UserDetailsForMessage_profileImageUrl(ctx, field, obj)
		case "rating":
			out.Values[i] = ec._UserDetailsForMessage_rating(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

// endregion **************************** object.gotpl ****************************

// region    ***************************** type.gotpl *****************************

func (ec *executionContext) unmarshalNAttachmentType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAttachmentType(ctx context.Context, v any) (models.AttachmentType, error) {
	var res models.AttachmentType
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNAttachmentType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAttachmentType(ctx context.Context, sel ast.SelectionSet, v models.AttachmentType) graphql.Marshaler {
	return v
}

func (ec *executionContext) unmarshalNGroupType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGroupType(ctx context.Context, v any) (models.GroupType, error) {
	var res models.GroupType
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNGroupType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGroupType(ctx context.Context, sel ast.SelectionSet, v models.GroupType) graphql.Marshaler {
	return v
}

func (ec *executionContext) marshalOAttachment2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAttachment(ctx context.Context, sel ast.SelectionSet, v []*models.Attachment) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOAttachment2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAttachment(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOAttachment2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAttachment(ctx context.Context, sel ast.SelectionSet, v *models.Attachment) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._Attachment(ctx, sel, v)
}

func (ec *executionContext) unmarshalOGetAllMessageGroupsInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGetAllMessageGroupsInput(ctx context.Context, v any) (*models.GetAllMessageGroupsInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputGetAllMessageGroupsInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOMessage2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMessage(ctx context.Context, sel ast.SelectionSet, v []*models.Message) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOMessage2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMessage(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOMessage2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMessage(ctx context.Context, sel ast.SelectionSet, v *models.Message) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._Message(ctx, sel, v)
}

func (ec *executionContext) marshalOMessageGroup2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMessageGroup(ctx context.Context, sel ast.SelectionSet, v []*models.MessageGroup) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOMessageGroup2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMessageGroup(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOMessageGroup2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMessageGroup(ctx context.Context, sel ast.SelectionSet, v *models.MessageGroup) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._MessageGroup(ctx, sel, v)
}

func (ec *executionContext) marshalOMessageRead2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMessageRead(ctx context.Context, sel ast.SelectionSet, v []*models.MessageRead) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOMessageRead2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMessageRead(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOMessageRead2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMessageRead(ctx context.Context, sel ast.SelectionSet, v *models.MessageRead) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._MessageRead(ctx, sel, v)
}

func (ec *executionContext) marshalOPaginatedMessage2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPaginatedMessage(ctx context.Context, sel ast.SelectionSet, v *models.PaginatedMessage) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._PaginatedMessage(ctx, sel, v)
}

func (ec *executionContext) marshalOPaginatedMessageGroups2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPaginatedMessageGroups(ctx context.Context, sel ast.SelectionSet, v *models.PaginatedMessageGroups) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._PaginatedMessageGroups(ctx, sel, v)
}

func (ec *executionContext) unmarshalOSortDirection2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSortDirection(ctx context.Context, v any) (*models.SortDirection, error) {
	if v == nil {
		return nil, nil
	}
	var res = new(models.SortDirection)
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOSortDirection2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSortDirection(ctx context.Context, sel ast.SelectionSet, v *models.SortDirection) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return v
}

func (ec *executionContext) marshalOUserDetailsForMessage2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserDetailsForMessage(ctx context.Context, sel ast.SelectionSet, v *models.UserDetailsForMessage) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._UserDetailsForMessage(ctx, sel, v)
}

// endregion ***************************** type.gotpl *****************************
