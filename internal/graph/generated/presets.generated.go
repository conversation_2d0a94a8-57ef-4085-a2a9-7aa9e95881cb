// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"context"
	"errors"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/ast"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// region    ************************** generated!.gotpl **************************

// endregion ************************** generated!.gotpl **************************

// region    ***************************** args.gotpl *****************************

// endregion ***************************** args.gotpl *****************************

// region    ************************** directives.gotpl **************************

// endregion ************************** directives.gotpl **************************

// region    **************************** field.gotpl *****************************

func (ec *executionContext) _AllPlayedPresetsOutput_presets(ctx context.Context, field graphql.CollectedField, obj *models.AllPlayedPresetsOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_AllPlayedPresetsOutput_presets(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Presets, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.PlayedPresets)
	fc.Result = res
	return ec.marshalOPlayedPresets2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPlayedPresets(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_AllPlayedPresetsOutput_presets(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "AllPlayedPresetsOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "identifier":
				return ec.fieldContext_PlayedPresets_identifier(ctx, field)
			case "avgTime":
				return ec.fieldContext_PlayedPresets_avgTime(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PlayedPresets", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _AllPlayedPresetsOutput_count(ctx context.Context, field graphql.CollectedField, obj *models.AllPlayedPresetsOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_AllPlayedPresetsOutput_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Count, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_AllPlayedPresetsOutput_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "AllPlayedPresetsOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GlobalPreset__id(ctx context.Context, field graphql.CollectedField, obj *models.GlobalPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GlobalPreset__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GlobalPreset__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GlobalPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GlobalPreset_identifier(ctx context.Context, field graphql.CollectedField, obj *models.GlobalPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GlobalPreset_identifier(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Identifier, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GlobalPreset_identifier(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GlobalPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GlobalPreset_globalAverageTime(ctx context.Context, field graphql.CollectedField, obj *models.GlobalPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GlobalPreset_globalAverageTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GlobalAverageTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalOFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GlobalPreset_globalAverageTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GlobalPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GlobalPreset_bestTime(ctx context.Context, field graphql.CollectedField, obj *models.GlobalPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GlobalPreset_bestTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.BestTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalOFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GlobalPreset_bestTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GlobalPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GlobalPreset_totalQuestionsSolved(ctx context.Context, field graphql.CollectedField, obj *models.GlobalPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GlobalPreset_totalQuestionsSolved(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalQuestionsSolved, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int64)
	fc.Result = res
	return ec.marshalOInt2int64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GlobalPreset_totalQuestionsSolved(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GlobalPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GlobalPreset_bestStreak(ctx context.Context, field graphql.CollectedField, obj *models.GlobalPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GlobalPreset_bestStreak(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.BestStreak, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GlobalPreset_bestStreak(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GlobalPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GlobalPreset_numOfCorrectSubmissions(ctx context.Context, field graphql.CollectedField, obj *models.GlobalPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GlobalPreset_numOfCorrectSubmissions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NumOfCorrectSubmissions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GlobalPreset_numOfCorrectSubmissions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GlobalPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GlobalPreset_globalAccuracy(ctx context.Context, field graphql.CollectedField, obj *models.GlobalPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GlobalPreset_globalAccuracy(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GlobalAccuracy, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalOFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GlobalPreset_globalAccuracy(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GlobalPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GlobalPreset_incorrectSubmissions(ctx context.Context, field graphql.CollectedField, obj *models.GlobalPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GlobalPreset_incorrectSubmissions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IncorrectSubmissions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GlobalPreset_incorrectSubmissions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GlobalPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GlobalPreset_top10Mathletes(ctx context.Context, field graphql.CollectedField, obj *models.GlobalPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GlobalPreset_top10Mathletes(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Top10Mathletes, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.Mathlete)
	fc.Result = res
	return ec.marshalOMathlete2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMathlete(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GlobalPreset_top10Mathletes(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GlobalPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "userId":
				return ec.fieldContext_Mathlete_userId(ctx, field)
			case "questionsSolved":
				return ec.fieldContext_Mathlete_questionsSolved(ctx, field)
			case "bestTime":
				return ec.fieldContext_Mathlete_bestTime(ctx, field)
			case "bestStreak":
				return ec.fieldContext_Mathlete_bestStreak(ctx, field)
			case "numOfCorrectSubmissions":
				return ec.fieldContext_Mathlete_numOfCorrectSubmissions(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Mathlete", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GlobalPresets_globalPresets(ctx context.Context, field graphql.CollectedField, obj *models.GlobalPresets) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GlobalPresets_globalPresets(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GlobalPresets, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.GlobalPreset)
	fc.Result = res
	return ec.marshalOGlobalPreset2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGlobalPreset(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GlobalPresets_globalPresets(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GlobalPresets",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_GlobalPreset__id(ctx, field)
			case "identifier":
				return ec.fieldContext_GlobalPreset_identifier(ctx, field)
			case "globalAverageTime":
				return ec.fieldContext_GlobalPreset_globalAverageTime(ctx, field)
			case "bestTime":
				return ec.fieldContext_GlobalPreset_bestTime(ctx, field)
			case "totalQuestionsSolved":
				return ec.fieldContext_GlobalPreset_totalQuestionsSolved(ctx, field)
			case "bestStreak":
				return ec.fieldContext_GlobalPreset_bestStreak(ctx, field)
			case "numOfCorrectSubmissions":
				return ec.fieldContext_GlobalPreset_numOfCorrectSubmissions(ctx, field)
			case "globalAccuracy":
				return ec.fieldContext_GlobalPreset_globalAccuracy(ctx, field)
			case "incorrectSubmissions":
				return ec.fieldContext_GlobalPreset_incorrectSubmissions(ctx, field)
			case "top10Mathletes":
				return ec.fieldContext_GlobalPreset_top10Mathletes(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type GlobalPreset", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GlobalPresets_totalCount(ctx context.Context, field graphql.CollectedField, obj *models.GlobalPresets) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GlobalPresets_totalCount(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalCount, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GlobalPresets_totalCount(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GlobalPresets",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Mathlete_userId(ctx context.Context, field graphql.CollectedField, obj *models.Mathlete) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Mathlete_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Mathlete_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Mathlete",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Mathlete_questionsSolved(ctx context.Context, field graphql.CollectedField, obj *models.Mathlete) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Mathlete_questionsSolved(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.QuestionsSolved, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Mathlete_questionsSolved(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Mathlete",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Mathlete_bestTime(ctx context.Context, field graphql.CollectedField, obj *models.Mathlete) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Mathlete_bestTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.BestTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalOFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Mathlete_bestTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Mathlete",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Mathlete_bestStreak(ctx context.Context, field graphql.CollectedField, obj *models.Mathlete) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Mathlete_bestStreak(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.BestStreak, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Mathlete_bestStreak(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Mathlete",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Mathlete_numOfCorrectSubmissions(ctx context.Context, field graphql.CollectedField, obj *models.Mathlete) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Mathlete_numOfCorrectSubmissions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NumOfCorrectSubmissions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Mathlete_numOfCorrectSubmissions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Mathlete",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PlayedPresets_identifier(ctx context.Context, field graphql.CollectedField, obj *models.PlayedPresets) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PlayedPresets_identifier(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Identifier, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PlayedPresets_identifier(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PlayedPresets",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PlayedPresets_avgTime(ctx context.Context, field graphql.CollectedField, obj *models.PlayedPresets) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PlayedPresets_avgTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.AvgTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*float64)
	fc.Result = res
	return ec.marshalOFloat2ᚖfloat64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PlayedPresets_avgTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PlayedPresets",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPreset__id(ctx context.Context, field graphql.CollectedField, obj *models.UserPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPreset__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPreset__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPreset_globalPresetId(ctx context.Context, field graphql.CollectedField, obj *models.UserPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPreset_globalPresetId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GlobalPresetID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPreset_globalPresetId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPreset_userId(ctx context.Context, field graphql.CollectedField, obj *models.UserPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPreset_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPreset_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPreset_identifier(ctx context.Context, field graphql.CollectedField, obj *models.UserPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPreset_identifier(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Identifier, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPreset_identifier(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPreset_name(ctx context.Context, field graphql.CollectedField, obj *models.UserPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPreset_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPreset_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPreset_questionsSolved(ctx context.Context, field graphql.CollectedField, obj *models.UserPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPreset_questionsSolved(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.QuestionsSolved, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int64)
	fc.Result = res
	return ec.marshalOInt2int64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPreset_questionsSolved(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPreset_curAvgTime(ctx context.Context, field graphql.CollectedField, obj *models.UserPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPreset_curAvgTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CurAvgTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalOFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPreset_curAvgTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPreset_curAvgAccuracy(ctx context.Context, field graphql.CollectedField, obj *models.UserPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPreset_curAvgAccuracy(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CurAvgAccuracy, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalOFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPreset_curAvgAccuracy(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPreset_incorrectSubmissions(ctx context.Context, field graphql.CollectedField, obj *models.UserPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPreset_incorrectSubmissions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IncorrectSubmissions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPreset_incorrectSubmissions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPreset_bestTime(ctx context.Context, field graphql.CollectedField, obj *models.UserPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPreset_bestTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.BestTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalOFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPreset_bestTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPreset_last10Time(ctx context.Context, field graphql.CollectedField, obj *models.UserPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPreset_last10Time(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Last10Time, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]int)
	fc.Result = res
	return ec.marshalOInt2ᚕint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPreset_last10Time(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPreset_last10IncorrectAttempts(ctx context.Context, field graphql.CollectedField, obj *models.UserPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPreset_last10IncorrectAttempts(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Last10IncorrectAttempts, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]int)
	fc.Result = res
	return ec.marshalOInt2ᚕint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPreset_last10IncorrectAttempts(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPreset_bestStreak(ctx context.Context, field graphql.CollectedField, obj *models.UserPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPreset_bestStreak(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.BestStreak, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPreset_bestStreak(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPreset_numOfCorrectSubmissions(ctx context.Context, field graphql.CollectedField, obj *models.UserPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPreset_numOfCorrectSubmissions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NumOfCorrectSubmissions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPreset_numOfCorrectSubmissions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPreset_saved(ctx context.Context, field graphql.CollectedField, obj *models.UserPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPreset_saved(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Saved, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalOBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPreset_saved(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPreset_savedConfig(ctx context.Context, field graphql.CollectedField, obj *models.UserPreset) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPreset_savedConfig(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.SavedConfig, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalOString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPreset_savedConfig(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPreset",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresetDayStats_date(ctx context.Context, field graphql.CollectedField, obj *models.UserPresetDayStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresetDayStats_date(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Date, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalOTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresetDayStats_date(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresetDayStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresetDayStats_userPresetStats(ctx context.Context, field graphql.CollectedField, obj *models.UserPresetDayStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresetDayStats_userPresetStats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserPresetStats, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.UserPresetStats)
	fc.Result = res
	return ec.marshalOUserPresetStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPresetStats(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresetDayStats_userPresetStats(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresetDayStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPresetStats__id(ctx, field)
			case "date":
				return ec.fieldContext_UserPresetStats_date(ctx, field)
			case "userPresetId":
				return ec.fieldContext_UserPresetStats_userPresetId(ctx, field)
			case "globalPresetId":
				return ec.fieldContext_UserPresetStats_globalPresetId(ctx, field)
			case "userId":
				return ec.fieldContext_UserPresetStats_userId(ctx, field)
			case "identifier":
				return ec.fieldContext_UserPresetStats_identifier(ctx, field)
			case "avgTime":
				return ec.fieldContext_UserPresetStats_avgTime(ctx, field)
			case "avgAccuracy":
				return ec.fieldContext_UserPresetStats_avgAccuracy(ctx, field)
			case "questionsSolved":
				return ec.fieldContext_UserPresetStats_questionsSolved(ctx, field)
			case "numOfCorrectSubmissions":
				return ec.fieldContext_UserPresetStats_numOfCorrectSubmissions(ctx, field)
			case "incorrectSubmissions":
				return ec.fieldContext_UserPresetStats_incorrectSubmissions(ctx, field)
			case "bestStreak":
				return ec.fieldContext_UserPresetStats_bestStreak(ctx, field)
			case "timePerformanceTrend":
				return ec.fieldContext_UserPresetStats_timePerformanceTrend(ctx, field)
			case "inaccuracyPerformanceTrend":
				return ec.fieldContext_UserPresetStats_inaccuracyPerformanceTrend(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPresetStats", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresetStats__id(ctx context.Context, field graphql.CollectedField, obj *models.UserPresetStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresetStats__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresetStats__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresetStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresetStats_date(ctx context.Context, field graphql.CollectedField, obj *models.UserPresetStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresetStats_date(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Date, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalOTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresetStats_date(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresetStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresetStats_userPresetId(ctx context.Context, field graphql.CollectedField, obj *models.UserPresetStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresetStats_userPresetId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserPresetID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresetStats_userPresetId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresetStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresetStats_globalPresetId(ctx context.Context, field graphql.CollectedField, obj *models.UserPresetStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresetStats_globalPresetId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GlobalPresetID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresetStats_globalPresetId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresetStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresetStats_userId(ctx context.Context, field graphql.CollectedField, obj *models.UserPresetStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresetStats_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresetStats_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresetStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresetStats_identifier(ctx context.Context, field graphql.CollectedField, obj *models.UserPresetStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresetStats_identifier(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Identifier, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresetStats_identifier(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresetStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresetStats_avgTime(ctx context.Context, field graphql.CollectedField, obj *models.UserPresetStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresetStats_avgTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.AvgTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalOFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresetStats_avgTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresetStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresetStats_avgAccuracy(ctx context.Context, field graphql.CollectedField, obj *models.UserPresetStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresetStats_avgAccuracy(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.AvgAccuracy, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalOFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresetStats_avgAccuracy(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresetStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresetStats_questionsSolved(ctx context.Context, field graphql.CollectedField, obj *models.UserPresetStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresetStats_questionsSolved(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.QuestionsSolved, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int64)
	fc.Result = res
	return ec.marshalOInt2int64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresetStats_questionsSolved(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresetStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresetStats_numOfCorrectSubmissions(ctx context.Context, field graphql.CollectedField, obj *models.UserPresetStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresetStats_numOfCorrectSubmissions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NumOfCorrectSubmissions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresetStats_numOfCorrectSubmissions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresetStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresetStats_incorrectSubmissions(ctx context.Context, field graphql.CollectedField, obj *models.UserPresetStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresetStats_incorrectSubmissions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IncorrectSubmissions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresetStats_incorrectSubmissions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresetStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresetStats_bestStreak(ctx context.Context, field graphql.CollectedField, obj *models.UserPresetStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresetStats_bestStreak(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.BestStreak, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalOInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresetStats_bestStreak(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresetStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresetStats_timePerformanceTrend(ctx context.Context, field graphql.CollectedField, obj *models.UserPresetStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresetStats_timePerformanceTrend(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TimePerformanceTrend, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]int)
	fc.Result = res
	return ec.marshalOInt2ᚕint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresetStats_timePerformanceTrend(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresetStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresetStats_inaccuracyPerformanceTrend(ctx context.Context, field graphql.CollectedField, obj *models.UserPresetStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresetStats_inaccuracyPerformanceTrend(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.InaccuracyPerformanceTrend, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]int)
	fc.Result = res
	return ec.marshalOInt2ᚕint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresetStats_inaccuracyPerformanceTrend(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresetStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresetStatsGraph_userStats(ctx context.Context, field graphql.CollectedField, obj *models.UserPresetStatsGraph) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresetStatsGraph_userStats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserStats, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.UserPresetDayStats)
	fc.Result = res
	return ec.marshalOUserPresetDayStats2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPresetDayStats(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresetStatsGraph_userStats(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresetStatsGraph",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "date":
				return ec.fieldContext_UserPresetDayStats_date(ctx, field)
			case "userPresetStats":
				return ec.fieldContext_UserPresetDayStats_userPresetStats(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPresetDayStats", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresetStatsGraph_globalStats(ctx context.Context, field graphql.CollectedField, obj *models.UserPresetStatsGraph) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresetStatsGraph_globalStats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GlobalStats, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.UserPresetDayStats)
	fc.Result = res
	return ec.marshalOUserPresetDayStats2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPresetDayStats(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresetStatsGraph_globalStats(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresetStatsGraph",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "date":
				return ec.fieldContext_UserPresetDayStats_date(ctx, field)
			case "userPresetStats":
				return ec.fieldContext_UserPresetDayStats_userPresetStats(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPresetDayStats", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresets_userPresets(ctx context.Context, field graphql.CollectedField, obj *models.UserPresets) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresets_userPresets(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserPresets, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.UserPreset)
	fc.Result = res
	return ec.marshalOUserPreset2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPreset(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresets_userPresets(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresets",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPreset__id(ctx, field)
			case "globalPresetId":
				return ec.fieldContext_UserPreset_globalPresetId(ctx, field)
			case "userId":
				return ec.fieldContext_UserPreset_userId(ctx, field)
			case "identifier":
				return ec.fieldContext_UserPreset_identifier(ctx, field)
			case "name":
				return ec.fieldContext_UserPreset_name(ctx, field)
			case "questionsSolved":
				return ec.fieldContext_UserPreset_questionsSolved(ctx, field)
			case "curAvgTime":
				return ec.fieldContext_UserPreset_curAvgTime(ctx, field)
			case "curAvgAccuracy":
				return ec.fieldContext_UserPreset_curAvgAccuracy(ctx, field)
			case "incorrectSubmissions":
				return ec.fieldContext_UserPreset_incorrectSubmissions(ctx, field)
			case "bestTime":
				return ec.fieldContext_UserPreset_bestTime(ctx, field)
			case "last10Time":
				return ec.fieldContext_UserPreset_last10Time(ctx, field)
			case "last10IncorrectAttempts":
				return ec.fieldContext_UserPreset_last10IncorrectAttempts(ctx, field)
			case "bestStreak":
				return ec.fieldContext_UserPreset_bestStreak(ctx, field)
			case "numOfCorrectSubmissions":
				return ec.fieldContext_UserPreset_numOfCorrectSubmissions(ctx, field)
			case "saved":
				return ec.fieldContext_UserPreset_saved(ctx, field)
			case "savedConfig":
				return ec.fieldContext_UserPreset_savedConfig(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPreset", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _UserPresets_totalCount(ctx context.Context, field graphql.CollectedField, obj *models.UserPresets) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_UserPresets_totalCount(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalCount, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_UserPresets_totalCount(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "UserPresets",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

// endregion **************************** field.gotpl *****************************

// region    **************************** input.gotpl *****************************

func (ec *executionContext) unmarshalInputUserPresetResult(ctx context.Context, obj any) (models.UserPresetResult, error) {
	var it models.UserPresetResult
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"identifier", "numOfQuestions", "submittedTimes", "incorrectAttempts", "bestStreak", "numOfCorrectSubmissions", "date", "savedConfig"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "identifier":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("identifier"))
			data, err := ec.unmarshalOString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Identifier = data
		case "numOfQuestions":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("numOfQuestions"))
			data, err := ec.unmarshalOInt2int64(ctx, v)
			if err != nil {
				return it, err
			}
			it.NumOfQuestions = data
		case "submittedTimes":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("submittedTimes"))
			data, err := ec.unmarshalOInt2ᚕint(ctx, v)
			if err != nil {
				return it, err
			}
			it.SubmittedTimes = data
		case "incorrectAttempts":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("incorrectAttempts"))
			data, err := ec.unmarshalOInt2ᚕint(ctx, v)
			if err != nil {
				return it, err
			}
			it.IncorrectAttempts = data
		case "bestStreak":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("bestStreak"))
			data, err := ec.unmarshalOInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.BestStreak = data
		case "numOfCorrectSubmissions":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("numOfCorrectSubmissions"))
			data, err := ec.unmarshalOInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.NumOfCorrectSubmissions = data
		case "date":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("date"))
			data, err := ec.unmarshalOTime2timeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.Date = data
		case "savedConfig":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("savedConfig"))
			data, err := ec.unmarshalOString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.SavedConfig = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputUserPresetResultInput(ctx context.Context, obj any) (models.UserPresetResultInput, error) {
	var it models.UserPresetResultInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"userPresetResults"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "userPresetResults":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("userPresetResults"))
			data, err := ec.unmarshalOUserPresetResult2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPresetResult(ctx, v)
			if err != nil {
				return it, err
			}
			it.UserPresetResults = data
		}
	}

	return it, nil
}

// endregion **************************** input.gotpl *****************************

// region    ************************** interface.gotpl ***************************

// endregion ************************** interface.gotpl ***************************

// region    **************************** object.gotpl ****************************

var allPlayedPresetsOutputImplementors = []string{"AllPlayedPresetsOutput"}

func (ec *executionContext) _AllPlayedPresetsOutput(ctx context.Context, sel ast.SelectionSet, obj *models.AllPlayedPresetsOutput) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, allPlayedPresetsOutputImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("AllPlayedPresetsOutput")
		case "presets":
			out.Values[i] = ec._AllPlayedPresetsOutput_presets(ctx, field, obj)
		case "count":
			out.Values[i] = ec._AllPlayedPresetsOutput_count(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var globalPresetImplementors = []string{"GlobalPreset"}

func (ec *executionContext) _GlobalPreset(ctx context.Context, sel ast.SelectionSet, obj *models.GlobalPreset) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, globalPresetImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GlobalPreset")
		case "_id":
			out.Values[i] = ec._GlobalPreset__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "identifier":
			out.Values[i] = ec._GlobalPreset_identifier(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "globalAverageTime":
			out.Values[i] = ec._GlobalPreset_globalAverageTime(ctx, field, obj)
		case "bestTime":
			out.Values[i] = ec._GlobalPreset_bestTime(ctx, field, obj)
		case "totalQuestionsSolved":
			out.Values[i] = ec._GlobalPreset_totalQuestionsSolved(ctx, field, obj)
		case "bestStreak":
			out.Values[i] = ec._GlobalPreset_bestStreak(ctx, field, obj)
		case "numOfCorrectSubmissions":
			out.Values[i] = ec._GlobalPreset_numOfCorrectSubmissions(ctx, field, obj)
		case "globalAccuracy":
			out.Values[i] = ec._GlobalPreset_globalAccuracy(ctx, field, obj)
		case "incorrectSubmissions":
			out.Values[i] = ec._GlobalPreset_incorrectSubmissions(ctx, field, obj)
		case "top10Mathletes":
			out.Values[i] = ec._GlobalPreset_top10Mathletes(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var globalPresetsImplementors = []string{"GlobalPresets"}

func (ec *executionContext) _GlobalPresets(ctx context.Context, sel ast.SelectionSet, obj *models.GlobalPresets) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, globalPresetsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GlobalPresets")
		case "globalPresets":
			out.Values[i] = ec._GlobalPresets_globalPresets(ctx, field, obj)
		case "totalCount":
			out.Values[i] = ec._GlobalPresets_totalCount(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var mathleteImplementors = []string{"Mathlete"}

func (ec *executionContext) _Mathlete(ctx context.Context, sel ast.SelectionSet, obj *models.Mathlete) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, mathleteImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("Mathlete")
		case "userId":
			out.Values[i] = ec._Mathlete_userId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "questionsSolved":
			out.Values[i] = ec._Mathlete_questionsSolved(ctx, field, obj)
		case "bestTime":
			out.Values[i] = ec._Mathlete_bestTime(ctx, field, obj)
		case "bestStreak":
			out.Values[i] = ec._Mathlete_bestStreak(ctx, field, obj)
		case "numOfCorrectSubmissions":
			out.Values[i] = ec._Mathlete_numOfCorrectSubmissions(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var playedPresetsImplementors = []string{"PlayedPresets"}

func (ec *executionContext) _PlayedPresets(ctx context.Context, sel ast.SelectionSet, obj *models.PlayedPresets) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, playedPresetsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PlayedPresets")
		case "identifier":
			out.Values[i] = ec._PlayedPresets_identifier(ctx, field, obj)
		case "avgTime":
			out.Values[i] = ec._PlayedPresets_avgTime(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userPresetImplementors = []string{"UserPreset"}

func (ec *executionContext) _UserPreset(ctx context.Context, sel ast.SelectionSet, obj *models.UserPreset) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userPresetImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserPreset")
		case "_id":
			out.Values[i] = ec._UserPreset__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "globalPresetId":
			out.Values[i] = ec._UserPreset_globalPresetId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userId":
			out.Values[i] = ec._UserPreset_userId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "identifier":
			out.Values[i] = ec._UserPreset_identifier(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "name":
			out.Values[i] = ec._UserPreset_name(ctx, field, obj)
		case "questionsSolved":
			out.Values[i] = ec._UserPreset_questionsSolved(ctx, field, obj)
		case "curAvgTime":
			out.Values[i] = ec._UserPreset_curAvgTime(ctx, field, obj)
		case "curAvgAccuracy":
			out.Values[i] = ec._UserPreset_curAvgAccuracy(ctx, field, obj)
		case "incorrectSubmissions":
			out.Values[i] = ec._UserPreset_incorrectSubmissions(ctx, field, obj)
		case "bestTime":
			out.Values[i] = ec._UserPreset_bestTime(ctx, field, obj)
		case "last10Time":
			out.Values[i] = ec._UserPreset_last10Time(ctx, field, obj)
		case "last10IncorrectAttempts":
			out.Values[i] = ec._UserPreset_last10IncorrectAttempts(ctx, field, obj)
		case "bestStreak":
			out.Values[i] = ec._UserPreset_bestStreak(ctx, field, obj)
		case "numOfCorrectSubmissions":
			out.Values[i] = ec._UserPreset_numOfCorrectSubmissions(ctx, field, obj)
		case "saved":
			out.Values[i] = ec._UserPreset_saved(ctx, field, obj)
		case "savedConfig":
			out.Values[i] = ec._UserPreset_savedConfig(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userPresetDayStatsImplementors = []string{"UserPresetDayStats"}

func (ec *executionContext) _UserPresetDayStats(ctx context.Context, sel ast.SelectionSet, obj *models.UserPresetDayStats) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userPresetDayStatsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserPresetDayStats")
		case "date":
			out.Values[i] = ec._UserPresetDayStats_date(ctx, field, obj)
		case "userPresetStats":
			out.Values[i] = ec._UserPresetDayStats_userPresetStats(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userPresetStatsImplementors = []string{"UserPresetStats"}

func (ec *executionContext) _UserPresetStats(ctx context.Context, sel ast.SelectionSet, obj *models.UserPresetStats) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userPresetStatsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserPresetStats")
		case "_id":
			out.Values[i] = ec._UserPresetStats__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "date":
			out.Values[i] = ec._UserPresetStats_date(ctx, field, obj)
		case "userPresetId":
			out.Values[i] = ec._UserPresetStats_userPresetId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "globalPresetId":
			out.Values[i] = ec._UserPresetStats_globalPresetId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userId":
			out.Values[i] = ec._UserPresetStats_userId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "identifier":
			out.Values[i] = ec._UserPresetStats_identifier(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "avgTime":
			out.Values[i] = ec._UserPresetStats_avgTime(ctx, field, obj)
		case "avgAccuracy":
			out.Values[i] = ec._UserPresetStats_avgAccuracy(ctx, field, obj)
		case "questionsSolved":
			out.Values[i] = ec._UserPresetStats_questionsSolved(ctx, field, obj)
		case "numOfCorrectSubmissions":
			out.Values[i] = ec._UserPresetStats_numOfCorrectSubmissions(ctx, field, obj)
		case "incorrectSubmissions":
			out.Values[i] = ec._UserPresetStats_incorrectSubmissions(ctx, field, obj)
		case "bestStreak":
			out.Values[i] = ec._UserPresetStats_bestStreak(ctx, field, obj)
		case "timePerformanceTrend":
			out.Values[i] = ec._UserPresetStats_timePerformanceTrend(ctx, field, obj)
		case "inaccuracyPerformanceTrend":
			out.Values[i] = ec._UserPresetStats_inaccuracyPerformanceTrend(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userPresetStatsGraphImplementors = []string{"UserPresetStatsGraph"}

func (ec *executionContext) _UserPresetStatsGraph(ctx context.Context, sel ast.SelectionSet, obj *models.UserPresetStatsGraph) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userPresetStatsGraphImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserPresetStatsGraph")
		case "userStats":
			out.Values[i] = ec._UserPresetStatsGraph_userStats(ctx, field, obj)
		case "globalStats":
			out.Values[i] = ec._UserPresetStatsGraph_globalStats(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var userPresetsImplementors = []string{"UserPresets"}

func (ec *executionContext) _UserPresets(ctx context.Context, sel ast.SelectionSet, obj *models.UserPresets) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, userPresetsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("UserPresets")
		case "userPresets":
			out.Values[i] = ec._UserPresets_userPresets(ctx, field, obj)
		case "totalCount":
			out.Values[i] = ec._UserPresets_totalCount(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

// endregion **************************** object.gotpl ****************************

// region    ***************************** type.gotpl *****************************

func (ec *executionContext) marshalOAllPlayedPresetsOutput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐAllPlayedPresetsOutput(ctx context.Context, sel ast.SelectionSet, v *models.AllPlayedPresetsOutput) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._AllPlayedPresetsOutput(ctx, sel, v)
}

func (ec *executionContext) marshalOGlobalPreset2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGlobalPreset(ctx context.Context, sel ast.SelectionSet, v []*models.GlobalPreset) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOGlobalPreset2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGlobalPreset(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOGlobalPreset2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGlobalPreset(ctx context.Context, sel ast.SelectionSet, v *models.GlobalPreset) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._GlobalPreset(ctx, sel, v)
}

func (ec *executionContext) marshalOGlobalPresets2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGlobalPresets(ctx context.Context, sel ast.SelectionSet, v *models.GlobalPresets) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._GlobalPresets(ctx, sel, v)
}

func (ec *executionContext) marshalOMathlete2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMathlete(ctx context.Context, sel ast.SelectionSet, v []*models.Mathlete) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOMathlete2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMathlete(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOMathlete2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMathlete(ctx context.Context, sel ast.SelectionSet, v *models.Mathlete) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._Mathlete(ctx, sel, v)
}

func (ec *executionContext) marshalOPlayedPresets2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPlayedPresets(ctx context.Context, sel ast.SelectionSet, v []*models.PlayedPresets) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOPlayedPresets2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPlayedPresets(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOPlayedPresets2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPlayedPresets(ctx context.Context, sel ast.SelectionSet, v *models.PlayedPresets) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._PlayedPresets(ctx, sel, v)
}

func (ec *executionContext) marshalOUserPreset2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPreset(ctx context.Context, sel ast.SelectionSet, v []*models.UserPreset) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOUserPreset2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPreset(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOUserPreset2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPreset(ctx context.Context, sel ast.SelectionSet, v *models.UserPreset) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._UserPreset(ctx, sel, v)
}

func (ec *executionContext) marshalOUserPresetDayStats2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPresetDayStats(ctx context.Context, sel ast.SelectionSet, v []*models.UserPresetDayStats) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOUserPresetDayStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPresetDayStats(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOUserPresetDayStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPresetDayStats(ctx context.Context, sel ast.SelectionSet, v *models.UserPresetDayStats) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._UserPresetDayStats(ctx, sel, v)
}

func (ec *executionContext) unmarshalOUserPresetResult2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPresetResult(ctx context.Context, v any) ([]*models.UserPresetResult, error) {
	if v == nil {
		return nil, nil
	}
	var vSlice []any
	vSlice = graphql.CoerceList(v)
	var err error
	res := make([]*models.UserPresetResult, len(vSlice))
	for i := range vSlice {
		ctx := graphql.WithPathContext(ctx, graphql.NewPathWithIndex(i))
		res[i], err = ec.unmarshalOUserPresetResult2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPresetResult(ctx, vSlice[i])
		if err != nil {
			return nil, err
		}
	}
	return res, nil
}

func (ec *executionContext) unmarshalOUserPresetResult2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPresetResult(ctx context.Context, v any) (*models.UserPresetResult, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputUserPresetResult(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) unmarshalOUserPresetResultInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPresetResultInput(ctx context.Context, v any) (*models.UserPresetResultInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputUserPresetResultInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOUserPresetStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPresetStats(ctx context.Context, sel ast.SelectionSet, v *models.UserPresetStats) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._UserPresetStats(ctx, sel, v)
}

func (ec *executionContext) marshalOUserPresets2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPresets(ctx context.Context, sel ast.SelectionSet, v *models.UserPresets) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._UserPresets(ctx, sel, v)
}

// endregion ***************************** type.gotpl *****************************
