// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"context"
	"errors"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/ast"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// region    ************************** generated!.gotpl **************************

// endregion ************************** generated!.gotpl **************************

// region    ***************************** args.gotpl *****************************

// endregion ***************************** args.gotpl *****************************

// region    ************************** directives.gotpl **************************

// endregion ************************** directives.gotpl **************************

// region    **************************** field.gotpl *****************************

func (ec *executionContext) _Cell_isVisible(ctx context.Context, field graphql.CollectedField, obj *models.Cell) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Cell_isVisible(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsVisible, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Cell_isVisible(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Cell",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Cell_value(ctx context.Context, field graphql.CollectedField, obj *models.Cell) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Cell_value(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Value, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Cell_value(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Cell",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Cell_type(ctx context.Context, field graphql.CollectedField, obj *models.Cell) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Cell_type(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Type, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.CellType)
	fc.Result = res
	return ec.marshalNCellType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCellType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Cell_type(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Cell",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type CellType does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CrossMathPuzzle_puzzleString(ctx context.Context, field graphql.CollectedField, obj *models.CrossMathPuzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CrossMathPuzzle_puzzleString(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PuzzleString, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CrossMathPuzzle_puzzleString(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CrossMathPuzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _HectocPuzzle_puzzleString(ctx context.Context, field graphql.CollectedField, obj *models.HectocPuzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_HectocPuzzle_puzzleString(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PuzzleString, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_HectocPuzzle_puzzleString(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "HectocPuzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _KenKenPuzzle_puzzleString(ctx context.Context, field graphql.CollectedField, obj *models.KenKenPuzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_KenKenPuzzle_puzzleString(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PuzzleString, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_KenKenPuzzle_puzzleString(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "KenKenPuzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_id(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_difficulty(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_difficulty(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Difficulty, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_difficulty(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_solvedBy(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_solvedBy(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.SolvedBy, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_solvedBy(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_cells(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_cells(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Cells, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([][]*models.Cell)
	fc.Result = res
	return ec.marshalNCell2ᚕᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCellᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_cells(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "isVisible":
				return ec.fieldContext_Cell_isVisible(ctx, field)
			case "value":
				return ec.fieldContext_Cell_value(ctx, field)
			case "type":
				return ec.fieldContext_Cell_type(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Cell", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_puzzleType(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_puzzleType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PuzzleType, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.PuzzleType)
	fc.Result = res
	return ec.marshalOPuzzleType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_puzzleType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type PuzzleType does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_puzzleDate(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_puzzleDate(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PuzzleDate, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_puzzleDate(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_availableAnswers(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_availableAnswers(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.AvailableAnswers, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]string)
	fc.Result = res
	return ec.marshalOString2ᚕstringᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_availableAnswers(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_hasAttempted(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_hasAttempted(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasAttempted, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_hasAttempted(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_currentUserResult(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_currentUserResult(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CurrentUserResult, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.PuzzleResult)
	fc.Result = res
	return ec.marshalOPuzzleResult2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleResult(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_currentUserResult(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_PuzzleResult_id(ctx, field)
			case "userId":
				return ec.fieldContext_PuzzleResult_userId(ctx, field)
			case "puzzleId":
				return ec.fieldContext_PuzzleResult_puzzleId(ctx, field)
			case "timeSpent":
				return ec.fieldContext_PuzzleResult_timeSpent(ctx, field)
			case "completedAt":
				return ec.fieldContext_PuzzleResult_completedAt(ctx, field)
			case "statikCoinsEarned":
				return ec.fieldContext_PuzzleResult_statikCoinsEarned(ctx, field)
			case "puzzleDate":
				return ec.fieldContext_PuzzleResult_puzzleDate(ctx, field)
			case "puzzleType":
				return ec.fieldContext_PuzzleResult_puzzleType(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PuzzleResult", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_stats(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_stats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Stats, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.PuzzleStats)
	fc.Result = res
	return ec.marshalOPuzzleStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleStats(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_stats(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "numOfSubmission":
				return ec.fieldContext_PuzzleStats_numOfSubmission(ctx, field)
			case "averageTime":
				return ec.fieldContext_PuzzleStats_averageTime(ctx, field)
			case "bestTime":
				return ec.fieldContext_PuzzleStats_bestTime(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PuzzleStats", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_userStat(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_userStat(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserStat, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.PuzzleUserStats)
	fc.Result = res
	return ec.marshalOPuzzleUserStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleUserStats(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_userStat(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_PuzzleUserStats_id(ctx, field)
			case "userId":
				return ec.fieldContext_PuzzleUserStats_userId(ctx, field)
			case "bestTime":
				return ec.fieldContext_PuzzleUserStats_bestTime(ctx, field)
			case "averageTime":
				return ec.fieldContext_PuzzleUserStats_averageTime(ctx, field)
			case "numOfSubmission":
				return ec.fieldContext_PuzzleUserStats_numOfSubmission(ctx, field)
			case "puzzleType":
				return ec.fieldContext_PuzzleUserStats_puzzleType(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PuzzleUserStats", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_typeSpecific(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_typeSpecific(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TypeSpecific, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.PuzzleTypeSpecificDetails)
	fc.Result = res
	return ec.marshalOPuzzleTypeSpecificDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleTypeSpecificDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_typeSpecific(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "puzzleType":
				return ec.fieldContext_PuzzleTypeSpecificDetails_puzzleType(ctx, field)
			case "crossMath":
				return ec.fieldContext_PuzzleTypeSpecificDetails_crossMath(ctx, field)
			case "kenKen":
				return ec.fieldContext_PuzzleTypeSpecificDetails_kenKen(ctx, field)
			case "hectoc":
				return ec.fieldContext_PuzzleTypeSpecificDetails_hectoc(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PuzzleTypeSpecificDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleMonthlySubmissionReport_yearMonth(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleMonthlySubmissionReport) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleMonthlySubmissionReport_yearMonth(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.YearMonth, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleMonthlySubmissionReport_yearMonth(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleMonthlySubmissionReport",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleMonthlySubmissionReport_puzzleSubmissions(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleMonthlySubmissionReport) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleMonthlySubmissionReport_puzzleSubmissions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PuzzleSubmissions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.PuzzleResult)
	fc.Result = res
	return ec.marshalOPuzzleResult2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleResultᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleMonthlySubmissionReport_puzzleSubmissions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleMonthlySubmissionReport",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_PuzzleResult_id(ctx, field)
			case "userId":
				return ec.fieldContext_PuzzleResult_userId(ctx, field)
			case "puzzleId":
				return ec.fieldContext_PuzzleResult_puzzleId(ctx, field)
			case "timeSpent":
				return ec.fieldContext_PuzzleResult_timeSpent(ctx, field)
			case "completedAt":
				return ec.fieldContext_PuzzleResult_completedAt(ctx, field)
			case "statikCoinsEarned":
				return ec.fieldContext_PuzzleResult_statikCoinsEarned(ctx, field)
			case "puzzleDate":
				return ec.fieldContext_PuzzleResult_puzzleDate(ctx, field)
			case "puzzleType":
				return ec.fieldContext_PuzzleResult_puzzleType(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PuzzleResult", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleResult_id(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleResult_id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleResult_id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleResult_userId(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleResult_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleResult_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleResult_puzzleId(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleResult_puzzleId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PuzzleID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleResult_puzzleId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleResult_timeSpent(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleResult_timeSpent(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TimeSpent, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleResult_timeSpent(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleResult_completedAt(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleResult_completedAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CompletedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleResult_completedAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleResult_statikCoinsEarned(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleResult_statikCoinsEarned(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StatikCoinsEarned, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleResult_statikCoinsEarned(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleResult_puzzleDate(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleResult_puzzleDate(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PuzzleDate, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleResult_puzzleDate(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleResult_puzzleType(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleResult) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleResult_puzzleType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PuzzleType, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.PuzzleType)
	fc.Result = res
	return ec.marshalOPuzzleType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleResult_puzzleType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleResult",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type PuzzleType does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleStats_numOfSubmission(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleStats_numOfSubmission(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NumOfSubmission, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleStats_numOfSubmission(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleStats_averageTime(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleStats_averageTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.AverageTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleStats_averageTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleStats_bestTime(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleStats_bestTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.BestTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleStats_bestTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleTypeSpecificDetails_puzzleType(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleTypeSpecificDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleTypeSpecificDetails_puzzleType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PuzzleType, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.PuzzleType)
	fc.Result = res
	return ec.marshalNPuzzleType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleTypeSpecificDetails_puzzleType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleTypeSpecificDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type PuzzleType does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleTypeSpecificDetails_crossMath(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleTypeSpecificDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleTypeSpecificDetails_crossMath(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CrossMath, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.CrossMathPuzzle)
	fc.Result = res
	return ec.marshalOCrossMathPuzzle2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCrossMathPuzzle(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleTypeSpecificDetails_crossMath(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleTypeSpecificDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "puzzleString":
				return ec.fieldContext_CrossMathPuzzle_puzzleString(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type CrossMathPuzzle", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleTypeSpecificDetails_kenKen(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleTypeSpecificDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleTypeSpecificDetails_kenKen(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.KenKen, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.KenKenPuzzle)
	fc.Result = res
	return ec.marshalOKenKenPuzzle2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐKenKenPuzzle(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleTypeSpecificDetails_kenKen(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleTypeSpecificDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "puzzleString":
				return ec.fieldContext_KenKenPuzzle_puzzleString(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type KenKenPuzzle", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleTypeSpecificDetails_hectoc(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleTypeSpecificDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleTypeSpecificDetails_hectoc(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Hectoc, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.HectocPuzzle)
	fc.Result = res
	return ec.marshalOHectocPuzzle2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐHectocPuzzle(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleTypeSpecificDetails_hectoc(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleTypeSpecificDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "puzzleString":
				return ec.fieldContext_HectocPuzzle_puzzleString(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type HectocPuzzle", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleUserStats_id(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleUserStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleUserStats_id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleUserStats_id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleUserStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleUserStats_userId(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleUserStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleUserStats_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleUserStats_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleUserStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleUserStats_bestTime(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleUserStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleUserStats_bestTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.BestTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleUserStats_bestTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleUserStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleUserStats_averageTime(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleUserStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleUserStats_averageTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.AverageTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleUserStats_averageTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleUserStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleUserStats_numOfSubmission(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleUserStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleUserStats_numOfSubmission(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NumOfSubmission, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleUserStats_numOfSubmission(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleUserStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleUserStats_puzzleType(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleUserStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleUserStats_puzzleType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PuzzleType, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.PuzzleType)
	fc.Result = res
	return ec.marshalOPuzzleType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleUserStats_puzzleType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleUserStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type PuzzleType does not have child fields")
		},
	}
	return fc, nil
}

// endregion **************************** field.gotpl *****************************

// region    **************************** input.gotpl *****************************

func (ec *executionContext) unmarshalInputCellInput(ctx context.Context, obj any) (models.CellInput, error) {
	var it models.CellInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"pos", "value"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "pos":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("pos"))
			data, err := ec.unmarshalNInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.Pos = data
		case "value":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("value"))
			data, err := ec.unmarshalNString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Value = data
		}
	}

	return it, nil
}

// endregion **************************** input.gotpl *****************************

// region    ************************** interface.gotpl ***************************

// endregion ************************** interface.gotpl ***************************

// region    **************************** object.gotpl ****************************

var cellImplementors = []string{"Cell"}

func (ec *executionContext) _Cell(ctx context.Context, sel ast.SelectionSet, obj *models.Cell) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, cellImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("Cell")
		case "isVisible":
			out.Values[i] = ec._Cell_isVisible(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "value":
			out.Values[i] = ec._Cell_value(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "type":
			out.Values[i] = ec._Cell_type(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var crossMathPuzzleImplementors = []string{"CrossMathPuzzle"}

func (ec *executionContext) _CrossMathPuzzle(ctx context.Context, sel ast.SelectionSet, obj *models.CrossMathPuzzle) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, crossMathPuzzleImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("CrossMathPuzzle")
		case "puzzleString":
			out.Values[i] = ec._CrossMathPuzzle_puzzleString(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var hectocPuzzleImplementors = []string{"HectocPuzzle"}

func (ec *executionContext) _HectocPuzzle(ctx context.Context, sel ast.SelectionSet, obj *models.HectocPuzzle) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, hectocPuzzleImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("HectocPuzzle")
		case "puzzleString":
			out.Values[i] = ec._HectocPuzzle_puzzleString(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var kenKenPuzzleImplementors = []string{"KenKenPuzzle"}

func (ec *executionContext) _KenKenPuzzle(ctx context.Context, sel ast.SelectionSet, obj *models.KenKenPuzzle) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, kenKenPuzzleImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("KenKenPuzzle")
		case "puzzleString":
			out.Values[i] = ec._KenKenPuzzle_puzzleString(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var puzzleImplementors = []string{"Puzzle"}

func (ec *executionContext) _Puzzle(ctx context.Context, sel ast.SelectionSet, obj *models.Puzzle) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, puzzleImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("Puzzle")
		case "id":
			out.Values[i] = ec._Puzzle_id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "difficulty":
			out.Values[i] = ec._Puzzle_difficulty(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "solvedBy":
			out.Values[i] = ec._Puzzle_solvedBy(ctx, field, obj)
		case "cells":
			out.Values[i] = ec._Puzzle_cells(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "puzzleType":
			out.Values[i] = ec._Puzzle_puzzleType(ctx, field, obj)
		case "puzzleDate":
			out.Values[i] = ec._Puzzle_puzzleDate(ctx, field, obj)
		case "availableAnswers":
			out.Values[i] = ec._Puzzle_availableAnswers(ctx, field, obj)
		case "hasAttempted":
			out.Values[i] = ec._Puzzle_hasAttempted(ctx, field, obj)
		case "currentUserResult":
			out.Values[i] = ec._Puzzle_currentUserResult(ctx, field, obj)
		case "stats":
			out.Values[i] = ec._Puzzle_stats(ctx, field, obj)
		case "userStat":
			out.Values[i] = ec._Puzzle_userStat(ctx, field, obj)
		case "typeSpecific":
			out.Values[i] = ec._Puzzle_typeSpecific(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var puzzleMonthlySubmissionReportImplementors = []string{"PuzzleMonthlySubmissionReport"}

func (ec *executionContext) _PuzzleMonthlySubmissionReport(ctx context.Context, sel ast.SelectionSet, obj *models.PuzzleMonthlySubmissionReport) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, puzzleMonthlySubmissionReportImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PuzzleMonthlySubmissionReport")
		case "yearMonth":
			out.Values[i] = ec._PuzzleMonthlySubmissionReport_yearMonth(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "puzzleSubmissions":
			out.Values[i] = ec._PuzzleMonthlySubmissionReport_puzzleSubmissions(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var puzzleResultImplementors = []string{"PuzzleResult"}

func (ec *executionContext) _PuzzleResult(ctx context.Context, sel ast.SelectionSet, obj *models.PuzzleResult) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, puzzleResultImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PuzzleResult")
		case "id":
			out.Values[i] = ec._PuzzleResult_id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userId":
			out.Values[i] = ec._PuzzleResult_userId(ctx, field, obj)
		case "puzzleId":
			out.Values[i] = ec._PuzzleResult_puzzleId(ctx, field, obj)
		case "timeSpent":
			out.Values[i] = ec._PuzzleResult_timeSpent(ctx, field, obj)
		case "completedAt":
			out.Values[i] = ec._PuzzleResult_completedAt(ctx, field, obj)
		case "statikCoinsEarned":
			out.Values[i] = ec._PuzzleResult_statikCoinsEarned(ctx, field, obj)
		case "puzzleDate":
			out.Values[i] = ec._PuzzleResult_puzzleDate(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "puzzleType":
			out.Values[i] = ec._PuzzleResult_puzzleType(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var puzzleStatsImplementors = []string{"PuzzleStats"}

func (ec *executionContext) _PuzzleStats(ctx context.Context, sel ast.SelectionSet, obj *models.PuzzleStats) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, puzzleStatsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PuzzleStats")
		case "numOfSubmission":
			out.Values[i] = ec._PuzzleStats_numOfSubmission(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "averageTime":
			out.Values[i] = ec._PuzzleStats_averageTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "bestTime":
			out.Values[i] = ec._PuzzleStats_bestTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var puzzleTypeSpecificDetailsImplementors = []string{"PuzzleTypeSpecificDetails"}

func (ec *executionContext) _PuzzleTypeSpecificDetails(ctx context.Context, sel ast.SelectionSet, obj *models.PuzzleTypeSpecificDetails) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, puzzleTypeSpecificDetailsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PuzzleTypeSpecificDetails")
		case "puzzleType":
			out.Values[i] = ec._PuzzleTypeSpecificDetails_puzzleType(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "crossMath":
			out.Values[i] = ec._PuzzleTypeSpecificDetails_crossMath(ctx, field, obj)
		case "kenKen":
			out.Values[i] = ec._PuzzleTypeSpecificDetails_kenKen(ctx, field, obj)
		case "hectoc":
			out.Values[i] = ec._PuzzleTypeSpecificDetails_hectoc(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var puzzleUserStatsImplementors = []string{"PuzzleUserStats"}

func (ec *executionContext) _PuzzleUserStats(ctx context.Context, sel ast.SelectionSet, obj *models.PuzzleUserStats) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, puzzleUserStatsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PuzzleUserStats")
		case "id":
			out.Values[i] = ec._PuzzleUserStats_id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userId":
			out.Values[i] = ec._PuzzleUserStats_userId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "bestTime":
			out.Values[i] = ec._PuzzleUserStats_bestTime(ctx, field, obj)
		case "averageTime":
			out.Values[i] = ec._PuzzleUserStats_averageTime(ctx, field, obj)
		case "numOfSubmission":
			out.Values[i] = ec._PuzzleUserStats_numOfSubmission(ctx, field, obj)
		case "puzzleType":
			out.Values[i] = ec._PuzzleUserStats_puzzleType(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

// endregion **************************** object.gotpl ****************************

// region    ***************************** type.gotpl *****************************

func (ec *executionContext) marshalNCell2ᚕᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCellᚄ(ctx context.Context, sel ast.SelectionSet, v [][]*models.Cell) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNCell2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCellᚄ(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNCell2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCellᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.Cell) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNCell2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCell(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNCell2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCell(ctx context.Context, sel ast.SelectionSet, v *models.Cell) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._Cell(ctx, sel, v)
}

func (ec *executionContext) unmarshalNCellType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCellType(ctx context.Context, v any) (models.CellType, error) {
	var res models.CellType
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNCellType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCellType(ctx context.Context, sel ast.SelectionSet, v models.CellType) graphql.Marshaler {
	return v
}

func (ec *executionContext) marshalNPuzzleMonthlySubmissionReport2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleMonthlySubmissionReport(ctx context.Context, sel ast.SelectionSet, v *models.PuzzleMonthlySubmissionReport) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._PuzzleMonthlySubmissionReport(ctx, sel, v)
}

func (ec *executionContext) marshalNPuzzleResult2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleResult(ctx context.Context, sel ast.SelectionSet, v models.PuzzleResult) graphql.Marshaler {
	return ec._PuzzleResult(ctx, sel, &v)
}

func (ec *executionContext) marshalNPuzzleResult2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleResult(ctx context.Context, sel ast.SelectionSet, v *models.PuzzleResult) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._PuzzleResult(ctx, sel, v)
}

func (ec *executionContext) unmarshalNPuzzleType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleType(ctx context.Context, v any) (models.PuzzleType, error) {
	var res models.PuzzleType
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNPuzzleType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleType(ctx context.Context, sel ast.SelectionSet, v models.PuzzleType) graphql.Marshaler {
	return v
}

func (ec *executionContext) marshalNPuzzleUserStats2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleUserStats(ctx context.Context, sel ast.SelectionSet, v models.PuzzleUserStats) graphql.Marshaler {
	return ec._PuzzleUserStats(ctx, sel, &v)
}

func (ec *executionContext) marshalNPuzzleUserStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleUserStats(ctx context.Context, sel ast.SelectionSet, v *models.PuzzleUserStats) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._PuzzleUserStats(ctx, sel, v)
}

func (ec *executionContext) marshalOCrossMathPuzzle2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCrossMathPuzzle(ctx context.Context, sel ast.SelectionSet, v *models.CrossMathPuzzle) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._CrossMathPuzzle(ctx, sel, v)
}

func (ec *executionContext) marshalOHectocPuzzle2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐHectocPuzzle(ctx context.Context, sel ast.SelectionSet, v *models.HectocPuzzle) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._HectocPuzzle(ctx, sel, v)
}

func (ec *executionContext) marshalOKenKenPuzzle2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐKenKenPuzzle(ctx context.Context, sel ast.SelectionSet, v *models.KenKenPuzzle) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._KenKenPuzzle(ctx, sel, v)
}

func (ec *executionContext) marshalOPuzzle2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzle(ctx context.Context, sel ast.SelectionSet, v *models.Puzzle) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._Puzzle(ctx, sel, v)
}

func (ec *executionContext) marshalOPuzzleMonthlySubmissionReport2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleMonthlySubmissionReportᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.PuzzleMonthlySubmissionReport) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNPuzzleMonthlySubmissionReport2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleMonthlySubmissionReport(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalOPuzzleResult2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleResultᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.PuzzleResult) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNPuzzleResult2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleResult(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalOPuzzleResult2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleResult(ctx context.Context, sel ast.SelectionSet, v *models.PuzzleResult) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._PuzzleResult(ctx, sel, v)
}

func (ec *executionContext) marshalOPuzzleStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleStats(ctx context.Context, sel ast.SelectionSet, v *models.PuzzleStats) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._PuzzleStats(ctx, sel, v)
}

func (ec *executionContext) unmarshalOPuzzleType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleType(ctx context.Context, v any) (*models.PuzzleType, error) {
	if v == nil {
		return nil, nil
	}
	var res = new(models.PuzzleType)
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOPuzzleType2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleType(ctx context.Context, sel ast.SelectionSet, v *models.PuzzleType) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return v
}

func (ec *executionContext) marshalOPuzzleTypeSpecificDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleTypeSpecificDetails(ctx context.Context, sel ast.SelectionSet, v *models.PuzzleTypeSpecificDetails) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._PuzzleTypeSpecificDetails(ctx, sel, v)
}

func (ec *executionContext) marshalOPuzzleUserStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleUserStats(ctx context.Context, sel ast.SelectionSet, v *models.PuzzleUserStats) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._PuzzleUserStats(ctx, sel, v)
}

// endregion ***************************** type.gotpl *****************************
