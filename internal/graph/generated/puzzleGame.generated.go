// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"context"
	"errors"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/ast"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// region    ************************** generated!.gotpl **************************

// endregion ************************** generated!.gotpl **************************

// region    ***************************** args.gotpl *****************************

// endregion ***************************** args.gotpl *****************************

// region    ************************** directives.gotpl **************************

// endregion ************************** directives.gotpl **************************

// region    **************************** field.gotpl *****************************

func (ec *executionContext) _CrossMathPuzzleRush__id(ctx context.Context, field graphql.CollectedField, obj *models.CrossMathPuzzleRush) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CrossMathPuzzleRush__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CrossMathPuzzleRush__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CrossMathPuzzleRush",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CrossMathPuzzleRush_userId(ctx context.Context, field graphql.CollectedField, obj *models.CrossMathPuzzleRush) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CrossMathPuzzleRush_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CrossMathPuzzleRush_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CrossMathPuzzleRush",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CrossMathPuzzleRush_bestAllTime(ctx context.Context, field graphql.CollectedField, obj *models.CrossMathPuzzleRush) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CrossMathPuzzleRush_bestAllTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.BestAllTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CrossMathPuzzleRush_bestAllTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CrossMathPuzzleRush",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CrossMathPuzzleRush_isNewBestScore(ctx context.Context, field graphql.CollectedField, obj *models.CrossMathPuzzleRush) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CrossMathPuzzleRush_isNewBestScore(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsNewBestScore, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CrossMathPuzzleRush_isNewBestScore(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CrossMathPuzzleRush",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CrossMathPuzzleRush_createdAt(ctx context.Context, field graphql.CollectedField, obj *models.CrossMathPuzzleRush) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CrossMathPuzzleRush_createdAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CrossMathPuzzleRush_createdAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CrossMathPuzzleRush",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CrossMathPuzzleRush_updatedAt(ctx context.Context, field graphql.CollectedField, obj *models.CrossMathPuzzleRush) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CrossMathPuzzleRush_updatedAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UpdatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CrossMathPuzzleRush_updatedAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CrossMathPuzzleRush",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CrossMathPuzzleRushPlayerInfo_rank(ctx context.Context, field graphql.CollectedField, obj *models.CrossMathPuzzleRushPlayerInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CrossMathPuzzleRushPlayerInfo_rank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CrossMathPuzzleRushPlayerInfo_rank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CrossMathPuzzleRushPlayerInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CrossMathPuzzleRushPlayerInfo_score(ctx context.Context, field graphql.CollectedField, obj *models.CrossMathPuzzleRushPlayerInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CrossMathPuzzleRushPlayerInfo_score(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Score, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CrossMathPuzzleRushPlayerInfo_score(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CrossMathPuzzleRushPlayerInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CrossMathPuzzleRushPlayerInfo_userInfo(ctx context.Context, field graphql.CollectedField, obj *models.CrossMathPuzzleRushPlayerInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CrossMathPuzzleRushPlayerInfo_userInfo(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserInfo, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.UserPublicDetails)
	fc.Result = res
	return ec.marshalOUserPublicDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CrossMathPuzzleRushPlayerInfo_userInfo(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CrossMathPuzzleRushPlayerInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPublicDetails__id(ctx, field)
			case "name":
				return ec.fieldContext_UserPublicDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_UserPublicDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserPublicDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserPublicDetails_rating(ctx, field)
			case "badge":
				return ec.fieldContext_UserPublicDetails_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_UserPublicDetails_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_UserPublicDetails_isGuest(ctx, field)
			case "globalRank":
				return ec.fieldContext_UserPublicDetails_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_UserPublicDetails_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_UserPublicDetails_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_UserPublicDetails_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_UserPublicDetails_stats(ctx, field)
			case "userStreaks":
				return ec.fieldContext_UserPublicDetails_userStreaks(ctx, field)
			case "ratingV2":
				return ec.fieldContext_UserPublicDetails_ratingV2(ctx, field)
			case "institutionId":
				return ec.fieldContext_UserPublicDetails_institutionId(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPublicDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _CrossMathPuzzleRushStats_bestAllTime(ctx context.Context, field graphql.CollectedField, obj *models.CrossMathPuzzleRushStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CrossMathPuzzleRushStats_bestAllTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.BestAllTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CrossMathPuzzleRushStats_bestAllTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CrossMathPuzzleRushStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CrossMathPuzzleRushStats_globalRank(ctx context.Context, field graphql.CollectedField, obj *models.CrossMathPuzzleRushStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CrossMathPuzzleRushStats_globalRank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GlobalRank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CrossMathPuzzleRushStats_globalRank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CrossMathPuzzleRushStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CrossMathPuzzleRushStats_friendsRank(ctx context.Context, field graphql.CollectedField, obj *models.CrossMathPuzzleRushStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CrossMathPuzzleRushStats_friendsRank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.FriendsRank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CrossMathPuzzleRushStats_friendsRank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CrossMathPuzzleRushStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GetPuzzleGamesOutput_games(ctx context.Context, field graphql.CollectedField, obj *models.GetPuzzleGamesOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GetPuzzleGamesOutput_games(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Games, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.MinifiedPuzzleGame)
	fc.Result = res
	return ec.marshalOMinifiedPuzzleGame2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMinifiedPuzzleGame(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GetPuzzleGamesOutput_games(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GetPuzzleGamesOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_MinifiedPuzzleGame__id(ctx, field)
			case "players":
				return ec.fieldContext_MinifiedPuzzleGame_players(ctx, field)
			case "config":
				return ec.fieldContext_MinifiedPuzzleGame_config(ctx, field)
			case "leaderBoard":
				return ec.fieldContext_MinifiedPuzzleGame_leaderBoard(ctx, field)
			case "startTime":
				return ec.fieldContext_MinifiedPuzzleGame_startTime(ctx, field)
			case "endTime":
				return ec.fieldContext_MinifiedPuzzleGame_endTime(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type MinifiedPuzzleGame", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GetPuzzleGamesOutput_users(ctx context.Context, field graphql.CollectedField, obj *models.GetPuzzleGamesOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GetPuzzleGamesOutput_users(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Users, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.UserPublicDetails)
	fc.Result = res
	return ec.marshalOUserPublicDetails2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GetPuzzleGamesOutput_users(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GetPuzzleGamesOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPublicDetails__id(ctx, field)
			case "name":
				return ec.fieldContext_UserPublicDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_UserPublicDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserPublicDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserPublicDetails_rating(ctx, field)
			case "badge":
				return ec.fieldContext_UserPublicDetails_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_UserPublicDetails_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_UserPublicDetails_isGuest(ctx, field)
			case "globalRank":
				return ec.fieldContext_UserPublicDetails_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_UserPublicDetails_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_UserPublicDetails_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_UserPublicDetails_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_UserPublicDetails_stats(ctx, field)
			case "userStreaks":
				return ec.fieldContext_UserPublicDetails_userStreaks(ctx, field)
			case "ratingV2":
				return ec.fieldContext_UserPublicDetails_ratingV2(ctx, field)
			case "institutionId":
				return ec.fieldContext_UserPublicDetails_institutionId(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPublicDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _MinifiedPuzzleGame__id(ctx context.Context, field graphql.CollectedField, obj *models.MinifiedPuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MinifiedPuzzleGame__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MinifiedPuzzleGame__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MinifiedPuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MinifiedPuzzleGame_players(ctx context.Context, field graphql.CollectedField, obj *models.MinifiedPuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MinifiedPuzzleGame_players(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Players, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.Player)
	fc.Result = res
	return ec.marshalNPlayer2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPlayerᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MinifiedPuzzleGame_players(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MinifiedPuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "userId":
				return ec.fieldContext_Player_userId(ctx, field)
			case "rating":
				return ec.fieldContext_Player_rating(ctx, field)
			case "statikCoins":
				return ec.fieldContext_Player_statikCoins(ctx, field)
			case "status":
				return ec.fieldContext_Player_status(ctx, field)
			case "timeLeft":
				return ec.fieldContext_Player_timeLeft(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Player", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _MinifiedPuzzleGame_config(ctx context.Context, field graphql.CollectedField, obj *models.MinifiedPuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MinifiedPuzzleGame_config(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Config, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.PuzzleGameConfig)
	fc.Result = res
	return ec.marshalOPuzzleGameConfig2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameConfig(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MinifiedPuzzleGame_config(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MinifiedPuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "timeLimit":
				return ec.fieldContext_PuzzleGameConfig_timeLimit(ctx, field)
			case "numPlayers":
				return ec.fieldContext_PuzzleGameConfig_numPlayers(ctx, field)
			case "numOfQuestions":
				return ec.fieldContext_PuzzleGameConfig_numOfQuestions(ctx, field)
			case "gameType":
				return ec.fieldContext_PuzzleGameConfig_gameType(ctx, field)
			case "difficultyLevel":
				return ec.fieldContext_PuzzleGameConfig_difficultyLevel(ctx, field)
			case "maxTimePerQuestion":
				return ec.fieldContext_PuzzleGameConfig_maxTimePerQuestion(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PuzzleGameConfig", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _MinifiedPuzzleGame_leaderBoard(ctx context.Context, field graphql.CollectedField, obj *models.MinifiedPuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MinifiedPuzzleGame_leaderBoard(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LeaderBoard, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.PuzzleLeaderboardEntry)
	fc.Result = res
	return ec.marshalOPuzzleLeaderboardEntry2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleLeaderboardEntry(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MinifiedPuzzleGame_leaderBoard(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MinifiedPuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "userId":
				return ec.fieldContext_PuzzleLeaderboardEntry_userId(ctx, field)
			case "correct":
				return ec.fieldContext_PuzzleLeaderboardEntry_correct(ctx, field)
			case "incorrect":
				return ec.fieldContext_PuzzleLeaderboardEntry_incorrect(ctx, field)
			case "totalPoints":
				return ec.fieldContext_PuzzleLeaderboardEntry_totalPoints(ctx, field)
			case "ratingChange":
				return ec.fieldContext_PuzzleLeaderboardEntry_ratingChange(ctx, field)
			case "statikCoinsEarned":
				return ec.fieldContext_PuzzleLeaderboardEntry_statikCoinsEarned(ctx, field)
			case "rank":
				return ec.fieldContext_PuzzleLeaderboardEntry_rank(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PuzzleLeaderboardEntry", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _MinifiedPuzzleGame_startTime(ctx context.Context, field graphql.CollectedField, obj *models.MinifiedPuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MinifiedPuzzleGame_startTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StartTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MinifiedPuzzleGame_startTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MinifiedPuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MinifiedPuzzleGame_endTime(ctx context.Context, field graphql.CollectedField, obj *models.MinifiedPuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MinifiedPuzzleGame_endTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EndTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MinifiedPuzzleGame_endTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MinifiedPuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGame__id(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGame__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGame__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGame_players(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGame_players(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Players, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.Player)
	fc.Result = res
	return ec.marshalNPlayer2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPlayerᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGame_players(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "userId":
				return ec.fieldContext_Player_userId(ctx, field)
			case "rating":
				return ec.fieldContext_Player_rating(ctx, field)
			case "statikCoins":
				return ec.fieldContext_Player_statikCoins(ctx, field)
			case "status":
				return ec.fieldContext_Player_status(ctx, field)
			case "timeLeft":
				return ec.fieldContext_Player_timeLeft(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Player", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGame_gameStatus(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGame_gameStatus(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GameStatus, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.PuzzleGameStatus)
	fc.Result = res
	return ec.marshalNPUZZLE_GAME_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameStatus(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGame_gameStatus(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type PUZZLE_GAME_STATUS does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGame_rematchRequestedBy(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGame_rematchRequestedBy(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RematchRequestedBy, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGame_rematchRequestedBy(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGame_gameType(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGame_gameType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GameType, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.PuzzleGameType)
	fc.Result = res
	return ec.marshalNPUZZLE_GAME_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGame_gameType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type PUZZLE_GAME_TYPE does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGame_createdBy(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGame_createdBy(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatedBy, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGame_createdBy(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGame_config(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGame_config(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Config, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.PuzzleGameConfig)
	fc.Result = res
	return ec.marshalOPuzzleGameConfig2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameConfig(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGame_config(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "timeLimit":
				return ec.fieldContext_PuzzleGameConfig_timeLimit(ctx, field)
			case "numPlayers":
				return ec.fieldContext_PuzzleGameConfig_numPlayers(ctx, field)
			case "numOfQuestions":
				return ec.fieldContext_PuzzleGameConfig_numOfQuestions(ctx, field)
			case "gameType":
				return ec.fieldContext_PuzzleGameConfig_gameType(ctx, field)
			case "difficultyLevel":
				return ec.fieldContext_PuzzleGameConfig_difficultyLevel(ctx, field)
			case "maxTimePerQuestion":
				return ec.fieldContext_PuzzleGameConfig_maxTimePerQuestion(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PuzzleGameConfig", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGame_questions(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGame_questions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Questions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.PuzzleGameQuestion)
	fc.Result = res
	return ec.marshalOPuzzleGameQuestion2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameQuestion(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGame_questions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_PuzzleGameQuestion__id(ctx, field)
			case "question":
				return ec.fieldContext_PuzzleGameQuestion_question(ctx, field)
			case "submissions":
				return ec.fieldContext_PuzzleGameQuestion_submissions(ctx, field)
			case "stats":
				return ec.fieldContext_PuzzleGameQuestion_stats(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PuzzleGameQuestion", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGame_leaderBoard(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGame_leaderBoard(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LeaderBoard, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.PuzzleLeaderboardEntry)
	fc.Result = res
	return ec.marshalOPuzzleLeaderboardEntry2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleLeaderboardEntry(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGame_leaderBoard(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "userId":
				return ec.fieldContext_PuzzleLeaderboardEntry_userId(ctx, field)
			case "correct":
				return ec.fieldContext_PuzzleLeaderboardEntry_correct(ctx, field)
			case "incorrect":
				return ec.fieldContext_PuzzleLeaderboardEntry_incorrect(ctx, field)
			case "totalPoints":
				return ec.fieldContext_PuzzleLeaderboardEntry_totalPoints(ctx, field)
			case "ratingChange":
				return ec.fieldContext_PuzzleLeaderboardEntry_ratingChange(ctx, field)
			case "statikCoinsEarned":
				return ec.fieldContext_PuzzleLeaderboardEntry_statikCoinsEarned(ctx, field)
			case "rank":
				return ec.fieldContext_PuzzleLeaderboardEntry_rank(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PuzzleLeaderboardEntry", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGame_isRatedGame(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGame_isRatedGame(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsRatedGame, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGame_isRatedGame(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGame_startTime(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGame_startTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StartTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGame_startTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGame_endTime(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGame_endTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EndTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGame_endTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGame_seriesId(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGame) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGame_seriesId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.SeriesID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGame_seriesId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGame",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGameConfig_timeLimit(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGameConfig_timeLimit(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TimeLimit, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGameConfig_timeLimit(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGameConfig_numPlayers(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGameConfig_numPlayers(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NumPlayers, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGameConfig_numPlayers(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGameConfig_numOfQuestions(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGameConfig_numOfQuestions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NumOfQuestions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGameConfig_numOfQuestions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGameConfig_gameType(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGameConfig_gameType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GameType, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.PuzzleGameType)
	fc.Result = res
	return ec.marshalOPUZZLE_GAME_TYPE2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGameConfig_gameType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type PUZZLE_GAME_TYPE does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGameConfig_difficultyLevel(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGameConfig_difficultyLevel(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.DifficultyLevel, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]int)
	fc.Result = res
	return ec.marshalOInt2ᚕintᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGameConfig_difficultyLevel(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGameConfig_maxTimePerQuestion(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGameConfig_maxTimePerQuestion(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.MaxTimePerQuestion, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGameConfig_maxTimePerQuestion(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGameQuestion__id(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGameQuestion) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGameQuestion__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Id, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGameQuestion__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGameQuestion",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGameQuestion_question(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGameQuestion) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGameQuestion_question(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Question, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGameQuestion_question(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGameQuestion",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGameQuestion_submissions(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGameQuestion) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGameQuestion_submissions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Submissions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.PuzzleQuestionSubmission)
	fc.Result = res
	return ec.marshalOPuzzleQuestionSubmission2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleQuestionSubmission(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGameQuestion_submissions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGameQuestion",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "userId":
				return ec.fieldContext_PuzzleQuestionSubmission_userId(ctx, field)
			case "timeTaken":
				return ec.fieldContext_PuzzleQuestionSubmission_timeTaken(ctx, field)
			case "points":
				return ec.fieldContext_PuzzleQuestionSubmission_points(ctx, field)
			case "submissionTime":
				return ec.fieldContext_PuzzleQuestionSubmission_submissionTime(ctx, field)
			case "isCorrect":
				return ec.fieldContext_PuzzleQuestionSubmission_isCorrect(ctx, field)
			case "inCorrectAttempts":
				return ec.fieldContext_PuzzleQuestionSubmission_inCorrectAttempts(ctx, field)
			case "submittedValues":
				return ec.fieldContext_PuzzleQuestionSubmission_submittedValues(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PuzzleQuestionSubmission", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGameQuestion_stats(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGameQuestion) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGameQuestion_stats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Stats, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.PuzzleGameQuestionStats)
	fc.Result = res
	return ec.marshalOPuzzleGameQuestionStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameQuestionStats(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGameQuestion_stats(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGameQuestion",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "fastestTime":
				return ec.fieldContext_PuzzleGameQuestionStats_fastestTime(ctx, field)
			case "userIds":
				return ec.fieldContext_PuzzleGameQuestionStats_userIds(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PuzzleGameQuestionStats", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGameQuestionStats_fastestTime(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGameQuestionStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGameQuestionStats_fastestTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.FastestTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGameQuestionStats_fastestTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGameQuestionStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleGameQuestionStats_userIds(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleGameQuestionStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleGameQuestionStats_userIds(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserIds, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚕᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleGameQuestionStats_userIds(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleGameQuestionStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleLeaderboardEntry_userId(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleLeaderboardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleLeaderboardEntry_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleLeaderboardEntry_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleLeaderboardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleLeaderboardEntry_correct(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleLeaderboardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleLeaderboardEntry_correct(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Correct, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleLeaderboardEntry_correct(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleLeaderboardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleLeaderboardEntry_incorrect(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleLeaderboardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleLeaderboardEntry_incorrect(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Incorrect, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleLeaderboardEntry_incorrect(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleLeaderboardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleLeaderboardEntry_totalPoints(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleLeaderboardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleLeaderboardEntry_totalPoints(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalPoints, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*float64)
	fc.Result = res
	return ec.marshalOFloat2ᚖfloat64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleLeaderboardEntry_totalPoints(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleLeaderboardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleLeaderboardEntry_ratingChange(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleLeaderboardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleLeaderboardEntry_ratingChange(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RatingChange, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleLeaderboardEntry_ratingChange(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleLeaderboardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleLeaderboardEntry_statikCoinsEarned(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleLeaderboardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleLeaderboardEntry_statikCoinsEarned(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StatikCoinsEarned, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleLeaderboardEntry_statikCoinsEarned(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleLeaderboardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleLeaderboardEntry_rank(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleLeaderboardEntry) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleLeaderboardEntry_rank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleLeaderboardEntry_rank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleLeaderboardEntry",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleQuestionSubmission_userId(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleQuestionSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleQuestionSubmission_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleQuestionSubmission_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleQuestionSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleQuestionSubmission_timeTaken(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleQuestionSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleQuestionSubmission_timeTaken(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TimeTaken, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleQuestionSubmission_timeTaken(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleQuestionSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleQuestionSubmission_points(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleQuestionSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleQuestionSubmission_points(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Points, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleQuestionSubmission_points(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleQuestionSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleQuestionSubmission_submissionTime(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleQuestionSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleQuestionSubmission_submissionTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.SubmissionTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.Date)
	fc.Result = res
	return ec.marshalODate2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐDate(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleQuestionSubmission_submissionTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleQuestionSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Date does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleQuestionSubmission_isCorrect(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleQuestionSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleQuestionSubmission_isCorrect(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsCorrect, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleQuestionSubmission_isCorrect(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleQuestionSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleQuestionSubmission_inCorrectAttempts(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleQuestionSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleQuestionSubmission_inCorrectAttempts(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.InCorrectAttempts, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleQuestionSubmission_inCorrectAttempts(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleQuestionSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleQuestionSubmission_submittedValues(ctx context.Context, field graphql.CollectedField, obj *models.PuzzleQuestionSubmission) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleQuestionSubmission_submittedValues(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.SubmittedValues, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*string)
	fc.Result = res
	return ec.marshalOString2ᚕᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleQuestionSubmission_submittedValues(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleQuestionSubmission",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

// endregion **************************** field.gotpl *****************************

// region    **************************** input.gotpl *****************************

func (ec *executionContext) unmarshalInputChallengeUserForPuzzleGameInput(ctx context.Context, obj any) (models.ChallengeUserForPuzzleGameInput, error) {
	var it models.ChallengeUserForPuzzleGameInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"userId", "gameConfig"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "userId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("userId"))
			data, err := ec.unmarshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.UserID = data
		case "gameConfig":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("gameConfig"))
			data, err := ec.unmarshalOPuzzleGameConfigInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameConfigInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.GameConfig = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputGetPuzzleGamesInput(ctx context.Context, obj any) (models.GetPuzzleGamesInput, error) {
	var it models.GetPuzzleGamesInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"userId", "timeRange", "pageInfo"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "userId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("userId"))
			data, err := ec.unmarshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.UserID = data
		case "timeRange":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("timeRange"))
			data, err := ec.unmarshalOTimeRangeInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐTimeRangeInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.TimeRange = data
		case "pageInfo":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("pageInfo"))
			data, err := ec.unmarshalOPageInfoInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPageInfoInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.PageInfo = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputPuzzleGameConfigInput(ctx context.Context, obj any) (models.PuzzleGameConfigInput, error) {
	var it models.PuzzleGameConfigInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"timeLimit", "numPlayers", "numOfQuestions", "gameType", "difficultyLevel", "maxTimePerQuestion"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "timeLimit":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("timeLimit"))
			data, err := ec.unmarshalOInt2ᚖint(ctx, v)
			if err != nil {
				return it, err
			}
			it.TimeLimit = data
		case "numPlayers":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("numPlayers"))
			data, err := ec.unmarshalOInt2ᚖint(ctx, v)
			if err != nil {
				return it, err
			}
			it.NumPlayers = data
		case "numOfQuestions":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("numOfQuestions"))
			data, err := ec.unmarshalOInt2ᚖint(ctx, v)
			if err != nil {
				return it, err
			}
			it.NumOfQuestions = data
		case "gameType":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("gameType"))
			data, err := ec.unmarshalOPUZZLE_GAME_TYPE2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameType(ctx, v)
			if err != nil {
				return it, err
			}
			it.GameType = data
		case "difficultyLevel":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("difficultyLevel"))
			data, err := ec.unmarshalOInt2ᚕintᚄ(ctx, v)
			if err != nil {
				return it, err
			}
			it.DifficultyLevel = data
		case "maxTimePerQuestion":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("maxTimePerQuestion"))
			data, err := ec.unmarshalOInt2ᚖint(ctx, v)
			if err != nil {
				return it, err
			}
			it.MaxTimePerQuestion = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputSubmitPuzzleGameAnswerInput(ctx context.Context, obj any) (models.SubmitPuzzleGameAnswerInput, error) {
	var it models.SubmitPuzzleGameAnswerInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"gameId", "questionId", "isCorrect", "inCorrectAttempts", "timeOfSubmission"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "gameId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("gameId"))
			data, err := ec.unmarshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.GameID = data
		case "questionId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("questionId"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.QuestionID = data
		case "isCorrect":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("isCorrect"))
			data, err := ec.unmarshalOBoolean2ᚖbool(ctx, v)
			if err != nil {
				return it, err
			}
			it.IsCorrect = data
		case "inCorrectAttempts":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("inCorrectAttempts"))
			data, err := ec.unmarshalOInt2ᚖint(ctx, v)
			if err != nil {
				return it, err
			}
			it.InCorrectAttempts = data
		case "timeOfSubmission":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("timeOfSubmission"))
			data, err := ec.unmarshalODate2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐDate(ctx, v)
			if err != nil {
				return it, err
			}
			it.TimeOfSubmission = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputSubmitPuzzleRushGame(ctx context.Context, obj any) (models.SubmitPuzzleRushGame, error) {
	var it models.SubmitPuzzleRushGame
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"score", "timeSpent"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "score":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("score"))
			data, err := ec.unmarshalOInt2ᚖint(ctx, v)
			if err != nil {
				return it, err
			}
			it.Score = data
		case "timeSpent":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("timeSpent"))
			data, err := ec.unmarshalOInt2ᚖint(ctx, v)
			if err != nil {
				return it, err
			}
			it.TimeSpent = data
		}
	}

	return it, nil
}

// endregion **************************** input.gotpl *****************************

// region    ************************** interface.gotpl ***************************

// endregion ************************** interface.gotpl ***************************

// region    **************************** object.gotpl ****************************

var crossMathPuzzleRushImplementors = []string{"CrossMathPuzzleRush"}

func (ec *executionContext) _CrossMathPuzzleRush(ctx context.Context, sel ast.SelectionSet, obj *models.CrossMathPuzzleRush) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, crossMathPuzzleRushImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("CrossMathPuzzleRush")
		case "_id":
			out.Values[i] = ec._CrossMathPuzzleRush__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userId":
			out.Values[i] = ec._CrossMathPuzzleRush_userId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "bestAllTime":
			out.Values[i] = ec._CrossMathPuzzleRush_bestAllTime(ctx, field, obj)
		case "isNewBestScore":
			out.Values[i] = ec._CrossMathPuzzleRush_isNewBestScore(ctx, field, obj)
		case "createdAt":
			out.Values[i] = ec._CrossMathPuzzleRush_createdAt(ctx, field, obj)
		case "updatedAt":
			out.Values[i] = ec._CrossMathPuzzleRush_updatedAt(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var crossMathPuzzleRushPlayerInfoImplementors = []string{"CrossMathPuzzleRushPlayerInfo"}

func (ec *executionContext) _CrossMathPuzzleRushPlayerInfo(ctx context.Context, sel ast.SelectionSet, obj *models.CrossMathPuzzleRushPlayerInfo) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, crossMathPuzzleRushPlayerInfoImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("CrossMathPuzzleRushPlayerInfo")
		case "rank":
			out.Values[i] = ec._CrossMathPuzzleRushPlayerInfo_rank(ctx, field, obj)
		case "score":
			out.Values[i] = ec._CrossMathPuzzleRushPlayerInfo_score(ctx, field, obj)
		case "userInfo":
			out.Values[i] = ec._CrossMathPuzzleRushPlayerInfo_userInfo(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var crossMathPuzzleRushStatsImplementors = []string{"CrossMathPuzzleRushStats"}

func (ec *executionContext) _CrossMathPuzzleRushStats(ctx context.Context, sel ast.SelectionSet, obj *models.CrossMathPuzzleRushStats) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, crossMathPuzzleRushStatsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("CrossMathPuzzleRushStats")
		case "bestAllTime":
			out.Values[i] = ec._CrossMathPuzzleRushStats_bestAllTime(ctx, field, obj)
		case "globalRank":
			out.Values[i] = ec._CrossMathPuzzleRushStats_globalRank(ctx, field, obj)
		case "friendsRank":
			out.Values[i] = ec._CrossMathPuzzleRushStats_friendsRank(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var getPuzzleGamesOutputImplementors = []string{"GetPuzzleGamesOutput"}

func (ec *executionContext) _GetPuzzleGamesOutput(ctx context.Context, sel ast.SelectionSet, obj *models.GetPuzzleGamesOutput) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, getPuzzleGamesOutputImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GetPuzzleGamesOutput")
		case "games":
			out.Values[i] = ec._GetPuzzleGamesOutput_games(ctx, field, obj)
		case "users":
			out.Values[i] = ec._GetPuzzleGamesOutput_users(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var minifiedPuzzleGameImplementors = []string{"MinifiedPuzzleGame"}

func (ec *executionContext) _MinifiedPuzzleGame(ctx context.Context, sel ast.SelectionSet, obj *models.MinifiedPuzzleGame) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, minifiedPuzzleGameImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("MinifiedPuzzleGame")
		case "_id":
			out.Values[i] = ec._MinifiedPuzzleGame__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "players":
			out.Values[i] = ec._MinifiedPuzzleGame_players(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "config":
			out.Values[i] = ec._MinifiedPuzzleGame_config(ctx, field, obj)
		case "leaderBoard":
			out.Values[i] = ec._MinifiedPuzzleGame_leaderBoard(ctx, field, obj)
		case "startTime":
			out.Values[i] = ec._MinifiedPuzzleGame_startTime(ctx, field, obj)
		case "endTime":
			out.Values[i] = ec._MinifiedPuzzleGame_endTime(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var puzzleGameImplementors = []string{"PuzzleGame"}

func (ec *executionContext) _PuzzleGame(ctx context.Context, sel ast.SelectionSet, obj *models.PuzzleGame) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, puzzleGameImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PuzzleGame")
		case "_id":
			out.Values[i] = ec._PuzzleGame__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "players":
			out.Values[i] = ec._PuzzleGame_players(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "gameStatus":
			out.Values[i] = ec._PuzzleGame_gameStatus(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "rematchRequestedBy":
			out.Values[i] = ec._PuzzleGame_rematchRequestedBy(ctx, field, obj)
		case "gameType":
			out.Values[i] = ec._PuzzleGame_gameType(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "createdBy":
			out.Values[i] = ec._PuzzleGame_createdBy(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "config":
			out.Values[i] = ec._PuzzleGame_config(ctx, field, obj)
		case "questions":
			out.Values[i] = ec._PuzzleGame_questions(ctx, field, obj)
		case "leaderBoard":
			out.Values[i] = ec._PuzzleGame_leaderBoard(ctx, field, obj)
		case "isRatedGame":
			out.Values[i] = ec._PuzzleGame_isRatedGame(ctx, field, obj)
		case "startTime":
			out.Values[i] = ec._PuzzleGame_startTime(ctx, field, obj)
		case "endTime":
			out.Values[i] = ec._PuzzleGame_endTime(ctx, field, obj)
		case "seriesId":
			out.Values[i] = ec._PuzzleGame_seriesId(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var puzzleGameConfigImplementors = []string{"PuzzleGameConfig"}

func (ec *executionContext) _PuzzleGameConfig(ctx context.Context, sel ast.SelectionSet, obj *models.PuzzleGameConfig) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, puzzleGameConfigImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PuzzleGameConfig")
		case "timeLimit":
			out.Values[i] = ec._PuzzleGameConfig_timeLimit(ctx, field, obj)
		case "numPlayers":
			out.Values[i] = ec._PuzzleGameConfig_numPlayers(ctx, field, obj)
		case "numOfQuestions":
			out.Values[i] = ec._PuzzleGameConfig_numOfQuestions(ctx, field, obj)
		case "gameType":
			out.Values[i] = ec._PuzzleGameConfig_gameType(ctx, field, obj)
		case "difficultyLevel":
			out.Values[i] = ec._PuzzleGameConfig_difficultyLevel(ctx, field, obj)
		case "maxTimePerQuestion":
			out.Values[i] = ec._PuzzleGameConfig_maxTimePerQuestion(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var puzzleGameQuestionImplementors = []string{"PuzzleGameQuestion"}

func (ec *executionContext) _PuzzleGameQuestion(ctx context.Context, sel ast.SelectionSet, obj *models.PuzzleGameQuestion) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, puzzleGameQuestionImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PuzzleGameQuestion")
		case "_id":
			out.Values[i] = ec._PuzzleGameQuestion__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "question":
			out.Values[i] = ec._PuzzleGameQuestion_question(ctx, field, obj)
		case "submissions":
			out.Values[i] = ec._PuzzleGameQuestion_submissions(ctx, field, obj)
		case "stats":
			out.Values[i] = ec._PuzzleGameQuestion_stats(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var puzzleGameQuestionStatsImplementors = []string{"PuzzleGameQuestionStats"}

func (ec *executionContext) _PuzzleGameQuestionStats(ctx context.Context, sel ast.SelectionSet, obj *models.PuzzleGameQuestionStats) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, puzzleGameQuestionStatsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PuzzleGameQuestionStats")
		case "fastestTime":
			out.Values[i] = ec._PuzzleGameQuestionStats_fastestTime(ctx, field, obj)
		case "userIds":
			out.Values[i] = ec._PuzzleGameQuestionStats_userIds(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var puzzleLeaderboardEntryImplementors = []string{"PuzzleLeaderboardEntry"}

func (ec *executionContext) _PuzzleLeaderboardEntry(ctx context.Context, sel ast.SelectionSet, obj *models.PuzzleLeaderboardEntry) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, puzzleLeaderboardEntryImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PuzzleLeaderboardEntry")
		case "userId":
			out.Values[i] = ec._PuzzleLeaderboardEntry_userId(ctx, field, obj)
		case "correct":
			out.Values[i] = ec._PuzzleLeaderboardEntry_correct(ctx, field, obj)
		case "incorrect":
			out.Values[i] = ec._PuzzleLeaderboardEntry_incorrect(ctx, field, obj)
		case "totalPoints":
			out.Values[i] = ec._PuzzleLeaderboardEntry_totalPoints(ctx, field, obj)
		case "ratingChange":
			out.Values[i] = ec._PuzzleLeaderboardEntry_ratingChange(ctx, field, obj)
		case "statikCoinsEarned":
			out.Values[i] = ec._PuzzleLeaderboardEntry_statikCoinsEarned(ctx, field, obj)
		case "rank":
			out.Values[i] = ec._PuzzleLeaderboardEntry_rank(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var puzzleQuestionSubmissionImplementors = []string{"PuzzleQuestionSubmission"}

func (ec *executionContext) _PuzzleQuestionSubmission(ctx context.Context, sel ast.SelectionSet, obj *models.PuzzleQuestionSubmission) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, puzzleQuestionSubmissionImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PuzzleQuestionSubmission")
		case "userId":
			out.Values[i] = ec._PuzzleQuestionSubmission_userId(ctx, field, obj)
		case "timeTaken":
			out.Values[i] = ec._PuzzleQuestionSubmission_timeTaken(ctx, field, obj)
		case "points":
			out.Values[i] = ec._PuzzleQuestionSubmission_points(ctx, field, obj)
		case "submissionTime":
			out.Values[i] = ec._PuzzleQuestionSubmission_submissionTime(ctx, field, obj)
		case "isCorrect":
			out.Values[i] = ec._PuzzleQuestionSubmission_isCorrect(ctx, field, obj)
		case "inCorrectAttempts":
			out.Values[i] = ec._PuzzleQuestionSubmission_inCorrectAttempts(ctx, field, obj)
		case "submittedValues":
			out.Values[i] = ec._PuzzleQuestionSubmission_submittedValues(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

// endregion **************************** object.gotpl ****************************

// region    ***************************** type.gotpl *****************************

func (ec *executionContext) marshalNCrossMathPuzzleRushPlayerInfo2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCrossMathPuzzleRushPlayerInfo(ctx context.Context, sel ast.SelectionSet, v *models.CrossMathPuzzleRushPlayerInfo) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._CrossMathPuzzleRushPlayerInfo(ctx, sel, v)
}

func (ec *executionContext) unmarshalNPUZZLE_GAME_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameStatus(ctx context.Context, v any) (models.PuzzleGameStatus, error) {
	var res models.PuzzleGameStatus
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNPUZZLE_GAME_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameStatus(ctx context.Context, sel ast.SelectionSet, v models.PuzzleGameStatus) graphql.Marshaler {
	return v
}

func (ec *executionContext) unmarshalNPUZZLE_GAME_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameType(ctx context.Context, v any) (models.PuzzleGameType, error) {
	var res models.PuzzleGameType
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNPUZZLE_GAME_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameType(ctx context.Context, sel ast.SelectionSet, v models.PuzzleGameType) graphql.Marshaler {
	return v
}

func (ec *executionContext) marshalNPuzzleGame2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGame(ctx context.Context, sel ast.SelectionSet, v models.PuzzleGame) graphql.Marshaler {
	return ec._PuzzleGame(ctx, sel, &v)
}

func (ec *executionContext) marshalNPuzzleGame2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGame(ctx context.Context, sel ast.SelectionSet, v *models.PuzzleGame) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._PuzzleGame(ctx, sel, v)
}

func (ec *executionContext) unmarshalNSubmitPuzzleRushGame2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSubmitPuzzleRushGame(ctx context.Context, v any) (models.SubmitPuzzleRushGame, error) {
	res, err := ec.unmarshalInputSubmitPuzzleRushGame(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) unmarshalOChallengeUserForPuzzleGameInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐChallengeUserForPuzzleGameInput(ctx context.Context, v any) (*models.ChallengeUserForPuzzleGameInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputChallengeUserForPuzzleGameInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOCrossMathPuzzleRush2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCrossMathPuzzleRush(ctx context.Context, sel ast.SelectionSet, v *models.CrossMathPuzzleRush) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._CrossMathPuzzleRush(ctx, sel, v)
}

func (ec *executionContext) marshalOCrossMathPuzzleRushPlayerInfo2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCrossMathPuzzleRushPlayerInfoᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.CrossMathPuzzleRushPlayerInfo) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNCrossMathPuzzleRushPlayerInfo2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCrossMathPuzzleRushPlayerInfo(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalOCrossMathPuzzleRushStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCrossMathPuzzleRushStats(ctx context.Context, sel ast.SelectionSet, v *models.CrossMathPuzzleRushStats) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._CrossMathPuzzleRushStats(ctx, sel, v)
}

func (ec *executionContext) unmarshalOGetPuzzleGamesInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGetPuzzleGamesInput(ctx context.Context, v any) (*models.GetPuzzleGamesInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputGetPuzzleGamesInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOGetPuzzleGamesOutput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGetPuzzleGamesOutput(ctx context.Context, sel ast.SelectionSet, v *models.GetPuzzleGamesOutput) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._GetPuzzleGamesOutput(ctx, sel, v)
}

func (ec *executionContext) marshalOMinifiedPuzzleGame2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMinifiedPuzzleGame(ctx context.Context, sel ast.SelectionSet, v []*models.MinifiedPuzzleGame) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOMinifiedPuzzleGame2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMinifiedPuzzleGame(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOMinifiedPuzzleGame2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐMinifiedPuzzleGame(ctx context.Context, sel ast.SelectionSet, v *models.MinifiedPuzzleGame) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._MinifiedPuzzleGame(ctx, sel, v)
}

func (ec *executionContext) unmarshalOPUZZLE_GAME_TYPE2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameType(ctx context.Context, v any) (*models.PuzzleGameType, error) {
	if v == nil {
		return nil, nil
	}
	var res = new(models.PuzzleGameType)
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOPUZZLE_GAME_TYPE2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameType(ctx context.Context, sel ast.SelectionSet, v *models.PuzzleGameType) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return v
}

func (ec *executionContext) marshalOPuzzleGame2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGame(ctx context.Context, sel ast.SelectionSet, v *models.PuzzleGame) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._PuzzleGame(ctx, sel, v)
}

func (ec *executionContext) marshalOPuzzleGameConfig2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameConfig(ctx context.Context, sel ast.SelectionSet, v *models.PuzzleGameConfig) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._PuzzleGameConfig(ctx, sel, v)
}

func (ec *executionContext) unmarshalOPuzzleGameConfigInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameConfigInput(ctx context.Context, v any) (*models.PuzzleGameConfigInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputPuzzleGameConfigInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOPuzzleGameQuestion2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameQuestion(ctx context.Context, sel ast.SelectionSet, v []*models.PuzzleGameQuestion) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOPuzzleGameQuestion2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameQuestion(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOPuzzleGameQuestion2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameQuestion(ctx context.Context, sel ast.SelectionSet, v *models.PuzzleGameQuestion) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._PuzzleGameQuestion(ctx, sel, v)
}

func (ec *executionContext) marshalOPuzzleGameQuestionStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleGameQuestionStats(ctx context.Context, sel ast.SelectionSet, v *models.PuzzleGameQuestionStats) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._PuzzleGameQuestionStats(ctx, sel, v)
}

func (ec *executionContext) marshalOPuzzleLeaderboardEntry2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleLeaderboardEntry(ctx context.Context, sel ast.SelectionSet, v []*models.PuzzleLeaderboardEntry) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOPuzzleLeaderboardEntry2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleLeaderboardEntry(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOPuzzleLeaderboardEntry2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleLeaderboardEntry(ctx context.Context, sel ast.SelectionSet, v *models.PuzzleLeaderboardEntry) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._PuzzleLeaderboardEntry(ctx, sel, v)
}

func (ec *executionContext) marshalOPuzzleQuestionSubmission2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleQuestionSubmission(ctx context.Context, sel ast.SelectionSet, v []*models.PuzzleQuestionSubmission) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOPuzzleQuestionSubmission2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleQuestionSubmission(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOPuzzleQuestionSubmission2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPuzzleQuestionSubmission(ctx context.Context, sel ast.SelectionSet, v *models.PuzzleQuestionSubmission) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._PuzzleQuestionSubmission(ctx, sel, v)
}

func (ec *executionContext) unmarshalOSubmitPuzzleGameAnswerInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐSubmitPuzzleGameAnswerInput(ctx context.Context, v any) (*models.SubmitPuzzleGameAnswerInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputSubmitPuzzleGameAnswerInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

// endregion ***************************** type.gotpl *****************************
