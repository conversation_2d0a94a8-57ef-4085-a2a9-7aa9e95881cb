// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"context"
	"errors"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"
	"strconv"
	"sync"
	"sync/atomic"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/ast"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// region    ************************** generated!.gotpl **************************

// endregion ************************** generated!.gotpl **************************

// region    ***************************** args.gotpl *****************************

// endregion ***************************** args.gotpl *****************************

// region    ************************** directives.gotpl **************************

// endregion ************************** directives.gotpl **************************

// region    **************************** field.gotpl *****************************

func (ec *executionContext) _FieldValidation_regex(ctx context.Context, field graphql.CollectedField, obj *models.FieldValidation) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FieldValidation_regex(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Regex, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FieldValidation_regex(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FieldValidation",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FieldValidation_min(ctx context.Context, field graphql.CollectedField, obj *models.FieldValidation) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FieldValidation_min(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Min, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FieldValidation_min(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FieldValidation",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FieldValidation_max(ctx context.Context, field graphql.CollectedField, obj *models.FieldValidation) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FieldValidation_max(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Max, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FieldValidation_max(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FieldValidation",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FieldValidation_emailSuffix(ctx context.Context, field graphql.CollectedField, obj *models.FieldValidation) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FieldValidation_emailSuffix(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EmailSuffix, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FieldValidation_emailSuffix(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FieldValidation",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FieldValidation_emailSuffixes(ctx context.Context, field graphql.CollectedField, obj *models.FieldValidation) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FieldValidation_emailSuffixes(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EmailSuffixes, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]string)
	fc.Result = res
	return ec.marshalOString2ᚕstringᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FieldValidation_emailSuffixes(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FieldValidation",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FieldValidation_needManualVerification(ctx context.Context, field graphql.CollectedField, obj *models.FieldValidation) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FieldValidation_needManualVerification(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NeedManualVerification, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FieldValidation_needManualVerification(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FieldValidation",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FormField__id(ctx context.Context, field graphql.CollectedField, obj *models.FormField) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FormField__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FormField__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FormField",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FormField_name(ctx context.Context, field graphql.CollectedField, obj *models.FormField) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FormField_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FormField_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FormField",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FormField_type(ctx context.Context, field graphql.CollectedField, obj *models.FormField) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FormField_type(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Type, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.FieldType)
	fc.Result = res
	return ec.marshalNFieldType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFieldType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FormField_type(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FormField",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type FieldType does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FormField_label(ctx context.Context, field graphql.CollectedField, obj *models.FormField) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FormField_label(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Label, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FormField_label(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FormField",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FormField_required(ctx context.Context, field graphql.CollectedField, obj *models.FormField) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FormField_required(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Required, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FormField_required(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FormField",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FormField_options(ctx context.Context, field graphql.CollectedField, obj *models.FormField) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FormField_options(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Options, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]string)
	fc.Result = res
	return ec.marshalOString2ᚕstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FormField_options(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FormField",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FormField_validation(ctx context.Context, field graphql.CollectedField, obj *models.FormField) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FormField_validation(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Validation, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.FieldValidation)
	fc.Result = res
	return ec.marshalOFieldValidation2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFieldValidation(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FormField_validation(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FormField",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "regex":
				return ec.fieldContext_FieldValidation_regex(ctx, field)
			case "min":
				return ec.fieldContext_FieldValidation_min(ctx, field)
			case "max":
				return ec.fieldContext_FieldValidation_max(ctx, field)
			case "emailSuffix":
				return ec.fieldContext_FieldValidation_emailSuffix(ctx, field)
			case "emailSuffixes":
				return ec.fieldContext_FieldValidation_emailSuffixes(ctx, field)
			case "needManualVerification":
				return ec.fieldContext_FieldValidation_needManualVerification(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type FieldValidation", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _RegistrationForm__id(ctx context.Context, field graphql.CollectedField, obj *models.RegistrationForm) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RegistrationForm__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RegistrationForm__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RegistrationForm",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _RegistrationForm_fields(ctx context.Context, field graphql.CollectedField, obj *models.RegistrationForm) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RegistrationForm_fields(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Fields, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.FormField)
	fc.Result = res
	return ec.marshalNFormField2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFormFieldᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RegistrationForm_fields(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RegistrationForm",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_FormField__id(ctx, field)
			case "name":
				return ec.fieldContext_FormField_name(ctx, field)
			case "type":
				return ec.fieldContext_FormField_type(ctx, field)
			case "label":
				return ec.fieldContext_FormField_label(ctx, field)
			case "required":
				return ec.fieldContext_FormField_required(ctx, field)
			case "options":
				return ec.fieldContext_FormField_options(ctx, field)
			case "validation":
				return ec.fieldContext_FormField_validation(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type FormField", field.Name)
		},
	}
	return fc, nil
}

// endregion **************************** field.gotpl *****************************

// region    **************************** input.gotpl *****************************

func (ec *executionContext) unmarshalInputFieldValidationInput(ctx context.Context, obj any) (models.FieldValidationInput, error) {
	var it models.FieldValidationInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"regex", "min", "max", "emailSuffix", "emailSuffixes", "needManualVerification"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "regex":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("regex"))
			data, err := ec.unmarshalOString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Regex = data
		case "min":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("min"))
			data, err := ec.unmarshalOInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.Min = data
		case "max":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("max"))
			data, err := ec.unmarshalOInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.Max = data
		case "emailSuffix":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("emailSuffix"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.EmailSuffix = data
		case "emailSuffixes":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("emailSuffixes"))
			data, err := ec.unmarshalOString2ᚕstringᚄ(ctx, v)
			if err != nil {
				return it, err
			}
			it.EmailSuffixes = data
		case "needManualVerification":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("needManualVerification"))
			data, err := ec.unmarshalOBoolean2bool(ctx, v)
			if err != nil {
				return it, err
			}
			it.NeedManualVerification = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputFormFieldInput(ctx context.Context, obj any) (models.FormFieldInput, error) {
	var it models.FormFieldInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"name", "type", "label", "required", "options", "validation"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "name":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("name"))
			data, err := ec.unmarshalNString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Name = data
		case "type":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("type"))
			data, err := ec.unmarshalNFieldType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFieldType(ctx, v)
			if err != nil {
				return it, err
			}
			it.Type = data
		case "label":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("label"))
			data, err := ec.unmarshalNString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Label = data
		case "required":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("required"))
			data, err := ec.unmarshalNBoolean2bool(ctx, v)
			if err != nil {
				return it, err
			}
			it.Required = data
		case "options":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("options"))
			data, err := ec.unmarshalOString2ᚕstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Options = data
		case "validation":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("validation"))
			data, err := ec.unmarshalOFieldValidationInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFieldValidationInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.Validation = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputRegistrationFormInput(ctx context.Context, obj any) (models.RegistrationFormInput, error) {
	var it models.RegistrationFormInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"fields"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "fields":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("fields"))
			data, err := ec.unmarshalNFormFieldInput2ᚕmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFormFieldInputᚄ(ctx, v)
			if err != nil {
				return it, err
			}
			it.Fields = data
		}
	}

	return it, nil
}

// endregion **************************** input.gotpl *****************************

// region    ************************** interface.gotpl ***************************

// endregion ************************** interface.gotpl ***************************

// region    **************************** object.gotpl ****************************

var fieldValidationImplementors = []string{"FieldValidation"}

func (ec *executionContext) _FieldValidation(ctx context.Context, sel ast.SelectionSet, obj *models.FieldValidation) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, fieldValidationImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("FieldValidation")
		case "regex":
			out.Values[i] = ec._FieldValidation_regex(ctx, field, obj)
		case "min":
			out.Values[i] = ec._FieldValidation_min(ctx, field, obj)
		case "max":
			out.Values[i] = ec._FieldValidation_max(ctx, field, obj)
		case "emailSuffix":
			out.Values[i] = ec._FieldValidation_emailSuffix(ctx, field, obj)
		case "emailSuffixes":
			out.Values[i] = ec._FieldValidation_emailSuffixes(ctx, field, obj)
		case "needManualVerification":
			out.Values[i] = ec._FieldValidation_needManualVerification(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var formFieldImplementors = []string{"FormField"}

func (ec *executionContext) _FormField(ctx context.Context, sel ast.SelectionSet, obj *models.FormField) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, formFieldImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("FormField")
		case "_id":
			out.Values[i] = ec._FormField__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "name":
			out.Values[i] = ec._FormField_name(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "type":
			out.Values[i] = ec._FormField_type(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "label":
			out.Values[i] = ec._FormField_label(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "required":
			out.Values[i] = ec._FormField_required(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "options":
			out.Values[i] = ec._FormField_options(ctx, field, obj)
		case "validation":
			out.Values[i] = ec._FormField_validation(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var registrationFormImplementors = []string{"RegistrationForm"}

func (ec *executionContext) _RegistrationForm(ctx context.Context, sel ast.SelectionSet, obj *models.RegistrationForm) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, registrationFormImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("RegistrationForm")
		case "_id":
			out.Values[i] = ec._RegistrationForm__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "fields":
			out.Values[i] = ec._RegistrationForm_fields(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

// endregion **************************** object.gotpl ****************************

// region    ***************************** type.gotpl *****************************

func (ec *executionContext) unmarshalNFieldType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFieldType(ctx context.Context, v any) (models.FieldType, error) {
	var res models.FieldType
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNFieldType2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFieldType(ctx context.Context, sel ast.SelectionSet, v models.FieldType) graphql.Marshaler {
	return v
}

func (ec *executionContext) marshalNFormField2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFormFieldᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.FormField) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNFormField2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFormField(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNFormField2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFormField(ctx context.Context, sel ast.SelectionSet, v *models.FormField) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._FormField(ctx, sel, v)
}

func (ec *executionContext) unmarshalNFormFieldInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFormFieldInput(ctx context.Context, v any) (models.FormFieldInput, error) {
	res, err := ec.unmarshalInputFormFieldInput(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) unmarshalNFormFieldInput2ᚕmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFormFieldInputᚄ(ctx context.Context, v any) ([]models.FormFieldInput, error) {
	var vSlice []any
	vSlice = graphql.CoerceList(v)
	var err error
	res := make([]models.FormFieldInput, len(vSlice))
	for i := range vSlice {
		ctx := graphql.WithPathContext(ctx, graphql.NewPathWithIndex(i))
		res[i], err = ec.unmarshalNFormFieldInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFormFieldInput(ctx, vSlice[i])
		if err != nil {
			return nil, err
		}
	}
	return res, nil
}

func (ec *executionContext) unmarshalNRegistrationFormInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFormInput(ctx context.Context, v any) (models.RegistrationFormInput, error) {
	res, err := ec.unmarshalInputRegistrationFormInput(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOFieldValidation2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFieldValidation(ctx context.Context, sel ast.SelectionSet, v *models.FieldValidation) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._FieldValidation(ctx, sel, v)
}

func (ec *executionContext) unmarshalOFieldValidationInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFieldValidationInput(ctx context.Context, v any) (models.FieldValidationInput, error) {
	res, err := ec.unmarshalInputFieldValidationInput(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalORegistrationForm2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationForm(ctx context.Context, sel ast.SelectionSet, v *models.RegistrationForm) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._RegistrationForm(ctx, sel, v)
}

func (ec *executionContext) unmarshalORegistrationFormInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFormInput(ctx context.Context, v any) (*models.RegistrationFormInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputRegistrationFormInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

// endregion ***************************** type.gotpl *****************************
