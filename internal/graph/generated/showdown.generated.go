// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"context"
	"errors"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/ast"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// region    ************************** generated!.gotpl **************************

// endregion ************************** generated!.gotpl **************************

// region    ***************************** args.gotpl *****************************

// endregion ***************************** args.gotpl *****************************

// region    ************************** directives.gotpl **************************

// endregion ************************** directives.gotpl **************************

// region    **************************** field.gotpl *****************************

func (ec *executionContext) _CurrentShowdonParticipant_showdownId(ctx context.Context, field graphql.CollectedField, obj *models.CurrentShowdonParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CurrentShowdonParticipant_showdownId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ShowdownID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CurrentShowdonParticipant_showdownId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CurrentShowdonParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CurrentShowdonParticipant_userId(ctx context.Context, field graphql.CollectedField, obj *models.CurrentShowdonParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CurrentShowdonParticipant_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CurrentShowdonParticipant_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CurrentShowdonParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CurrentShowdonParticipant_registrationData(ctx context.Context, field graphql.CollectedField, obj *models.CurrentShowdonParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CurrentShowdonParticipant_registrationData(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RegistrationData, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.RegistrationFieldData)
	fc.Result = res
	return ec.marshalORegistrationFieldData2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFieldData(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CurrentShowdonParticipant_registrationData(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CurrentShowdonParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "name":
				return ec.fieldContext_RegistrationFieldData_name(ctx, field)
			case "values":
				return ec.fieldContext_RegistrationFieldData_values(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type RegistrationFieldData", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _CurrentShowdonParticipant_lastSubmissionTime(ctx context.Context, field graphql.CollectedField, obj *models.CurrentShowdonParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CurrentShowdonParticipant_lastSubmissionTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.LastSubmissionTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CurrentShowdonParticipant_lastSubmissionTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CurrentShowdonParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CurrentShowdonParticipant_stats(ctx context.Context, field graphql.CollectedField, obj *models.CurrentShowdonParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CurrentShowdonParticipant_stats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Stats, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.ShowdownParticipantStats)
	fc.Result = res
	return ec.marshalNShowdownParticipantStats2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownParticipantStats(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CurrentShowdonParticipant_stats(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CurrentShowdonParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "currentScore":
				return ec.fieldContext_ShowdownParticipantStats_currentScore(ctx, field)
			case "win":
				return ec.fieldContext_ShowdownParticipantStats_win(ctx, field)
			case "loss":
				return ec.fieldContext_ShowdownParticipantStats_loss(ctx, field)
			case "draw":
				return ec.fieldContext_ShowdownParticipantStats_draw(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ShowdownParticipantStats", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _CurrentShowdonParticipant_rank(ctx context.Context, field graphql.CollectedField, obj *models.CurrentShowdonParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CurrentShowdonParticipant_rank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CurrentShowdonParticipant_rank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CurrentShowdonParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CurrentShowdonParticipant_rounds(ctx context.Context, field graphql.CollectedField, obj *models.CurrentShowdonParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CurrentShowdonParticipant_rounds(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rounds, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.ShowdownRound)
	fc.Result = res
	return ec.marshalOShowdownRound2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownRound(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CurrentShowdonParticipant_rounds(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CurrentShowdonParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "opponent":
				return ec.fieldContext_ShowdownRound_opponent(ctx, field)
			case "round":
				return ec.fieldContext_ShowdownRound_round(ctx, field)
			case "score":
				return ec.fieldContext_ShowdownRound_score(ctx, field)
			case "games":
				return ec.fieldContext_ShowdownRound_games(ctx, field)
			case "wins":
				return ec.fieldContext_ShowdownRound_wins(ctx, field)
			case "loose":
				return ec.fieldContext_ShowdownRound_loose(ctx, field)
			case "totalGamesPlayed":
				return ec.fieldContext_ShowdownRound_totalGamesPlayed(ctx, field)
			case "playerStatus":
				return ec.fieldContext_ShowdownRound_playerStatus(ctx, field)
			case "isBye":
				return ec.fieldContext_ShowdownRound_isBye(ctx, field)
			case "isRoundEnded":
				return ec.fieldContext_ShowdownRound_isRoundEnded(ctx, field)
			case "hasJoined":
				return ec.fieldContext_ShowdownRound_hasJoined(ctx, field)
			case "hasOpponentNotShown":
				return ec.fieldContext_ShowdownRound_hasOpponentNotShown(ctx, field)
			case "hasFailedToPlay":
				return ec.fieldContext_ShowdownRound_hasFailedToPlay(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ShowdownRound", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _CurrentShowdonParticipant_hadABye(ctx context.Context, field graphql.CollectedField, obj *models.CurrentShowdonParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CurrentShowdonParticipant_hadABye(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HadABye, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CurrentShowdonParticipant_hadABye(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CurrentShowdonParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CurrentShowdonParticipant_currentGame(ctx context.Context, field graphql.CollectedField, obj *models.CurrentShowdonParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CurrentShowdonParticipant_currentGame(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CurrentGame, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CurrentShowdonParticipant_currentGame(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CurrentShowdonParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CurrentShowdonParticipant_recentOpponent(ctx context.Context, field graphql.CollectedField, obj *models.CurrentShowdonParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CurrentShowdonParticipant_recentOpponent(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RecentOpponent, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CurrentShowdonParticipant_recentOpponent(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CurrentShowdonParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CurrentShowdonParticipant_totalScore(ctx context.Context, field graphql.CollectedField, obj *models.CurrentShowdonParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CurrentShowdonParticipant_totalScore(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalScore, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CurrentShowdonParticipant_totalScore(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CurrentShowdonParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _CurrentShowdonParticipant_currentRound(ctx context.Context, field graphql.CollectedField, obj *models.CurrentShowdonParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_CurrentShowdonParticipant_currentRound(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CurrentRound, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.ShowdownRound)
	fc.Result = res
	return ec.marshalOShowdownRound2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownRound(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_CurrentShowdonParticipant_currentRound(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "CurrentShowdonParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "opponent":
				return ec.fieldContext_ShowdownRound_opponent(ctx, field)
			case "round":
				return ec.fieldContext_ShowdownRound_round(ctx, field)
			case "score":
				return ec.fieldContext_ShowdownRound_score(ctx, field)
			case "games":
				return ec.fieldContext_ShowdownRound_games(ctx, field)
			case "wins":
				return ec.fieldContext_ShowdownRound_wins(ctx, field)
			case "loose":
				return ec.fieldContext_ShowdownRound_loose(ctx, field)
			case "totalGamesPlayed":
				return ec.fieldContext_ShowdownRound_totalGamesPlayed(ctx, field)
			case "playerStatus":
				return ec.fieldContext_ShowdownRound_playerStatus(ctx, field)
			case "isBye":
				return ec.fieldContext_ShowdownRound_isBye(ctx, field)
			case "isRoundEnded":
				return ec.fieldContext_ShowdownRound_isRoundEnded(ctx, field)
			case "hasJoined":
				return ec.fieldContext_ShowdownRound_hasJoined(ctx, field)
			case "hasOpponentNotShown":
				return ec.fieldContext_ShowdownRound_hasOpponentNotShown(ctx, field)
			case "hasFailedToPlay":
				return ec.fieldContext_ShowdownRound_hasFailedToPlay(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ShowdownRound", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Fictures_id(ctx context.Context, field graphql.CollectedField, obj *models.Fictures) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Fictures_id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Fictures_id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Fictures",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Fictures_showdownId(ctx context.Context, field graphql.CollectedField, obj *models.Fictures) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Fictures_showdownId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ShowdownID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Fictures_showdownId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Fictures",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Fictures_users(ctx context.Context, field graphql.CollectedField, obj *models.Fictures) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Fictures_users(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Users, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.ShowdownParticipantDetail)
	fc.Result = res
	return ec.marshalNShowdownParticipantDetail2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownParticipantDetail(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Fictures_users(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Fictures",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "showdownParticipant":
				return ec.fieldContext_ShowdownParticipantDetail_showdownParticipant(ctx, field)
			case "currentRound":
				return ec.fieldContext_ShowdownParticipantDetail_currentRound(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ShowdownParticipantDetail", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Fictures_round(ctx context.Context, field graphql.CollectedField, obj *models.Fictures) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Fictures_round(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Round, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Fictures_round(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Fictures",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Fictures_participants(ctx context.Context, field graphql.CollectedField, obj *models.Fictures) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Fictures_participants(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Participants, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2ᚕgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectIDᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Fictures_participants(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Fictures",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _FicturesCollection_currentUserFicture(ctx context.Context, field graphql.CollectedField, obj *models.FicturesCollection) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_FicturesCollection_currentUserFicture(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CurrentUserFicture, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.Fictures)
	fc.Result = res
	return ec.marshalOFictures2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFictures(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_FicturesCollection_currentUserFicture(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "FicturesCollection",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_Fictures_id(ctx, field)
			case "showdownId":
				return ec.fieldContext_Fictures_showdownId(ctx, field)
			case "users":
				return ec.fieldContext_Fictures_users(ctx, field)
			case "round":
				return ec.fieldContext_Fictures_round(ctx, field)
			case "participants":
				return ec.fieldContext_Fictures_participants(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Fictures", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _HostInfo_id(ctx context.Context, field graphql.CollectedField, obj *models.HostInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_HostInfo_id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_HostInfo_id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "HostInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _HostInfo_hostType(ctx context.Context, field graphql.CollectedField, obj *models.HostInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_HostInfo_hostType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HostType, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.HostType)
	fc.Result = res
	return ec.marshalOHOST_TYPE2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐHostType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_HostInfo_hostType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "HostInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type HOST_TYPE does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _HostInfo_hostLogo(ctx context.Context, field graphql.CollectedField, obj *models.HostInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_HostInfo_hostLogo(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HostLogo, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_HostInfo_hostLogo(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "HostInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderParticipantEntity__id(ctx context.Context, field graphql.CollectedField, obj *models.LeaderParticipantEntity) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderParticipantEntity__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderParticipantEntity__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderParticipantEntity",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderParticipantEntity_participant(ctx context.Context, field graphql.CollectedField, obj *models.LeaderParticipantEntity) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderParticipantEntity_participant(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Participant, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.ParticipantBasicInfo)
	fc.Result = res
	return ec.marshalNParticipantBasicInfo2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐParticipantBasicInfo(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderParticipantEntity_participant(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderParticipantEntity",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_ParticipantBasicInfo__id(ctx, field)
			case "userID":
				return ec.fieldContext_ParticipantBasicInfo_userID(ctx, field)
			case "showdownId":
				return ec.fieldContext_ParticipantBasicInfo_showdownId(ctx, field)
			case "rounds":
				return ec.fieldContext_ParticipantBasicInfo_rounds(ctx, field)
			case "userInfo":
				return ec.fieldContext_ParticipantBasicInfo_userInfo(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ParticipantBasicInfo", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderParticipantEntity_score(ctx context.Context, field graphql.CollectedField, obj *models.LeaderParticipantEntity) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderParticipantEntity_score(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Score, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderParticipantEntity_score(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderParticipantEntity",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderParticipantEntity_rank(ctx context.Context, field graphql.CollectedField, obj *models.LeaderParticipantEntity) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderParticipantEntity_rank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderParticipantEntity_rank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderParticipantEntity",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderParticipantEntity_showdownId(ctx context.Context, field graphql.CollectedField, obj *models.LeaderParticipantEntity) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderParticipantEntity_showdownId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ShowdownId, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderParticipantEntity_showdownId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderParticipantEntity",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _LeaderParticipantEntity_userId(ctx context.Context, field graphql.CollectedField, obj *models.LeaderParticipantEntity) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_LeaderParticipantEntity_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserId, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_LeaderParticipantEntity_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "LeaderParticipantEntity",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PaginatedLeaderboard_participants(ctx context.Context, field graphql.CollectedField, obj *models.PaginatedLeaderboard) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PaginatedLeaderboard_participants(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Participants, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.LeaderParticipantEntity)
	fc.Result = res
	return ec.marshalNLeaderParticipantEntity2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeaderParticipantEntity(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PaginatedLeaderboard_participants(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PaginatedLeaderboard",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_LeaderParticipantEntity__id(ctx, field)
			case "participant":
				return ec.fieldContext_LeaderParticipantEntity_participant(ctx, field)
			case "score":
				return ec.fieldContext_LeaderParticipantEntity_score(ctx, field)
			case "rank":
				return ec.fieldContext_LeaderParticipantEntity_rank(ctx, field)
			case "showdownId":
				return ec.fieldContext_LeaderParticipantEntity_showdownId(ctx, field)
			case "userId":
				return ec.fieldContext_LeaderParticipantEntity_userId(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type LeaderParticipantEntity", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PaginatedLeaderboard_count(ctx context.Context, field graphql.CollectedField, obj *models.PaginatedLeaderboard) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PaginatedLeaderboard_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Count, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PaginatedLeaderboard_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PaginatedLeaderboard",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PaginatedShowdowns_showdowns(ctx context.Context, field graphql.CollectedField, obj *models.PaginatedShowdowns) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PaginatedShowdowns_showdowns(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Showdowns, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.Showdown)
	fc.Result = res
	return ec.marshalNShowdown2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PaginatedShowdowns_showdowns(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PaginatedShowdowns",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_Showdown__id(ctx, field)
			case "clubId":
				return ec.fieldContext_Showdown_clubId(ctx, field)
			case "name":
				return ec.fieldContext_Showdown_name(ctx, field)
			case "description":
				return ec.fieldContext_Showdown_description(ctx, field)
			case "Instructions":
				return ec.fieldContext_Showdown_Instructions(ctx, field)
			case "hostedBy":
				return ec.fieldContext_Showdown_hostedBy(ctx, field)
			case "isRatedEvent":
				return ec.fieldContext_Showdown_isRatedEvent(ctx, field)
			case "rounds":
				return ec.fieldContext_Showdown_rounds(ctx, field)
			case "startTime":
				return ec.fieldContext_Showdown_startTime(ctx, field)
			case "endTime":
				return ec.fieldContext_Showdown_endTime(ctx, field)
			case "registrationCount":
				return ec.fieldContext_Showdown_registrationCount(ctx, field)
			case "registrationStartTime":
				return ec.fieldContext_Showdown_registrationStartTime(ctx, field)
			case "registrationEndTime":
				return ec.fieldContext_Showdown_registrationEndTime(ctx, field)
			case "registrationForm":
				return ec.fieldContext_Showdown_registrationForm(ctx, field)
			case "currentRound":
				return ec.fieldContext_Showdown_currentRound(ctx, field)
			case "details":
				return ec.fieldContext_Showdown_details(ctx, field)
			case "duration":
				return ec.fieldContext_Showdown_duration(ctx, field)
			case "roundTime":
				return ec.fieldContext_Showdown_roundTime(ctx, field)
			case "gapBwRounds":
				return ec.fieldContext_Showdown_gapBwRounds(ctx, field)
			case "status":
				return ec.fieldContext_Showdown_status(ctx, field)
			case "roundConfig":
				return ec.fieldContext_Showdown_roundConfig(ctx, field)
			case "stats":
				return ec.fieldContext_Showdown_stats(ctx, field)
			case "currentUserParticipation":
				return ec.fieldContext_Showdown_currentUserParticipation(ctx, field)
			case "createdAt":
				return ec.fieldContext_Showdown_createdAt(ctx, field)
			case "updatedAt":
				return ec.fieldContext_Showdown_updatedAt(ctx, field)
			case "recentParticipants":
				return ec.fieldContext_Showdown_recentParticipants(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Showdown", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PaginatedShowdowns_count(ctx context.Context, field graphql.CollectedField, obj *models.PaginatedShowdowns) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PaginatedShowdowns_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Count, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PaginatedShowdowns_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PaginatedShowdowns",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ParticipantBasicInfo__id(ctx context.Context, field graphql.CollectedField, obj *models.ParticipantBasicInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ParticipantBasicInfo__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ParticipantBasicInfo__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ParticipantBasicInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ParticipantBasicInfo_userID(ctx context.Context, field graphql.CollectedField, obj *models.ParticipantBasicInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ParticipantBasicInfo_userID(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ParticipantBasicInfo_userID(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ParticipantBasicInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ParticipantBasicInfo_showdownId(ctx context.Context, field graphql.CollectedField, obj *models.ParticipantBasicInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ParticipantBasicInfo_showdownId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ShowdownID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ParticipantBasicInfo_showdownId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ParticipantBasicInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ParticipantBasicInfo_rounds(ctx context.Context, field graphql.CollectedField, obj *models.ParticipantBasicInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ParticipantBasicInfo_rounds(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rounds, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.ShowdownRound)
	fc.Result = res
	return ec.marshalNShowdownRound2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownRound(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ParticipantBasicInfo_rounds(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ParticipantBasicInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "opponent":
				return ec.fieldContext_ShowdownRound_opponent(ctx, field)
			case "round":
				return ec.fieldContext_ShowdownRound_round(ctx, field)
			case "score":
				return ec.fieldContext_ShowdownRound_score(ctx, field)
			case "games":
				return ec.fieldContext_ShowdownRound_games(ctx, field)
			case "wins":
				return ec.fieldContext_ShowdownRound_wins(ctx, field)
			case "loose":
				return ec.fieldContext_ShowdownRound_loose(ctx, field)
			case "totalGamesPlayed":
				return ec.fieldContext_ShowdownRound_totalGamesPlayed(ctx, field)
			case "playerStatus":
				return ec.fieldContext_ShowdownRound_playerStatus(ctx, field)
			case "isBye":
				return ec.fieldContext_ShowdownRound_isBye(ctx, field)
			case "isRoundEnded":
				return ec.fieldContext_ShowdownRound_isRoundEnded(ctx, field)
			case "hasJoined":
				return ec.fieldContext_ShowdownRound_hasJoined(ctx, field)
			case "hasOpponentNotShown":
				return ec.fieldContext_ShowdownRound_hasOpponentNotShown(ctx, field)
			case "hasFailedToPlay":
				return ec.fieldContext_ShowdownRound_hasFailedToPlay(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ShowdownRound", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ParticipantBasicInfo_userInfo(ctx context.Context, field graphql.CollectedField, obj *models.ParticipantBasicInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ParticipantBasicInfo_userInfo(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserInfo, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.ShowdownUserDetails)
	fc.Result = res
	return ec.marshalOShowdownUserDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownUserDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ParticipantBasicInfo_userInfo(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ParticipantBasicInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "name":
				return ec.fieldContext_ShowdownUserDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_ShowdownUserDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_ShowdownUserDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_ShowdownUserDetails_rating(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ShowdownUserDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _RoundConfig_gameDuration(ctx context.Context, field graphql.CollectedField, obj *models.RoundConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RoundConfig_gameDuration(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GameDuration, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RoundConfig_gameDuration(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RoundConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _RoundConfig_numOfPlayer(ctx context.Context, field graphql.CollectedField, obj *models.RoundConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RoundConfig_numOfPlayer(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NumOfPlayer, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RoundConfig_numOfPlayer(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RoundConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _RoundConfig_gameType(ctx context.Context, field graphql.CollectedField, obj *models.RoundConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RoundConfig_gameType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GameType, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.GameType)
	fc.Result = res
	return ec.marshalNGAME_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RoundConfig_gameType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RoundConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type GAME_TYPE does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _RoundConfig_maxGapBwGame(ctx context.Context, field graphql.CollectedField, obj *models.RoundConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RoundConfig_maxGapBwGame(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.MaxGapBwGame, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RoundConfig_maxGapBwGame(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RoundConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _RoundConfig_maxWaitTime(ctx context.Context, field graphql.CollectedField, obj *models.RoundConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RoundConfig_maxWaitTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.MaxWaitTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RoundConfig_maxWaitTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RoundConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _RoundConfig_numOfGames(ctx context.Context, field graphql.CollectedField, obj *models.RoundConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RoundConfig_numOfGames(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NumOfGames, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RoundConfig_numOfGames(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RoundConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown__id(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_clubId(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_clubId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ClubID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_clubId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_name(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_description(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_description(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Description, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_description(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_Instructions(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_Instructions(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Instructions, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*string)
	fc.Result = res
	return ec.marshalOString2ᚕᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_Instructions(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_hostedBy(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_hostedBy(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HostedBy, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.HostInfo)
	fc.Result = res
	return ec.marshalOHostInfo2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐHostInfo(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_hostedBy(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_HostInfo_id(ctx, field)
			case "hostType":
				return ec.fieldContext_HostInfo_hostType(ctx, field)
			case "hostLogo":
				return ec.fieldContext_HostInfo_hostLogo(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type HostInfo", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_isRatedEvent(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_isRatedEvent(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsRatedEvent, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_isRatedEvent(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_rounds(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_rounds(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rounds, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_rounds(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_startTime(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_startTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StartTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_startTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_endTime(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_endTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EndTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_endTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_registrationCount(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_registrationCount(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RegistrationCount, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_registrationCount(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_registrationStartTime(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_registrationStartTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RegistrationStartTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_registrationStartTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_registrationEndTime(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_registrationEndTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RegistrationEndTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_registrationEndTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_registrationForm(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_registrationForm(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RegistrationForm, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.RegistrationForm)
	fc.Result = res
	return ec.marshalORegistrationForm2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationForm(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_registrationForm(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_RegistrationForm__id(ctx, field)
			case "fields":
				return ec.fieldContext_RegistrationForm_fields(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type RegistrationForm", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_currentRound(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_currentRound(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CurrentRound, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_currentRound(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_details(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_details(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Details, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.ShowdownDetails)
	fc.Result = res
	return ec.marshalNshowdownDetails2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_details(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "format":
				return ec.fieldContext_showdownDetails_format(ctx, field)
			case "rules":
				return ec.fieldContext_showdownDetails_rules(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type showdownDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_duration(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_duration(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Duration, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_duration(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_roundTime(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_roundTime(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RoundTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_roundTime(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_gapBwRounds(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_gapBwRounds(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GapBwRounds, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_gapBwRounds(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_status(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_status(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Status, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.ShowdownContestStatus)
	fc.Result = res
	return ec.marshalNSHOWDOWN_CONTEST_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownContestStatus(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_status(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type SHOWDOWN_CONTEST_STATUS does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_roundConfig(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_roundConfig(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RoundConfig, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.RoundConfig)
	fc.Result = res
	return ec.marshalNRoundConfig2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRoundConfig(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_roundConfig(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "gameDuration":
				return ec.fieldContext_RoundConfig_gameDuration(ctx, field)
			case "numOfPlayer":
				return ec.fieldContext_RoundConfig_numOfPlayer(ctx, field)
			case "gameType":
				return ec.fieldContext_RoundConfig_gameType(ctx, field)
			case "maxGapBwGame":
				return ec.fieldContext_RoundConfig_maxGapBwGame(ctx, field)
			case "maxWaitTime":
				return ec.fieldContext_RoundConfig_maxWaitTime(ctx, field)
			case "numOfGames":
				return ec.fieldContext_RoundConfig_numOfGames(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type RoundConfig", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_stats(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_stats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Stats, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.ShowdownStats)
	fc.Result = res
	return ec.marshalOShowdownStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownStats(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_stats(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "totalParticipants":
				return ec.fieldContext_ShowdownStats_totalParticipants(ctx, field)
			case "totalGamesPlayed":
				return ec.fieldContext_ShowdownStats_totalGamesPlayed(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ShowdownStats", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_currentUserParticipation(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_currentUserParticipation(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CurrentUserParticipation, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.CurrentShowdonParticipant)
	fc.Result = res
	return ec.marshalOCurrentShowdonParticipant2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCurrentShowdonParticipant(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_currentUserParticipation(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "showdownId":
				return ec.fieldContext_CurrentShowdonParticipant_showdownId(ctx, field)
			case "userId":
				return ec.fieldContext_CurrentShowdonParticipant_userId(ctx, field)
			case "registrationData":
				return ec.fieldContext_CurrentShowdonParticipant_registrationData(ctx, field)
			case "lastSubmissionTime":
				return ec.fieldContext_CurrentShowdonParticipant_lastSubmissionTime(ctx, field)
			case "stats":
				return ec.fieldContext_CurrentShowdonParticipant_stats(ctx, field)
			case "rank":
				return ec.fieldContext_CurrentShowdonParticipant_rank(ctx, field)
			case "rounds":
				return ec.fieldContext_CurrentShowdonParticipant_rounds(ctx, field)
			case "hadABye":
				return ec.fieldContext_CurrentShowdonParticipant_hadABye(ctx, field)
			case "currentGame":
				return ec.fieldContext_CurrentShowdonParticipant_currentGame(ctx, field)
			case "recentOpponent":
				return ec.fieldContext_CurrentShowdonParticipant_recentOpponent(ctx, field)
			case "totalScore":
				return ec.fieldContext_CurrentShowdonParticipant_totalScore(ctx, field)
			case "currentRound":
				return ec.fieldContext_CurrentShowdonParticipant_currentRound(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type CurrentShowdonParticipant", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_createdAt(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_createdAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_createdAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_updatedAt(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_updatedAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UpdatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_updatedAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Showdown_recentParticipants(ctx context.Context, field graphql.CollectedField, obj *models.Showdown) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Showdown_recentParticipants(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RecentParticipants, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.UserPublicDetails)
	fc.Result = res
	return ec.marshalOUserPublicDetails2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetailsᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Showdown_recentParticipants(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Showdown",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPublicDetails__id(ctx, field)
			case "name":
				return ec.fieldContext_UserPublicDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_UserPublicDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserPublicDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserPublicDetails_rating(ctx, field)
			case "badge":
				return ec.fieldContext_UserPublicDetails_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_UserPublicDetails_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_UserPublicDetails_isGuest(ctx, field)
			case "globalRank":
				return ec.fieldContext_UserPublicDetails_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_UserPublicDetails_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_UserPublicDetails_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_UserPublicDetails_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_UserPublicDetails_stats(ctx, field)
			case "userStreaks":
				return ec.fieldContext_UserPublicDetails_userStreaks(ctx, field)
			case "ratingV2":
				return ec.fieldContext_UserPublicDetails_ratingV2(ctx, field)
			case "institutionId":
				return ec.fieldContext_UserPublicDetails_institutionId(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPublicDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownGameConfig_isRoundEnded(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownGameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownGameConfig_isRoundEnded(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsRoundEnded, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownGameConfig_isRoundEnded(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownGameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownGameConfig_hasOpponentNotShown(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownGameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownGameConfig_hasOpponentNotShown(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasOpponentNotShown, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownGameConfig_hasOpponentNotShown(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownGameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownGameConfig_nextGameId(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownGameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownGameConfig_nextGameId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NextGameID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownGameConfig_nextGameId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownGameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownGameConfig_round(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownGameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownGameConfig_round(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Round, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownGameConfig_round(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownGameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownGameConfig_nextGameStartsAt(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownGameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownGameConfig_nextGameStartsAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NextGameStartsAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚖtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownGameConfig_nextGameStartsAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownGameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownGameConfig_totalGamesPlayed(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownGameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownGameConfig_totalGamesPlayed(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalGamesPlayed, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownGameConfig_totalGamesPlayed(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownGameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownGameConfig_showdownGamePlayer(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownGameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownGameConfig_showdownGamePlayer(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ShowdownGamePlayer, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.ShowdownGamePlayer)
	fc.Result = res
	return ec.marshalNShowdownGamePlayer2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownGamePlayer(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownGameConfig_showdownGamePlayer(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownGameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "isTie":
				return ec.fieldContext_ShowdownGamePlayer_isTie(ctx, field)
			case "isWinner":
				return ec.fieldContext_ShowdownGamePlayer_isWinner(ctx, field)
			case "userId":
				return ec.fieldContext_ShowdownGamePlayer_userId(ctx, field)
			case "wins":
				return ec.fieldContext_ShowdownGamePlayer_wins(ctx, field)
			case "score":
				return ec.fieldContext_ShowdownGamePlayer_score(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ShowdownGamePlayer", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownGameConfig_numOfGames(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownGameConfig) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownGameConfig_numOfGames(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.NumOfGames, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownGameConfig_numOfGames(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownGameConfig",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownGamePlayer_isTie(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownGamePlayer) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownGamePlayer_isTie(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsTie, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownGamePlayer_isTie(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownGamePlayer",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownGamePlayer_isWinner(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownGamePlayer) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownGamePlayer_isWinner(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsWinner, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownGamePlayer_isWinner(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownGamePlayer",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownGamePlayer_userId(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownGamePlayer) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownGamePlayer_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownGamePlayer_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownGamePlayer",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownGamePlayer_wins(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownGamePlayer) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownGamePlayer_wins(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Wins, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownGamePlayer_wins(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownGamePlayer",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownGamePlayer_score(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownGamePlayer) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownGamePlayer_score(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Score, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownGamePlayer_score(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownGamePlayer",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipant__id(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipant__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipant__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipant_userID(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipant_userID(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipant_userID(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipant_showdownId(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipant_showdownId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ShowdownID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipant_showdownId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipant_status(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipant_status(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Status, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.ShowdownParticipantStatus)
	fc.Result = res
	return ec.marshalNSHOWDOWN_PARTICIPANT_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownParticipantStatus(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipant_status(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type SHOWDOWN_PARTICIPANT_STATUS does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipant_registrationData(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipant_registrationData(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RegistrationData, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]*models.RegistrationFieldData)
	fc.Result = res
	return ec.marshalORegistrationFieldData2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFieldData(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipant_registrationData(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "name":
				return ec.fieldContext_RegistrationFieldData_name(ctx, field)
			case "values":
				return ec.fieldContext_RegistrationFieldData_values(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type RegistrationFieldData", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipant_stats(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipant_stats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Stats, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.ShowdownParticipantStats)
	fc.Result = res
	return ec.marshalOShowdownParticipantStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownParticipantStats(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipant_stats(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "currentScore":
				return ec.fieldContext_ShowdownParticipantStats_currentScore(ctx, field)
			case "win":
				return ec.fieldContext_ShowdownParticipantStats_win(ctx, field)
			case "loss":
				return ec.fieldContext_ShowdownParticipantStats_loss(ctx, field)
			case "draw":
				return ec.fieldContext_ShowdownParticipantStats_draw(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ShowdownParticipantStats", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipant_rounds(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipant_rounds(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rounds, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.ShowdownRound)
	fc.Result = res
	return ec.marshalNShowdownRound2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownRound(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipant_rounds(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "opponent":
				return ec.fieldContext_ShowdownRound_opponent(ctx, field)
			case "round":
				return ec.fieldContext_ShowdownRound_round(ctx, field)
			case "score":
				return ec.fieldContext_ShowdownRound_score(ctx, field)
			case "games":
				return ec.fieldContext_ShowdownRound_games(ctx, field)
			case "wins":
				return ec.fieldContext_ShowdownRound_wins(ctx, field)
			case "loose":
				return ec.fieldContext_ShowdownRound_loose(ctx, field)
			case "totalGamesPlayed":
				return ec.fieldContext_ShowdownRound_totalGamesPlayed(ctx, field)
			case "playerStatus":
				return ec.fieldContext_ShowdownRound_playerStatus(ctx, field)
			case "isBye":
				return ec.fieldContext_ShowdownRound_isBye(ctx, field)
			case "isRoundEnded":
				return ec.fieldContext_ShowdownRound_isRoundEnded(ctx, field)
			case "hasJoined":
				return ec.fieldContext_ShowdownRound_hasJoined(ctx, field)
			case "hasOpponentNotShown":
				return ec.fieldContext_ShowdownRound_hasOpponentNotShown(ctx, field)
			case "hasFailedToPlay":
				return ec.fieldContext_ShowdownRound_hasFailedToPlay(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ShowdownRound", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipant_recentOpponents(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipant_recentOpponents(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RecentOpponents, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2ᚕgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectIDᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipant_recentOpponents(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipant_rank(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipant_rank(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rank, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipant_rank(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipant_totalScore(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipant_totalScore(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalScore, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipant_totalScore(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipant_ratingChange(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipant_ratingChange(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RatingChange, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipant_ratingChange(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipant_hadABye(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipant_hadABye(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HadABye, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipant_hadABye(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipant_createdAt(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipant_createdAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipant_createdAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipant_updatedAt(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipant_updatedAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UpdatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipant_updatedAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipant_userInfo(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipant) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipant_userInfo(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserInfo, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.ShowdownUserDetails)
	fc.Result = res
	return ec.marshalOShowdownUserDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownUserDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipant_userInfo(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipant",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "name":
				return ec.fieldContext_ShowdownUserDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_ShowdownUserDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_ShowdownUserDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_ShowdownUserDetails_rating(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ShowdownUserDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipantDetail_showdownParticipant(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipantDetail) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipantDetail_showdownParticipant(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ShowdownParticipant, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(models.ShowdownParticipant)
	fc.Result = res
	return ec.marshalOShowdownParticipant2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownParticipant(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipantDetail_showdownParticipant(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipantDetail",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_ShowdownParticipant__id(ctx, field)
			case "userID":
				return ec.fieldContext_ShowdownParticipant_userID(ctx, field)
			case "showdownId":
				return ec.fieldContext_ShowdownParticipant_showdownId(ctx, field)
			case "status":
				return ec.fieldContext_ShowdownParticipant_status(ctx, field)
			case "registrationData":
				return ec.fieldContext_ShowdownParticipant_registrationData(ctx, field)
			case "stats":
				return ec.fieldContext_ShowdownParticipant_stats(ctx, field)
			case "rounds":
				return ec.fieldContext_ShowdownParticipant_rounds(ctx, field)
			case "recentOpponents":
				return ec.fieldContext_ShowdownParticipant_recentOpponents(ctx, field)
			case "rank":
				return ec.fieldContext_ShowdownParticipant_rank(ctx, field)
			case "totalScore":
				return ec.fieldContext_ShowdownParticipant_totalScore(ctx, field)
			case "ratingChange":
				return ec.fieldContext_ShowdownParticipant_ratingChange(ctx, field)
			case "hadABye":
				return ec.fieldContext_ShowdownParticipant_hadABye(ctx, field)
			case "createdAt":
				return ec.fieldContext_ShowdownParticipant_createdAt(ctx, field)
			case "updatedAt":
				return ec.fieldContext_ShowdownParticipant_updatedAt(ctx, field)
			case "userInfo":
				return ec.fieldContext_ShowdownParticipant_userInfo(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ShowdownParticipant", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipantDetail_currentRound(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipantDetail) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipantDetail_currentRound(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CurrentRound, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.ShowdownRound)
	fc.Result = res
	return ec.marshalNShowdownRound2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownRound(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipantDetail_currentRound(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipantDetail",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "opponent":
				return ec.fieldContext_ShowdownRound_opponent(ctx, field)
			case "round":
				return ec.fieldContext_ShowdownRound_round(ctx, field)
			case "score":
				return ec.fieldContext_ShowdownRound_score(ctx, field)
			case "games":
				return ec.fieldContext_ShowdownRound_games(ctx, field)
			case "wins":
				return ec.fieldContext_ShowdownRound_wins(ctx, field)
			case "loose":
				return ec.fieldContext_ShowdownRound_loose(ctx, field)
			case "totalGamesPlayed":
				return ec.fieldContext_ShowdownRound_totalGamesPlayed(ctx, field)
			case "playerStatus":
				return ec.fieldContext_ShowdownRound_playerStatus(ctx, field)
			case "isBye":
				return ec.fieldContext_ShowdownRound_isBye(ctx, field)
			case "isRoundEnded":
				return ec.fieldContext_ShowdownRound_isRoundEnded(ctx, field)
			case "hasJoined":
				return ec.fieldContext_ShowdownRound_hasJoined(ctx, field)
			case "hasOpponentNotShown":
				return ec.fieldContext_ShowdownRound_hasOpponentNotShown(ctx, field)
			case "hasFailedToPlay":
				return ec.fieldContext_ShowdownRound_hasFailedToPlay(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ShowdownRound", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipantStats_currentScore(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipantStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipantStats_currentScore(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CurrentScore, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipantStats_currentScore(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipantStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipantStats_win(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipantStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipantStats_win(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Win, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipantStats_win(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipantStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipantStats_loss(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipantStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipantStats_loss(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Loss, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipantStats_loss(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipantStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownParticipantStats_draw(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownParticipantStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownParticipantStats_draw(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Draw, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownParticipantStats_draw(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownParticipantStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownRound_opponent(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownRound) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownRound_opponent(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Opponent, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownRound_opponent(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownRound",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownRound_round(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownRound) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownRound_round(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Round, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownRound_round(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownRound",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownRound_score(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownRound) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownRound_score(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Score, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownRound_score(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownRound",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownRound_games(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownRound) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownRound_games(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Games, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2ᚕgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectIDᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownRound_games(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownRound",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownRound_wins(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownRound) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownRound_wins(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Wins, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownRound_wins(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownRound",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownRound_loose(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownRound) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownRound_loose(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Loose, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownRound_loose(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownRound",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownRound_totalGamesPlayed(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownRound) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownRound_totalGamesPlayed(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalGamesPlayed, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownRound_totalGamesPlayed(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownRound",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownRound_playerStatus(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownRound) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownRound_playerStatus(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PlayerStatus, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.RoundPlayerStatus)
	fc.Result = res
	return ec.marshalNRoundPlayerStatus2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRoundPlayerStatus(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownRound_playerStatus(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownRound",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type RoundPlayerStatus does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownRound_isBye(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownRound) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownRound_isBye(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsBye, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownRound_isBye(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownRound",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownRound_isRoundEnded(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownRound) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownRound_isRoundEnded(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsRoundEnded, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownRound_isRoundEnded(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownRound",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownRound_hasJoined(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownRound) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownRound_hasJoined(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasJoined, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownRound_hasJoined(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownRound",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownRound_hasOpponentNotShown(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownRound) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownRound_hasOpponentNotShown(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasOpponentNotShown, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownRound_hasOpponentNotShown(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownRound",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownRound_hasFailedToPlay(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownRound) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownRound_hasFailedToPlay(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasFailedToPlay, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownRound_hasFailedToPlay(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownRound",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownStats_totalParticipants(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownStats_totalParticipants(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalParticipants, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownStats_totalParticipants(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownStats_totalGamesPlayed(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownStats_totalGamesPlayed(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalGamesPlayed, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownStats_totalGamesPlayed(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownUserDetails_name(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownUserDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownUserDetails_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownUserDetails_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownUserDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownUserDetails_username(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownUserDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownUserDetails_username(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Username, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownUserDetails_username(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownUserDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownUserDetails_profileImageUrl(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownUserDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownUserDetails_profileImageUrl(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ProfileImageURL, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownUserDetails_profileImageUrl(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownUserDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ShowdownUserDetails_rating(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownUserDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ShowdownUserDetails_rating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ShowdownUserDetails_rating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ShowdownUserDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _showdownDetails_format(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_showdownDetails_format(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Format, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_showdownDetails_format(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "showdownDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _showdownDetails_rules(ctx context.Context, field graphql.CollectedField, obj *models.ShowdownDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_showdownDetails_rules(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rules, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_showdownDetails_rules(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "showdownDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

// endregion **************************** field.gotpl *****************************

// region    **************************** input.gotpl *****************************

func (ec *executionContext) unmarshalInputCreateShowdownInput(ctx context.Context, obj any) (models.CreateShowdownInput, error) {
	var it models.CreateShowdownInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"name", "description", "hostedBy", "isRatedEvent", "startTime", "details", "rounds", "gapBwRounds", "registrationStartTime", "registrationEndTime", "roundConfig", "registrationForm"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "name":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("name"))
			data, err := ec.unmarshalNString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Name = data
		case "description":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("description"))
			data, err := ec.unmarshalNString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Description = data
		case "hostedBy":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("hostedBy"))
			data, err := ec.unmarshalNHostInfoInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐHostInfoInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.HostedBy = data
		case "isRatedEvent":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("isRatedEvent"))
			data, err := ec.unmarshalOBoolean2ᚖbool(ctx, v)
			if err != nil {
				return it, err
			}
			it.IsRatedEvent = data
		case "startTime":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("startTime"))
			data, err := ec.unmarshalNTime2timeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.StartTime = data
		case "details":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("details"))
			data, err := ec.unmarshalNShowdownDetailsInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownDetailsInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.Details = data
		case "rounds":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("rounds"))
			data, err := ec.unmarshalNInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.Rounds = data
		case "gapBwRounds":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("gapBwRounds"))
			data, err := ec.unmarshalNInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.GapBwRounds = data
		case "registrationStartTime":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("registrationStartTime"))
			data, err := ec.unmarshalNTime2timeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.RegistrationStartTime = data
		case "registrationEndTime":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("registrationEndTime"))
			data, err := ec.unmarshalNTime2timeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.RegistrationEndTime = data
		case "roundConfig":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("roundConfig"))
			data, err := ec.unmarshalNRoundConfigInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRoundConfigInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.RoundConfig = data
		case "registrationForm":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("registrationForm"))
			data, err := ec.unmarshalNRegistrationFormInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRegistrationFormInput(ctx, v)
			if err != nil {
				return it, err
			}
			it.RegistrationForm = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputHostInfoInput(ctx context.Context, obj any) (models.HostInfoInput, error) {
	var it models.HostInfoInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"id", "hostType", "hostLogo"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "id":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("id"))
			data, err := ec.unmarshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.ID = data
		case "hostType":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("hostType"))
			data, err := ec.unmarshalOHOST_TYPE2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐHostType(ctx, v)
			if err != nil {
				return it, err
			}
			it.HostType = data
		case "hostLogo":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("hostLogo"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.HostLogo = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputPaginatedLeaderboardInput(ctx context.Context, obj any) (models.PaginatedLeaderboardInput, error) {
	var it models.PaginatedLeaderboardInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"showdownId", "page"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "showdownId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("showdownId"))
			data, err := ec.unmarshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.ShowdownID = data
		case "page":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("page"))
			data, err := ec.unmarshalNInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.Page = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputRoundConfigInput(ctx context.Context, obj any) (models.RoundConfigInput, error) {
	var it models.RoundConfigInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"gameDuration", "numOfPlayer", "gameType", "maxGapBwGame", "maxWaitTime", "numOfGames"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "gameDuration":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("gameDuration"))
			data, err := ec.unmarshalNInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.GameDuration = data
		case "numOfPlayer":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("numOfPlayer"))
			data, err := ec.unmarshalNInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.NumOfPlayer = data
		case "gameType":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("gameType"))
			data, err := ec.unmarshalNGAME_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐGameType(ctx, v)
			if err != nil {
				return it, err
			}
			it.GameType = data
		case "maxGapBwGame":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("maxGapBwGame"))
			data, err := ec.unmarshalNInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.MaxGapBwGame = data
		case "maxWaitTime":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("maxWaitTime"))
			data, err := ec.unmarshalNInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.MaxWaitTime = data
		case "numOfGames":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("numOfGames"))
			data, err := ec.unmarshalNInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.NumOfGames = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputShowdownDetailsInput(ctx context.Context, obj any) (models.ShowdownDetailsInput, error) {
	var it models.ShowdownDetailsInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"format", "rules"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "format":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("format"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Format = data
		case "rules":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("rules"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Rules = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputShowdownRegistrationFormValuesInput(ctx context.Context, obj any) (models.ShowdownRegistrationFormValuesInput, error) {
	var it models.ShowdownRegistrationFormValuesInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"showdownId"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "showdownId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("showdownId"))
			data, err := ec.unmarshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.ShowdownID = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputStartGameForShowdownInput(ctx context.Context, obj any) (models.StartGameForShowdownInput, error) {
	var it models.StartGameForShowdownInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"showdownId", "userId", "gameId"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "showdownId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("showdownId"))
			data, err := ec.unmarshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.ShowdownID = data
		case "userId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("userId"))
			data, err := ec.unmarshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.UserID = data
		case "gameId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("gameId"))
			data, err := ec.unmarshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.GameID = data
		}
	}

	return it, nil
}

// endregion **************************** input.gotpl *****************************

// region    ************************** interface.gotpl ***************************

// endregion ************************** interface.gotpl ***************************

// region    **************************** object.gotpl ****************************

var currentShowdonParticipantImplementors = []string{"CurrentShowdonParticipant"}

func (ec *executionContext) _CurrentShowdonParticipant(ctx context.Context, sel ast.SelectionSet, obj *models.CurrentShowdonParticipant) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, currentShowdonParticipantImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("CurrentShowdonParticipant")
		case "showdownId":
			out.Values[i] = ec._CurrentShowdonParticipant_showdownId(ctx, field, obj)
		case "userId":
			out.Values[i] = ec._CurrentShowdonParticipant_userId(ctx, field, obj)
		case "registrationData":
			out.Values[i] = ec._CurrentShowdonParticipant_registrationData(ctx, field, obj)
		case "lastSubmissionTime":
			out.Values[i] = ec._CurrentShowdonParticipant_lastSubmissionTime(ctx, field, obj)
		case "stats":
			out.Values[i] = ec._CurrentShowdonParticipant_stats(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "rank":
			out.Values[i] = ec._CurrentShowdonParticipant_rank(ctx, field, obj)
		case "rounds":
			out.Values[i] = ec._CurrentShowdonParticipant_rounds(ctx, field, obj)
		case "hadABye":
			out.Values[i] = ec._CurrentShowdonParticipant_hadABye(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "currentGame":
			out.Values[i] = ec._CurrentShowdonParticipant_currentGame(ctx, field, obj)
		case "recentOpponent":
			out.Values[i] = ec._CurrentShowdonParticipant_recentOpponent(ctx, field, obj)
		case "totalScore":
			out.Values[i] = ec._CurrentShowdonParticipant_totalScore(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "currentRound":
			out.Values[i] = ec._CurrentShowdonParticipant_currentRound(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var ficturesImplementors = []string{"Fictures"}

func (ec *executionContext) _Fictures(ctx context.Context, sel ast.SelectionSet, obj *models.Fictures) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, ficturesImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("Fictures")
		case "id":
			out.Values[i] = ec._Fictures_id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "showdownId":
			out.Values[i] = ec._Fictures_showdownId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "users":
			out.Values[i] = ec._Fictures_users(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "round":
			out.Values[i] = ec._Fictures_round(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "participants":
			out.Values[i] = ec._Fictures_participants(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var ficturesCollectionImplementors = []string{"FicturesCollection"}

func (ec *executionContext) _FicturesCollection(ctx context.Context, sel ast.SelectionSet, obj *models.FicturesCollection) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, ficturesCollectionImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("FicturesCollection")
		case "currentUserFicture":
			out.Values[i] = ec._FicturesCollection_currentUserFicture(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var hostInfoImplementors = []string{"HostInfo"}

func (ec *executionContext) _HostInfo(ctx context.Context, sel ast.SelectionSet, obj *models.HostInfo) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, hostInfoImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("HostInfo")
		case "id":
			out.Values[i] = ec._HostInfo_id(ctx, field, obj)
		case "hostType":
			out.Values[i] = ec._HostInfo_hostType(ctx, field, obj)
		case "hostLogo":
			out.Values[i] = ec._HostInfo_hostLogo(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var leaderParticipantEntityImplementors = []string{"LeaderParticipantEntity"}

func (ec *executionContext) _LeaderParticipantEntity(ctx context.Context, sel ast.SelectionSet, obj *models.LeaderParticipantEntity) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, leaderParticipantEntityImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("LeaderParticipantEntity")
		case "_id":
			out.Values[i] = ec._LeaderParticipantEntity__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "participant":
			out.Values[i] = ec._LeaderParticipantEntity_participant(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "score":
			out.Values[i] = ec._LeaderParticipantEntity_score(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "rank":
			out.Values[i] = ec._LeaderParticipantEntity_rank(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "showdownId":
			out.Values[i] = ec._LeaderParticipantEntity_showdownId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userId":
			out.Values[i] = ec._LeaderParticipantEntity_userId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var paginatedLeaderboardImplementors = []string{"PaginatedLeaderboard"}

func (ec *executionContext) _PaginatedLeaderboard(ctx context.Context, sel ast.SelectionSet, obj *models.PaginatedLeaderboard) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, paginatedLeaderboardImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PaginatedLeaderboard")
		case "participants":
			out.Values[i] = ec._PaginatedLeaderboard_participants(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "count":
			out.Values[i] = ec._PaginatedLeaderboard_count(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var paginatedShowdownsImplementors = []string{"PaginatedShowdowns"}

func (ec *executionContext) _PaginatedShowdowns(ctx context.Context, sel ast.SelectionSet, obj *models.PaginatedShowdowns) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, paginatedShowdownsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PaginatedShowdowns")
		case "showdowns":
			out.Values[i] = ec._PaginatedShowdowns_showdowns(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "count":
			out.Values[i] = ec._PaginatedShowdowns_count(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var participantBasicInfoImplementors = []string{"ParticipantBasicInfo"}

func (ec *executionContext) _ParticipantBasicInfo(ctx context.Context, sel ast.SelectionSet, obj *models.ParticipantBasicInfo) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, participantBasicInfoImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ParticipantBasicInfo")
		case "_id":
			out.Values[i] = ec._ParticipantBasicInfo__id(ctx, field, obj)
		case "userID":
			out.Values[i] = ec._ParticipantBasicInfo_userID(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "showdownId":
			out.Values[i] = ec._ParticipantBasicInfo_showdownId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "rounds":
			out.Values[i] = ec._ParticipantBasicInfo_rounds(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userInfo":
			out.Values[i] = ec._ParticipantBasicInfo_userInfo(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var roundConfigImplementors = []string{"RoundConfig"}

func (ec *executionContext) _RoundConfig(ctx context.Context, sel ast.SelectionSet, obj *models.RoundConfig) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, roundConfigImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("RoundConfig")
		case "gameDuration":
			out.Values[i] = ec._RoundConfig_gameDuration(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "numOfPlayer":
			out.Values[i] = ec._RoundConfig_numOfPlayer(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "gameType":
			out.Values[i] = ec._RoundConfig_gameType(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "maxGapBwGame":
			out.Values[i] = ec._RoundConfig_maxGapBwGame(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "maxWaitTime":
			out.Values[i] = ec._RoundConfig_maxWaitTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "numOfGames":
			out.Values[i] = ec._RoundConfig_numOfGames(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var showdownImplementors = []string{"Showdown"}

func (ec *executionContext) _Showdown(ctx context.Context, sel ast.SelectionSet, obj *models.Showdown) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, showdownImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("Showdown")
		case "_id":
			out.Values[i] = ec._Showdown__id(ctx, field, obj)
		case "clubId":
			out.Values[i] = ec._Showdown_clubId(ctx, field, obj)
		case "name":
			out.Values[i] = ec._Showdown_name(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "description":
			out.Values[i] = ec._Showdown_description(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "Instructions":
			out.Values[i] = ec._Showdown_Instructions(ctx, field, obj)
		case "hostedBy":
			out.Values[i] = ec._Showdown_hostedBy(ctx, field, obj)
		case "isRatedEvent":
			out.Values[i] = ec._Showdown_isRatedEvent(ctx, field, obj)
		case "rounds":
			out.Values[i] = ec._Showdown_rounds(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "startTime":
			out.Values[i] = ec._Showdown_startTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "endTime":
			out.Values[i] = ec._Showdown_endTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "registrationCount":
			out.Values[i] = ec._Showdown_registrationCount(ctx, field, obj)
		case "registrationStartTime":
			out.Values[i] = ec._Showdown_registrationStartTime(ctx, field, obj)
		case "registrationEndTime":
			out.Values[i] = ec._Showdown_registrationEndTime(ctx, field, obj)
		case "registrationForm":
			out.Values[i] = ec._Showdown_registrationForm(ctx, field, obj)
		case "currentRound":
			out.Values[i] = ec._Showdown_currentRound(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "details":
			out.Values[i] = ec._Showdown_details(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "duration":
			out.Values[i] = ec._Showdown_duration(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "roundTime":
			out.Values[i] = ec._Showdown_roundTime(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "gapBwRounds":
			out.Values[i] = ec._Showdown_gapBwRounds(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "status":
			out.Values[i] = ec._Showdown_status(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "roundConfig":
			out.Values[i] = ec._Showdown_roundConfig(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "stats":
			out.Values[i] = ec._Showdown_stats(ctx, field, obj)
		case "currentUserParticipation":
			out.Values[i] = ec._Showdown_currentUserParticipation(ctx, field, obj)
		case "createdAt":
			out.Values[i] = ec._Showdown_createdAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "updatedAt":
			out.Values[i] = ec._Showdown_updatedAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "recentParticipants":
			out.Values[i] = ec._Showdown_recentParticipants(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var showdownGameConfigImplementors = []string{"ShowdownGameConfig"}

func (ec *executionContext) _ShowdownGameConfig(ctx context.Context, sel ast.SelectionSet, obj *models.ShowdownGameConfig) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, showdownGameConfigImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ShowdownGameConfig")
		case "isRoundEnded":
			out.Values[i] = ec._ShowdownGameConfig_isRoundEnded(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "hasOpponentNotShown":
			out.Values[i] = ec._ShowdownGameConfig_hasOpponentNotShown(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "nextGameId":
			out.Values[i] = ec._ShowdownGameConfig_nextGameId(ctx, field, obj)
		case "round":
			out.Values[i] = ec._ShowdownGameConfig_round(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "nextGameStartsAt":
			out.Values[i] = ec._ShowdownGameConfig_nextGameStartsAt(ctx, field, obj)
		case "totalGamesPlayed":
			out.Values[i] = ec._ShowdownGameConfig_totalGamesPlayed(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "showdownGamePlayer":
			out.Values[i] = ec._ShowdownGameConfig_showdownGamePlayer(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "numOfGames":
			out.Values[i] = ec._ShowdownGameConfig_numOfGames(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var showdownGamePlayerImplementors = []string{"ShowdownGamePlayer"}

func (ec *executionContext) _ShowdownGamePlayer(ctx context.Context, sel ast.SelectionSet, obj *models.ShowdownGamePlayer) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, showdownGamePlayerImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ShowdownGamePlayer")
		case "isTie":
			out.Values[i] = ec._ShowdownGamePlayer_isTie(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "isWinner":
			out.Values[i] = ec._ShowdownGamePlayer_isWinner(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userId":
			out.Values[i] = ec._ShowdownGamePlayer_userId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "wins":
			out.Values[i] = ec._ShowdownGamePlayer_wins(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "score":
			out.Values[i] = ec._ShowdownGamePlayer_score(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var showdownParticipantImplementors = []string{"ShowdownParticipant"}

func (ec *executionContext) _ShowdownParticipant(ctx context.Context, sel ast.SelectionSet, obj *models.ShowdownParticipant) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, showdownParticipantImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ShowdownParticipant")
		case "_id":
			out.Values[i] = ec._ShowdownParticipant__id(ctx, field, obj)
		case "userID":
			out.Values[i] = ec._ShowdownParticipant_userID(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "showdownId":
			out.Values[i] = ec._ShowdownParticipant_showdownId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "status":
			out.Values[i] = ec._ShowdownParticipant_status(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "registrationData":
			out.Values[i] = ec._ShowdownParticipant_registrationData(ctx, field, obj)
		case "stats":
			out.Values[i] = ec._ShowdownParticipant_stats(ctx, field, obj)
		case "rounds":
			out.Values[i] = ec._ShowdownParticipant_rounds(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "recentOpponents":
			out.Values[i] = ec._ShowdownParticipant_recentOpponents(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "rank":
			out.Values[i] = ec._ShowdownParticipant_rank(ctx, field, obj)
		case "totalScore":
			out.Values[i] = ec._ShowdownParticipant_totalScore(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "ratingChange":
			out.Values[i] = ec._ShowdownParticipant_ratingChange(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "hadABye":
			out.Values[i] = ec._ShowdownParticipant_hadABye(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "createdAt":
			out.Values[i] = ec._ShowdownParticipant_createdAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "updatedAt":
			out.Values[i] = ec._ShowdownParticipant_updatedAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userInfo":
			out.Values[i] = ec._ShowdownParticipant_userInfo(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var showdownParticipantDetailImplementors = []string{"ShowdownParticipantDetail"}

func (ec *executionContext) _ShowdownParticipantDetail(ctx context.Context, sel ast.SelectionSet, obj *models.ShowdownParticipantDetail) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, showdownParticipantDetailImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ShowdownParticipantDetail")
		case "showdownParticipant":
			out.Values[i] = ec._ShowdownParticipantDetail_showdownParticipant(ctx, field, obj)
		case "currentRound":
			out.Values[i] = ec._ShowdownParticipantDetail_currentRound(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var showdownParticipantStatsImplementors = []string{"ShowdownParticipantStats"}

func (ec *executionContext) _ShowdownParticipantStats(ctx context.Context, sel ast.SelectionSet, obj *models.ShowdownParticipantStats) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, showdownParticipantStatsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ShowdownParticipantStats")
		case "currentScore":
			out.Values[i] = ec._ShowdownParticipantStats_currentScore(ctx, field, obj)
		case "win":
			out.Values[i] = ec._ShowdownParticipantStats_win(ctx, field, obj)
		case "loss":
			out.Values[i] = ec._ShowdownParticipantStats_loss(ctx, field, obj)
		case "draw":
			out.Values[i] = ec._ShowdownParticipantStats_draw(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var showdownRoundImplementors = []string{"ShowdownRound"}

func (ec *executionContext) _ShowdownRound(ctx context.Context, sel ast.SelectionSet, obj *models.ShowdownRound) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, showdownRoundImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ShowdownRound")
		case "opponent":
			out.Values[i] = ec._ShowdownRound_opponent(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "round":
			out.Values[i] = ec._ShowdownRound_round(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "score":
			out.Values[i] = ec._ShowdownRound_score(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "games":
			out.Values[i] = ec._ShowdownRound_games(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "wins":
			out.Values[i] = ec._ShowdownRound_wins(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "loose":
			out.Values[i] = ec._ShowdownRound_loose(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "totalGamesPlayed":
			out.Values[i] = ec._ShowdownRound_totalGamesPlayed(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "playerStatus":
			out.Values[i] = ec._ShowdownRound_playerStatus(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "isBye":
			out.Values[i] = ec._ShowdownRound_isBye(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "isRoundEnded":
			out.Values[i] = ec._ShowdownRound_isRoundEnded(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "hasJoined":
			out.Values[i] = ec._ShowdownRound_hasJoined(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "hasOpponentNotShown":
			out.Values[i] = ec._ShowdownRound_hasOpponentNotShown(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "hasFailedToPlay":
			out.Values[i] = ec._ShowdownRound_hasFailedToPlay(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var showdownStatsImplementors = []string{"ShowdownStats"}

func (ec *executionContext) _ShowdownStats(ctx context.Context, sel ast.SelectionSet, obj *models.ShowdownStats) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, showdownStatsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ShowdownStats")
		case "totalParticipants":
			out.Values[i] = ec._ShowdownStats_totalParticipants(ctx, field, obj)
		case "totalGamesPlayed":
			out.Values[i] = ec._ShowdownStats_totalGamesPlayed(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var showdownUserDetailsImplementors = []string{"ShowdownUserDetails"}

func (ec *executionContext) _ShowdownUserDetails(ctx context.Context, sel ast.SelectionSet, obj *models.ShowdownUserDetails) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, showdownUserDetailsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ShowdownUserDetails")
		case "name":
			out.Values[i] = ec._ShowdownUserDetails_name(ctx, field, obj)
		case "username":
			out.Values[i] = ec._ShowdownUserDetails_username(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "profileImageUrl":
			out.Values[i] = ec._ShowdownUserDetails_profileImageUrl(ctx, field, obj)
		case "rating":
			out.Values[i] = ec._ShowdownUserDetails_rating(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var showdownDetailsImplementors = []string{"showdownDetails"}

func (ec *executionContext) _showdownDetails(ctx context.Context, sel ast.SelectionSet, obj *models.ShowdownDetails) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, showdownDetailsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("showdownDetails")
		case "format":
			out.Values[i] = ec._showdownDetails_format(ctx, field, obj)
		case "rules":
			out.Values[i] = ec._showdownDetails_rules(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

// endregion **************************** object.gotpl ****************************

// region    ***************************** type.gotpl *****************************

func (ec *executionContext) unmarshalNCreateShowdownInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCreateShowdownInput(ctx context.Context, v any) (models.CreateShowdownInput, error) {
	res, err := ec.unmarshalInputCreateShowdownInput(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNFicturesCollection2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFicturesCollection(ctx context.Context, sel ast.SelectionSet, v models.FicturesCollection) graphql.Marshaler {
	return ec._FicturesCollection(ctx, sel, &v)
}

func (ec *executionContext) marshalNFicturesCollection2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFicturesCollection(ctx context.Context, sel ast.SelectionSet, v *models.FicturesCollection) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._FicturesCollection(ctx, sel, v)
}

func (ec *executionContext) unmarshalNHostInfoInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐHostInfoInput(ctx context.Context, v any) (*models.HostInfoInput, error) {
	res, err := ec.unmarshalInputHostInfoInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNLeaderParticipantEntity2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeaderParticipantEntity(ctx context.Context, sel ast.SelectionSet, v []*models.LeaderParticipantEntity) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOLeaderParticipantEntity2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeaderParticipantEntity(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalNPaginatedLeaderboard2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPaginatedLeaderboard(ctx context.Context, sel ast.SelectionSet, v models.PaginatedLeaderboard) graphql.Marshaler {
	return ec._PaginatedLeaderboard(ctx, sel, &v)
}

func (ec *executionContext) marshalNPaginatedLeaderboard2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPaginatedLeaderboard(ctx context.Context, sel ast.SelectionSet, v *models.PaginatedLeaderboard) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._PaginatedLeaderboard(ctx, sel, v)
}

func (ec *executionContext) marshalNPaginatedShowdowns2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPaginatedShowdowns(ctx context.Context, sel ast.SelectionSet, v models.PaginatedShowdowns) graphql.Marshaler {
	return ec._PaginatedShowdowns(ctx, sel, &v)
}

func (ec *executionContext) marshalNPaginatedShowdowns2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPaginatedShowdowns(ctx context.Context, sel ast.SelectionSet, v *models.PaginatedShowdowns) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._PaginatedShowdowns(ctx, sel, v)
}

func (ec *executionContext) marshalNParticipantBasicInfo2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐParticipantBasicInfo(ctx context.Context, sel ast.SelectionSet, v models.ParticipantBasicInfo) graphql.Marshaler {
	return ec._ParticipantBasicInfo(ctx, sel, &v)
}

func (ec *executionContext) marshalNRoundConfig2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRoundConfig(ctx context.Context, sel ast.SelectionSet, v models.RoundConfig) graphql.Marshaler {
	return ec._RoundConfig(ctx, sel, &v)
}

func (ec *executionContext) unmarshalNRoundConfigInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRoundConfigInput(ctx context.Context, v any) (*models.RoundConfigInput, error) {
	res, err := ec.unmarshalInputRoundConfigInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) unmarshalNRoundPlayerStatus2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRoundPlayerStatus(ctx context.Context, v any) (models.RoundPlayerStatus, error) {
	var res models.RoundPlayerStatus
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNRoundPlayerStatus2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐRoundPlayerStatus(ctx context.Context, sel ast.SelectionSet, v models.RoundPlayerStatus) graphql.Marshaler {
	return v
}

func (ec *executionContext) unmarshalNSHOWDOWN_CONTEST_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownContestStatus(ctx context.Context, v any) (models.ShowdownContestStatus, error) {
	var res models.ShowdownContestStatus
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNSHOWDOWN_CONTEST_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownContestStatus(ctx context.Context, sel ast.SelectionSet, v models.ShowdownContestStatus) graphql.Marshaler {
	return v
}

func (ec *executionContext) unmarshalNSHOWDOWN_CONTEST_STATUS2ᚕmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownContestStatusᚄ(ctx context.Context, v any) ([]models.ShowdownContestStatus, error) {
	var vSlice []any
	vSlice = graphql.CoerceList(v)
	var err error
	res := make([]models.ShowdownContestStatus, len(vSlice))
	for i := range vSlice {
		ctx := graphql.WithPathContext(ctx, graphql.NewPathWithIndex(i))
		res[i], err = ec.unmarshalNSHOWDOWN_CONTEST_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownContestStatus(ctx, vSlice[i])
		if err != nil {
			return nil, err
		}
	}
	return res, nil
}

func (ec *executionContext) marshalNSHOWDOWN_CONTEST_STATUS2ᚕmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownContestStatusᚄ(ctx context.Context, sel ast.SelectionSet, v []models.ShowdownContestStatus) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNSHOWDOWN_CONTEST_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownContestStatus(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) unmarshalNSHOWDOWN_PARTICIPANT_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownParticipantStatus(ctx context.Context, v any) (models.ShowdownParticipantStatus, error) {
	var res models.ShowdownParticipantStatus
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNSHOWDOWN_PARTICIPANT_STATUS2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownParticipantStatus(ctx context.Context, sel ast.SelectionSet, v models.ShowdownParticipantStatus) graphql.Marshaler {
	return v
}

func (ec *executionContext) marshalNShowdown2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdown(ctx context.Context, sel ast.SelectionSet, v models.Showdown) graphql.Marshaler {
	return ec._Showdown(ctx, sel, &v)
}

func (ec *executionContext) marshalNShowdown2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.Showdown) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNShowdown2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdown(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNShowdown2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdown(ctx context.Context, sel ast.SelectionSet, v *models.Showdown) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._Showdown(ctx, sel, v)
}

func (ec *executionContext) unmarshalNShowdownDetailsInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownDetailsInput(ctx context.Context, v any) (models.ShowdownDetailsInput, error) {
	res, err := ec.unmarshalInputShowdownDetailsInput(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNShowdownGamePlayer2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownGamePlayer(ctx context.Context, sel ast.SelectionSet, v []*models.ShowdownGamePlayer) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOShowdownGamePlayer2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownGamePlayer(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalNShowdownParticipantDetail2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownParticipantDetail(ctx context.Context, sel ast.SelectionSet, v []*models.ShowdownParticipantDetail) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOShowdownParticipantDetail2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownParticipantDetail(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalNShowdownParticipantStats2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownParticipantStats(ctx context.Context, sel ast.SelectionSet, v models.ShowdownParticipantStats) graphql.Marshaler {
	return ec._ShowdownParticipantStats(ctx, sel, &v)
}

func (ec *executionContext) unmarshalNShowdownRegistrationFormValuesInput2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownRegistrationFormValuesInput(ctx context.Context, v any) (models.ShowdownRegistrationFormValuesInput, error) {
	res, err := ec.unmarshalInputShowdownRegistrationFormValuesInput(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNShowdownRound2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownRound(ctx context.Context, sel ast.SelectionSet, v models.ShowdownRound) graphql.Marshaler {
	return ec._ShowdownRound(ctx, sel, &v)
}

func (ec *executionContext) marshalNShowdownRound2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownRound(ctx context.Context, sel ast.SelectionSet, v []*models.ShowdownRound) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOShowdownRound2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownRound(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalNshowdownDetails2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownDetails(ctx context.Context, sel ast.SelectionSet, v models.ShowdownDetails) graphql.Marshaler {
	return ec._showdownDetails(ctx, sel, &v)
}

func (ec *executionContext) marshalOCurrentShowdonParticipant2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐCurrentShowdonParticipant(ctx context.Context, sel ast.SelectionSet, v *models.CurrentShowdonParticipant) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._CurrentShowdonParticipant(ctx, sel, v)
}

func (ec *executionContext) marshalOFictures2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐFictures(ctx context.Context, sel ast.SelectionSet, v *models.Fictures) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._Fictures(ctx, sel, v)
}

func (ec *executionContext) unmarshalOHOST_TYPE2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐHostType(ctx context.Context, v any) (*models.HostType, error) {
	if v == nil {
		return nil, nil
	}
	var res = new(models.HostType)
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOHOST_TYPE2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐHostType(ctx context.Context, sel ast.SelectionSet, v *models.HostType) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return v
}

func (ec *executionContext) marshalOHostInfo2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐHostInfo(ctx context.Context, sel ast.SelectionSet, v *models.HostInfo) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._HostInfo(ctx, sel, v)
}

func (ec *executionContext) marshalOLeaderParticipantEntity2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐLeaderParticipantEntity(ctx context.Context, sel ast.SelectionSet, v *models.LeaderParticipantEntity) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._LeaderParticipantEntity(ctx, sel, v)
}

func (ec *executionContext) unmarshalOPaginatedLeaderboardInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐPaginatedLeaderboardInput(ctx context.Context, v any) (*models.PaginatedLeaderboardInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputPaginatedLeaderboardInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) unmarshalOSHOWDOWN_CONTEST_STATUS2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownContestStatus(ctx context.Context, v any) (*models.ShowdownContestStatus, error) {
	if v == nil {
		return nil, nil
	}
	var res = new(models.ShowdownContestStatus)
	err := res.UnmarshalGQL(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOSHOWDOWN_CONTEST_STATUS2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownContestStatus(ctx context.Context, sel ast.SelectionSet, v *models.ShowdownContestStatus) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return v
}

func (ec *executionContext) marshalOShowdown2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdown(ctx context.Context, sel ast.SelectionSet, v []*models.Showdown) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOShowdown2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdown(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOShowdown2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdown(ctx context.Context, sel ast.SelectionSet, v *models.Showdown) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._Showdown(ctx, sel, v)
}

func (ec *executionContext) marshalOShowdownGameConfig2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownGameConfig(ctx context.Context, sel ast.SelectionSet, v *models.ShowdownGameConfig) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._ShowdownGameConfig(ctx, sel, v)
}

func (ec *executionContext) marshalOShowdownGamePlayer2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownGamePlayer(ctx context.Context, sel ast.SelectionSet, v *models.ShowdownGamePlayer) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._ShowdownGamePlayer(ctx, sel, v)
}

func (ec *executionContext) marshalOShowdownParticipant2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownParticipant(ctx context.Context, sel ast.SelectionSet, v models.ShowdownParticipant) graphql.Marshaler {
	return ec._ShowdownParticipant(ctx, sel, &v)
}

func (ec *executionContext) marshalOShowdownParticipantDetail2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownParticipantDetail(ctx context.Context, sel ast.SelectionSet, v *models.ShowdownParticipantDetail) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._ShowdownParticipantDetail(ctx, sel, v)
}

func (ec *executionContext) marshalOShowdownParticipantStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownParticipantStats(ctx context.Context, sel ast.SelectionSet, v *models.ShowdownParticipantStats) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._ShowdownParticipantStats(ctx, sel, v)
}

func (ec *executionContext) marshalOShowdownRound2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownRound(ctx context.Context, sel ast.SelectionSet, v []*models.ShowdownRound) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalOShowdownRound2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownRound(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	return ret
}

func (ec *executionContext) marshalOShowdownRound2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownRound(ctx context.Context, sel ast.SelectionSet, v *models.ShowdownRound) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._ShowdownRound(ctx, sel, v)
}

func (ec *executionContext) marshalOShowdownStats2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownStats(ctx context.Context, sel ast.SelectionSet, v *models.ShowdownStats) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._ShowdownStats(ctx, sel, v)
}

func (ec *executionContext) marshalOShowdownUserDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐShowdownUserDetails(ctx context.Context, sel ast.SelectionSet, v *models.ShowdownUserDetails) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._ShowdownUserDetails(ctx, sel, v)
}

func (ec *executionContext) unmarshalOStartGameForShowdownInput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐStartGameForShowdownInput(ctx context.Context, v any) (*models.StartGameForShowdownInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputStartGameForShowdownInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

// endregion ***************************** type.gotpl *****************************
