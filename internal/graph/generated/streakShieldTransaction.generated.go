// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"context"
	"errors"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/ast"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// region    ************************** generated!.gotpl **************************

// endregion ************************** generated!.gotpl **************************

// region    ***************************** args.gotpl *****************************

// endregion ***************************** args.gotpl *****************************

// region    ************************** directives.gotpl **************************

// endregion ************************** directives.gotpl **************************

// region    **************************** field.gotpl *****************************

func (ec *executionContext) _ReferralDetails_referral(ctx context.Context, field graphql.CollectedField, obj *models.ReferralDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ReferralDetails_referral(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Referral, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.Referral)
	fc.Result = res
	return ec.marshalNReferral2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐReferral(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ReferralDetails_referral(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ReferralDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_Referral__id(ctx, field)
			case "referrer":
				return ec.fieldContext_Referral_referrer(ctx, field)
			case "referredTo":
				return ec.fieldContext_Referral_referredTo(ctx, field)
			case "referredAt":
				return ec.fieldContext_Referral_referredAt(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Referral", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _ReferralDetails_referredUser(ctx context.Context, field graphql.CollectedField, obj *models.ReferralDetails) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ReferralDetails_referredUser(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ReferredUser, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.UserPublicDetails)
	fc.Result = res
	return ec.marshalNUserPublicDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐUserPublicDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ReferralDetails_referredUser(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ReferralDetails",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_UserPublicDetails__id(ctx, field)
			case "name":
				return ec.fieldContext_UserPublicDetails_name(ctx, field)
			case "username":
				return ec.fieldContext_UserPublicDetails_username(ctx, field)
			case "profileImageUrl":
				return ec.fieldContext_UserPublicDetails_profileImageUrl(ctx, field)
			case "rating":
				return ec.fieldContext_UserPublicDetails_rating(ctx, field)
			case "badge":
				return ec.fieldContext_UserPublicDetails_badge(ctx, field)
			case "countryCode":
				return ec.fieldContext_UserPublicDetails_countryCode(ctx, field)
			case "isGuest":
				return ec.fieldContext_UserPublicDetails_isGuest(ctx, field)
			case "globalRank":
				return ec.fieldContext_UserPublicDetails_globalRank(ctx, field)
			case "previousGlobalRank":
				return ec.fieldContext_UserPublicDetails_previousGlobalRank(ctx, field)
			case "countryRank":
				return ec.fieldContext_UserPublicDetails_countryRank(ctx, field)
			case "previousCountryRank":
				return ec.fieldContext_UserPublicDetails_previousCountryRank(ctx, field)
			case "stats":
				return ec.fieldContext_UserPublicDetails_stats(ctx, field)
			case "userStreaks":
				return ec.fieldContext_UserPublicDetails_userStreaks(ctx, field)
			case "ratingV2":
				return ec.fieldContext_UserPublicDetails_ratingV2(ctx, field)
			case "institutionId":
				return ec.fieldContext_UserPublicDetails_institutionId(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type UserPublicDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _StreakShieldTransaction__id(ctx context.Context, field graphql.CollectedField, obj *models.StreakShieldTransaction) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StreakShieldTransaction__id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StreakShieldTransaction__id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StreakShieldTransaction",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StreakShieldTransaction_userId(ctx context.Context, field graphql.CollectedField, obj *models.StreakShieldTransaction) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StreakShieldTransaction_userId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(primitive.ObjectID)
	fc.Result = res
	return ec.marshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StreakShieldTransaction_userId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StreakShieldTransaction",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StreakShieldTransaction_quantity(ctx context.Context, field graphql.CollectedField, obj *models.StreakShieldTransaction) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StreakShieldTransaction_quantity(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Quantity, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StreakShieldTransaction_quantity(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StreakShieldTransaction",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StreakShieldTransaction_transactionType(ctx context.Context, field graphql.CollectedField, obj *models.StreakShieldTransaction) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StreakShieldTransaction_transactionType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TransactionType, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.TransactionType)
	fc.Result = res
	return ec.marshalNTRANSACTION_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐTransactionType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StreakShieldTransaction_transactionType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StreakShieldTransaction",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type TRANSACTION_TYPE does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StreakShieldTransaction_earnVia(ctx context.Context, field graphql.CollectedField, obj *models.StreakShieldTransaction) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StreakShieldTransaction_earnVia(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EarnVia, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.EarnVia)
	fc.Result = res
	return ec.marshalOEARN_VIA2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐEarnVia(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StreakShieldTransaction_earnVia(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StreakShieldTransaction",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type EARN_VIA does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StreakShieldTransaction_referralId(ctx context.Context, field graphql.CollectedField, obj *models.StreakShieldTransaction) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StreakShieldTransaction_referralId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ReferralID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*primitive.ObjectID)
	fc.Result = res
	return ec.marshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StreakShieldTransaction_referralId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StreakShieldTransaction",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StreakShieldTransaction_transactionId(ctx context.Context, field graphql.CollectedField, obj *models.StreakShieldTransaction) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StreakShieldTransaction_transactionId(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TransactionID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StreakShieldTransaction_transactionId(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StreakShieldTransaction",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StreakShieldTransaction_redeemedOn(ctx context.Context, field graphql.CollectedField, obj *models.StreakShieldTransaction) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StreakShieldTransaction_redeemedOn(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RedeemedOn, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]time.Time)
	fc.Result = res
	return ec.marshalOTime2ᚕtimeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StreakShieldTransaction_redeemedOn(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StreakShieldTransaction",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StreakShieldTransaction_createdAt(ctx context.Context, field graphql.CollectedField, obj *models.StreakShieldTransaction) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StreakShieldTransaction_createdAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StreakShieldTransaction_createdAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StreakShieldTransaction",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StreakShieldTransaction_updatedAt(ctx context.Context, field graphql.CollectedField, obj *models.StreakShieldTransaction) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StreakShieldTransaction_updatedAt(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UpdatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StreakShieldTransaction_updatedAt(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StreakShieldTransaction",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StreakShieldTransactionOutput_transaction(ctx context.Context, field graphql.CollectedField, obj *models.StreakShieldTransactionOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StreakShieldTransactionOutput_transaction(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Transaction, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.StreakShieldTransaction)
	fc.Result = res
	return ec.marshalNStreakShieldTransaction2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐStreakShieldTransaction(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StreakShieldTransactionOutput_transaction(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StreakShieldTransactionOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "_id":
				return ec.fieldContext_StreakShieldTransaction__id(ctx, field)
			case "userId":
				return ec.fieldContext_StreakShieldTransaction_userId(ctx, field)
			case "quantity":
				return ec.fieldContext_StreakShieldTransaction_quantity(ctx, field)
			case "transactionType":
				return ec.fieldContext_StreakShieldTransaction_transactionType(ctx, field)
			case "earnVia":
				return ec.fieldContext_StreakShieldTransaction_earnVia(ctx, field)
			case "referralId":
				return ec.fieldContext_StreakShieldTransaction_referralId(ctx, field)
			case "transactionId":
				return ec.fieldContext_StreakShieldTransaction_transactionId(ctx, field)
			case "redeemedOn":
				return ec.fieldContext_StreakShieldTransaction_redeemedOn(ctx, field)
			case "createdAt":
				return ec.fieldContext_StreakShieldTransaction_createdAt(ctx, field)
			case "updatedAt":
				return ec.fieldContext_StreakShieldTransaction_updatedAt(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type StreakShieldTransaction", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _StreakShieldTransactionOutput_referralDetails(ctx context.Context, field graphql.CollectedField, obj *models.StreakShieldTransactionOutput) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StreakShieldTransactionOutput_referralDetails(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ReferralDetails, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.ReferralDetails)
	fc.Result = res
	return ec.marshalOReferralDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐReferralDetails(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StreakShieldTransactionOutput_referralDetails(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StreakShieldTransactionOutput",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "referral":
				return ec.fieldContext_ReferralDetails_referral(ctx, field)
			case "referredUser":
				return ec.fieldContext_ReferralDetails_referredUser(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ReferralDetails", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _StreakShieldTransactionPage_results(ctx context.Context, field graphql.CollectedField, obj *models.StreakShieldTransactionPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StreakShieldTransactionPage_results(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Results, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*models.StreakShieldTransactionOutput)
	fc.Result = res
	return ec.marshalNStreakShieldTransactionOutput2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐStreakShieldTransactionOutputᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StreakShieldTransactionPage_results(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StreakShieldTransactionPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "transaction":
				return ec.fieldContext_StreakShieldTransactionOutput_transaction(ctx, field)
			case "referralDetails":
				return ec.fieldContext_StreakShieldTransactionOutput_referralDetails(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type StreakShieldTransactionOutput", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _StreakShieldTransactionPage_pageNumber(ctx context.Context, field graphql.CollectedField, obj *models.StreakShieldTransactionPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StreakShieldTransactionPage_pageNumber(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PageNumber, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StreakShieldTransactionPage_pageNumber(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StreakShieldTransactionPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StreakShieldTransactionPage_pageSize(ctx context.Context, field graphql.CollectedField, obj *models.StreakShieldTransactionPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StreakShieldTransactionPage_pageSize(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PageSize, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int)
	fc.Result = res
	return ec.marshalNInt2int(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StreakShieldTransactionPage_pageSize(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StreakShieldTransactionPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StreakShieldTransactionPage_hasMore(ctx context.Context, field graphql.CollectedField, obj *models.StreakShieldTransactionPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StreakShieldTransactionPage_hasMore(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasMore, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*bool)
	fc.Result = res
	return ec.marshalOBoolean2ᚖbool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StreakShieldTransactionPage_hasMore(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StreakShieldTransactionPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _StreakShieldTransactionPage_totalResults(ctx context.Context, field graphql.CollectedField, obj *models.StreakShieldTransactionPage) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_StreakShieldTransactionPage_totalResults(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalResults, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*int)
	fc.Result = res
	return ec.marshalOInt2ᚖint(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_StreakShieldTransactionPage_totalResults(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "StreakShieldTransactionPage",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

// endregion **************************** field.gotpl *****************************

// region    **************************** input.gotpl *****************************

func (ec *executionContext) unmarshalInputCreateStreakShieldTransactionInput(ctx context.Context, obj any) (models.CreateStreakShieldTransactionInput, error) {
	var it models.CreateStreakShieldTransactionInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"userId", "quantity", "transactionType", "earnVia", "referralId", "transactionId"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "userId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("userId"))
			data, err := ec.unmarshalNID2goᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.UserID = data
		case "quantity":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("quantity"))
			data, err := ec.unmarshalNInt2int(ctx, v)
			if err != nil {
				return it, err
			}
			it.Quantity = data
		case "transactionType":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("transactionType"))
			data, err := ec.unmarshalNTRANSACTION_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐTransactionType(ctx, v)
			if err != nil {
				return it, err
			}
			it.TransactionType = data
		case "earnVia":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("earnVia"))
			data, err := ec.unmarshalOEARN_VIA2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐEarnVia(ctx, v)
			if err != nil {
				return it, err
			}
			it.EarnVia = data
		case "referralId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("referralId"))
			data, err := ec.unmarshalOID2ᚖgoᚗmongodbᚗorgᚋmongoᚑdriverᚋbsonᚋprimitiveᚐObjectID(ctx, v)
			if err != nil {
				return it, err
			}
			it.ReferralID = data
		case "transactionId":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("transactionId"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.TransactionID = data
		}
	}

	return it, nil
}

// endregion **************************** input.gotpl *****************************

// region    ************************** interface.gotpl ***************************

// endregion ************************** interface.gotpl ***************************

// region    **************************** object.gotpl ****************************

var referralDetailsImplementors = []string{"ReferralDetails"}

func (ec *executionContext) _ReferralDetails(ctx context.Context, sel ast.SelectionSet, obj *models.ReferralDetails) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, referralDetailsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ReferralDetails")
		case "referral":
			out.Values[i] = ec._ReferralDetails_referral(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "referredUser":
			out.Values[i] = ec._ReferralDetails_referredUser(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var streakShieldTransactionImplementors = []string{"StreakShieldTransaction"}

func (ec *executionContext) _StreakShieldTransaction(ctx context.Context, sel ast.SelectionSet, obj *models.StreakShieldTransaction) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, streakShieldTransactionImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("StreakShieldTransaction")
		case "_id":
			out.Values[i] = ec._StreakShieldTransaction__id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "userId":
			out.Values[i] = ec._StreakShieldTransaction_userId(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "quantity":
			out.Values[i] = ec._StreakShieldTransaction_quantity(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "transactionType":
			out.Values[i] = ec._StreakShieldTransaction_transactionType(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "earnVia":
			out.Values[i] = ec._StreakShieldTransaction_earnVia(ctx, field, obj)
		case "referralId":
			out.Values[i] = ec._StreakShieldTransaction_referralId(ctx, field, obj)
		case "transactionId":
			out.Values[i] = ec._StreakShieldTransaction_transactionId(ctx, field, obj)
		case "redeemedOn":
			out.Values[i] = ec._StreakShieldTransaction_redeemedOn(ctx, field, obj)
		case "createdAt":
			out.Values[i] = ec._StreakShieldTransaction_createdAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "updatedAt":
			out.Values[i] = ec._StreakShieldTransaction_updatedAt(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var streakShieldTransactionOutputImplementors = []string{"StreakShieldTransactionOutput"}

func (ec *executionContext) _StreakShieldTransactionOutput(ctx context.Context, sel ast.SelectionSet, obj *models.StreakShieldTransactionOutput) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, streakShieldTransactionOutputImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("StreakShieldTransactionOutput")
		case "transaction":
			out.Values[i] = ec._StreakShieldTransactionOutput_transaction(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "referralDetails":
			out.Values[i] = ec._StreakShieldTransactionOutput_referralDetails(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var streakShieldTransactionPageImplementors = []string{"StreakShieldTransactionPage"}

func (ec *executionContext) _StreakShieldTransactionPage(ctx context.Context, sel ast.SelectionSet, obj *models.StreakShieldTransactionPage) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, streakShieldTransactionPageImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("StreakShieldTransactionPage")
		case "results":
			out.Values[i] = ec._StreakShieldTransactionPage_results(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "pageNumber":
			out.Values[i] = ec._StreakShieldTransactionPage_pageNumber(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "pageSize":
			out.Values[i] = ec._StreakShieldTransactionPage_pageSize(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "hasMore":
			out.Values[i] = ec._StreakShieldTransactionPage_hasMore(ctx, field, obj)
		case "totalResults":
			out.Values[i] = ec._StreakShieldTransactionPage_totalResults(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

// endregion **************************** object.gotpl ****************************

// region    ***************************** type.gotpl *****************************

func (ec *executionContext) marshalNStreakShieldTransaction2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐStreakShieldTransaction(ctx context.Context, sel ast.SelectionSet, v *models.StreakShieldTransaction) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._StreakShieldTransaction(ctx, sel, v)
}

func (ec *executionContext) marshalNStreakShieldTransactionOutput2ᚕᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐStreakShieldTransactionOutputᚄ(ctx context.Context, sel ast.SelectionSet, v []*models.StreakShieldTransactionOutput) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNStreakShieldTransactionOutput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐStreakShieldTransactionOutput(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNStreakShieldTransactionOutput2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐStreakShieldTransactionOutput(ctx context.Context, sel ast.SelectionSet, v *models.StreakShieldTransactionOutput) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._StreakShieldTransactionOutput(ctx, sel, v)
}

func (ec *executionContext) marshalNStreakShieldTransactionPage2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐStreakShieldTransactionPage(ctx context.Context, sel ast.SelectionSet, v models.StreakShieldTransactionPage) graphql.Marshaler {
	return ec._StreakShieldTransactionPage(ctx, sel, &v)
}

func (ec *executionContext) marshalNStreakShieldTransactionPage2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐStreakShieldTransactionPage(ctx context.Context, sel ast.SelectionSet, v *models.StreakShieldTransactionPage) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._StreakShieldTransactionPage(ctx, sel, v)
}

func (ec *executionContext) unmarshalNTRANSACTION_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐTransactionType(ctx context.Context, v any) (models.TransactionType, error) {
	tmp, err := graphql.UnmarshalString(v)
	res := models.TransactionType(tmp)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNTRANSACTION_TYPE2matiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐTransactionType(ctx context.Context, sel ast.SelectionSet, v models.TransactionType) graphql.Marshaler {
	_ = sel
	res := graphql.MarshalString(string(v))
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return res
}

func (ec *executionContext) unmarshalOEARN_VIA2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐEarnVia(ctx context.Context, v any) (*models.EarnVia, error) {
	if v == nil {
		return nil, nil
	}
	tmp, err := graphql.UnmarshalString(v)
	res := models.EarnVia(tmp)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOEARN_VIA2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐEarnVia(ctx context.Context, sel ast.SelectionSet, v *models.EarnVia) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	_ = sel
	_ = ctx
	res := graphql.MarshalString(string(*v))
	return res
}

func (ec *executionContext) marshalOReferralDetails2ᚖmatiksOfficialᚋmatiksᚑserverᚑgoᚋinternalᚋmodelsᚐReferralDetails(ctx context.Context, sel ast.SelectionSet, v *models.ReferralDetails) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._ReferralDetails(ctx, sel, v)
}

// endregion ***************************** type.gotpl *****************************
