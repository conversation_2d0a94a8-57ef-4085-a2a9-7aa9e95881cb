package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// MarkAnnouncementAsRead is the resolver for the markAnnouncementAsRead field.
func (r *mutationResolver) MarkAnnouncementAsRead(ctx context.Context, announcementID primitive.ObjectID) (*models.AnnouncementMutationResponse, error) {
	return r.AnnouncementService.MarkAnnouncementAsRead(ctx, announcementID)
}

// MarkAllAnnouncementsAsRead is the resolver for the markAllAnnouncementsAsRead field.
func (r *mutationResolver) MarkAllAnnouncementsAsRead(ctx context.Context) (*models.AnnouncementMutationResponse, error) {
	return r.AnnouncementService.MarkAllAnnouncementsAsRead(ctx)
}

// CreateAnnouncement is the resolver for the createAnnouncement field.
func (r *mutationResolver) CreateAnnouncement(ctx context.Context, input models.CreateAnnouncementInput) (*models.Announcement, error) {
	return r.AnnouncementService.CreateAnnouncement(ctx, input)
}

// UpdateAnnouncement is the resolver for the updateAnnouncement field.
func (r *mutationResolver) UpdateAnnouncement(ctx context.Context, id primitive.ObjectID, input models.UpdateAnnouncementInput) (*models.Announcement, error) {
	return r.AnnouncementService.UpdateAnnouncement(ctx, id, input)
}

// DeleteAnnouncement is the resolver for the deleteAnnouncement field.
func (r *mutationResolver) DeleteAnnouncement(ctx context.Context, id primitive.ObjectID) (*models.AnnouncementMutationResponse, error) {
	return r.AnnouncementService.DeleteAnnouncement(ctx, id)
}

// GetUnreadAnnouncements is the resolver for the getUnreadAnnouncements field.
func (r *queryResolver) GetUnreadAnnouncements(ctx context.Context, limit *int, offset *int) ([]*models.Announcement, error) {
	return r.AnnouncementService.GetUnreadAnnouncements(ctx, limit, offset)
}

// GetAnnouncement is the resolver for the getAnnouncement field.
func (r *queryResolver) GetAnnouncement(ctx context.Context, id primitive.ObjectID) (*models.Announcement, error) {
	return r.AnnouncementService.GetAnnouncementByID(ctx, id)
}

// GetAllAnnouncements is the resolver for the getAllAnnouncements field.
func (r *queryResolver) GetAllAnnouncements(ctx context.Context, limit *int, offset *int, typeArg *models.AnnouncementType) ([]*models.Announcement, error) {
	return r.AnnouncementService.GetAllAnnouncements(ctx, limit, offset, typeArg)
}
