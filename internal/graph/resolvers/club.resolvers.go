package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	"github.com/99designs/gqlgen/graphql"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreateClub is the resolver for the createClub field.
func (r *mutationResolver) CreateClub(ctx context.Context, input models.CreateClubInput) (*models.Club, error) {
	return r.ClubService.CreateClub(ctx, input)
}

// UpdateClub is the resolver for the updateClub field.
func (r *mutationResolver) UpdateClub(ctx context.Context, input models.UpdateClubInput) (bool, error) {
	return r.ClubService.UpdateClub(ctx, input)
}

// UploadClubLogoImage is the resolver for the uploadClubLogoImage field.
func (r *mutationResolver) UploadClubLogoImage(ctx context.Context, file graphql.Upload, clubID primitive.ObjectID) (*models.File, error) {
	return r.ClubService.UploadClubLogoImage(ctx, file, clubID)
}

// UploadClubBannerImage is the resolver for the uploadClubBannerImage field.
func (r *mutationResolver) UploadClubBannerImage(ctx context.Context, file graphql.Upload, clubID primitive.ObjectID) (*models.File, error) {
	return r.ClubService.UploadClubBannerImage(ctx, file, clubID)
}

// DeleteClub is the resolver for the deleteClub field.
func (r *mutationResolver) DeleteClub(ctx context.Context, id primitive.ObjectID) (bool, error) {
	return r.ClubService.DeleteClub(ctx, id)
}

// JoinClub is the resolver for the joinClub field.
func (r *mutationResolver) JoinClub(ctx context.Context, clubID primitive.ObjectID) (bool, error) {
	return r.ClubService.JoinClub(ctx, clubID)
}

// LeaveClub is the resolver for the leaveClub field.
func (r *mutationResolver) LeaveClub(ctx context.Context, clubID primitive.ObjectID) (bool, error) {
	return r.ClubService.LeaveClub(ctx, clubID)
}

// AddClubMember is the resolver for the addClubMember field.
func (r *mutationResolver) AddClubMember(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID) (bool, error) {
	return r.ClubService.AddClubMember(ctx, clubID, userID)
}

// UpdateMemberRole is the resolver for the updateMemberRole field.
func (r *mutationResolver) UpdateMemberRole(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID, role models.ClubMemberRole) (*models.ClubMember, error) {
	return r.ClubService.UpdateMemberRole(ctx, clubID, userID, role)
}

// RemoveClubMember is the resolver for the removeClubMember field.
func (r *mutationResolver) RemoveClubMember(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID) (bool, error) {
	return r.ClubService.RemoveClubMember(ctx, clubID, userID)
}

// Club is the resolver for the club field.
func (r *queryResolver) Club(ctx context.Context, id primitive.ObjectID) (*models.Club, error) {
	return r.ClubService.Club(ctx, id)
}

// Clubs is the resolver for the club field.
func (r *queryResolver) Clubs(ctx context.Context, page *int, pageSize *int, visibility *models.Visibility, search *string) (*models.ClubsPage, error) {
	return r.ClubService.Clubs(ctx, page, pageSize, visibility, search)
}

// ClubMembers is the resolver for the clubMembers field.
func (r *queryResolver) ClubMembers(ctx context.Context, clubID primitive.ObjectID, clubMembershipStatus models.ClubMembershipStatus, page *int, pageSize *int) (*models.ClubMembersPage, error) {
	return r.ClubService.Members(ctx, clubID, clubMembershipStatus, page, pageSize)
}

// GetClubMemberInfo is the resolver for the getClubMemberInfo field.
func (r *queryResolver) GetClubMemberInfo(ctx context.Context, clubID primitive.ObjectID) (*models.ClubMember, error) {
	return r.ClubService.GetClubMemberInfo(ctx, clubID)
}

// GetClubLeaderboard is the resolver for the getClubLeaderboard field.
func (r *queryResolver) GetClubLeaderboard(ctx context.Context, clubID primitive.ObjectID, page *int, pageSize *int) (*models.ClubLeaderboard, error) {
	return r.ClubService.GetClubLeaderboard(ctx, clubID, page, pageSize)
}
