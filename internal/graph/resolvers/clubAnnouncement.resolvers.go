package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreateClubAnnouncement is the resolver for the createClubAnnouncement field.
func (r *mutationResolver) CreateClubAnnouncement(ctx context.Context, input models.CreateClubAnnouncementInput) (*models.ClubAnnouncement, error) {
	return r.ClubAnnouncementService.CreateClubAnnouncement(ctx, input)
}

// DeleteClubAnnouncement is the resolver for the deleteClubAnnouncement field.
func (r *mutationResolver) DeleteClubAnnouncement(ctx context.Context, id primitive.ObjectID) (bool, error) {
	return r.ClubAnnouncementService.DeleteClubAnnouncement(ctx, id)
}

// ClubAnnouncements is the resolver for the clubAnnouncement field.
func (r *queryResolver) ClubAnnouncements(ctx context.Context, page *int, pageSize *int, clubID *primitive.ObjectID, from *time.Time, to *time.Time) (*models.ClubAnnouncementsPage, error) {
	return r.ClubAnnouncementService.ClubAnnouncements(ctx, page, pageSize, clubID, from, to)
}
