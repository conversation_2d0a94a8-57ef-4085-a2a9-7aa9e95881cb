package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreateClubEvent is the resolver for the createClubEvent field.
func (r *mutationResolver) CreateClubEvent(ctx context.Context, input models.CreateClubEventInput) (*models.ClubEvent, error) {
	return r.ClubEventService.CreateClubEvent(ctx, input)
}

// UpdateClubEvent is the resolver for the updateClubEvent field.
func (r *mutationResolver) UpdateClubEvent(ctx context.Context, input models.UpdateClubEventInput) (*models.ClubEvent, error) {
	return r.ClubEventService.UpdateClubEvent(ctx, input)
}

// DeleteClubEvent is the resolver for the deleteClubEvent field.
func (r *mutationResolver) DeleteClubEvent(ctx context.Context, id primitive.ObjectID) (bool, error) {
	return r.ClubEventService.DeleteClubEvent(ctx, id)
}

// JoinClubEvent is the resolver for the joinClubEvent field.
func (r *mutationResolver) JoinClubEvent(ctx context.Context, eventID primitive.ObjectID) (*models.ClubEventParticipant, error) {
	return r.ClubEventService.JoinClubEvent(ctx, eventID)
}

// LeaveClubEvent is the resolver for the leaveClubEvent field.
func (r *mutationResolver) LeaveClubEvent(ctx context.Context, eventID primitive.ObjectID) (bool, error) {
	return r.ClubEventService.LeaveClubEvent(ctx, eventID)
}

// ClubEvent is the resolver for the clubEvent field.
func (r *queryResolver) ClubEvent(ctx context.Context, id primitive.ObjectID) (*models.ClubEvent, error) {
	return r.ClubEventService.ClubEvent(ctx, id)
}

// ClubEvents is the resolver for the clubEvents field.
func (r *queryResolver) ClubEvents(ctx context.Context, page *int, pageSize *int, clubID *primitive.ObjectID, clubEventType *models.ClubEventType, from *time.Time, to *time.Time) (*models.ClubEventsPage, error) {
	return r.ClubEventService.ClubEvents(ctx, page, pageSize, clubID, clubEventType, from, to)
}
