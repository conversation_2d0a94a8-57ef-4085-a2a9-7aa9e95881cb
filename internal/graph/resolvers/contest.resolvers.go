package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Create<PERSON>onte<PERSON> is the resolver for the createContest field.
func (r *mutationResolver) CreateContest(ctx context.Context, input models.CreateContestInput) (*models.Contest, error) {
	return r.ContestService.CreateContest(ctx, input)
}

// RegisterForContest is the resolver for the registerForContest field.
func (r *mutationResolver) RegisterForContest(ctx context.Context, input models.RegistrationFormValuesInput) (bool, error) {
	return r.ContestService.RegisterForContest(ctx, input)
}

// SubmitContestAnswer is the resolver for the submitContestAnswer field.
func (r *mutationResolver) SubmitContestAnswer(ctx context.Context, contestID primitive.ObjectID, questionID string, answer string) (bool, error) {
	return r.ContestService.SubmitContestAnswer(ctx, contestID, questionID, answer)
}

// UnregisterFromContest is the resolver for the unregisterFromContest field.
func (r *mutationResolver) UnregisterFromContest(ctx context.Context, contestID primitive.ObjectID) (bool, error) {
	return r.ContestService.UnregisterFromContest(ctx, contestID)
}

// UpdateContestParticipantStartTime is the resolver for the updateContestParticipantStartTime field.
func (r *mutationResolver) UpdateContestParticipantStartTime(ctx context.Context, contestID primitive.ObjectID) (bool, error) {
	return r.ContestService.UpdateContestParticipantStartTime(ctx, contestID)
}

// SubmitVirtualContestAnswer is the resolver for the submitVirtualContestAnswer field.
func (r *mutationResolver) SubmitVirtualContestAnswer(ctx context.Context, contestID primitive.ObjectID, questionID string, answer string) (bool, error) {
	return r.ContestService.SubmitVirtualContestAnswer(ctx, contestID, questionID, answer)
}

// JoinVirtualContest is the resolver for the joinVirtualContest field.
func (r *mutationResolver) JoinVirtualContest(ctx context.Context, contestID primitive.ObjectID) (bool, error) {
	return r.ContestService.JoinVirtualContest(ctx, contestID)
}

// GetFeaturedContests is the resolver for the getFeaturedContests field.
func (r *queryResolver) GetFeaturedContests(ctx context.Context) ([]*models.Contest, error) {
	return r.ContestService.GetFeaturedContests(ctx)
}

// GetContestByID is the resolver for the getContestById field.
func (r *queryResolver) GetContestByID(ctx context.Context, contestID primitive.ObjectID) (*models.Contest, error) {
	return r.ContestService.GetContestByID(ctx, contestID)
}

// GetContestLeaderboard is the resolver for the getContestLeaderboard field.
func (r *queryResolver) GetContestLeaderboard(ctx context.Context, contestID primitive.ObjectID, pageNumber *int, pageSize *int) (*models.ContestLeaderboard, error) {
	return r.ContestService.GetContestLeaderboard(ctx, contestID, pageNumber, pageSize)
}

// GetRegisteredContests is the resolver for the getRegisteredContests field.
func (r *queryResolver) GetRegisteredContests(ctx context.Context) ([]*models.Contest, error) {
	return r.ContestService.GetRegisteredContests(ctx)
}

// GetUserContestSubmissions is the resolver for the getUserContestSubmissions field.
func (r *queryResolver) GetUserContestSubmissions(ctx context.Context, userID *primitive.ObjectID, contestID primitive.ObjectID) (*models.UserContestSubmissions, error) {
	return r.ContestService.GetUserContestSubmissions(ctx, userID, contestID)
}

// GetUserContestResult is the resolver for the getUserContestResult field.
func (r *queryResolver) GetUserContestResult(ctx context.Context, contestID primitive.ObjectID) (*models.UserContestResult, error) {
	return r.ContestService.GetUserContestResult(ctx, contestID)
}

// GetContestsByStatus is the resolver for the getContestsByStatus field.
func (r *queryResolver) GetContestsByStatus(ctx context.Context, statuses []models.ContestStatus, page *int, pageSize *int, sortDirection *string) (*models.PaginatedContests, error) {
	return r.ContestService.GetContestsByStatus(ctx, statuses, page, pageSize, sortDirection)
}

// ContestLeaderboardUpdated is the resolver for the contestLeaderboardUpdated field.
func (r *subscriptionResolver) ContestLeaderboardUpdated(ctx context.Context, contestID primitive.ObjectID) (<-chan *models.ContestLeaderboard, error) {
	panic(fmt.Errorf("not implemented: ContestLeaderboardUpdated - contestLeaderboardUpdated"))
}
