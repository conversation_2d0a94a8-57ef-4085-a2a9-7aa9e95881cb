package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// SubmitChallengeResult is the resolver for the submitChallengeResult field.
func (r *mutationResolver) SubmitChallengeResult(ctx context.Context, input models.SubmitSolutionInput) (*models.SubmitChallengeResult, error) {
	return r.DailyChallengeService.SubmitDailyChallenge(ctx, input)
}

// AttemptDailyChallenge is the resolver for the attemptDailyChallenge field.
func (r *mutationResolver) AttemptDailyChallenge(ctx context.Context, challengeID primitive.ObjectID) (bool, error) {
	return r.DailyChallengeService.AttemptDailyChallenge(ctx, challengeID)
}

// GetUserResult is the resolver for the getUserResult field.
func (r *queryResolver) GetUserResult(ctx context.Context, challengeNumber *int) (*models.UserResult, error) {
	return r.DailyChallengeService.GetUserChallengeResult(ctx, challengeNumber)
}

// GetDailyChallenge is the resolver for the getDailyChallenge field.
func (r *queryResolver) GetDailyChallenge(ctx context.Context) (*models.DailyChallenge, error) {
	return r.DailyChallengeService.GetDailyChallenge(ctx)
}

// GetDailyChallenges is the resolver for the getDailyChallenges field.
func (r *queryResolver) GetDailyChallenges(ctx context.Context) ([]*models.DailyChallenge, error) {
	return r.DailyChallengeService.GetDailyChallenges(ctx)
}

// GetDailyChallengeByID is the resolver for the getDailyChallengeById field.
func (r *queryResolver) GetDailyChallengeByID(ctx context.Context, id primitive.ObjectID) (*models.DailyChallenge, error) {
	return r.DailyChallengeService.GetDailyChallengeByID(ctx, id)
}

// GetDailyChallengeLeaderboard is the resolver for the getDailyChallengeLeaderboard field.
func (r *queryResolver) GetDailyChallengeLeaderboard(ctx context.Context, challengeNumber *int, pageNumber *int, pageSize *int) (*models.LeaderboardPage, error) {
	return r.DailyChallengeService.GetDailyChallengeLeaderboard(ctx, challengeNumber, pageNumber, pageSize)
}

// CheckBotBehavior is the resolver for the checkBotBehavior field.
func (r *queryResolver) CheckBotBehavior(ctx context.Context, challengeID primitive.ObjectID, userID primitive.ObjectID) (*models.BotDetectionResult, error) {
	return r.DailyChallengeService.CheckBotBehavior(ctx, challengeID, userID)
}

// GetUserResultByDivision is the resolver for the getUserResultByDivision field.
func (r *queryResolver) GetUserResultByDivision(ctx context.Context, dateStr *string, division *models.ChallengeDivision) (*models.UserResult, error) {
	return r.DailyChallengeService.GetUserChallengeResultByDivision(ctx, dateStr, division)
}

// GetUserResultByDailyChallengeID is the resolver for the getUserResultByDailyChallengeId field.
func (r *queryResolver) GetUserResultByDailyChallengeID(ctx context.Context, challengeID primitive.ObjectID) (*models.UserDailyChallengeResultWithStats, error) {
	return r.DailyChallengeService.GetUserChallengeResultByDailyChallengeID(ctx, challengeID)
}

// GetDailyChallengeLeaderboardByDivision is the resolver for the getDailyChallengeLeaderboardByDivision field.
func (r *queryResolver) GetDailyChallengeLeaderboardByDivision(ctx context.Context, dateStr *string, division *models.ChallengeDivision, pageNumber *int, pageSize *int) (*models.LeaderboardPage, error) {
	return r.DailyChallengeService.GetDailyChallengeLeaderboardByDivision(ctx, dateStr, pageNumber, pageSize, division)
}

// GetUserResultByDivison is the resolver for the getUserResultByDivison field.
func (r *queryResolver) GetUserResultByDivison(ctx context.Context, dateStr *string, division *models.ChallengeDivision) (*models.UserResult, error) {
	return r.DailyChallengeService.GetUserChallengeResultByDivision(ctx, dateStr, division)
}

// GetDailyChallengeLeaderboardByDivison is the resolver for the getDailyChallengeLeaderboardByDivison field.
func (r *queryResolver) GetDailyChallengeLeaderboardByDivison(ctx context.Context, dateStr *string, division *models.ChallengeDivision, pageNumber *int, pageSize *int) (*models.LeaderboardPage, error) {
	return r.DailyChallengeService.GetDailyChallengeLeaderboardByDivision(ctx, dateStr, pageNumber, pageSize, division)
}
