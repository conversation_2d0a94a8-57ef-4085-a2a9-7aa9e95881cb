package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// UpdateLikeStatus is the resolver for the updateLikeStatus field.
func (r *mutationResolver) UpdateLikeStatus(ctx context.Context, feedID primitive.ObjectID) (bool, error) {
	return r.FeedService.UpdateLikeStatus(ctx, feedID)
}

// UpdateLastReadFeedID is the resolver for the updateLastReadFeedId field.
func (r *mutationResolver) UpdateLastReadFeedID(ctx context.Context, lastReadFeedID primitive.ObjectID) (bool, error) {
	return r.FeedService.UpdateLastReadFeedId(ctx, lastReadFeedID)
}

// GetUserFeeds is the resolver for the getUserFeeds field.
func (r *queryResolver) GetUserFeeds(ctx context.Context, lastID *primitive.ObjectID, pageSize *int) (*models.FeedResponse, error) {
	return r.FeedService.GetUserFeed(ctx, lastID, pageSize)
}
