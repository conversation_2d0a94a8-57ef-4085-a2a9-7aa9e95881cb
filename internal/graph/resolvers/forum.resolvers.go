package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreateForum is the resolver for the createForum field.
func (r *mutationResolver) CreateForum(ctx context.Context, input models.CreateForumInput) (*models.Forum, error) {
	return r.ForumService.CreateForum(ctx, input)
}

// CreateForumThread is the resolver for the createForumThread field.
func (r *mutationResolver) CreateForumThread(ctx context.Context, input models.CreateForumThreadInput) (*models.ForumThread, error) {
	return r.ForumService.CreateForumThread(ctx, input)
}

// CreateForumReply is the resolver for the createForumReply field.
func (r *mutationResolver) CreateForumReply(ctx context.Context, input models.CreateForumReplyInput) (*models.ForumReply, error) {
	return r.ForumService.CreateForumReply(ctx, input)
}

// Forum is the resolver for the forum field.
func (r *queryResolver) Forum(ctx context.Context, id primitive.ObjectID) (*models.Forum, error) {
	return r.ForumService.Forum(ctx, id)
}

// Forums is the resolver for the forums field.
func (r *queryResolver) Forums(ctx context.Context, clubID primitive.ObjectID, page *int, pageSize *int) (*models.ForumPage, error) {
	return r.ForumService.Forums(ctx, clubID, page, pageSize)
}

// ForumThread is the resolver for the forumThread field.
func (r *queryResolver) ForumThread(ctx context.Context, id primitive.ObjectID) (*models.ForumThread, error) {
	return r.ForumService.ForumThread(ctx, id)
}

// ForumThreads is the resolver for the forumThreads field.
func (r *queryResolver) ForumThreads(ctx context.Context, forumID primitive.ObjectID, page *int, pageSize *int) (*models.ThreadsPage, error) {
	return r.ForumService.ForumThreads(ctx, forumID, page, pageSize)
}

// ForumReplies is the resolver for the forumReplies field.
func (r *queryResolver) ForumReplies(ctx context.Context, threadID primitive.ObjectID, page *int, pageSize *int) (*models.RepliesPage, error) {
	return r.ForumService.ForumReplies(ctx, threadID, page, pageSize)
}
