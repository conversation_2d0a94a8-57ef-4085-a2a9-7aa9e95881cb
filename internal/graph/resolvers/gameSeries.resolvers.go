package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetGameSeriesByID is the resolver for the getGameSeriesById field.
func (r *queryResolver) GetGameSeriesByID(ctx context.Context, gameSeriesID primitive.ObjectID) (*models.GameSeries, error) {
	panic(fmt.Errorf("not implemented: GetGameSeriesByID - getGameSeriesById"))
}
