package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"
)

// CreateInstitution is the resolver for the createInstitution field.
func (r *mutationResolver) CreateInstitution(ctx context.Context, input models.CreateInstitutionInput) (*models.Institution, error) {
	return r.InstitutionService.AddNewInstitute(ctx, input)
}

// SearchInstitutions is the resolver for the searchInstitutions field.
func (r *queryResolver) SearchInstitutions(ctx context.Context, query string, limit *int) ([]*models.Institution, error) {
	return r.InstitutionService.SearchInstitute(ctx, query, limit)
}
