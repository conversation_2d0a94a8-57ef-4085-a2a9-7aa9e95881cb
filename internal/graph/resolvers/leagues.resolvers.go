package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreateLeague is the resolver for the createLeague field.
func (r *mutationResolver) CreateLeague(ctx context.Context, input models.CreateLeagueInput) (*models.League, error) {
	return r.LeagueService.CreateLeague(ctx, input)
}

// JoinLeague is the resolver for the joinLeague field.
func (r *mutationResolver) JoinLeague(ctx context.Context, joinLeagueInput *models.JoinLeagueInput) (bool, error) {
	return r.LeagueService.JoinLeague(ctx, joinLeagueInput)
}

// GetLeague is the resolver for the getLeague field.
func (r *queryResolver) GetLeague(ctx context.Context, id primitive.ObjectID) (*models.League, error) {
	return r.LeagueService.GetLeague(ctx, id)
}

// GetLeaguesByStatus is the resolver for the getLeaguesByStatus field.
func (r *queryResolver) GetLeaguesByStatus(ctx context.Context, statuses []models.LeagueStatus, page *int, pageSize *int, sortDirection *string) (*models.PaginatedLeagues, error) {
	return r.LeagueService.GetLeaguesByStatus(ctx, statuses, page, pageSize, sortDirection)
}

// GetLeagueLeaderboard is the resolver for the getLeagueLeaderboard field.
func (r *queryResolver) GetLeagueLeaderboard(ctx context.Context, leagueID primitive.ObjectID, page int, pageSize int) (*models.LeagueLeaderboardPage, error) {
	return r.LeagueService.GetLeagueLeaderboard(ctx, leagueID, page, pageSize)
}
