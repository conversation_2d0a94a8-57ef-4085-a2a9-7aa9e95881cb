package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// UpdateLastMessageRead is the resolver for the updateLastMessageRead field.
func (r *mutationResolver) UpdateLastMessageRead(ctx context.Context, groupID primitive.ObjectID, lastMessageReadID primitive.ObjectID) (bool, error) {
	return r.MessageService.UpdateLastMessageRead(ctx, groupID, lastMessageReadID)
}

// GetMessagesByGroupID is the resolver for the getMessagesByGroupId field.
func (r *queryResolver) GetMessagesByGroupID(ctx context.Context, groupID primitive.ObjectID, lastMessageID *primitive.ObjectID, pageSize *int, sortDirection *models.SortDirection) (*models.PaginatedMessage, error) {
	return r.MessageService.GetMessagesByGroupID(ctx, groupID, lastMessageID, pageSize, sortDirection)
}

// GetMessageGroupDetailsByID is the resolver for the getMessageGroupDetailsById field.
func (r *queryResolver) GetMessageGroupDetailsByID(ctx context.Context, groupID primitive.ObjectID) (*models.MessageGroup, error) {
	return r.MessageService.GetMessageGroupDetailsByID(ctx, groupID)
}

// GetAllMessageGroups is the resolver for the getAllMessageGroups field.
func (r *queryResolver) GetAllMessageGroups(ctx context.Context, input *models.GetAllMessageGroupsInput) (*models.PaginatedMessageGroups, error) {
	return r.MessageService.GetAllMessageGroup(ctx, input)
}

// GetMessageGroupIDForFriends is the resolver for the getMessageGroupIdForFriends field.
func (r *queryResolver) GetMessageGroupIDForFriends(ctx context.Context, friendID primitive.ObjectID) (primitive.ObjectID, error) {
	return r.MessageService.GetMessageGroupIdForFriends(ctx, friendID)
}
