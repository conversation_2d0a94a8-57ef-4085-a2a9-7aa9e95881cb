package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	"github.com/99designs/gqlgen/graphql"
)

// UploadFiles is the resolver for the uploadFiles field.
func (r *mutationResolver) UploadFiles(ctx context.Context, files []*graphql.Upload) ([]*models.File, error) {
	return r.UserService.UploadFiles(ctx, files)
}

// SendOtp is the resolver for the sendOTP field.
func (r *mutationResolver) SendOtp(ctx context.Context, email string) (bool, error) {
	return r.UserService.SendOtp(ctx, email)
}

// VerifyOtp is the resolver for the verifyOTP field.
func (r *mutationResolver) VerifyOtp(ctx context.Context, otp string) (bool, error) {
	return r.UserService.VerifyOTP(ctx, otp)
}

// GetPlatformStats is the resolver for the getPlatformStats field.
func (r *queryResolver) GetPlatformStats(ctx context.Context) (*models.PlatformStats, error) {
	return r.GameService.GetPlatformStats(ctx)
}
