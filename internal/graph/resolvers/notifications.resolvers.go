package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"
)

// RegisterDeviceToken is the resolver for the registerDeviceToken field.
func (r *mutationResolver) RegisterDeviceToken(ctx context.Context, pushNotificationToken string, deviceID *string, platform *string) (*models.DeviceTokenRegistrationResponse, error) {
	return r.NotificationService.RegisterDeviceToken(ctx, pushNotificationToken, deviceID, platform)
}

// UnregisterDeviceToken is the resolver for the unregisterDeviceToken field.
func (r *mutationResolver) UnregisterDeviceToken(ctx context.Context, pushNotificationToken *string, deviceID *string) (*models.DeviceTokenRegistrationResponse, error) {
	return r.NotificationService.UnregisterDeviceToken(ctx, pushNotificationToken, deviceID)
}

// SendFeedback is the resolver for the sendFeedback field.
func (r *mutationResolver) SendFeedback(ctx context.Context, input models.Feedback) (*bool, error) {
	return r.NotificationService.SendFeedback(ctx, &input)
}
