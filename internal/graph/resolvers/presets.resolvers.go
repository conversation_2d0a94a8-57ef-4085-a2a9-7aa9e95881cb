package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// SubmitUserPresetResult is the resolver for the submitUserPresetResult field.
func (r *mutationResolver) SubmitUserPresetResult(ctx context.Context, userPresetResultInput *models.UserPresetResultInput) (*bool, error) {
	return r.PresetsService.SubmitUserPresetResult(ctx, userPresetResultInput)
}

// SaveUserPreset is the resolver for the saveUserPreset field.
func (r *mutationResolver) SaveUserPreset(ctx context.Context, identifier *string, name *string) (*models.UserPreset, error) {
	return r.PresetsService.SaveUserPreset(ctx, identifier, name)
}

// DeleteUserSavedPreset is the resolver for the deleteUserSavedPreset field.
func (r *mutationResolver) DeleteUserSavedPreset(ctx context.Context, presetID primitive.ObjectID) (*bool, error) {
	return r.PresetsService.DeleteUserSavedPreset(ctx, presetID)
}

// GetGlobalPresets is the resolver for the getGlobalPresets field.
func (r *queryResolver) GetGlobalPresets(ctx context.Context, page *int, pageSize *int) (*models.GlobalPresets, error) {
	return r.PresetsService.GetGlobalPresets(ctx, page, pageSize)
}

// GetGlobalPresetsByIdentifier is the resolver for the getGlobalPresetsByIdentifier field.
func (r *queryResolver) GetGlobalPresetsByIdentifier(ctx context.Context, identifier *string) (*models.GlobalPreset, error) {
	return r.PresetsService.GetGlobalPresetsByIdentifier(ctx, identifier)
}

// GetUserPresetsByIdentifier is the resolver for the getUserPresetsByIdentifier field.
func (r *queryResolver) GetUserPresetsByIdentifier(ctx context.Context, identifier *string) (*models.UserPreset, error) {
	return r.PresetsService.GetUserPresetsByIdentifier(ctx, identifier)
}

// GetUserPresetsStatsByDate is the resolver for the getUserPresetsStatsByDate field.
func (r *queryResolver) GetUserPresetStatsByDate(ctx context.Context, username *string, durationFilter *int, identifier *string) ([]*models.UserPresetDayStats, error) {
	return r.PresetsService.GetUserPresetStatsByDate(ctx, username, durationFilter, identifier)
}

// GetUserRecentPresets is the resolver for the getUserRecentPresets field.
func (r *queryResolver) GetUserRecentPresets(ctx context.Context) (*models.UserPresets, error) {
	return r.PresetsService.GetUserRecentPresets(ctx, nil, nil)
}

// GetUserSavedPresets is the resolver for the getUserSavedPresets field.
func (r *queryResolver) GetUserSavedPresets(ctx context.Context, page *int, pageSize *int) (*models.UserPresets, error) {
	return r.PresetsService.GetUserSavedPresets(ctx, page, pageSize)
}

// GetUsersAllPlayedPresets is the resolver for the getUsersAllPlayedPresets field.
func (r *queryResolver) GetUsersAllPlayedPresets(ctx context.Context, username *string) (*models.AllPlayedPresetsOutput, error) {
	return r.PresetsService.GetUsersAllPlayedPresets(ctx, username)
}
