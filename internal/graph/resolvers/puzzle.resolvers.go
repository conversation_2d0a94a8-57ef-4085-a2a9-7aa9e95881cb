package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// SubmitPuzzleSolution is the resolver for the submitPuzzleSolution field.
func (r *mutationResolver) SubmitPuzzleSolution(ctx context.Context, puzzleID primitive.ObjectID, timeSpent int) (*models.PuzzleResult, error) {
	return r.PuzzleService.SubmitPuzzleSolution(ctx, puzzleID, timeSpent)
}

// GetDailyPuzzle is the resolver for the getDailyPuzzle field.
func (r *queryResolver) GetDailyPuzzle(ctx context.Context, date string) (*models.Puzzle, error) {
	return r.PuzzleService.GetDailyPuzzle(ctx, date)
}

// GetPuzzleSubmissionsByMonth is the resolver for the getPuzzleSubmissionsByMonth field.
func (r *queryResolver) GetPuzzleSubmissionsByMonth(ctx context.Context, yearMonths []string) ([]*models.PuzzleMonthlySubmissionReport, error) {
	return r.PuzzleService.GetPuzzleSubmissionsByMonth(ctx, yearMonths)
}

// GetUserPuzzleStats is the resolver for the getUserPuzzleStats field.
func (r *queryResolver) GetUserPuzzleStats(ctx context.Context) (*models.PuzzleUserStats, error) {
	return r.PuzzleService.GetUserPuzzleStats(ctx)
}

// GetDailyPuzzleByType is the resolver for the getDailyPuzzleByType field.
func (r *queryResolver) GetDailyPuzzleByType(ctx context.Context, date string, puzzleType models.PuzzleType) (*models.Puzzle, error) {
	return r.PuzzleService.GetDailyPuzzleByType(ctx, date, puzzleType)
}

// GetPuzzleSubmissionsByMonthByType is the resolver for the getPuzzleSubmissionsByMonthByType field.
func (r *queryResolver) GetPuzzleSubmissionsByMonthByType(ctx context.Context, yearMonths []string, puzzleType models.PuzzleType) ([]*models.PuzzleMonthlySubmissionReport, error) {
	return r.PuzzleService.GetPuzzleSubmissionsByMonthByType(ctx, yearMonths, puzzleType)
}

// GetUserPuzzleStatsByType is the resolver for the getUserPuzzleStatsByType field.
func (r *queryResolver) GetUserPuzzleStatsByType(ctx context.Context, puzzleType models.PuzzleType) (*models.PuzzleUserStats, error) {
	return r.PuzzleService.GetUserPuzzleStatsByType(ctx, puzzleType)
}
