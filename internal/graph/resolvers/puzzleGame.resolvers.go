package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreatePuzzleGame is the resolver for the createPuzzleGame field.
func (r *mutationResolver) CreatePuzzleGame(ctx context.Context, gameConfig *models.PuzzleGameConfigInput) (*models.PuzzleGame, error) {
	return r.PuzzleGameService.CreatePuzzleGame(ctx, gameConfig)
}

// SubmitPuzzleGameAnswer is the resolver for the submitPuzzleGameAnswer field.
func (r *mutationResolver) SubmitPuzzleGameAnswer(ctx context.Context, answerInput *models.SubmitPuzzleGameAnswerInput) (*models.PuzzleGame, error) {
	return r.PuzzleGameService.SubmitPuzzleGameAnswer(ctx, answerInput)
}

// JoinPuzzleGame is the resolver for the joinPuzzleGame field.
func (r *mutationResolver) JoinPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	return r.PuzzleGameService.JoinPuzzleGame(ctx, gameID)
}

// RemovePlayerFromPuzzleGame is the resolver for the removePlayerFromPuzzleGame field.
func (r *mutationResolver) RemovePlayerFromPuzzleGame(ctx context.Context, gameID primitive.ObjectID, playerID primitive.ObjectID) (bool, error) {
	return r.PuzzleGameService.RemovePlayerFromPuzzleGame(ctx, gameID, playerID)
}

// LeavePuzzleGame is the resolver for the leavePuzzleGame field.
func (r *mutationResolver) LeavePuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	return r.PuzzleGameService.LeavePuzzleGame(ctx, gameID)
}

// StartPuzzleGame is the resolver for the startPuzzleGame field.
func (r *mutationResolver) StartPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	return r.PuzzleGameService.StartPuzzleGame(ctx, gameID)
}

// EndPuzzleGame is the resolver for the endPuzzleGame field.
func (r *mutationResolver) EndPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	return r.PuzzleGameService.EndPuzzleGame(ctx, gameID)
}

// StartSearchingForPuzzleGame is the resolver for the startSearchingForPuzzleGame field.
func (r *mutationResolver) StartSearchingForPuzzleGame(ctx context.Context, gameConfig *models.PuzzleGameConfigInput) (*bool, error) {
	return r.PuzzleGameService.StartSearchingForPuzzleGame(ctx, gameConfig)
}

// AbortSearchingForPuzzleGame is the resolver for the abortSearchingForPuzzleGame field.
func (r *mutationResolver) AbortSearchingForPuzzleGame(ctx context.Context) (*bool, error) {
	return r.PuzzleGameService.AbortSearchingForPuzzleGame(ctx)
}

// CancelPuzzleGame is the resolver for the cancelPuzzleGame field.
func (r *mutationResolver) CancelPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*bool, error) {
	return r.PuzzleGameService.CancelPuzzleGame(ctx, gameID)
}

// RequestRematchForPuzzleGame is the resolver for the requestRematchForPuzzleGame field.
func (r *mutationResolver) RequestRematchForPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	return r.PuzzleGameService.RequestRematchForPuzzleGame(ctx, gameID)
}

// AcceptRematchOfPuzzleGame is the resolver for the acceptRematchOfPuzzleGame field.
func (r *mutationResolver) AcceptRematchOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	return r.PuzzleGameService.AcceptRematchOfPuzzleGame(ctx, gameID)
}

// RejectRematchOfPuzzleGame is the resolver for the rejectRematchOfPuzzleGame field.
func (r *mutationResolver) RejectRematchOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	return r.PuzzleGameService.RejectRematchOfPuzzleGame(ctx, gameID)
}

// ChallengeUserForPuzzleGame is the resolver for the challengeUserForPuzzleGame field.
func (r *mutationResolver) ChallengeUserForPuzzleGame(ctx context.Context, challengeUserInput *models.ChallengeUserForPuzzleGameInput) (*models.PuzzleGame, error) {
	return r.PuzzleGameService.ChallengeUserForPuzzleGame(ctx, challengeUserInput)
}

// AcceptChallengeOfPuzzleGame is the resolver for the acceptChallengeOfPuzzleGame field.
func (r *mutationResolver) AcceptChallengeOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	return r.PuzzleGameService.AcceptChallengeOfPuzzleGame(ctx, gameID)
}

// RejectChallengeOfPuzzleGame is the resolver for the rejectChallengeOfPuzzleGame field.
func (r *mutationResolver) RejectChallengeOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*bool, error) {
	return r.PuzzleGameService.RejectChallengeOfPuzzleGame(ctx, gameID)
}

// SubmitPuzzleGameRush is the resolver for the submitPuzzleGameRush field.
func (r *mutationResolver) SubmitPuzzleGameRush(ctx context.Context, input models.SubmitPuzzleRushGame) (*models.CrossMathPuzzleRush, error) {
	return r.PuzzleGameService.SubmitPuzzleGameRush(ctx, input)
}

// GetPuzzleGameByID is the resolver for the getPuzzleGameById field.
func (r *queryResolver) GetPuzzleGameByID(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	return r.PuzzleGameService.GetPuzzleGameByID(ctx, gameID)
}

// GetPuzzleGamesByUser is the resolver for the getPuzzleGamesByUser field.
func (r *queryResolver) GetPuzzleGamesByUser(ctx context.Context, payload *models.GetPuzzleGamesInput) (*models.GetPuzzleGamesOutput, error) {
	return r.PuzzleGameService.GetPuzzleGamesByUser(ctx, payload)
}

// GetMyCrossMathPuzzleRushStats is the resolver for the getMyCrossMathPuzzleRushStats field.
func (r *queryResolver) GetMyCrossMathPuzzleRushStats(ctx context.Context) (*models.CrossMathPuzzleRushStats, error) {
	return r.PuzzleGameService.GetMyCrossMathPuzzleRushStats(ctx)
}

// GetGlobalTop5CrossMathPuzzleRushStats is the resolver for the getGlobalTop5CrossMathPuzzleRushStats field.
func (r *queryResolver) GetGlobalTop5CrossMathPuzzleRushStats(ctx context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error) {
	return r.PuzzleGameService.GetGlobalTop5CrossMathPuzzleRushStats(ctx)
}

// GetFriendsTop5CrossMathPuzzleRushStats is the resolver for the getFriendsTop5CrossMathPuzzleRushStats field.
func (r *queryResolver) GetFriendsTop5CrossMathPuzzleRushStats(ctx context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error) {
	return r.PuzzleGameService.GetFriendsTop5CrossMathPuzzleRushStats(ctx)
}
