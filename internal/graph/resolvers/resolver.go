package resolvers

//go:generate go run generate.go

// This file will not be regenerated automatically.
//
// It serves as dependency injection for your app, add any dependencies you require here.

import (
	"matiksOfficial/matiks-server-go/internal/domain"

	"go.uber.org/fx"
)

type Resolver struct {
	UserService                    domain.UserStore
	ContestService                 domain.ContestStore
	DailyChallengeService          domain.DailyChallengeStore
	GameService                    domain.GameStore
	PresetsService                 domain.PresetsStore
	FriendsAndFollowersService     domain.FriendsAndFollowersStore
	NotificationService            domain.NotificationStore
	ShowdownService                domain.ShowdownStore
	UserResolutionService          domain.UserResolutionStore
	UserSettingsService            domain.UserSettingsStore
	LeagueService                  domain.LeagueStore
	PuzzleService                  domain.PuzzleStore
	MessageService                 domain.MessageStore
	PuzzleGameService              domain.PuzzleGameStore
	ClubService                    domain.ClubsStore
	ClubEventService               domain.ClubEventStore
	ClubAnnouncementService        domain.ClubAnnouncementStore
	ForumService                   domain.ForumStore
	AnnouncementService            domain.AnnouncementStore
	FeedService                    domain.FeedStore
	InstitutionService             domain.InstitutionStore
	UserStreak                     domain.UserStreakStore
	StreakShieldTransactionService domain.StreakShieldTransactionStore
}

func NewResolver(
	UserService domain.UserStore,
	ContestService domain.ContestStore,
	DailyChallengeService domain.DailyChallengeStore,
	GameService domain.GameStore,
	PresetsService domain.PresetsStore,
	FriendsAndFollowersService domain.FriendsAndFollowersStore,
	NotificationService domain.NotificationStore,
	ShowdownService domain.ShowdownStore,
	UserResolutionService domain.UserResolutionStore,
	UserSettingsService domain.UserSettingsStore,
	LeagueService domain.LeagueStore,
	PuzzleService domain.PuzzleStore,
	MessageService domain.MessageStore,
	PuzzleGameService domain.PuzzleGameStore,
	ClubService domain.ClubsStore,
	ClubEventService domain.ClubEventStore,
	ClubAnnouncementService domain.ClubAnnouncementStore,
	ForumService domain.ForumStore,
	FeedService domain.FeedStore,
	AnnouncementService domain.AnnouncementStore,
	InstitutionService domain.InstitutionStore,
	UserStreak domain.UserStreakStore,
	StreakShieldTransactionService domain.StreakShieldTransactionStore,
) *Resolver {
	return &Resolver{
		UserService:                    UserService,
		ContestService:                 ContestService,
		DailyChallengeService:          DailyChallengeService,
		GameService:                    GameService,
		PresetsService:                 PresetsService,
		FriendsAndFollowersService:     FriendsAndFollowersService,
		NotificationService:            NotificationService,
		ShowdownService:                ShowdownService,
		UserResolutionService:          UserResolutionService,
		UserSettingsService:            UserSettingsService,
		LeagueService:                  LeagueService,
		PuzzleService:                  PuzzleService,
		MessageService:                 MessageService,
		PuzzleGameService:              PuzzleGameService,
		ClubService:                    ClubService,
		ClubEventService:               ClubEventService,
		ClubAnnouncementService:        ClubAnnouncementService,
		ForumService:                   ForumService,
		FeedService:                    FeedService,
		AnnouncementService:            AnnouncementService,
		InstitutionService:             InstitutionService,
		UserStreak:                     UserStreak,
		StreakShieldTransactionService: StreakShieldTransactionService,
	}
}

var Module = fx.Module(
	"resolver",
	fx.Provide(NewResolver),
)
