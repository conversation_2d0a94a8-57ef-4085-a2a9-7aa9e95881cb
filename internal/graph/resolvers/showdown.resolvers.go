package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreateShowdown is the resolver for the createShowdown field.
func (r *mutationResolver) CreateShowdown(ctx context.Context, input models.CreateShowdownInput) (*models.Showdown, error) {
	return r.ShowdownService.CreateShowdown(ctx, input)
}

// RegisterForShowdown is the resolver for the registerForShowdown field.
func (r *mutationResolver) RegisterForShowdown(ctx context.Context, input models.ShowdownRegistrationFormValuesInput) (bool, error) {
	return r.ShowdownService.RegisterForShowdown(ctx, input)
}

// UnregisterFromShowdown is the resolver for the unregisterFromShowdown field.
func (r *mutationResolver) UnregisterFromShowdown(ctx context.Context, showdownID primitive.ObjectID) (bool, error) {
	return r.ShowdownService.UnregisterFromShowdown(ctx, showdownID)
}

// StartGameForShowdown is the resolver for the startGameForShowdown field.
func (r *mutationResolver) StartGameForShowdown(ctx context.Context, input *models.StartGameForShowdownInput) (*models.Game, error) {
	panic(fmt.Errorf("not implemented: StartGameForShowdown - startGameForShowdown"))
}

// GetShowdownByID is the resolver for the getShowdownById field.
func (r *queryResolver) GetShowdownByID(ctx context.Context, showdownID primitive.ObjectID) (*models.Showdown, error) {
	return r.ShowdownService.GetShowdownByID(ctx, showdownID)
}

// GetUpcomingShowdown is the resolver for the getUpcomingShowdown field.
func (r *queryResolver) GetUpcomingShowdown(ctx context.Context) (*models.Showdown, error) {
	panic(fmt.Errorf("not implemented: GetUpcomingShowdown - getUpcomingShowdown"))
}

// GetShowdownByStatus is the resolver for the getShowdownByStatus field.
func (r *queryResolver) GetShowdownByStatus(ctx context.Context, status *models.ShowdownContestStatus) (*models.Showdown, error) {
	panic(fmt.Errorf("not implemented: GetShowdownByStatus - getShowdownByStatus"))
}

// GetFicturesByShowdownID is the resolver for the getFicturesByShowdownId field.
func (r *queryResolver) GetFicturesByShowdownID(ctx context.Context, showdownID primitive.ObjectID) (*models.FicturesCollection, error) {
	return r.ShowdownService.GetFixturesByShowdownId(ctx, showdownID)
}

// GetPaginatedLeaderboard is the resolver for the getPaginatedLeaderboard field.
func (r *queryResolver) GetPaginatedLeaderboard(ctx context.Context, input *models.PaginatedLeaderboardInput) (*models.PaginatedLeaderboard, error) {
	return r.ShowdownService.GetPaginatedLeaderboard(ctx, input)
}

// GetFeaturedShowdown is the resolver for the getFeaturedShowdown field.
func (r *queryResolver) GetFeaturedShowdown(ctx context.Context) ([]*models.Showdown, error) {
	return r.ShowdownService.GetFeaturedShowdown(ctx)
}

// GetShowdownsByStatus is the resolver for the getShowdownsByStatus field.
func (r *queryResolver) GetShowdownsByStatus(ctx context.Context, statuses []models.ShowdownContestStatus, page *int, pageSize *int, sortDirection *string) (*models.PaginatedShowdowns, error) {
	return r.ShowdownService.GetShowdownsByStatus(ctx, statuses, page, pageSize, sortDirection)
}
