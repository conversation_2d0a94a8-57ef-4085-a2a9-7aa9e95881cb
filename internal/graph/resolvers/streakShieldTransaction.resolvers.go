package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

// GetUserStreakShieldTransactions is the resolver for the getUserStreakShieldTransactions field.
func (r *queryResolver) GetUserStreakShieldTransactions(ctx context.Context, page *int, pageSize *int) (*models.StreakShieldTransactionPage, error) {
	if page == nil {
		page = utils.AllocPtr(1)
	}

	if pageSize == nil {
		pageSize = utils.AllocPtr(20)
	}

	return r.StreakShieldTransactionService.GetUserStreakShieldTransactions(ctx, *page, *pageSize)
}
