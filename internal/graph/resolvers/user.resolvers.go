package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"

	"github.com/99designs/gqlgen/graphql"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Create<PERSON><PERSON> is the resolver for the createUser field.
func (r *mutationResolver) CreateUser(ctx context.Context, userInput *models.UserInput) (*models.User, error) {
	return r.UserService.CreateUser(ctx, userInput)
}

// LoginAsGuest is the resolver for the loginAsGuest field.
func (r *mutationResolver) LoginAsGuest(ctx context.Context, guestID primitive.ObjectID) (*models.User, error) {
	return r.UserService.LoginAsGuest(ctx, guestID)
}

// SignInWithApple is the resolver for the signInWithApple field.
func (r *mutationResolver) SignInWithApple(ctx context.Context, input models.AppleSignInInput) (*models.User, error) {
	return r.UserService.SignInWithApple(ctx, &input)
}

// GoogleLogin is the resolver for the googleLogin field.
func (r *mutationResolver) GoogleLogin(ctx context.Context, authCode string, tokenType *string, expiresIn *string, guestID *primitive.ObjectID) (*models.User, error) {
	return r.UserService.GoogleLogin(ctx, authCode, tokenType, expiresIn, guestID)
}

// LegacyGoogleLogin is the resolver for the legacyGoogleLogin field.
func (r *mutationResolver) LegacyGoogleLogin(ctx context.Context, idToken string, guestID *primitive.ObjectID) (*models.User, error) {
	return r.UserService.LegacyGoogleLogin(ctx, idToken, guestID)
}

// UpdateUser is the resolver for the updateUser field.
func (r *mutationResolver) UpdateUser(ctx context.Context, updateUserInput *models.UpdateUserInput) (*models.User, error) {
	return r.UserService.UpdateUser(ctx, updateUserInput)
}

// SubmitRatingFixtureResponses is the resolver for the submitRatingFixtureResponses field.
func (r *mutationResolver) SubmitRatingFixtureResponses(ctx context.Context, submission []*int, timeTaken int) (*models.UserRatingFixtureSubmission, error) {
	return r.UserService.SubmitRatingFixtureResponses(ctx, submission, timeTaken)
}

// UpdateRatingBasedOnFixtureResponse is the resolver for the updateRatingBasedOnFixtureResponse field.
func (r *mutationResolver) UpdateRatingBasedOnFixtureResponse(ctx context.Context, userStance models.UserStance) (*models.User, error) {
	return r.UserService.UpdateRatingBasedOnFixtureResponse(ctx, userStance)
}

// UploadProfilePicture is the resolver for the uploadProfilePicture field.
func (r *mutationResolver) UploadProfilePicture(ctx context.Context, file graphql.Upload) (*models.File, error) {
	return r.UserService.UploadProfilePicture(ctx, file)
}

// SubmitReferral is the resolver for the submitReferral field.
func (r *mutationResolver) SubmitReferral(ctx context.Context, referralCode string) (bool, error) {
	return r.UserService.SubmitReferral(ctx, referralCode)
}

// DeleteUser is the resolver for the deleteUser field.
func (r *mutationResolver) DeleteUser(ctx context.Context) (bool, error) {
	return r.UserService.DeleteUser(ctx)
}

// Login is the resolver for the login field.
func (r *queryResolver) Login(ctx context.Context, email string, password string) (*models.User, error) {
	return r.UserService.Login(ctx, email, password)
}

// VerifyToken is the resolver for the verifyToken field.
func (r *queryResolver) VerifyToken(ctx context.Context, token string) (*models.User, error) {
	return r.UserService.VerifyToken(ctx, token)
}

// GetUserByID is the resolver for the getUserById field.
func (r *queryResolver) GetUserByID(ctx context.Context, userID primitive.ObjectID) (*models.User, error) {
	return r.UserService.GetUserByID(ctx, userID)
}

// GetCurrentUser is the resolver for the getCurrentUser field.
func (r *queryResolver) GetCurrentUser(ctx context.Context) (*models.User, error) {
	return r.UserService.GetCurrentUser(ctx)
}

// GetUserByUsername is the resolver for the getUserByUsername field.
func (r *queryResolver) GetUserByUsername(ctx context.Context, username *string) (*models.SearchUserOutput, error) {
	return r.UserService.GetUserByUsername(ctx, username)
}

// GetUserLeagueGroupLeaderboard is the resolver for the getUserLeagueGroupLeaderboard field.
func (r *queryResolver) GetUserLeagueGroupLeaderboard(ctx context.Context, page *int, pageSize *int) (*models.WeeklyLeagueLeaderboardPage, error) {
	return r.UserService.GetUserLeagueGroupLeaderboard(ctx, page, pageSize)
}

// LeaderboardV3 is the resolver for the leaderboardV3 field.
func (r *queryResolver) LeaderboardV3(ctx context.Context, countryCode *string, searchKey *string, page *int, limit *int) (*models.UserLeaderboardPage, error) {
	return r.UserService.LeaderBoardV3(ctx, countryCode, searchKey, page, limit)
}

// LeaderboardNew is the resolver for the leaderboard_new field.
func (r *queryResolver) LeaderboardNew(ctx context.Context, countryCode *string, searchKey *string, page *int, limit *int, ratingType *string) (*models.UserLeaderboardPage, error) {
	return r.UserService.LeaderboardNew(ctx, countryCode, searchKey, page, limit, ratingType)
}

// Leaderboard is the resolver for the leaderboard field.
func (r *queryResolver) Leaderboard(ctx context.Context, countryCode *string, searchKey *string, first *int, after *string) (*models.LeaderboardConnection, error) {
	return r.UserService.Leaderboard(ctx, countryCode, searchKey, first, after)
}

// GetRatingFixtureQuestions is the resolver for the getRatingFixtureQuestions field.
func (r *queryResolver) GetRatingFixtureQuestions(ctx context.Context) ([]*models.Question, error) {
	return r.UserService.GetRatingFixtureQuestions(ctx)
}

// GetRatingFixtureSubmission is the resolver for the getRatingFixtureSubmission
func (r *queryResolver) GetRatingFixtureSubmission(ctx context.Context) (*models.UserRatingFixtureSubmission, error) {
	return r.UserService.GetRatingFixtureSubmission(ctx)
}

// IsUsernameAvailable is the resolver for the isUsernameAvailable field.
func (r *queryResolver) IsUsernameAvailable(ctx context.Context, username string) (bool, error) {
	return r.UserService.IsUsernameAvailable(ctx, &username)
}

// OnlineUsers is the resolver for the onlineUsers field.
func (r *queryResolver) OnlineUsers(ctx context.Context, page int, pageSize int) (*models.OnlineUsersPage, error) {
	return r.UserService.OnlineUsers(ctx, page, pageSize)
}

// StatikCoinsLeaderboard is the resolver for the statikCoinsLeaderboard field.
func (r *queryResolver) StatikCoinsLeaderboard(ctx context.Context, page int, pageSize *int, leaderboardType *models.StatikCoinLeaderboardType) (*models.StatikCoinLeaderboardPage, error) {
	return r.UserService.GetStatikCoinsLeaderboard(ctx, page, pageSize, leaderboardType)
}

// GetTimeSpentByUser is the resolver for the getTimeSpentByUser field.
func (r *queryResolver) GetTimeSpentByUser(ctx context.Context, date *string) (int, error) {
	return r.UserService.GetTimeSpentByUser(ctx, date)
}

// GetGlobalTopPlayers is the resolver for the getGlobalTopPlayers field.
func (r *queryResolver) GetGlobalTopPlayers(ctx context.Context) (*models.TopPlayersLeaderboard, error) {
	return r.UserService.GetGlobalTopPlayers(ctx)
}

// GetFriendsLeaderboard is the resolver for the getFriendsLeaderboard field.
func (r *queryResolver) GetFriendsLeaderboard(ctx context.Context, page *int, pageSize *int, ratingType *string) (*models.UserLeaderboardPage, error) {
	return r.UserService.GetFriendsLeaderboard(ctx, page, pageSize, ratingType)
}

// GetFriendsTopPlayers is the resolver for the getFriendsTopPlayers field.
func (r *queryResolver) GetFriendsTopPlayers(ctx context.Context) (*models.TopPlayersLeaderboard, error) {
	return r.UserService.GetFriendsTopPlayers(ctx)
}

// GetUsersWeeklyStatikCoins is the resolver for the getUsersWeeklyStatikCoins field.
func (r *queryResolver) GetUsersWeeklyStatikCoins(ctx context.Context) (int, error) {
	return r.UserService.GetUsersWeeklyStatikCoins(ctx)
}

// GetUsersWeeklyStatikCoinsV2 is the resolver for the getUsersWeeklyStatikCoinsV2 field.
func (r *queryResolver) GetUsersWeeklyStatikCoinsV2(ctx context.Context, userID primitive.ObjectID) (*models.UsersWeeklyStatikCoinsOutput, error) {
	return r.UserService.GetUsersWeeklyStatikCoinsV2(ctx, userID)
}

// GetUsersOfMyInstitute is the resolver for the getUsersOfMyInstitute field.
func (r *queryResolver) GetUsersOfMyInstitute(ctx context.Context, page *int, pageSize *int) (*models.MyInstituteUsersPage, error) {
	return r.UserService.GetUsersOfMyInstitute(ctx, page, pageSize)
}

// SearchUsersInMyInstitute is the resolver for the searchUsersInMyInstitute field.
func (r *queryResolver) SearchUsersInMyInstitute(ctx context.Context, searchKey *string, page *int, pageSize *int) (*models.MyInstituteUsersPage, error) {
	return r.UserService.SearchUsersInMyInstitute(ctx, searchKey, page, pageSize)
}

// UserEvents is the resolver for the userEvents field.
func (r *subscriptionResolver) UserEvents(ctx context.Context, userID *primitive.ObjectID) (<-chan models.UserEvent, error) {
	panic(fmt.Errorf("not implemented: UserEvents - userEvents"))
}
