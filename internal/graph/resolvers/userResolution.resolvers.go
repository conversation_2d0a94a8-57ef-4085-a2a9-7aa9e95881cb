package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"
)

// Take<PERSON><PERSON> is the resolver for the takePledge field.
func (r *mutationResolver) TakePledge(ctx context.Context, duration *int) (*bool, error) {
	return r.UserResolutionService.TakePledge(ctx, duration)
}

// CheckIfPledgeTaken is the resolver for the checkIfPledgeTaken field.
func (r *queryResolver) CheckIfPledgeTaken(ctx context.Context) (*bool, error) {
	return r.UserResolutionService.CheckIfPledgeTaken(ctx)
}

// GetUserResolution is the resolver for the getUserResolution field.
func (r *queryResolver) GetUserResolution(ctx context.Context) (*models.UserResolution, error) {
	return r.UserResolutionService.GetUserResolution(ctx)
}
