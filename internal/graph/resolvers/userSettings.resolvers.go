package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"
)

// UpdateUserSettings is the resolver for the updateUserSettings field.
func (r *mutationResolver) UpdateUserSettings(ctx context.Context, settings *models.UpdateSettingsInput) (*models.UserSettings, error) {
	return r.UserSettingsService.UpdateUserSettings(ctx, settings)
}

// GetUserSettings is the resolver for the getUserSettings field.
func (r *queryResolver) GetUserSettings(ctx context.Context) (*models.UserSettings, error) {
	return r.UserSettingsService.GetUserSettings(ctx)
}
