package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"
)

// UseStreakFreezer is the resolver for the useStreakFreezer field.
func (r *mutationResolver) UseStreakFreezer(ctx context.Context) (bool, error) {
	return r.UserStreak.UseStreakFreezer(ctx)
}

// GetUpdatedUserStreaks is the resolver for the getUpdatedUserStreaks field.
func (r *mutationResolver) GetUpdatedUserStreaks(ctx context.Context) (*models.UserStreaks, error) {
	return r.UserStreak.GetUserUpdatedStreak(ctx)
}

// GetUserStreakHistoryByMonth is the resolver for the getUserStreakHistoryByMonth field.
func (r *queryResolver) GetUserStreakHistoryByMonth(ctx context.Context, yearMonths []string) ([]*models.StreakEntry, error) {
	return r.UserStreak.GetUserStreakHistoryByMonth(ctx, yearMonths)
}

// CheckUserStreakStatus is the resolver for the checkUserStreakStatus field.
func (r *queryResolver) CheckUserStreakStatus(ctx context.Context) (*models.StreakStatusResponse, error) {
	return r.UserStreak.CheckUserStreakStatus(ctx)
}
