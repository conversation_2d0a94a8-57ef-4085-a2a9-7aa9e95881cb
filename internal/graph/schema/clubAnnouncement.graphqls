type ClubAnnouncement {
    id: ID!
    clubId: ID!
    title: String!
    content: String!
    createdAt: Time!
    createdBy: ID!
    creatorInfo: ClubAnnouncementCreatorInfo
}

type ClubAnnouncementCreatorInfo {
    username: String!
    profileImageUrl: String
}

input CreateClubAnnouncementInput {
    clubId: ID!
    title: String!
    content: String!
}

type ClubAnnouncementsPage {
    results: [ClubAnnouncement!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

extend type Query {
    clubAnnouncements(
        page: Int = 1
        pageSize: Int = 20
        clubId: ID
        from: Time
        to: Time
    ): ClubAnnouncementsPage! @auth
}

extend type Mutation {
    createClubAnnouncement(
        input: CreateClubAnnouncementInput!
    ): ClubAnnouncement! @auth
    deleteClubAnnouncement(id: ID!): Boolean! @auth
}
