type DailyChallenge {
    _id: ID!
    challengeStatus: String
    questions: [GameQuestion!]!
    startTime: Time
    endTime: Time
    hasAttempted: Boolean
    encryptedQuestions: [String]
    challengeNumber: Int
    division: CHALLENGE_DIVISION
    stats: DailyChallengeStat
}

type DailyChallengeStat {
    totalAttempts: Int
    totalSubmission: Int
    averageTime: Int
    bestTime: Int
    averageAccuracy: Float
}

type Result {
    userId: ID!
    challengeId: ID
    submittedTimes: [Int]
    score: Int
    completedAt: Time
}

type ChallengeResult {
    user: UserPublicDetails!
    rank: Int!
    score: Float!
}

type LeaderboardPage {
    results: [ChallengeResult!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

enum RESULT_STATUS {
    ATTEMPTED
    COMPLETED
}

enum CHALLENGE_DIVISION {
    OPEN
    DIV1
    DIV2
    DIV3
}

type DailyChallengeResult {
    userId: ID
    challengeId: ID
    score: Int
    completedAt: Time
    rank: Int
    statikCoinsEarned: Int
    resultStatus: RESULT_STATUS
}

type UserResult {
    success: Boolean
    error: String
    result: DailyChallengeResult
}

type UserDailyChallengeStats {
    _id: ID!
    userId: ID!
    division: CHALLENGE_DIVISION!
    totalAttempts: Int!
    totalSubmission: Int!
    averageTime: Int!
    bestTime: Int!
    streaks: UserDailyChallengeStreaks!
    averageAccuracy: Float
}

type UserDailyChallengeStreaks {
    current: Int!
    highest: Int!
    lastPlayedDate: Time
}

type DailyChallengeResultWithStats {
    result: DailyChallengeResult
    stats: UserDailyChallengeStats
}

type UserDailyChallengeResultWithStats {
    success: Boolean
    error: String
    result: DailyChallengeResultWithStats
}

type BotDetectionResult {
    isBotBehavior: Boolean!
    userId: ID!
    challengeId: ID!
}

# Define the Query type for getting the daily challenge
extend type Query {
    getUserResult(challengeNumber: Int): UserResult @auth
    getDailyChallenge: DailyChallenge @auth
    getDailyChallenges: [DailyChallenge] @auth
    getDailyChallengeById(id: ID!): DailyChallenge @auth
    getDailyChallengeLeaderboard(
        challengeNumber: Int
        pageNumber: Int
        pageSize: Int
    ): LeaderboardPage @auth
    checkBotBehavior(challengeId: ID!, userId: ID!): BotDetectionResult! @auth
    getUserResultByDivision(
        dateStr: String
        division: CHALLENGE_DIVISION
    ): UserResult @auth
    getUserResultByDailyChallengeId(
        challengeId: ID!
    ): UserDailyChallengeResultWithStats @auth
    getDailyChallengeLeaderboardByDivision(
        dateStr: String
        division: CHALLENGE_DIVISION
        pageNumber: Int
        pageSize: Int
    ): LeaderboardPage @auth
    getUserResultByDivison(
        dateStr: String
        division: CHALLENGE_DIVISION
    ): UserResult @auth @deprecated(reason: "Use getUserResultByDivision instead")
    getDailyChallengeLeaderboardByDivison(
        dateStr: String
        division: CHALLENGE_DIVISION
        pageNumber: Int
        pageSize: Int
    ): LeaderboardPage
    @auth
    @deprecated(reason: "Use getDailyChallengeLeaderboardByDivision instead")
}

# Define the input type for submitting the solution
input SubmitSolutionInput {
    challengeId: ID!
    submittedTimes: [Int]
    challengeNumber: Int
}

type SubmitChallengeResult {
    success: Boolean
    message: String
    result: Result
}

# Define the Mutation type for submitting the solution
extend type Mutation {
    submitChallengeResult(input: SubmitSolutionInput!): SubmitChallengeResult
    @auth
    attemptDailyChallenge(challengeId: ID!): Boolean! @auth
}
