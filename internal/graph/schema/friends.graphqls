type FriendRequest {
    _id: ID!
    senderId: ID!
    receiverId: ID!
    status: FRIEND_REQUEST_STATUS
    sentAt: Time
    respondedAt: Time
}

type Friends {
    _id: ID!
    senderId: ID!
    receiverId: ID!
    acceptedAt: Time
}

type FollowersAndFollowee {
    _id: ID!
    followerId: ID!
    followeeId: ID!
    followedAt: Time
}

type FriendRequestOutput {
    _id: ID!
    senderId: ID!
    receiverId: ID!
    status: FRIEND_REQUEST_STATUS
    sentAt: Time
    respondedAt: Time
    sender: UserPublicDetails
}

type FriendsOutput {
    _id: ID!
    senderId: ID!
    receiverId: ID!
    acceptedAt: Time
    friendInfo: UserPublicDetails
    isOnline: Boolean
    currActivity: UserActivityType!
}

type FollowersAndFolloweeOutput {
    _id: ID!
    followerId: ID!
    followeeId: ID!
    followedAt: Time
    userInfo: UserPublicDetails
}

type FollowersAndFolloweePage {
    results: [FollowersAndFolloweeOutput!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

type FriendRequestPage {
    results: [FriendRequestOutput!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

type FriendsPage {
    results: [FriendsOutput!]!
    pageNumber: Int!
    pageSize: Int!
    hasMore: Boolean
    totalResults: Int
}

enum FRIEND_REQUEST_STATUS {
    ACCEPTED
    REJECTED
    PENDING
}

enum FRIENDSHIP_STATUS {
    REQUEST_SENT #Current User has sent request for Friendship
    ACCEPTED #Both Are Friends
    PENDING_REQUEST #Searched User has asked for Friendship
    NOT_FRIEND
}

input SendFriendRequestInput {
    userId: ID!
}

input FriendRequestInput {
    userId: ID!
}

input FollowUserInput {
    userId: ID!
}

input UnFollowUserInput {
    userId: ID!
}

input RemoveFollowerInput {
    userId: ID!
}

input WithdrawFriendRequestInput {
    userId: ID!
}

input RemoveFriendInput {
    userId: ID!
}

extend type Query {
    getFollowers(page: Int = 1, pageSize: Int = 20): FollowersAndFolloweePage
    @auth

    getFollowings(page: Int = 1, pageSize: Int = 20): FollowersAndFolloweePage
    @auth

    getFriends(
        page: Int = 1
        pageSize: Int = 20
        sortOption: SortOptions
    ): FriendsPage @auth

    getPendingFriendRequests(
        page: Int = 1
        pageSize: Int = 20
    ): FriendRequestPage @auth
}

extend type Mutation {
    sendFriendRequest(sendRequestInput: FriendRequestInput): Boolean! @auth
    withdrawFriendRequest(
        withdrawFriendRequestInput: WithdrawFriendRequestInput
    ): Boolean! @auth

    acceptFriendRequest(acceptRequestInput: FriendRequestInput): Boolean! @auth
    rejectFriendRequest(rejectRequestInput: FriendRequestInput): Boolean! @auth

    followUser(followUserInput: FollowUserInput): Boolean! @auth
    unFollowUser(unFollowUserInput: UnFollowUserInput): Boolean! @auth
    removeFollower(removeFollowerInput: RemoveFollowerInput): Boolean! @auth

    removeFriend(removeFriendInput: RemoveFriendInput): Boolean! @auth
}
