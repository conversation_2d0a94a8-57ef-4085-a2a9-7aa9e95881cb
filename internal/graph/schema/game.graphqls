type Game {
    _id: ID!
    players: [Player!]!
    gameStatus: GAME_STATUS!
    rematchRequestedBy: ID
    gameType: GAME_TYPE!
    createdBy: ID!
    config: GameConfig
    questions: [GameQuestion]
    minifiedQuestions: [String]
    encryptedQuestions: [String]
    leaderBoard: [LeaderBoardEntry]
    startTime: Time
    endTime: Time
    seriesId: ID
    showdownId: ID
    showdownGameConfig: ShowdownGameConfig
}

type Player {
    userId: ID!
    rating: Int
    statikCoins: Int
    status: PLAYER_STATUS
    timeLeft: Int
}

type GameQuestion {
    question: Question
    submissions: [Submission]
    stats: GameQuestionStats
}

type GameQuestionStats {
    fastestTime: Int
    userIds: [ID]
}

type Submission {
    userId: ID
    timeTaken: Int
    points: Int
    submissionTime: Date
    isCorrect: Boolean
    inCorrectAttempts: Int
    submittedValues: [String]
}

type LeaderBoardEntry {
    userId: ID
    correct: Int
    incorrect: Int
    totalPoints: Float
    ratingChange: Int
    statikCoinsEarned: Int
    rank: Int
}

type GameConfig {
    "timeLimit in seconds"
    timeLimit: Int
    numPlayers: Int
    gameType: GAME_TYPE
    questionTags: [String]
    difficultyLevel: [Int!]
    maxTimePerQuestion: Int
}

input GameConfigInput {
    timeLimit: Int
    numPlayers: Int
    gameType: GAME_TYPE
    questionTags: [String!]
    difficultyLevel: [Int!]
    maxTimePerQuestion: Int
}

enum GAME_STATUS {
    CREATED
    READY
    STARTED
    PAUSED
    ENDED
    CANCELLED
}

enum GAME_TYPE {
    PLAY_ONLINE
    PLAY_WITH_FRIEND
    ONLINE_CHALLENGE
    PRACTICE
    FLASH_ANZAN
    FASTEST_FINGER
    SUMDAY_SHOWDOWN
    GROUP_PLAY
    ABILITY_DUELS
}

enum PLAYER_STATUS {
    INVITED
    ACCEPTED
    REJECTED
}

input JoinGameInput {
    gameId: ID
}

input SubmitAnswerInput {
    gameId: ID
    questionId: String
    submittedValue: String
    isCorrect: Boolean
    inCorrectAttempts: Int
    timeOfSubmission: Date
}

input SubmitFlashAnzanAnswerInput {
    gameId: ID
    questionIdentifier: String
    submittedValue: String
    isCorrect: Boolean
    timeOfSubmission: Date
    maxScore: Int
}

input StartGameInput {
    gameId: ID
}

type MinifiedGame {
    _id: ID!
    players: [Player!]!
    config: GameConfig
    leaderBoard: [LeaderBoardEntry]
    startTime: Time
    endTime: Time
}

type GetGamesOutput {
    games: [MinifiedGame]
    users: [UserPublicDetails]
}

type GetGamesByRatingOutput {
    games: [MinifiedGame]
    puzzleGames: [MinifiedPuzzleGame]
    users: [UserPublicDetails]
    totalCount: Int
}

input TimeRangeInput {
    startTime: Time
    endTime: Time
}

input PageInfoInput {
    pageNumber: Int
    rows: Int
}

input GetGamesInput {
    userId: ID
    timeRange: TimeRangeInput
    pageInfo: PageInfoInput
}

input GetGamesByRatingInput {
    userId: ID
    pageInfo: PageInfoInput
    ratingType: String
}

type GameDetailedAnalysis {
    game: Game!
    questions: [GameQuestionAnalysis!]!
}

type GameQuestionAnalysis {
    question: Question!
    avgTimes: [UserAvgTime!]!
    globalAvgTime: Float!
    globalBestTime: Float!
}

type UserAvgTime {
    userId: String!
    questionAvgTime: Float!
    presetAvgTime: Float!
    presetBestTime: Float!
}

input ChallengeUserInput {
    userId: ID
    gameConfig: GameConfigInput
}

type ChallengeOutput {
    gameId: ID!
    challengedBy: ID!
    gameConfig: GameConfig!
    createdAt: Time!
    status: ChallengeStatus
    opponent: User
}

enum ChallengeStatus {
    CHALLENGE_SENT
    CHALLENGE_ACCEPTED
    CHALLENGE_EXPIRED
    CHALLENGE_REJECTED

    GAME_CANCELLED
}

extend type Query {
    getGameById(gameId: ID): Game @auth
    getGamesByUser(payload: GetGamesInput): GetGamesOutput @auth
    getUserGamesByRatingType(
        payload: GetGamesByRatingInput
    ): GetGamesByRatingOutput @auth
    getGameDetailedAnalysis(gameId: ID): GameDetailedAnalysis @auth
}

extend type Mutation {
    createGame(gameConfig: GameConfigInput): Game @auth
    submitAnswer(answerInput: SubmitAnswerInput): Game @auth
    submitFlashAnzanAnswer(answerInput: SubmitFlashAnzanAnswerInput): Game @auth
    joinGame(joinGameInput: JoinGameInput): Game @auth
    removePlayer(gameId: ID!, playerId: ID!): Boolean! @auth
    leaveGame(gameId: ID!): Game @auth
    startGame(startGameInput: StartGameInput): Game @auth
    endGame(gameId: ID): Game

    endAbilityDuelsGame(gameId: ID): Game @auth

    endGameForShowdown(gameId: ID): Game @auth

    startSearching(gameConfig: GameConfigInput): Boolean @auth
    abortSearching: Boolean @auth
    cancelGame(gameId: ID!): Boolean @auth

    requestRematch(gameId: ID!): Boolean! @auth
    acceptRematch(gameId: ID!): Game! @auth
    rejectRematch(gameId: ID!): Boolean! @auth
    cancelRematchRequest(gameId: ID!): Boolean! @auth

    challengeUser(challengeUserInput: ChallengeUserInput): Game @auth
    acceptChallenge(gameId: ID!): Game @auth
    rejectChallenge(gameId: ID!): Boolean @auth
}

type SubscriptionOutput {
    game: Game
    event: String
    question: Question
}

type SearchSubscriptionOutput {
    game: Game
    event: String
    opponent: User
}

extend type Subscription {
    searchPlayer(userId: ID): SearchSubscriptionOutput
    gameEvent(gameId: ID): SubscriptionOutput
    rematchRequest(gameId: ID!): RematchRequestOutput!
}
