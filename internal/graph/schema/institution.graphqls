type Institution {
    id: ID!
    name: String!
    domains: [String!]!
    country: String
    state: String
    city: String
    slug: String!
}

input CreateInstitutionInput {
    name: String!
    domains: [String!]
    country: String
    state: String
    city: String
}

extend type Query {
    searchInstitutions(query: String!, limit: Int = 10): [Institution!]!
}

extend type Mutation {
    createInstitution(input: CreateInstitutionInput!): Institution!
}
