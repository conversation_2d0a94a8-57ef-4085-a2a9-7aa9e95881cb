type GlobalPreset {
    _id: ID!
    identifier: String!
    globalAverageTime: Float
    bestTime: Float
    totalQuestionsSolved: Int
    bestStreak: Int
    numOfCorrectSubmissions: Int
    globalAccuracy: Float
    incorrectSubmissions: Int
    top10Mathletes: [<PERSON><PERSON><PERSON>]
}

type UserPreset {
    _id: ID!
    globalPresetId: ID!
    userId: ID!
    identifier: String!
    name: String
    questionsSolved: Int
    curAvgTime: Float
    curAvgAccuracy: Float
    incorrectSubmissions: Int
    bestTime: Float
    last10Time: [Int]
    last10IncorrectAttempts: [Int]
    bestStreak: Int
    numOfCorrectSubmissions: Int
    saved: Boolean
    savedConfig: String
}

type UserPresetStats {
    _id: ID!
    date: Time
    userPresetId: ID!
    globalPresetId: ID!
    userId: ID!
    identifier: String!
    avgTime: Float
    avgAccuracy: Float
    questionsSolved: Int
    numOfCorrectSubmissions: Int
    incorrectSubmissions: Int
    bestStreak: Int
    timePerformanceTrend: [Int]
    inaccuracyPerformanceTrend: [Int]
}

type Mathlete {
    userId: ID!
    questionsSolved: Int
    bestTime: Float
    bestStreak: Int
    numOfCorrectSubmissions: Int
}

type GlobalPresets {
    globalPresets: [GlobalPreset]
    totalCount: Int!
}

type UserPresets {
    userPresets: [UserPreset]
    totalCount: Int!
}

type UserPresetDayStats {
    date: Time
    userPresetStats: UserPresetStats
}

enum PresetCategory {
    ADD
    ADDSUB
    MULT
    DIV
}

input UserPresetResultInput {
    userPresetResults: [UserPresetResult]
}

input UserPresetResult {
    identifier: String
    numOfQuestions: Int
    submittedTimes: [Int]
    incorrectAttempts: [Int]
    bestStreak: Int
    numOfCorrectSubmissions: Int
    date: Time
    savedConfig: String
}

type PlayedPresets {
    identifier: String
    avgTime: Float
}

type AllPlayedPresetsOutput {
    presets: [PlayedPresets]
    count: Int
}

type UserPresetStatsGraph {
    userStats: [UserPresetDayStats]
    globalStats: [UserPresetDayStats]
}

extend type Mutation {
    submitUserPresetResult(userPresetResultInput: UserPresetResultInput): Boolean
    @auth
    saveUserPreset(identifier: String, name: String): UserPreset @auth
    deleteUserSavedPreset(presetId: ID!): Boolean @auth
}

extend type Query {
    getGlobalPresets(page: Int, pageSize: Int): GlobalPresets @auth
    getGlobalPresetsByIdentifier(identifier: String): GlobalPreset @auth
    getUserPresetsByIdentifier(identifier: String): UserPreset @auth
    getUserPresetStatsByDate(
        username: String
        durationFilter: Int
        identifier: String
    ): [UserPresetDayStats] @auth
    # TODO: Implement this
    # getGlobalPresetsStatsByDate(durationFilet: Int, identifier: String): [GlobalPresetDayStats] @auth
    getUserRecentPresets: UserPresets @auth
    getUserSavedPresets(page: Int = 1, pageSize: Int = 10): UserPresets @auth
    getUsersAllPlayedPresets(username: String): AllPlayedPresetsOutput @auth
}
