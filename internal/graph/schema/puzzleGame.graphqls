type PuzzleGame {
    _id: ID!
    players: [Player!]!
    gameStatus: PUZZLE_GAME_STATUS!
    rematchRequestedBy: ID
    gameType: PUZZLE_GAME_TYPE!
    createdBy: ID!
    config: PuzzleGameConfig
    questions: [PuzzleGameQuestion]
    leaderBoard: [PuzzleLeaderboardEntry]
    isRatedGame: Boolean
    startTime: Time
    endTime: Time
    seriesId: ID
}

type PuzzleGameQuestion {
    _id: ID!
    question: String
    submissions: [PuzzleQuestionSubmission]
    stats: PuzzleGameQuestionStats
}

type PuzzleGameQuestionStats {
    fastestTime: Int
    userIds: [ID]
}

type PuzzleQuestionSubmission {
    userId: ID
    timeTaken: Int
    points: Int
    submissionTime: Date
    isCorrect: Boolean
    inCorrectAttempts: Int
    submittedValues: [String]
}

type PuzzleLeaderboardEntry {
    userId: ID
    correct: Int
    incorrect: Int
    totalPoints: Float
    ratingChange: Int
    statikCoinsEarned: Int
    rank: Int
}

type PuzzleGameConfig {
    timeLimit: Int #"timeLimit in seconds"
    numPlayers: Int
    numOfQuestions: Int
    gameType: PUZZLE_GAME_TYPE
    difficultyLevel: [Int!]
    maxTimePerQuestion: Int
}

input PuzzleGameConfigInput {
    timeLimit: Int
    numPlayers: Int
    numOfQuestions: Int
    gameType: PUZZLE_GAME_TYPE
    difficultyLevel: [Int!]
    maxTimePerQuestion: Int
}

enum PUZZLE_GAME_STATUS {
    CREATED
    READY
    STARTED
    PAUSED
    ENDED
    CANCELLED
}

enum PUZZLE_GAME_TYPE {
    CROSS_MATH_PUZZLE_DUEL
    CROSS_MATH_PUZZLE_WITH_FRIEND
}

input SubmitPuzzleGameAnswerInput {
    gameId: ID
    questionId: String
    isCorrect: Boolean
    inCorrectAttempts: Int
    timeOfSubmission: Date
}

type MinifiedPuzzleGame {
    _id: ID!
    players: [Player!]!
    config: PuzzleGameConfig
    leaderBoard: [PuzzleLeaderboardEntry]
    startTime: Time
    endTime: Time
}

type GetPuzzleGamesOutput {
    games: [MinifiedPuzzleGame]
    users: [UserPublicDetails]
}

type CrossMathPuzzleRush {
    _id: ID!
    userId: ID!
    bestAllTime: Int
    isNewBestScore: Boolean
    createdAt: Time
    updatedAt: Time
}

input SubmitPuzzleRushGame {
    score: Int
    timeSpent: Int
}

type CrossMathPuzzleRushPlayerInfo {
    rank: Int
    score: Int
    userInfo: UserPublicDetails
}

type CrossMathPuzzleRushStats {
    bestAllTime: Int
    globalRank: Int
    friendsRank: Int
}

input GetPuzzleGamesInput {
    userId: ID
    timeRange: TimeRangeInput
    pageInfo: PageInfoInput
}

input ChallengeUserForPuzzleGameInput {
    userId: ID
    gameConfig: PuzzleGameConfigInput
}

extend type Query {
    getPuzzleGameById(gameId: ID!): PuzzleGame @auth
    getPuzzleGamesByUser(payload: GetPuzzleGamesInput): GetPuzzleGamesOutput @auth
    getMyCrossMathPuzzleRushStats: CrossMathPuzzleRushStats @auth
    getGlobalTop5CrossMathPuzzleRushStats: [CrossMathPuzzleRushPlayerInfo!] @auth
    getFriendsTop5CrossMathPuzzleRushStats: [CrossMathPuzzleRushPlayerInfo!] @auth
}

extend type Mutation {
    createPuzzleGame(gameConfig: PuzzleGameConfigInput): PuzzleGame @auth
    submitPuzzleGameAnswer(answerInput: SubmitPuzzleGameAnswerInput): PuzzleGame
    @auth
    joinPuzzleGame(gameId: ID!): PuzzleGame @auth
    removePlayerFromPuzzleGame(gameId: ID!, playerId: ID!): Boolean! @auth
    leavePuzzleGame(gameId: ID!): PuzzleGame @auth
    startPuzzleGame(gameId: ID!): PuzzleGame @auth
    endPuzzleGame(gameId: ID!): PuzzleGame @auth
    startSearchingForPuzzleGame(gameConfig: PuzzleGameConfigInput): Boolean @auth
    abortSearchingForPuzzleGame: Boolean @auth
    cancelPuzzleGame(gameId: ID!): Boolean @auth

    requestRematchForPuzzleGame(gameId: ID!): Boolean! @auth
    acceptRematchOfPuzzleGame(gameId: ID!): PuzzleGame! @auth
    rejectRematchOfPuzzleGame(gameId: ID!): Boolean! @auth

    challengeUserForPuzzleGame(
        challengeUserInput: ChallengeUserForPuzzleGameInput
    ): PuzzleGame @auth
    acceptChallengeOfPuzzleGame(gameId: ID!): PuzzleGame @auth
    rejectChallengeOfPuzzleGame(gameId: ID!): Boolean @auth

    submitPuzzleGameRush(input: SubmitPuzzleRushGame!): CrossMathPuzzleRush @auth
}
