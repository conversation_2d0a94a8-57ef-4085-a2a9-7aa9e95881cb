type Question {
    id: String
    expression: [String]!
    description: String
    options: [String]
    answers: [String]
    questionType: QuestionType
    rating: Int
    maxTimeLimit: Int
    tags: [String]
    fastestTimeTaken: Int
    presetIdentifier: String
}

"""
TODO: derive the questionutils types dynamically
"""
enum QuestionType {
    SINGLE_CHOICE
    MULTI_CHOICE
    FILL_IN_THE_BLANKS
}
