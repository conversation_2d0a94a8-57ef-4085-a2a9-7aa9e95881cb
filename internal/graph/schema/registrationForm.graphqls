type RegistrationForm {
    _id: ID!
    fields: [FormField!]!
}

type Form<PERSON>ield {
    _id: ID!
    name: String!
    type: FieldType!
    label: String!
    required: Boolean!
    options: [String]
    validation: FieldValidation
}

enum FieldType {
    TEXT
    NUMBER
    EMAIL
    MOBILE
    SINGLE_SELECT
    MULTI_SELECT
    CHECKBOX
    RADIO
    IMAGE_INPUT
    FILE_INPUT
}

type FieldValidation {
    regex: String
    min: Int
    max: Int
    emailSuffix: String
    emailSuffixes: [String!]
    needManualVerification: Boolean
}

input RegistrationFormInput {
    fields: [FormFieldInput!]!
}

input FormFieldInput {
    name: String!
    type: FieldType!
    label: String!
    required: Boolean!
    options: [String]
    validation: FieldValidationInput
}

input FieldValidationInput {
    regex: String
    min: Int
    max: Int
    emailSuffix: String
    emailSuffixes: [String!]
    needManualVerification: Boolean
}
