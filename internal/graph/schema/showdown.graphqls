type Showdown {
    _id: ID
    clubId: ID
    name: String!
    description: String!
    Instructions: [String]
    hostedBy: HostInfo

    isRatedEvent: Boolean
    rounds: Int!
    startTime: Time!
    endTime: Time!
    registrationCount: Int
    registrationStartTime: Time
    registrationEndTime: Time
    registrationForm: RegistrationForm
    currentRound: Int!
    details: showdownDetails!

    duration: Int!
    roundTime: Int!
    gapBwRounds: Int!
    status: SHOWDOWN_CONTEST_STATUS!

    roundConfig: RoundConfig!
    stats: ShowdownStats
    currentUserParticipation: CurrentShowdonParticipant
    createdAt: Time!
    updatedAt: Time!
    recentParticipants: [UserPublicDetails!]
}

type showdownDetails {
    format: String
    rules: String
}

type ShowdownStats {
    totalParticipants: Int
    totalGamesPlayed: Int
}

type ShowdownParticipant {
    _id: ID
    userID: ID!
    showdownId: ID!
    status: SHOWDOWN_PARTICIPANT_STATUS!
    registrationData: [RegistrationFieldData]
    stats: ShowdownParticipantStats
    rounds: [ShowdownRound]!
    recentOpponents: [ID!]!
    rank: Int
    totalScore: Float!
    ratingChange: Int!
    # points: Int!
    hadABye: Boolean!
    createdAt: Time!
    updatedAt: Time!
    userInfo: ShowdownUserDetails
}

type ParticipantBasicInfo {
    _id: ID
    userID: ID!
    showdownId: ID!
    rounds: [ShowdownRound]!
    userInfo: ShowdownUserDetails
}

type ShowdownUserDetails {
    name: String
    username: String!
    profileImageUrl: String
    rating: Int
}

type ShowdownParticipantStats {
    currentScore: Int
    win: Int
    loss: Int
    draw: Int
}

type CurrentShowdonParticipant {
    showdownId: ID
    userId: ID
    registrationData: [RegistrationFieldData]
    lastSubmissionTime: Time
    stats: ShowdownParticipantStats!
    rank: Int
    rounds: [ShowdownRound]
    hadABye: Boolean!
    currentGame: ID
    recentOpponent: ID
    totalScore: Float!
    currentRound: ShowdownRound
}

type ShowdownGameConfig {
    isRoundEnded: Boolean!
    hasOpponentNotShown: Boolean!
    nextGameId: ID
    round: Int!
    nextGameStartsAt: Time
    totalGamesPlayed: Int!
    showdownGamePlayer: [ShowdownGamePlayer]!
    numOfGames: Int!
}

type ShowdownGamePlayer {
    isTie: Boolean!
    isWinner: Boolean!
    userId: ID!
    wins: Float!
    score: Float!
}

type HostInfo {
    id: ID
    hostType: HOST_TYPE
    hostLogo: String
}

type RoundConfig {
    gameDuration: Int!
    numOfPlayer: Int!
    gameType: GAME_TYPE!
    maxGapBwGame: Int!
    maxWaitTime: Int!
    numOfGames: Int!
}

type Fictures {
    id: ID!
    showdownId: ID!
    users: [ShowdownParticipantDetail]!
    round: Int!
    participants: [ID!]!
}

type ShowdownParticipantDetail {
    showdownParticipant: ShowdownParticipant
    currentRound: ShowdownRound!
}

type FicturesCollection {
    currentUserFicture: Fictures
}

type ShowdownRound {
    opponent: ID!
    round: Int!
    score: Float!
    games: [ID!]!
    wins: Float!
    loose: Float!
    totalGamesPlayed: Int!
    playerStatus: RoundPlayerStatus!
    isBye: Boolean!
    isRoundEnded: Boolean!
    hasJoined: Boolean!
    hasOpponentNotShown: Boolean!
    hasFailedToPlay: Boolean!
}

enum RoundPlayerStatus {
    OPPONENT_ABSENT
    BYE
    DID_NOT_PLAY
    BOTH_DID_NOT_PLAY
    PENDING_JOIN
    ROUND_COMPLETED
}

type LeaderParticipantEntity {
    _id: ID!
    participant: ParticipantBasicInfo!
    score: Float!
    rank: Int!
    showdownId: ID!
    userId: ID!
}

type PaginatedLeaderboard {
    participants: [LeaderParticipantEntity]!
    count: Int!
}

enum SHOWDOWN_CONTEST_STATUS {
    UPCOMING
    REGISTRATION_OPEN
    REGISTRATION_ENDED
    FICTURES_CREATED
    LIVE
    FICTURES_PENDING
    ENDED
    ARCHIVED
}

enum SHOWDOWN_PARTICIPANT_STATUS {
    ACTIVE
    DISQUALIFIED
}

enum HOST_TYPE {
    MATIKS
    ORGANIZATION
    USER
}

input RoundConfigInput {
    gameDuration: Int!
    numOfPlayer: Int!
    gameType: GAME_TYPE!
    maxGapBwGame: Int!
    maxWaitTime: Int!
    numOfGames: Int!
}

input HostInfoInput {
    id: ID
    hostType: HOST_TYPE
    hostLogo: String
}

input ShowdownDetailsInput {
    format: String
    rules: String
}

input CreateShowdownInput {
    name: String!
    description: String!
    hostedBy: HostInfoInput!
    isRatedEvent: Boolean
    startTime: Time!
    details: ShowdownDetailsInput!
    rounds: Int!
    gapBwRounds: Int!
    registrationStartTime: Time!
    registrationEndTime: Time!
    roundConfig: RoundConfigInput!
    registrationForm: RegistrationFormInput!
}

input ShowdownRegistrationFormValuesInput {
    showdownId: ID!
    # formData: [RegistrationFormFieldValueInput]!
}

input StartGameForShowdownInput {
    showdownId: ID!
    userId: ID!
    gameId: ID!
}

input PaginatedLeaderboardInput {
    showdownId: ID!
    page: Int!
}

type PaginatedShowdowns {
    showdowns: [Showdown!]!
    count: Int!
}

extend type Query {
    getShowdownById(showdownId: ID!): Showdown! @auth
    getUpcomingShowdown: Showdown! @auth
    getShowdownByStatus(status: SHOWDOWN_CONTEST_STATUS): Showdown! @auth
    getFicturesByShowdownId(showdownId: ID!): FicturesCollection! @auth
    getPaginatedLeaderboard(
        input: PaginatedLeaderboardInput
    ): PaginatedLeaderboard! @auth
    getFeaturedShowdown: [Showdown] @auth
    getShowdownsByStatus(
        statuses: [SHOWDOWN_CONTEST_STATUS!]!
        page: Int = 1
        pageSize: Int = 20
        sortDirection: String
    ): PaginatedShowdowns! @auth
}

extend type Mutation {
    createShowdown(input: CreateShowdownInput!): Showdown! @auth
    registerForShowdown(input: ShowdownRegistrationFormValuesInput!): Boolean!
    @auth
    unregisterFromShowdown(showdownId: ID!): Boolean! @auth
    startGameForShowdown(input: StartGameForShowdownInput): Game @auth
}
