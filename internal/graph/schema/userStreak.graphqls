type UserStreaks {
    currentStreak: Int
    longestStreak: Int
    lastPlayedDate: Time
    lastSevenDays: [Boolean]
    streakHistory: [Time]
    streakFreezers: Int
}

type StreakHistory {
    _id: ID!
    streakHistoryObj: [StreakEntry]!
}

type StreakDay {
    date: Time!
    activity: Boolean!
    isShieldUsed: Boolean!
}

type StreakEntry {
    date: Time!
    isShieldUsed: Boolean!
}

type StreakStatusResponse {
    hasStreak: Boolean!
    streakFreezers: Int!
    missedDays: Int!
    canSaveStreak: Boolean!
    lostStreakCount: Int!
    lastSevenDays: [StreakDay!]
}

extend type Query {
    # TODO: should expect date
    getUserStreakHistoryByMonth(yearMonths: [String!]!): [StreakEntry] @auth
    checkUserStreakStatus: StreakStatusResponse! @auth
}

extend type Mutation {
    useStreakFreezer: Boolean! @auth
    getUpdatedUserStreaks: UserStreaks @auth
}
