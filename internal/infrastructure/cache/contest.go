package cache

import (
	"context"
	"time"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/fx"

	"matiksOfficial/matiks-server-go/internal/models"
)

const (
	contestLeaderboardKey string = "contestLeaderboard:"
	contestKey            string = "contest:"
)

type ContestCache interface {
	GetContest(ctx context.Context, contestID primitive.ObjectID) (*models.Contest, error)
	SetContest(ctx context.Context, contest *models.Contest) error
	DeleteContest(ctx context.Context, contestID primitive.ObjectID) error
	DeleteAllContests(ctx context.Context) error

	// GetContestLeaderboard(ctx context.Context, contestID primitive.ObjectID) (*models.ContestLeaderboard, error)
	// SetContestLeaderboard(ctx context.Context, contestLeaderboard *models.ContestLeaderboard) error
	// DeleteContestLeaderboard(ctx context.Context, contestID primitive.ObjectID) error
	// DeleteAllContestLeaderboards(ctx context.Context) error
}

type ContestCacheWrapper struct {
	cache Cache
}

func NewContestCacheWrapper(lc fx.Lifecycle, cache Cache) ContestCache {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Contest cache initialized and ready.")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down contest cache.")
			return nil
		},
	})
	return &ContestCacheWrapper{cache: cache}
}

func (c *ContestCacheWrapper) GetContest(ctx context.Context, contestID primitive.ObjectID) (*models.Contest, error) {
	data, err := c.cache.Get(ctx, contestKey+contestID.Hex())
	if err != nil {
		return nil, err
	}
	contest := &models.Contest{}
	if err := bson.Unmarshal(data, contest); err != nil {
		return nil, err
	}
	return contest, nil
}

func (c *ContestCacheWrapper) SetContest(ctx context.Context, contest *models.Contest) error {
	data, err := bson.Marshal(contest)
	if err != nil {
		return err
	}
	return c.cache.Set(ctx, contestKey+contest.ID.Hex(), data, 24*time.Hour)
}

func (c *ContestCacheWrapper) DeleteContest(ctx context.Context, contestID primitive.ObjectID) error {
	return c.cache.Delete(ctx, contestKey+contestID.Hex())
}

func (c *ContestCacheWrapper) DeleteAllContests(ctx context.Context) error {
	return c.cache.DeleteAll(ctx, contestKey+"*")
}
