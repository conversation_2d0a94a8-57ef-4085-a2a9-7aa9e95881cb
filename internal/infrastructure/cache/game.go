package cache

import (
	"context"
	"encoding/json"
	"time"

	"matiksOfficial/matiks-server-go/internal/models"
)

const (
	gameCacheKey   = "game:"
	searchCacheKey = "search:"
)

type GameCache interface {
	GetGame(ctx context.Context, gameID string) (*models.Game, error)
	GetAllGames(ctx context.Context) ([]*models.Game, error)
	SetGame(ctx context.Context, game *models.Game) error
	DeleteGame(ctx context.Context, gameID string) error

	GetSearchEntry(ctx context.Context, userID string) (*models.SearchEntry, error)
	GetAllSearchEntries(ctx context.Context) ([]*models.SearchEntry, error)
	SetSearchEntry(ctx context.Context, searchEntry *models.SearchEntry) error
	DeleteSearchEntry(ctx context.Context, userID string) error
}

type GameCacheWrapper struct {
	cache Cache
}

func NewGameCacheWrapper(cache Cache) GameCache {
	return &GameCacheWrapper{cache: cache}
}

func (w *GameCacheWrapper) GetGame(ctx context.Context, gameID string) (*models.Game, error) {
	data, err := w.cache.Get(ctx, gameCacheKey+gameID)
	if err != nil {
		return nil, err
	}

	var game models.Game
	err = json.Unmarshal(data, &game)
	if err != nil {
		return nil, err
	}

	return &game, nil
}

func (w *GameCacheWrapper) GetAllGames(ctx context.Context) ([]*models.Game, error) {
	data, err := w.cache.GetAll(ctx, gameCacheKey+"*")
	if err != nil {
		return nil, err
	}

	games := make([]*models.Game, 0, len(data))
	for _, d := range data {
		var game models.Game
		err = json.Unmarshal(d, &game)
		if err != nil {
			return nil, err
		}

		games = append(games, &game)
	}

	return games, nil
}

func (w *GameCacheWrapper) SetGame(ctx context.Context, game *models.Game) error {
	data, err := json.Marshal(game)
	if err != nil {
		return err
	}

	return w.cache.Set(ctx, gameCacheKey+game.ID.Hex(), data, 24*time.Hour)
}

// TODO: check if the 2 min delay is needed
func (w *GameCacheWrapper) DeleteGame(ctx context.Context, gameID string) error {
	time.Sleep(2 * time.Minute)
	return w.cache.Delete(ctx, gameCacheKey+gameID)
}

func (w *GameCacheWrapper) SetSearchEntry(ctx context.Context, searchEntry *models.SearchEntry) error {
	data, err := json.Marshal(searchEntry)
	if err != nil {
		return err
	}

	return w.cache.Set(ctx, searchCacheKey+searchEntry.UserID.Hex(), data, 5*time.Minute)
}

func (w *GameCacheWrapper) GetSearchEntry(ctx context.Context, userID string) (*models.SearchEntry, error) {
	data, err := w.cache.Get(ctx, searchCacheKey+userID)
	if err != nil {
		return nil, err
	}

	var entry models.SearchEntry
	err = json.Unmarshal(data, &entry)
	if err != nil {
		return nil, err
	}

	return &entry, nil
}

func (w *GameCacheWrapper) GetAllSearchEntries(ctx context.Context) ([]*models.SearchEntry, error) {
	data, err := w.cache.GetAll(ctx, searchCacheKey+"*")
	if err != nil {
		return nil, err
	}

	entries := make([]*models.SearchEntry, 0, len(data))
	for _, value := range data {
		var entry models.SearchEntry
		err = json.Unmarshal(value, &entry)
		if err != nil {
			continue
		}
		entries = append(entries, &entry)
	}

	return entries, nil
}

func (w *GameCacheWrapper) DeleteSearchEntry(ctx context.Context, userID string) error {
	return w.cache.Delete(ctx, searchCacheKey+userID)
}
