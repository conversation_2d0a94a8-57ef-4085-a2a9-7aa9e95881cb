package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
)

type LocalCache struct {
	data  map[string][]byte
	mutex sync.RWMutex
}

func NewLocalCache() Cache {
	return &LocalCache{
		data: make(map[string][]byte),
	}
}

func (c *LocalCache) Get(ctx context.Context, key string) ([]byte, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	value, ok := c.data[key]
	if !ok {
		return nil, fmt.Errorf("key not found")
	}
	return value, nil
}

func (c *LocalCache) Set(ctx context.Context, key string, value []byte, expiration time.Duration) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.data[key] = value
	return nil
}

func (c *LocalCache) Delete(ctx context.Context, key string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	delete(c.data, key)
	return nil
}

func (c *LocalCache) GetAll(ctx context.Context, pattern string) (map[string][]byte, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	result := make(map[string][]byte)
	for key, value := range c.data {
		if matchPattern(key, pattern) {
			result[key] = value
		}
	}
	return result, nil
}

func (c *LocalCache) ClearAll(ctx context.Context) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.data = make(map[string][]byte)
	return nil
}

func matchPattern(key, pattern string) bool {
	return len(key) >= len(pattern) && key[:len(pattern)] == pattern
}

func (c *LocalCache) DeleteAll(ctx context.Context, pattern string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	for key := range c.data {
		if matchPattern(key, pattern) {
			delete(c.data, key)
		}
	}
	return nil
}

func (c *LocalCache) HSet(ctx context.Context, key, field string, value interface{}) error {
	return c.HSet(ctx, key, field, value)
}

func (c *LocalCache) Expire(ctx context.Context, key string, expiration time.Duration) error {
	return c.Expire(ctx, key, expiration)
}

func (c *LocalCache) HGetAll(ctx context.Context, key string) (map[string]string, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if data, ok := c.data[key]; ok {
		result := make(map[string]string)
		err := json.Unmarshal(data, &result)
		if err != nil {
			return nil, err
		}
		return result, nil
	}
	return nil, nil
}

func (c *LocalCache) ZAdd(ctx context.Context, key string, members ...redis.Z) error {
	return c.ZAdd(ctx, key, members...)
}

func (c *LocalCache) ZRemRangeByScore(ctx context.Context, key, min, max string) error {
	return c.ZRemRangeByScore(ctx, key, min, max)
}

func (c *LocalCache) ZCount(ctx context.Context, key, min, max string) (int64, error) {
	return c.ZCount(ctx, key, min, max)
}

func (c *LocalCache) ZRevRangeWithScores(ctx context.Context, key string, start, stop int64) ([]redis.Z, error) {
	return c.ZRevRangeWithScores(ctx, key, start, stop)
}

func (c *LocalCache) ZScore(ctx context.Context, key, member string) (float64, error) {
	return c.ZScore(ctx, key, member)
}

func (c *LocalCache) ZRangeByScore(ctx context.Context, key string, opt *redis.ZRangeBy) ([]string, error) {
	return c.ZRangeByScore(ctx, key, opt)
}

func (c *LocalCache) ZRem(ctx context.Context, key, entry string) error {
	return c.ZRem(ctx, key, entry)
}

func (c *LocalCache) Incr(ctx context.Context, key string) (int64, error) {
	return c.Incr(ctx, key)
}

func (c *LocalCache) Decr(ctx context.Context, key string) (int64, error) {
	return c.Decr(ctx, key)
}

func (c *LocalCache) DeleteMany(ctx context.Context, keys ...string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	for _, key := range keys {
		delete(c.data, key)
	}
	return nil
}

func (c *LocalCache) MGet(ctx context.Context, keys ...string) ([]interface{}, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	result := make([]interface{}, len(keys))
	for i, key := range keys {
		if data, ok := c.data[key]; ok {
			result[i] = data
		}
	}
	return result, nil
}
