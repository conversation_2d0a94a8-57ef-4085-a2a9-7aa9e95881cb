package cache

import (
	"context"
	"encoding/json"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"matiksOfficial/matiks-server-go/internal/models"
)

const (
	globalPresetCacheKey    = "globalPreset:"
	userPresetCacheKey      = "userPreset:"
	userPresetStatsCacheKey = "userPresetStats:"
)

type PresetsCache interface {
	GetGlobalPreset(ctx context.Context, identifier string, userID primitive.ObjectID) (*models.GlobalPreset, error)
	GetAllGlobalPresets(ctx context.Context, userID primitive.ObjectID) ([]*models.GlobalPreset, error)
	SetGlobalPreset(ctx context.Context, preset *models.GlobalPreset, userID primitive.ObjectID) error
	DeleteGlobalPreset(ctx context.Context, identifier string, userID primitive.ObjectID) error
	DeleteAllGlobalPresets(ctx context.Context, userID primitive.ObjectID) error

	GetUserPreset(ctx context.Context, identifier string, userID primitive.ObjectID) (*models.UserPreset, error)
	GetAllUserPresets(ctx context.Context, userID primitive.ObjectID) ([]*models.UserPreset, error)
	SetUserPreset(ctx context.Context, preset *models.UserPreset, userID primitive.ObjectID) error
	DeleteUserPreset(ctx context.Context, identifier string, userID primitive.ObjectID) error
	DeleteAllUserPresets(ctx context.Context, userID primitive.ObjectID) error

	GetUserPresetStats(ctx context.Context, identifier string, userID primitive.ObjectID) (*models.UserPresetStats, error)
	GetAllUserPresetStats(ctx context.Context, userID primitive.ObjectID) ([]*models.UserPresetStats, error)
	SetUserPresetStats(ctx context.Context, stats *models.UserPresetStats, userID primitive.ObjectID) error
	DeleteUserPresetStats(ctx context.Context, identifier string, userID primitive.ObjectID) error
	DeleteAllUserPresetStats(ctx context.Context, userID primitive.ObjectID) error
}

type PresetsCacheWrapper struct {
	cache Cache
}

func NewPresetsCacheWrapper(cache Cache) PresetsCache {
	return &PresetsCacheWrapper{cache: cache}
}

func (c *PresetsCacheWrapper) GetGlobalPreset(ctx context.Context, identifier string, userID primitive.ObjectID) (*models.GlobalPreset, error) {
	data, err := c.cache.Get(ctx, globalPresetCacheKey+userID.Hex()+"_"+identifier)
	if err != nil {
		return nil, err
	}

	var preset models.GlobalPreset
	err = json.Unmarshal(data, &preset)
	if err != nil {
		return nil, err
	}

	return &preset, nil
}

func (c *PresetsCacheWrapper) GetAllGlobalPresets(ctx context.Context, userID primitive.ObjectID) ([]*models.GlobalPreset, error) {
	data, err := c.cache.GetAll(ctx, globalPresetCacheKey+userID.Hex()+"_"+"*")
	if err != nil {
		return nil, err
	}

	presets := make([]*models.GlobalPreset, 0, len(data))
	for _, d := range data {
		var preset models.GlobalPreset
		err = json.Unmarshal(d, &preset)
		if err != nil {
			return nil, err
		}

		presets = append(presets, &preset)
	}

	return presets, nil
}

func (c *PresetsCacheWrapper) SetGlobalPreset(ctx context.Context, preset *models.GlobalPreset, userID primitive.ObjectID) error {
	data, err := json.Marshal(preset)
	if err != nil {
		return err
	}

	return c.cache.Set(ctx, globalPresetCacheKey+userID.Hex()+"_"+preset.Identifier, data, 24*time.Hour)
}

func (c *PresetsCacheWrapper) DeleteGlobalPreset(ctx context.Context, identifier string, userID primitive.ObjectID) error {
	return c.cache.Delete(ctx, globalPresetCacheKey+userID.Hex()+"_"+identifier)
}

func (c *PresetsCacheWrapper) DeleteAllGlobalPresets(ctx context.Context, userID primitive.ObjectID) error {
	return c.cache.DeleteAll(ctx, globalPresetCacheKey+userID.Hex()+"_"+"*")
}

func (c *PresetsCacheWrapper) GetUserPreset(ctx context.Context, identifier string, userID primitive.ObjectID) (*models.UserPreset, error) {
	data, err := c.cache.Get(ctx, userPresetCacheKey+userID.Hex()+"_"+identifier)
	if err != nil {
		return nil, err
	}

	var preset models.UserPreset
	err = json.Unmarshal(data, &preset)
	if err != nil {
		return nil, err
	}

	return &preset, nil
}

func (c *PresetsCacheWrapper) GetAllUserPresets(ctx context.Context, userID primitive.ObjectID) ([]*models.UserPreset, error) {
	data, err := c.cache.GetAll(ctx, userPresetCacheKey+userID.Hex()+"_"+"*")
	if err != nil {
		return nil, err
	}

	presets := make([]*models.UserPreset, 0, len(data))
	for _, d := range data {
		var preset models.UserPreset
		err = json.Unmarshal(d, &preset)
		if err != nil {
			return nil, err
		}

		presets = append(presets, &preset)
	}

	return presets, nil
}

func (c *PresetsCacheWrapper) SetUserPreset(ctx context.Context, preset *models.UserPreset, userID primitive.ObjectID) error {
	data, err := json.Marshal(preset)
	if err != nil {
		return err
	}

	return c.cache.Set(ctx, userPresetCacheKey+userID.Hex()+"_"+preset.Identifier, data, 24*time.Hour)
}

func (c *PresetsCacheWrapper) DeleteUserPreset(ctx context.Context, identifier string, userID primitive.ObjectID) error {
	return c.cache.Delete(ctx, userPresetCacheKey+userID.Hex()+"_"+identifier)
}

func (c *PresetsCacheWrapper) DeleteAllUserPresets(ctx context.Context, userID primitive.ObjectID) error {
	return c.cache.DeleteAll(ctx, userPresetCacheKey+userID.Hex()+"_"+"*")
}

func (c *PresetsCacheWrapper) GetUserPresetStats(ctx context.Context, identifier string, userID primitive.ObjectID) (*models.UserPresetStats, error) {
	data, err := c.cache.Get(ctx, userPresetStatsCacheKey+userID.Hex()+"_"+identifier)
	if err != nil {
		return nil, err
	}

	var preset models.UserPresetStats
	err = json.Unmarshal(data, &preset)
	if err != nil {
		return nil, err
	}

	return &preset, nil
}

func (c *PresetsCacheWrapper) GetAllUserPresetStats(ctx context.Context, userID primitive.ObjectID) ([]*models.UserPresetStats, error) {
	data, err := c.cache.GetAll(ctx, userPresetStatsCacheKey+userID.Hex()+"_"+"*")
	if err != nil {
		return nil, err
	}

	stats := make([]*models.UserPresetStats, 0, len(data))
	for _, d := range data {
		var stat models.UserPresetStats
		err = json.Unmarshal(d, &stat)
		if err != nil {
			return nil, err
		}
		stats = append(stats, &stat)
	}
	return stats, nil
}

func (c *PresetsCacheWrapper) SetUserPresetStats(ctx context.Context, preset *models.UserPresetStats, userID primitive.ObjectID) error {
	data, err := json.Marshal(preset)
	if err != nil {
		return err
	}

	return c.cache.Set(ctx, userPresetStatsCacheKey+userID.Hex()+"_"+preset.Identifier, data, 24*time.Hour)
}

func (c *PresetsCacheWrapper) DeleteUserPresetStats(ctx context.Context, identifier string, userID primitive.ObjectID) error {
	return c.cache.Delete(ctx, userPresetStatsCacheKey+userID.Hex()+"_"+identifier)
}

func (c *PresetsCacheWrapper) DeleteAllUserPresetStats(ctx context.Context, userID primitive.ObjectID) error {
	return c.cache.DeleteAll(ctx, userPresetStatsCacheKey+userID.Hex()+"_"+"*")
}
