package cache

import (
	"context"
	"encoding/json"
	"time"

	"matiksOfficial/matiks-server-go/internal/models"
)

const (
	puzzleGameCacheKey       = "puzzleGame:"
	searchPuzzleGameCacheKey = "searchPuzzleGame:"
)

type PuzzleGameCache interface {
	GetPuzzleGame(ctx context.Context, gameID string) (*models.PuzzleGame, error)
	GetAllPuzzleGames(ctx context.Context) ([]*models.PuzzleGame, error)
	SetPuzzleGame(ctx context.Context, game *models.PuzzleGame) error
	DeletePuzzleGame(ctx context.Context, gameID string) error

	GetPuzzleGameSearchEntry(ctx context.Context, userID string) (*models.SearchEntry, error)
	GetAllPuzzleGameSearchEntries(ctx context.Context) ([]*models.SearchEntry, error)
	SetPuzzleGameSearchEntry(ctx context.Context, searchEntry *models.SearchEntry) error
	DeletePuzzleGameSearchEntry(ctx context.Context, userID string) error
}

type PuzzleGameCacheWrapper struct {
	cache Cache
}

func NewPuzzleGameCacheWrapper(cache Cache) PuzzleGameCache {
	return &PuzzleGameCacheWrapper{cache: cache}
}

func (w *PuzzleGameCacheWrapper) GetPuzzleGame(ctx context.Context, gameID string) (*models.PuzzleGame, error) {
	data, err := w.cache.Get(ctx, puzzleGameCacheKey+gameID)
	if err != nil {
		return nil, err
	}

	var game models.PuzzleGame
	err = json.Unmarshal(data, &game)
	if err != nil {
		return nil, err
	}

	return &game, nil
}

func (w *PuzzleGameCacheWrapper) GetAllPuzzleGames(ctx context.Context) ([]*models.PuzzleGame, error) {
	data, err := w.cache.GetAll(ctx, puzzleGameCacheKey+"*")
	if err != nil {
		return nil, err
	}

	games := make([]*models.PuzzleGame, 0, len(data))
	for _, d := range data {
		var game models.PuzzleGame
		err = json.Unmarshal(d, &game)
		if err != nil {
			return nil, err
		}

		games = append(games, &game)
	}

	return games, nil
}

func (w *PuzzleGameCacheWrapper) SetPuzzleGame(ctx context.Context, game *models.PuzzleGame) error {
	data, err := json.Marshal(game)
	if err != nil {
		return err
	}

	return w.cache.Set(ctx, puzzleGameCacheKey+game.ID.Hex(), data, 24*time.Hour)
}

func (w *PuzzleGameCacheWrapper) DeletePuzzleGame(ctx context.Context, gameID string) error {
	time.Sleep(2 * time.Minute)
	return w.cache.Delete(ctx, puzzleGameCacheKey+gameID)
}

func (w *PuzzleGameCacheWrapper) SetPuzzleGameSearchEntry(ctx context.Context, searchEntry *models.SearchEntry) error {
	data, err := json.Marshal(searchEntry)
	if err != nil {
		return err
	}

	return w.cache.Set(ctx, searchPuzzleGameCacheKey+searchEntry.UserID.Hex(), data, 5*time.Minute)
}

func (w *PuzzleGameCacheWrapper) GetPuzzleGameSearchEntry(ctx context.Context, userID string) (*models.SearchEntry, error) {
	data, err := w.cache.Get(ctx, searchPuzzleGameCacheKey+userID)
	if err != nil {
		return nil, err
	}

	var entry models.SearchEntry
	err = json.Unmarshal(data, &entry)
	if err != nil {
		return nil, err
	}

	return &entry, nil
}

func (w *PuzzleGameCacheWrapper) GetAllPuzzleGameSearchEntries(ctx context.Context) ([]*models.SearchEntry, error) {
	data, err := w.cache.GetAll(ctx, searchPuzzleGameCacheKey+"*")
	if err != nil {
		return nil, err
	}

	entries := make([]*models.SearchEntry, 0, len(data))
	for _, value := range data {
		var entry models.SearchEntry
		err = json.Unmarshal(value, &entry)
		if err != nil {
			continue
		}
		entries = append(entries, &entry)
	}

	return entries, nil
}

func (w *PuzzleGameCacheWrapper) DeletePuzzleGameSearchEntry(ctx context.Context, userID string) error {
	return w.cache.Delete(ctx, searchPuzzleGameCacheKey+userID)
}
