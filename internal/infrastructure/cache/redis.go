package cache

import (
	"context"
	"time"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"github.com/redis/go-redis/v9"
	"go.uber.org/fx"
)

type RedisCache struct {
	client *redis.Client
}

func NewRedisCache(lc fx.Lifecycle, client *redis.Client) Cache {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Redis cache initialized and ready.")
			if err = client.Ping(ctx).Err(); err != nil {
				zlog.Error(ctx, "Failed to connect to Redis: ", err)
				return err
			}
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down Redis cache.")
			return nil
		},
	})
	return &RedisCache{client: client}
}

func (c *RedisCache) Get(ctx context.Context, key string) ([]byte, error) {
	return c.client.Get(ctx, key).Bytes()
}

func (c *RedisCache) Set(ctx context.Context, key string, value []byte, expiration time.Duration) error {
	return c.client.Set(ctx, key, value, expiration).Err()
}

func (c *RedisCache) Delete(ctx context.Context, key string) error {
	return c.client.Del(ctx, key).Err()
}

func (c *RedisCache) ClearAll(ctx context.Context) error {
	return c.client.FlushDB(ctx).Err()
}

func (c *RedisCache) GetAll(ctx context.Context, pattern string) (map[string][]byte, error) {
	keys, err := c.client.Keys(ctx, pattern).Result()
	if err != nil {
		return nil, err
	}

	result := make(map[string][]byte)
	for _, key := range keys {
		value, err := c.client.Get(ctx, key).Bytes()
		if err != nil {
			continue
		}
		result[key] = value
	}

	return result, nil
}

func (c *RedisCache) DeleteAll(ctx context.Context, pattern string) error {
	keys, err := c.client.Keys(ctx, pattern).Result()
	if err != nil {
		return err
	}
	for _, key := range keys {
		if err := c.client.Del(ctx, key).Err(); err != nil {
			return err
		}
	}
	return nil
}

func (c *RedisCache) HSet(ctx context.Context, key, field string, value interface{}) error {
	return c.client.HSet(ctx, key, field, value).Err()
}

func (c *RedisCache) Expire(ctx context.Context, key string, expiration time.Duration) error {
	return c.client.Expire(ctx, key, expiration).Err()
}

func (c *RedisCache) HGetAll(ctx context.Context, key string) (map[string]string, error) {
	return c.client.HGetAll(ctx, key).Result()
}

func (c *RedisCache) ZAdd(ctx context.Context, key string, members ...redis.Z) error {
	return c.client.ZAdd(ctx, key, members...).Err()
}

func (c *RedisCache) ZRemRangeByScore(ctx context.Context, key, min, max string) error {
	return c.client.ZRemRangeByScore(ctx, key, min, max).Err()
}

func (c *RedisCache) ZCount(ctx context.Context, key, min, max string) (int64, error) {
	return c.client.ZCount(ctx, key, min, max).Result()
}

func (c *RedisCache) ZRevRangeWithScores(ctx context.Context, key string, start, stop int64) ([]redis.Z, error) {
	return c.client.ZRevRangeWithScores(ctx, key, start, stop).Result()
}

func (c *RedisCache) ZScore(ctx context.Context, key, member string) (float64, error) {
	return c.client.ZScore(ctx, key, member).Result()
}

func (c *RedisCache) ZRangeByScore(ctx context.Context, key string, opt *redis.ZRangeBy) ([]string, error) {
	return c.client.ZRangeByScore(ctx, key, opt).Result()
}

func (c *RedisCache) ZRem(ctx context.Context, key, entry string) error {
	return c.client.ZRem(ctx, key, entry).Err()
}

func (c *RedisCache) Incr(ctx context.Context, key string) (int64, error) {
	return c.client.Incr(ctx, key).Result()
}

func (c *RedisCache) Decr(ctx context.Context, key string) (int64, error) {
	return c.client.Decr(ctx, key).Result()
}

func (c *RedisCache) DeleteMany(ctx context.Context, keys ...string) error {
	return c.client.Del(ctx, keys...).Err()
}

func (c *RedisCache) MGet(ctx context.Context, keys ...string) ([]interface{}, error) {
	return c.client.MGet(ctx, keys...).Result()
}
