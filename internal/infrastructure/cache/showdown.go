package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	showdownKey            string = "showdown:"
	ficturesKey            string = "fictures:"
	showdownParticipantKey string = "showdownParticipant:"
	showdownLeaderboardKey string = "showdownLeaderboard:"
)

type ShowdownCache interface {
	GetShowdown(ctx context.Context, showdownId primitive.ObjectID) (*models.Showdown, error)
	SetShowdown(ctx context.Context, showdown *models.Showdown) error
	Deleteshowdown(ctx context.Context, showdownId primitive.ObjectID) error
	GetShowdownParticipant(ctx context.Context, showdownId primitive.ObjectID, userId string) (*models.ShowdownParticipant, error)
	SetShowdownParticipant(ctx context.Context, showdownParticipant *models.ShowdownParticipant) (int64, error)
	DeleteshowdownParticipant(ctx context.Context, showdownId, userId primitive.ObjectID) error
	DeleteAllshowdowns(ctx context.Context) error
	GetLeaderboardEntitiesCount(ctx context.Context, showdownId primitive.ObjectID) (int64, error)
	GetPaginatedLeaderboard(ctx context.Context, showdownId primitive.ObjectID, page, pageSize int) ([]*LeaderboardEntity, error)
	GetPaginatedLeaderboardRev(ctx context.Context, showdownId primitive.ObjectID, page, pageSize int) ([]*LeaderboardEntity, error)
	SetFictures(ctx context.Context, showdownId primitive.ObjectID, fictures *models.FicturesCollection) error
	GetFictures(ctx context.Context, showdownId primitive.ObjectID) (*models.FicturesCollection, error)
}

type ShowdownCacheWrapper struct {
	cache            Cache
	leaderboardCache LeaderboardCache
}

func NewShowdownCacheWrapper(cache Cache, leaderboardCache LeaderboardCache) ShowdownCache {
	return &ShowdownCacheWrapper{cache: cache, leaderboardCache: leaderboardCache}
}

func (c *ShowdownCacheWrapper) GetShowdown(ctx context.Context, showdownId primitive.ObjectID) (*models.Showdown, error) {
	data, err := c.cache.Get(ctx, showdownKey+showdownId.Hex())
	if err != nil {
		return nil, err
	}
	showdown := &models.Showdown{}
	if err := json.Unmarshal(data, showdown); err != nil {
		return nil, err
	}
	return showdown, nil
}

func (c *ShowdownCacheWrapper) SetShowdown(ctx context.Context, showdown *models.Showdown) error {
	data, err := json.Marshal(showdown)
	if err != nil {
		return err
	}
	return c.cache.Set(ctx, showdownKey+showdown.ID.Hex(), data, 24*time.Hour)
}

func (c *ShowdownCacheWrapper) Deleteshowdown(ctx context.Context, showdownId primitive.ObjectID) error {
	return c.cache.Delete(ctx, showdownKey+showdownId.Hex())
}

func (c *ShowdownCacheWrapper) GetShowdownParticipant(ctx context.Context, showdownId primitive.ObjectID, userId string) (*models.ShowdownParticipant, error) {
	data, err := c.cache.Get(ctx, fmt.Sprintf("%s%s_%s", showdownParticipantKey, showdownId.Hex(), userId))
	if err != nil {
		return nil, err
	}
	showdownParticipant := &models.ShowdownParticipant{}
	if err := json.Unmarshal(data, showdownParticipant); err != nil {
		return nil, err
	}
	return showdownParticipant, nil
}

func (c *ShowdownCacheWrapper) SetShowdownParticipant(ctx context.Context, showdownParticipant *models.ShowdownParticipant) (int64, error) {
	if showdownParticipant == nil {
		return 0, fmt.Errorf("participant is nil")
	}
	data, err := json.Marshal(showdownParticipant)
	if err != nil {
		return 0, err
	}
	leaderboardKey := showdownLeaderboardKey + showdownParticipant.ShowdownID.Hex()
	err = c.leaderboardCache.AddOrUpdateScore(ctx, leaderboardKey, showdownParticipant.UserID.Hex(), float64(showdownParticipant.TotalScore))
	if err != nil {
		return 0, err
	}
	err = c.cache.Set(ctx, fmt.Sprintf("%s%s_%s", showdownParticipantKey, showdownParticipant.ShowdownID.Hex(), showdownParticipant.UserID.Hex()), data, 24*time.Hour)
	if err != nil {
		return 0, err
	}
	revRank, err := c.leaderboardCache.GetRank(ctx, leaderboardKey, showdownParticipant.UserID.Hex())
	if err != nil {
		return 0, nil
	}
	return revRank, nil
}

func (c *ShowdownCacheWrapper) GetLeaderboardEntitiesCount(ctx context.Context, showdownId primitive.ObjectID) (int64, error) {
	totalCount, err := c.leaderboardCache.GetTotalEntityCount(ctx, showdownLeaderboardKey+showdownId.Hex())
	if err != nil {
		return 0, err
	}
	return totalCount, nil
}

// Get from bottom scorers to top

func (c *ShowdownCacheWrapper) GetPaginatedLeaderboardRev(ctx context.Context, showdownId primitive.ObjectID, page, pageSize int) ([]*LeaderboardEntity, error) {
	if page < 1 {
		page = 1
	}
	start := (page - 1) * pageSize
	leaderBoardEntities, err := c.leaderboardCache.GetPaginatedLeaderboard(ctx, showdownLeaderboardKey+showdownId.Hex(), int64(start), int64(pageSize))
	if err != nil {
		return nil, err
	}
	return leaderBoardEntities, nil
}

// Get from top scorers to bottom
func (c *ShowdownCacheWrapper) GetPaginatedLeaderboard(ctx context.Context, showdownId primitive.ObjectID, page, pageSize int) ([]*LeaderboardEntity, error) {
	if page < 1 {
		page = 1
	}
	start := (page - 1) * pageSize
	leaderBoardEntities, err := c.leaderboardCache.GetPaginatedLeaderboardRev(ctx, showdownLeaderboardKey+showdownId.Hex(), int64(start), int64(pageSize))
	if err != nil {
		return nil, err
	}
	return leaderBoardEntities, nil
}

func (c *ShowdownCacheWrapper) DeleteshowdownParticipant(ctx context.Context, showdownId, userId primitive.ObjectID) error {
	return c.cache.Delete(ctx, fmt.Sprintf("%s%s_%s", showdownParticipantKey, showdownId.Hex(), userId.Hex()))
}

func (c *ShowdownCacheWrapper) DeleteAllshowdowns(ctx context.Context) error {
	return c.cache.DeleteAll(ctx, showdownKey+"*")
}

func (c *ShowdownCacheWrapper) GetFictures(ctx context.Context, showdownId primitive.ObjectID) (*models.FicturesCollection, error) {
	data, err := c.cache.Get(ctx, ficturesKey+showdownId.Hex())
	if err != nil {
		return nil, err
	}
	showdown := &models.FicturesCollection{}
	if err := json.Unmarshal(data, showdown); err != nil {
		return nil, err
	}
	return showdown, nil
}

func (c *ShowdownCacheWrapper) SetFictures(ctx context.Context, showdownId primitive.ObjectID, fictures *models.FicturesCollection) error {
	data, err := json.Marshal(fictures)
	if err != nil {
		return err
	}
	return c.cache.Set(ctx, ficturesKey+showdownId.Hex(), data, 24*time.Hour)
}
