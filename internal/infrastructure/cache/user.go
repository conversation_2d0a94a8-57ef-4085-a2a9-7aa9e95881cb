package cache

import (
	"context"
	"encoding/json"
	"time"

	"matiksOfficial/matiks-server-go/internal/models"
	stringUtils "matiksOfficial/matiks-server-go/utils/string"
)

const (
	userCacheKey      = "user:"
	userCacheDuration = 2 * time.Hour
)

type UserCache interface {
	GetUser(ctx context.Context, key string) (*models.User, error)
	SetUser(ctx context.Context, key string, value *models.User) error
	DeleteUser(ctx context.Context, key string) error
	GetUsersByIDs(ctx context.Context, keys []string) ([]*models.User, error)
}

type UserCacheWrapper struct {
	cache Cache
}

func NewUserCacheWrapper(cache Cache) UserCache {
	return &UserCacheWrapper{cache: cache}
}

func (c *UserCacheWrapper) GetUser(ctx context.Context, key string) (*models.User, error) {
	data, err := c.cache.Get(ctx, stringUtils.Concat(userCacheKey, key))
	if err != nil {
		return nil, err
	}
	user := &models.User{}
	if err := json.Unmarshal(data, user); err != nil {
		return nil, err
	}
	return user, nil
}

func (c *UserCacheWrapper) SetUser(ctx context.Context, key string, value *models.User) error {
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}
	return c.cache.Set(ctx, stringUtils.Concat(userCacheKey, key), data, userCacheDuration)
}

func (c *UserCacheWrapper) DeleteUser(ctx context.Context, key string) error {
	return c.cache.Delete(ctx, stringUtils.Concat(userCacheKey, key))
}

func (c *UserCacheWrapper) GetUsersByIDs(ctx context.Context, keys []string) ([]*models.User, error) {
	data, err := c.cache.MGet(ctx, keys...)
	if err != nil {
		return nil, err
	}
	var users []*models.User = make([]*models.User, len(data))
	for i, d := range data {
		if d == nil {
			continue
		}
		user := &models.User{}
		dataBytes, ok := d.([]byte)
		if !ok {
			continue
		}
		if err := json.Unmarshal(dataBytes, user); err != nil {
			return nil, err
		}
		users[i] = user
	}
	return users, nil
}
