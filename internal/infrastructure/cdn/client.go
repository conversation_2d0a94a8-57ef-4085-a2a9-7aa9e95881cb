package cdn

import (
	"context"
	"encoding/base64"
	"fmt"
	stdurl "net/url"
	"time"

	"cloud.google.com/go/compute/metadata"
	"go.uber.org/fx"
	"go.uber.org/zap"
	"google.golang.org/api/compute/v1"
	"google.golang.org/api/option"

	"matiksOfficial/matiks-server-go/pkg/config"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

type CDNClient interface {
	InvalidateCache(ctx context.Context, path string) error
	InvalidateCacheWithHost(ctx context.Context, host, path string) error
	ExtractPathFromURL(url string) string
}

type cdnClient struct {
	client    *compute.Service
	projectID string
	urlMap    string
	cdnHost   string
}

func NewCDNClient(lc fx.Lifecycle, cfg *config.Config) (CDNClient, error) {
	ctx := context.Background()

	serviceAccountKey, err := getServiceAccountKeyFromEnv(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to get service account key: %v", err)
	}

	client, err := compute.NewService(ctx, option.WithCredentialsJSON(serviceAccountKey))
	if err != nil {
		return nil, fmt.Errorf("failed to create Compute Engine client: %v", err)
	}

	projectID := cfg.GoogleCloudProject
	if projectID == "" {
		if metadata.OnGCE() {
			projectID, err = metadata.ProjectIDWithContext(ctx)
			if err != nil {
				return nil, fmt.Errorf("failed to get project ID from metadata: %v", err)
			}
		} else {
			return nil, fmt.Errorf("project ID not set in config and not running on GCP")
		}
	}

	urlMap := cfg.CDNUrlMap
	if urlMap == "" {
		return nil, fmt.Errorf("CDN URL map name not set in config")
	}

	cdnHost := cfg.CDNHost
	if cdnHost == "" {
		cdnHost = "cdn.matiks.com"
	}

	c := &cdnClient{
		client:    client,
		projectID: projectID,
		urlMap:    urlMap,
		cdnHost:   cdnHost,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting CDN client")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down CDN client")
			return nil
		},
	})

	return c, nil
}

func (c *cdnClient) InvalidateCache(ctx context.Context, path string) error {
	return c.invalidate(ctx, "", path)
}

func (c *cdnClient) InvalidateCacheWithHost(ctx context.Context, host, path string) error {
	return c.invalidate(ctx, host, path)
}

func (c *cdnClient) invalidate(ctx context.Context, host, path string) error {
	if host == "" {
		host = c.cdnHost
	}

	zlog.Info(ctx, "Invalidating CDN cache",
		zap.String("path", path),
		zap.String("host", host),
		zap.String("urlMap", c.urlMap))

	op, err := c.client.UrlMaps.InvalidateCache(
		c.projectID,
		c.urlMap,
		&compute.CacheInvalidationRule{
			Path:      path,
			Host:      host,
			CacheTags: nil,
		},
	).Context(ctx).Do()
	if err != nil {
		return fmt.Errorf("failed to invalidate cache: %v", err)
	}

	timeoutCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	for {
		select {
		case <-timeoutCtx.Done():
			return fmt.Errorf("timeout waiting for cache invalidation operation to complete")
		default:
			opStatus, err := c.client.GlobalOperations.Get(c.projectID, op.Name).Context(timeoutCtx).Do()
			if err != nil {
				return fmt.Errorf("error checking operation status: %v", err)
			}

			if opStatus.Status == "DONE" {
				if opStatus.Error != nil && len(opStatus.Error.Errors) > 0 {
					return fmt.Errorf("operation failed: %s", opStatus.Error.Errors[0].Message)
				}
				zlog.Info(ctx, "Successfully invalidated CDN cache",
					zap.String("path", path),
					zap.String("host", host))
				return nil
			}

			// Wait before polling again
			time.Sleep(1 * time.Second)
		}
	}
}

// ExtractPathFromURL extracts the path from a CDN URL for cache invalidation
func (c *cdnClient) ExtractPathFromURL(url string) string {
	// Parse the URL to properly handle query parameters
	parsedURL, err := stdurl.Parse(url)
	if err != nil {
		// If URL parsing fails, fall back to the original behavior
		prefix := fmt.Sprintf("https://%s/", c.cdnHost)
		if len(url) > len(prefix) && url[:len(prefix)] == prefix {
			return "/" + url[len(prefix):]
		}
		return url
	}

	// If the host matches our CDN host, extract just the path
	if parsedURL.Host == c.cdnHost {
		return parsedURL.Path
	}

	// If the URL doesn't match the expected format, return the whole URL as path
	return url
}

// getServiceAccountKeyFromEnv gets the service account key from the config
func getServiceAccountKeyFromEnv(cfg *config.Config) ([]byte, error) {
	encodedKey := cfg.CDNServiceKey
	if encodedKey == "" {
		return nil, fmt.Errorf("CDN service account key not set in config")
	}

	decodedKey, err := base64.StdEncoding.DecodeString(encodedKey)
	if err != nil {
		return nil, fmt.Errorf("failed to decode CDN service account key: %v", err)
	}

	return decodedKey, nil
}
