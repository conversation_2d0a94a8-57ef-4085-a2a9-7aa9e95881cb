package cdn

import (
	"testing"
)

func TestExtractPathFromURL(t *testing.T) {
	tests := []struct {
		name     string
		url      string
		cdnHost  string
		expected string
	}{
		{
			name:     "URL with CDN host and no query parameters",
			url:      "https://cdn.matiks.com/images/logo.png",
			cdnHost:  "cdn.matiks.com",
			expected: "/images/logo.png",
		},
		{
			name:     "URL with CDN host and query parameters",
			url:      "https://cdn.matiks.com/images/logo.png?v=123&t=456",
			cdnHost:  "cdn.matiks.com",
			expected: "/images/logo.png",
		},
		{
			name:     "URL with CDN host and path segments",
			url:      "https://cdn.matiks.com/images/profile/user123/avatar.jpg",
			cdnHost:  "cdn.matiks.com",
			expected: "/images/profile/user123/avatar.jpg",
		},
		{
			name:     "URL with different host",
			url:      "https://example.com/images/logo.png",
			cdnHost:  "cdn.matiks.com",
			expected: "https://example.com/images/logo.png", // Should return the original URL
		},
		{
			name:     "Invalid URL format",
			url:      "not-a-valid-url",
			cdnHost:  "cdn.matiks.com",
			expected: "not-a-valid-url", // Should return the original URL
		},
		{
			name:     "URL with timestamp in path",
			url:      "https://cdn.matiks.com/profile_picture/123456/1620000000",
			cdnHost:  "cdn.matiks.com",
			expected: "/profile_picture/123456/1620000000",
		},
		{
			name:     "URL with fragment",
			url:      "https://cdn.matiks.com/images/logo.png#section1",
			cdnHost:  "cdn.matiks.com",
			expected: "/images/logo.png",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := &cdnClient{
				cdnHost: tt.cdnHost,
			}
			result := client.ExtractPathFromURL(tt.url)
			if result != tt.expected {
				t.Errorf("ExtractPathFromURL() = %v, want %v", result, tt.expected)
			}
		})
	}
}
