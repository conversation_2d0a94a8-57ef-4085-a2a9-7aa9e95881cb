package database

import (
	"context"
	"time"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/pkg/config"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

type Database interface {
	Collection(string) *mongo.Collection
	Ping(context.Context) error
}

type database struct {
	db     *mongo.Database
	client *mongo.Client
}

func NewDatabase(lc fx.Lifecycle, cfg *config.Config) (Database, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 25*time.Second)
	defer cancel()

	clientOptions := options.Client().
		ApplyURI(cfg.MongoURI).
		SetMaxPoolSize(0).
		SetMinPoolSize(10)

	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		return nil, err
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "MongoDB connected and ready.")
			return client.Ping(ctx, nil)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down MongoDB.")
			return client.Disconnect(ctx)
		},
	})

	db := client.Database(cfg.MongoDB)
	return &database{db: db, client: client}, nil
}

func (d *database) Collection(name string) *mongo.Collection {
	return d.db.Collection(name)
}

func (d *database) Ping(ctx context.Context) error {
	return d.client.Ping(ctx, nil)
}
