package firebase

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/pkg/config"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	"go.uber.org/fx"
	"google.golang.org/api/option"
)

type firebaseApp struct {
	app *firebase.App
}

type App interface {
	Messaging(ctx context.Context) (*messaging.Client, error)
}

func InitializeApp(lc fx.Lifecycle, cfg *config.Config) (App, error) {
	jsonData, err := getServiceAccountKeyFromEnv(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to get service account key from env: %w", err)
	}
	opt := option.WithCredentialsJSON(jsonData)
	ctx := context.Background()
	app, err := firebase.NewApp(ctx, nil, opt)
	if err != nil {
		return nil, fmt.Errorf("error initializing app: %w", err)
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Firebase app initialized and ready.")
			return nil
		},
		OnStop: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Shutting down firebase app.")
			return nil
		},
	})

	return &firebaseApp{app: app}, nil
}

func (f *firebaseApp) Messaging(ctx context.Context) (*messaging.Client, error) {
	return f.app.Messaging(ctx)
}

var FirebaseAppModule = fx.Module(
	"firebase",
	fx.Provide(InitializeApp, NewFirebaseClient),
)
