package firebase

import (
	"context"
	"encoding/base64"
	"fmt"

	"matiksOfficial/matiks-server-go/pkg/config"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"firebase.google.com/go/v4/messaging"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

type firebaseClient struct {
	client *messaging.Client
}

type FirebaseClient interface {
	SendBatch(ctx context.Context, messages []*messaging.Message) error
	Send(ctx context.Context, message *messaging.Message) error
}

func NewFirebaseClient(lc fx.Lifecycle, firebaseApp App, config *config.Config) (FirebaseClient, error) {
	ctx := context.Background()
	client, err := firebaseApp.Messaging(ctx)
	if err != nil {
		return nil, fmt.Errorf("error getting Messaging client: %w", err)
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Firebase client initialized and ready.")
			return nil
		},
		OnStop: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Shutting down firebase client.")
			return nil
		},
	})

	return &firebaseClient{client: client}, nil
}

func getServiceAccountKeyFromEnv(cfg *config.Config) ([]byte, error) {
	encodedKey := cfg.FirebasePushNotificationServiceKey
	if encodedKey == "" {
		return nil, fmt.Errorf("environment variable FIREBASE_SERVICE_ACCOUNT_KEY not set")
	}

	decodedKey, err := base64.StdEncoding.DecodeString(encodedKey)
	if err != nil {
		return nil, fmt.Errorf("failed to decode service account key: %v", err)
	}

	return decodedKey, nil
}

func (c *firebaseClient) SendBatch(ctx context.Context, messages []*messaging.Message) error {
	if len(messages) == 0 {
		return nil
	}

	resp, err := c.client.SendEach(ctx, messages)
	if err != nil {
		return fmt.Errorf("error sending batch: %w", err)
	}
	zlog.Info(ctx, "Successfully sent batch", zap.Any("response", resp))
	return nil
}

func (c *firebaseClient) Send(ctx context.Context, message *messaging.Message) error {
	if message == nil {
		return fmt.Errorf("message is nil")
	}
	resp, err := c.client.Send(ctx, message)
	if err != nil {
		return fmt.Errorf("error sending message: %w", err)
	}
	zlog.Info(ctx, "Successfully sent message", zap.String("response", resp))
	return nil
}
