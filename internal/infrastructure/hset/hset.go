package hset

import "context"

type HSet interface {
	HSet(ctx context.Context, key, field string, value interface{}) error
	HGetAll(ctx context.Context, key string) (map[string]string, error)
	HIncrBy(ctx context.Context, key, field string, value int64) error
	HMGet(ctx context.Context, key string, fields ...string) ([]interface{}, error)
	HGet(ctx context.Context, key, field string) (string, error)
	IncrBy(ctx context.Context, key string, value int64) error
	DeleteAll(ctx context.Context, keys ...string) error
}
