package hset

import (
	"context"

	"github.com/redis/go-redis/v9"
)

type redisHSet struct {
	redisClient *redis.Client
}

func NewRedisHSet(client *redis.Client) HSet {
	return &redisHSet{redisClient: client}
}

func (r *redisHSet) HSet(ctx context.Context, key, field string, value interface{}) error {
	return r.redisClient.HSet(ctx, key, field, value).Err()
}

func (r *redisHSet) HGetAll(ctx context.Context, key string) (map[string]string, error) {
	return r.redisClient.HGetAll(ctx, key).Result()
}

func (r *redisHSet) HIncrBy(ctx context.Context, key, field string, value int64) error {
	return r.redisClient.HIncrBy(ctx, key, field, value).Err()
}

func (r *redisHSet) HMGet(ctx context.Context, key string, fields ...string) ([]interface{}, error) {
	return r.redisClient.HMGet(ctx, key, fields...).Result()
}

func (r *redisHSet) HGet(ctx context.Context, key, field string) (string, error) {
	return r.redisClient.HGet(ctx, key, field).Result()
}

func (r *redisHSet) IncrBy(ctx context.Context, key string, value int64) error {
	return r.redisClient.IncrBy(ctx, key, value).Err()
}

func (r *redisHSet) DeleteAll(ctx context.Context, keys ...string) error {
	return r.redisClient.Del(ctx, keys...).Err()
}
