package leaderboard

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.uber.org/fx"
)

var (
	ErrUsersNotFound = fmt.Errorf("users not found")
	ErrUserRatingNil = fmt.Errorf("user rating is nil")
)

func WriteLog(message string) {
	execPath, err := os.Executable()
	if err != nil {
		log.Fatalf("Failed to get executable path: %v", err)
	}
	dir := filepath.Dir(execPath)
	logFileName := filepath.Join(dir, "app.log")

	logFile, err := os.OpenFile(logFileName, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0o644)
	if err != nil {
		log.Fatalf("Failed to open or create log file: %v", err)
	}
	defer logFile.Close()
	logger := log.New(logFile, "", log.LstdFlags)
	logger.Printf("%s\n", message)
}

type globalLeaderboard struct {
	ratingCounter *RatingCounter
	userRepo      repository.UserRepository
}

type GlobalLeaderboard interface {
	AddUser(ctx context.Context, userRating int) error
	UpdateUserRating(ctx context.Context, oldRating *int, newRating int) error
	GetLeaderboard(ctx context.Context, params models.LeaderboardParams) (*models.UserLeaderboardPage, error)
	GetUserRank(ctx context.Context, userRating int) (int64, error)
}

func NewGlobalLeaderboard(lc fx.Lifecycle, rc *RatingCounter, repositoryFactory *repository.RepositoryFactory) GlobalLeaderboard {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Global leaderboard initialized and ready.")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down global leaderboard.")
			return nil
		},
	})
	return &globalLeaderboard{
		ratingCounter: rc,
		userRepo:      repositoryFactory.UserRepository,
	}
}

func (gl *globalLeaderboard) AddUser(ctx context.Context, userRating int) error {
	return gl.ratingCounter.IncrementRating(ctx, userRating)
}

func (gl *globalLeaderboard) UpdateUserRating(ctx context.Context, oldRating *int, newRating int) error {
	if oldRating != nil {
		if err := gl.ratingCounter.DecrementRating(ctx, *oldRating); err != nil {
			return err
		}
	}
	return gl.ratingCounter.IncrementRating(ctx, newRating)
}

func (gl *globalLeaderboard) GetUserRank(ctx context.Context, userRating int) (int64, error) {
	rankings, err := gl.ratingCounter.GetUserRankings(ctx)
	if err != nil {
		return 0, err
	}

	rank := rankings[userRating]
	return rank, nil
}

func (gl *globalLeaderboard) GetLeaderboard(ctx context.Context, params models.LeaderboardParams) (*models.UserLeaderboardPage, error) {
	higherRatedCounts, err := gl.ratingCounter.GetUserRankings(ctx)
	if err != nil {
		return nil, err
	}

	users, err := gl.fetchUsers(ctx, params)
	if err != nil {
		return nil, err
	}

	edges, err := gl.createLeaderboardEdges(users, higherRatedCounts)
	if err != nil {
		return nil, err
	}
	return gl.buildLeaderboardPage(ctx, edges)
}

func (gl *globalLeaderboard) buildLeaderboardPage(ctx context.Context, edges []*models.LeaderboardEdge) (*models.UserLeaderboardPage, error) {
	totalUsers, err := gl.ratingCounter.GetTotalUsers(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get total users: %v", err)
	}

	return &models.UserLeaderboardPage{
		Edges:      edges,
		TotalCount: int(totalUsers),
	}, nil
}

func (gl *globalLeaderboard) createLeaderboardEdges(users []*models.User, higherRatedCounts map[int]int64) ([]*models.LeaderboardEdge, error) {
	if len(users) == 0 {
		return nil, nil
	}
	edges := make([]*models.LeaderboardEdge, 0, len(users))
	for _, user := range users {
		userPublicData := utils.GetUserPublicDetails(user)
		if user.Rating == nil {
			edges = append(edges, getEdge(user))
			continue
		}
		rank, ok := higherRatedCounts[*user.Rating]
		if !ok {
			edges = append(edges, getEdge(user))
			continue
		}
		userPublicData.GlobalRank = utils.AllocPtr(int(rank))

		edges = append(edges, getEdge(user))

	}
	return edges, nil
}

func (gl *globalLeaderboard) fetchUsers(ctx context.Context, params models.LeaderboardParams) ([]*models.User, error) {
	users, err := gl.userRepo.GetPaginatedUsersByRatingAndCreationTime(ctx, int64(params.Page), int64(params.PageSize))
	if err != nil {
		return nil, fmt.Errorf("failed to fetch users: %w", err)
	}

	if len(users) == 0 || users[0] == nil {
		return nil, ErrUsersNotFound
	}

	if users[0].Rating == nil {
		return nil, ErrUserRatingNil
	}

	return users, nil
}

func getEdge(user *models.User) *models.LeaderboardEdge {
	if user == nil {
		return nil
	}
	return &models.LeaderboardEdge{
		Node:   utils.GetUserPublicDetails(user),
		Cursor: user.ID.Hex(),
	}
}
