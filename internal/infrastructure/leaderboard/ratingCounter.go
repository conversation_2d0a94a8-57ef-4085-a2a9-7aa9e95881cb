package leaderboard

import (
	"context"
	"errors"
	"fmt"
	"log"
	"strconv"

	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/infrastructure/hset"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"github.com/redis/go-redis/v9"
	"go.uber.org/fx"
)

const (
	MinRating      = 100
	MaxRating      = 5000
	RatingCountKey = "rating_counts"
	TotalUsersKey  = "total_users"
)

type RatingCounter struct {
	userRepo repository.UserRepository
	hset     hset.HSet
	cache    cache.Cache
	// rankings map[int]int64
}

const (
	RETRY_ATTEMPTS = 5
)

func NewRatingCounter(lc fx.Lifecycle, repositoryFactory *repository.RepositoryFactory, hset hset.HSet, cache cache.Cache) *RatingCounter {
	rc := &RatingCounter{userRepo: repositoryFactory.UserRepository, hset: hset, cache: cache}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Rating counter initialized and ready.")
			for range RETRY_ATTEMPTS {
				err := rc.InitializeRatingCounts(context.Background())
				if err == nil {
					zlog.Debug(ctx, "Rating counter initialized successfully.")
					return nil
				}
				zlog.Error(ctx, "Failed to initialize rating counter: ", err)
			}
			return fmt.Errorf("failed to initialize rating counter after %d attempts with error: %v", RETRY_ATTEMPTS, err)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down rating counter.")
			return nil
		},
	})

	return rc
}

func validateRatingCounter(rc *RatingCounter) error {
	if rc == nil {
		return errors.New("RatingCounter is nil")
	}
	if rc.hset == nil {
		return errors.New("RatingCounter.hset is nil")
	}
	if rc.cache == nil {
		return errors.New("RatingCounter.cache is nil")
	}
	if rc.userRepo == nil {
		return errors.New("RatingCounter.userRepo is nil")
	}
	return nil
}

func (rc *RatingCounter) IncrementRating(ctx context.Context, rating int) error {
	if err := validateRatingCounter(rc); err != nil {
		return err
	}
	if rating < MinRating || rating > MaxRating {
		return fmt.Errorf("rating %d out of bounds", rating)
	}

	if err := rc.hset.HIncrBy(ctx, RatingCountKey, fmt.Sprint(rating), 1); err != nil {
		return err
	}
	if _, err := rc.cache.Incr(ctx, TotalUsersKey); err != nil {
		return err
	}
	return nil
}

func (rc *RatingCounter) DecrementRating(ctx context.Context, rating int) error {
	if err := validateRatingCounter(rc); err != nil {
		return err
	}
	if rating < MinRating || rating > MaxRating {
		return fmt.Errorf("rating %d out of bounds", rating)
	}

	if err := rc.hset.HIncrBy(ctx, RatingCountKey, fmt.Sprint(rating), -1); err != nil {
		return err
	}
	if _, err := rc.cache.Decr(ctx, TotalUsersKey); err != nil {
		return err
	}
	return nil
}

func (rc *RatingCounter) GetRatingCount(ctx context.Context, rating int) (int64, error) {
	if err := validateRatingCounter(rc); err != nil {
		return 0, err
	}
	countStr, err := rc.hset.HGet(ctx, RatingCountKey, fmt.Sprint(rating))
	if errors.Is(err, redis.Nil) {
		return 0, nil
	}
	if err != nil {
		return 0, err
	}
	count, err := strconv.ParseInt(countStr, 10, 64)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (rc *RatingCounter) GetTotalUsers(ctx context.Context) (int64, error) {
	if err := validateRatingCounter(rc); err != nil {
		return 0, err
	}
	countBytes, err := rc.cache.Get(ctx, TotalUsersKey)
	if errors.Is(err, redis.Nil) {
		return 0, nil
	}
	if err != nil {
		return 0, err
	}
	count, err := strconv.ParseInt(string(countBytes), 10, 64)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (rc *RatingCounter) GetUserRankings(ctx context.Context) (map[int]int64, error) {
	if err := validateRatingCounter(rc); err != nil {
		return nil, err
	}
	ratingCounts, err := rc.hset.HGetAll(ctx, RatingCountKey)
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, fmt.Errorf("failed to get rating counts: %w", err)
	}

	rankings := make(map[int]int64)
	currentRank := int64(1)

	for rating := MaxRating; rating >= MinRating; rating-- {
		if countStr, exists := ratingCounts[fmt.Sprint(rating)]; exists {
			count := mustParseInt64(countStr)
			rankings[rating] = currentRank
			currentRank += count
		} else {
			rankings[rating] = currentRank
		}
	}

	return rankings, nil
}

func (rc *RatingCounter) InitializeRatingCounts(ctx context.Context) error {
	if err := validateRatingCounter(rc); err != nil {
		return err
	}
	if err := rc.cache.DeleteMany(ctx, RatingCountKey, TotalUsersKey); err != nil {
		return fmt.Errorf("failed to clear existing counts: %w", err)
	}

	var totalUsers int64
	userRatingCounts, err := rc.userRepo.GetUsersRatingCount(ctx, MinRating, MaxRating)
	if err != nil {
		return fmt.Errorf("failed to get user rating counts, err: %v", err)
	}
	if len(userRatingCounts) == 0 {
		return fmt.Errorf("user rating count is 0")
	}

	for _, counter := range userRatingCounts {
		if err := rc.hset.HSet(ctx, RatingCountKey, fmt.Sprint(counter.Rating), counter.Count); err != nil {
			return fmt.Errorf("failed to set rating count: %w", err)
		}
		totalUsers += counter.Count
	}

	if err := rc.cache.Set(ctx, TotalUsersKey, []byte(fmt.Sprint(totalUsers)), 0); err != nil {
		return fmt.Errorf("failed to set total users: %w", err)
	}

	return nil
}

func mustParseInt64(s string) int64 {
	val, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		log.Fatalf("failed to parse string to int64: %v", err)
	}
	return val
}
