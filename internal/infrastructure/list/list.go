package list

import (
	"context"
	"time"

	"github.com/redis/go-redis/v9"
)

type redisList struct {
	client *redis.Client
}

type List interface {
	LPush(ctx context.Context, key string, values ...interface{}) error
	RPush(ctx context.Context, key string, values ...interface{}) error
	LPop(ctx context.Context, key string) (string, error)
	LPopCount(ctx context.Context, key string, count int) ([]string, error)
	RPop(ctx context.Context, key string) (string, error)
	RPopCount(ctx context.Context, key string, count int) ([]string, error)

	BLPop(ctx context.Context, timeout time.Duration, keys ...string) ([]string, error)
	BRPop(ctx context.Context, timeout time.Duration, keys ...string) ([]string, error)

	LRange(ctx context.Context, key string, start, stop int64) ([]string, error)
	LTrim(ctx context.Context, key string, start, stop int64) error

	LLen(ctx context.Context, key string) (int64, error)
	LIndex(ctx context.Context, key string, index int64) (string, error)

	LSet(ctx context.Context, key string, index int64, value interface{}) error
	LRem(ctx context.Context, key string, count int64, value interface{}) (int64, error)

	LInsertBefore(ctx context.Context, key string, pivot, value interface{}) (int64, error)
	LInsertAfter(ctx context.Context, key string, pivot, value interface{}) (int64, error)

	LPos(ctx context.Context, key, value string, args redis.LPosArgs) (int64, error)
	LPosCount(ctx context.Context, key, value string, count int64, args redis.LPosArgs) ([]int64, error)
}

func NewRedisList(client *redis.Client) List {
	return &redisList{client: client}
}

func (c *redisList) LPush(ctx context.Context, key string, values ...interface{}) error {
	return c.client.LPush(ctx, key, values...).Err()
}

func (c *redisList) RPush(ctx context.Context, key string, values ...interface{}) error {
	return c.client.RPush(ctx, key, values...).Err()
}

func (c *redisList) LPop(ctx context.Context, key string) (string, error) {
	return c.client.LPop(ctx, key).Result()
}

func (c *redisList) LPopCount(ctx context.Context, key string, count int) ([]string, error) {
	return c.client.LPopCount(ctx, key, count).Result()
}

func (c *redisList) RPop(ctx context.Context, key string) (string, error) {
	return c.client.RPop(ctx, key).Result()
}

func (c *redisList) RPopCount(ctx context.Context, key string, count int) ([]string, error) {
	return c.client.RPopCount(ctx, key, count).Result()
}

func (c *redisList) BLPop(ctx context.Context, timeout time.Duration, keys ...string) ([]string, error) {
	return c.client.BLPop(ctx, timeout, keys...).Result()
}

func (c *redisList) BRPop(ctx context.Context, timeout time.Duration, keys ...string) ([]string, error) {
	return c.client.BRPop(ctx, timeout, keys...).Result()
}

func (c *redisList) LRange(ctx context.Context, key string, start, stop int64) ([]string, error) {
	return c.client.LRange(ctx, key, start, stop).Result()
}

func (c *redisList) LTrim(ctx context.Context, key string, start, stop int64) error {
	return c.client.LTrim(ctx, key, start, stop).Err()
}

func (c *redisList) LLen(ctx context.Context, key string) (int64, error) {
	return c.client.LLen(ctx, key).Result()
}

func (c *redisList) LIndex(ctx context.Context, key string, index int64) (string, error) {
	return c.client.LIndex(ctx, key, index).Result()
}

func (c *redisList) LSet(ctx context.Context, key string, index int64, value interface{}) error {
	return c.client.LSet(ctx, key, index, value).Err()
}

func (c *redisList) LRem(ctx context.Context, key string, count int64, value interface{}) (int64, error) {
	return c.client.LRem(ctx, key, count, value).Result()
}

func (c *redisList) LInsertBefore(ctx context.Context, key string, pivot, value interface{}) (int64, error) {
	return c.client.LInsertBefore(ctx, key, pivot, value).Result()
}

func (c *redisList) LInsertAfter(ctx context.Context, key string, pivot, value interface{}) (int64, error) {
	return c.client.LInsertAfter(ctx, key, pivot, value).Result()
}

func (c *redisList) LPos(ctx context.Context, key, value string, args redis.LPosArgs) (int64, error) {
	return c.client.LPos(ctx, key, value, args).Result()
}

func (c *redisList) LPosCount(ctx context.Context, key, value string, count int64, args redis.LPosArgs) ([]int64, error) {
	return c.client.LPosCount(ctx, key, value, count, args).Result()
}
