package locks

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

type RedisLock struct {
	client *redis.Client
}

func NewRedisLock(client *redis.Client) Locks {
	return &RedisLock{client: client}
}

func (r *RedisLock) AcquireLock(ctx context.Context, lockKey, lockValue string, ttl time.Duration) (bool, error) {
	result, err := r.client.SetNX(ctx, lockKey, lockValue, ttl).Result()
	if err != nil {
		return false, err
	}
	return result, nil
}

func (r *RedisLock) ReleaseLock(ctx context.Context, lockKey, lockValue string) error {
	luaScript := `
        if redis.call("GET", KEYS[1]) == ARGV[1] then
            return redis.call("DEL", KEYS[1])
        else
            return 0
        end
    `

	result, err := r.client.Eval(ctx, luaScript, []string{lockKey}, lockValue).Result()
	if err != nil {
		return err
	}

	if result.(int64) == 0 {
		return fmt.Errorf("lock not released: either lock expired or not owned by the client")
	}

	return nil
}
