package pubsub

import (
	"context"
	"sync"
	"time"
)

type LocalPubSub struct {
	mu            sync.RWMutex
	subscriptions map[string][]chan interface{}
}

func NewLocalPubSub() (PubSub, error) {
	pubsub := &LocalPubSub{
		subscriptions: make(map[string][]chan interface{}),
	}
	defer pubsub.cleanup()
	return pubsub, nil
}

func (m *LocalPubSub) Publish(ctx context.Context, channel string, message interface{}) error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if subscribers, ok := m.subscriptions[channel]; ok {
		for _, ch := range subscribers {
			select {
			case ch <- message:
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(100 * time.Millisecond):
				// Log a warning about slow subscriber
			}
		}
	}

	return nil
}

func (m *LocalPubSub) Subscribe(ctx context.Context, channel string) (Subscription, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	ch := make(chan interface{}, 100)
	m.subscriptions[channel] = append(m.subscriptions[channel], ch)

	sub := &LocalSubscription{
		pubsub:  m,
		channel: channel,
		ch:      ch,
		done:    make(chan struct{}),
	}

	go func() {
		select {
		case <-ctx.Done():
			sub.Close()
		case <-sub.done:
		}
	}()

	return sub, nil
}

func (m *LocalPubSub) cleanup() {
	m.mu.Lock()
	defer m.mu.Unlock()

	for channel, subs := range m.subscriptions {
		activeSubs := subs[:0]
		for _, sub := range subs {
			select {
			case _, ok := <-sub:
				if ok {
					activeSubs = append(activeSubs, sub)
				}
			default:
				activeSubs = append(activeSubs, sub)
			}
		}
		m.subscriptions[channel] = activeSubs
		if len(activeSubs) == 0 {
			delete(m.subscriptions, channel)
		}
	}
}

type LocalSubscription struct {
	pubsub    *LocalPubSub
	channel   string
	ch        chan interface{}
	done      chan struct{}
	closeOnce sync.Once
}

func (s *LocalSubscription) Channel() <-chan interface{} {
	return s.ch
}

func (s *LocalSubscription) Close() error {
	s.pubsub.mu.Lock()
	defer s.pubsub.mu.Unlock()

	s.closeOnce.Do(func() {
		subscribers := s.pubsub.subscriptions[s.channel]
		newSubscribers := make([]chan interface{}, 0, len(subscribers))
		for _, ch := range subscribers {
			if ch != s.ch {
				newSubscribers = append(newSubscribers, ch)
			}
		}
		s.pubsub.subscriptions[s.channel] = newSubscribers

		close(s.ch)
		close(s.done)

		if len(newSubscribers) == 0 {
			delete(s.pubsub.subscriptions, s.channel)
		}
	})

	return nil
}
