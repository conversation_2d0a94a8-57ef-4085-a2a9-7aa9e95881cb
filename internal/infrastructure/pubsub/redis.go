package pubsub

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"github.com/redis/go-redis/v9"
	"go.uber.org/fx"
)

type RedisPubSub struct {
	client *redis.Client
}

func NewRedisPubSub(lc fx.Lifecycle, client *redis.Client) PubSub {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Redis pubsub initialized and ready.")
			if err = client.Ping(ctx).Err(); err != nil {
				zlog.Error(ctx, "Failed to connect to Redis: %v", err)
			}
			return
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down Redis pubsub.")
			return nil
		},
	})
	return &RedisPubSub{client: client}
}

func (rps *RedisPubSub) Publish(ctx context.Context, channel string, message interface{}) error {
	jsonMessage, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	if err := rps.client.Publish(ctx, channel, jsonMessage).Err(); err != nil {
		return fmt.Errorf("failed to publish message: %w", err)
	}

	return nil
}

type RedisSubscription struct {
	pubsub    *redis.PubSub
	channel   chan interface{}
	done      chan struct{}
	closeOnce sync.Once
	mu        sync.Mutex
	closed    bool
}

func (rs *RedisSubscription) Channel() <-chan interface{} {
	return rs.channel
}

func (rs *RedisSubscription) Close() error {
	zlog.Info(context.Background(), "Closing Redis subscription")
	var err error
	rs.closeOnce.Do(func() {
		close(rs.done)
		rs.mu.Lock()
		rs.closed = true
		rs.mu.Unlock()
		close(rs.channel)
		err = rs.pubsub.Close()
	})
	return err
}

func (rps *RedisPubSub) Subscribe(ctx context.Context, channel string) (Subscription, error) {
	pubsub := rps.client.Subscribe(ctx, channel)

	subscription := &RedisSubscription{
		pubsub:  pubsub,
		channel: make(chan interface{}, 100),
		done:    make(chan struct{}),
		closed:  false,
	}

	ctxWithCancel, cancel := context.WithCancel(ctx)

	go func() {
		defer cancel()
		defer pubsub.Close()

		defer func() {
			if r := recover(); r != nil {
				zlog.Error(ctx, "Recovered from panic in Redis subscription", r.(error), zap.String("channel", channel))
			}
		}()

		messageCh := make(chan *redis.Message, 10)
		errCh := make(chan error, 1)

		go func() {
			defer close(messageCh)
			defer close(errCh)

			for {
				msg, err := pubsub.ReceiveMessage(ctxWithCancel)
				if err != nil {
					if !errors.Is(err, redis.Nil) {
						errCh <- err
					}

					select {
					case <-ctxWithCancel.Done():
						return
					default:
						if strings.Contains(err.Error(), "use of closed network connection") ||
							strings.Contains(err.Error(), "connection closed") {
							return
						}
						continue
					}
				}

				select {
				case messageCh <- msg:
					// Message sent successfully
				case <-ctxWithCancel.Done():
					return
				}
			}
		}()

		for {
			select {
			case <-subscription.done:
				zlog.Debug(ctx, "Redis subscription done signal received", zap.String("channel", channel))
				return

			case <-ctx.Done():
				zlog.Debug(ctx, "Context canceled for Redis subscription", zap.String("channel", channel))
				return

			case err, ok := <-errCh:
				if !ok {
					return
				}

				if ctx.Err() != nil || strings.Contains(err.Error(), "context canceled") {
					zlog.Debug(ctx, "Context canceled while receiving message", zap.Error(err), zap.String("channel", channel))
					return
				}

				if strings.Contains(err.Error(), "use of closed network connection") {
					zlog.Debug(ctx, "Redis connection closed", zap.Error(err), zap.String("channel", channel))
					return
				} else {
					zlog.Error(ctx, "Error receiving message", err, zap.String("channel", channel))
				}

			case msg, ok := <-messageCh:
				if !ok {
					return
				}

				subscription.mu.Lock()
				isClosed := subscription.closed
				subscription.mu.Unlock()

				if isClosed {
					zlog.Debug(ctx, "Not sending message because subscription is closed", zap.String("channel", channel))
					return
				}

				select {
				case subscription.channel <- msg.Payload:
					// Message sent successfully
				case <-time.After(time.Second):
					zlog.Warn(ctx, "Failed to send message: channel might be full or slow consumer", zap.String("channel", channel))
				case <-subscription.done:
					return
				case <-ctx.Done():
					return
				}
			}
		}
	}()

	return subscription, nil
}

func (rps *RedisPubSub) Close() error {
	return rps.client.Close()
}
