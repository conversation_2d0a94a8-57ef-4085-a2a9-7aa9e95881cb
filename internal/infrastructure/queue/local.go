package queue

import (
	"context"
	"sync"
	"time"

	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/zap"
)

type waitingPlayersQueue struct {
	waitingList []*waitingUser
	mu          sync.Mutex
}

var (
	instance *waitingPlayersQueue
	once     sync.Once
)

func GetWaitingPlayersQueue() Queue {
	once.Do(func() {
		instance = &waitingPlayersQueue{}
	})
	return instance
}

func (q *waitingPlayersQueue) AddUser(ctx context.Context, user *models.User, gameConfig models.GameConfigInterface) ([]*models.User, error) {
	q.mu.Lock()
	defer q.mu.Unlock()
	q.removeUserByID(ctx, user.ID.Hex())

	bannedIDsStr := []string{
		"6737985c017de8d955e41352", "670e8462f42c2b89f3a8217b", "67e1a6dcfc81f50df2a2d0d1", "67fa1e13d83744b468e2c047", "6816e279938bf4a7e6e0ecb3",
		"681b60e12fda3a0da5d7ed98", "66f45bddd93afbd829643852", "681b650b95bb0605254d49ed", "67fd154e94168d273725a420", "6738f374650cad273cd15100",
		"680a506061e082a513dac47d", "67fd82140397e7c8ed75b3dc", "680e9d3938b2e13634e248dc", "67fe38e9cb1e1aff238a63f7", "6816deda11e2a69441abc5d4",
		"6813e050a593b1bca13261f7", "6814a6eb302cb899de1dc0f8", "67fa7564f63fff1d71936cd8", "6737985c017de8d955e41352", "670f9fec50eda0cb36ce4860",
		"67092a21340114ceca88897f", "671114bf9670bbf54fa9925f", "681db78f4ea66a4648a3f8e8",
	}

	for _, bannedIDStr := range bannedIDsStr {
		if user.ID.Hex() == bannedIDStr {
			return nil, nil
		}
	}

	currentUserRating := gameutils.GetPlayerRatingByGameType(user, gameConfig.GetGameType())
	isCurrentUserHumanBot := false

	if user.IsHumanBot != nil {
		isCurrentUserHumanBot = *user.IsHumanBot
	}

	if user.IsShadowBanned != nil && *user.IsShadowBanned {
		return nil, nil
	}

	for i, waitingUser := range q.waitingList {
		rating := gameutils.GetPlayerRatingByGameType(waitingUser.User, gameConfig.GetGameType())
		isHumanBot := false
		if waitingUser.User.IsHumanBot != nil {
			isHumanBot = *waitingUser.User.IsHumanBot
		}
		if isHumanBot && isCurrentUserHumanBot {
			continue
		}
		if isGameConfigCompatible(gameConfig, waitingUser.GameConfig) && ValidRatingMatch(currentUserRating, rating) {
			if isHumanBot {
				if PlayedInLastHour(user.ID, waitingUser.User) > 4 {
					continue
				} else {
					UpdatePlayedInLastHour(user.ID, waitingUser.User)
				}
			}
			if isCurrentUserHumanBot {
				if PlayedInLastHour(waitingUser.User.ID, user) > 4 {
					continue
				} else {
					UpdatePlayedInLastHour(waitingUser.User.ID, user)
				}
			}
			matchedUser := q.waitingList[i]
			q.waitingList = append(q.waitingList[:i], q.waitingList[i+1:]...)

			zlog.Info(ctx, "Match found", zap.String("matchedUserID", matchedUser.User.ID.Hex()),
				zap.String("currentUserID", user.ID.Hex()), zap.Int("currentUserRating", currentUserRating),
				zap.Int("matchedUserRating", rating))

			return []*models.User{matchedUser.User, user}, nil
		}
	}

	q.waitingList = append(q.waitingList, &waitingUser{
		User:         user,
		WaitingSince: time.Now(),
		GameConfig:   gameConfig,
	})
	zlog.Info(ctx, "User added to waiting queue", zap.Int("queueLength", len(q.waitingList)))

	return nil, nil
}

func (q *waitingPlayersQueue) RemoveUser(ctx context.Context, userID string) (bool, error) {
	q.mu.Lock()
	defer q.mu.Unlock()
	return q.removeUserByID(ctx, userID)
}

func (q *waitingPlayersQueue) removeUserByID(ctx context.Context, userID string) (bool, error) {
	zlog.Info(ctx, "Remove user from waiting queue", zap.String("userID", userID))
	for i, waitingUser := range q.waitingList {
		if waitingUser.User.ID.Hex() == userID {
			q.waitingList = append(q.waitingList[:i], q.waitingList[i+1:]...)
			zlog.Debug(ctx, "User removed from waiting queue")
			return true, nil
		}
	}
	zlog.Debug(ctx, "User not found in waiting queue")
	return false, nil
}

func (q *waitingPlayersQueue) IsUserPresentInWaitingList(ctx context.Context, user *models.User) (bool, error) {
	q.mu.Lock()
	defer q.mu.Unlock()

	zlog.Debug(ctx, "Checking if user is present in waiting list")
	for _, waitingUser := range q.waitingList {
		if waitingUser.User.ID == user.ID {
			zlog.Debug(ctx, "User found in waiting list")
			return true, nil
		}
	}
	zlog.Debug(ctx, "User not found in waiting list")
	return false, nil
}
