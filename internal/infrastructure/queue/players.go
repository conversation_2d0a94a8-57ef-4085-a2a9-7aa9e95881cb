package queue

import (
	"context"
	"fmt"
	"math"
	"time"

	"matiksOfficial/matiks-server-go/internal/models"
)

type waitingUser struct {
	User         *models.User               `json:"user"`
	WaitingSince time.Time                  `json:"waiting_since"`
	GameConfig   models.GameConfigInterface `json:"game_config"`
}

const maxAllowedRatingDiff = 200

type Queue interface {
	AddUser(ctx context.Context, user *models.User, gameConfig models.GameConfigInterface) ([]*models.User, error)
	RemoveUser(ctx context.Context, userID string) (bool, error)
	IsUserPresentInWaitingList(ctx context.Context, user *models.User) (bool, error)
}

func isGameConfigCompatible(config1, config2 models.GameConfigInterface) bool {
	gameType1 := fmt.Sprintf("%v", config1.GetGameType())
	gameType2 := fmt.Sprintf("%v", config2.GetGameType())
	if gameType1 != gameType2 || *config1.GetNumPlayers() != *config2.GetNumPlayers() || *config1.GetTimeLimit() != *config2.GetTimeLimit() {
		return false
	}
	return true
}

func ValidRatingMatch(curUserRating, queuedUserRating int) bool {
	if curUserRating >= 2000 && queuedUserRating >= 2000 {
		return true
	} else {
		ratingDiff := math.Abs(float64(queuedUserRating) - float64(curUserRating))
		return int(ratingDiff) <= maxAllowedRatingDiff
	}
}

func PlayedInLastHour(userID models.ObjectID, botUser *models.User) int {
	var validTimes []time.Time
	playedTimes := botUser.HumanBotConfig.LastOneHourOpponents[userID]
	for _, t := range playedTimes {
		if time.Since(t) <= time.Hour {
			validTimes = append(validTimes, t)
		}
	}
	return len(botUser.HumanBotConfig.LastOneHourOpponents[userID])
}

func UpdatePlayedInLastHour(userID models.ObjectID, botUser *models.User) {
	currentTime := time.Now()

	if botUser.HumanBotConfig.LastOneHourOpponents == nil {
		botUser.HumanBotConfig.LastOneHourOpponents = make(map[models.ObjectID][]time.Time)
	}

	times, exists := botUser.HumanBotConfig.LastOneHourOpponents[userID]

	if exists {
		var validTimes []time.Time
		for _, t := range times {
			if currentTime.Sub(t) <= time.Hour {
				validTimes = append(validTimes, t)
			}
		}

		validTimes = append(validTimes, currentTime)

		botUser.HumanBotConfig.LastOneHourOpponents[userID] = validTimes
	} else {
		botUser.HumanBotConfig.LastOneHourOpponents[userID] = []time.Time{currentTime}
	}
}
