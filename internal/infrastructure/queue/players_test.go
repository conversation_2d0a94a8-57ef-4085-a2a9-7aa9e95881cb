package queue

import (
	"testing"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"matiksOfficial/matiks-server-go/internal/models"
)

func Test_validRatingMatch(t *testing.T) {
	type args struct {
		curUserRating    int
		queuedUserRating int
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// Case 1: Both ratings below 2000
		{
			name: "both ratings below 2000 with difference within maxAllowedRatingDiff",
			args: args{
				curUserRating:    1000,
				queuedUserRating: 1100,
			},
			want: true,
		},
		{
			name: "both ratings below 2000 with difference exactly maxAllowedRatingDiff",
			args: args{
				curUserRating:    1000,
				queuedUserRating: 1200,
			},
			want: true,
		},
		{
			name: "both ratings below 2000 with difference exceeding maxAllowedRatingDiff",
			args: args{
				curUserRating:    1000,
				queuedUserRating: 1201,
			},
			want: false,
		},
		{
			name: "both ratings below 2000 with negative difference within maxAllowedRatingDiff",
			args: args{
				curUserRating:    1200,
				queuedUserRating: 1000,
			},
			want: true,
		},
		{
			name: "both ratings below 2000 with negative difference exceeding maxAllowedRatingDiff",
			args: args{
				curUserRating:    1300,
				queuedUserRating: 1000,
			},
			want: false,
		},

		// Case 2: Both ratings at or above 2000
		{
			name: "both ratings exactly 2000",
			args: args{
				curUserRating:    2000,
				queuedUserRating: 2000,
			},
			want: true,
		},
		{
			name: "both ratings above 2000 with small difference",
			args: args{
				curUserRating:    2100,
				queuedUserRating: 2050,
			},
			want: true,
		},
		{
			name: "both ratings above 2000 with large difference",
			args: args{
				curUserRating:    2500,
				queuedUserRating: 3000,
			},
			want: true,
		},

		// Case 3: Boundary cases - one rating at 2000, one below
		{
			name: "boundary case: 1999 vs 2000 (just below boundary vs at boundary)",
			args: args{
				curUserRating:    1999,
				queuedUserRating: 2000,
			},
			want: true,
		},
		{
			name: "boundary case: 2000 vs 1999 (at boundary vs just below boundary)",
			args: args{
				curUserRating:    2000,
				queuedUserRating: 1999,
			},
			want: true,
		},
		{
			name: "boundary case: 1800 vs 2000 (exactly at maxAllowedRatingDiff from boundary)",
			args: args{
				curUserRating:    1800,
				queuedUserRating: 2000,
			},
			want: true,
		},
		{
			name: "boundary case: 2000 vs 1800 (boundary vs exactly at maxAllowedRatingDiff below)",
			args: args{
				curUserRating:    2000,
				queuedUserRating: 1800,
			},
			want: true,
		},
		{
			name: "boundary case: 1799 vs 2000 (just beyond maxAllowedRatingDiff from boundary)",
			args: args{
				curUserRating:    1799,
				queuedUserRating: 2000,
			},
			want: false,
		},
		{
			name: "boundary case: 2000 vs 1799 (boundary vs just beyond maxAllowedRatingDiff below)",
			args: args{
				curUserRating:    2000,
				queuedUserRating: 1799,
			},
			want: false,
		},

		// Case 4: Boundary cases - one rating just above 2000, one below
		{
			name: "boundary case: 2001 vs 1801 (just above boundary vs at maxAllowedRatingDiff)",
			args: args{
				curUserRating:    2001,
				queuedUserRating: 1801,
			},
			want: true,
		},
		{
			name: "boundary case: 1801 vs 2001 (at maxAllowedRatingDiff vs just above boundary)",
			args: args{
				curUserRating:    1801,
				queuedUserRating: 2001,
			},
			want: true,
		},
		{
			name: "boundary case: 2001 vs 1800 (just above boundary vs just beyond maxAllowedRatingDiff)",
			args: args{
				curUserRating:    2001,
				queuedUserRating: 1800,
			},
			want: false,
		},
		{
			name: "boundary case: 1800 vs 2001 (just beyond maxAllowedRatingDiff vs just above boundary)",
			args: args{
				curUserRating:    1800,
				queuedUserRating: 2001,
			},
			want: false,
		},

		// Case 5: Edge cases
		{
			name: "edge case: zero rating and high rating",
			args: args{
				curUserRating:    0,
				queuedUserRating: 2500,
			},
			want: false,
		},
		{
			name: "edge case: negative rating and high rating",
			args: args{
				curUserRating:    -100,
				queuedUserRating: 2500,
			},
			want: false,
		},
		{
			name: "edge case: very low rating and very high rating",
			args: args{
				curUserRating:    100,
				queuedUserRating: 3000,
			},
			want: false,
		},
	}

	// Run all test cases
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ValidRatingMatch(tt.args.curUserRating, tt.args.queuedUserRating)
			if got != tt.want {
				t.Errorf("ValidRatingMatch(%d, %d) = %v, want %v",
					tt.args.curUserRating, tt.args.queuedUserRating, got, tt.want)
			}
		})
	}

	// Test all combinations around the boundary
	t.Run("comprehensive boundary test", func(t *testing.T) {
		// Test all combinations of ratings around the 2000 boundary
		for i := 1790; i <= 2010; i += 10 {
			for j := 1790; j <= 2010; j += 10 {
				expected := false

				// Both ratings >= 2000
				if i >= 2000 && j >= 2000 {
					expected = true
				} else {
					// Check if within maxAllowedRatingDiff
					diff := i - j
					if diff < 0 {
						diff = -diff
					}
					expected = diff <= maxAllowedRatingDiff
				}

				got := ValidRatingMatch(i, j)
				if got != expected {
					t.Errorf("ValidRatingMatch(%d, %d) = %v, want %v", i, j, got, expected)
				}
			}
		}
	})
}

func TestUpdatePlayedInLastHour(t *testing.T) {
	// Create a test user ID
	userID := primitive.NewObjectID()

	// Get the current time for testing
	currentTime := time.Now()

	t.Run("when LastOneHourOpponents is nil", func(t *testing.T) {
		// Setup a bot user with nil LastOneHourOpponents
		botUser := &models.User{
			HumanBotConfig: &models.HumanBotConfig{
				LastOneHourOpponents: nil,
			},
		}

		// Call the function
		UpdatePlayedInLastHour(userID, botUser)

		// Verify the map was initialized and contains the user ID with one timestamp
		if botUser.HumanBotConfig.LastOneHourOpponents == nil {
			t.Error("LastOneHourOpponents should be initialized but is still nil")
		}

		times, exists := botUser.HumanBotConfig.LastOneHourOpponents[userID]
		if !exists {
			t.Error("User ID should exist in LastOneHourOpponents map")
		}

		if len(times) != 1 {
			t.Errorf("Expected 1 timestamp, got %d", len(times))
		}
	})

	t.Run("when user hasn't played against bot before", func(t *testing.T) {
		// Setup a bot user with initialized but empty LastOneHourOpponents
		botUser := &models.User{
			HumanBotConfig: &models.HumanBotConfig{
				LastOneHourOpponents: make(map[models.ObjectID][]time.Time),
			},
		}

		// Call the function
		UpdatePlayedInLastHour(userID, botUser)

		// Verify the user ID was added with one timestamp
		times, exists := botUser.HumanBotConfig.LastOneHourOpponents[userID]
		if !exists {
			t.Error("User ID should exist in LastOneHourOpponents map")
		}

		if len(times) != 1 {
			t.Errorf("Expected 1 timestamp, got %d", len(times))
		}
	})

	t.Run("when user has played against bot before with some old entries", func(t *testing.T) {
		// Setup a bot user with existing entries for the user ID
		oldTime := currentTime.Add(-2 * time.Hour)       // 2 hours ago (outside the 1-hour window)
		recentTime := currentTime.Add(-30 * time.Minute) // 30 minutes ago (within the 1-hour window)

		botUser := &models.User{
			HumanBotConfig: &models.HumanBotConfig{
				LastOneHourOpponents: map[models.ObjectID][]time.Time{
					userID: {oldTime, recentTime},
				},
			},
		}

		// Call the function
		UpdatePlayedInLastHour(userID, botUser)

		// Verify that only the recent entry and the new entry are kept
		times, exists := botUser.HumanBotConfig.LastOneHourOpponents[userID]
		if !exists {
			t.Error("User ID should exist in LastOneHourOpponents map")
		}

		if len(times) != 2 {
			t.Errorf("Expected 2 timestamps (recent one and new one), got %d", len(times))
		}

		// Check that the old timestamp is not in the list
		for _, timestamp := range times {
			if timestamp.Equal(oldTime) {
				t.Error("Old timestamp should have been removed")
			}
		}
	})

	t.Run("when user has played against bot before with all recent entries", func(t *testing.T) {
		// Setup a bot user with existing recent entries for the user ID
		recentTime1 := currentTime.Add(-30 * time.Minute) // 30 minutes ago
		recentTime2 := currentTime.Add(-45 * time.Minute) // 45 minutes ago

		botUser := &models.User{
			HumanBotConfig: &models.HumanBotConfig{
				LastOneHourOpponents: map[models.ObjectID][]time.Time{
					userID: {recentTime1, recentTime2},
				},
			},
		}

		// Call the function
		UpdatePlayedInLastHour(userID, botUser)

		// Verify that all entries are kept plus the new one
		times, exists := botUser.HumanBotConfig.LastOneHourOpponents[userID]
		if !exists {
			t.Error("User ID should exist in LastOneHourOpponents map")
		}

		if len(times) != 3 {
			t.Errorf("Expected 3 timestamps (2 recent ones and new one), got %d", len(times))
		}

		// Check that both recent timestamps are still in the list
		found1, found2 := false, false
		for _, timestamp := range times {
			if timestamp.Equal(recentTime1) {
				found1 = true
			}
			if timestamp.Equal(recentTime2) {
				found2 = true
			}
		}

		if !found1 || !found2 {
			t.Error("Recent timestamps should have been kept")
		}
	})
}
