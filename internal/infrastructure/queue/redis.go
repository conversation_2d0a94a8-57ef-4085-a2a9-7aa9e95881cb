package queue

import (
	"context"
	"encoding/json"
	"time"

	"matiksOfficial/matiks-server-go/internal/models"

	"github.com/redis/go-redis/v9"
)

const queueKey = "waiting_players_queue"

type redisQueue struct {
	redisClient *redis.Client
}

func NewRedisQueue(redisClient *redis.Client) Queue {
	return &redisQueue{
		redisClient: redisClient,
	}
}

func (q *redisQueue) AddUser(ctx context.Context, u *models.User, gameConfig models.GameConfigInterface) ([]*models.User, error) {
	q.RemoveUser(ctx, u.ID.Hex())

	waitingUsers, err := q.getAllWaitingUsers(ctx)
	if err != nil {
		return nil, err
	}

	if u.IsShadowBanned != nil && *u.IsShadowBanned {
		return nil, nil
	}

	for i, waitingUser := range waitingUsers {
		if isGameConfigCompatible(gameConfig, waitingUser.GameConfig) {
			matchedUser := waitingUsers[i]
			q.removeUserFromRedis(ctx, matchedUser.User.ID.Hex())
			return []*models.User{matchedUser.User, u}, nil
		}
	}

	newWaitingUser := waitingUser{
		User:         u,
		WaitingSince: time.Now(),
		GameConfig:   gameConfig,
	}

	err = q.addUserToRedis(ctx, &newWaitingUser)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

func (q *redisQueue) RemoveUser(ctx context.Context, userID string) (bool, error) {
	return q.removeUserFromRedis(ctx, userID)
}

func (q *redisQueue) IsUserPresentInWaitingList(ctx context.Context, u *models.User) (bool, error) {
	waitingUsers, err := q.getAllWaitingUsers(ctx)
	if err != nil {
		return false, err
	}

	for _, waitingUser := range waitingUsers {
		if waitingUser.User.ID == u.ID {
			return true, nil
		}
	}
	return false, nil
}

func (q *redisQueue) getAllWaitingUsers(ctx context.Context) ([]*waitingUser, error) {
	result, err := q.redisClient.LRange(ctx, queueKey, 0, -1).Result()
	if err != nil {
		return nil, err
	}

	var waitingUsers []*waitingUser
	for _, userJSON := range result {
		var waitingUser waitingUser
		err := json.Unmarshal([]byte(userJSON), &waitingUser)
		if err != nil {
			return nil, err
		}
		waitingUsers = append(waitingUsers, &waitingUser)
	}

	return waitingUsers, nil
}

func (q *redisQueue) addUserToRedis(ctx context.Context, user *waitingUser) error {
	userJSON, err := json.Marshal(user)
	if err != nil {
		return err
	}

	return q.redisClient.RPush(ctx, queueKey, userJSON).Err()
}

func (q *redisQueue) removeUserFromRedis(ctx context.Context, userID string) (bool, error) {
	waitingUsers, err := q.getAllWaitingUsers(ctx)
	if err != nil {
		return false, err
	}

	for _, waitingUser := range waitingUsers {
		if waitingUser.User.ID.Hex() == userID {
			userJSON, err := json.Marshal(waitingUser)
			if err != nil {
				return false, err
			}
			return true, q.redisClient.LRem(ctx, queueKey, 1, userJSON).Err()
		}
	}

	return false, nil
}
