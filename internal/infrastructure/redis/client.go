package redis

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/pkg/config"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"github.com/redis/go-redis/v9"
	"go.uber.org/fx"
)

type Config struct {
	Addr         string
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
}

// add uberfx start and stop

func NewClient(lc fx.Lifecycle, cfg *config.Config) *redis.Client {
	client := redis.NewClient(&redis.Options{
		Addr: cfg.RedisAddr,

		PoolSize:     100,
		MinIdleConns: 10,

		DialTimeout:  15 * time.Second,
		ReadTimeout:  10 * time.Second,
		WriteTimeout: 10 * time.Second,

		MaxRetries:      3,
		MinRetryBackoff: 8 * time.Millisecond,
		MaxRetryBackoff: 512 * time.Millisecond,
	})
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting redis client")
			if err := client.Ping(ctx).Err(); err != nil {
				zlog.Error(ctx, "Failed to connect to Redis: ", err)
				return err
			}
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down redis client")
			return client.Close()
		},
	})

	return client
}
