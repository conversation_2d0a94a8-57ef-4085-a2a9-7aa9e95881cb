package repository

import (
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
)

// AnnouncementRepository defines the interface for announcement data operations.
type AnnouncementRepository interface {
	CreateAnnouncement(ctx context.Context, announcement *models.Announcement) (primitive.ObjectID, error)
	GetAnnouncementByID(ctx context.Context, id primitive.ObjectID) (*models.Announcement, error)
	UpdateAnnouncement(ctx context.Context, id primitive.ObjectID, updates bson.M) error // Consider using a map[string]interface{} for partial updates
	DeleteAnnouncement(ctx context.Context, id primitive.ObjectID) error

	FindAnnouncements(ctx context.Context, filter map[string]interface{}, limit, offset int64) ([]*models.Announcement, error)
	FindActiveAnnouncements(ctx context.Context, now time.Time) ([]*models.Announcement, error)

	MarkAsRead(ctx context.Context, userID, announcementID primitive.ObjectID) error
	MarkMultipleAsRead(ctx context.Context, userID primitive.ObjectID, announcementIDs []primitive.ObjectID) error

	GetReadAnnouncementIDs(ctx context.Context, userID primitive.ObjectID) (map[primitive.ObjectID]bool, error)

	GetUserReadStatus(ctx context.Context, userID, announcementID primitive.ObjectID) (*models.UserAnnouncementStatus, error)
}

const (
	announcementCollectionName           = "announcements"
	userAnnouncementStatusCollectionName = "user_announcement_statuses"
)

type announcementRepository struct {
	announcementCol           *mongo.Collection
	userAnnouncementStatusCol *mongo.Collection
}

// NewAnnouncementRepository creates a new MongoDB implementation of the AnnouncementRepository.
func NewAnnouncementRepository(lc fx.Lifecycle, db database.Database) AnnouncementRepository {
	r := &announcementRepository{
		announcementCol:           db.Collection(announcementCollectionName),
		userAnnouncementStatusCol: db.Collection(userAnnouncementStatusCollectionName),
	}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Announcement repository initialized and ready.")
			go func() {
				err = r.syncIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for announcement repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down announcement repository.")
			return nil
		},
	})
	return r
}

// syncIndexes creates necessary indexes if they don't exist.
func (r *announcementRepository) syncIndexes(ctx context.Context) error {
	// Index for announcements: type, publishedAt, expiresAt, priority
	announcementIndexes := []mongo.IndexModel{
		{
			Keys: bson.D{{Key: "type", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "publishedAt", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "expiresAt", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "priority", Value: -1}}, // -1 for descending order (higher priority first)
		},
	}

	_, err := r.announcementCol.Indexes().CreateMany(ctx, announcementIndexes)
	if err != nil {
		return err
	}

	// Index for user statuses: (userId, announcementId) unique, userId
	userStatusIndexes := []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "userId", Value: 1}, {Key: "announcementId", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{{Key: "userId", Value: 1}},
		},
	}

	_, err = r.userAnnouncementStatusCol.Indexes().CreateMany(ctx, userStatusIndexes)
	return err
}

func (r *announcementRepository) CreateAnnouncement(ctx context.Context, announcement *models.Announcement) (primitive.ObjectID, error) {
	if announcement.ID.IsZero() {
		announcement.ID = primitive.NewObjectID()
	}
	if announcement.CreatedAt.IsZero() {
		announcement.CreatedAt = time.Now().UTC()
	}
	// Default publishedAt to CreatedAt if not provided
	if announcement.PublishedAt == nil {
		announcement.PublishedAt = &announcement.CreatedAt
	}

	res, err := r.announcementCol.InsertOne(ctx, announcement)
	if err != nil {
		// Handle potential duplicate key errors if needed, though unlikely with ObjectID
		return primitive.NilObjectID, err
	}
	return res.InsertedID.(primitive.ObjectID), nil
}

func (r *announcementRepository) GetAnnouncementByID(ctx context.Context, id primitive.ObjectID) (*models.Announcement, error) {
	var announcement models.Announcement
	err := r.announcementCol.FindOne(ctx, bson.M{"_id": id}).Decode(&announcement)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil // Or return a specific "not found" error
		}
		return nil, err
	}
	return &announcement, nil
}

func (r *announcementRepository) UpdateAnnouncement(ctx context.Context, id primitive.ObjectID, updates bson.M) error {
	updateDoc := bson.M{"$set": updates} // Simplistic approach - assumes 'updates' is already structured correctly for $set
	_, err := r.announcementCol.UpdateOne(ctx, bson.M{"_id": id}, updateDoc)
	return err
}

func (r *announcementRepository) DeleteAnnouncement(ctx context.Context, id primitive.ObjectID) error {
	// Also consider deleting related UserAnnouncementStatus entries or handling dangling references.
	_, err := r.announcementCol.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (r *announcementRepository) FindAnnouncements(ctx context.Context, filter map[string]interface{}, limit, offset int64) ([]*models.Announcement, error) {
	opts := options.Find().
		SetLimit(limit).
		SetSkip(offset).
		SetSort(bson.D{{Key: "priority", Value: -1}, {Key: "createdAt", Value: -1}})

	cursor, err := r.announcementCol.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var announcements []*models.Announcement
	if err = cursor.All(ctx, &announcements); err != nil {
		return nil, err
	}
	return announcements, nil
}

func (r *announcementRepository) FindActiveAnnouncements(ctx context.Context, now time.Time) ([]*models.Announcement, error) {
	filter := bson.M{
		"$and": []bson.M{
			{"$or": []bson.M{
				{"publishedAt": nil},
				{"publishedAt": bson.M{"$lte": now}},
			}},
			{"$or": []bson.M{
				{"expiresAt": nil},
				{"expiresAt": bson.M{"$gt": now}},
			}},
		},
	}

	opts := options.Find().
		SetSort(bson.D{{Key: "priority", Value: -1}, {Key: "createdAt", Value: -1}})

	cursor, err := r.announcementCol.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var announcements []*models.Announcement
	if err = cursor.All(ctx, &announcements); err != nil {
		return nil, err
	}
	return announcements, nil
}

func (r *announcementRepository) MarkAsRead(ctx context.Context, userID, announcementID primitive.ObjectID) error {
	filter := bson.M{
		"userId":         userID,
		"announcementId": announcementID,
	}
	update := bson.M{
		"$setOnInsert": bson.M{
			"_id":            primitive.NewObjectID(),
			"userId":         userID,
			"announcementId": announcementID,
			"readAt":         time.Now().UTC(),
		},
	}
	opts := options.Update().SetUpsert(true)
	_, err := r.userAnnouncementStatusCol.UpdateOne(ctx, filter, update, opts)
	return err
}

func (r *announcementRepository) MarkMultipleAsRead(ctx context.Context, userID primitive.ObjectID, announcementIDs []primitive.ObjectID) error {
	now := time.Now().UTC()
	var operations []mongo.WriteModel

	for _, announcementID := range announcementIDs {
		operation := mongo.NewUpdateOneModel().
			SetFilter(bson.M{
				"userId":         userID,
				"announcementId": announcementID,
			}).
			SetUpdate(bson.M{
				"$setOnInsert": bson.M{
					"_id":            primitive.NewObjectID(),
					"userId":         userID,
					"announcementId": announcementID,
					"readAt":         now,
				},
			}).
			SetUpsert(true)

		operations = append(operations, operation)
	}

	if len(operations) == 0 {
		return nil
	}

	_, err := r.userAnnouncementStatusCol.BulkWrite(ctx, operations)
	return err
}

func (r *announcementRepository) GetReadAnnouncementIDs(ctx context.Context, userID primitive.ObjectID) (map[primitive.ObjectID]bool, error) {
	cursor, err := r.userAnnouncementStatusCol.Find(ctx, bson.M{"userId": userID})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	readIDs := make(map[primitive.ObjectID]bool)
	var status models.UserAnnouncementStatus
	for cursor.Next(ctx) {
		if err := cursor.Decode(&status); err != nil {
			return nil, err
		}
		readIDs[status.AnnouncementID] = true
	}

	if err := cursor.Err(); err != nil {
		return nil, err
	}

	return readIDs, nil
}

func (r *announcementRepository) GetUserReadStatus(ctx context.Context, userID, announcementID primitive.ObjectID) (*models.UserAnnouncementStatus, error) {
	var status models.UserAnnouncementStatus
	err := r.userAnnouncementStatusCol.FindOne(ctx, bson.M{
		"userId":         userID,
		"announcementId": announcementID,
	}).Decode(&status)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return &status, nil
}
