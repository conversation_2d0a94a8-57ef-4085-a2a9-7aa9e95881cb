package repository

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

type BotDetectionRepository interface {
	Create(ctx context.Context, detection *models.BotDetection) error
	FindByUserID(ctx context.Context, userID primitive.ObjectID) ([]*models.BotDetection, error)
	FindByID(ctx context.Context, id primitive.ObjectID) (*models.BotDetection, error)
	UpdateStatus(ctx context.Context, id primitive.ObjectID, status models.BotDetectionStatus) error
	FindRecentByUserID(ctx context.Context, userID primitive.ObjectID, since time.Time) ([]*models.BotDetection, error)
	CountByUserID(ctx context.Context, userID primitive.ObjectID) (int64, error)
}

type mongoBotDetectionRepository struct {
	collection *mongo.Collection
}

func NewBotDetectionRepository(lc fx.Lifecycle, db database.Database) BotDetectionRepository {
	collection := db.Collection("bot_detections")
	botDetectionRepo := mongoBotDetectionRepository{collection: collection}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Bot detection repository initialized and ready.")
			go func() {
				err = botDetectionRepo.syncIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for bot detection repository", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down bot detection repository.")
			return nil
		},
	})

	return &botDetectionRepo
}

func (r *mongoBotDetectionRepository) syncIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{{Key: "userId", Value: 1}}},
		{Keys: bson.D{{Key: "createdAt", Value: -1}}},
		{Keys: bson.D{{Key: "status", Value: 1}}},
	})
}

func (r *mongoBotDetectionRepository) Create(ctx context.Context, detection *models.BotDetection) error {
	if detection.ID.IsZero() {
		detection.ID = primitive.NewObjectID()
	}
	detection.CreatedAt = time.Now()

	_, err := r.collection.InsertOne(ctx, detection)
	if err != nil {
		zlog.Error(ctx, "Failed to insert bot detection", err,
			zap.String("userId", detection.UserID.Hex()))
		return err
	}

	return nil
}

func (r *mongoBotDetectionRepository) FindByUserID(ctx context.Context, userID primitive.ObjectID) ([]*models.BotDetection, error) {
	var detections []*models.BotDetection

	opts := options.Find().SetSort(bson.D{{Key: "createdAt", Value: -1}})
	cursor, err := r.collection.Find(ctx, bson.M{"userId": userID}, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if err = cursor.All(ctx, &detections); err != nil {
		return nil, err
	}

	return detections, nil
}

func (r *mongoBotDetectionRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.BotDetection, error) {
	var detection models.BotDetection

	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&detection)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, err
	}

	return &detection, nil
}

func (r *mongoBotDetectionRepository) UpdateStatus(ctx context.Context, id primitive.ObjectID, status models.BotDetectionStatus) error {
	now := time.Now()
	_, err := r.collection.UpdateOne(
		ctx,
		bson.M{"_id": id},
		bson.M{
			"$set": bson.M{
				"status":    status,
				"updatedAt": now,
			},
		},
	)
	return err
}

func (r *mongoBotDetectionRepository) FindRecentByUserID(ctx context.Context, userID primitive.ObjectID, since time.Time) ([]*models.BotDetection, error) {
	var detections []*models.BotDetection

	opts := options.Find().SetSort(bson.D{{Key: "createdAt", Value: -1}})
	cursor, err := r.collection.Find(ctx, bson.M{
		"userId":    userID,
		"createdAt": bson.M{"$gte": since},
	}, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if err = cursor.All(ctx, &detections); err != nil {
		return nil, err
	}

	return detections, nil
}

func (r *mongoBotDetectionRepository) CountByUserID(ctx context.Context, userID primitive.ObjectID) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{"userId": userID})
	if err != nil {
		return 0, err
	}
	return count, nil
}
