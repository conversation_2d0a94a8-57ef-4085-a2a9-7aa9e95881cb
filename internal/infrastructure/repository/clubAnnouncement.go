package repository

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/fx"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
)

type ClubAnnouncementRepository interface {
	FindAnnouncementByID(ctx context.Context, id primitive.ObjectID) (*models.ClubAnnouncement, error)
	CreateAnnouncement(ctx context.Context, event *models.ClubAnnouncement) error
	DeleteAnnouncement(ctx context.Context, id primitive.ObjectID) error
	ListAnnouncements(ctx context.Context, clubID primitive.ObjectID, opts *options.FindOptions) ([]*models.ClubAnnouncement, error)
	FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.ClubAnnouncement, error)
	Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.ClubAnnouncement, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
}

type clubAnnouncementRepository struct {
	announcements *mongo.Collection
}

func NewClubAnnouncementRepository(lc fx.Lifecycle, db database.Database) ClubAnnouncementRepository {
	r := &clubAnnouncementRepository{
		announcements: db.Collection("clubAnnouncements"),
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Club announcement repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down club announcement repository.")
			return nil
		},
	})

	return r
}

func (r *clubAnnouncementRepository) FindAnnouncementByID(ctx context.Context, id primitive.ObjectID) (*models.ClubAnnouncement, error) {
	var announcement models.ClubAnnouncement
	err := r.announcements.FindOne(ctx, bson.M{"_id": id}).Decode(&announcement)
	if err != nil {
		return nil, err
	}
	return &announcement, nil
}

func (r *clubAnnouncementRepository) CreateAnnouncement(ctx context.Context, announcement *models.ClubAnnouncement) error {
	if announcement == nil {
		return fmt.Errorf("announcement cannot be nil")
	}
	announcement.CreatedAt = time.Now()
	_, err := r.announcements.InsertOne(ctx, announcement)
	return err
}

func (r *clubAnnouncementRepository) DeleteAnnouncement(ctx context.Context, id primitive.ObjectID) error {
	_, err := r.announcements.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (r *clubAnnouncementRepository) ListAnnouncements(ctx context.Context, clubID primitive.ObjectID, opts *options.FindOptions) ([]*models.ClubAnnouncement, error) {
	if clubID == primitive.NilObjectID {
		return nil, fmt.Errorf("club ID cannot be empty")
	}
	matchStage := bson.D{{Key: "$match", Value: bson.M{"clubId": clubID}}}

	var pipelineStages []bson.D
	pipelineStages = append(pipelineStages, matchStage)

	if opts != nil {
		if opts.Sort != nil {
			pipelineStages = append(pipelineStages, bson.D{{Key: "$sort", Value: opts.Sort}})
		}
		if opts.Limit != nil {
			pipelineStages = append(pipelineStages, bson.D{{Key: "$limit", Value: *opts.Limit}})
		}
		if opts.Skip != nil {
			pipelineStages = append(pipelineStages, bson.D{{Key: "$skip", Value: *opts.Skip}})
		}
	}

	lookupStage := bson.D{
		{Key: "$lookup", Value: bson.D{
			{Key: "from", Value: "users"},
			{Key: "localField", Value: "createdBy"},
			{Key: "foreignField", Value: "_id"},
			{Key: "as", Value: "userDetails"},
		}},
	}
	pipelineStages = append(pipelineStages, lookupStage)

	unwindStage := bson.D{{Key: "$unwind", Value: bson.D{{Key: "path", Value: "$userDetails"}, {Key: "preserveNullAndEmptyArrays", Value: true}}}}
	pipelineStages = append(pipelineStages, unwindStage)

	projectStage := bson.D{
		{Key: "$project", Value: bson.D{
			{Key: "_id", Value: 1},
			{Key: "clubId", Value: 1},
			{Key: "title", Value: 1},
			{Key: "content", Value: 1},
			{Key: "createdAt", Value: 1},
			{Key: "createdBy", Value: 1},
			{Key: "creatorInfo", Value: bson.D{
				{Key: "username", Value: "$userDetails.username"},
				{Key: "profileImageUrl", Value: "$userDetails.profileImageUrl"},
			}},
		}},
	}
	pipelineStages = append(pipelineStages, projectStage)

	cursor, err := r.announcements.Aggregate(ctx, pipelineStages)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var announcements []*models.ClubAnnouncement
	if err = cursor.All(ctx, &announcements); err != nil {
		return nil, err
	}

	return announcements, nil
}

func (r *clubAnnouncementRepository) FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.ClubAnnouncement, error) {
	var announcement models.ClubAnnouncement
	err := r.announcements.FindOne(ctx, filter, opts...).Decode(&announcement)
	if err != nil {
		return nil, err
	}
	return &announcement, nil
}

func (r *clubAnnouncementRepository) Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.ClubAnnouncement, error) {
	cursor, err := r.announcements.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var announcements []*models.ClubAnnouncement
	if err = cursor.All(ctx, &announcements); err != nil {
		return nil, err
	}
	return announcements, nil
}

func (r *clubAnnouncementRepository) Count(ctx context.Context, filter bson.M) (int64, error) {
	return r.announcements.CountDocuments(ctx, filter)
}
