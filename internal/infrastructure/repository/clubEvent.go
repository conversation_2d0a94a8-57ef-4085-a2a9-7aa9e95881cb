package repository

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/fx"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
)

type ClubEventRepository interface {
	FindEventByID(ctx context.Context, id primitive.ObjectID) (*models.ClubEvent, error)
	CreateEvent(ctx context.Context, event *models.ClubEvent) error
	UpdateEvent(ctx context.Context, id primitive.ObjectID, update bson.M) error
	DeleteEvent(ctx context.Context, id primitive.ObjectID) error
	ListEvents(ctx context.Context, clubID primitive.ObjectID, opts *options.FindOptions) ([]*models.ClubEvent, error)
	Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.ClubEvent, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	CountEvents(ctx context.Context, clubID primitive.ObjectID) (int64, error)
	IncrementEventParticipationCount(ctx context.Context, clubID, clubEventPlayId primitive.ObjectID) error
	DecrementEventParticipationCount(ctx context.Context, clubID, clubEventPlayId primitive.ObjectID) error
}

type clubEventRepository struct {
	events *mongo.Collection
	clubs  ClubRepository
}

func NewClubEventRepository(lc fx.Lifecycle, db database.Database, clubsRepo ClubRepository) ClubEventRepository {
	repo := &clubEventRepository{
		events: db.Collection("clubEvents"),
		clubs:  clubsRepo,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Club event repository initialized and ready.")
			go func() {
				err = repo.syncIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error in Indexing ", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down club event repository.")
			return nil
		},
	})

	return repo
}

func (r *clubEventRepository) syncIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.events, []mongo.IndexModel{
		{Keys: bson.D{{Key: "clubId", Value: 1}}},
		{Keys: bson.D{{Key: "startTime", Value: 1}}},
	})
}

func (r *clubEventRepository) FindEventByID(ctx context.Context, id primitive.ObjectID) (*models.ClubEvent, error) {
	var event models.ClubEvent
	err := r.events.FindOne(ctx, bson.M{"_id": id}).Decode(&event)
	if err != nil {
		return nil, err
	}
	return &event, nil
}

func (r *clubEventRepository) CreateEvent(ctx context.Context, event *models.ClubEvent) error {
	event.CreatedAt = time.Now()
	_, err := r.events.InsertOne(ctx, event)
	if err != nil {
		return err
	}

	err = r.clubs.IncrementEventCount(ctx, event.ClubID)

	return err
}

func (r *clubEventRepository) UpdateEvent(ctx context.Context, id primitive.ObjectID, update bson.M) error {
	_, err := r.events.UpdateOne(
		ctx,
		bson.M{"_id": id},
		bson.M{"$set": update},
	)
	return err
}

func (r *clubEventRepository) DeleteEvent(ctx context.Context, id primitive.ObjectID) error {
	_, err := r.events.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return err
	}

	return nil
}

func (r *clubEventRepository) IncrementEventParticipationCount(ctx context.Context, clubID, clubEventPlayId primitive.ObjectID) error {
	_, err := r.events.UpdateOne(
		ctx,
		bson.M{
			"clubEventPlayId": clubEventPlayId,
			"clubId":          clubID,
		},
		bson.M{"$inc": bson.M{"participationCount": 1}},
	)
	return err
}

func (r *clubEventRepository) DecrementEventParticipationCount(ctx context.Context, clubID, clubEventPlayId primitive.ObjectID) error {
	_, err := r.events.UpdateOne(
		ctx,
		bson.M{
			"clubEventPlayId": clubEventPlayId,
			"clubId":          clubID,
		},
		bson.M{"$inc": bson.M{"participationCount": -1}},
	)
	return err
}

func (r *clubEventRepository) ListEvents(ctx context.Context, clubID primitive.ObjectID, opts *options.FindOptions) ([]*models.ClubEvent, error) {
	if clubID == primitive.NilObjectID {
		return nil, fmt.Errorf("club ID cannot be empty")
	}
	cursor, err := r.events.Find(ctx, bson.M{"clubId": clubID}, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var events []*models.ClubEvent
	if err = cursor.All(ctx, &events); err != nil {
		return nil, err
	}
	return events, nil
}

func (r *clubEventRepository) Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.ClubEvent, error) {
	cursor, err := r.events.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var events []*models.ClubEvent
	if err = cursor.All(ctx, &events); err != nil {
		return nil, err
	}
	return events, nil
}

func (r *clubEventRepository) CountEvents(ctx context.Context, clubID primitive.ObjectID) (int64, error) {
	if clubID == primitive.NilObjectID {
		return 0, fmt.Errorf("club ID cannot be empty")
	}
	return r.events.CountDocuments(ctx, bson.M{"clubId": clubID})
}

func (r *clubEventRepository) Count(ctx context.Context, filter bson.M) (int64, error) {
	return r.events.CountDocuments(ctx, filter)
}
