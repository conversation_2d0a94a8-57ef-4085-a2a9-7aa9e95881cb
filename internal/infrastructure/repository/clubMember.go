package repository

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/fx"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
)

type ClubMemberRepository interface {
	AddMember(ctx context.Context, member *models.ClubMember) error
	GetMemberInfo(ctx context.Context, memberId, clubId primitive.ObjectID) (*models.ClubMember, error)
	RemoveMember(ctx context.Context, clubID, userID primitive.ObjectID) error
	UpdateMemberRole(ctx context.Context, clubID, userID primitive.ObjectID, role string) error
	GetClubLeaderboard(ctx context.Context, clubID primitive.ObjectID, page, pageSize int) (*models.ClubLeaderboard, error)
	ListMembers(ctx context.Context, clubID primitive.ObjectID, opts *options.FindOptions) ([]*models.ClubMember, error)
	CountMembers(ctx context.Context, clubID primitive.ObjectID) (int64, error)
	RejectMemberJoinRequest(ctx context.Context, clubId, userId primitive.ObjectID) error
	AcceptMemberJoinRequest(ctx context.Context, clubId, userId primitive.ObjectID) error
	Aggregate(ctx context.Context, pipeline mongo.Pipeline) ([]*models.ClubMember, error)
	AggregateProjected(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error)
}

type clubMemberRepository struct {
	members *mongo.Collection
	clubs   ClubRepository
}

func NewClubMemberRepository(lc fx.Lifecycle, db database.Database, clubRepo ClubRepository) ClubMemberRepository {
	repo := &clubMemberRepository{members: db.Collection("clubMembers"), clubs: clubRepo}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Club member repository initialized and ready.")
			go func() {
				err = repo.syncIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for club member repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down club member repository.")
			return nil
		},
	})

	return repo
}

func (r *clubMemberRepository) syncIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.members, []mongo.IndexModel{
		{Keys: bson.D{
			{Key: "clubId", Value: 1},
			{Key: "userId", Value: 1},
		}, Options: options.Index().SetUnique(true)},
		{Keys: bson.D{{Key: "role", Value: 1}}},
	})
}

func (r *clubMemberRepository) GetClubLeaderboard(ctx context.Context, clubID primitive.ObjectID, page, pageSize int) (*models.ClubLeaderboard, error) {
	if clubID == primitive.NilObjectID {
		return nil, fmt.Errorf("club ID cannot be empty")
	}
	skip := (page - 1) * pageSize
	pipeline := mongo.Pipeline{
		{
			{Key: "$match", Value: bson.M{
				"clubId":               clubID,
				"clubMembershipStatus": models.ClubMembershipStatusAccepted,
			}},
		},
		{
			{Key: "$lookup", Value: bson.M{
				"from":         "users",
				"localField":   "userId",
				"foreignField": "_id",
				"as":           "userDetails",
			}},
		},
		{
			{Key: "$unwind", Value: "$userDetails"},
		},
		{
			{Key: "$project", Value: bson.M{
				"user": bson.M{
					"_id":             "$userDetails._id",
					"username":        "$userDetails.username",
					"profileImageUrl": "$userDetails.profileImageUrl",
					"rating":          "$userDetails.rating",
				},
			}},
		},
		{
			{Key: "$sort", Value: bson.M{
				"user.rating": -1,
			}},
		},
		{
			{Key: "$group", Value: bson.M{
				"_id":     nil,
				"entries": bson.M{"$push": "$$ROOT"},
				"count":   bson.M{"$sum": 1},
			}},
		},

		{
			{Key: "$project", Value: bson.M{
				"entries": bson.M{
					"$map": bson.M{
						"input": "$entries",
						"as":    "entry",
						"in": bson.M{
							"user": "$$entry.user",
							"rank": bson.M{"$add": bson.A{bson.M{"$indexOfArray": bson.A{"$entries", "$$entry"}}, 1}},
						},
					},
				},
				"count": 1,
			}},
		},
		{
			{Key: "$project", Value: bson.M{
				"totalResults": "$count",
				"results":      bson.M{"$slice": bson.A{"$entries", skip, pageSize}},
			}},
		},
	}

	cursor, err := r.members.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var aggregationResult []bson.M
	if err = cursor.All(ctx, &aggregationResult); err != nil {
		return nil, err
	}

	var leaderboardEntries []*models.ClubLeaderboardEntry
	var totalResults int

	if len(aggregationResult) > 0 {
		if resultsArray, ok := aggregationResult[0]["results"].(bson.A); ok {
			for _, result := range resultsArray {
				if resultMap, ok := result.(bson.M); ok {
					userMap := resultMap["user"].(bson.M)
					leaderboardEntries = append(leaderboardEntries, &models.ClubLeaderboardEntry{
						User: utils.ExtractUserPublicDetails(userMap),
						Rank: int(resultMap["rank"].(int32)),
					})
				}
			}
		}

		if total, ok := aggregationResult[0]["totalResults"].(int32); ok {
			totalResults = int(total)
		}
	}

	hasMore := (page * pageSize) < totalResults

	return &models.ClubLeaderboard{
		Results:      leaderboardEntries,
		PageNumber:   page,
		PageSize:     pageSize,
		HasMore:      utils.AllocPtr(hasMore),
		TotalResults: utils.AllocPtr(totalResults),
	}, nil
}

func (r *clubMemberRepository) GetMemberInfo(ctx context.Context, memberId, clubId primitive.ObjectID) (*models.ClubMember, error) {
	if clubId == primitive.NilObjectID || memberId == primitive.NilObjectID {
		return nil, fmt.Errorf("club ID and member ID cannot be empty")
	}
	var memberInfo *models.ClubMember

	err := r.members.FindOne(ctx, bson.M{
		"clubId": clubId,
		"userId": memberId,
	}).Decode(&memberInfo)
	if err != nil {
		return nil, err
	}

	return memberInfo, nil
}

func (r *clubMemberRepository) AddMember(ctx context.Context, member *models.ClubMember) error {
	if member == nil {
		return fmt.Errorf("member cannot be nil")
	}
	member.JoinedAt = time.Now()
	_, err := r.members.InsertOne(ctx, member)
	if err != nil {
		return err
	}

	if member.ClubMembershipStatus == models.ClubMembershipStatusAccepted {
		err = r.clubs.IncrementMemberCount(ctx, member.ClubID)
	}
	return err
}

func (r *clubMemberRepository) AcceptMemberJoinRequest(ctx context.Context, clubId, userId primitive.ObjectID) error {
	_, err := r.members.UpdateOne(ctx, bson.M{
		"clubId": clubId,
		"userId": userId,
	}, bson.M{
		"$set": bson.M{
			"clubMembershipStatus": models.ClubMembershipStatusAccepted,
		},
	})

	return err
}

func (r *clubMemberRepository) RejectMemberJoinRequest(ctx context.Context, clubId, userId primitive.ObjectID) error {
	_, err := r.members.DeleteOne(ctx, bson.M{
		"clubId": clubId,
		"userId": userId,
	})

	return err
}

func (r *clubMemberRepository) RemoveMember(ctx context.Context, clubID, userID primitive.ObjectID) error {
	_, err := r.members.DeleteOne(ctx, bson.M{
		"clubId": clubID,
		"userId": userID,
	})
	if err != nil {
		return err
	}

	err = r.clubs.DecrementMemberCount(ctx, clubID)
	return err
}

func (r *clubMemberRepository) UpdateMemberRole(ctx context.Context, clubID, userID primitive.ObjectID, role string) error {
	_, err := r.members.UpdateOne(
		ctx,
		bson.M{
			"clubId": clubID,
			"userId": userID,
		},
		bson.M{
			"$set": bson.M{"role": role},
		},
	)
	return err
}

func (r *clubMemberRepository) ListMembers(ctx context.Context, clubID primitive.ObjectID, opts *options.FindOptions) ([]*models.ClubMember, error) {
	cursor, err := r.members.Find(ctx, bson.M{"clubId": clubID}, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var members []*models.ClubMember
	if err = cursor.All(ctx, &members); err != nil {
		return nil, err
	}
	return members, nil
}

func (r *clubMemberRepository) CountMembers(ctx context.Context, clubID primitive.ObjectID) (int64, error) {
	return r.members.CountDocuments(ctx, bson.M{"clubId": clubID})
}

func (r *clubMemberRepository) Aggregate(ctx context.Context, pipeline mongo.Pipeline) ([]*models.ClubMember, error) {
	cursor, err := r.members.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var members []*models.ClubMember
	if err = cursor.All(ctx, &members); err != nil {
		return nil, err
	}
	return members, nil
}

func (r *clubMemberRepository) AggregateProjected(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error) {
	cursor, err := r.members.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	return cursor, nil
}
