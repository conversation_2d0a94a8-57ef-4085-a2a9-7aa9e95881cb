package repository

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/fx"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const DefaultPageSize = 20

type ClubRepository interface {
	CreateClub(ctx context.Context, club *models.Club) error
	FindClubByID(ctx context.Context, id primitive.ObjectID) (*models.Club, error)
	UpdateClub(ctx context.Context, id primitive.ObjectID, update bson.M) error
	DeleteClub(ctx context.Context, id primitive.ObjectID) error
	ListClubs(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.Club, error)
	CountClubs(ctx context.Context, filter bson.M) (int64, error)
	IncrementEventCount(ctx context.Context, clubID primitive.ObjectID) error
	DecrementEventCount(ctx context.Context, clubID primitive.ObjectID) error
	IncrementMemberCount(ctx context.Context, clubID primitive.ObjectID) error
	DecrementMemberCount(ctx context.Context, clubID primitive.ObjectID) error
}

type clubRepository struct {
	clubs *mongo.Collection
}

func NewClubRepository(lc fx.Lifecycle, db database.Database) ClubRepository {
	repo := &clubRepository{
		clubs: db.Collection("clubs"),
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Club repository initialized and ready.")
			go func() {
				err = repo.syncIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for club repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down club repository.")
			return nil
		},
	})

	return repo
}

func (r *clubRepository) syncIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.clubs, []mongo.IndexModel{
		{Keys: bson.D{{Key: "name", Value: 1}}},
		{Keys: bson.D{{Key: "visibility", Value: 1}}},
	})
}

func (r *clubRepository) CreateClub(ctx context.Context, club *models.Club) error {
	if club == nil {
		return fmt.Errorf("club cannot be nil")
	}
	club.CreatedAt = time.Now()
	club.UpdatedAt = time.Now()
	_, err := r.clubs.InsertOne(ctx, club)
	return err
}

func (r *clubRepository) FindClubByID(ctx context.Context, id primitive.ObjectID) (*models.Club, error) {
	var club models.Club
	err := r.clubs.FindOne(ctx, bson.M{"_id": id}).Decode(&club)
	if err != nil {
		return nil, err
	}
	return &club, nil
}

func (r *clubRepository) UpdateClub(ctx context.Context, id primitive.ObjectID, update bson.M) error {
	if _, ok := update["$set"]; !ok {
		update["$set"] = bson.M{}
	}
	update["$set"].(bson.M)["updatedAt"] = time.Now()
	_, err := r.clubs.UpdateOne(ctx, bson.M{"_id": id}, update)
	return err
}

func (r *clubRepository) DeleteClub(ctx context.Context, id primitive.ObjectID) error {
	// TODO: @k123ritesh add club status instead of deleting ,
	_, err := r.clubs.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (r *clubRepository) ListClubs(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.Club, error) {
	cursor, err := r.clubs.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var clubs []*models.Club
	if err = cursor.All(ctx, &clubs); err != nil {
		return nil, err
	}
	return clubs, nil
}

func (r *clubRepository) IncrementMemberCount(ctx context.Context, clubID primitive.ObjectID) error {
	_, err := r.clubs.UpdateOne(
		ctx,
		bson.M{"_id": clubID},
		bson.M{"$inc": bson.M{"membersCount": 1}},
	)
	return err
}

func (r *clubRepository) DecrementMemberCount(ctx context.Context, clubID primitive.ObjectID) error {
	_, err := r.clubs.UpdateOne(ctx,
		bson.M{"_id": clubID},
		bson.M{"$inc": bson.M{"membersCount": -1}},
	)
	return err
}

func (r *clubRepository) CountClubs(ctx context.Context, filter bson.M) (int64, error) {
	return r.clubs.CountDocuments(ctx, filter)
}

func (r *clubRepository) SearchClubs(ctx context.Context, query string, opts *options.FindOptions) ([]*models.Club, error) {
	filter := bson.M{
		"$or": []bson.M{
			{"name": bson.M{"$regex": query, "$options": "i"}},
			{"description": bson.M{"$regex": query, "$options": "i"}},
		},
	}
	return r.ListClubs(ctx, filter, opts)
}

func (r *clubRepository) IncrementEventCount(ctx context.Context, clubID primitive.ObjectID) error {
	_, err := r.clubs.UpdateOne(
		ctx,
		bson.M{"_id": clubID},
		bson.M{"$inc": bson.M{"clubEventsCount": 1}},
	)
	return err
}

func (r *clubRepository) DecrementEventCount(ctx context.Context, clubID primitive.ObjectID) error {
	_, err := r.clubs.UpdateOne(ctx,
		bson.M{"_id": clubID},
		bson.M{"$inc": bson.M{"clubEventsCount": -1}},
	)
	return err
}
