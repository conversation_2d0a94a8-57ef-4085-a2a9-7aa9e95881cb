package repository

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

type ContestRepository interface {
	Create(ctx context.Context, contest *models.Contest) error
	FindByID(ctx context.Context, id primitive.ObjectID) (*models.Contest, error)
	Update(ctx context.Context, filter, update bson.M) error
	Count(ctx context.Context, filter bson.M) (int64, error)
	IncrementRegistrationCount(ctx context.Context, contestID primitive.ObjectID) error
	DecrementRegistrationCount(ctx context.Context, contestID primitive.ObjectID) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.Contest, error)
	GetLatestWeeklyContest(background context.Context) (*models.Contest, error)
	Aggregate(ctx context.Context, pipeline mongo.Pipeline) ([]*models.Contest, error)
	AggregateProjected(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error)
}

type contestRepository struct {
	collection *mongo.Collection
}

func (r *contestRepository) syncIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{
			{Key: "startTime", Value: 1},
			{Key: "endTime", Value: 1},
		}},
		{Keys: bson.D{
			{Key: "registrationStartTime", Value: 1},
			{Key: "registrationEndTime", Value: 1},
		}},
		{Keys: bson.D{{Key: "status", Value: 1}}},
	})
}

func NewContestRepository(lc fx.Lifecycle, db database.Database) ContestRepository {
	r := &contestRepository{collection: db.Collection("contests")}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Contest repository initialized and ready.")
			go func() {
				err = r.syncIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for contest repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down contest repository.")
			return nil
		},
	})
	return r
}

func (r *contestRepository) Collection() *mongo.Collection {
	return r.collection
}

func (r *contestRepository) Create(ctx context.Context, contest *models.Contest) error {
	_, err := r.collection.InsertOne(ctx, contest)
	return err
}

func (r *contestRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.Contest, error) {
	var contest models.Contest
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&contest)
	if err != nil {
		return nil, err
	}

	return &contest, nil
}

func (r *contestRepository) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, filter)
	return count, err
}

func (r *contestRepository) IncrementRegistrationCount(ctx context.Context, contestID primitive.ObjectID) error {
	_, err := r.collection.UpdateOne(
		ctx,
		bson.M{"_id": contestID},
		bson.M{"$inc": bson.M{"registrationCount": 1}},
	)
	return err
}

func (r *contestRepository) DecrementRegistrationCount(ctx context.Context, contestID primitive.ObjectID) error {
	_, err := r.collection.UpdateOne(
		ctx,
		bson.M{"_id": contestID},
		bson.M{"$inc": bson.M{"registrationCount": -1}},
	)
	return err
}

func (r *contestRepository) Update(ctx context.Context, filter, update bson.M) error {
	opts := options.Update().SetUpsert(true)
	_, err := r.collection.UpdateOne(ctx, filter, update, opts)
	return err
}

func (r *contestRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	_, err := r.collection.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (r *contestRepository) List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.Contest, error) {
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var contests []*models.Contest
	if err = cursor.All(ctx, &contests); err != nil {
		return nil, err
	}

	return contests, nil
}

func (r *contestRepository) GetLatestWeeklyContest(background context.Context) (*models.Contest, error) {
	var contest models.Contest
	err := r.collection.FindOne(background, bson.M{"name": bson.M{"$regex": "Matiks Weekly Contest #"}}, options.FindOne().SetSort(bson.M{"startTime": -1})).Decode(&contest)
	if err != nil {
		return nil, err
	}

	return &contest, nil
}

func (r *contestRepository) Aggregate(ctx context.Context, pipeline mongo.Pipeline) ([]*models.Contest, error) {
	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var contests []*models.Contest
	if err = cursor.All(ctx, &contests); err != nil {
		return nil, err
	}

	return contests, nil
}

func (r *contestRepository) AggregateProjected(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error) {
	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}

	return cursor, nil
}
