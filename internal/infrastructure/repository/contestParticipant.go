package repository

import (
	"context"
	"errors"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

type ContestParticipantRepository interface {
	Create(ctx context.Context, participant *models.ContestParticipant) error
	GetByID(ctx context.Context, id primitive.ObjectID) (*models.ContestParticipant, error)
	GetByUserID(ctx context.Context, userID primitive.ObjectID) ([]*models.ContestParticipant, error)
	UpdateOne(ctx context.Context, filter, update bson.M) error
	// Update(ctx context.Context, participant *models.ContestParticipant) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	GetByContestAndUser(ctx context.Context, contestID, userID primitive.ObjectID) (*models.ContestParticipant, error)
	DeleteByContestAndUser(ctx context.Context, contestID, userID primitive.ObjectID) (bool, error)
	GetTopParticipantsByContest(ctx context.Context, contestID primitive.ObjectID, limit int64) ([]*models.ContestParticipant, error)
	AddSubmission(ctx context.Context, participantID primitive.ObjectID, submission models.Submission) error
	Count(ctx context.Context, filter bson.M) (int64, error)
	GetParticipantByUserAndContest(ctx context.Context, contestId []*models.ObjectID, userId primitive.ObjectID) ([]*models.ContestParticipant, error)
	Aggregate(ctx context.Context, pipeline mongo.Pipeline) ([]*models.ContestParticipant, error)
	AggregateProjected(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error)
}

type contestParticipantRepository struct {
	collection *mongo.Collection
}

func (r *contestParticipantRepository) syncIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{
			{Key: "contestId", Value: 1},
			{Key: "userId", Value: 1},
		}, Options: options.Index().SetUnique(true)},
		{Keys: bson.D{
			{Key: "contestId", Value: 1},
			{Key: "score", Value: -1},
			{Key: "lastSubmissionTime", Value: 1},
		}},
	})
}

func NewContestParticipantRepository(lc fx.Lifecycle, db database.Database) ContestParticipantRepository {
	r := &contestParticipantRepository{collection: db.Collection("contestparticipants")}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Contest participant repository initialized and ready.")
			go func() {
				err = r.syncIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for contest participant repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down contest participant repository.")
			return nil
		},
	})
	return r
}

func (r *contestParticipantRepository) Collection() *mongo.Collection {
	return r.collection
}

func (r *contestParticipantRepository) Count(ctx context.Context, filter bson.M) (int64, error) {
	return r.collection.CountDocuments(ctx, filter)
}

func (r *contestParticipantRepository) Create(ctx context.Context, participant *models.ContestParticipant) error {
	participant.CreatedAt = time.Now()
	participant.UpdatedAt = time.Now()

	result, err := r.collection.InsertOne(ctx, participant)
	if err != nil {
		return err
	}

	participant.ID = result.InsertedID.(primitive.ObjectID)
	return nil
}

func (r *contestParticipantRepository) GetByID(ctx context.Context, id primitive.ObjectID) (*models.ContestParticipant, error) {
	var participant models.ContestParticipant
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&participant)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &participant, nil
}

func (r *contestParticipantRepository) GetByUserID(ctx context.Context, userID primitive.ObjectID) ([]*models.ContestParticipant, error) {
	filter := bson.M{"userId": userID}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var participants []*models.ContestParticipant
	if err := cursor.All(ctx, &participants); err != nil {
		return nil, err
	}

	return participants, nil
}

func (r *contestParticipantRepository) UpdateOne(ctx context.Context, filter, update bson.M) error {
	opts := options.Update().SetUpsert(true)
	_, err := r.collection.UpdateOne(ctx, filter, update, opts)
	return err
}

func (r *contestParticipantRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	_, err := r.collection.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (r *contestParticipantRepository) GetByContestAndUser(ctx context.Context, contestID, userID primitive.ObjectID) (*models.ContestParticipant, error) {
	var participant models.ContestParticipant
	err := r.collection.FindOne(ctx, bson.M{
		"contestId": contestID,
		"userId":    userID,
	}).Decode(&participant)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &participant, nil
}

func (r *contestParticipantRepository) DeleteByContestAndUser(ctx context.Context, contestID, userID primitive.ObjectID) (bool, error) {
	filter := bson.M{"contestId": contestID, "userId": userID}
	result, err := r.collection.DeleteOne(ctx, filter)
	if err != nil {
		return false, err
	}
	return result.DeletedCount > 0, nil
}

func (r *contestParticipantRepository) GetTopParticipantsByContest(ctx context.Context, contestID primitive.ObjectID, limit int64) ([]*models.ContestParticipant, error) {
	opts := options.Find().SetSort(bson.D{{Key: "score", Value: -1}, {Key: "lastSubmissionTime", Value: 1}}).SetLimit(limit)

	cursor, err := r.collection.Find(ctx, bson.M{"contestId": contestID}, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var participants []*models.ContestParticipant
	if err = cursor.All(ctx, &participants); err != nil {
		return nil, err
	}

	return participants, nil
}

func (r *contestParticipantRepository) AddSubmission(ctx context.Context, participantID primitive.ObjectID, submission models.Submission) error {
	timeNow := time.Now()

	submission.SubmissionTime = utils.AllocPtr(models.DateFromTime(timeNow))

	update := bson.M{
		"$push": bson.M{"submissions": submission},
		"$set": bson.M{
			"lastSubmissionTime": submission.SubmissionTime,
			"updatedAt":          time.Now(),
		},
		"$inc": bson.M{
			"score": submission.Points,
		},
	}

	if *submission.IsCorrect {
		update["$inc"].(bson.M)["correctSubmission"] = 1
	} else {
		update["$inc"].(bson.M)["incorrectSubmission"] = 1
	}

	_, err := r.collection.UpdateOne(
		ctx,
		bson.M{"_id": participantID},
		update,
	)
	return err
}

func (r *contestParticipantRepository) GetParticipantByUserAndContest(ctx context.Context, contestId []*primitive.ObjectID, userId primitive.ObjectID) ([]*models.ContestParticipant, error) {
	filter := bson.M{"contestId": bson.M{"$in": contestId}, "userId": userId}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var participants []*models.ContestParticipant
	if err = cursor.All(ctx, &participants); err != nil {
		return nil, err
	}
	return participants, nil
}

func (r *contestParticipantRepository) Aggregate(ctx context.Context, pipeline mongo.Pipeline) ([]*models.ContestParticipant, error) {
	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var participants []*models.ContestParticipant
	if err = cursor.All(ctx, &participants); err != nil {
		return nil, err
	}
	return participants, nil
}

func (r *contestParticipantRepository) AggregateProjected(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error) {
	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	return cursor, nil
}
