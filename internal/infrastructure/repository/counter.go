package repository

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

// CounterRepository defines the interface for counter operations
type CounterRepository interface {
	// GetNextSequence increments and returns the next value for a given counter name
	GetNextSequence(ctx context.Context, counterName string) (int64, error)
}

type counterRepository struct {
	collection *mongo.Collection
}

// Counter represents a named counter in the database
type Counter struct {
	Name  string `bson:"_id"`
	Value int64  `bson:"value"`
}

// NewCounterRepository creates a new counter repository
func NewCounterRepository(lc fx.Lifecycle, db database.Database) CounterRepository {
	collection := db.Collection("counters")
	r := &counterRepository{collection: collection}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Counter repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down counter repository.")
			return nil
		},
	})

	return r
}

// GetNextSequence atomically increments and returns the next value for a given counter
func (r *counterRepository) GetNextSequence(ctx context.Context, counterName string) (int64, error) {
	opts := options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)

	filter := bson.M{"_id": counterName}
	update := bson.M{"$inc": bson.M{"value": 1}}

	var counter Counter
	err := r.collection.FindOneAndUpdate(ctx, filter, update, opts).Decode(&counter)
	if err != nil {
		return 0, fmt.Errorf("failed to get next sequence for counter %s: %w", counterName, err)
	}

	return counter.Value, nil
}
