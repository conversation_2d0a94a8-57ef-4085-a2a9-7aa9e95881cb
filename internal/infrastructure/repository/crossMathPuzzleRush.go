package repository

import (
	"context"
	"errors"
	"time"

	"go.uber.org/fx"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type CrossMathPuzzleRushRepository interface {
	GetUserPuzzleRushStats(ctx context.Context, userID primitive.ObjectID) (*models.CrossMathPuzzleRush, error)
	GetMyGlobalRankInPuzzleRush(ctx context.Context, userID primitive.ObjectID, score int) (int, error)
	GetMyRankRelativeToFriend(ctx context.Context, userID primitive.ObjectID, score int) (int, error)
	GetGlobalTop5InPuzzleRush(ctx context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error)
	GetFriendsTop5InPuzzleRush(ctx context.Context, userID primitive.ObjectID) ([]*models.CrossMathPuzzleRushPlayerInfo, error)
	FindUserRushStatsById(ctx context.Context, userId primitive.ObjectID) (*models.CrossMathPuzzleRush, error)
	InsertOne(ctx context.Context, crossMathPuzzleRush models.CrossMathPuzzleRush) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
}

type crossMathPuzzleRushRepository struct {
	collection        *mongo.Collection
	friendsCollection *mongo.Collection
	usersCollection   *mongo.Collection
}

func NewCrossMathPuzzleRushRepository(lc fx.Lifecycle, db database.Database) CrossMathPuzzleRushRepository {
	collection := db.Collection("crossMathPuzzleRush")
	friendsCollection := db.Collection("friends")
	usersCollection := db.Collection("users")
	r := &crossMathPuzzleRushRepository{collection: collection, friendsCollection: friendsCollection, usersCollection: usersCollection}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Cross math puzzle rush repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down cross math puzzle rush repository.")
			return nil
		},
	})

	return r
}

func (r *crossMathPuzzleRushRepository) GetUserPuzzleRushStats(ctx context.Context, userID primitive.ObjectID) (*models.CrossMathPuzzleRush, error) {
	var stats models.CrossMathPuzzleRush
	err := r.collection.FindOne(ctx, bson.M{"userId": userID}).Decode(&stats)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &stats, nil
}

func (r *crossMathPuzzleRushRepository) GetMyGlobalRankInPuzzleRush(ctx context.Context, userID primitive.ObjectID, score int) (int, error) {
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.D{{Key: "bestAllTime", Value: bson.D{{Key: "$gt", Value: score}}}}}},
		{{Key: "$count", Value: "rank"}},
	}

	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return 0, err
	}
	defer cursor.Close(ctx)

	type RankResult struct {
		Rank int `bson:"rank"`
	}

	var result RankResult
	if cursor.Next(ctx) {
		if err := cursor.Decode(&result); err != nil {
			return 0, err
		}
		return result.Rank + 1, nil
	}

	return 1, nil
}

func (r *crossMathPuzzleRushRepository) GetMyRankRelativeToFriend(ctx context.Context, userID primitive.ObjectID, score int) (int, error) {
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.D{
			{Key: "$and", Value: bson.A{
				bson.D{{Key: "$or", Value: bson.A{
					bson.D{{Key: "senderId", Value: userID}},
					bson.D{{Key: "receiverId", Value: userID}},
				}}},
				bson.D{{Key: "acceptedAt", Value: bson.D{{Key: "$exists", Value: true}}}},
			}},
		}}},
		{{Key: "$project", Value: bson.D{
			{Key: "friendId", Value: bson.D{
				{Key: "$cond", Value: bson.D{
					{Key: "if", Value: bson.D{{Key: "$eq", Value: bson.A{"$senderId", userID}}}},
					{Key: "then", Value: "$receiverId"},
					{Key: "else", Value: "$senderId"},
				}},
			}},
		}}},
		{{Key: "$lookup", Value: bson.D{
			{Key: "from", Value: "crossMathPuzzleRush"},
			{Key: "localField", Value: "friendId"},
			{Key: "foreignField", Value: "userId"},
			{Key: "as", Value: "rushStats"},
		}}},
		{{Key: "$match", Value: bson.D{
			{Key: "rushStats.bestAllTime", Value: bson.D{{Key: "$gt", Value: score}}},
		}}},
		{{Key: "$count", Value: "rank"}},
	}

	cursor, err := r.friendsCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return 0, err
	}
	defer cursor.Close(ctx)

	type RankResult struct {
		Rank int `bson:"rank"`
	}

	var result RankResult
	if cursor.Next(ctx) {
		if err := cursor.Decode(&result); err != nil {
			return 0, err
		}
		return result.Rank + 1, nil
	}

	return 1, nil
}

func (r *crossMathPuzzleRushRepository) GetGlobalTop5InPuzzleRush(ctx context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error) {
	pipeline := mongo.Pipeline{
		{{Key: "$sort", Value: bson.D{{Key: "bestAllTime", Value: -1}}}},
		{{Key: "$limit", Value: 5}},
		{{Key: "$lookup", Value: bson.D{
			{Key: "from", Value: "users"},
			{Key: "localField", Value: "userId"},
			{Key: "foreignField", Value: "_id"},
			{Key: "as", Value: "userInfo"},
		}}},
		{{Key: "$unwind", Value: "$userInfo"}},
		{{Key: "$project", Value: bson.D{
			{Key: "score", Value: "$bestAllTime"},
			{Key: "userInfo", Value: 1},
			{Key: "_id", Value: 0},
		}}},
	}

	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	result := make([]*models.CrossMathPuzzleRushPlayerInfo, 0, 5)
	rank := 1

	for cursor.Next(ctx) {
		var rushInfo struct {
			Score    *int                     `bson:"score"`
			UserInfo models.UserPublicDetails `bson:"userInfo"`
		}
		if err := cursor.Decode(&rushInfo); err != nil {
			return nil, err
		}

		result = append(result, &models.CrossMathPuzzleRushPlayerInfo{
			Rank:     utils.AllocPtr(rank),
			Score:    rushInfo.Score,
			UserInfo: utils.AllocPtr(rushInfo.UserInfo),
		})

		rank++
	}

	if err := cursor.Err(); err != nil {
		return nil, err
	}

	return result, nil
}

func (r *crossMathPuzzleRushRepository) GetFriendsTop5InPuzzleRush(ctx context.Context, userID primitive.ObjectID) ([]*models.CrossMathPuzzleRushPlayerInfo, error) {
	friendsPipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.D{
			{Key: "$and", Value: bson.A{
				bson.D{{Key: "$or", Value: bson.A{
					bson.D{{Key: "senderId", Value: userID}},
					bson.D{{Key: "receiverId", Value: userID}},
				}}},
				bson.D{{Key: "acceptedAt", Value: bson.D{{Key: "$exists", Value: true}}}},
			}},
		}}},
		{{Key: "$project", Value: bson.D{
			{Key: "friendId", Value: bson.D{
				{Key: "$cond", Value: bson.D{
					{Key: "if", Value: bson.D{{Key: "$eq", Value: bson.A{"$senderId", userID}}}},
					{Key: "then", Value: "$receiverId"},
					{Key: "else", Value: "$senderId"},
				}},
			}},
		}}},
	}

	friendsCursor, err := r.friendsCollection.Aggregate(ctx, friendsPipeline)
	if err != nil {
		return nil, err
	}
	defer friendsCursor.Close(ctx)

	friendIDs := []primitive.ObjectID{userID}
	for friendsCursor.Next(ctx) {
		var result struct {
			FriendID primitive.ObjectID `bson:"friendId"`
		}
		if err := friendsCursor.Decode(&result); err != nil {
			return nil, err
		}
		friendIDs = append(friendIDs, result.FriendID)
	}

	if err := friendsCursor.Err(); err != nil {
		return nil, err
	}

	mainPipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.D{{Key: "userId", Value: bson.D{{Key: "$in", Value: friendIDs}}}}}},
		{{Key: "$sort", Value: bson.D{{Key: "bestAllTime", Value: -1}}}},
		{{Key: "$limit", Value: 5}},
		{{Key: "$lookup", Value: bson.D{
			{Key: "from", Value: "users"},
			{Key: "localField", Value: "userId"},
			{Key: "foreignField", Value: "_id"},
			{Key: "as", Value: "userInfo"},
		}}},
		{{Key: "$unwind", Value: "$userInfo"}},
		{{Key: "$project", Value: bson.D{
			{Key: "score", Value: "$bestAllTime"},
			{Key: "userInfo", Value: 1},
			{Key: "_id", Value: 0},
		}}},
	}

	mainCursor, err := r.collection.Aggregate(ctx, mainPipeline)
	if err != nil {
		return nil, err
	}
	defer mainCursor.Close(ctx)

	result := make([]*models.CrossMathPuzzleRushPlayerInfo, 0, 5)
	rank := 1

	for mainCursor.Next(ctx) {
		var rushInfo struct {
			Score    *int                     `bson:"score"`
			UserInfo models.UserPublicDetails `bson:"userInfo"`
		}
		if err := mainCursor.Decode(&rushInfo); err != nil {
			return nil, err
		}

		result = append(result, &models.CrossMathPuzzleRushPlayerInfo{
			Rank:     utils.AllocPtr(rank),
			Score:    rushInfo.Score,
			UserInfo: utils.AllocPtr(rushInfo.UserInfo),
		})

		rank++
	}

	if err := mainCursor.Err(); err != nil {
		return nil, err
	}

	return result, nil
}

func (r *crossMathPuzzleRushRepository) FindUserRushStatsById(ctx context.Context, userId primitive.ObjectID) (*models.CrossMathPuzzleRush, error) {
	var existingStats models.CrossMathPuzzleRush
	err := r.collection.FindOne(ctx, bson.M{"userId": userId}).Decode(&existingStats)
	if err != nil {
		return nil, err
	}
	return &existingStats, nil
}

func (r *crossMathPuzzleRushRepository) InsertOne(ctx context.Context, crossMathPuzzleRush models.CrossMathPuzzleRush) error {
	if crossMathPuzzleRush.ID == primitive.NilObjectID {
		crossMathPuzzleRush.ID = primitive.NewObjectID()
	}
	crossMathPuzzleRush.CreatedAt = utils.AllocPtr(time.Now())
	crossMathPuzzleRush.UpdatedAt = utils.AllocPtr(time.Now())
	_, err := r.collection.InsertOne(ctx, crossMathPuzzleRush)
	if err != nil {
		return err
	}
	return nil
}

func (r *crossMathPuzzleRushRepository) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}
