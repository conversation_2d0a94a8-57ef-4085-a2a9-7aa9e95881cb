package repository

import (
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

type DailyChallengeRepository interface {
	Create(ctx context.Context, dailyChallenge *models.DailyChallenge) error
	FindByID(ctx context.Context, id primitive.ObjectID) (*models.DailyChallenge, error)
	FindByChallengeNumber(ctx context.Context, challengeNumber int) (*models.DailyChallenge, error)
	FindByChallengeNumberAndDivison(ctx context.Context, challengeNumber int, division string) (*models.DailyChallenge, error)
	Update(ctx context.Context, dailyChallenge *models.DailyChallenge) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.DailyChallenge, error)
	FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.DailyChallenge, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
}

type dailyChallengeRepository struct {
	collection *mongo.Collection
}

func NewDailyChallengeRepository(lc fx.Lifecycle, db database.Database) DailyChallengeRepository {
	collection := db.Collection("dailychallenges")
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Daily challenge repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down daily challenge repository.")
			return nil
		},
	})
	return &dailyChallengeRepository{collection: collection}
}

func (r *dailyChallengeRepository) Create(ctx context.Context, dailyChallenge *models.DailyChallenge) error {
	dailyChallenge.CreatedAt = utils.AllocPtr(time.Now())
	dailyChallenge.UpdatedAt = utils.AllocPtr(time.Now())

	result, err := r.collection.InsertOne(ctx, dailyChallenge)
	if err != nil {
		return err
	}

	dailyChallenge.ID = result.InsertedID.(primitive.ObjectID)
	return nil
}

func (r *dailyChallengeRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.DailyChallenge, error) {
	var dailyChallenge models.DailyChallenge
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&dailyChallenge)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &dailyChallenge, nil
}

func (r *dailyChallengeRepository) FindByChallengeNumber(ctx context.Context, challengeNumber int) (*models.DailyChallenge, error) {
	var dailyChallenge models.DailyChallenge
	err := r.collection.FindOne(ctx, bson.M{"challengeNumber": challengeNumber}).Decode(&dailyChallenge)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &dailyChallenge, nil
}

func (r *dailyChallengeRepository) FindByChallengeNumberAndDivison(ctx context.Context, challengeNumber int, division string) (*models.DailyChallenge, error) {
	var dailyChallenge models.DailyChallenge
	filter := bson.M{"challengeNumber": challengeNumber, "division": division}

	err := r.collection.FindOne(ctx, filter).Decode(&dailyChallenge)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &dailyChallenge, nil
}

func (r *dailyChallengeRepository) Update(ctx context.Context, dailyChallenge *models.DailyChallenge) error {
	dailyChallenge.UpdatedAt = utils.AllocPtr(time.Now())

	_, err := r.collection.ReplaceOne(
		ctx,
		bson.M{"_id": dailyChallenge.ID},
		dailyChallenge,
	)
	return err
}

func (r *dailyChallengeRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	_, err := r.collection.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (r *dailyChallengeRepository) List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.DailyChallenge, error) {
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var dailyChallenges []*models.DailyChallenge
	if err = cursor.All(ctx, &dailyChallenges); err != nil {
		return nil, err
	}

	return dailyChallenges, nil
}

func (r *dailyChallengeRepository) Count(ctx context.Context, filter bson.M) (int64, error) {
	return r.collection.CountDocuments(ctx, filter)
}

func (r *dailyChallengeRepository) FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.DailyChallenge, error) {
	var dailyChallenge models.DailyChallenge
	err := r.collection.FindOne(ctx, filter, opts...).Decode(&dailyChallenge)
	if err != nil {
		return nil, err
	}
	return &dailyChallenge, nil
}
