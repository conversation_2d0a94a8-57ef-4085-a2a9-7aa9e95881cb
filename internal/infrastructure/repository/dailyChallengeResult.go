package repository

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

type DailyChallengeResultRepository interface {
	Create(ctx context.Context, dailyChallengeResult *models.DailyChallengeResult) error
	FindByID(ctx context.Context, id primitive.ObjectID) (*models.DailyChallengeResult, error)
	FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.DailyChallengeResult, error)
	Update(ctx context.Context, dailyChallengeResult *models.DailyChallengeResult) error
	UpdateOne(ctx context.Context, filter, update bson.M, opts ...*options.UpdateOptions) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.DailyChallengeResult, error)
	Aggregate(ctx context.Context, pipeline mongo.Pipeline) ([]*models.DailyChallengeResult, error)
	AggregateProjected(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
}

type dailyChallengeResultRepository struct {
	collection *mongo.Collection
}

func (r *dailyChallengeResultRepository) syncDCRIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{{Key: "challengeId", Value: 1}}},
	})
}

func (r *dailyChallengeResultRepository) syncFDCRIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{{Key: "challengeId", Value: 1}}},
	})
}

func NewDailyChallengeResultRepository(lc fx.Lifecycle, db database.Database) DailyChallengeResultRepository {
	r := &dailyChallengeResultRepository{collection: db.Collection("dailychallengeresults")}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Daily challenge result repository initialized and ready.")
			go func() {
				err = r.syncDCRIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for daily challenge result repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down daily challenge result repository.")
			return nil
		},
	})
	return r
}

func NewFlaggedDCResultRepository(lc fx.Lifecycle, db database.Database) DailyChallengeResultRepository {
	r := &dailyChallengeResultRepository{collection: db.Collection("flaggeddailychallengeresults")}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Flagged daily challenge result repository initialized and ready.")
			go func() {
				err = r.syncFDCRIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for flagged daily challenge result repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down flagged daily challenge result repository.")
			return nil
		},
	})
	return r
}

func (r *dailyChallengeResultRepository) Create(ctx context.Context, dailyChallengeResult *models.DailyChallengeResult) error {
	dailyChallengeResult.ID = utils.AllocPtr(primitive.NewObjectID())
	_, err := r.collection.InsertOne(ctx, dailyChallengeResult)
	return err
}

func (r *dailyChallengeResultRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.DailyChallengeResult, error) {
	var dailyChallengeResult models.DailyChallengeResult
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&dailyChallengeResult)
	if err != nil {
		return nil, err
	}
	return &dailyChallengeResult, nil
}

func (r *dailyChallengeResultRepository) FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.DailyChallengeResult, error) {
	var dailyChallengeResult models.DailyChallengeResult
	err := r.collection.FindOne(ctx, filter, opts...).Decode(&dailyChallengeResult)
	if err != nil {
		return nil, err
	}
	return &dailyChallengeResult, nil
}

func (r *dailyChallengeResultRepository) FindByChallengeAndUser(ctx context.Context, challengeID, userID primitive.ObjectID) (*models.DailyChallengeResult, error) {
	var dailyChallengeResult models.DailyChallengeResult
	filter := bson.M{
		"$and": []bson.M{
			{"userId": userID},
			{
				"$or": []bson.M{
					{"challengeId": challengeID},
					{"challengeId": challengeID},
				},
			},
		},
	}
	err := r.collection.FindOne(ctx, filter).Decode(&dailyChallengeResult)
	if err != nil {
		return nil, err
	}
	return &dailyChallengeResult, nil
}

func (r *dailyChallengeResultRepository) Update(ctx context.Context, dailyChallengeResult *models.DailyChallengeResult) error {
	filter := bson.M{
		"userId":          dailyChallengeResult.UserID,
		"challengeNumber": dailyChallengeResult.ChallengeNumber,
	}

	opts := options.Update().SetUpsert(true)

	_, err := r.collection.UpdateOne(
		ctx,
		filter,
		bson.M{"$set": bson.M{
			"score":             dailyChallengeResult.Score,
			"statikCoinsEarned": dailyChallengeResult.StatikCoinsEarned,
			"submittedTimes":    dailyChallengeResult.SubmittedTimes,
			"rank":              dailyChallengeResult.Rank,
			"updatedAt":         time.Now(),
			"userId":            dailyChallengeResult.UserID,
			"challengeId":       dailyChallengeResult.ChallengeID,
			"resultStatus":      dailyChallengeResult.ResultStatus,
			"challengeNumber":   dailyChallengeResult.ChallengeNumber,
			"completedAt":       dailyChallengeResult.CompletedAt,
		}},
		opts,
	)
	return err
}

func (r *dailyChallengeResultRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	_, err := r.collection.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (r *dailyChallengeResultRepository) List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.DailyChallengeResult, error) {
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var results []*models.DailyChallengeResult
	if err = cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	return results, nil
}

func (r *dailyChallengeResultRepository) Aggregate(ctx context.Context, pipeline mongo.Pipeline) ([]*models.DailyChallengeResult, error) {
	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var results []*models.DailyChallengeResult
	if err = cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	return results, nil
}

func (r *dailyChallengeResultRepository) AggregateProjected(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error) {
	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	return cursor, nil
}

func (r *dailyChallengeResultRepository) UpdateOne(ctx context.Context, filter, update bson.M, opts ...*options.UpdateOptions) error {
	_, err := r.collection.UpdateOne(ctx, filter, update, opts...)
	return err
}

func (r *dailyChallengeResultRepository) Count(ctx context.Context, filter bson.M) (int64, error) {
	return r.collection.CountDocuments(ctx, filter)
}
