package repository

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/fx"
)

type DeletedUserRepository interface {
	InsertOne(ctx context.Context, document *models.User) (*mongo.InsertOneResult, error)
}

type deletedUserRepository struct {
	collection *mongo.Collection
}

func NewDeletedUserRepository(lc fx.Lifecycle, db database.Database) DeletedUserRepository {
	collection := db.Collection("deletedUsers")
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Deleted user repository initialized and ready.")
			return db.<PERSON>(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down deleted user repository.")
			return nil
		},
	},
	)
	return &deletedUserRepository{collection: collection}
}

func (r *deletedUserRepository) InsertOne(ctx context.Context, user *models.User) (*mongo.InsertOneResult, error) {
	return r.collection.InsertOne(ctx, user)
}
