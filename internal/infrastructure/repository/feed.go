package repository

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/fx"
)

type FeedRepository interface {
	GetFeedById(ctx context.Context, feedId primitive.ObjectID) (*models.UserFeed, error)
	CreateUserFeed(ctx context.Context, userFeed *models.UserFeed) error
	GetUserFeed(ctx context.Context, userId primitive.ObjectID, lastId *primitive.ObjectID, pageSize int) ([]*models.UserFeed, error)
	BatchInsertUserFeed(ctx context.Context, userFeeds []*models.UserFeed) error
	UpdateFeed(ctx context.Context, userFeed *models.UserFeed) error
}

type feedRepository struct {
	collection *mongo.Collection
}

func NewUserFeed(lc fx.Lifecycle, db database.Database) FeedRepository {
	s := &feedRepository{
		collection: db.Collection("userFeed"),
	}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "User feed repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down user feed repository.")
			return nil
		},
	})
	return s
}

func (s *feedRepository) GetFeedById(ctx context.Context, feedId primitive.ObjectID) (*models.UserFeed, error) {
	if feedId.IsZero() {
		return nil, nil
	}
	var feed models.UserFeed
	if err := s.collection.FindOne(ctx, bson.M{"_id": feedId}).Decode(&feed); err != nil {
		return nil, err
	}
	return &feed, nil
}

func (s *feedRepository) CreateUserFeed(ctx context.Context, userFeed *models.UserFeed) error {
	if userFeed.ID.IsZero() {
		userFeed.ID = primitive.NewObjectID()
	}
	userFeed.CreatedAt = time.Now()
	_, err := s.collection.InsertOne(ctx, userFeed)
	return err
}

func (s *feedRepository) UpdateFeed(ctx context.Context, userFeed *models.UserFeed) error {
	update := bson.M{}
	update["isLiked"] = userFeed.IsLiked
	_, err := s.collection.UpdateOne(ctx, bson.M{"_id": userFeed.ID}, bson.M{"$set": update})
	return err
}

func (s *feedRepository) GetUserFeed(ctx context.Context, userId primitive.ObjectID, lastId *primitive.ObjectID, pageSize int) ([]*models.UserFeed, error) {
	if userId.IsZero() {
		return nil, nil
	}

	// Base match
	matchStage := bson.M{"userId": userId}
	if lastId != nil {
		matchStage["_id"] = bson.M{"$lt": *lastId}
	}

	pipeline := []bson.M{
		{
			"$match": matchStage,
		},
		{
			"$sort": bson.M{"_id": -1}, // Descending so that _id > lastId makes sense
		},
		{
			"$limit": pageSize,
		},
		{
			"$lookup": bson.M{
				"from":         "feedData",
				"localField":   "feedReferenceId",
				"foreignField": "_id",
				"as":           "feedDataArray",
			},
		},
		{
			"$addFields": bson.M{
				"feedData": bson.M{"$arrayElemAt": bson.A{"$feedDataArray", 0}},
			},
		},
		{
			"$project": bson.M{
				"feedDataArray": 0,
			},
		},
	}

	cursor, err := s.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var userFeeds []*models.UserFeed
	for cursor.Next(ctx) {
		var feed models.UserFeed
		if err := cursor.Decode(&feed); err != nil {
			return nil, err
		}
		userFeeds = append(userFeeds, &feed)
	}
	if err := cursor.Err(); err != nil {
		return nil, err
	}

	return userFeeds, nil
}

func (s *feedRepository) BatchInsertUserFeed(ctx context.Context, userFeeds []*models.UserFeed) error {
	if len(userFeeds) == 0 {
		return nil
	}
	var userFeedsArray []interface{}
	for _, userFeed := range userFeeds {
		if userFeed.ID.IsZero() {
			userFeed.ID = primitive.NewObjectID()
		}
		userFeed.CreatedAt = time.Now()
		userFeedsArray = append(userFeedsArray, userFeed)
	}
	_, err := s.collection.InsertMany(ctx, userFeedsArray)
	return err
}
