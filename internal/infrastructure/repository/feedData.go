package repository

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/fx"
)

type FeedDataRepository interface {
	CreateFeedData(ctx context.Context, feedData *models.FeedData) error
	GetFeedDataById(ctx context.Context, feedDataId primitive.ObjectID) (*models.FeedData, error)
	GetFeedDataByIdGroup(ctx context.Context, feedIdGroup []primitive.ObjectID) ([]*models.FeedData, error)
	UpdateLikeCount(ctx context.Context, feedDataId primitive.ObjectID, isLike bool) (bool, error)
}

type feedDataRepository struct {
	collection *mongo.Collection
}

func NewFeedData(lc fx.Lifecycle, db database.Database) FeedDataRepository {
	s := &feedDataRepository{
		collection: db.Collection("feedData"),
	}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Feed data repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down feed data repository.")
			return nil
		},
	})
	return s
}

func (s *feedDataRepository) CreateFeedData(ctx context.Context, feedData *models.FeedData) error {
	if feedData.ID.IsZero() {
		feedData.ID = primitive.NewObjectID()
	}
	feedData.CreatedAt = time.Now()
	feedData.UpdatedAt = time.Now()
	_, err := s.collection.InsertOne(ctx, feedData)
	if err != nil {
		return err
	}
	return nil
}

func (s *feedDataRepository) GetFeedDataById(ctx context.Context, feedDataId primitive.ObjectID) (*models.FeedData, error) {
	if feedDataId.IsZero() {
		return nil, systemErrors.ErrFeedDataIdNil
	}
	var feedData models.FeedData
	if err := s.collection.FindOne(ctx, bson.M{"_id": feedDataId}).Decode(&feedData); err != nil {
		return nil, err
	}
	return &feedData, nil
}

func (s *feedDataRepository) GetFeedDataByIdGroup(ctx context.Context, feedIdGroup []primitive.ObjectID) (feedData []*models.FeedData, err error) {
	if len(feedIdGroup) == 0 {
		return nil, nil
	}
	cursor, err := s.collection.Find(ctx, bson.M{"_id": bson.M{"$in": feedIdGroup}})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)
	if err := cursor.All(ctx, &feedData); err != nil {
		return nil, err
	}
	return feedData, nil
}

func (s *feedDataRepository) UpdateLikeCount(ctx context.Context, feedDataId primitive.ObjectID, isLike bool) (bool, error) {
	increment := 1
	if !isLike {
		increment = -1
	}
	result, err := s.collection.UpdateOne(ctx, bson.M{"_id": feedDataId}, bson.M{"$inc": bson.M{"likesCount": increment}})
	if err != nil {
		return false, err
	}
	return result.MatchedCount > 0, nil
}
