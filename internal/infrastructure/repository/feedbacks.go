package repository

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/mongo"
)

type FeedbackRepository interface {
	Create(ctx context.Context, feedback *models.Feedback) error
}

type feedbackRepository struct {
	collection *mongo.Collection
	db         database.Database
}

func NewFeedbackRepository(db database.Database) FeedbackRepository {
	collection := db.Collection("feedbacks")
	return &feedbackRepository{collection: collection, db: db}
}

func (r *feedbackRepository) Create(ctx context.Context, feedback *models.Feedback) error {
	_, err := r.collection.InsertOne(ctx, feedback)
	if err != nil {
		return err
	}
	return nil
}
