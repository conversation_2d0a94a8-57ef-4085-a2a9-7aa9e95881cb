package repository

import (
	"context"
	"errors"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

type FixturesRepository interface {
	BulkInsert(ctx context.Context, fixtures []*models.Fictures) error
	Update(ctx context.Context, fixture *models.Fictures) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	GetByShowdownUserIDAndRound(ctx context.Context, showdownID, participantID primitive.ObjectID, round int) (*models.Fictures, error)
	GetFixturesByShowdownIDAndRound(ctx context.Context, showdownId primitive.ObjectID, round int) ([]*models.Fictures, error)
}

type fixturesRepository struct {
	collection *mongo.Collection
}

func NewFixturesRepository(lc fx.Lifecycle, db database.Database) FixturesRepository {
	collection := db.Collection("fixtures")
	repo := &fixturesRepository{collection: collection}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Fixtures repository initialized and ready.")
			go func() {
				err = repo.syncIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for fixtures repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down fixtures repository.")
			return nil
		},
	})
	return repo
}

func (r *fixturesRepository) syncIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{
			{Key: "showdownId", Value: 1},
			{Key: "participants", Value: 1},
			{Key: "round", Value: 1},
		}, Options: options.Index().SetUnique(true)},
		{Keys: bson.D{
			{Key: "showdownId", Value: 1},
			{Key: "round", Value: 1},
		}},
	})
}

func (r *fixturesRepository) BulkInsert(ctx context.Context, fixtures []*models.Fictures) error {
	documents := make([]interface{}, len(fixtures))
	for i, fixture := range fixtures {
		if fixture.ID.IsZero() {
			fixture.ID = primitive.NewObjectID()
		}
		documents[i] = fixture
	}

	result, err := r.collection.InsertMany(ctx, documents)
	if err != nil {
		return err
	}

	for i, insertedID := range result.InsertedIDs {
		fixtures[i].ID = insertedID.(primitive.ObjectID)
	}

	return nil
}

func (r *fixturesRepository) GetFixturesByShowdownIDAndRound(ctx context.Context, showdownId primitive.ObjectID, round int) ([]*models.Fictures, error) {
	filter := bson.M{
		"showdownId": showdownId,
		"round":      round,
	}
	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	var fixtures []*models.Fictures
	if err := cursor.All(ctx, &fixtures); err != nil {
		return nil, err
	}

	return fixtures, nil
}

func (r *fixturesRepository) Update(ctx context.Context, fixture *models.Fictures) error {
	opts := options.Update().SetUpsert(true)
	_, err := r.collection.UpdateOne(
		ctx,
		bson.M{"_id": fixture.ID},
		fixture,
		opts,
	)
	return err
}

func (r *fixturesRepository) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := r.collection.UpdateOne(ctx, filter, update)
	return err
}

func (r *fixturesRepository) GetByShowdownUserIDAndRound(ctx context.Context, showdownID, participantID primitive.ObjectID, round int) (*models.Fictures, error) {
	filter := bson.M{
		"showdownId":   showdownID,
		"participants": participantID,
		"round":        round,
	}

	var fixture models.Fictures

	err := r.collection.FindOne(ctx, filter).Decode(&fixture)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return &models.Fictures{}, nil
		}
		return nil, err
	}

	return &fixture, nil
}

func (r *fixturesRepository) UpdateFixture(ctx context.Context, fixture *models.Fictures) error {
	_, err := r.collection.UpdateOne(ctx, bson.M{"_id": fixture.ID}, bson.M{"$set": fixture})
	if err != nil {
		return fmt.Errorf("failed to update game: %w", err)
	}
	return nil
}
