package repository

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/fx"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
)

type ForumRepository interface {
	CreateForum(ctx context.Context, forum *models.Forum) (*models.Forum, error)
	CreateForumThread(ctx context.Context, thread *models.ForumThread) (*models.ForumThread, error)
	GetForumThreads(ctx context.Context, forumID primitive.ObjectID, pageNumber, pageSize *int, sort bson.D) ([]*models.ForumThread, error)
	CreateForumReply(ctx context.Context, reply *models.ForumReply) (*models.ForumReply, error)
	GetForumReplies(ctx context.Context, threadID primitive.ObjectID, pageNumber, pageSize *int, sort bson.D) ([]*models.ForumReply, error)
	GetForums(ctx context.Context, clubID primitive.ObjectID, pageNumber, pageSize *int, sort bson.D) ([]*models.Forum, error)
	GetForum(ctx context.Context, forumID primitive.ObjectID) (*models.Forum, error)
	GetForumThread(ctx context.Context, threadId primitive.ObjectID) (*models.ForumThread, error)
	CountForumThreads(ctx context.Context, forumID primitive.ObjectID) (int64, error)
	CountForumReplies(ctx context.Context, threadID primitive.ObjectID) (int64, error)
	CountForums(ctx context.Context, clubID primitive.ObjectID) (int64, error)
}

type forumRepository struct {
	forums       *mongo.Collection
	forumThreads *mongo.Collection
	forumReplies *mongo.Collection
}

func NewForumRepository(lc fx.Lifecycle, db database.Database) ForumRepository {
	r := &forumRepository{
		forums:       db.Collection("clubForums"),
		forumThreads: db.Collection("clubForumThreads"),
		forumReplies: db.Collection("clubForumReplies"),
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Forum repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down forum repository.")
			return nil
		},
	})

	return r
}

func (r *forumRepository) GetForum(ctx context.Context, forumID primitive.ObjectID) (*models.Forum, error) {
	if forumID == primitive.NilObjectID {
		return nil, fmt.Errorf("forum ID cannot be empty")
	}
	var forum models.Forum
	err := r.forums.FindOne(ctx, bson.M{"_id": forumID}).Decode(&forum)
	if err != nil {
		return nil, err
	}
	return &forum, nil
}

func (r *forumRepository) GetForumThread(ctx context.Context, threadId primitive.ObjectID) (*models.ForumThread, error) {
	if threadId == primitive.NilObjectID {
		return nil, fmt.Errorf("thread ID cannot be empty")
	}
	var forumThread models.ForumThread
	err := r.forumThreads.FindOne(ctx, bson.M{"_id": threadId}).Decode(&forumThread)
	if err != nil {
		return nil, err
	}
	return &forumThread, nil
}

func (r *forumRepository) CreateForum(ctx context.Context, forum *models.Forum) (*models.Forum, error) {
	forum.CreatedAt = time.Now()
	forum.UpdatedAt = time.Now()
	_, err := r.forums.InsertOne(ctx, forum)
	if err != nil {
		return nil, err
	}

	return forum, nil
}

func (r *forumRepository) CreateForumThread(ctx context.Context, thread *models.ForumThread) (*models.ForumThread, error) {
	thread.CreatedAt = time.Now()
	_, err := r.forumThreads.InsertOne(ctx, thread)
	if err != nil {
		return nil, err
	}
	return thread, nil
}

func (r *forumRepository) CreateForumReply(ctx context.Context, reply *models.ForumReply) (*models.ForumReply, error) {
	reply.CreatedAt = time.Now()
	_, err := r.forumReplies.InsertOne(ctx, reply)
	if err != nil {
		return nil, err
	}
	return reply, nil
}

func (r *forumRepository) GetForumThreads(ctx context.Context, forumID primitive.ObjectID, pageNumber, pageSize *int, sort bson.D) ([]*models.ForumThread, error) {
	if forumID == primitive.NilObjectID {
		return nil, fmt.Errorf("forum ID cannot be empty")
	}
	skipValue := int64(0)
	limitValue := int64(DefaultPageSize)

	if pageSize != nil {
		limitValue = int64(*pageSize)
	}

	if pageNumber != nil && pageSize != nil && *pageNumber > 0 && *pageSize > 0 {
		skipValue = int64((*pageNumber - 1) * *pageSize)
	}

	matchStage := bson.D{{Key: "$match", Value: bson.M{"forumId": forumID}}}

	var pipelineStages []bson.D
	pipelineStages = append(pipelineStages, matchStage)

	if sort != nil {
		sortStage := bson.D{{Key: "$sort", Value: sort}}
		pipelineStages = append(pipelineStages, sortStage)
	} else {
		defaultSort := bson.D{{Key: "$sort", Value: bson.D{{Key: "createdAt", Value: -1}}}}
		pipelineStages = append(pipelineStages, defaultSort)
	}

	skipStage := bson.D{{Key: "$skip", Value: skipValue}}
	limitStage := bson.D{{Key: "$limit", Value: limitValue}}
	pipelineStages = append(pipelineStages, skipStage, limitStage)

	lookupStage := bson.D{
		{Key: "$lookup", Value: bson.D{
			{Key: "from", Value: "users"},
			{Key: "localField", Value: "createdBy"},
			{Key: "foreignField", Value: "_id"},
			{Key: "as", Value: "creatorDetails"},
		}},
	}

	pipelineStages = append(pipelineStages, lookupStage)

	unwindStage := bson.D{
		{Key: "$unwind", Value: bson.D{
			{Key: "path", Value: "$creatorDetails"},
			{Key: "preserveNullAndEmptyArrays", Value: true},
		}},
	}
	pipelineStages = append(pipelineStages, unwindStage)

	projectStage := bson.D{
		{Key: "$project", Value: bson.D{
			{Key: "_id", Value: 1},
			{Key: "forumId", Value: 1},
			{Key: "title", Value: 1},
			{Key: "content", Value: 1},
			{Key: "createdAt", Value: 1},
			{Key: "createdBy", Value: 1},
			{Key: "creatorInfo", Value: bson.D{
				{Key: "username", Value: "$creatorDetails.username"},
				{Key: "profileImageUrl", Value: "$creatorDetails.profileImageUrl"},
			}},
		}},
	}
	pipelineStages = append(pipelineStages, projectStage)

	cursor, err := r.forumThreads.Aggregate(ctx, pipelineStages)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var threads []*models.ForumThread
	if err = cursor.All(ctx, &threads); err != nil {
		return nil, err
	}

	return threads, nil
}

func (r *forumRepository) GetForumReplies(ctx context.Context, threadID primitive.ObjectID, pageNumber, pageSize *int, sort bson.D) ([]*models.ForumReply, error) {
	if threadID == primitive.NilObjectID {
		return nil, fmt.Errorf("thread ID cannot be empty")
	}
	skipValue := int64(0)
	limitValue := int64(DefaultPageSize)

	if pageSize != nil {
		limitValue = int64(*pageSize)
	}

	if pageNumber != nil && pageSize != nil && *pageNumber > 0 && *pageSize > 0 {
		skipValue = int64((*pageNumber - 1) * *pageSize)
	}

	matchStage := bson.D{{Key: "$match", Value: bson.M{"threadId": threadID}}}

	var pipelineStages []bson.D
	pipelineStages = append(pipelineStages, matchStage)

	if sort != nil {
		sortStage := bson.D{{Key: "$sort", Value: sort}}
		pipelineStages = append(pipelineStages, sortStage)
	} else {
		defaultSort := bson.D{{Key: "$sort", Value: bson.D{{Key: "createdAt", Value: 1}}}}
		pipelineStages = append(pipelineStages, defaultSort)
	}

	skipStage := bson.D{{Key: "$skip", Value: skipValue}}
	limitStage := bson.D{{Key: "$limit", Value: limitValue}}
	pipelineStages = append(pipelineStages, skipStage, limitStage)

	lookupStage := bson.D{
		{Key: "$lookup", Value: bson.D{
			{Key: "from", Value: "users"},
			{Key: "localField", Value: "createdBy"},
			{Key: "foreignField", Value: "_id"},
			{Key: "as", Value: "creatorDetails"},
		}},
	}

	pipelineStages = append(pipelineStages, lookupStage)

	unwindStage := bson.D{
		{Key: "$unwind", Value: bson.D{
			{Key: "path", Value: "$creatorDetails"},
			{Key: "preserveNullAndEmptyArrays", Value: true},
		}},
	}
	pipelineStages = append(pipelineStages, unwindStage)

	projectStage := bson.D{
		{Key: "$project", Value: bson.D{
			{Key: "_id", Value: 1},
			{Key: "threadId", Value: 1},
			{Key: "content", Value: 1},
			{Key: "createdAt", Value: 1},
			{Key: "createdBy", Value: 1},
			{Key: "creatorInfo", Value: bson.D{
				{Key: "username", Value: "$creatorDetails.username"},
				{Key: "profileImageUrl", Value: "$creatorDetails.profileImageUrl"},
			}},
		}},
	}

	pipelineStages = append(pipelineStages, projectStage)

	cursor, err := r.forumReplies.Aggregate(ctx, pipelineStages)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var replies []*models.ForumReply
	if err = cursor.All(ctx, &replies); err != nil {
		return nil, err
	}

	return replies, nil
}

func (r *forumRepository) GetForums(ctx context.Context, clubID primitive.ObjectID, pageNumber, pageSize *int, sort bson.D) ([]*models.Forum, error) {
	skipValue := int64(0)
	limitValue := int64(DefaultPageSize)

	if pageSize != nil {
		limitValue = int64(*pageSize)
	}

	if pageNumber != nil && pageSize != nil && *pageNumber > 0 && *pageSize > 0 {
		skipValue = int64((*pageNumber - 1) * *pageSize)
	}

	matchStage := bson.D{{Key: "$match", Value: bson.M{"clubId": clubID}}}

	var pipelineStages []bson.D
	pipelineStages = append(pipelineStages, matchStage)

	if sort != nil {
		sortStage := bson.D{{Key: "$sort", Value: sort}}
		pipelineStages = append(pipelineStages, sortStage)
	} else {
		defaultSort := bson.D{{Key: "$sort", Value: bson.D{{Key: "createdAt", Value: -1}}}}
		pipelineStages = append(pipelineStages, defaultSort)
	}

	skipStage := bson.D{{Key: "$skip", Value: skipValue}}
	limitStage := bson.D{{Key: "$limit", Value: limitValue}}
	pipelineStages = append(pipelineStages, skipStage, limitStage)

	lookupStage := bson.D{
		{Key: "$lookup", Value: bson.D{
			{Key: "from", Value: "users"},
			{Key: "localField", Value: "createdBy"},
			{Key: "foreignField", Value: "_id"},
			{Key: "as", Value: "creatorDetails"},
		}},
	}
	pipelineStages = append(pipelineStages, lookupStage)

	unwindStage := bson.D{
		{Key: "$unwind", Value: bson.D{
			{Key: "path", Value: "$creatorDetails"},
			{Key: "preserveNullAndEmptyArrays", Value: true},
		}},
	}
	pipelineStages = append(pipelineStages, unwindStage)

	projectStage := bson.D{
		{Key: "$project", Value: bson.D{
			{Key: "_id", Value: 1},
			{Key: "clubId", Value: 1},
			{Key: "title", Value: 1},
			{Key: "description", Value: 1},
			{Key: "createdAt", Value: 1},
			{Key: "updatedAt", Value: 1},
			{Key: "createdBy", Value: 1},
			{Key: "creatorInfo", Value: bson.D{
				{Key: "username", Value: "$creatorDetails.username"},
				{Key: "profileImageUrl", Value: "$creatorDetails.profileImageUrl"},
			}},
		}},
	}
	pipelineStages = append(pipelineStages, projectStage)

	cursor, err := r.forums.Aggregate(ctx, pipelineStages)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var forums []*models.Forum
	if err = cursor.All(ctx, &forums); err != nil {
		return nil, err
	}

	return forums, nil
}

func (r *forumRepository) CountForumThreads(ctx context.Context, forumID primitive.ObjectID) (int64, error) {
	return r.forumThreads.CountDocuments(ctx, bson.M{"forumId": forumID})
}

func (r *forumRepository) CountForumReplies(ctx context.Context, threadID primitive.ObjectID) (int64, error) {
	return r.forumReplies.CountDocuments(ctx, bson.M{"threadId": threadID})
}

func (r *forumRepository) CountForums(ctx context.Context, clubID primitive.ObjectID) (int64, error) {
	return r.forums.CountDocuments(ctx, bson.M{"clubId": clubID})
}
