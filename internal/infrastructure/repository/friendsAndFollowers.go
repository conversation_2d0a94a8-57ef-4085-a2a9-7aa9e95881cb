package repository

import (
	"context"
	"errors"
	"sync"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

type FriendsAndFollowersRepository interface {
	CheckIfFriendRequestSent(ctx context.Context, senderId, receiverId primitive.ObjectID) (bool, *models.FriendRequest, error)
	CreateFriendRequest(ctx context.Context, friendRequest models.FriendRequest) (bool, error)
	CheckIfAlreadyFollowing(ctx context.Context, followerId, followeeId primitive.ObjectID) (bool, *models.FollowersAndFollowee, error)
	CheckFollowingAndFriendsStatusConcurrent(ctx context.Context, user1Id, user2Id primitive.ObjectID) (bool, models.FriendshipStatus, error)
	AddFollower(ctx context.Context, followRequest models.FollowersAndFollowee) (bool, error)
	RemoveFollower(ctx context.Context, id primitive.ObjectID) (bool, error)
	CheckIfAlreadyFriends(ctx context.Context, user1Id, user2Id primitive.ObjectID) (bool, *models.Friends, error)
	CreateFriends(ctx context.Context, friend models.Friends) (bool, error)
	DeleteFriend(ctx context.Context, filter bson.M) (bool, error)
	DeleteFriendRequest(ctx context.Context, filter bson.M) (bool, error)
	AggregateFriends(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error)
	AggregateFollowers(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error)
	AggregateFriendRequests(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error)
	GetFriendsUserIds(ctx context.Context, userId primitive.ObjectID) ([]primitive.ObjectID, error)
}

type friendsAndFollowersRepository struct {
	friendsCollection       *mongo.Collection
	friendRequestCollection *mongo.Collection
	followersCollection     *mongo.Collection
}

func NewFriendsAndFollowersRepository(lc fx.Lifecycle, db database.Database) FriendsAndFollowersRepository {
	friendsCollection := db.Collection("friends")
	followersCollection := db.Collection("followers")
	friendRequestCollection := db.Collection("friendrequests")
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Friends and followers repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down friends and followers repository.")
			return nil
		},
	})
	return &friendsAndFollowersRepository{friendsCollection: friendsCollection, followersCollection: followersCollection, friendRequestCollection: friendRequestCollection}
}

func (r *friendsAndFollowersRepository) CheckIfFriendRequestSent(ctx context.Context, senderId, receiverId primitive.ObjectID) (bool, *models.FriendRequest, error) {
	var existingFriendRequest models.FriendRequest

	filter := bson.M{
		"$or": []bson.M{
			{"senderId": senderId, "receiverId": receiverId},
			{"senderId": receiverId, "receiverId": senderId},
		},
	}

	err := r.friendRequestCollection.FindOne(ctx, filter).Decode(&existingFriendRequest)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, nil, nil
		}
		return false, nil, err
	}

	return true, &existingFriendRequest, nil
}

func (r *friendsAndFollowersRepository) CreateFriendRequest(ctx context.Context, friendRequest models.FriendRequest) (bool, error) {
	friendRequest.ID = primitive.NewObjectID()
	_, err := r.friendRequestCollection.InsertOne(ctx, friendRequest)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, nil
		}
		return false, err
	}
	return true, err
}

func (r *friendsAndFollowersRepository) CheckIfAlreadyFollowing(ctx context.Context, followerId, followeeId primitive.ObjectID) (bool, *models.FollowersAndFollowee, error) {
	var existingFollower models.FollowersAndFollowee

	filter := bson.M{"followerId": followerId, "followeeId": followeeId}

	err := r.followersCollection.FindOne(ctx, filter).Decode(&existingFollower)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, nil, nil
		}
		return false, nil, err
	}
	return true, &existingFollower, nil
}

func (r *friendsAndFollowersRepository) AddFollower(ctx context.Context, followRequest models.FollowersAndFollowee) (bool, error) {
	followRequest.ID = primitive.NewObjectID()
	_, err := r.followersCollection.InsertOne(ctx, followRequest)
	if err != nil {
		return false, err
	}
	return true, err
}

func (r *friendsAndFollowersRepository) RemoveFollower(ctx context.Context, id primitive.ObjectID) (bool, error) {
	_, err := r.followersCollection.DeleteOne(ctx, bson.M{"_id": id}, options.Delete())
	if err != nil {
		return false, err
	}

	return true, err
}

func (r *friendsAndFollowersRepository) CreateFriends(ctx context.Context, friend models.Friends) (bool, error) {
	friend.ID = primitive.NewObjectID()
	_, err := r.friendsCollection.InsertOne(ctx, friend)
	if err != nil {
		return false, err
	}

	return true, nil
}

func (r *friendsAndFollowersRepository) CheckIfAlreadyFriends(ctx context.Context, user1Id, user2Id primitive.ObjectID) (bool, *models.Friends, error) {
	var existingFriends models.Friends
	filter := bson.M{
		"$or": []bson.M{
			{"senderId": user1Id, "receiverId": user2Id},
			{"senderId": user2Id, "receiverId": user1Id},
		},
	}

	err := r.friendsCollection.FindOne(ctx, filter).Decode(&existingFriends)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, nil, nil
		}
		return false, nil, err
	}

	return true, &existingFriends, nil
}

func (r *friendsAndFollowersRepository) CheckFollowingAndFriendsStatusConcurrent(ctx context.Context, user1Id, user2Id primitive.ObjectID) (bool, models.FriendshipStatus, error) {
	var wg sync.WaitGroup
	wg.Add(3)

	followChan := make(chan bool, 1)
	friendRequestChan := make(chan *models.FriendRequest, 1)
	friendsChan := make(chan *models.Friends, 1)
	errChan := make(chan error, 3)

	go func() {
		defer wg.Done()
		followingFilter := bson.M{
			"followerId": user1Id,
			"followeeId": user2Id,
		}
		var existingFollower models.FollowersAndFollowee
		err := r.followersCollection.FindOne(ctx, followingFilter).Decode(&existingFollower)
		if err == nil {
			followChan <- true
		} else if errors.Is(err, mongo.ErrNoDocuments) {
			followChan <- false
		} else {
			errChan <- err
		}
	}()

	go func() {
		defer wg.Done()
		friendRequestFilter := bson.M{
			"$or": []bson.M{
				{"senderId": user1Id, "receiverId": user2Id},
				{"senderId": user2Id, "receiverId": user1Id},
			},
		}
		var existingFriendRequest models.FriendRequest
		err := r.friendRequestCollection.FindOne(ctx, friendRequestFilter).Decode(&existingFriendRequest)
		if err == nil {
			friendRequestChan <- &existingFriendRequest
		} else if errors.Is(err, mongo.ErrNoDocuments) {
			friendRequestChan <- nil
		} else {
			errChan <- err
		}
	}()

	go func() {
		defer wg.Done()
		friendsFilter := bson.M{
			"$or": []bson.M{
				{"senderId": user1Id, "receiverId": user2Id},
				{"senderId": user2Id, "receiverId": user1Id},
			},
		}
		var existingFriends models.Friends
		err := r.friendsCollection.FindOne(ctx, friendsFilter).Decode(&existingFriends)
		if err == nil {
			friendsChan <- &existingFriends
		} else if errors.Is(err, mongo.ErrNoDocuments) {
			friendsChan <- nil
		} else {
			errChan <- err
		}
	}()

	go func() {
		wg.Wait()
		close(followChan)
		close(friendRequestChan)
		close(friendsChan)
		close(errChan)
	}()

	isFollowing := <-followChan
	err := <-errChan
	friendRequest := <-friendRequestChan
	friends := <-friendsChan

	if friendRequest != nil {
		if friendRequest.SenderID.Hex() == user1Id.Hex() && friendRequest.ReceiverID.Hex() == user2Id.Hex() {
			return isFollowing, models.FriendshipStatusRequestSent, nil
		} else {
			return isFollowing, models.FriendshipStatusPendingRequest, nil
		}
	}

	if friends != nil {
		return isFollowing, models.FriendshipStatusAccepted, nil
	}

	return isFollowing, models.FriendshipStatusNotFriend, err
}

func (r *friendsAndFollowersRepository) DeleteFriend(ctx context.Context, filter bson.M) (bool, error) {
	_, err := r.friendsCollection.DeleteOne(ctx, filter, options.Delete())
	if err != nil {
		return false, err
	}

	return true, err
}

func (r *friendsAndFollowersRepository) DeleteFriendRequest(ctx context.Context, filter bson.M) (bool, error) {
	_, err := r.friendRequestCollection.DeleteOne(ctx, filter, options.Delete())
	if err != nil {
		return false, err
	}

	return true, err
}

func (r *friendsAndFollowersRepository) AggregateFriends(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error) {
	cursor, err := r.friendsCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	return cursor, nil
}

func (r *friendsAndFollowersRepository) AggregateFollowers(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error) {
	cursor, err := r.followersCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	return cursor, nil
}

func (r *friendsAndFollowersRepository) AggregateFriendRequests(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error) {
	cursor, err := r.friendRequestCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	return cursor, nil
}

// add fields limited to senderId and receiverId
func (r *friendsAndFollowersRepository) GetFriendsUserIds(ctx context.Context, userId primitive.ObjectID) ([]primitive.ObjectID, error) {
	cursor, err := r.friendsCollection.Find(ctx, bson.M{"$or": []bson.M{{"senderId": userId}, {"receiverId": userId}}})
	if err != nil {
		return nil, err
	}
	var friends []models.Friends
	if err := cursor.All(ctx, &friends); err != nil {
		return nil, err
	}
	var userIds []primitive.ObjectID
	for _, friend := range friends {
		if friend.SenderID.Hex() == userId.Hex() {
			userIds = append(userIds, friend.ReceiverID)
		} else {
			userIds = append(userIds, friend.SenderID)
		}
	}
	return userIds, nil
}
