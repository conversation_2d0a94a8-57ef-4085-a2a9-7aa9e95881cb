package repository

import (
	"context"
	"errors"
	"fmt"

	"go.mongodb.org/mongo-driver/mongo/options"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/fx"
)

type GameRepository interface {
	CreateGame(ctx context.Context, game *models.Game) (*models.Game, error)
	GetMinifiedGames(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.MinifiedGame, error)
	GetGameByID(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error)
	UpdateGame(ctx context.Context, game *models.Game) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.Game, error)
	Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.Game, error)
	FindActiveGamesByUserID(ctx context.Context, userID primitive.ObjectID) ([]models.Game, error)
	CancelGames(ctx context.Context, gameIDs []primitive.ObjectID) error
	CreateManyGames(ctx context.Context, games []*models.Game) error
	EstimatedDocumentCount(ctx context.Context) (int64, error)
}

type gameRepository struct {
	collection *mongo.Collection
}

func NewGameRepository(lc fx.Lifecycle, db database.Database) GameRepository {
	collection := db.Collection("games")
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Game repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down game repository.")
			return nil
		},
	})
	return &gameRepository{collection: collection}
}

func (r *gameRepository) Collection() *mongo.Collection {
	return r.collection
}

func (r *gameRepository) CreateGame(ctx context.Context, game *models.Game) (*models.Game, error) {
	if game.ID == nil {
		game.ID = utils.AllocPtr(primitive.NewObjectID())
	}
	result, err := r.collection.InsertOne(ctx, game)
	if err != nil {
		return nil, fmt.Errorf("failed to create game: %w", err)
	}
	game.ID = utils.AllocPtr(result.InsertedID.(primitive.ObjectID))
	return game, nil
}

func (r *gameRepository) GetMinifiedGames(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.MinifiedGame, error) {
	cursor, err := r.collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to find games: %w", err)
	}
	defer cursor.Close(ctx)

	var games []*models.MinifiedGame
	for cursor.Next(ctx) {
		var game models.MinifiedGame
		if err := cursor.Decode(&game); err != nil {
			return nil, fmt.Errorf("failed to decode game: %w", err)
		}
		games = append(games, &game)
	}
	return games, nil
}

func (r *gameRepository) CreateManyGames(ctx context.Context, games []*models.Game) error {
	documents := make([]interface{}, len(games))
	for i, game := range games {
		if game.ID == nil {
			game.ID = utils.AllocPtr(primitive.NewObjectID())
		}
		documents[i] = game
	}

	_, err := r.collection.InsertMany(ctx, documents)
	if err != nil {
		return fmt.Errorf("failed to create games: %w", err)
	}

	return nil
}

func (r *gameRepository) GetGameByID(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error) {
	var game models.Game
	err := r.collection.FindOne(ctx, bson.M{"_id": gameID}).Decode(&game)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, fmt.Errorf("game not found")
		}
		return nil, fmt.Errorf("failed to get game: %w", err)
	}
	return &game, nil
}

func (r *gameRepository) FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.Game, error) {
	var game models.Game
	err := r.collection.FindOne(ctx, filter, opts...).Decode(&game)
	if err != nil {
		return nil, err
	}
	return &game, nil
}

func (r *gameRepository) Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.Game, error) {
	cursor, err := r.collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var games []*models.Game
	if err := cursor.All(ctx, &games); err != nil {
		return nil, err
	}
	return games, nil
}

func (r *gameRepository) UpdateGame(ctx context.Context, game *models.Game) error {
	_, err := r.collection.UpdateOne(ctx, bson.M{"_id": game.ID}, bson.M{"$set": game})
	if err != nil {
		return fmt.Errorf("failed to update game: %w", err)
	}
	return nil
}

func (r *gameRepository) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := r.collection.UpdateOne(ctx, filter, update)
	return err
}

func (r *gameRepository) FindActiveGamesByUserID(ctx context.Context, userID primitive.ObjectID) ([]models.Game, error) {
	cursor, err := r.collection.Find(ctx, bson.M{
		"gameStatus": bson.M{"$nin": []string{string(constants.GameStatusEnum.ENDED), string(constants.GameStatusEnum.CANCELLED)}},
		"players": bson.M{
			"$elemMatch": bson.M{
				"userId": userID,
				"status": constants.PlayerStatusEnum.ACCEPTED,
			},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to find active games: %w", err)
	}

	var games []models.Game
	if err := cursor.All(ctx, &games); err != nil {
		return nil, fmt.Errorf("failed to decode active games: %w", err)
	}
	return games, nil
}

func (r *gameRepository) CancelGames(ctx context.Context, gameIDs []primitive.ObjectID) error {
	_, err := r.collection.UpdateMany(
		ctx,
		bson.M{"_id": bson.M{"$in": gameIDs}},
		bson.M{"$set": bson.M{"gameStatus": constants.GameStatusEnum.CANCELLED}},
	)
	if err != nil {
		return fmt.Errorf("failed to cancel games: %w", err)
	}
	return nil
}

func (r *gameRepository) EstimatedDocumentCount(ctx context.Context) (int64, error) {
	return r.collection.EstimatedDocumentCount(ctx)
}
