package repository

import (
	"context"
	"errors"
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/fx"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

type GameSeriesRepository interface {
	GetGameSeriesByID(ctx context.Context, gameSeriesID primitive.ObjectID) (*models.GameSeries, error)
	CreateGameSeries(ctx context.Context, game *models.GameSeries) (*models.GameSeries, error)
	Collection() *mongo.Collection
	Update(ctx context.Context, gameSeries *models.GameSeries) error
}

type gameSeriesRepository struct {
	collection *mongo.Collection
}

func NewGameSeriesRepository(lc fx.Lifecycle, db database.Database) GameSeriesRepository {
	collection := db.Collection("game_series")
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Game series repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down game series repository.")
			return nil
		},
	})
	return &gameSeriesRepository{collection: collection}
}

func (r *gameSeriesRepository) Collection() *mongo.Collection {
	return r.collection
}

func (r *gameSeriesRepository) CreateGameSeries(ctx context.Context, gameSeries *models.GameSeries) (*models.GameSeries, error) {
	if gameSeries.ID == nil {
		gameSeries.ID = utils.AllocPtr(primitive.NewObjectID())
	}
	result, err := r.collection.InsertOne(ctx, gameSeries)
	if err != nil {
		return nil, fmt.Errorf("failed to create game series: %w", err)
	}
	gameSeries.ID = utils.AllocPtr(result.InsertedID.(primitive.ObjectID))
	return gameSeries, nil
}

func (r *gameSeriesRepository) GetGameSeriesByID(ctx context.Context, gameSeriesID primitive.ObjectID) (*models.GameSeries, error) {
	var gameSeries models.GameSeries
	err := r.collection.FindOne(ctx, bson.M{"_id": gameSeriesID}).Decode(&gameSeries)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, fmt.Errorf("game series not found")
		}
		return nil, fmt.Errorf("failed to get game series: %w", err)
	}
	return &gameSeries, nil
}

func (r *gameSeriesRepository) Update(ctx context.Context, gameSeries *models.GameSeries) error {
	_, err := r.collection.UpdateOne(ctx, bson.M{"_id": gameSeries.ID}, bson.M{"$set": gameSeries})
	return err
}
