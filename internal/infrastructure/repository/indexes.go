package repository

import (
	"context"
	"reflect"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

func normalizeBsonD(d bson.D) bson.D {
	normalized := bson.D{}
	for _, elem := range d {
		switch v := elem.Value.(type) {
		case int:
			normalized = append(normalized, bson.E{Key: elem.Key, Value: int32(v)})
		case int64:
			normalized = append(normalized, bson.E{Key: elem.Key, Value: int32(v)})
		default:
			normalized = append(normalized, bson.E{Key: elem.Key, Value: v})
		}
	}
	return normalized
}

func compareBsonD(a, b bson.D) bool {
	res := reflect.DeepEqual(normalizeBsonD(a), normalizeBsonD(b))
	return res
}

func EnsureIndexes(ctx context.Context, collection *mongo.Collection, indexes []mongo.IndexModel) error {
	cursor, err := collection.Indexes().List(ctx)
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	existingIndexes := make([]bson.D, 0)
	for cursor.Next(ctx) {
		var index bson.M
		if err := cursor.Decode(&index); err != nil {
			return err
		}
		if key, ok := index["key"].(bson.M); ok {
			keyD := bson.D{}
			for k, v := range key {
				keyD = append(keyD, bson.E{Key: k, Value: v})
			}
			existingIndexes = append(existingIndexes, keyD)
		}
	}

	for _, index := range indexes {
		exists := false
		for _, existing := range existingIndexes {
			if compareBsonD(index.Keys.(bson.D), existing) {
				exists = true
				break
			}
		}
		if !exists {
			_, err := collection.Indexes().CreateOne(ctx, index)
			if err != nil {
				return err
			}
			zlog.Warn(ctx, "Created missing index:", zap.Any("Keys", index.Keys))
		}
	}
	return nil
}
