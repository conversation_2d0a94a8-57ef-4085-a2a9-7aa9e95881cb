package repository

import (
	"context"
	"errors"
	"regexp"
	"strings"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

type InstitutionRepository interface {
	Create(ctx context.Context, input models.CreateInstitutionInput) (*models.Institution, error)
	FindByID(ctx context.Context, id primitive.ObjectID) (*models.Institution, error)
	FindBySlug(ctx context.Context, slug string) (*models.Institution, error)
	Search(ctx context.Context, filter models.InstitutionFilter) ([]*models.Institution, error)
	Update(ctx context.Context, institution *models.Institution) error
	Delete(ctx context.Context, id primitive.ObjectID) error
}

type instituteRepo struct {
	collection *mongo.Collection
}

func NewInstitutionRepository(lc fx.Lifecycle, db database.Database) InstitutionRepository {
	collection := db.Collection("institutions")

	repo := &instituteRepo{
		collection: collection,
	}

	indexModels := []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "slug", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{
				{Key: "name", Value: "text"},
				{Key: "domains", Value: "text"},
				{Key: "country", Value: "text"},
				{Key: "state", Value: "text"},
				{Key: "city", Value: "text"},
			},
			Options: options.Index().SetWeights(bson.M{
				"name":    10,
				"domains": 5,
				"country": 2,
				"state":   2,
				"city":    2,
			}),
		},
	}

	err := EnsureIndexes(context.Background(), collection, indexModels)
	if err != nil {
		zlog.Error(context.Background(), "Failed to ensure indexes for institutions collection", err)
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Institution repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down Institution repository.")
			return nil
		},
	})

	return repo
}

func (s *instituteRepo) Create(ctx context.Context, input models.CreateInstitutionInput) (*models.Institution, error) {
	if input.Name == "" {
		return nil, errors.New("institution name is required")
	}

	slug := createSlug(input.Name)

	existing, _ := s.FindBySlug(ctx, slug)
	if existing != nil {
		slug = slug + "-" + primitive.NewObjectID().Hex()[:8]
	}

	institution := &models.Institution{
		ID:      primitive.NewObjectID(),
		Name:    input.Name,
		Domains: input.Domains,
		Country: input.Country,
		State:   input.State,
		City:    input.City,
		Slug:    slug,
	}

	_, err := s.collection.InsertOne(ctx, institution)
	if err != nil {
		return nil, err
	}

	return institution, nil
}

func (s *instituteRepo) FindByID(ctx context.Context, id primitive.ObjectID) (*models.Institution, error) {
	var institution models.Institution

	filter := bson.M{
		"_id":        id,
		"deleted_at": nil,
	}

	err := s.collection.FindOne(ctx, filter).Decode(&institution)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return &institution, nil
}

func (s *instituteRepo) FindBySlug(ctx context.Context, slug string) (*models.Institution, error) {
	var institution models.Institution

	filter := bson.M{
		"slug":       slug,
		"deleted_at": nil,
	}

	err := s.collection.FindOne(ctx, filter).Decode(&institution)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return &institution, nil
}

func (s *instituteRepo) Search(ctx context.Context, filter models.InstitutionFilter) ([]*models.Institution, error) {
	if len(filter.Query) < 3 {
		return nil, errors.New("search query must be at least 3 characters long")
	}

	if filter.Limit <= 0 {
		filter.Limit = 10
	}

	query := filter.Query

	searchFilter := bson.M{
		"$or": []bson.M{
			{"name": bson.M{"$regex": query, "$options": "i"}},
			{"domains": bson.M{"$elemMatch": bson.M{"$regex": query, "$options": "i"}}},
			{"country": bson.M{"$regex": query, "$options": "i"}},
			{"state": bson.M{"$regex": query, "$options": "i"}},
			{"city": bson.M{"$regex": query, "$options": "i"}},
		},
		"deleted_at": nil,
	}

	findOptions := options.Find()
	findOptions.SetLimit(filter.Limit)
	findOptions.SetSkip(filter.Offset)

	findOptions.SetSort(bson.D{{Key: "name", Value: 1}})

	cursor, err := s.collection.Find(ctx, searchFilter, findOptions)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var institutions []*models.Institution
	if err = cursor.All(ctx, &institutions); err != nil {
		return nil, err
	}

	return institutions, nil
}

func (s *instituteRepo) Update(ctx context.Context, institution *models.Institution) error {
	if institution.ID == primitive.NilObjectID {
		return errors.New("institution ID is required")
	}

	filter := bson.M{"_id": institution.ID}

	update := bson.M{
		"$set": institution,
	}

	_, err := s.collection.UpdateOne(ctx, filter, update)
	return err
}

func (s *instituteRepo) Delete(ctx context.Context, id primitive.ObjectID) error {
	// TODO implement me
	panic("implement me")
}

func createSlug(name string) string {
	slug := strings.ToLower(name)

	reg := regexp.MustCompile("[^a-z0-9]+")
	slug = reg.ReplaceAllString(slug, "-")

	slug = strings.Trim(slug, "-")

	return slug
}
