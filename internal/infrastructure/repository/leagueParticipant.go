package repository

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/fx"
)

type LeagueParticipantRepository interface {
	CreateParticipant(ctx context.Context, participant *models.LeagueParticipant) error
	GetParticipantsByLeagueID(ctx context.Context, leagueID primitive.ObjectID) ([]*models.LeagueParticipant, error)
	GetParticipantByUserAndLeagueID(ctx context.Context, userID, leagueID primitive.ObjectID) (*models.LeagueParticipant, error)
	GetParticipantsCountInLeague(ctx context.Context, leagueID primitive.ObjectID) int64
}

type leagueParticipantRepository struct {
	collection *mongo.Collection
}

func NewLeagueParticipantRepository(lc fx.Lifecycle, db database.Database) LeagueParticipantRepository {
	collection := db.Collection("leagueParticipants")
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "League participant repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down league participant repository.")
			return nil
		},
	})
	return &leagueParticipantRepository{collection: collection}
}

func (r *leagueParticipantRepository) CreateParticipant(ctx context.Context, participant *models.LeagueParticipant) error {
	participant.ID = primitive.NewObjectID()
	_, err := r.collection.InsertOne(ctx, participant)
	return err
}

func (r *leagueParticipantRepository) GetParticipantsByLeagueID(ctx context.Context, leagueID primitive.ObjectID) ([]*models.LeagueParticipant, error) {
	cursor, err := r.collection.Find(ctx, bson.M{"leagueId": leagueID})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var participants []*models.LeagueParticipant
	if err = cursor.All(ctx, &participants); err != nil {
		return nil, err
	}

	return participants, nil
}

func (r *leagueParticipantRepository) GetParticipantsCountInLeague(ctx context.Context, leagueID primitive.ObjectID) int64 {
	registratonCount, err := r.collection.CountDocuments(ctx, bson.M{
		"leagueId": leagueID,
	})
	if err != nil {
		return 0
	}
	return registratonCount
}

func (r *leagueParticipantRepository) GetParticipantByUserAndLeagueID(ctx context.Context, userID, leagueID primitive.ObjectID) (*models.LeagueParticipant, error) {
	var participant models.LeagueParticipant
	err := r.collection.FindOne(ctx, bson.M{"userId": userID, "leagueId": leagueID}).Decode(&participant)
	if err != nil {
		return nil, err
	}
	return &participant, nil
}
