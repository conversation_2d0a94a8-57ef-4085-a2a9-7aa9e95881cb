package repository

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

type LeagueRepository interface {
	IsEmailRegisteredInLeague(ctx context.Context, leagueID primitive.ObjectID, email string) (bool, error)
	CreateLeague(ctx context.Context, league *models.League) (*models.League, error)
	RegisterUserForLeague(ctx context.Context, leagueID, userID primitive.ObjectID, registrationData []*models.RegistrationFieldData) error
	UnregisterUserFromLeague(ctx context.Context, leagueID, userID primitive.ObjectID) error
	GetLeagueByID(ctx context.Context, id primitive.ObjectID) (*models.League, error)
	GetLeaguesByStatus(ctx context.Context, statuses []models.LeagueStatus, page, pageSize int, sortDirection string) (*models.PaginatedLeagues, error)
	GetLeagueLeaderboard(ctx context.Context, leagueID primitive.ObjectID, page, pageSize int) (*models.LeagueLeaderboardPage, error)
	GetUserLeagueResult(ctx context.Context, leagueID, userID primitive.ObjectID) (*models.LeagueLeaderboardEntry, error)
}

type leagueRepository struct {
	collection            *mongo.Collection
	userDailyActivityRepo UserDailyActivityRepository
	userActivitiesRepo    UserActivitiesRepository
	leagueParticipantRepo LeagueParticipantRepository
	userRepo              UserRepository
}

func NewLeagueRepository(lc fx.Lifecycle, db database.Database,
	userDailyActivityRepo UserDailyActivityRepository, userActivitiesRepo UserActivitiesRepository,
	leagueParticipantRepo LeagueParticipantRepository, userRepo UserRepository,
) LeagueRepository {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "League repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down league repository.")
			return nil
		},
	})
	return &leagueRepository{
		collection:            db.Collection("leagues"),
		leagueParticipantRepo: leagueParticipantRepo,
		userDailyActivityRepo: userDailyActivityRepo,
		userActivitiesRepo:    userActivitiesRepo,
		userRepo:              userRepo,
	}
}

func (r *leagueRepository) CreateLeague(ctx context.Context, league *models.League) (*models.League, error) {
	result, err := r.collection.InsertOne(ctx, league)
	if err != nil {
		return nil, err
	}
	league.ID = result.InsertedID.(primitive.ObjectID)
	return league, nil
}

func (r *leagueRepository) RegisterUserForLeague(ctx context.Context, leagueID, userID primitive.ObjectID, registrationData []*models.RegistrationFieldData) error {
	err := r.leagueParticipantRepo.CreateParticipant(ctx, &models.LeagueParticipant{
		LeagueID:         leagueID,
		ID:               primitive.NewObjectID(),
		UserID:           userID,
		JoinedAt:         time.Now(),
		RegistrationData: registrationData,
	})
	return err
}

func (r *leagueRepository) UnregisterUserFromLeague(ctx context.Context, leagueID, userID primitive.ObjectID) error {
	_, err := r.collection.UpdateOne(
		ctx,
		bson.M{"_id": leagueID},
		bson.M{"$pull": bson.M{"participants": bson.M{"userId": userID}}},
	)
	return err
}

func (r *leagueRepository) GetLeagueByID(ctx context.Context, id primitive.ObjectID) (*models.League, error) {
	var league models.League
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&league)
	if err != nil {
		return nil, err
	}
	return &league, nil
}

func (r *leagueRepository) GetLeaguesByStatus(ctx context.Context, statuses []models.LeagueStatus, page, pageSize int, sortDirection string) (*models.PaginatedLeagues, error) {
	currentTime := time.Now().UTC()

	filterConditions := []bson.M{}

	for _, status := range statuses {
		switch status {
		case models.LeagueStatusUpcoming:
			filterConditions = append(filterConditions, bson.M{
				"registrationStart": bson.M{"$gt": currentTime},
			})
		case models.LeagueStatusRegistrationOpen:
			filterConditions = append(filterConditions, bson.M{
				"registrationStart": bson.M{"$lte": currentTime},
				"registrationEnd":   bson.M{"$gt": currentTime},
			})
		case models.LeagueStatusLive:
			filterConditions = append(filterConditions, bson.M{
				"leagueStart": bson.M{"$lte": currentTime},
				"leagueEnd":   bson.M{"$gt": currentTime},
			})
		case models.LeagueStatusEnded:
			filterConditions = append(filterConditions, bson.M{
				"leagueEnd": bson.M{"$lte": currentTime},
			})
		}
	}

	filter := bson.M{"$or": filterConditions}

	sort := bson.D{{Key: "leagueStart", Value: 1}}
	if sortDirection == "desc" {
		sort = bson.D{{Key: "leagueStart", Value: -1}}
	}

	options := options.Find().
		SetSort(sort).
		SetSkip(int64((page - 1) * pageSize)).
		SetLimit(int64(pageSize))

	cursor, err := r.collection.Find(ctx, filter, options)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var leagues []*models.League
	if err = cursor.All(ctx, &leagues); err != nil {
		return nil, err
	}

	totalCount, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, err
	}

	return &models.PaginatedLeagues{
		League:     leagues,
		TotalCount: utils.AllocPtr(int(totalCount)),
	}, nil
}

func (r *leagueRepository) GetLeagueLeaderboard(ctx context.Context, leagueID primitive.ObjectID, page, pageSize int) (*models.LeagueLeaderboardPage, error) {
	league, err := r.GetLeagueByID(ctx, leagueID)
	if err != nil {
		return nil, fmt.Errorf("failed to get league: %w", err)
	}

	participants, err := r.leagueParticipantRepo.GetParticipantsByLeagueID(ctx, leagueID)
	if err != nil {
		return nil, fmt.Errorf("failed to get participants: %w", err)
	}

	participantIDs := getParticipantUserIDs(participants)

	leagueStartTime := league.LeagueStart
	leagueEndTime := league.LeagueEnd

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"_id": bson.M{"$in": participantIDs},
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "user_activities",
			"let":  bson.M{"userId": "$_id"},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{
					"$expr": bson.M{
						"$and": bson.A{
							bson.M{"$eq": bson.A{"$userId", "$$userId"}},
							bson.M{"$gte": bson.A{"$timestamp", leagueStartTime}},
							bson.M{"$lte": bson.A{"$timestamp", leagueEndTime}},
						},
					},
				}},
				bson.M{"$group": bson.M{
					"_id":        "$activityType",
					"totalCoins": bson.M{"$sum": "$xpEarned"},
				}},
				bson.M{"$project": bson.M{
					"_id":      0,
					"activity": "$_id",
					"coins":    "$totalCoins",
				}},
				bson.M{"$group": bson.M{
					"_id":             nil,
					"totalCoins":      bson.M{"$sum": "$coins"},
					"activitySummary": bson.M{"$push": "$$ROOT"},
				}},
			},
			"as": "userCoins",
		}}},
		{{Key: "$addFields", Value: bson.M{
			"statikCoins": bson.M{
				"$ifNull": bson.A{
					bson.M{"$arrayElemAt": bson.A{"$userCoins.totalCoins", 0}},
					0,
				},
			},
			"activitySummary": bson.M{
				"$ifNull": bson.A{
					bson.M{"$arrayElemAt": bson.A{"$userCoins.activitySummary", 0}},
					bson.A{},
				},
			},
		}}},
	}

	allUsersCursor, err := r.userRepo.AggregateProjected(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("failed to aggregate all users: %w", err)
	}
	defer allUsersCursor.Close(ctx)

	var allUsers []struct {
		ID              primitive.ObjectID `bson:"_id"`
		Name            string             `bson:"name"`
		Username        string             `bson:"username"`
		ProfileImageUrl string             `bson:"profileImageUrl"`
		Rating          int                `bson:"rating"`
		Badge           string             `bson:"badge"`
		GlobalRank      int                `bson:"globalRank"`
		StatikCoins     int                `bson:"statikCoins"`
		ActivitySummary []bson.M           `bson:"activitySummary"`
	}

	if err = allUsersCursor.All(ctx, &allUsers); err != nil {
		return nil, fmt.Errorf("failed to decode all users: %w", err)
	}

	sort.Slice(allUsers, func(i, j int) bool {
		if allUsers[i].StatikCoins != allUsers[j].StatikCoins {
			return allUsers[i].StatikCoins > allUsers[j].StatikCoins
		}
		return allUsers[i].Rating > allUsers[j].Rating
	})

	userRanks := make(map[primitive.ObjectID]int)
	currentRank := 1
	for i := 0; i < len(allUsers); i++ {
		if i > 0 && (allUsers[i].StatikCoins != allUsers[i-1].StatikCoins) {
			currentRank = i + 1
		}
		userRanks[allUsers[i].ID] = currentRank
	}

	startIdx := (page - 1) * pageSize
	endIdx := startIdx + pageSize
	if startIdx >= len(allUsers) {
		return &models.LeagueLeaderboardPage{
			Participants: []*models.LeagueLeaderboardEntry{},
			TotalCount:   utils.AllocPtr(len(participants)),
		}, nil
	}
	if endIdx > len(allUsers) {
		endIdx = len(allUsers)
	}
	paginatedUsers := allUsers[startIdx:endIdx]

	var leaderboardEntries []*models.LeagueLeaderboardEntry
	for _, u := range paginatedUsers {
		entry := &models.LeagueLeaderboardEntry{
			User: &models.User{
				ID:              u.ID,
				Name:            utils.AllocPtr(u.Name),
				Username:        u.Username,
				ProfileImageURL: utils.AllocPtr(u.ProfileImageUrl),
				Rating:          utils.AllocPtr(u.Rating),
			},
			StatikCoins:     u.StatikCoins,
			Rank:            userRanks[u.ID],
			ActivitySummary: convertActivitySummary(u.ActivitySummary),
		}
		leaderboardEntries = append(leaderboardEntries, entry)
	}

	return &models.LeagueLeaderboardPage{
		Participants: leaderboardEntries,
		TotalCount:   utils.AllocPtr(len(participants)),
	}, nil
}

func convertActivitySummary(summary []bson.M) []*models.ActivitySummaryEntry {
	var result []*models.ActivitySummaryEntry
	for _, entry := range summary {
		activity, _ := entry["activity"].(string)
		coins, _ := entry["coins"].(int32)

		result = append(result, &models.ActivitySummaryEntry{
			Activity: activity,
			Coins:    int(coins),
		})
	}
	return result
}

func (r *leagueRepository) GetUserLeagueResult(ctx context.Context, leagueID, userID primitive.ObjectID) (*models.LeagueLeaderboardEntry, error) {
	league, err := r.GetLeagueByID(ctx, leagueID)
	if err != nil {
		return nil, fmt.Errorf("failed to get league: %w", err)
	}

	participants, err := r.leagueParticipantRepo.GetParticipantsByLeagueID(ctx, leagueID)
	if err != nil {
		return nil, fmt.Errorf("failed to get participants: %w", err)
	}

	isParticipant := false
	for _, p := range participants {
		if p.UserID == userID {
			isParticipant = true
			break
		}
	}

	if !isParticipant {
		return nil, fmt.Errorf("user is not a participant in this league")
	}

	leagueStartTime := league.LeagueStart
	leagueEndTime := league.LeagueEnd

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"_id": userID,
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "user_activities",
			"let":  bson.M{"userId": "$_id"},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{
					"$expr": bson.M{
						"$and": bson.A{
							bson.M{"$eq": bson.A{"$userId", "$$userId"}},
							bson.M{"$gte": bson.A{"$timestamp", leagueStartTime}},
							bson.M{"$lte": bson.A{"$timestamp", leagueEndTime}},
						},
					},
				}},
				bson.M{"$group": bson.M{
					"_id":        nil,
					"totalCoins": bson.M{"$sum": "$xpEarned"},
				}},
			},
			"as": "userCoins",
		}}},
		{{Key: "$addFields", Value: bson.M{
			"statikCoins": bson.M{
				"$ifNull": bson.A{
					bson.M{"$arrayElemAt": bson.A{"$userCoins.totalCoins", 0}},
					0,
				},
			},
		}}},
		{{Key: "$project", Value: bson.M{
			"user": bson.M{
				"_id":             "$_id",
				"name":            "$name",
				"username":        "$username",
				"profileImageUrl": "$profileImageUrl",
				"rating":          "$rating",
				"badge":           "$badge",
				"globalRank":      "$globalRank",
			},
			"statikCoins": "$statikCoins",
		}}},
	}

	cursor, err := r.userRepo.AggregateProjected(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("failed to aggregate user result: %w", err)
	}
	defer cursor.Close(ctx)

	var results []*models.LeagueLeaderboardEntry
	if err = cursor.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("failed to decode user result: %w", err)
	}

	if len(results) == 0 {
		return nil, fmt.Errorf("user not found")
	}

	userResult := results[0]

	rankPipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"_id": bson.M{"$in": getParticipantUserIDs(participants)},
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "user_activities",
			"let":  bson.M{"userId": "$_id"},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{
					"$expr": bson.M{
						"$and": bson.A{
							bson.M{"$eq": bson.A{"$userId", "$$userId"}},
							bson.M{"$gte": bson.A{"$timestamp", leagueStartTime}},
							bson.M{"$lte": bson.A{"$timestamp", leagueEndTime}},
						},
					},
				}},
				bson.M{"$group": bson.M{
					"_id":        nil,
					"totalCoins": bson.M{"$sum": "$xpEarned"},
				}},
			},
			"as": "userCoins",
		}}},
		{{Key: "$addFields", Value: bson.M{
			"statikCoins": bson.M{
				"$ifNull": bson.A{
					bson.M{"$arrayElemAt": bson.A{"$userCoins.totalCoins", 0}},
					0,
				},
			},
		}}},
		{{Key: "$match", Value: bson.M{
			"statikCoins": bson.M{"$gt": userResult.StatikCoins},
		}}},
		{{Key: "$count", Value: "count"}},
	}

	cursor, err = r.userRepo.AggregateProjected(ctx, rankPipeline)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate rank: %w", err)
	}
	defer cursor.Close(ctx)

	var rankResults []struct{ Count int }
	if err = cursor.All(ctx, &rankResults); err != nil {
		return nil, fmt.Errorf("failed to decode rank results: %w", err)
	}

	userResult.Rank = 1
	if len(rankResults) > 0 {
		userResult.Rank = rankResults[0].Count + 1
	}

	return userResult, nil
}

func (r *leagueRepository) IsEmailRegisteredInLeague(ctx context.Context, leagueID primitive.ObjectID, email string) (bool, error) {
	participants, err := r.leagueParticipantRepo.GetParticipantsByLeagueID(ctx, leagueID)
	if err != nil {
		return false, fmt.Errorf("failed to get participants: %w", err)
	}

	for _, participant := range participants {
		if participant.RegistrationData != nil {
			for _, field := range participant.RegistrationData {
				if strings.Contains(strings.ToLower(field.Name), "email") {
					if len(field.Values) > 0 {
						if strings.EqualFold(field.Values[0], email) {
							return true, nil
						}
					}
				}
			}
		}
	}

	return false, nil
}

func getParticipantUserIDs(participants []*models.LeagueParticipant) []primitive.ObjectID {
	ids := make([]primitive.ObjectID, len(participants))
	for i, p := range participants {
		ids[i] = p.UserID
	}
	return ids
}
