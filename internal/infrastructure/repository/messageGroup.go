package repository

import (
	"context"
	"errors"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

type messageGroupRepository struct {
	collection *mongo.Collection
}

type MessageGroupRepository interface {
	GetByID(ctx context.Context, id primitive.ObjectID) (*models.MessageGroup, error)
	GetByAlias(ctx context.Context, alias string) (*models.MessageGroup, error)
	Create(ctx context.Context, messageGroup *models.MessageGroup) error
	AddParticipant(ctx context.Context, groupID primitive.ObjectID, userID *primitive.ObjectID) error
	RemoveParticipant(ctx context.Context, groupID, userID primitive.ObjectID) error
	GetIndividualUsers(ctx context.Context, userID primitive.ObjectID, page, pageSize int) ([]*models.MessageGroup, error)
	UpdateLastMessage(ctx context.Context, groupID primitive.ObjectID, message *models.Message) error
	UpdateLastMessageRead(ctx context.Context, group *models.MessageGroup) error
}

func (r *messageGroupRepository) UpdateLastMessageRead(ctx context.Context, group *models.MessageGroup) error {
	if group == nil {
		return errors.New("group is nil")
	}
	if group.LastMessageRead == nil {
		group.LastMessageRead = []*models.MessageRead{}
	}
	filter := bson.M{"_id": group.ID}
	update := bson.M{"$set": bson.M{"lastMessageRead": group.LastMessageRead, "updatedAt": time.Now()}}
	_, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func NewMessageGroupRepository(lc fx.Lifecycle, db database.Database) MessageGroupRepository {
	r := &messageGroupRepository{
		collection: db.Collection("messageGroups"),
	}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Message group repository initialized and ready.")
			go func() {
				err = r.syncIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for message group repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down message group repository.")
			return nil
		},
	})
	return r
}

func (r *messageGroupRepository) syncIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{{Key: "updatedAt", Value: 1}}},
	})
}

func (r *messageGroupRepository) GetByID(ctx context.Context, id primitive.ObjectID) (*models.MessageGroup, error) {
	var messageGroup models.MessageGroup
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&messageGroup)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &messageGroup, nil
}

// add function UpdateLastMessage
func (r *messageGroupRepository) UpdateLastMessage(ctx context.Context, groupID primitive.ObjectID, message *models.Message) error {
	filter := bson.M{"_id": groupID}
	update := bson.M{"$set": bson.M{"lastMessage": message, "updatedAt": time.Now()}}
	_, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (r *messageGroupRepository) GetByAlias(ctx context.Context, alias string) (*models.MessageGroup, error) {
	var messageGroup models.MessageGroup
	err := r.collection.FindOne(ctx, bson.M{"alias": alias}).Decode(&messageGroup)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &messageGroup, nil
}

func (r *messageGroupRepository) Create(ctx context.Context, messageGroup *models.MessageGroup) error {
	_, err := r.collection.InsertOne(ctx, messageGroup)
	if err != nil {
		return err
	}
	return nil
}

func (r *messageGroupRepository) GetIndividualUsers(ctx context.Context, userID primitive.ObjectID, page, pageSize int) ([]*models.MessageGroup, error) {
	filter := bson.M{
		"members":   userID,
		"groupType": models.GroupTypeIndividual,
	}

	opts := options.Find().
		SetSort(bson.D{{Key: "updatedAt", Value: -1}}).
		SetLimit(int64(pageSize)).
		SetSkip(int64((page - 1) * pageSize))
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var groups []*models.MessageGroup
	if err = cursor.All(ctx, &groups); err != nil {
		return nil, err
	}
	return groups, nil
}

func (r *messageGroupRepository) AddParticipant(ctx context.Context, groupID primitive.ObjectID, userID *primitive.ObjectID) error {
	if groupID.IsZero() || userID.IsZero() {
		return errors.New("invalid group ID or user ID")
	}

	update := bson.M{
		"$addToSet": bson.M{"members": userID},
		"$set":      bson.M{"updatedAt": time.Now()},
	}
	opts := options.Update().SetUpsert(true)

	result, err := r.collection.UpdateOne(ctx, bson.M{"_id": groupID}, update, opts)
	if err != nil {
		return fmt.Errorf("failed to add participant: %w", err)
	}

	if result.MatchedCount == 0 && result.UpsertedCount == 0 {
		return fmt.Errorf("message group not found")
	}

	return nil
}

func (r *messageGroupRepository) RemoveParticipant(ctx context.Context, groupID, userID primitive.ObjectID) error {
	update := bson.M{
		"$pull": bson.M{
			"members": userID,
		},
		"$set": bson.M{
			"updatedAt": time.Now(),
		},
	}

	result, err := r.collection.UpdateOne(
		ctx,
		bson.M{"_id": groupID},
		update,
	)
	if err != nil {
		return err
	}

	if result.MatchedCount == 0 {
		return errors.New("message group not found")
	}

	return nil
}
