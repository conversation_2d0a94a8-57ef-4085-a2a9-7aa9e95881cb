package repository

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

type messagesRepository struct {
	collection *mongo.Collection
}

type MessagesRepository interface {
	GetMessagesByGroupID(ctx context.Context, groupID primitive.ObjectID, lastMessageId *primitive.ObjectID, pageSize int64, sortValue int) ([]*models.Message, error)
	CreateMessage(ctx context.Context, message *models.Message) (primitive.ObjectID, error)
	getLatestMessage(ctx context.Context, groupID primitive.ObjectID) (*models.Message, error)
}

func NewMessagesRepository(lc fx.Lifecycle, db database.Database) MessagesRepository {
	r := &messagesRepository{
		collection: db.Collection("messages"),
	}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Messages repository initialized and ready.")
			go func() {
				err = r.syncIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for messages repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down messages repository.")
			return nil
		},
	})
	return r
}

func (r *messagesRepository) getLatestMessage(ctx context.Context, groupID primitive.ObjectID) (*models.Message, error) {
	filter := bson.M{"groupId": groupID}
	cursor := r.collection.FindOne(ctx, filter, options.FindOne().SetSort(bson.D{{Key: "createdAt", Value: -1}}))
	if cursor.Err() != nil {
		return nil, cursor.Err()
	}
	var message models.Message
	if err := cursor.Decode(&message); err != nil {
		return nil, err
	}
	return &message, nil
}

func (r *messagesRepository) syncIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{
			{Key: "groupId", Value: 1},
			{Key: "createdAt", Value: 1},
		}},
	})
}

func (r *messagesRepository) CreateMessage(ctx context.Context, message *models.Message) (primitive.ObjectID, error) {
	result, err := r.collection.InsertOne(ctx, message)
	if err != nil {
		return primitive.ObjectID{}, err
	}

	return result.InsertedID.(primitive.ObjectID), nil
}

func (r *messagesRepository) GetMessagesByGroupID(ctx context.Context, groupID primitive.ObjectID, lastMessageId *primitive.ObjectID, pageSize int64, sortValue int) ([]*models.Message, error) {
	matchStage := bson.D{{Key: "groupId", Value: groupID}}

	if lastMessageId != nil {
		if sortValue == -1 {
			matchStage = append(matchStage, bson.E{Key: "_id", Value: bson.M{"$lt": lastMessageId}})
		} else {
			matchStage = append(matchStage, bson.E{Key: "_id", Value: bson.M{"$gt": lastMessageId}})
		}
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: matchStage}},
		{{Key: "$sort", Value: bson.D{{Key: "createdAt", Value: sortValue}}}},
		{{Key: "$limit", Value: pageSize}},
		{{Key: "$lookup", Value: bson.D{
			{Key: "from", Value: "users"},
			{Key: "localField", Value: "sender"},
			{Key: "foreignField", Value: "_id"},
			{Key: "as", Value: "senderInfo"},
		}}},
		{{Key: "$unwind", Value: bson.D{
			{Key: "path", Value: "$senderInfo"},
			{Key: "preserveNullAndEmptyArrays", Value: true},
		}}},
	}

	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var messages []*models.Message
	if err = cursor.All(ctx, &messages); err != nil {
		return nil, err
	}

	return messages, nil
}
