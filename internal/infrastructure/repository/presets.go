package repository

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/mongo"
)

type PresetsRepository interface {
	CreateGlobalPreset(context.Context, *models.GlobalPreset) error
	UpdateGlobalPreset(context.Context, bson.M, bson.M) error
	GetGlobalPresetByID(context.Context, primitive.ObjectID) (*models.GlobalPreset, error)
	GetGlobalPresetByIdentifier(context.Context, string) (*models.GlobalPreset, error)
	GetGlobalPresets(context.Context, int, int) (*models.GlobalPresets, error)
	BulkUpdateGlobalPresets(context.Context, []*models.GlobalPreset) error

	CreateUserPreset(context.Context, *models.UserPreset) error
	UpdateUserPreset(context.Context, bson.M, bson.M) error
	GetUserPresetByID(context.Context, primitive.ObjectID) (*models.UserPreset, error)
	GetUserPresetByIdentifier(context.Context, string, primitive.ObjectID) (*models.UserPreset, error)
	GetUserPresetCategories(context.Context, primitive.ObjectID) (*models.AllPlayedPresetsOutput, error)
	GetUserPresets(context.Context, primitive.ObjectID, bool, int, int) (*models.UserPresets, error)
	BulkUpdateUserPresets(context.Context, []*models.UserPreset) error

	CreateUserPresetStats(context.Context, *models.UserPresetStats) error
	UpdateUserPresetStats(context.Context, bson.M, bson.M) error
	GetUserPresetStats(context.Context, string, time.Time, primitive.ObjectID) (*models.UserPresetStats, error)
	GetUserPresetStatsByDates(context.Context, time.Time, time.Time, int, primitive.ObjectID, string) ([]*models.UserPresetDayStats, error)
	BulkUpdateUserPresetStats(context.Context, []*models.UserPresetStats) error

	GetBulkGlobalPresets(ctx context.Context, identifiers []string) (map[string]*models.GlobalPreset, error)
	GetBulkUserPresets(ctx context.Context, identifiers []string, userID primitive.ObjectID) (map[string]*models.UserPreset, error)
	DeleteSavedPreset(ctx context.Context, presetId primitive.ObjectID) error
}

type presetsRepository struct {
	globalPresetsCollection   *mongo.Collection
	userPresetsCollection     *mongo.Collection
	userPresetStatsCollection *mongo.Collection
}

func NewPresetsRepository(lc fx.Lifecycle, db database.Database) PresetsRepository {
	globalPresetsCollection := db.Collection("globalpresets")
	userPresetsCollection := db.Collection("userpresets")
	userPresetStatsCollection := db.Collection("userpresetstats")
	r := &presetsRepository{globalPresetsCollection, userPresetsCollection, userPresetStatsCollection}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Presets repository initialized and ready.")
			go func() {
				err = r.syncGlobalPresetIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for global preset repository: %v", err)
				}
				err = r.syncUserPresetIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for user preset repository: %v", err)
				}
				err = r.syncUserPresetStatsIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for user preset stats repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down presets repository.")
			return nil
		},
	})
	return r
}

func (r *presetsRepository) syncGlobalPresetIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.globalPresetsCollection, []mongo.IndexModel{
		{Keys: bson.D{{Key: "identifier", Value: 1}}, Options: options.Index().SetUnique(true)},
	})
}

func (r *presetsRepository) syncUserPresetIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.userPresetsCollection, []mongo.IndexModel{
		{Keys: bson.D{{Key: "globalPresetId", Value: 1}, {Key: "userId", Value: 1}}, Options: options.Index().SetUnique(true)},
		{Keys: bson.D{{Key: "identifier", Value: 1}, {Key: "userId", Value: 1}}, Options: options.Index().SetUnique(true)},
		{Keys: bson.D{{Key: "userId", Value: 1}}},
	})
}

func (r *presetsRepository) syncUserPresetStatsIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.userPresetStatsCollection, []mongo.IndexModel{
		{Keys: bson.D{{Key: "identifier", Value: 1}, {Key: "userId", Value: 1}, {Key: "date", Value: 1}}, Options: options.Index().SetUnique(true)},
		{Keys: bson.D{{Key: "globalPresetId", Value: 1}, {Key: "userId", Value: 1}}},
		{Keys: bson.D{{Key: "userId", Value: 1}}},
		{Keys: bson.D{{Key: "date", Value: 1}, {Key: "userId", Value: 1}}},
	})
}

func (r *presetsRepository) UpdateGlobalPreset(ctx context.Context, filter, update bson.M) error {
	_, err := r.globalPresetsCollection.UpdateOne(ctx, filter, update)
	return err
}

func (r *presetsRepository) CreateGlobalPreset(ctx context.Context, globalPreset *models.GlobalPreset) error {
	if globalPreset == nil {
		return fmt.Errorf("global preset is nil")
	}
	if globalPreset.ID == primitive.NilObjectID {
		globalPreset.ID = primitive.NewObjectID()
	}
	globalPreset.CreatedAt = utils.AllocPtr(time.Now())
	globalPreset.UpdatedAt = utils.AllocPtr(time.Now())
	_, err := r.globalPresetsCollection.InsertOne(ctx, globalPreset)
	return err
}

func (r *presetsRepository) UpdateUserPreset(ctx context.Context, filter, update bson.M) error {
	_, err := r.userPresetsCollection.UpdateOne(ctx, filter, update)
	return err
}

func (r *presetsRepository) CreateUserPreset(ctx context.Context, userPreset *models.UserPreset) error {
	if userPreset == nil {
		return fmt.Errorf("user preset is nil")
	}
	if userPreset.ID == primitive.NilObjectID {
		userPreset.ID = primitive.NewObjectID()
	}
	userPreset.CreatedAt = utils.AllocPtr(time.Now())
	userPreset.UpdatedAt = utils.AllocPtr(time.Now())
	_, err := r.userPresetsCollection.InsertOne(ctx, userPreset)
	return err
}

func (r *presetsRepository) UpdateUserPresetStats(ctx context.Context, filter, update bson.M) error {
	_, err := r.userPresetStatsCollection.UpdateOne(ctx, filter, update)
	return err
}

func (r *presetsRepository) CreateUserPresetStats(ctx context.Context, userPresetStats *models.UserPresetStats) error {
	if userPresetStats == nil {
		return fmt.Errorf("user preset stats is nil")
	}
	if userPresetStats.ID == primitive.NilObjectID {
		userPresetStats.ID = primitive.NewObjectID()
	}
	_, err := r.userPresetStatsCollection.InsertOne(ctx, userPresetStats)
	return err
}

func (r *presetsRepository) GetGlobalPresetByID(ctx context.Context, id primitive.ObjectID) (*models.GlobalPreset, error) {
	var globalPreset models.GlobalPreset
	err := r.globalPresetsCollection.FindOne(ctx, bson.M{"_id": id}).Decode(&globalPreset)
	if err != nil {
		return nil, err
	}

	return &globalPreset, nil
}

func (r *presetsRepository) GetUserPresetByID(ctx context.Context, id primitive.ObjectID) (*models.UserPreset, error) {
	var userPreset models.UserPreset
	err := r.userPresetsCollection.FindOne(ctx, bson.M{"_id": id}).Decode(&userPreset)
	if err != nil {
		return nil, err
	}
	return &userPreset, nil
}

func (r *presetsRepository) GetGlobalPresetByIdentifier(ctx context.Context, identifier string) (*models.GlobalPreset, error) {
	var globalPreset models.GlobalPreset
	err := r.globalPresetsCollection.FindOne(ctx, bson.M{"identifier": identifier}).Decode(&globalPreset)
	if err != nil {
		return nil, err
	}
	return &globalPreset, nil
}

func (r *presetsRepository) GetUserPresetStatsByDates(ctx context.Context, startDate, endDate time.Time, duration int, userID primitive.ObjectID, identifier string) ([]*models.UserPresetDayStats, error) {
	filter := bson.M{
		"date": bson.M{
			"$gt":  startDate,
			"$lte": endDate,
		},
		"userId":     userID,
		"identifier": identifier,
	}

	opts := options.Find().SetSort(bson.D{{Key: "date", Value: 1}})

	cursor, err := r.userPresetStatsCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("error querying user preset stats: %w", err)
	}
	defer cursor.Close(ctx)

	result := make([]*models.UserPresetDayStats, 0, duration)
	for cursor.Next(ctx) {
		var stat models.UserPresetStats
		if err := cursor.Decode(&stat); err != nil {
			return nil, fmt.Errorf("error decoding user preset stats: %w", err)
		}
		dayStats := &models.UserPresetDayStats{
			Date:            stat.Date,
			UserPresetStats: utils.AllocPtr(stat),
		}
		result = append(result, dayStats)
	}
	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("error iterating through cursor: %w", err)
	}

	return result, nil
}

func (r *presetsRepository) GetUserPresetStats(ctx context.Context, identfier string, date time.Time, userID primitive.ObjectID) (*models.UserPresetStats, error) {
	filter := bson.M{"date": date, "userId": userID, "identifier": identfier}
	var userPresetStats models.UserPresetStats
	err := r.userPresetStatsCollection.FindOne(ctx, filter).Decode(&userPresetStats)
	if err != nil {
		return nil, err
	}

	return &userPresetStats, nil
}

func (r *presetsRepository) GetGlobalPresets(ctx context.Context, page, pageSize int) (*models.GlobalPresets, error) {
	skip := (page - 1) * pageSize
	limit := int64(pageSize)

	filter := bson.M{}

	opts := options.Find().
		SetSkip(int64(skip)).
		SetLimit(limit).
		SetSort(bson.D{{Key: "updatedAt", Value: -1}})

	cursor, err := r.globalPresetsCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var globalPresets []*models.GlobalPreset
	if err := cursor.All(ctx, &globalPresets); err != nil {
		return nil, err
	}

	totalCount, err := r.globalPresetsCollection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, err
	}

	return &models.GlobalPresets{
		GlobalPresets: globalPresets,
		TotalCount:    int(totalCount),
	}, nil
}

func (r *presetsRepository) GetUserPresetByIdentifier(ctx context.Context, identifier string, userID primitive.ObjectID) (*models.UserPreset, error) {
	filter := bson.M{"identifier": identifier, "userId": userID}
	var userPreset models.UserPreset
	err := r.userPresetsCollection.FindOne(ctx, filter).Decode(&userPreset)
	if err != nil {
		return nil, err
	}
	return &userPreset, nil
}

func (r *presetsRepository) GetUserPresets(ctx context.Context, userID primitive.ObjectID, savedOnly bool, page, pageSize int) (*models.UserPresets, error) {
	skip := (page - 1) * pageSize
	limit := int64(pageSize)

	filter := bson.M{"userId": userID}
	if savedOnly {
		filter["saved"] = true
	}

	opts := options.Find().
		SetSkip(int64(skip)).
		SetLimit(limit).
		SetSort(bson.D{{Key: "updatedAt", Value: -1}})

	cursor, err := r.userPresetsCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var userPresets []*models.UserPreset
	if err := cursor.All(ctx, &userPresets); err != nil {
		return nil, err
	}

	totalCount, err := r.userPresetsCollection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, err
	}

	return &models.UserPresets{
		UserPresets: userPresets,
		TotalCount:  int(totalCount),
	}, nil
}

func (r *presetsRepository) BulkUpdateGlobalPresets(ctx context.Context, globalPresets []*models.GlobalPreset) error {
	if len(globalPresets) == 0 {
		return nil
	}

	var operations []mongo.WriteModel
	for _, globalPreset := range globalPresets {
		update := bson.M{"$set": globalPreset}
		operation := mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": globalPreset.ID}).
			SetUpdate(update).
			SetUpsert(true)
		operations = append(operations, operation)
	}

	_, err := r.globalPresetsCollection.BulkWrite(ctx, operations, options.BulkWrite().SetOrdered(false))
	return err
}

func (r *presetsRepository) BulkUpdateUserPresets(ctx context.Context, userPresets []*models.UserPreset) error {
	if len(userPresets) == 0 {
		return nil
	}

	var operations []mongo.WriteModel
	for _, userPreset := range userPresets {
		update := bson.M{"$set": userPreset}
		operation := mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": userPreset.ID}).
			SetUpdate(update).
			SetUpsert(true)
		operations = append(operations, operation)
	}

	_, err := r.userPresetsCollection.BulkWrite(ctx, operations, options.BulkWrite().SetOrdered(false))
	return err
}

func (r *presetsRepository) BulkUpdateUserPresetStats(ctx context.Context, userPresetStats []*models.UserPresetStats) error {
	if len(userPresetStats) == 0 {
		return nil
	}

	var operations []mongo.WriteModel
	for _, stats := range userPresetStats {
		update := bson.M{"$set": stats}
		operation := mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": stats.ID}).
			SetUpdate(update).
			SetUpsert(true)
		operations = append(operations, operation)
	}

	_, err := r.userPresetStatsCollection.BulkWrite(ctx, operations, options.BulkWrite().SetOrdered(false))
	return err
}

func (r *presetsRepository) GetUserPresetCategories(ctx context.Context, userID primitive.ObjectID) (*models.AllPlayedPresetsOutput, error) {
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"userId": userID}}},
		{{Key: "$group", Value: bson.M{
			"_id":         "$identifier",
			"avgTime":     bson.M{"$avg": "$curAvgTime"},
			"presetCount": bson.M{"$sum": 1},
		}}},
		{{Key: "$project", Value: bson.M{
			"identifier":  "$_id",
			"avgTime":     1,
			"presetCount": 1,
			"_id":         0,
		}}},
	}

	cursor, err := r.userPresetsCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var categories []*models.PlayedPresets
	if err := cursor.All(ctx, &categories); err != nil {
		return nil, err
	}

	count := len(categories)
	return &models.AllPlayedPresetsOutput{
		Presets: categories,
		Count:   &count,
	}, nil
}

func (r *presetsRepository) GetBulkGlobalPresets(ctx context.Context, identifiers []string) (map[string]*models.GlobalPreset, error) {
	filter := bson.M{"identifier": bson.M{"$in": identifiers}}
	cursor, err := r.globalPresetsCollection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	result := make(map[string]*models.GlobalPreset)
	for cursor.Next(ctx) {
		var preset models.GlobalPreset
		if err := cursor.Decode(&preset); err != nil {
			return nil, err
		}
		result[preset.Identifier] = &preset
	}

	return result, nil
}

func (r *presetsRepository) GetBulkUserPresets(ctx context.Context, identifiers []string, userID primitive.ObjectID) (map[string]*models.UserPreset, error) {
	filter := bson.M{
		"identifier": bson.M{"$in": identifiers},
		"userId":     userID,
	}
	cursor, err := r.userPresetsCollection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	result := make(map[string]*models.UserPreset)
	for cursor.Next(ctx) {
		var preset models.UserPreset
		if err := cursor.Decode(&preset); err != nil {
			return nil, err
		}
		result[preset.Identifier] = &preset
	}

	return result, nil
}

func (r *presetsRepository) DeleteSavedPreset(ctx context.Context, presetId primitive.ObjectID) error {
	_, err := r.userPresetsCollection.DeleteOne(ctx, bson.M{"_id": presetId})
	if err != nil {
		return err
	}
	return nil
}
