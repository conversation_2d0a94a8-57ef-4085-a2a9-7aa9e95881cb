package repository

import (
	"context"
	"errors"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

type PuzzleRepository interface {
	Create(ctx context.Context, puzzle *models.Puzzle) error
	FindByID(ctx context.Context, id primitive.ObjectID) (*models.Puzzle, error)
	FindOne(ctx context.Context, filter bson.M) (*models.Puzzle, error)
	Update(ctx context.Context, puzzle *models.Puzzle) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.Puzzle, error)
}

type puzzleRepository struct {
	collection *mongo.Collection
}

func (r *puzzleRepository) syncIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{{Key: "challengeNumber", Value: 1}}},
	})
}

func NewPuzzleRepository(lc fx.Lifecycle, db database.Database) PuzzleRepository {
	r := &puzzleRepository{collection: db.Collection("puzzles")}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Puzzle repository initialized and ready.")
			go func() {
				err = r.syncIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for puzzle repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down puzzle repository.")
			return nil
		},
	})
	return r
}

func (r *puzzleRepository) Create(ctx context.Context, puzzle *models.Puzzle) error {
	result, err := r.collection.InsertOne(ctx, puzzle)
	if err != nil {
		return err
	}

	puzzle.ID = result.InsertedID.(primitive.ObjectID)
	return nil
}

func (r *puzzleRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.Puzzle, error) {
	var puzzle models.Puzzle
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&puzzle)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &puzzle, nil
}

func (r *puzzleRepository) FindOne(ctx context.Context, filter bson.M) (*models.Puzzle, error) {
	var puzzle models.Puzzle
	err := r.collection.FindOne(ctx, filter).Decode(&puzzle)
	if err != nil {
		return nil, err
	}
	return &puzzle, nil
}

func (r *puzzleRepository) Update(ctx context.Context, puzzle *models.Puzzle) error {
	_, err := r.collection.ReplaceOne(
		ctx,
		bson.M{"_id": puzzle.ID},
		puzzle,
	)
	return err
}

func (r *puzzleRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	_, err := r.collection.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (r *puzzleRepository) List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.Puzzle, error) {
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var puzzles []*models.Puzzle
	if err = cursor.All(ctx, &puzzles); err != nil {
		return nil, err
	}

	return puzzles, nil
}

func (r *puzzleRepository) Count(ctx context.Context, filter bson.M) (int64, error) {
	return r.collection.CountDocuments(ctx, filter)
}
