package repository

import (
	"context"
	"errors"
	"fmt"
	"time"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"

	"go.mongodb.org/mongo-driver/mongo/options"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type PuzzleGameRepository interface {
	CreatePuzzleGame(ctx context.Context, game *models.PuzzleGame) (*models.PuzzleGame, error)
	GetPuzzleGameByID(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)
	UpdatePuzzleGame(ctx context.Context, game *models.PuzzleGame) error
	FindActivePuzzleGamesByUserID(ctx context.Context, userID primitive.ObjectID) ([]models.PuzzleGame, error)
	CancelPuzzleGames(ctx context.Context, gameIDs []primitive.ObjectID) error
	CreateManyPuzzleGames(ctx context.Context, games []*models.PuzzleGame) error
	FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.PuzzleGame, error)
	UpdateOne(ctx context.Context, filter, update bson.M) error
	FindMany(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.PuzzleGame, error)
	GetMinifiedPuzzleGames(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.MinifiedPuzzleGame, error) // Added for GetGamesByUserV2
}

type puzzleGameRepository struct {
	collection *mongo.Collection
}

func (r *puzzleGameRepository) syncPuzzleGameIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{{Key: "challengeNumber", Value: 1}}},
	})
}

func NewPuzzleGameRepository(lc fx.Lifecycle, db database.Database) PuzzleGameRepository {
	r := &puzzleGameRepository{collection: db.Collection("puzzleGames")}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Puzzle game repository initialized and ready.")
			go func() {
				err = r.syncPuzzleGameIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for puzzle game repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down puzzle game repository.")
			return nil
		},
	})

	return r
}

func (r *puzzleGameRepository) CreatePuzzleGame(ctx context.Context, game *models.PuzzleGame) (*models.PuzzleGame, error) {
	game.CreatedAt = utils.AllocPtr(time.Now())
	game.UpdatedAt = utils.AllocPtr(time.Now())
	if game.ID == primitive.NilObjectID {
		game.ID = primitive.NewObjectID()
	}
	result, err := r.collection.InsertOne(ctx, game)
	if err != nil {
		return nil, fmt.Errorf("failed to create puzzle game: %w", err)
	}
	game.ID = result.InsertedID.(primitive.ObjectID)
	return game, nil
}

func (r *puzzleGameRepository) CreateManyPuzzleGames(ctx context.Context, games []*models.PuzzleGame) error {
	documents := make([]interface{}, len(games))
	for i, game := range games {
		if game.ID == primitive.NilObjectID {
			game.ID = primitive.NewObjectID()
		}
		documents[i] = game
	}

	_, err := r.collection.InsertMany(ctx, documents)
	if err != nil {
		return fmt.Errorf("failed to create puzzle games: %w", err)
	}

	return nil
}

func (r *puzzleGameRepository) GetPuzzleGameByID(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	var game models.PuzzleGame
	err := r.collection.FindOne(ctx, bson.M{"_id": gameID}).Decode(&game)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, fmt.Errorf("puzzle game not found")
		}
		return nil, fmt.Errorf("failed to get puzzle game: %w", err)
	}
	return &game, nil
}

func (r *puzzleGameRepository) UpdatePuzzleGame(ctx context.Context, game *models.PuzzleGame) error {
	game.UpdatedAt = utils.AllocPtr(time.Now())
	_, err := r.collection.UpdateOne(ctx, bson.M{"_id": game.ID}, bson.M{"$set": game})
	if err != nil {
		return fmt.Errorf("failed to update puzzle game: %w", err)
	}
	return nil
}

func (r *puzzleGameRepository) FindActivePuzzleGamesByUserID(ctx context.Context, userID primitive.ObjectID) ([]models.PuzzleGame, error) {
	cursor, err := r.collection.Find(ctx, bson.M{
		"gameStatus": bson.M{"$nin": []string{string(constants.GameStatusEnum.ENDED), string(constants.GameStatusEnum.CANCELLED)}},
		"players": bson.M{
			"$elemMatch": bson.M{
				"userId": userID,
				"status": constants.PlayerStatusEnum.ACCEPTED,
			},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to find active puzzle games: %w", err)
	}

	var games []models.PuzzleGame
	if err := cursor.All(ctx, &games); err != nil {
		return nil, fmt.Errorf("failed to decode active puzzle games: %w", err)
	}
	return games, nil
}

func (r *puzzleGameRepository) CancelPuzzleGames(ctx context.Context, gameIDs []primitive.ObjectID) error {
	_, err := r.collection.UpdateMany(
		ctx,
		bson.M{"_id": bson.M{"$in": gameIDs}},
		bson.M{"$set": bson.M{"gameStatus": constants.GameStatusEnum.CANCELLED}},
	)
	if err != nil {
		return fmt.Errorf("failed to cancel puzzle games: %w", err)
	}
	return nil
}

func (r *puzzleGameRepository) FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.PuzzleGame, error) {
	var game models.PuzzleGame
	err := r.collection.FindOne(ctx, filter, opts...).Decode(&game)
	if err != nil {
		return nil, err
	}
	return &game, nil
}

func (r *puzzleGameRepository) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := r.collection.UpdateOne(ctx, filter, update)
	return err
}

func (r *puzzleGameRepository) FindMany(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.PuzzleGame, error) {
	cursor, err := r.collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var games []*models.PuzzleGame
	if err := cursor.All(ctx, &games); err != nil {
		return nil, err
	}
	return games, nil
}

func (r *puzzleGameRepository) GetMinifiedPuzzleGames(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.MinifiedPuzzleGame, error) {
	cursor, err := r.collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to find minified puzzle games: %w", err)
	}
	defer cursor.Close(ctx)

	var games []*models.MinifiedPuzzleGame
	if err := cursor.All(ctx, &games); err != nil {
		return nil, fmt.Errorf("failed to decode minified puzzle games: %w", err)
	}
	return games, nil
}
