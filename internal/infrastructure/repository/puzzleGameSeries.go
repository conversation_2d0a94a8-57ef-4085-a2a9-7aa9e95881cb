package repository

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/fx"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
)

type PuzzleGameSeriesRepository interface {
	GetPuzzleGameSeriesByID(ctx context.Context, gameSeriesID primitive.ObjectID) (*models.GameSeries, error)
	CreatePuzzleGameSeries(ctx context.Context, game *models.GameSeries) (*models.GameSeries, error)
	Update(ctx context.Context, gameSeries *models.GameSeries) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
}

type puzzleGameSeriesRepository struct {
	collection *mongo.Collection
}

func NewPuzzleGameSeriesRepository(lc fx.Lifecycle, db database.Database) PuzzleGameSeriesRepository {
	collection := db.Collection("game_series")
	r := &puzzleGameSeriesRepository{collection: collection}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Puzzle game series repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down puzzle game series repository.")
			return nil
		},
	})

	return r
}

func (r *puzzleGameSeriesRepository) CreatePuzzleGameSeries(ctx context.Context, gameSeries *models.GameSeries) (*models.GameSeries, error) {
	if gameSeries.ID == nil {
		gameSeries.ID = utils.AllocPtr(primitive.NewObjectID())
	}
	result, err := r.collection.InsertOne(ctx, gameSeries)
	if err != nil {
		return nil, fmt.Errorf("failed to create game series: %w", err)
	}
	gameSeries.ID = utils.AllocPtr(result.InsertedID.(primitive.ObjectID))
	return gameSeries, nil
}

func (r *puzzleGameSeriesRepository) GetPuzzleGameSeriesByID(ctx context.Context, gameSeriesID primitive.ObjectID) (*models.GameSeries, error) {
	var gameSeries models.GameSeries
	err := r.collection.FindOne(ctx, bson.M{"_id": gameSeriesID}).Decode(&gameSeries)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, fmt.Errorf("game series not found")
		}
		return nil, fmt.Errorf("failed to get game series: %w", err)
	}
	return &gameSeries, nil
}

func (r *puzzleGameSeriesRepository) Update(ctx context.Context, gameSeries *models.GameSeries) error {
	_, err := r.collection.UpdateOne(ctx, bson.M{"_id": gameSeries.ID}, bson.M{"$set": gameSeries})
	return err
}

func (r *puzzleGameSeriesRepository) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := r.collection.UpdateOne(ctx, filter, update)
	return err
}
