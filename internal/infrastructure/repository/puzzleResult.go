package repository

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

type PuzzleResultRepository interface {
	Create(ctx context.Context, puzzleResult *models.PuzzleResult) error
	FindByID(ctx context.Context, id primitive.ObjectID) (*models.PuzzleResult, error)
	FindOne(ctx context.Context, filter bson.M) (*models.PuzzleResult, error)
	Update(ctx context.Context, puzzleResult *models.PuzzleResult) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.PuzzleResult, error)
	GetUserRank(ctx context.Context, score int, puzzleId primitive.ObjectID) *int
	GetSubmissionsBetweenDates(ctx context.Context, startDate, endDate string) ([]*models.PuzzleResult, error)

	GetSubmissionsBetweenDatesByType(ctx context.Context, startDate, endDate string, puzzleType models.PuzzleType) ([]*models.PuzzleResult, error)
}

type puzzleResultRepository struct {
	collection *mongo.Collection
}

func (r *puzzleResultRepository) syncPuzzleResultIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{
			{Key: "puzzleId", Value: 1},
			{Key: "challengeNumber", Value: 1},
			{Key: "resultStatus", Value: 1},
			{Key: "score", Value: -1},
		}},
	})
}

func NewPuzzleResultRepository(lc fx.Lifecycle, db database.Database) PuzzleResultRepository {
	r := &puzzleResultRepository{collection: db.Collection("puzzleresults")}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Puzzle result repository initialized and ready.")
			go func() {
				err = r.syncPuzzleResultIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for puzzle result repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down puzzle result repository.")
			return nil
		},
	})
	return r
}

func (r *puzzleResultRepository) Create(ctx context.Context, puzzleResult *models.PuzzleResult) error {
	puzzleResult.ID = primitive.NewObjectID()
	_, err := r.collection.InsertOne(ctx, puzzleResult)
	return err
}

func (r *puzzleResultRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.PuzzleResult, error) {
	var puzzleResult models.PuzzleResult
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&puzzleResult)
	if err != nil {
		return nil, err
	}
	return &puzzleResult, nil
}

func (r *puzzleResultRepository) FindOne(ctx context.Context, filter bson.M) (*models.PuzzleResult, error) {
	var puzzleResult models.PuzzleResult
	err := r.collection.FindOne(ctx, filter).Decode(&puzzleResult)
	if err != nil {
		return nil, err
	}
	return &puzzleResult, nil
}

func (r *puzzleResultRepository) FindByChallengeAndUser(ctx context.Context, challengeID, userID primitive.ObjectID) (*models.PuzzleResult, error) {
	var puzzleResult models.PuzzleResult
	filter := bson.M{
		"$and": []bson.M{
			{"userId": userID},
			{"puzzleId": challengeID},
		},
	}

	err := r.collection.FindOne(ctx, filter).Decode(&puzzleResult)
	if err != nil {
		return nil, err
	}
	return &puzzleResult, nil
}

func (r *puzzleResultRepository) Update(ctx context.Context, puzzleResult *models.PuzzleResult) error {
	filter := bson.M{
		"userId":   puzzleResult.UserID,
		"puzzleId": puzzleResult.PuzzleID,
	}

	opts := options.Update().SetUpsert(true)

	_, err := r.collection.UpdateOne(
		ctx,
		filter,
		bson.M{"$set": bson.M{
			"timeSpent":         puzzleResult.TimeSpent,
			"statikCoinsEarned": puzzleResult.StatikCoinsEarned,
			"userId":            puzzleResult.UserID,
			"puzzleId":          puzzleResult.PuzzleID,
			"completedAt":       puzzleResult.CompletedAt,
			"puzzleDate":        puzzleResult.PuzzleDate,
		}},
		opts,
	)
	return err
}

func (r *puzzleResultRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	_, err := r.collection.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (r *puzzleResultRepository) List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.PuzzleResult, error) {
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var results []*models.PuzzleResult
	if err = cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	return results, nil
}

func (r *puzzleResultRepository) GetUserRank(ctx context.Context, score int, puzzleId primitive.ObjectID) *int {
	filter := bson.M{
		"puzzleId":     puzzleId,
		"resultStatus": bson.M{"$ne": models.ResultStatusAttempted},
		"score":        bson.M{"$gt": score},
	}

	count, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return utils.AllocPtr(0)
	}

	if count == 0 {
		count = 1
	}

	return utils.AllocPtr(int(count))
}

func (r *puzzleResultRepository) GetSubmissionsBetweenDates(ctx context.Context, startDate, endDate string) ([]*models.PuzzleResult, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("unauthorized: user not authenticated")
	}
	filter := bson.M{
		"userId": userId,
		"puzzleDate": bson.M{
			"$gte": startDate,
			"$lte": endDate,
		},
		"$or": []bson.M{
			{"puzzleType": models.PuzzleTypeCrossMath},
			{"puzzleType": nil},
		},
	}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var results []*models.PuzzleResult
	if err = cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	return results, nil
}

func (r *puzzleResultRepository) GetSubmissionsBetweenDatesByType(ctx context.Context, startDate, endDate string, puzzleType models.PuzzleType) ([]*models.PuzzleResult, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("unauthorized: user not authenticated")
	}
	filter := bson.M{
		"userId": userId,
		"puzzleDate": bson.M{
			"$gte": startDate,
			"$lte": endDate,
		},
	}
	if puzzleType == models.PuzzleTypeCrossMath {
		filter["$or"] = []bson.M{
			{"puzzleType": puzzleType},
			{"puzzleType": nil},
		}
	} else {
		filter["puzzleType"] = puzzleType
	}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var results []*models.PuzzleResult
	if err = cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	return results, nil
}
