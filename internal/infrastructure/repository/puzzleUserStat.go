package repository

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

type PuzzleUserStatRepository interface {
	Create(ctx context.Context, userStat *models.PuzzleUserStats) error
	FindByID(ctx context.Context, id primitive.ObjectID) (*models.PuzzleUserStats, error)
	FindByUserID(ctx context.Context, userID primitive.ObjectID, puzzleType models.PuzzleType) (*models.PuzzleUserStats, error)
	Update(ctx context.Context, userStat *models.PuzzleUserStats) error
	List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.PuzzleUserStats, error)
}

type puzzleUserStatRepository struct {
	collection *mongo.Collection
}

func (r *puzzleUserStatRepository) syncIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{{Key: "userId", Value: 1}}},
	})
}

func NewPuzzleUserStatRepository(lc fx.Lifecycle, db database.Database) PuzzleUserStatRepository {
	r := &puzzleUserStatRepository{collection: db.Collection("puzzleuserstats")}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Puzzle user stat repository initialized and ready.")
			go func() {
				err = r.syncIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for puzzle user stat repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down puzzle user stat repository.")
			return nil
		},
	})
	return r
}

func (r *puzzleUserStatRepository) Create(ctx context.Context, userStat *models.PuzzleUserStats) error {
	userStat.ID = primitive.NewObjectID()
	_, err := r.collection.InsertOne(ctx, userStat)
	return err
}

func (r *puzzleUserStatRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.PuzzleUserStats, error) {
	var userStat models.PuzzleUserStats
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&userStat)
	if err != nil {
		return nil, err
	}
	return &userStat, nil
}

func (r *puzzleUserStatRepository) FindByUserID(ctx context.Context, userID primitive.ObjectID, puzzleType models.PuzzleType) (*models.PuzzleUserStats, error) {
	var userStat models.PuzzleUserStats
	filter := bson.M{"userId": userID}
	if puzzleType == models.PuzzleTypeCrossMath {
		filter["$or"] = []bson.M{
			{"puzzleType": puzzleType},
			{"puzzleType": nil},
		}
	} else {
		filter["puzzleType"] = puzzleType
	}

	err := r.collection.FindOne(ctx, filter).Decode(&userStat)
	if err != nil {
		return nil, err
	}
	return &userStat, nil
}

func (r *puzzleUserStatRepository) Update(ctx context.Context, userStat *models.PuzzleUserStats) error {
	filter := bson.M{"_id": userStat.ID}
	opts := options.Update().SetUpsert(true)

	update := bson.M{}
	if userStat.BestTime != nil {
		update["bestTime"] = *userStat.BestTime
	}
	if userStat.NumOfSubmission != nil {
		update["numOfSubmission"] = *userStat.NumOfSubmission
	}
	if userStat.AverageTime != nil {
		update["averageTime"] = *userStat.AverageTime
	}
	update["userId"] = userStat.UserID

	_, err := r.collection.UpdateOne(
		ctx,
		filter,
		bson.M{"$set": update},
		opts,
	)
	return err
}

func (r *puzzleUserStatRepository) List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.PuzzleUserStats, error) {
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var results []*models.PuzzleUserStats
	if err = cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	return results, nil
}
