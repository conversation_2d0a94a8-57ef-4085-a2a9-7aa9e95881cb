package repository

import (
	"context"
	"errors"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/fx"
)

type ReferralsRepository interface {
	Create(ctx context.Context, referral *models.Referral) error
	GetUserByReferralCode(ctx context.Context, referralCode string) (*models.User, error)
	FindByID(ctx context.Context, id primitive.ObjectID) (*models.Referral, error)
}

type referralsRepository struct {
	collection *mongo.Collection
}

func NewReferralsRepository(lc fx.Lifecycle, db database.Database) ReferralsRepository {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Referrals repository initialized and ready.")
			return db.<PERSON>(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down referrals repository.")
			return nil
		},
	})
	return &referralsRepository{
		collection: db.Collection("referrals"),
	}
}

func (r *referralsRepository) Create(ctx context.Context, referral *models.Referral) error {
	timeNow := time.Now()
	referral.ReferredAt = &timeNow

	_, err := r.collection.InsertOne(ctx, referral)
	if err != nil {
		return err
	}
	return nil
}

func (r *referralsRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.Referral, error) {
	var referral models.Referral
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&referral)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, fmt.Errorf("referral not found: %w", err)
		}
		return nil, fmt.Errorf("error finding referral: %w", err)
	}
	return &referral, nil
}

func (r *referralsRepository) GetUserByReferralCode(ctx context.Context, referralCode string) (*models.User, error) {
	filter := bson.M{"referralCode": referralCode}

	userCollection := r.collection.Database().Collection("users")

	var user models.User
	err := userCollection.FindOne(ctx, filter).Decode(&user)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, fmt.Errorf("invalid referral code")
		}
		return nil, fmt.Errorf("failed to find user by referral code: %v", err)
	}

	return &user, nil
}
