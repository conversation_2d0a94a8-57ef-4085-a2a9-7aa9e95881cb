package repository

import (
	"go.uber.org/fx"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
)

type RepositoryFactory struct {
	UserRepository                        UserRepository
	DeletedUserRepository                 DeletedUserRepository
	GameRepository                        GameRepository
	ContestRepository                     ContestRepository
	DailyChallengeRepository              DailyChallengeRepository
	DailyChallengeResultRepository        DailyChallengeResultRepository
	FlaggedDCResultRepository             DailyChallengeResultRepository
	PresetsRepository                     PresetsRepository
	ShowdownRepository                    ShowdownRepository
	ShowdownParticipantRepository         ShowdownParticipantRepository
	FixtureRepository                     FixturesRepository
	UserResolutionRepository              UserResolutionRepository
	CounterRepository                     CounterRepository
	BotDetectionRepository                BotDetectionRepository
	UserShadowBanRepository               UserShadowBanRepository
	ShowdownLeaderboardRepository         ShowdownLeaderboardRepository
	UserDailyActivityRepository           UserDailyActivityRepository
	UserActivitiesRepository              UserActivitiesRepository
	LeagueParticipantRepository           LeagueParticipantRepository
	LeaguesRepository                     LeagueRepository
	UserSettingsRepository                UserSettingsRepository
	UserRatingFixtureSubmissionRepository UserRatingFixtureSubmissionRepository
	FriendsAndFollowersRepository         FriendsAndFollowersRepository
	GameSeriesRepository                  GameSeriesRepository
	UserDailyChallengeStatsRepository     UserDailyChallengeStatsRepository
	ParticipantRepository                 ContestParticipantRepository
	PuzzleRepository                      PuzzleRepository
	PuzzleResultRepository                PuzzleResultRepository
	MessageGroupRepository                MessageGroupRepository
	MessagesRepository                    MessagesRepository
	PuzzleUserStatRepository              PuzzleUserStatRepository
	PuzzleGameRepository                  PuzzleGameRepository
	PuzzleGameSeriesRepository            PuzzleGameSeriesRepository
	CrossMathPuzzleRushRepository         CrossMathPuzzleRushRepository
	UserGameBucketRepository              UserGameBucketRepository
	ClubRepository                        ClubRepository
	ClubEventRepository                   ClubEventRepository
	ForumRepository                       ForumRepository
	ClubAnnouncementRepository            ClubAnnouncementRepository
	ClubMemberRepository                  ClubMemberRepository
	FeedRepository                        FeedRepository
	FeedDataRepository                    FeedDataRepository
	FeedbackRepository                    FeedbackRepository
	AnnouncementRepository                AnnouncementRepository
	InstitutionRepository                 InstitutionRepository
	ReferralsRepository                   ReferralsRepository
	UserStreakRepository                  UserStreakRepository
	StreakShieldTransactionRepository     StreakShieldTransactionRepository
}

type RepositoryParams struct {
	fx.In

	DB                                database.Database
	UserRepository                    UserRepository
	DeletedUserRepository             DeletedUserRepository
	GameRepository                    GameRepository
	ContestRepository                 ContestRepository
	DailyChallengeRepository          DailyChallengeRepository
	DailyChallengeResultRepository    DailyChallengeResultRepository `name:"dailyChallengeResultRepository"`
	FlaggedDCResultRepository         DailyChallengeResultRepository `name:"flaggedDCResultRepository"`
	PresetsRepository                 PresetsRepository
	ShowdownRepository                ShowdownRepository
	ShowdownParticipantRepository     ShowdownParticipantRepository
	FixtureRepository                 FixturesRepository
	UserResolutionRepository          UserResolutionRepository
	BotDetectionRepository            BotDetectionRepository
	UserShadowBanRepository           UserShadowBanRepository
	CounterRepository                 CounterRepository
	ShowdownLeaderboardRepository     ShowdownLeaderboardRepository
	UserDailyActivityRepository       UserDailyActivityRepository
	UserActivitiesRepository          UserActivitiesRepository
	LeagueParticipantRepository       LeagueParticipantRepository
	LeaguesRepository                 LeagueRepository
	UserSettingsRepository            UserSettingsRepository
	UserRatingFixtureSubmissionRepo   UserRatingFixtureSubmissionRepository
	FriendsAndFollowersRepository     FriendsAndFollowersRepository
	GameSeriesRepository              GameSeriesRepository
	UserDailyChallengeStatsRepository UserDailyChallengeStatsRepository
	ParticipantRepository             ContestParticipantRepository
	PuzzleRepository                  PuzzleRepository
	PuzzleResultRepository            PuzzleResultRepository
	MessageGroupRepository            MessageGroupRepository
	MessagesRepository                MessagesRepository
	PuzzleUserStatRepository          PuzzleUserStatRepository
	PuzzleGameRepository              PuzzleGameRepository
	PuzzleGameSeriesRepository        PuzzleGameSeriesRepository
	CrossMathPuzzleRushRepository     CrossMathPuzzleRushRepository
	UserGameBucketRepository          UserGameBucketRepository
	ClubRepository                    ClubRepository
	ClubEventRepository               ClubEventRepository
	ForumRepository                   ForumRepository
	ClubAnnouncementRepository        ClubAnnouncementRepository
	ClubMemberRepository              ClubMemberRepository
	FeedRepository                    FeedRepository
	FeedDataRepository                FeedDataRepository
	FeedbackRepository                FeedbackRepository
	AnnouncementRepository            AnnouncementRepository
	InstitutionRepository             InstitutionRepository
	ReferralsRepository               ReferralsRepository
	UserStreakRepository              UserStreakRepository
	StreakShieldTransactionRepository StreakShieldTransactionRepository
}

func NewRepositoryFactory(params RepositoryParams) *RepositoryFactory {
	return &RepositoryFactory{
		UserRepository:                        params.UserRepository,
		DeletedUserRepository:                 params.DeletedUserRepository,
		GameRepository:                        params.GameRepository,
		ContestRepository:                     params.ContestRepository,
		DailyChallengeRepository:              params.DailyChallengeRepository,
		DailyChallengeResultRepository:        params.DailyChallengeResultRepository,
		FlaggedDCResultRepository:             params.FlaggedDCResultRepository,
		PresetsRepository:                     params.PresetsRepository,
		ShowdownRepository:                    params.ShowdownRepository,
		ShowdownParticipantRepository:         params.ShowdownParticipantRepository,
		BotDetectionRepository:                params.BotDetectionRepository,
		UserShadowBanRepository:               params.UserShadowBanRepository,
		FixtureRepository:                     params.FixtureRepository,
		UserResolutionRepository:              params.UserResolutionRepository,
		CounterRepository:                     params.CounterRepository,
		ShowdownLeaderboardRepository:         params.ShowdownLeaderboardRepository,
		UserDailyActivityRepository:           params.UserDailyActivityRepository,
		UserActivitiesRepository:              params.UserActivitiesRepository,
		LeagueParticipantRepository:           params.LeagueParticipantRepository,
		LeaguesRepository:                     params.LeaguesRepository,
		UserSettingsRepository:                params.UserSettingsRepository,
		UserRatingFixtureSubmissionRepository: params.UserRatingFixtureSubmissionRepo,
		FriendsAndFollowersRepository:         params.FriendsAndFollowersRepository,
		GameSeriesRepository:                  params.GameSeriesRepository,
		UserDailyChallengeStatsRepository:     params.UserDailyChallengeStatsRepository,
		ParticipantRepository:                 params.ParticipantRepository,
		PuzzleRepository:                      params.PuzzleRepository,
		PuzzleResultRepository:                params.PuzzleResultRepository,
		MessageGroupRepository:                params.MessageGroupRepository,
		MessagesRepository:                    params.MessagesRepository,
		PuzzleUserStatRepository:              params.PuzzleUserStatRepository,
		PuzzleGameRepository:                  params.PuzzleGameRepository,
		PuzzleGameSeriesRepository:            params.PuzzleGameSeriesRepository,
		CrossMathPuzzleRushRepository:         params.CrossMathPuzzleRushRepository,
		UserGameBucketRepository:              params.UserGameBucketRepository,
		ClubRepository:                        params.ClubRepository,
		ClubEventRepository:                   params.ClubEventRepository,
		ForumRepository:                       params.ForumRepository,
		ClubAnnouncementRepository:            params.ClubAnnouncementRepository,
		ClubMemberRepository:                  params.ClubMemberRepository,
		FeedRepository:                        params.FeedRepository,
		FeedDataRepository:                    params.FeedDataRepository,
		FeedbackRepository:                    params.FeedbackRepository,
		AnnouncementRepository:                params.AnnouncementRepository,
		InstitutionRepository:                 params.InstitutionRepository,
		ReferralsRepository:                   params.ReferralsRepository,
		UserStreakRepository:                  params.UserStreakRepository,
		StreakShieldTransactionRepository:     params.StreakShieldTransactionRepository,
	}
}

var Module = fx.Module(
	"repository",
	fx.Provide(
		NewUserRepository,
		NewDeletedUserRepository,
		NewGameRepository,
		NewContestRepository,
		NewDailyChallengeRepository,
		fx.Annotate(NewDailyChallengeResultRepository, fx.ResultTags(`name:"dailyChallengeResultRepository"`)),
		fx.Annotate(NewFlaggedDCResultRepository, fx.ResultTags(`name:"flaggedDCResultRepository"`)),
		NewPresetsRepository,
		NewShowdownRepository,
		NewShowdownParticipantRepository,
		NewFixturesRepository,
		NewUserResolutionRepository,
		NewCounterRepository,
		NewBotDetectionRepository,
		NewUserShadowBanRepository,
		NewShowdownLeaderboardRepository,
		NewUserDailyActivityRepository,
		NewUserActivitiesRepository,
		NewLeagueParticipantRepository,
		NewLeagueRepository,
		NewUserSettingsRepository,
		NewUserRatingFixtureSubmissionRepository,
		NewFriendsAndFollowersRepository,
		NewGameSeriesRepository,
		NewUserDailyChallengeStatsRepository,
		NewContestParticipantRepository,
		NewPuzzleRepository,
		NewPuzzleResultRepository,
		NewMessageGroupRepository,
		NewMessagesRepository,
		NewPuzzleUserStatRepository,
		NewPuzzleGameRepository,
		NewPuzzleGameSeriesRepository,
		NewCrossMathPuzzleRushRepository,
		NewUserGameBucketRepository,
		NewClubRepository,
		NewClubEventRepository,
		NewForumRepository,
		NewClubAnnouncementRepository,
		NewClubMemberRepository,
		NewFeedData,
		NewUserFeed,
		NewRepositoryFactory,
		NewFeedbackRepository,
		NewAnnouncementRepository,
		NewInstitutionRepository,
		NewReferralsRepository,
		NewUserStreakRepository,
		NewStreakShieldTransactionRepository,
	),
)
