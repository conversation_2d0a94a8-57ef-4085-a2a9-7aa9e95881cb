package repository

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

type ShowdownRepository interface {
	CreateNewShowdown(ctx context.Context, sumdayShowDown *models.Showdown) (*models.Showdown, error)
	FindByID(ctx context.Context, id primitive.ObjectID) (*models.Showdown, error)
	Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.Showdown, error)
	FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.Showdown, error)
	UpdateOne(ctx context.Context, filter, update bson.M) error
	GetLiveShowdown(ctx context.Context) (*models.Showdown, error)
	GetLatestShowdownByStatus(ctx context.Context, status models.ShowdownContestStatus) (*models.Showdown, error)
	Update(ctx context.Context, showdown *models.Showdown) error
	IncrementRegistrationCount(ctx context.Context, showdownID primitive.ObjectID) (*models.Showdown, error)
	DecrementRegistrationCount(ctx context.Context, showdownID primitive.ObjectID) (*models.Showdown, error)
	GetShowdownsByStatus(ctx context.Context, statuses []models.ShowdownContestStatus, page, pageSize *int, sortDirection *string) (*models.PaginatedShowdowns, error)
	GetLatestShowdownByStartTime(ctx context.Context, limit int) ([]*models.Showdown, error)
}

type showdownRepository struct {
	collection *mongo.Collection
}

func NewShowdownRepository(lc fx.Lifecycle, db database.Database) ShowdownRepository {
	collection := db.Collection("showdown")
	repo := showdownRepository{collection: collection}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Showdown repository initialized and ready.")
			go func() {
				err = repo.syncIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for showdown repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down showdown repository.")
			return nil
		},
	})

	return &repo
}

func (r *showdownRepository) syncIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{
			{Key: "startTime", Value: 1},
			{Key: "endTime", Value: 1},
		}},
		{Keys: bson.D{
			{Key: "registrationStartTime", Value: 1},
			{Key: "registrationEndTime", Value: 1},
		}},
		{Keys: bson.D{{Key: "status", Value: 1}}},
	})
}

func (r *showdownRepository) CreateNewShowdown(ctx context.Context, sumdayShowDown *models.Showdown) (*models.Showdown, error) {
	if sumdayShowDown.ID == nil {
		sumdayShowDown.ID = utils.AllocPtr(primitive.NewObjectID())
	}
	result, err := r.collection.InsertOne(ctx, sumdayShowDown)
	if err != nil {
		return nil, fmt.Errorf("failed to create new showdown: %w", err)
	}
	sumdayShowDown.ID = utils.AllocPtr(result.InsertedID.(primitive.ObjectID))
	return sumdayShowDown, nil
}

func (r *showdownRepository) GetLatestShowdownByStartTime(ctx context.Context, limit int) ([]*models.Showdown, error) {
	filter := bson.M{
		"endTime": bson.M{"$gt": time.Now()},
	}

	options := options.Find().
		SetSort(bson.M{"startTime": 1}).
		SetLimit(int64(limit))

	cursor, err := r.collection.Find(ctx, filter, options)
	if err != nil {
		return nil, fmt.Errorf("failed to query latest showdowns: %w", err)
	}
	defer cursor.Close(ctx)

	var showdowns []*models.Showdown
	if err := cursor.All(ctx, &showdowns); err != nil {
		return nil, fmt.Errorf("failed to decode showdowns: %w", err)
	}

	return showdowns, nil
}

func (r *showdownRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.Showdown, error) {
	var showdown models.Showdown
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&showdown)
	if err != nil {
		return nil, err
	}

	return &showdown, nil
}

func (r *showdownRepository) FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.Showdown, error) {
	var showdown models.Showdown
	err := r.collection.FindOne(ctx, filter, opts...).Decode(&showdown)
	if err != nil {
		return nil, err
	}

	return &showdown, nil
}

func (r *showdownRepository) Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.Showdown, error) {
	cursor, err := r.collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var showdowns []*models.Showdown
	if err := cursor.All(ctx, &showdowns); err != nil {
		return nil, err
	}
	return showdowns, nil
}

func (r *showdownRepository) Update(ctx context.Context, showdown *models.Showdown) error {
	showdown.UpdatedAt = time.Now()

	_, err := r.collection.ReplaceOne(
		ctx,
		bson.M{"_id": showdown.ID},
		showdown,
	)
	return err
}

func (r *showdownRepository) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := r.collection.UpdateOne(ctx, filter, update)
	return err
}

func (r *showdownRepository) GetShowdownsByStatus(ctx context.Context, statuses []models.ShowdownContestStatus, page, pageSize *int, sortDirection *string) (*models.PaginatedShowdowns, error) {
	if page == nil {
		defaultPage := 1
		page = &defaultPage
	}
	if pageSize == nil {
		defaultPageSize := 20
		pageSize = &defaultPageSize
	}

	defaultSortDirection := "ASC"
	if sortDirection == nil {
		sortDirection = &defaultSortDirection
	}

	skip := (*page - 1) * *pageSize

	sortOrder := -1
	if *sortDirection == "ASC" {
		sortOrder = 1
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"status": bson.M{"$in": statuses},
			"$or": []bson.M{
				{"clubId": bson.M{"$exists": false}},
				{"clubId": nil},
			},
		}}},
		{{Key: "$facet", Value: bson.M{
			"totalCount": bson.A{
				bson.M{"$count": "count"},
			},
			"showdowns": bson.A{
				bson.M{"$sort": bson.M{"startTime": sortOrder}},
				bson.M{"$skip": skip},
				bson.M{"$limit": *pageSize},
			},
		}}},
		{{Key: "$project", Value: bson.M{
			"count":     bson.M{"$arrayElemAt": bson.A{"$totalCount.count", 0}},
			"showdowns": "$showdowns",
		}}},
	}

	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch showdowns: %w", err)
	}

	var result []*models.PaginatedShowdowns

	if err := cursor.All(ctx, &result); err != nil {
		return nil, fmt.Errorf("failed to decode showdowns: %w", err)
	}
	return result[0], nil
}

func (r *showdownRepository) GetLiveShowdown(ctx context.Context) (*models.Showdown, error) {
	return r.FindOne(ctx, bson.M{
		"status": bson.M{
			"$in": []string{models.ShowdownContestStatusLive.String(), models.ShowdownContestStatusUpcoming.String()},
		},
	}, options.FindOne().SetSort(bson.M{"startTime": 1}))
}

func (r *showdownRepository) GetLatestShowdownByStatus(ctx context.Context, status models.ShowdownContestStatus) (*models.Showdown, error) {
	return r.FindOne(ctx, bson.M{"status": status}, options.FindOne().SetSort(bson.M{"createdAt": -1}))
}

func (r *showdownRepository) updateRegistrationCount(ctx context.Context, showdownId primitive.ObjectID, count int) (*models.Showdown, error) {
	var updatedShowdown models.Showdown
	err := r.collection.FindOneAndUpdate(
		ctx,
		bson.M{"_id": showdownId},
		bson.M{"$inc": bson.M{"registrationCount": count}},
		options.FindOneAndUpdate().SetReturnDocument(options.After),
	).Decode(&updatedShowdown)
	if err != nil {
		return nil, err
	}
	return &updatedShowdown, nil
}

func (r *showdownRepository) IncrementRegistrationCount(ctx context.Context, showdownID primitive.ObjectID) (*models.Showdown, error) {
	showdown, err := r.updateRegistrationCount(ctx, showdownID, 1)
	return showdown, err
}

func (r *showdownRepository) DecrementRegistrationCount(ctx context.Context, showdownID primitive.ObjectID) (*models.Showdown, error) {
	showdown, err := r.updateRegistrationCount(ctx, showdownID, -1)
	return showdown, err
}
