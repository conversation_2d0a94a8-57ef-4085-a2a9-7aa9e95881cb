package repository

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

type ShowdownLeaderboardRepository interface {
	BulkInsert(ctx context.Context, participants []*models.LeaderParticipantEntity) error
	GetLeaderboardParticipantsPaginated(ctx context.Context, showdownID primitive.ObjectID, page, pageSize int) ([]*models.LeaderParticipantEntity, error)
	GetShowdownLeaderboardParticipantsCount(ctx context.Context, showdownID primitive.ObjectID) (int64, error)
}

type showdownLeaderboardRepository struct {
	collection *mongo.Collection
}

func NewShowdownLeaderboardRepository(lc fx.Lifecycle, db database.Database) ShowdownLeaderboardRepository {
	collection := db.Collection("showdownLeaderboard")
	repo := showdownLeaderboardRepository{collection: collection}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Showdown leaderboard repository initialized and ready.")
			go func() {
				err = repo.syncIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for showdown leaderboard repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down showdown leaderboard repository.")
			return nil
		},
	})

	return &repo
}

func (r *showdownLeaderboardRepository) syncIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "showdownId", Value: 1},
		}},
		{Keys: bson.D{{Key: "rank", Value: 1}}},
	})
}

func (r *showdownLeaderboardRepository) BulkInsert(ctx context.Context, participants []*models.LeaderParticipantEntity) error {
	var documents []interface{}
	for _, participant := range participants {
		if participant.ID.IsZero() {
			participant.ID = primitive.NewObjectID()
		}
		documents = append(documents, participant)
	}

	_, err := r.collection.InsertMany(ctx, documents)
	if err != nil {
		return fmt.Errorf("bulk insert error: %v", err)
	}

	return nil
}

func (r *showdownLeaderboardRepository) GetShowdownLeaderboardParticipantsCount(ctx context.Context, showdownID primitive.ObjectID) (int64, error) {
	matchStage := bson.D{{
		Key: "$match",
		Value: bson.D{{
			Key:   "showdownId",
			Value: showdownID,
		}},
	}}
	countPipeline := []bson.D{
		matchStage,
		{{Key: "$count", Value: "totalParticipants"}},
	}

	var countResult []bson.M
	countCursor, err := r.collection.Aggregate(ctx, countPipeline)
	if err != nil {
		return 0, fmt.Errorf("failed to count participants: %v", err)
	}
	defer countCursor.Close(ctx)

	if err := countCursor.All(ctx, &countResult); err != nil {
		return 0, fmt.Errorf("failed to decode participant count: %v", err)
	}

	// Extract total count
	totalParticipants := int32(0)
	if len(countResult) > 0 {
		totalParticipants = countResult[0]["totalParticipants"].(int32)
	}
	return int64(totalParticipants), err
}

func (r *showdownLeaderboardRepository) GetLeaderboardParticipantsPaginated(ctx context.Context, showdownID primitive.ObjectID, page, pageSize int) ([]*models.LeaderParticipantEntity, error) {
	skip := (page - 1) * pageSize
	filter := bson.M{"showdownId": showdownID}

	findOptions := options.Find()
	findOptions.SetSort(bson.M{"rank": 1})
	findOptions.SetSkip(int64(skip))
	findOptions.SetLimit(int64(pageSize))

	cursor, err := r.collection.Find(ctx, filter, findOptions)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var participants []*models.LeaderParticipantEntity
	for cursor.Next(ctx) {
		var participant models.LeaderParticipantEntity
		if err := cursor.Decode(&participant); err != nil {
			return nil, err
		}
		participants = append(participants, &participant)
	}

	if err := cursor.Err(); err != nil {
		return nil, err
	}

	return participants, nil
}
