package repository

import (
	"context"
	"errors"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

type ShowdownParticipantRepository interface {
	Create(ctx context.Context, participant *models.ShowdownParticipant) error
	GetByID(ctx context.Context, id primitive.ObjectID) (*models.ShowdownParticipant, error)
	GetByUserID(ctx context.Context, userID primitive.ObjectID) ([]*models.ShowdownParticipant, error)
	Update(ctx context.Context, participant *models.ShowdownParticipant) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	GetByShowdownIDAndUserID(ctx context.Context, showdownId, userID primitive.ObjectID) (*models.ShowdownParticipant, error)
	DeleteByShowdownAndUser(ctx context.Context, showdownId, userID primitive.ObjectID) (bool, error)
	GetTopParticipantsByShowdown(ctx context.Context, showdownId primitive.ObjectID, limit int64) ([]*models.ShowdownParticipant, error)
	BulkUpdate(ctx context.Context, participants []*models.ShowdownParticipant) error
	CountByShowdown(ctx context.Context, showdownId primitive.ObjectID) (int64, error)
	GetShowdownParticipantsPaginated(ctx context.Context, showdownID primitive.ObjectID, page, pageSize int) ([]*models.ShowdownParticipant, error)
	GetShowdownParticipantsCount(ctx context.Context, showdownID primitive.ObjectID) (int64, error)
	GetShowdownParticipantsOfLastPass(ctx context.Context, showdownID primitive.ObjectID, roundCount int) ([]*models.ShowdownParticipant, error)
	UpdateShowdownRoundsBatch(ctx context.Context, roundDtos map[primitive.ObjectID]*models.ShowdownRoundDto, showdownID primitive.ObjectID) error
	UpdateRounds(ctx context.Context, participantID primitive.ObjectID, rounds []*models.ShowdownRound) error
	GetParticipantsByUserAndShowdowns(ctx context.Context, showdownIds []*primitive.ObjectID, userId primitive.ObjectID) ([]*models.ShowdownParticipant, error)
}

type showdownParticipantRepository struct {
	collection *mongo.Collection
}

func NewShowdownParticipantRepository(lc fx.Lifecycle, db database.Database) ShowdownParticipantRepository {
	collection := db.Collection("showdownParticipant")
	repo := showdownParticipantRepository{collection: collection}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Showdown participant repository initialized and ready.")
			go func() {
				err = repo.syncIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for showdown participant repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down showdown participant repository.")
			return nil
		},
	})

	return &repo
}

func (r *showdownParticipantRepository) syncIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{
			{Key: "showdownId", Value: 1},
			{Key: "userId", Value: 1},
		}, Options: options.Index().SetUnique(true)},
	})
}

func (r *showdownParticipantRepository) Create(ctx context.Context, participant *models.ShowdownParticipant) error {
	participant.CreatedAt = time.Now()
	participant.UpdatedAt = time.Now()

	result, err := r.collection.InsertOne(ctx, participant)
	if err != nil {
		return err
	}

	participant.ID = utils.AllocPtr(result.InsertedID.(primitive.ObjectID))
	return nil
}

func (r *showdownParticipantRepository) GetByID(ctx context.Context, id primitive.ObjectID) (*models.ShowdownParticipant, error) {
	var participant models.ShowdownParticipant
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&participant)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &participant, nil
}

func (r *showdownParticipantRepository) GetByUserID(ctx context.Context, userID primitive.ObjectID) ([]*models.ShowdownParticipant, error) {
	filter := bson.M{"userId": userID}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var participants []*models.ShowdownParticipant
	if err := cursor.All(ctx, &participants); err != nil {
		return nil, err
	}

	return participants, nil
}

func (r *showdownParticipantRepository) Update(ctx context.Context, participant *models.ShowdownParticipant) error {
	participant.UpdatedAt = time.Now()

	_, err := r.collection.ReplaceOne(
		ctx,
		bson.M{"_id": participant.ID},
		participant,
	)
	return err
}

func (r *showdownParticipantRepository) UpdateRounds(ctx context.Context, participantID primitive.ObjectID, rounds []*models.ShowdownRound) error {
	_, err := r.collection.UpdateOne(
		ctx,
		bson.M{"_id": participantID},
		bson.M{
			"$set": bson.M{
				"rounds":    rounds,
				"updatedAt": time.Now(),
			},
		},
	)

	return err
}

func (r *showdownParticipantRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	_, err := r.collection.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (r *showdownParticipantRepository) GetTopParticipantsByShowdown(ctx context.Context, showdownId primitive.ObjectID, limit int64) ([]*models.ShowdownParticipant, error) {
	opts := options.Find().SetSort(bson.D{{Key: "rank", Value: -1}, {Key: "lastSubmissionTime", Value: 1}}).SetLimit(limit)

	cursor, err := r.collection.Find(ctx, bson.M{"showdownId": showdownId}, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var participants []*models.ShowdownParticipant
	if err = cursor.All(ctx, &participants); err != nil {
		return nil, err
	}

	return participants, nil
}

func (r *showdownParticipantRepository) GetByShowdownIDAndUserID(ctx context.Context, showdownId, userID primitive.ObjectID) (*models.ShowdownParticipant, error) {
	var participant models.ShowdownParticipant
	err := r.collection.FindOne(ctx, bson.M{
		"showdownId": showdownId,
		"userId":     userID,
	}).Decode(&participant)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &participant, nil
}

func (r *showdownParticipantRepository) DeleteByShowdownAndUser(ctx context.Context, showdownId, userID primitive.ObjectID) (bool, error) {
	filter := bson.M{"showdownId": showdownId, "userId": userID}
	result, err := r.collection.DeleteOne(ctx, filter)
	if err != nil {
		return false, err
	}
	return result.DeletedCount > 0, nil
}

func (r *showdownParticipantRepository) GetParticipantsByUserAndShowdowns(ctx context.Context, showdowmIds []*primitive.ObjectID, userId primitive.ObjectID) ([]*models.ShowdownParticipant, error) {
	filter := bson.M{"showdownId": bson.M{"$in": showdowmIds}, "userId": userId}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var participants []*models.ShowdownParticipant
	if err = cursor.All(ctx, &participants); err != nil {
		return nil, err
	}

	return participants, nil
}

func (r *showdownParticipantRepository) CountByShowdown(ctx context.Context, showdownId primitive.ObjectID) (int64, error) {
	filter := bson.M{"showdownId": showdownId}
	count, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (r *showdownParticipantRepository) GetShowdownParticipantsCount(ctx context.Context, showdownID primitive.ObjectID) (int64, error) {
	matchStage := bson.D{{
		Key: "$match",
		Value: bson.D{{
			Key:   "showdownId",
			Value: showdownID,
		}},
	}}
	countPipeline := []bson.D{
		matchStage,
		{{Key: "$count", Value: "totalParticipants"}},
	}

	var countResult []bson.M
	countCursor, err := r.collection.Aggregate(ctx, countPipeline)
	if err != nil {
		return 0, fmt.Errorf("failed to count participants: %v", err)
	}
	defer countCursor.Close(ctx)

	if err := countCursor.All(ctx, &countResult); err != nil {
		return 0, fmt.Errorf("failed to decode participant count: %v", err)
	}

	// Extract total count
	totalParticipants := int32(0)
	if len(countResult) > 0 {
		totalParticipants = countResult[0]["totalParticipants"].(int32)
	}
	return int64(totalParticipants), err
}

func (r *showdownParticipantRepository) GetShowdownParticipantsOfLastPass(ctx context.Context, showdownID primitive.ObjectID, roundCount int) ([]*models.ShowdownParticipant, error) {
	filter := bson.M{"showdownId": showdownID}

	findOptions := options.Find().SetLimit(int64(roundCount)).SetSort(bson.M{"createdAt": 1})

	cursor, err := r.collection.Find(ctx, filter, findOptions)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var participants []*models.ShowdownParticipant
	if err = cursor.All(ctx, &participants); err != nil {
		return nil, err
	}

	return participants, nil
}

func (r *showdownParticipantRepository) GetShowdownParticipantsPaginated(ctx context.Context, showdownID primitive.ObjectID, page, pageSize int) ([]*models.ShowdownParticipant, error) {
	skip := (page - 1) * pageSize
	filter := bson.M{"showdownId": showdownID}

	findOptions := options.Find()
	findOptions.SetSkip(int64(skip))
	findOptions.SetLimit(int64(pageSize))
	findOptions.SetSort(bson.M{"createdAt": -1})

	cursor, err := r.collection.Find(ctx, filter, findOptions)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	// Parse results
	var participants []*models.ShowdownParticipant
	for cursor.Next(ctx) {
		var participant models.ShowdownParticipant
		if err := cursor.Decode(&participant); err != nil {
			return nil, err
		}
		participants = append(participants, &participant)
	}

	if err := cursor.Err(); err != nil {
		return nil, err
	}

	return participants, nil
}

func (r *showdownParticipantRepository) BulkUpdate(ctx context.Context, participants []*models.ShowdownParticipant) error {
	var models []mongo.WriteModel

	for _, participant := range participants {
		filter := bson.M{"_id": participant.ID}
		update := bson.M{"$set": participant}
		model := mongo.NewUpdateOneModel().
			SetFilter(filter).
			SetUpdate(update)

		models = append(models, model)
	}

	opts := options.BulkWrite().SetOrdered(false)
	_, err := r.collection.BulkWrite(ctx, models, opts)
	if err != nil {
		return fmt.Errorf("bulk write error: %v", err)
	}

	return nil
}

func (r *showdownParticipantRepository) UpdateShowdownRoundsBatch(ctx context.Context, roundDtos map[primitive.ObjectID]*models.ShowdownRoundDto, showdownID primitive.ObjectID) error {
	data := make([]mongo.WriteModel, 0, len(roundDtos))

	for _, dto := range roundDtos {
		if dto == nil {
			// TODO: add log
			continue
		}
		filter := bson.M{
			"userId":     dto.UserId,
			"showdownId": showdownID,
		}
		update := bson.M{
			"$set": bson.M{
				"updatedAt": time.Now(),
			},
			"$push": bson.M{
				"rounds": bson.M{
					"$each": []models.ShowdownRound{dto.Round},
				},
			},
		}
		// "rounds":    []models.ShowdownRound{dto.Round},
		model := mongo.NewUpdateOneModel().SetFilter(filter).SetUpdate(update)
		data = append(data, model)
	}

	bulkOptions := options.BulkWrite().SetOrdered(false)
	_, err := r.collection.BulkWrite(ctx, data, bulkOptions)
	if err != nil {
		return err
	}

	// zlog.Info("Matched %d document(s), modified %d document(s), inserted %d document(s)",
	// 	result.MatchedCount, result.ModifiedCount, result.InsertedCount)
	return nil
}
