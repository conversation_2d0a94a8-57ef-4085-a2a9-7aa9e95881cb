package repository

import (
	"context"
	"errors"
	"time"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

type StreakShieldTransactionRepository interface {
	Create(ctx context.Context, transaction *models.StreakShieldTransaction) error
	FindByID(ctx context.Context, id primitive.ObjectID) (*models.StreakShieldTransaction, error)
	FindByUserID(ctx context.Context, userID primitive.ObjectID, page, pageSize int) ([]*models.StreakShieldTransaction, int, error)
	FindAll(ctx context.Context, page, pageSize int) ([]*models.StreakShieldTransaction, int, error)
	UpdateOne(ctx context.Context, filter, update bson.M) error
}

type streakShieldTransactionRepository struct {
	collection *mongo.Collection
}

func NewStreakShieldTransactionRepository(lc fx.Lifecycle, db database.Database) StreakShieldTransactionRepository {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Streak shield transaction repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down streak shield transaction repository.")
			return nil
		},
	})
	return &streakShieldTransactionRepository{
		collection: db.Collection("streakShieldTransactions"),
	}
}

func (r *streakShieldTransactionRepository) Create(ctx context.Context, transaction *models.StreakShieldTransaction) error {
	if transaction.ID == primitive.NilObjectID {
		transaction.ID = primitive.NewObjectID()
	}

	timeNow := time.Now()
	transaction.CreatedAt = timeNow
	transaction.UpdatedAt = timeNow

	_, err := r.collection.InsertOne(ctx, transaction)
	if err != nil {
		zlog.Error(ctx, "Failed to create streak shield transaction", err, zap.String("userId", transaction.UserID.Hex()))
		return err
	}
	return nil
}

func (r *streakShieldTransactionRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.StreakShieldTransaction, error) {
	var transaction models.StreakShieldTransaction
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&transaction)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &transaction, nil
}

func (r *streakShieldTransactionRepository) FindByUserID(ctx context.Context, userID primitive.ObjectID, page, pageSize int) ([]*models.StreakShieldTransaction, int, error) {
	filter := bson.M{"userId": userID}

	// Count total documents
	total, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// Set up pagination
	skip := (page - 1) * pageSize
	opts := options.Find().
		SetSkip(int64(skip)).
		SetLimit(int64(pageSize)).
		SetSort(bson.D{{Key: "createdAt", Value: -1}})

	// Execute query
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	var transactions []*models.StreakShieldTransaction
	if err := cursor.All(ctx, &transactions); err != nil {
		return nil, 0, err
	}

	return transactions, int(total), nil
}

func (r *streakShieldTransactionRepository) FindAll(ctx context.Context, page, pageSize int) ([]*models.StreakShieldTransaction, int, error) {
	filter := bson.M{}

	// Count total documents
	total, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// Set up pagination
	skip := (page - 1) * pageSize
	opts := options.Find().
		SetSkip(int64(skip)).
		SetLimit(int64(pageSize)).
		SetSort(bson.D{{Key: "createdAt", Value: -1}})

	// Execute query
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	var transactions []*models.StreakShieldTransaction
	if err := cursor.All(ctx, &transactions); err != nil {
		return nil, 0, err
	}

	return transactions, int(total), nil
}

func (r *streakShieldTransactionRepository) UpdateOne(ctx context.Context, filter, update bson.M) error {
	// Add updatedAt field to the update
	if update["$set"] == nil {
		update["$set"] = bson.M{}
	}
	update["$set"].(bson.M)["updatedAt"] = time.Now()

	_, err := r.collection.UpdateOne(ctx, filter, update)
	return err
}
