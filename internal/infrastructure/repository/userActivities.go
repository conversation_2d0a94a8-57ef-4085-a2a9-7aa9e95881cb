package repository

import (
	"context"
	"fmt" // Added for error formatting in GetActivitiesByActivityType
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

// UserActivitiesRepository defines the interface for user activities data operations
type UserActivitiesRepository interface {
	Create(ctx context.Context, activity *models.UserActivity) error
	FindByID(ctx context.Context, id primitive.ObjectID) (*models.UserActivity, error)
	FindByUserID(ctx context.Context, userID primitive.ObjectID, limit, skip int64) ([]*models.UserActivity, error)
	FindByActivityType(ctx context.Context, activityType constants.ActivityType, limit, skip int64) ([]*models.UserActivity, error)
	FindByUserIDAndActivityType(ctx context.Context, userID primitive.ObjectID, activityType constants.ActivityType, limit, skip int64) ([]*models.UserActivity, error)
	FindByTimeRange(ctx context.Context, startTime, endTime time.Time, limit, skip int64) ([]*models.UserActivity, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	Aggregate(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) ([]*models.UserActivity, error)
	GetActivitiesByActivityType(ctx context.Context, userID primitive.ObjectID, activityTypes []constants.ActivityType, skip, limit int64) ([]*models.UserActivity, error)
}

type userActivitiesRepository struct {
	collection *mongo.Collection
}

// NewUserActivitiesRepository creates a new repository for user activities
func NewUserActivitiesRepository(lc fx.Lifecycle, db database.Database) UserActivitiesRepository {
	collection := db.Collection("user_activities")
	repo := &userActivitiesRepository{collection: collection}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "User activities repository initialized and ready.")
			go func() {
				err = repo.syncIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for user activities repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down user activities repository.")
			return nil
		},
	})

	return repo
}

func (r *userActivitiesRepository) syncIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{{Key: "userId", Value: 1}}},
		{Keys: bson.D{{Key: "activityType", Value: 1}}},
		{Keys: bson.D{{Key: "timestamp", Value: -1}}},
		{Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "activityType", Value: 1},
		}},
	})
}

func (r *userActivitiesRepository) Create(ctx context.Context, activity *models.UserActivity) error {
	if activity.Timestamp.IsZero() {
		activity.Timestamp = time.Now()
	}

	result, err := r.collection.InsertOne(ctx, activity)
	if err != nil {
		return err
	}

	activity.ID = result.InsertedID.(primitive.ObjectID)
	return nil
}

func (r *userActivitiesRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.UserActivity, error) {
	var activity models.UserActivity
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&activity)
	if err != nil {
		return nil, err
	}
	return &activity, nil
}

func (r *userActivitiesRepository) FindByUserID(ctx context.Context, userID primitive.ObjectID, limit, skip int64) ([]*models.UserActivity, error) {
	opts := options.Find().SetSort(bson.D{{Key: "timestamp", Value: -1}}).SetLimit(limit).SetSkip(skip)
	return r.find(ctx, bson.M{"userId": userID}, opts)
}

func (r *userActivitiesRepository) FindByActivityType(ctx context.Context, activityType constants.ActivityType, limit, skip int64) ([]*models.UserActivity, error) {
	opts := options.Find().SetSort(bson.D{{Key: "timestamp", Value: -1}}).SetLimit(limit).SetSkip(skip)
	return r.find(ctx, bson.M{"activityType": activityType}, opts)
}

func (r *userActivitiesRepository) FindByUserIDAndActivityType(ctx context.Context, userID primitive.ObjectID, activityType constants.ActivityType, limit, skip int64) ([]*models.UserActivity, error) {
	opts := options.Find().SetSort(bson.D{{Key: "timestamp", Value: -1}}).SetLimit(limit).SetSkip(skip)
	return r.find(ctx, bson.M{
		"userId":       userID,
		"activityType": activityType,
	}, opts)
}

func (r *userActivitiesRepository) FindByTimeRange(ctx context.Context, startTime, endTime time.Time, limit, skip int64) ([]*models.UserActivity, error) {
	opts := options.Find().SetSort(bson.D{{Key: "timestamp", Value: -1}}).SetLimit(limit).SetSkip(skip)
	return r.find(ctx, bson.M{
		"timestamp": bson.M{
			"$gte": startTime,
			"$lte": endTime,
		},
	}, opts)
}

func (r *userActivitiesRepository) find(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.UserActivity, error) {
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var activities []*models.UserActivity
	if err = cursor.All(ctx, &activities); err != nil {
		return nil, err
	}

	return activities, nil
}

func (r *userActivitiesRepository) Count(ctx context.Context, filter bson.M) (int64, error) {
	return r.collection.CountDocuments(ctx, filter)
}

func (r *userActivitiesRepository) Aggregate(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) ([]*models.UserActivity, error) {
	cursor, err := r.collection.Aggregate(ctx, pipeline, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var activities []*models.UserActivity
	if err = cursor.All(ctx, &activities); err != nil {
		return nil, err
	}

	return activities, nil
}

func (r *userActivitiesRepository) GetActivitiesByActivityType(ctx context.Context, userID primitive.ObjectID, activityTypes []constants.ActivityType, skip, limit int64) ([]*models.UserActivity, error) {
	if len(activityTypes) == 0 {
		return []*models.UserActivity{}, nil
	}
	if limit <= 0 {
		return []*models.UserActivity{}, nil
	}
	matchStage := bson.D{
		{Key: "$match", Value: bson.M{
			"userId":       userID,
			"activityType": bson.M{"$in": activityTypes},
			"activityId": bson.M{
				"$exists": true,
				"$ne":     primitive.NilObjectID,
			},
		}},
	}

	sortStage := bson.D{{Key: "$sort", Value: bson.M{"timestamp": -1}}}
	skipStage := bson.D{{Key: "$skip", Value: skip}}
	limitStage := bson.D{{Key: "$limit", Value: limit}}

	pipeline := mongo.Pipeline{matchStage, sortStage, skipStage, limitStage}

	activities, err := r.Aggregate(ctx, pipeline)
	if err != nil {
		zlog.Error(ctx, "Failed to aggregate activities by type", err)
		return nil, fmt.Errorf("failed to get activities by type: %w", err)
	}

	return activities, nil
}
