package repository

import (
	"context"
	"errors"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

type UserDailyActivityRepository interface {
	UpsertUserActivity(ctx context.Context, userID primitive.ObjectID, date time.Time, activityType string, duration int64, coins int) error
	GetTimeSpentByUser(ctx context.Context, userID primitive.ObjectID, dateString string) (*int64, error)
	FindByUserIDAndDate(ctx context.Context, userID primitive.ObjectID, date time.Time) (*models.UserDailyActivity, error)
	Aggregate(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) ([]*models.UserDailyActivity, error)
	AggregateProjected(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) (*mongo.Cursor, error)
}

type userDailyActivityRepository struct {
	collection *mongo.Collection
}

func NewUserDailyActivityRepository(lc fx.Lifecycle, db database.Database) UserDailyActivityRepository {
	collection := db.Collection("userDailyActivity")
	r := &userDailyActivityRepository{collection: collection}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "User daily activity repository initialized and ready.")
			go func() {
				err = r.syncUserDailyActivityIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for user daily activity repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down user daily activity repository.")
			return nil
		},
	})

	return r
}

func (r *userDailyActivityRepository) syncUserDailyActivityIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "dateString", Value: 1},
		}, Options: options.Index().SetUnique(true)},
	})
}

func (r *userDailyActivityRepository) Collection() *mongo.Collection {
	return r.collection
}

func (r *userDailyActivityRepository) UpsertUserActivity(ctx context.Context, userID primitive.ObjectID, date time.Time, activityType string, duration int64, coins int) error {
	dateString := date.Format("2006-01-02")

	filter := bson.M{
		"userId":     userID,
		"dateString": dateString,
	}

	update := bson.M{
		"$set": bson.M{
			"date":       date,
			"dateString": dateString,
		},
		"$inc": bson.M{
			"totalDuration":     duration,
			"statikCoinsEarned": coins,
			fmt.Sprintf("activityBreakdown.%s", activityType): coins,
		},
	}

	opts := options.Update().SetUpsert(true)
	_, err := r.collection.UpdateOne(ctx, filter, update, opts)
	return err
}

func (r *userDailyActivityRepository) FindByUserIDAndDate(ctx context.Context, userID primitive.ObjectID, date time.Time) (*models.UserDailyActivity, error) {
	dateString := date.Format("2006-01-02")

	var userActivity models.UserDailyActivity
	err := r.collection.FindOne(ctx, bson.M{
		"userId":     userID,
		"dateString": dateString,
	}).Decode(&userActivity)

	if err == nil {
		return &userActivity, nil
	}

	if errors.Is(err, mongo.ErrNoDocuments) {
		err = r.collection.FindOne(ctx, bson.M{
			"userId":     userID,
			"date":       date,
			"dateString": bson.M{"$exists": false},
		}).Decode(&userActivity)
		if err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				return nil, nil
			}
			return nil, err
		}
		return &userActivity, nil
	}

	return nil, err
}

func (r *userDailyActivityRepository) GetTimeSpentByUser(ctx context.Context, userID primitive.ObjectID, dateString string) (*int64, error) {
	now := time.Now()
	date := utils.GetUserDate(ctx, now)
	var userActivity models.UserDailyActivity
	err := r.collection.FindOne(ctx, bson.M{
		"userId":     userID,
		"dateString": dateString,
	}).Decode(&userActivity)

	if err == nil {
		return utils.AllocPtr(userActivity.TotalDuration), nil
	}

	if errors.Is(err, mongo.ErrNoDocuments) {
		err = r.collection.FindOne(ctx, bson.M{
			"userId":     userID,
			"date":       date,
			"dateString": bson.M{"$exists": false},
		}).Decode(&userActivity)
		if err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				return nil, nil
			}
			return nil, err
		}
		return utils.AllocPtr(userActivity.TotalDuration), nil
	}

	return nil, err
}

func (r *userDailyActivityRepository) Aggregate(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) ([]*models.UserDailyActivity, error) {
	cursor, err := r.collection.Aggregate(ctx, pipeline, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var userActivities []*models.UserDailyActivity
	if err = cursor.All(ctx, &userActivities); err != nil {
		return nil, err
	}

	return userActivities, nil
}

func (r *userDailyActivityRepository) AggregateProjected(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) (*mongo.Cursor, error) {
	cursor, err := r.collection.Aggregate(ctx, pipeline, opts...)
	if err != nil {
		return nil, err
	}
	return cursor, nil
}
