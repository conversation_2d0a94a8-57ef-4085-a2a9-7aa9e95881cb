package repository

import (
	"context"
	"errors"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

type UserDailyChallengeStatsRepository interface {
	Create(ctx context.Context, stats *models.UserDailyChallengeStats) error
	FindByID(ctx context.Context, id primitive.ObjectID) (*models.UserDailyChallengeStats, error)
	FindByUserIDAndDivision(ctx context.Context, userID primitive.ObjectID, division models.ChallengeDivision) (*models.UserDailyChallengeStats, error)
	Update(ctx context.Context, stats *models.UserDailyChallengeStats) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.UserDailyChallengeStats, error)
	Collection() *mongo.Collection
}

type userDailyChallengeStatsRepository struct {
	collection *mongo.Collection
}

func NewUserDailyChallengeStatsRepository(lc fx.Lifecycle, db database.Database) UserDailyChallengeStatsRepository {
	r := &userDailyChallengeStatsRepository{collection: db.Collection("userdailychallengestats")}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "User daily challenge stats repository initialized and ready.")
			go func() {
				err = r.syncUserDailyChallengeStatsIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for user daily challenge stats repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down user daily challenge stats repository.")
			return nil
		},
	})

	return r
}

func (r *userDailyChallengeStatsRepository) syncUserDailyChallengeStatsIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "division", Value: 1},
		}, Options: options.Index().SetUnique(true)},
	})
}

func (r *userDailyChallengeStatsRepository) Collection() *mongo.Collection {
	return r.collection
}

func (r *userDailyChallengeStatsRepository) Create(ctx context.Context, stats *models.UserDailyChallengeStats) error {
	if stats.ID == primitive.NilObjectID {
		stats.ID = primitive.NewObjectID()
	}
	_, err := r.collection.InsertOne(ctx, stats)
	return err
}

func (r *userDailyChallengeStatsRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.UserDailyChallengeStats, error) {
	var stats models.UserDailyChallengeStats
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&stats)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &stats, nil
}

func (r *userDailyChallengeStatsRepository) FindByUserIDAndDivision(ctx context.Context, userID primitive.ObjectID, division models.ChallengeDivision) (*models.UserDailyChallengeStats, error) {
	var stats models.UserDailyChallengeStats
	filter := bson.M{"userId": userID, "division": division}
	err := r.collection.FindOne(ctx, filter).Decode(&stats)
	if err != nil {
		return nil, err
	}
	return &stats, nil
}

func (r *userDailyChallengeStatsRepository) Update(ctx context.Context, stats *models.UserDailyChallengeStats) error {
	_, err := r.collection.ReplaceOne(
		ctx,
		bson.M{"_id": stats.ID},
		stats,
	)
	return err
}

func (r *userDailyChallengeStatsRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	_, err := r.collection.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (r *userDailyChallengeStatsRepository) List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.UserDailyChallengeStats, error) {
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var statsList []*models.UserDailyChallengeStats
	if err = cursor.All(ctx, &statsList); err != nil {
		return nil, err
	}

	return statsList, nil
}
