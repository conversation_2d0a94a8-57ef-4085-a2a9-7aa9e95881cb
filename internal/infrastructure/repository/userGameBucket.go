package repository

import (
	"context"
	"errors"
	"fmt"
	"time"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const MaxGamesPerBucket = 100

type UserGameBucketRepository interface {
	Create(ctx context.Context, bucket *models.UserGameBucket) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.UserGameBucket, error)
	FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.UserGameBucket, error)
	Update(ctx context.Context, bucket *models.UserGameBucket) error
	UpdateOne(ctx context.Context, filter, update bson.M, opts ...*options.UpdateOptions) error
	GetActiveUserGameBucket(ctx context.Context, userID primitive.ObjectID) (*models.UserGameBucket, error)
	GetUserGameBucket(ctx context.Context, userID primitive.ObjectID, bucketNum int) (*models.UserGameBucket, error)
	GetUserGameBuckets(ctx context.Context, userID primitive.ObjectID, opts ...*options.FindOptions) ([]*models.UserGameBucket, error)
	GetGamesPaginated(ctx context.Context, userID primitive.ObjectID, page, pageSize int) ([]primitive.ObjectID, error)
	AddGame(ctx context.Context, userID primitive.ObjectID, game *models.UserGame) (bool, error)
}
type userGameBucketRepository struct {
	collection *mongo.Collection
}

func NewUserGameBucketRepository(lc fx.Lifecycle, db database.Database) UserGameBucketRepository {
	collection := db.Collection("usergames")

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "User game bucket repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down user game bucket repository.")
			return nil
		},
	})

	return &userGameBucketRepository{collection: collection}
}

func (r *userGameBucketRepository) Create(ctx context.Context, bucket *models.UserGameBucket) error {
	if bucket.ID.IsZero() {
		bucket.ID = primitive.NewObjectID()
	}

	if bucket.Games != nil {
		bucket.NumGames = len(bucket.Games)
	} else {
		bucket.NumGames = 0
		bucket.Games = []primitive.ObjectID{}
	}

	_, err := r.collection.InsertOne(ctx, bucket)
	if err != nil {
		return fmt.Errorf("failed to create document: %w", err)
	}

	return nil
}

func (r *userGameBucketRepository) Update(ctx context.Context, bucket *models.UserGameBucket) error {
	filter := bson.M{"_id": bucket.ID}

	bucket.NumGames = len(bucket.Games)

	update := bson.M{
		"$set": bson.M{
			"games":     bucket.Games,
			"numGames":  bucket.NumGames,
			"startTime": bucket.StartTime,
			"endTime":   bucket.EndTime,
		},
	}

	_, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update user game bucket: %w", err)
	}

	return nil
}

func (r *userGameBucketRepository) UpdateOne(ctx context.Context, filter, update bson.M, opts ...*options.UpdateOptions) error {
	_, err := r.collection.UpdateOne(ctx, filter, update, opts...)
	if err != nil {
		return fmt.Errorf("failed to update document: %w", err)
	}

	return nil
}

func (r *userGameBucketRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{"_id": id}

	_, err := r.collection.DeleteOne(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete document: %w", err)
	}

	return nil
}

func (r *userGameBucketRepository) Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.UserGameBucket, error) {
	cursor, err := r.collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to find documents: %w", err)
	}
	defer cursor.Close(ctx)

	var buckets []*models.UserGameBucket
	if err := cursor.All(ctx, &buckets); err != nil {
		return nil, fmt.Errorf("failed to decode documents: %w", err)
	}

	return buckets, nil
}

func (r *userGameBucketRepository) FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.UserGameBucket, error) {
	var bucket models.UserGameBucket

	err := r.collection.FindOne(ctx, filter, opts...).Decode(&bucket)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, mongo.ErrNoDocuments
		}
		return nil, fmt.Errorf("failed to find document: %w", err)
	}

	return &bucket, nil
}

func (r *userGameBucketRepository) GetActiveUserGameBucket(ctx context.Context, userID primitive.ObjectID) (*models.UserGameBucket, error) {
	filter := bson.M{
		"userId": userID,
	}

	opts := options.FindOne().SetSort(bson.M{"bucketNum": -1})

	bucket, err := r.FindOne(ctx, filter, opts)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			bucket = &models.UserGameBucket{
				ID:        primitive.NewObjectID(),
				UserID:    userID,
				Games:     []primitive.ObjectID{},
				BucketNum: 1,
			}
			return bucket, nil
		}
		return nil, fmt.Errorf("failed to find active user game bucket: %w", err)
	}
	return bucket, nil
}

func (r *userGameBucketRepository) GetUserGameBucket(ctx context.Context, userID primitive.ObjectID, bucketNum int) (*models.UserGameBucket, error) {
	filter := bson.M{
		"userId":    userID,
		"bucketNum": bucketNum,
	}
	bucket, err := r.FindOne(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to get user game bucket: %w", err)
	}
	return bucket, nil
}

func (r *userGameBucketRepository) GetUserGameBuckets(ctx context.Context, userID primitive.ObjectID, opts ...*options.FindOptions) ([]*models.UserGameBucket, error) {
	filter := bson.M{"userId": userID}
	buckets, err := r.Find(ctx, filter, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to get user game buckets: %w", err)
	}
	return buckets, nil
}

func (r *userGameBucketRepository) GetGamesPaginated(ctx context.Context, userID primitive.ObjectID, page, pageSize int) ([]primitive.ObjectID, error) {
	filter := bson.M{"userId": userID}
	opts := options.Find().
		SetSort(bson.M{"bucketNum": 1}).
		SetProjection(bson.M{"bucketNum": 1, "numGames": 1, "_id": 1})

	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to get bucket metadata: %w", err)
	}
	defer cursor.Close(ctx)

	var bucketsMeta []struct {
		ID        primitive.ObjectID `bson:"_id"`
		BucketNum int                `bson:"bucketNum"`
		NumGames  int                `bson:"numGames"`
	}

	if err := cursor.All(ctx, &bucketsMeta); err != nil {
		return nil, fmt.Errorf("failed to decode bucket metadata: %w", err)
	}

	if len(bucketsMeta) == 0 {
		return []primitive.ObjectID{}, nil
	}

	var totalGames int
	for _, b := range bucketsMeta {
		totalGames += b.NumGames
	}

	startIndex := page * pageSize
	if startIndex >= totalGames {
		return []primitive.ObjectID{}, nil
	}

	gamesToFetch := min(pageSize, totalGames-startIndex)

	var bucketsNeeded []primitive.ObjectID
	var skipInFirstBucket int
	var gamesSkipped int

	for _, b := range bucketsMeta {
		if gamesSkipped+b.NumGames <= startIndex {
			gamesSkipped += b.NumGames
			continue
		}

		bucketsNeeded = append(bucketsNeeded, b.ID)

		if len(bucketsNeeded) == 1 {
			skipInFirstBucket = startIndex - gamesSkipped
		}

		gamesAvailableInThisBucket := b.NumGames - max(0, startIndex-gamesSkipped)
		if gamesAvailableInThisBucket >= gamesToFetch {
			break
		}

		gamesToFetch -= gamesAvailableInThisBucket
		gamesSkipped += b.NumGames
	}

	if len(bucketsNeeded) == 0 {
		return []primitive.ObjectID{}, nil
	}

	fullBucketFilter := bson.M{"_id": bson.M{"$in": bucketsNeeded}}
	fullBucketOpts := options.Find().SetSort(bson.M{"bucketNum": 1})

	fullBucketsCursor, err := r.collection.Find(ctx, fullBucketFilter, fullBucketOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch bucket data: %w", err)
	}
	defer fullBucketsCursor.Close(ctx)

	var buckets []*models.UserGameBucket
	if err := fullBucketsCursor.All(ctx, &buckets); err != nil {
		return nil, fmt.Errorf("failed to decode buckets: %w", err)
	}

	result := make([]primitive.ObjectID, 0, pageSize)
	gamesNeeded := min(pageSize, totalGames-startIndex)
	currentSkip := skipInFirstBucket

	for i, bucket := range buckets {
		skip := 0
		if i == 0 {
			skip = currentSkip
		}

		if skip >= len(bucket.Games) {
			continue
		}

		availableGames := len(bucket.Games) - skip
		gamesToTake := min(availableGames, gamesNeeded)

		for j := skip; j < skip+gamesToTake; j++ {
			result = append(result, bucket.Games[j])
		}

		gamesNeeded -= gamesToTake

		if gamesNeeded <= 0 {
			break
		}
	}

	return result, nil
}

func (r *userGameBucketRepository) AddGame(ctx context.Context, userID primitive.ObjectID, game *models.UserGame) (bool, error) {
	if game.ID == nil {
		return false, fmt.Errorf("invalid game")
	}
	if game.ST == nil {
		game.ST = utils.AllocPtr(time.Now())
	}
	gameID, err := primitive.ObjectIDFromHex(*game.ID)
	if err != nil {
		return false, fmt.Errorf("invalid game ID: %w", err)
	}
	gameStartTime := *game.ST

	bucket, err := r.GetUserGameBucket(ctx, userID, 1)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			newBucket := &models.UserGameBucket{
				ID:        primitive.NewObjectID(),
				UserID:    userID,
				BucketNum: 1,
				NumGames:  1,
				Games:     []primitive.ObjectID{gameID},
				StartTime: gameStartTime,
				EndTime:   gameStartTime,
			}

			if err := r.Create(ctx, newBucket); err != nil {
				return false, fmt.Errorf("failed to create new bucket: %w", err)
			}
			return true, nil
		}
		return false, fmt.Errorf("failed to get active bucket: %w", err)
	}

	if bucket.NumGames >= MaxGamesPerBucket {
		if err := r.shiftBucketsForNewGame(ctx, userID, gameID, gameStartTime); err != nil {
			return false, fmt.Errorf("failed to shift buckets: %w", err)
		}
		return true, nil
	}

	if bucket.EndTime.Before(gameStartTime) {
		bucket.EndTime = gameStartTime
	}
	if bucket.StartTime.IsZero() || bucket.NumGames == 0 {
		bucket.StartTime = gameStartTime
	}

	bucket.Games = append([]primitive.ObjectID{gameID}, bucket.Games...)
	bucket.NumGames = len(bucket.Games)

	if err := r.Update(ctx, bucket); err != nil {
		return false, fmt.Errorf("failed to update bucket: %w", err)
	}

	return true, nil
}

func (r *userGameBucketRepository) shiftBucketsForNewGame(ctx context.Context, userID, gameID primitive.ObjectID, gameTime time.Time) error {
	opts := options.Find().SetSort(bson.M{"bucketNum": -1})
	buckets, err := r.GetUserGameBuckets(ctx, userID, opts)
	if err != nil {
		return fmt.Errorf("failed to get user buckets: %w", err)
	}

	if len(buckets) == 0 {
		newBucket := &models.UserGameBucket{
			ID:        primitive.NewObjectID(),
			UserID:    userID,
			BucketNum: 1,
			NumGames:  1,
			Games:     []primitive.ObjectID{gameID},
			StartTime: gameTime,
			EndTime:   gameTime,
		}
		return r.Create(ctx, newBucket)
	}

	client := r.collection.Database().Client()
	session, err := client.StartSession()
	if err != nil {
		return fmt.Errorf("failed to start session: %w", err)
	}
	defer session.EndSession(ctx)

	err = mongo.WithSession(ctx, session, func(sessCtx mongo.SessionContext) error {
		if err := session.StartTransaction(); err != nil {
			return fmt.Errorf("failed to start transaction: %w", err)
		}

		for _, bucket := range buckets {
			newBucketNum := bucket.BucketNum + 1

			filter := bson.M{"_id": bucket.ID}
			update := bson.M{"$set": bson.M{"bucketNum": newBucketNum}}

			if _, err := r.collection.UpdateOne(sessCtx, filter, update, options.Update().SetUpsert(true)); err != nil {
				session.AbortTransaction(sessCtx)
				return fmt.Errorf("failed to update bucket %d: %w", bucket.BucketNum, err)
			}
		}

		newBucket1 := &models.UserGameBucket{
			ID:        primitive.NewObjectID(),
			UserID:    userID,
			BucketNum: 1,
			NumGames:  1,
			Games:     []primitive.ObjectID{gameID},
			StartTime: gameTime,
			EndTime:   gameTime,
		}

		if _, err := r.collection.InsertOne(sessCtx, newBucket1); err != nil {
			session.AbortTransaction(sessCtx)
			return fmt.Errorf("failed to create new bucket 1: %w", err)
		}

		return session.CommitTransaction(sessCtx)
	})
	if err != nil {
		return fmt.Errorf("transaction failed: %w", err)
	}

	return nil
}
