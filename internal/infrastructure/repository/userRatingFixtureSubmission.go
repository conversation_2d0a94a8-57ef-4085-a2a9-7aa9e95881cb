package repository

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/fx"
)

type UserRatingFixtureSubmissionRepository interface {
	InsertOne(ctx context.Context, document *models.UserRatingFixtureSubmission) (*mongo.InsertOneResult, error)
	FindByUserID(ctx context.Context, userID primitive.ObjectID) (*models.UserRatingFixtureSubmission, error)
	UpdateOne(ctx context.Context, submission *models.UserRatingFixtureSubmission) (*mongo.UpdateResult, error)
}

type userRatingFixtureSubmissionRepository struct {
	collection *mongo.Collection
}

func NewUserRatingFixtureSubmissionRepository(lc fx.Lifecycle, db database.Database) UserRatingFixtureSubmissionRepository {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "User rating fixture submission repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down user rating fixture submission repository.")
			return nil
		},
	})
	return &userRatingFixtureSubmissionRepository{
		collection: db.Collection("userratingfixturesubmissions"),
	}
}

func (r *userRatingFixtureSubmissionRepository) InsertOne(ctx context.Context, document *models.UserRatingFixtureSubmission) (*mongo.InsertOneResult, error) {
	return r.collection.InsertOne(ctx, document)
}

func (r *userRatingFixtureSubmissionRepository) FindByUserID(ctx context.Context, userID primitive.ObjectID) (*models.UserRatingFixtureSubmission, error) {
	var submission models.UserRatingFixtureSubmission
	err := r.collection.FindOne(ctx, bson.M{"userId": userID}).Decode(&submission)
	if err != nil {
		return nil, err
	}
	return &submission, nil
}

func (r *userRatingFixtureSubmissionRepository) UpdateOne(ctx context.Context, submission *models.UserRatingFixtureSubmission) (*mongo.UpdateResult, error) {
	return r.collection.UpdateOne(
		ctx,
		bson.M{"_id": submission.ID},
		bson.M{"$set": submission},
	)
}
