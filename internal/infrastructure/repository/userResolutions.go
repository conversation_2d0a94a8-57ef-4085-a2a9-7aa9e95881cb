package repository

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/fx"
)

type UserResolutionRepository interface {
	InsertOne(ctx context.Context, document *models.UserResolution) (*mongo.InsertOneResult, error)
	FindByUserID(ctx context.Context, userID primitive.ObjectID) (*models.UserResolution, error)
	UpdateOne(ctx context.Context, pledge *models.UserResolution) (*mongo.UpdateResult, error)
}

type userResolutionRepository struct {
	collection *mongo.Collection
}

func NewUserResolutionRepository(lc fx.Lifecycle, db database.Database) UserResolutionRepository {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "User resolution repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down user resolution repository.")
			return nil
		},
	})
	return &userResolutionRepository{
		collection: db.Collection("userResolutions"),
	}
}

func (r *userResolutionRepository) InsertOne(ctx context.Context, document *models.UserResolution) (*mongo.InsertOneResult, error) {
	return r.collection.InsertOne(ctx, document)
}

func (r *userResolutionRepository) FindByUserID(ctx context.Context, userID primitive.ObjectID) (*models.UserResolution, error) {
	var pledge models.UserResolution
	err := r.collection.FindOne(ctx, bson.M{"userId": userID}).Decode(&pledge)
	if err != nil {
		return nil, err
	}
	return &pledge, nil
}

func (r *userResolutionRepository) UpdateOne(ctx context.Context, pledge *models.UserResolution) (*mongo.UpdateResult, error) {
	return r.collection.UpdateOne(
		ctx,
		bson.M{"_id": pledge.ID},
		bson.M{"$set": pledge},
	)
}
