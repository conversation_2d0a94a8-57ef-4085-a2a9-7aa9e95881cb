package repository

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

type UserSettingsRepository interface {
	CreateUserSettings(ctx context.Context, settings models.CreateSettingsInput) error
	GetUserSettings(ctx context.Context, userID primitive.ObjectID) (*models.UserSettings, error)
	UpdateOne(ctx context.Context, filter, update bson.M, opts ...*options.UpdateOptions) error
}

type userSettingsRepository struct {
	collection *mongo.Collection
}

func NewUserSettingsRepository(lc fx.Lifecycle, db database.Database) UserSettingsRepository {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "User settings repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down user settings repository.")
			return nil
		},
	})
	return &userSettingsRepository{
		collection: db.Collection("usersettings"),
	}
}

func (r *userSettingsRepository) CreateUserSettings(ctx context.Context, settings models.CreateSettingsInput) error {
	if settings.UserID == primitive.NilObjectID {
		settings.UserID = primitive.NewObjectID()
	}
	_, err := r.collection.InsertOne(ctx, settings)
	if err != nil {
		return err
	}
	return nil
}

func (r *userSettingsRepository) GetUserSettings(ctx context.Context, userID primitive.ObjectID) (*models.UserSettings, error) {
	var settings models.UserSettings
	err := r.collection.FindOne(ctx, bson.M{"userId": userID}).Decode(&settings)
	if err != nil {
		return nil, err
	}
	return &settings, nil
}

func (r *userSettingsRepository) UpdateOne(ctx context.Context, filter, update bson.M, opts ...*options.UpdateOptions) error {
	_, err := r.collection.UpdateOne(ctx, filter, update, opts...)
	if err != nil {
		return err
	}
	return nil
}
