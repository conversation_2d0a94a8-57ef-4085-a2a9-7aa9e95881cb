package repository

import (
	"context"
	"errors"
	"time"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

type UserShadowBanRepository interface {
	Create(ctx context.Context, shadowBan *models.UserShadowBan) error
	FindByUserID(ctx context.Context, userID primitive.ObjectID) (*models.UserShadowBan, error)
	FindByID(ctx context.Context, id primitive.ObjectID) (*models.UserShadowBan, error)
	UpdateStatus(ctx context.Context, id primitive.ObjectID, status models.UserShadowBanStatus) error
	FindAll(ctx context.Context, page, pageSize int64) ([]*models.UserShadowBan, error)
	FindAllByStatus(ctx context.Context, status models.UserShadowBanStatus, page, pageSize int64) ([]*models.UserShadowBan, error)
	CountAllByStatus(ctx context.Context, status models.UserShadowBanStatus) (int64, error)
	Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.UserShadowBan, error)
	CountAll(ctx context.Context) (int64, error)
	Delete(ctx context.Context, id primitive.ObjectID) error
}

type userShadowBanRepository struct {
	collection *mongo.Collection
}

func NewUserShadowBanRepository(lc fx.Lifecycle, db database.Database) UserShadowBanRepository {
	collection := db.Collection("user_shadow_bans")
	userShadowBanRepo := userShadowBanRepository{collection: collection}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "User shadow ban repository initialized and ready.")
			go func() {
				err = userShadowBanRepo.syncIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for user shadow ban repository", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down user shadow ban repository.")
			return nil
		},
	})

	return &userShadowBanRepo
}

func (r *userShadowBanRepository) syncIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{{Key: "userId", Value: 1}}, Options: options.Index().SetUnique(true)},
		{Keys: bson.D{{Key: "createdAt", Value: -1}}},
		{Keys: bson.D{{Key: "status", Value: 1}}},
	})
}

func (r *userShadowBanRepository) Create(ctx context.Context, shadowBan *models.UserShadowBan) error {
	if shadowBan.ID.IsZero() {
		shadowBan.ID = primitive.NewObjectID()
	}
	shadowBan.CreatedAt = time.Now()

	// Use an atomic upsert operation to prevent race conditions
	now := time.Now()
	shadowBan.UpdatedAt = &now

	// Prepare the update document
	update := bson.M{
		"$set": bson.M{
			"status":       shadowBan.Status,
			"reason":       shadowBan.Reason,
			"detectionIds": shadowBan.DetectionIDs,
			"updatedAt":    now,
			"expiresAt":    shadowBan.ExpiresAt,
			"updatedBy":    shadowBan.UpdatedBy,
		},
		"$setOnInsert": bson.M{
			"_id":       shadowBan.ID,
			"createdAt": shadowBan.CreatedAt,
		},
	}

	// Use upsert option to either update an existing document or insert a new one
	opts := options.Update().SetUpsert(true)
	_, err := r.collection.UpdateOne(
		ctx,
		bson.M{"userId": shadowBan.UserID},
		update,
		opts,
	)
	if err != nil {
		zlog.Error(ctx, "Failed to upsert user shadow ban", err,
			zap.String("userId", shadowBan.UserID.Hex()))
		return err
	}

	return nil
}

func (r *userShadowBanRepository) FindByUserID(ctx context.Context, userID primitive.ObjectID) (*models.UserShadowBan, error) {
	var shadowBan models.UserShadowBan

	err := r.collection.FindOne(ctx, bson.M{"userId": userID}).Decode(&shadowBan)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return &shadowBan, nil
}

func (r *userShadowBanRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.UserShadowBan, error) {
	var shadowBan models.UserShadowBan

	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&shadowBan)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return &shadowBan, nil
}

func (r *userShadowBanRepository) UpdateStatus(ctx context.Context, id primitive.ObjectID, status models.UserShadowBanStatus) error {
	now := time.Now()
	_, err := r.collection.UpdateOne(
		ctx,
		bson.M{"_id": id},
		bson.M{
			"$set": bson.M{
				"status":    status,
				"updatedAt": now,
			},
		},
	)
	return err
}

func (r *userShadowBanRepository) FindAll(ctx context.Context, page, pageSize int64) ([]*models.UserShadowBan, error) {
	var shadowBans []*models.UserShadowBan

	opts := options.Find().
		SetSort(bson.D{{Key: "createdAt", Value: -1}}).
		SetSkip((page - 1) * pageSize).
		SetLimit(pageSize)

	cursor, err := r.collection.Find(ctx, bson.M{}, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if err = cursor.All(ctx, &shadowBans); err != nil {
		return nil, err
	}

	return shadowBans, nil
}

func (r *userShadowBanRepository) FindAllByStatus(ctx context.Context, status models.UserShadowBanStatus, page, pageSize int64) ([]*models.UserShadowBan, error) {
	var shadowBans []*models.UserShadowBan

	opts := options.Find().
		SetSort(bson.D{{Key: "createdAt", Value: -1}}).
		SetSkip((page - 1) * pageSize).
		SetLimit(pageSize)
	cursor, err := r.collection.Find(ctx, bson.M{"status": status}, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if err = cursor.All(ctx, &shadowBans); err != nil {
		return nil, err
	}

	return shadowBans, nil
}

func (r *userShadowBanRepository) CountAllByStatus(ctx context.Context, status models.UserShadowBanStatus) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{"status": status})
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (r *userShadowBanRepository) Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.UserShadowBan, error) {
	var shadowBans []*models.UserShadowBan

	cursor, err := r.collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if err = cursor.All(ctx, &shadowBans); err != nil {
		return nil, err
	}

	return shadowBans, nil
}

func (r *userShadowBanRepository) CountAll(ctx context.Context) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (r *userShadowBanRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	_, err := r.collection.DeleteOne(ctx, bson.M{"_id": id})
	return err
}
