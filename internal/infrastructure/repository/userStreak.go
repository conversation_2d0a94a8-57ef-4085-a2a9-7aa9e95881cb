package repository

import (
	"context"
	"errors"

	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

type UserStreakRepository interface {
	GetStreakHistoryByUserID(ctx context.Context, userID primitive.ObjectID) (*models.StreakHistory, error)
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpsertStreakHistoryEntries(ctx context.Context, userId primitive.ObjectID, entries []*models.StreakEntry) error
}

type userStreakRepository struct {
	collection *mongo.Collection
}

func NewUserStreakRepository(lc fx.Lifecycle, db database.Database) UserStreakRepository {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "User streak repository initialized and ready.")
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down user streak repository.")
			return nil
		},
	})
	return &userStreakRepository{
		collection: db.Collection("userStreaks"),
	}
}

func (r *userStreakRepository) GetStreakHistoryByUserID(ctx context.Context, userID primitive.ObjectID) (*models.StreakHistory, error) { // Returns a single StreakHistory document
	var streakHistory models.StreakHistory
	err := r.collection.FindOne(ctx, bson.M{"_id": userID}).Decode(&streakHistory)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &streakHistory, nil
}

func (r *userStreakRepository) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := r.collection.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
	return err
}

func (r *userStreakRepository) UpsertStreakHistoryEntries(ctx context.Context, userId primitive.ObjectID, entries []*models.StreakEntry) error {
	filter := bson.M{"_id": userId}
	update := bson.M{"$set": bson.M{"streakHistoryObj": entries}}
	opts := options.Update().SetUpsert(true)

	_, err := r.collection.UpdateOne(ctx, filter, update, opts)
	return err
}
