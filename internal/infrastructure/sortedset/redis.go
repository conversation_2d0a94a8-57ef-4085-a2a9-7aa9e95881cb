package sortedset

import (
	"context"

	"github.com/redis/go-redis/v9"
)

type redisCache struct {
	client *redis.Client
}

type SortedSet interface {
	ZAdd(ctx context.Context, key string, members ...redis.Z) error
	ZRemRangeByScore(ctx context.Context, key, min, max string) error
	ZCount(ctx context.Context, key, min, max string) (int64, error)
	ZRevRangeWithScores(ctx context.Context, key string, start, stop int64) ([]redis.Z, error)
	ZScore(ctx context.Context, key, member string) (float64, error)
	ZRangeByScore(ctx context.Context, key string, opt *redis.ZRangeBy) ([]string, error)
	ZRem(ctx context.Context, key, entry string) error
	ZRevRank(ctx context.Context, key, entry string) (int64, error)
	ZRevRangeByScoreWithScores(ctx context.Context, key string, opt *redis.ZRangeBy) ([]redis.Z, error)
	ZCard(ctx context.Context, key string) (int64, error)
	ZRangeWithScores(ctx context.Context, key string, start, stop int64) ([]redis.Z, error)
}

func NewRedisSortedSet(client *redis.Client) SortedSet {
	return &redisCache{client: client}
}

func (c *redisCache) ZAdd(ctx context.Context, key string, members ...redis.Z) error {
	return c.client.ZAdd(ctx, key, members...).Err()
}

func (c *redisCache) ZRemRangeByScore(ctx context.Context, key, min, max string) error {
	return c.client.ZRemRangeByScore(ctx, key, min, max).Err()
}

func (c *redisCache) ZCount(ctx context.Context, key, min, max string) (int64, error) {
	return c.client.ZCount(ctx, key, min, max).Result()
}

func (c *redisCache) ZRevRangeWithScores(ctx context.Context, key string, start, stop int64) ([]redis.Z, error) {
	return c.client.ZRevRangeWithScores(ctx, key, start, stop).Result()
}

func (c *redisCache) ZScore(ctx context.Context, key, member string) (float64, error) {
	return c.client.ZScore(ctx, key, member).Result()
}

func (c *redisCache) ZRangeByScore(ctx context.Context, key string, opt *redis.ZRangeBy) ([]string, error) {
	return c.client.ZRangeByScore(ctx, key, opt).Result()
}

func (c *redisCache) ZRem(ctx context.Context, key, entry string) error {
	return c.client.ZRem(ctx, key, entry).Err()
}

func (c *redisCache) ZRevRank(ctx context.Context, key, entry string) (int64, error) {
	return c.client.ZRevRank(ctx, key, entry).Result()
}

func (c *redisCache) ZRevRangeByScoreWithScores(ctx context.Context, key string, opt *redis.ZRangeBy) ([]redis.Z, error) {
	return c.client.ZRevRangeByScoreWithScores(ctx, key, opt).Result()
}

func (c *redisCache) ZCard(ctx context.Context, key string) (int64, error) {
	return c.client.ZCard(ctx, key).Result()
}

func (c *redisCache) ZRangeWithScores(ctx context.Context, key string, start, stop int64) ([]redis.Z, error) {
	return c.client.ZRangeWithScores(ctx, key, start, stop).Result()
}
