package storage

import (
	"context"
	"encoding/base64"
	"fmt"

	"cloud.google.com/go/storage"
	"google.golang.org/api/option"

	"matiksOfficial/matiks-server-go/pkg/config"
)

type Storage struct {
	Storage *storage.Client
}

func InitializeStorageClient(cfg *config.Config) (*Storage, error) {
	serviceAccountKey, err := getServiceAccountKeyFromEnv(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to get service account key: %v", err)
	}

	ctx := context.Background()
	client, err := storage.NewClient(ctx, option.WithCredentialsJSON(serviceAccountKey))
	if err != nil {
		return nil, fmt.Errorf("failed to create Storage client: %v", err)
	}

	return &Storage{client}, nil
}

func getServiceAccountKeyFromEnv(cfg *config.Config) ([]byte, error) {
	encodedKey := cfg.FirebaseStorageServiceKey
	if encodedKey == "" {
		return nil, fmt.Errorf("environment variable FIREBASE_SERVICE_ACCOUNT_KEY not set")
	}

	decodedKey, err := base64.StdEncoding.DecodeString(encodedKey)
	if err != nil {
		return nil, fmt.Errorf("failed to decode service account key: %v", err)
	}

	return decodedKey, nil
}
