package websocket

import (
	"context"
	"encoding/json"
	"sync"
	"time"

	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"

	"matiksOfficial/matiks-server-go/internal/infrastructure/pubsub"
	"matiksOfficial/matiks-server-go/internal/middleware"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/pkg/metrics"

	"github.com/gorilla/websocket"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"
)

type Client struct {
	conn   *websocket.Conn
	server *websocketHanlder
	send   chan []byte
	subs   map[string]pubsub.Subscription
	mu     sync.RWMutex
	ctx    context.Context
}

func newClient(ctx context.Context, conn *websocket.Conn, server *websocketHanlder) *Client {
	return &Client{
		conn:   conn,
		server: server,
		send:   make(chan []byte, 256),
		subs:   make(map[string]pubsub.Subscription),
		ctx:    ctx,
	}
}

func (c *Client) readPump() {
	defer func() {
		c.close()
		metrics.Get().WebsocketDisconnected(c.ctx)
		c.server.clientsMu.Lock()
		delete(c.server.clients, c)
		c.server.clientsMu.Unlock()
	}()

	c.conn.SetReadLimit(maxMessageSize)
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})

	for {
		_, message, err := c.conn.ReadMessage()
		messageStart := time.Now()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				metrics.Get().RecordWebsocketError(c.ctx, "read_error")
				zlog.Error(c.ctx, "Error reading message", err)
			}
			break
		}

		var span trace.Span
		if websocketTracingEnabled {
			c.ctx, span = middleware.WithSpan(c.ctx, "websocket.read")
		}

		var msg Message
		if err := json.Unmarshal(message, &msg); err != nil {
			if websocketTracingEnabled {
				span.RecordError(err)
				span.SetStatus(codes.Error, err.Error())
				span.End()
			}
			metrics.Get().RecordWebsocketError(c.ctx, "unmarshal_error")
			zlog.Error(c.ctx, "Error unmarshaling message", err)
			continue
		}

		if websocketTracingEnabled {
			span.SetAttributes(attribute.String("message.type", string(msg.Type)))
		}

		c.server.mu.RLock()
		handler, exists := c.server.handlers[msg.Type]
		c.server.mu.RUnlock()

		if exists {
			if err := handler(c.ctx, c, msg); err != nil {
				if websocketTracingEnabled {
					span.RecordError(err)
					span.SetStatus(codes.Error, err.Error())
					span.End()
				}
				metrics.Get().RecordWebsocketError(c.ctx, "handleing_error")
				zlog.Error(c.ctx, "Error handling message", err)
			}
		} else {
			zlog.Warn(c.ctx, "No handler registered for the message type", zap.String("messageType", string(msg.Type)))
		}
		metrics.Get().RecordWebsocketMessage(c.ctx, string(msg.Type), time.Since(messageStart))
		if websocketTracingEnabled {
			span.End()
		}
	}
}

func (c *Client) writePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			var span trace.Span
			if websocketTracingEnabled {
				c.ctx, span = middleware.WithSpan(c.ctx, "websocket.write")
			}
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				if websocketTracingEnabled {
					span.End()
				}
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				zlog.Error(c.ctx, "Failed to send message", err)
				if websocketTracingEnabled {
					span.RecordError(err)
					span.End()
				}
				return
			}
			w.Write(message)

			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				zlog.Error(c.ctx, "Failed to send message", err)
				if websocketTracingEnabled {
					span.RecordError(err)
					span.End()
				}
				return
			}
			if websocketTracingEnabled {
				span.End()
			}

		case <-ticker.C:
			var span trace.Span
			if websocketTracingEnabled {
				c.ctx, span = middleware.WithSpan(c.ctx, "websocket.ping")
			}
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				zlog.Error(c.ctx, "Failed to send ping", err)
				if websocketTracingEnabled {
					span.RecordError(err)
					span.End()
				}
				return
			}
			if websocketTracingEnabled {
				span.End()
			}

		case <-c.ctx.Done():
			return
		}
	}
}

func (c *Client) subscribe(channel string) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if _, exists := c.subs[channel]; exists {
		return nil
	}

	sub, err := c.server.pubsub.Subscribe(c.ctx, channel)
	if err != nil {
		return err
	}

	c.subs[channel] = sub

	go func() {
		defer func() {
			if r := recover(); r != nil {
				zlog.Info(c.ctx, "Recovered from panic in subscription handler", zap.String("channel", channel), zap.Any("recovered", r))
			}
		}()

		for {
			select {
			case msg, ok := <-sub.Channel():
				if !ok {
					return
				}

				data, err := json.Marshal(Message{
					Type:    PublishedMessage,
					Channel: channel,
					Data:    msg,
				})
				if err != nil {
					continue
				}

				select {
				case c.send <- data:
				case <-time.After(time.Second):
					zlog.Warn(c.ctx, "Failed to send message to client: channel full", zap.String("channel", channel))
				case <-c.ctx.Done():
					return
				}

			case <-c.ctx.Done():
				return
			}
		}
	}()

	return nil
}

func (c *Client) unsubscribe(channel string) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if sub, exists := c.subs[channel]; exists {
		err := sub.Close()
		delete(c.subs, channel)
		return err
	}
	return nil
}

func (c *Client) unsubscribeAll() {
	c.mu.Lock()
	defer c.mu.Unlock()

	for channel, sub := range c.subs {
		if err := sub.Close(); err != nil {
			zlog.Error(c.ctx, "Error closing subscription for channel", err, zap.String("channel", channel))
		}
	}
	c.subs = make(map[string]pubsub.Subscription)
}

func (c *Client) close() {
	c.unsubscribeAll()
	close(c.send)
	c.conn.Close()
}
