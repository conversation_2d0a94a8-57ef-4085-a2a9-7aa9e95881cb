package websocket

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"go.opentelemetry.io/otel/codes"
	"golang.org/x/time/rate"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/pubsub"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/pkg/metrics"

	"github.com/gorilla/websocket"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

const (
	writeWait               = 10 * time.Second
	pongWait                = 60 * time.Second
	pingPeriod              = (pongWait * 9) / 10
	maxMessageSize          = 512
	websocketTracingEnabled = false
)

type MessageType string

type Message struct {
	Type    MessageType `json:"type"`
	Channel string      `json:"channel"`
	Data    interface{} `json:"data"`
}

type Handler func(ctx context.Context, client *Client, msg Message) error

type ipRateLimiter struct {
	visitors map[string]*rate.Limiter
	mu       sync.Mutex
	r        rate.Limit
	b        int
}

// newIPRateLimiter creates an IP rate limiter that allows r events per second with a burst size of b.
func newIPRateLimiter(r rate.Limit, b int) *ipRateLimiter {
	return &ipRateLimiter{
		visitors: make(map[string]*rate.Limiter),
		r:        r,
		b:        b,
	}
}

// getVisitor returns the rate limiter for the given IP address, creating one if needed.
func (i *ipRateLimiter) getVisitor(ip string) *rate.Limiter {
	i.mu.Lock()
	defer i.mu.Unlock()

	limiter, exists := i.visitors[ip]
	if !exists {
		limiter = rate.NewLimiter(i.r, i.b)
		i.visitors[ip] = limiter
	}
	return limiter
}

type websocketHanlder struct {
	pubsub    pubsub.PubSub
	handlers  map[MessageType]Handler
	mu        sync.RWMutex
	clients   map[*Client]bool
	clientsMu sync.RWMutex
	limiter   *ipRateLimiter
}

type Websocket interface {
	Publish(ctx context.Context, channel string, message interface{}) error
	RegisterHandler(msgType MessageType, handler Handler)
	ServeHTTP(w http.ResponseWriter, r *http.Request)
	Subscribe(ctx context.Context, channel string) (<-chan interface{}, error)
	Shutdown(ctx context.Context) error
}

const (
	ChannelSubscribe       MessageType = "channel_subscribe"
	ChannelUnsubscribe     MessageType = "channel_unsubscribe"
	PublishedMessage       MessageType = "message"
	SubmitAnswer           MessageType = "submitAnswer"
	SubmitPuzzleGameAnswer MessageType = "submitPuzzleGameAnswer"
	PingPong               MessageType = "ping-pong"
	AddMessage             MessageType = "addMessage"
	SendMessage            MessageType = "sendMessage"
)

func NewServer(lc fx.Lifecycle, ps pubsub.PubSub) Websocket {
	server := &websocketHanlder{
		pubsub:   ps,
		handlers: make(map[MessageType]Handler),
		clients:  make(map[*Client]bool),
		limiter:  newIPRateLimiter(rate.Limit(5), 10),
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Info(ctx, "Starting websocket server")
			server.registerDefaultHandlers()
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Info(ctx, "Shutting down websocket server")
			return nil
		},
	})

	return server
}

func (s *websocketHanlder) Subscribe(ctx context.Context, channel string) (<-chan interface{}, error) {
	subscription, err := s.pubsub.Subscribe(ctx, channel)
	if err != nil {
		zlog.Error(ctx, "Failed to start subscription", err, zap.String("channel", channel))
		return nil, fmt.Errorf("failed to start subscription: %w", err)
	}

	outputChan := make(chan interface{})

	go func() {
		defer close(outputChan)
		defer subscription.Close()
		zlog.Debug(ctx, "Subscription started", zap.String("channel", channel))

		for {
			select {
			case <-ctx.Done():
				zlog.Info(ctx, "Subscription context cancelled", zap.Error(ctx.Err()), zap.String("channel", channel))
				return
			case msg, ok := <-subscription.Channel():
				if !ok {
					zlog.Warn(ctx, "Subscription channel closed unexpectedly", zap.String("channel", channel))
					return
				}

				select {
				case outputChan <- msg:
					zlog.Debug(ctx, "Message sent to client", zap.String("channel", channel))
				case <-ctx.Done():
					zlog.Info(ctx, "Subscription context cancelled while sending message", zap.Error(ctx.Err()), zap.String("channel", channel))
					return
				}
			}
		}
	}()

	return outputChan, nil
}

func (s *websocketHanlder) Publish(ctx context.Context, channel string, message interface{}) error {
	return s.pubsub.Publish(ctx, channel, message)
}

func (s *websocketHanlder) registerDefaultHandlers() {
	s.RegisterHandler(ChannelSubscribe, func(ctx context.Context, client *Client, msg Message) error {
		if msg.Channel == "" {
			return nil
		}
		zlog.Debug(ctx, "Client subscribing to channel", zap.String("channel", msg.Channel))

		if err := client.subscribe(msg.Channel); err != nil {
			zlog.Error(ctx, "Failed to subscribe to channel", err, zap.String("channel", msg.Channel))
			return err
		}
		return nil
	})

	s.RegisterHandler(ChannelUnsubscribe, func(ctx context.Context, client *Client, msg Message) error {
		if msg.Channel == "" {
			return nil
		}
		zlog.Debug(ctx, "Client unsubscribing from channel", zap.String("channel", msg.Channel))

		if err := client.unsubscribe(msg.Channel); err != nil {
			zlog.Error(ctx, "Failed to unsubscribe from channel", err, zap.String("channel", msg.Channel))
			return err
		}
		return nil
	})

	s.RegisterHandler(PublishedMessage, func(ctx context.Context, client *Client, msg Message) error {
		if msg.Channel == "" || msg.Data == nil {
			return nil
		}

		client.mu.RLock()
		_, subscribed := client.subs[msg.Channel]
		client.mu.RUnlock()

		if !subscribed {
			zlog.Warn(ctx, "Client attempted to publish to unsubscribed channel", zap.String("channel", msg.Channel))
			return nil
		}

		return s.pubsub.Publish(ctx, msg.Channel, msg.Data)
	})
}

func (s *websocketHanlder) RegisterHandler(msgType MessageType, handler Handler) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.handlers[msgType] = handler
}

func (s *websocketHanlder) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	var span trace.Span
	if websocketTracingEnabled {
		serviceName := utils.GetServiceNameFromContext(ctx)
		tracer := otel.GetTracerProvider().Tracer(serviceName)
		ctx, span = tracer.Start(ctx, "websocket.connect",
			trace.WithAttributes(
				attribute.String("http.url", r.URL.String()),
			),
		)
		defer span.End()
	}

	upgrader := websocket.Upgrader{
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		if websocketTracingEnabled {
			span.RecordError(err)
			span.SetStatus(codes.Error, err.Error())
		}
		metrics.Get().RecordWebsocketError(ctx, "upgrade_error")
		http.Error(w, "Failed to upgrade WebSocket", http.StatusInternalServerError)
		zlog.Error(ctx, "Failed to upgrade connection", err)
		return
	}

	ctx = context.WithoutCancel(ctx)

	client := newClient(ctx, conn, s)

	metrics.Get().WebsocketConnected(ctx)

	s.clientsMu.Lock()
	s.clients[client] = true
	s.clientsMu.Unlock()

	go client.writePump()
	go client.readPump()
}

func (s *websocketHanlder) Shutdown(ctx context.Context) error {
	s.clientsMu.Lock()
	for client := range s.clients {
		client.close()
	}
	s.clientsMu.Unlock()
	return nil
}
