package middleware

import (
	"context"
	"net"
	"net/http"
	"strings"

	"matiksOfficial/matiks-server-go/internal/auth"
	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/pkg/config"
)

// canonicalizeIP returns a form of ip suitable for comparison to other IPs.
// For IPv4 addresses, this is simply the whole string.
// For IPv6 addresses, this is the /64 prefix.
func canonicalizeIP(ip string) string {
	isIPv6 := false
	// This is how net.ParseIP decides if an address is IPv6
	// https://cs.opensource.google/go/go/+/refs/tags/go1.17.7:src/net/ip.go;l=704
	for i := 0; !isIPv6 && i < len(ip); i++ {
		switch ip[i] {
		case '.':
			// IPv4
			return ip
		case ':':
			// IPv6
			isIPv6 = true
		}
	}
	if !isIPv6 {
		// Not an IP address at all
		return ip
	}

	ipv6 := net.ParseIP(ip)
	if ipv6 == nil {
		return ip
	}

	return ipv6.Mask(net.CIDRMask(64, 128)).String()
}

func getRealIP(r *http.Request) (string, error) {
	var ip string

	if tcip := r.Header.Get("True-Client-IP"); tcip != "" {
		ip = tcip
	} else if xrip := r.Header.Get("X-Real-IP"); xrip != "" {
		ip = xrip
	} else if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		i := strings.Index(xff, ", ")
		if i == -1 {
			i = len(xff)
		}
		ip = xff[:i]
	} else {
		var err error
		ip, _, err = net.SplitHostPort(r.RemoteAddr)
		if err != nil {
			ip = r.RemoteAddr
		}
	}

	return canonicalizeIP(ip), nil
}

func AuthMiddleware(authService auth.AuthStore, cfg *config.Config) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			token := extractToken(r) // New helper function

			if token != "" {
				if claims, err := authService.VerifyToken(token); err == nil {
					if userID, ok := claims["id"].(string); ok {
						ctx = context.WithValue(ctx, constants.UserContextKey, userID)
					}
				}
			}

			// Extract and validate client info
			ip, _ := getRealIP(r)
			ctx = enrichContextWithClientInfo(ctx, r, ip, cfg)

			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

func extractToken(r *http.Request) string {
	// Extract from Authorization header
	if auth := r.Header.Get("Authorization"); auth != "" {
		if len(auth) > 7 && strings.ToUpper(auth[0:7]) == "BEARER " {
			return auth[7:]
		}
	}
	// Fallback to query parameter
	return r.URL.Query().Get("token")
}

func enrichContextWithClientInfo(ctx context.Context, r *http.Request, ip string, cfg *config.Config) context.Context {
	ctx = context.WithValue(ctx, constants.IPKey, ip)
	ctx = context.WithValue(ctx, constants.UserAgentKey, r.Header.Get("User-Agent"))
	ctx = context.WithValue(ctx, constants.UserTimezoneKey, r.Header.Get("X-Timezone"))

	serviceName := constants.DevServiceName
	if cfg.Environment == constants.ProdEnvironment {
		serviceName = constants.ProdServiceName
	}
	return context.WithValue(ctx, constants.ServiceName, serviceName)
}
