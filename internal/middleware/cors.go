package middleware

import (
	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/pkg/config"

	"github.com/go-chi/cors"
)

func CorsMiddleware(cfg *config.Config) *cors.Cors {
	return cors.New(cors.Options{
		AllowedOrigins:   cfg.AllowedOrigins,
		AllowedMethods:   []string{"GET", "POST", "OPTIONS"},
		AllowedHeaders:   []string{"Accept", "Authorization", "Content-Type", "X-CSRF-Token", "X-Timezone", "X-Request-Id"},
		ExposedHeaders:   []string{"Link", "X-Request-Id"},
		AllowCredentials: true,
		MaxAge:           300,
		Debug:            cfg.Environment != constants.ProdEnvironment, // Only enable debug in non-prod
	})
}
