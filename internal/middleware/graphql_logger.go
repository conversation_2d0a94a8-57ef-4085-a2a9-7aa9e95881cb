package middleware

import (
	"context"
	"time"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"github.com/99designs/gqlgen/graphql"
	"go.uber.org/zap"
)

type GraphQLLogger struct{}

func NewGraphQLLogger() *GraphQLLogger {
	return &GraphQLLogger{}
}

func (l *GraphQLLogger) Middleware() graphql.OperationMiddleware {
	return func(ctx context.Context, next graphql.OperationHandler) graphql.ResponseHandler {
		oc := graphql.GetOperationContext(ctx)

		zlog.Info(ctx, "GraphQL operation: "+oc.OperationName,
			zap.String("operation", oc.Operation.Name),
			zap.Any("variables", oc.Variables),
			zap.Any("stats", oc.Stats),
		)

		return next(ctx)
	}
}

func (l *GraphQLLogger) ResolverMiddleware() graphql.FieldMiddleware {
	return func(ctx context.Context, next graphql.Resolver) (interface{}, error) {
		start := time.Now()
		rctx := graphql.GetFieldContext(ctx)

		res, err := next(ctx)
		duration := time.Since(start)

		if err != nil {
			fields := []zap.Field{
				zap.String("field", rctx.Field.Name),
				zap.String("type", rctx.Field.Definition.Type.String()),
				zap.Any("arguments", rctx.Args),
				zap.Any("result", rctx.Result),
				zap.Duration("duration", duration),
			}
			zlog.Error(ctx, "Resolver error", err, fields...)
		}

		if rctx.Object == "Subscription" {
			zlog.Info(ctx, "Subscription event",
				zap.String("field", rctx.Field.Name),
				zap.Any("arguments", rctx.Args),
				zap.Any("result", rctx.Result),
			)
		}

		return res, err
	}
}

func (l *GraphQLLogger) AsExtension() graphql.HandlerExtension {
	return l
}

func (l *GraphQLLogger) ExtensionName() string {
	return "GraphQLLogger"
}

func (l *GraphQLLogger) Validate(schema graphql.ExecutableSchema) error {
	return nil
}

func (l *GraphQLLogger) InterceptOperation(ctx context.Context, next graphql.OperationHandler) graphql.ResponseHandler {
	return l.Middleware()(ctx, next)
}

func (l *GraphQLLogger) InterceptField(ctx context.Context, next graphql.Resolver) (interface{}, error) {
	return l.ResolverMiddleware()(ctx, next)
}
