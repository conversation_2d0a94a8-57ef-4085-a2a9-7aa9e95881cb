package middleware

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"net/http"
)

type contextKey string

const IntrospectionContextKey contextKey = "introspectionEnabled"

func IntrospectionMiddleware(secretKey string, next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Get the key from the header
		key := r.Header.Get("X-Admin-Key")

		// Create a new HMAC by defining the hash type and the key (as byte array)
		h := hmac.New(sha256.New, []byte(secretKey))

		// Write Data to it
		h.Write([]byte("admin_key"))

		// Get result and encode as hexadecimal string
		expectedSignature := hex.EncodeToString(h.Sum(nil))

		// Check if the signature matches the expected signature
		if key == expectedSignature {
			// If it matches, add a flag to the context
			ctx := context.WithValue(r.Context(), IntrospectionContextKey, true)
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}

		// If it doesn't match, return a 403 Forbidden error
		http.Error(w, "Forbidden", http.StatusForbidden)
	})
}

func IsIntrospectionEnabled(ctx context.Context) bool {
	enabled, ok := ctx.Value(IntrospectionContextKey).(bool)
	return ok && enabled
}
