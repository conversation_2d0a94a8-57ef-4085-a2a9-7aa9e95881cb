package middleware

import (
	"net"
	"net/http"
	"strconv"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"github.com/go-chi/chi/v5/middleware"
	"go.uber.org/zap"
)

type reqLogEntry struct {
	RequestID     string `json:"requestId,omitempty"`
	UserID        string `json:"userId,omitempty"`
	RequestMethod string `json:"requestMethod,omitempty"`
	RequestUrl    string `json:"requestUrl,omitempty"`
	RequestSize   string `json:"requestSize,omitempty"`
	Status        int    `json:"status,omitempty"`
	ResponseSize  string `json:"responseSize,omitempty"`
	UserAgent     string `json:"userAgent,omitempty"`
	RemoteIp      string `json:"remoteIp,omitempty"`
	ServerIp      string `json:"serverIp,omitempty"`
	Referer       string `json:"referer,omitempty"`
	Latency       string `json:"latency,omitempty"`
	Protocol      string `json:"protocol,omitempty"`
}

func RequestLogger() func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()
			ww := middleware.NewWrapResponseWriter(w, r.ProtoMajor)

			defer func() {
				// Add request tracking ID
				requestID := middleware.GetReqID(r.Context())

				// Get user ID if authenticated
				userID, _ := r.Context().Value(constants.UserContextKey).(string)

				entry := createLogEntry(r, ww, start, requestID, userID)
				zlog.Info(r.Context(), "httpRequest", zap.Any("httpRequest", entry))
			}()

			next.ServeHTTP(ww, r)
		})
	}
}

func createLogEntry(r *http.Request, ww middleware.WrapResponseWriter, start time.Time, requestID, userID string) reqLogEntry {
	serverIP, _, _ := net.SplitHostPort(r.Context().Value(http.LocalAddrContextKey).(net.Addr).String())

	return reqLogEntry{
		RequestID:     requestID,
		UserID:        userID,
		RequestMethod: r.Method,
		RequestUrl:    r.URL.String(),
		RequestSize:   r.Header.Get("Content-Length"),
		Status:        ww.Status(),
		ResponseSize:  strconv.Itoa(ww.BytesWritten()),
		UserAgent:     r.UserAgent(),
		RemoteIp:      r.RemoteAddr,
		ServerIp:      serverIP,
		Referer:       r.Referer(),
		Latency:       time.Since(start).String(),
		Protocol:      r.Proto,
	}
}
