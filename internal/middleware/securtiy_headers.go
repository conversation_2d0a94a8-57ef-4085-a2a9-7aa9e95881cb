package middleware

import (
	"net/http"

	"github.com/go-chi/chi/v5/middleware"
)

func SecurityHeaders(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("X-XSS-Protection", "1; mode=block")
		w.<PERSON><PERSON>().Set("X-Frame-Options", "DENY")
		w.<PERSON><PERSON>().Set("X-Content-Type-Options", "nosniff")
		w.<PERSON><PERSON>().Set("Referrer-Policy", "strict-origin-when-cross-origin")
		w.<PERSON>er().Set("Content-Security-Policy",
			"default-src 'self'; "+
				"script-src 'self' 'unsafe-inline' 'unsafe-eval'; "+
				"style-src 'self' 'unsafe-inline'; "+
				"img-src 'self' data: https:; "+
				"connect-src 'self' wss:")
		w.<PERSON><PERSON>().Set("Permissions-Policy", "geolocation=(), microphone=(), camera=()")
		w.<PERSON><PERSON>().Set("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		w.<PERSON><PERSON>().Set("X-Request-Id", middleware.GetReqID(r.Context()))
		next.ServeHTTP(w, r)
	})
}
