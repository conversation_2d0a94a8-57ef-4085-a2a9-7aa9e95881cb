package middleware

import (
	"context"
	"math/rand/v2"
	"runtime"
	"sync/atomic"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/pkg/metrics"
)

// Operational metrics for adaptive sampling
var (
	currentLoad               atomic.Int64
	lastLoadUpdate            time.Time
	maxConcurrency            = runtime.GOMAXPROCS(0) * 100
	samplingPeriod            = 2 * time.Second
	fieldLevelTracingDisabled = true
)

func WithSpan(ctx context.Context, name string, opts ...trace.SpanStartOption) (context.Context, trace.Span) {
	serviceName := utils.GetServiceNameFromContext(ctx)
	tracer := otel.GetTracerProvider().Tracer(serviceName)
	return tracer.Start(ctx, name, opts...)
}

type GraphQLTracer struct{}

func NewGraphQLTracer() *GraphQLTracer {
	return &GraphQLTracer{}
}

func (t *GraphQLTracer) ExtensionName() string {
	return "GraphQLTracer"
}

func (t *GraphQLTracer) Validate(schema graphql.ExecutableSchema) error {
	return nil
}

func (t *GraphQLTracer) InterceptOperation(ctx context.Context, next graphql.OperationHandler) graphql.ResponseHandler {
	serviceName := utils.GetServiceNameFromContext(ctx)
	if !shouldTrace() {
		return next(ctx)
	}
	oc := graphql.GetOperationContext(ctx)
	tracer := otel.GetTracerProvider().Tracer(serviceName)
	ctx, span := tracer.Start(ctx, oc.OperationName, trace.WithAttributes(
		attribute.String("graphql.operation", oc.Operation.Name),
		attribute.String("graphql.operationName", oc.OperationName),
		attribute.Int64("graphql.concurrency", currentLoad.Load()),
	))
	ctx = context.WithValue(ctx, constants.SpanContextKey, span)

	currentLoad.Add(1)
	return next(ctx)
}

func (t *GraphQLTracer) InterceptField(ctx context.Context, next graphql.Resolver) (interface{}, error) {
	if fieldLevelTracingDisabled {
		return next(ctx)
	}

	if !shouldTrace() {
		return next(ctx)
	}

	serviceName := utils.GetServiceNameFromContext(ctx)
	fc := graphql.GetFieldContext(ctx)

	tracer := otel.GetTracerProvider().Tracer(serviceName)
	ctx, span := tracer.Start(ctx, "Field_"+fc.Field.Name, trace.WithAttributes(
		attribute.String("graphql.fieldName", fc.Field.Name),
		attribute.String("graphql.parentType", fc.Object),
		attribute.String("graphql.fieldPath", fc.Path().String()),
		attribute.String("graphql.returnType", fc.Field.Definition.Type.String()),
	))

	defer span.End()

	return next(ctx)
}

func (t *GraphQLTracer) InterceptResponse(ctx context.Context, next graphql.ResponseHandler) *graphql.Response {
	resp := next(ctx)
	currentLoad.Add(-1)
	span, err := utils.GetSpanFromContext(ctx)
	if err == nil && span != nil {
		span.End()
	}
	ok := graphql.HasOperationContext(ctx)
	if !ok {
		return resp
	}
	oc := graphql.GetOperationContext(ctx)

	duration := time.Since(oc.Stats.OperationStart)

	if oc.Operation == nil || oc.Operation.Operation == "" {
		return resp
	}

	metrics.Get().RecordGraphQLOperation(
		ctx,
		string(oc.Operation.Operation),
		oc.OperationName,
		duration,
	)

	if len(resp.Errors) > 0 {
		metrics.Get().RecordGraphQLError(ctx, "operation_error")
	}

	return resp
}

func (t *GraphQLTracer) AsExtension() graphql.HandlerExtension {
	return t
}

func shouldTrace() bool {
	// Update load metrics periodically
	now := time.Now()
	if now.Sub(lastLoadUpdate) > samplingPeriod {
		lastLoadUpdate = now
	}

	// Calculate sampling rate based on current load
	load := float64(currentLoad.Load()) / float64(maxConcurrency)

	// Adaptive sampling rates based on load:
	// - Under 30% load: sample 30% of requests
	// - 30-60% load: sample 20% of requests
	// - 60-80% load: sample 10% of requests
	// - Over 80% load: sample 5% of requests
	switch {
	case load < 0.3:
		return rand.Float64() < 0.3
	case load < 0.6:
		return rand.Float64() < 0.2
	case load < 0.8:
		return rand.Float64() < 0.1
	default:
		return rand.Float64() < 0.05
	}
}
