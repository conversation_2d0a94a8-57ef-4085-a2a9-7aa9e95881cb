package models

import (
	"fmt"
	"io"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type (
	CTAActionType    string
	AnnouncementType string
)

type CallToAction struct {
	Text       string        `bson:"text" json:"text"`
	Target     *string       `bson:"target,omitempty" json:"target,omitempty"` // Optional
	ActionType CTAActionType `bson:"actionType" json:"actionType"`             // Store enum type
	Style      *string       `bson:"style,omitempty" json:"style,omitempty"`   // Optional
}

// Announcement represents an announcement stored in the database.
type Announcement struct {
	ID               primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`
	Type             AnnouncementType   `bson:"type" json:"type"`                       // Store enum type
	Title            *string            `bson:"title,omitempty" json:"title,omitempty"` // Optional
	Description      string             `bson:"description" json:"description"`
	ImageURL         *string            `bson:"imageUrl,omitempty" json:"imageUrl,omitempty"` // Optional
	MediaURL         *string            `bson:"mediaUrl,omitempty" json:"mediaUrl,omitempty"` // Optional
	CTAs             []CallToAction     `bson:"ctas,omitempty" json:"ctas,omitempty"`         // Optional list
	Priority         int                `bson:"priority" json:"priority"`                     // Default 0
	CreatedAt        time.Time          `bson:"createdAt" json:"createdAt"`
	PublishedAt      *time.Time         `bson:"publishedAt,omitempty" json:"publishedAt,omitempty"` // Optional, controls visibility start
	ExpiresAt        *time.Time         `bson:"expiresAt,omitempty" json:"expiresAt,omitempty"`     // Optional, controls visibility end
	RiveAnimationURL *string            `bson:"riveAnimationUrl,omitempty" json:"riveAnimationUrl,omitempty"`
}

// UserAnnouncementStatus tracks the read status of an announcement for a specific user.
// This would typically be stored in a separate collection (e.g., "user_announcement_statuses").
type UserAnnouncementStatus struct {
	ID             primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`
	UserID         primitive.ObjectID `bson:"userId" json:"userId"`                 // Foreign key to the users collection
	AnnouncementID primitive.ObjectID `bson:"announcementId" json:"announcementId"` // Foreign key to the announcements collection
	ReadAt         time.Time          `bson:"readAt" json:"readAt"`                 // Timestamp when the user marked it as read
}

// Input for creating a CallToAction.
type CTAInput struct {
	Text       string        `json:"text" bson:"text"`
	Target     *string       `json:"target,omitempty" bson:"target"`
	ActionType CTAActionType `json:"actionType" bson:"actionType"`
	Style      *string       `json:"style,omitempty" bson:"style"`
}

// Input for creating a new announcement.
type CreateAnnouncementInput struct {
	Type             AnnouncementType `json:"type" bson:"type"`
	Title            *string          `json:"title,omitempty" bson:"title"`
	Description      string           `json:"description" bson:"description"`
	ImageURL         *string          `json:"imageUrl,omitempty" bson:"imageUrl"`
	MediaURL         *string          `json:"mediaUrl,omitempty" bson:"mediaUrl"`
	Ctas             []*CTAInput      `json:"ctas,omitempty" bson:"ctas"`
	Priority         *int             `json:"priority,omitempty" bson:"priority"`
	PublishedAt      *time.Time       `json:"publishedAt,omitempty" bson:"publishedAt"`
	ExpiresAt        *time.Time       `json:"expiresAt,omitempty" bson:"expiresAt"`
	RiveAnimationURL *string          `json:"riveAnimationUrl,omitempty" bson:"riveAnimationUrl"`
}

// Standard response for mutations that primarily indicate success/failure.
type AnnouncementMutationResponse struct {
	Success bool    `json:"success" bson:"success"`
	Message *string `json:"message,omitempty" bson:"message"`
}

// Input for updating an existing announcement. Fields are optional.
type UpdateAnnouncementInput struct {
	Type             *AnnouncementType `json:"type,omitempty" bson:"type"`
	Title            *string           `json:"title,omitempty" bson:"title"`
	Description      *string           `json:"description,omitempty" bson:"description"`
	ImageURL         *string           `json:"imageUrl,omitempty" bson:"imageUrl"`
	MediaURL         *string           `json:"mediaUrl,omitempty" bson:"mediaUrl"`
	Ctas             []*CTAInput       `json:"ctas,omitempty" bson:"ctas"`
	Priority         *int              `json:"priority,omitempty" bson:"priority"`
	PublishedAt      *time.Time        `json:"publishedAt,omitempty" bson:"publishedAt"`
	ExpiresAt        *time.Time        `json:"expiresAt,omitempty" bson:"expiresAt"`
	RiveAnimationURL *string           `json:"riveAnimationUrl,omitempty" bson:"riveAnimationUrl"`
}

const (
	AnnouncementTypeGeneral       AnnouncementType = "GENERAL"
	AnnouncementTypeFeatureUpdate AnnouncementType = "FEATURE_UPDATE"
	AnnouncementTypePromotion     AnnouncementType = "PROMOTION"
	AnnouncementTypeEvent         AnnouncementType = "EVENT"
	AnnouncementTypeMaintenance   AnnouncementType = "MAINTENANCE"
	AnnouncementTypeSurvey        AnnouncementType = "SURVEY"
)

var AllAnnouncementType = []AnnouncementType{
	AnnouncementTypeGeneral,
	AnnouncementTypeFeatureUpdate,
	AnnouncementTypePromotion,
	AnnouncementTypeEvent,
	AnnouncementTypeMaintenance,
	AnnouncementTypeSurvey,
}

func (e AnnouncementType) IsValid() bool {
	switch e {
	case AnnouncementTypeGeneral, AnnouncementTypeFeatureUpdate, AnnouncementTypePromotion, AnnouncementTypeEvent, AnnouncementTypeMaintenance, AnnouncementTypeSurvey:
		return true
	}
	return false
}

func (e AnnouncementType) String() string {
	return string(e)
}

func (e *AnnouncementType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = AnnouncementType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid AnnouncementType", str)
	}
	return nil
}

func (e AnnouncementType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

const (
	CTAActionTypeOpenURL          CTAActionType = "OPEN_URL"
	CTAActionTypeNavigateInternal CTAActionType = "NAVIGATE_INTERNAL"
	CTAActionTypeDismiss          CTAActionType = "DISMISS"
)

var AllCTAActionType = []CTAActionType{
	CTAActionTypeOpenURL,
	CTAActionTypeNavigateInternal,
	CTAActionTypeDismiss,
}

func (e CTAActionType) IsValid() bool {
	switch e {
	case CTAActionTypeOpenURL, CTAActionTypeNavigateInternal, CTAActionTypeDismiss:
		return true
	}
	return false
}

func (e CTAActionType) String() string {
	return string(e)
}

func (e *CTAActionType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = CTAActionType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid CTAActionType", str)
	}
	return nil
}

func (e CTAActionType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}
