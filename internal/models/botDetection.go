package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type BotDetectionStatus string

const (
	BotDetectionStatusSuspicious    BotDetectionStatus = "SUSPICIOUS"
	BotDetectionStatusConfirmed     BotDetectionStatus = "CONFIRMED"
	BotDetectionStatusFalsePositive BotDetectionStatus = "FALSE_POSITIVE"
)

type BotDetectionType string

const (
	BotDetectionTypeRapidSubmission      BotDetectionType = "RAPID_SUBMISSION"
	BotDetectionTypeFixedPattern         BotDetectionType = "FIXED_PATTERN"
	BotDetectionTypeHeadlessBrowser      BotDetectionType = "HEADLESS_BROWSER"
	BotDetectionTypeSuspiciousIP         BotDetectionType = "SUSPICIOUS_IP"
	BotDetectionTypeStraightLinePattern  BotDetectionType = "STRAIGHT_LINE_PATTERN"
	BotDetectionTypeMinTimeDiffViolation BotDetectionType = "MIN_TIME_DIFF_VIOLATION"
	BotDetectionTypeConsecutiveFast      BotDetectionType = "CONSECUTIVE_FAST_SUBMISSIONS"
	BotDetectionTypeStatisticalAnomaly   BotDetectionType = "STATISTICAL_ANOMALY"
)

type BotDetection struct {
	ID              primitive.ObjectID     `json:"_id" bson:"_id"`
	UserID          primitive.ObjectID     `json:"userId" bson:"userId"`
	GameID          *primitive.ObjectID    `json:"gameId,omitempty" bson:"gameId,omitempty"`
	ChallengeID     *primitive.ObjectID    `json:"challengeId,omitempty" bson:"challengeId,omitempty"`
	DetectionType   BotDetectionType       `json:"detectionType" bson:"detectionType"`
	Status          BotDetectionStatus     `json:"status" bson:"status"`
	Evidence        map[string]interface{} `json:"evidence" bson:"evidence"`
	SubmissionTimes []int                  `json:"submissionTimes" bson:"submissionTimes"`
	UserAgent       string                 `json:"userAgent" bson:"userAgent"`
	IPAddress       string                 `json:"ipAddress" bson:"ipAddress"`
	CreatedAt       time.Time              `json:"createdAt" bson:"createdAt"`
	UpdatedAt       *time.Time             `json:"updatedAt,omitempty" bson:"updatedAt,omitempty"`
	ReviewedBy      *primitive.ObjectID    `json:"reviewedBy,omitempty" bson:"reviewedBy,omitempty"`
	ReviewNotes     *string                `json:"reviewNotes,omitempty" bson:"reviewNotes,omitempty"`
}

type UserShadowBanStatus string

const (
	UserShadowBanStatusNone    UserShadowBanStatus = "NONE"
	UserShadowBanStatusPartial UserShadowBanStatus = "PARTIAL"
	UserShadowBanStatusFull    UserShadowBanStatus = "FULL"
)

type UserShadowBan struct {
	ID           primitive.ObjectID   `json:"_id" bson:"_id"`
	UserID       primitive.ObjectID   `json:"userId" bson:"userId"`
	Status       UserShadowBanStatus  `json:"status" bson:"status"`
	Reason       string               `json:"reason" bson:"reason"`
	DetectionIDs []primitive.ObjectID `json:"detectionIds" bson:"detectionIds"`
	CreatedAt    time.Time            `json:"createdAt" bson:"createdAt"`
	UpdatedAt    *time.Time           `json:"updatedAt,omitempty" bson:"updatedAt,omitempty"`
	ExpiresAt    *time.Time           `json:"expiresAt,omitempty" bson:"expiresAt,omitempty"`
	CreatedBy    *primitive.ObjectID  `json:"createdBy,omitempty" bson:"createdBy,omitempty"`
	UpdatedBy    *primitive.ObjectID  `json:"updatedBy,omitempty" bson:"updatedBy,omitempty"`
}

type BotFlagConfidenceLevel string

const (
	ConfidenceLevelHigh   BotFlagConfidenceLevel = "HIGH"
	ConfidenceLevelMedium BotFlagConfidenceLevel = "MEDIUM"
	ConfidenceLevelLow    BotFlagConfidenceLevel = "LOW"
)
