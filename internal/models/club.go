package models

import (
	"fmt"
	"io"
	"strconv"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Club struct {
	ID                 primitive.ObjectID `json:"id" bson:"_id"`
	Name               string             `json:"name" bson:"name"`
	Description        *string            `json:"description,omitempty" bson:"description"`
	Visibility         Visibility         `json:"visibility" bson:"visibility"`
	LogoImage          *string            `json:"logoImage,omitempty" bson:"logoImage"`
	BannerImage        *string            `json:"bannerImage,omitempty" bson:"bannerImage"`
	Category           *ClubCategory      `json:"category,omitempty" bson:"category"`
	CreatedAt          time.Time          `json:"createdAt" bson:"createdAt"`
	CreatedBy          primitive.ObjectID `json:"createdBy" bson:"createdBy"`
	ForumID            primitive.ObjectID `json:"forumId" bson:"forumId"`
	ChatRoomID         primitive.ObjectID `json:"chatRoomId" bson:"chatRoomId"`
	UpdatedAt          time.Time          `json:"updatedAt" bson:"updatedAt"`
	MembersCount       int                `json:"membersCount" bson:"membersCount"`
	ClubEventsCount    int                `json:"clubEventsCount" bson:"clubEventsCount"`
	IsAdmin            bool               `json:"isAdmin,omitempty" bson:"-"`
	IsClubMember       bool               `json:"isClubMember,omitempty" bson:"-"`
	HasRequestedToJoin bool               `json:"hasRequestedToJoin,omitempty" bson:"-"`
}

type ClubLeaderboard struct {
	Results      []*ClubLeaderboardEntry `json:"results" bson:"results"`
	PageNumber   int                     `json:"pageNumber" bson:"pageNumber"`
	PageSize     int                     `json:"pageSize" bson:"pageSize"`
	HasMore      *bool                   `json:"hasMore,omitempty" bson:"hasMore"`
	TotalResults *int                    `json:"totalResults,omitempty" bson:"totalResults"`
}

type ClubLeaderboardEntry struct {
	User *UserPublicDetails `json:"user" bson:"user"`
	Rank int                `json:"rank" bson:"rank"`
}

type ClubMember struct {
	ID                   primitive.ObjectID   `json:"id" bson:"_id"`
	ClubID               primitive.ObjectID   `json:"clubId" bson:"clubId"`
	UserID               primitive.ObjectID   `json:"userId" bson:"userId"`
	Role                 ClubMemberRole       `json:"role" bson:"role"`
	JoinedAt             time.Time            `json:"joinedAt" bson:"joinedAt"`
	ClubMembershipStatus ClubMembershipStatus `json:"clubMembershipStatus" bson:"clubMembershipStatus"`
	MemberInfo           *CreatorInfo         `json:"memberInfo,omitempty" bson:"memberInfo"`
}

type ClubMemberConnection struct {
	Edges      []*ClubMemberEdge `json:"edges" bson:"edges"`
	PageInfo   *PageInfo         `json:"pageInfo" bson:"pageInfo"`
	TotalCount int               `json:"totalCount" bson:"totalCount"`
}

type ClubMemberEdge struct {
	Node   *ClubMember `json:"node" bson:"node"`
	Cursor string      `json:"cursor" bson:"cursor"`
}

type ClubMembersPage struct {
	Results      []*ClubMember `json:"results" bson:"results"`
	PageNumber   int           `json:"pageNumber" bson:"pageNumber"`
	PageSize     int           `json:"pageSize" bson:"pageSize"`
	HasMore      *bool         `json:"hasMore,omitempty" bson:"hasMore"`
	TotalResults *int          `json:"totalResults,omitempty" bson:"totalResults"`
}

type CreateClubInput struct {
	Name        string          `json:"name" bson:"name"`
	Description *string         `json:"description,omitempty" bson:"description"`
	Visibility  Visibility      `json:"visibility" bson:"visibility"`
	LogoImage   *graphql.Upload `json:"logoImage,omitempty" bson:"logoImage"`
	BannerImage *graphql.Upload `json:"bannerImage,omitempty" bson:"bannerImage"`
	Category    *ClubCategory   `json:"category,omitempty" bson:"category"`
}

type CreatorInfo struct {
	Username        string  `json:"username" bson:"username"`
	ProfileImageURL *string `json:"profileImageUrl,omitempty" bson:"profileImageUrl"`
	Rating          *int    `json:"rating,omitempty" bson:"rating"`
}

type UpdateClubInput struct {
	ClubID      primitive.ObjectID `json:"clubId" bson:"clubId"`
	Name        *string            `json:"name,omitempty" bson:"name"`
	Description *string            `json:"description,omitempty" bson:"description"`
	Visibility  *Visibility        `json:"visibility,omitempty" bson:"visibility"`
}

type ClubsPage struct {
	Results      []*Club `json:"results" bson:"results"`
	PageNumber   int     `json:"pageNumber" bson:"pageNumber"`
	PageSize     int     `json:"pageSize" bson:"pageSize"`
	HasMore      *bool   `json:"hasMore,omitempty" bson:"hasMore"`
	TotalResults *int    `json:"totalResults,omitempty" bson:"totalResults"`
}

type ClubCategory string

const (
	ClubCategoryMentalMath ClubCategory = "MENTAL_MATH"
)

var AllClubCategory = []ClubCategory{
	ClubCategoryMentalMath,
}

func (e ClubCategory) IsValid() bool {
	switch e {
	case ClubCategoryMentalMath:
		return true
	}
	return false
}

func (e ClubCategory) String() string {
	return string(e)
}

func (e *ClubCategory) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ClubCategory(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ClubCategory", str)
	}
	return nil
}

func (e ClubCategory) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type ClubMemberRole string

const (
	ClubMemberRoleOwner  ClubMemberRole = "OWNER"
	ClubMemberRoleAdmin  ClubMemberRole = "ADMIN"
	ClubMemberRoleMember ClubMemberRole = "MEMBER"
)

var AllClubMemberRole = []ClubMemberRole{
	ClubMemberRoleOwner,
	ClubMemberRoleAdmin,
	ClubMemberRoleMember,
}

func (e ClubMemberRole) IsValid() bool {
	switch e {
	case ClubMemberRoleOwner, ClubMemberRoleAdmin, ClubMemberRoleMember:
		return true
	}
	return false
}

func (e ClubMemberRole) String() string {
	return string(e)
}

func (e *ClubMemberRole) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ClubMemberRole(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ClubMemberRole", str)
	}
	return nil
}

func (e ClubMemberRole) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type ClubMembershipStatus string

const (
	ClubMembershipStatusPending  ClubMembershipStatus = "PENDING"
	ClubMembershipStatusAccepted ClubMembershipStatus = "ACCEPTED"
	ClubMembershipStatusRejected ClubMembershipStatus = "REJECTED"
)

var AllClubMembershipStatus = []ClubMembershipStatus{
	ClubMembershipStatusPending,
	ClubMembershipStatusAccepted,
	ClubMembershipStatusRejected,
}

func (e ClubMembershipStatus) IsValid() bool {
	switch e {
	case ClubMembershipStatusPending, ClubMembershipStatusAccepted, ClubMembershipStatusRejected:
		return true
	}
	return false
}

func (e ClubMembershipStatus) String() string {
	return string(e)
}

func (e *ClubMembershipStatus) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ClubMembershipStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ClubMembershipStatus", str)
	}
	return nil
}

func (e ClubMembershipStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type Visibility string

const (
	VisibilityPublic  Visibility = "PUBLIC"
	VisibilityPrivate Visibility = "PRIVATE"
)

var AllVisibility = []Visibility{
	VisibilityPublic,
	VisibilityPrivate,
}

func (e Visibility) IsValid() bool {
	switch e {
	case VisibilityPublic, VisibilityPrivate:
		return true
	}
	return false
}

func (e Visibility) String() string {
	return string(e)
}

func (e *Visibility) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = Visibility(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid Visibility", str)
	}
	return nil
}

func (e Visibility) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}
