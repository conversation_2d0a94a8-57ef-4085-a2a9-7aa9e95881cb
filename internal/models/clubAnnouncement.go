package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ClubAnnouncement struct {
	ID          primitive.ObjectID           `json:"id" bson:"_id"`
	ClubID      primitive.ObjectID           `json:"clubId" bson:"clubId"`
	Title       string                       `json:"title" bson:"title"`
	Content     string                       `json:"content" bson:"content"`
	CreatedAt   time.Time                    `json:"createdAt" bson:"createdAt"`
	CreatedBy   primitive.ObjectID           `json:"createdBy" bson:"createdBy"`
	CreatorInfo *ClubAnnouncementCreatorInfo `json:"creatorInfo,omitempty" bson:"creatorInfo"`
}

type ClubAnnouncementsPage struct {
	Results      []*ClubAnnouncement `json:"results" bson:"results"`
	PageNumber   int                 `json:"pageNumber" bson:"pageNumber"`
	PageSize     int                 `json:"pageSize" bson:"pageSize"`
	HasMore      *bool               `json:"hasMore,omitempty" bson:"hasMore"`
	TotalResults *int                `json:"totalResults,omitempty" bson:"totalResults"`
}

type ClubAnnouncementCreatorInfo struct {
	Username        string  `json:"username" bson:"username"`
	ProfileImageURL *string `json:"profileImageUrl,omitempty" bson:"profileImageUrl"`
}

type CreateClubAnnouncementInput struct {
	ClubID  primitive.ObjectID `json:"clubId" bson:"clubId"`
	Title   string             `json:"title" bson:"title"`
	Content string             `json:"content" bson:"content"`
}
