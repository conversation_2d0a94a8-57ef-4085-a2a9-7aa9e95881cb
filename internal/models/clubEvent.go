package models

import (
	"fmt"
	"io"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ClubEvent struct {
	ID                 primitive.ObjectID `json:"id" bson:"_id"`
	ClubEventPlayID    primitive.ObjectID `json:"clubEventPlayId" bson:"clubEventPlayId"`
	ParticipationCount int                `json:"participationCount" bson:"participationCount"`
	ClubID             primitive.ObjectID `json:"clubId" bson:"clubId"`
	Title              string             `json:"title" bson:"title"`
	Visibility         *Visibility        `json:"visibility,omitempty" bson:"visibility"`
	Description        *string            `json:"description,omitempty" bson:"description"`
	ClubEventType      ClubEventType      `json:"clubEventType" bson:"clubEventType"`
	StartTime          time.Time          `json:"startTime" bson:"startTime"`
	GameConfig         *GameConfig        `json:"gameConfig" bson:"gameConfig"`
	RatedEvent         *bool              `json:"ratedEvent,omitempty" bson:"ratedEvent"`
	OpenToAll          *bool              `json:"openToAll,omitempty" bson:"openToAll"`
	PlayerSetting      *PlayerSetting     `json:"playerSetting,omitempty" bson:"playerSetting"`
	CreatedBy          primitive.ObjectID `json:"createdBy" bson:"createdBy"`
	CreatedAt          time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt          time.Time          `json:"updatedAt" bson:"updatedAt"`
}

type ClubEventsPage struct {
	Results      []*ClubEvent `json:"results" bson:"results"`
	PageNumber   int          `json:"pageNumber" bson:"pageNumber"`
	PageSize     int          `json:"pageSize" bson:"pageSize"`
	HasMore      *bool        `json:"hasMore,omitempty" bson:"hasMore"`
	TotalResults *int         `json:"totalResults,omitempty" bson:"totalResults"`
}

type ClubEventParticipant struct {
	ID          primitive.ObjectID `json:"id" bson:"_id"`
	ClubEventID primitive.ObjectID `json:"clubEventId" bson:"clubEventId"`
	UserID      primitive.ObjectID `json:"userId" bson:"userId"`
	JoinedAt    time.Time          `json:"joinedAt" bson:"joinedAt"`
}

type ClubEventParticipantConnection struct {
	Edges      []*ClubEventParticipantEdge `json:"edges" bson:"edges"`
	PageInfo   *PageInfo                   `json:"pageInfo" bson:"pageInfo"`
	TotalCount int                         `json:"totalCount" bson:"totalCount"`
}

type ClubEventParticipantEdge struct {
	Node   *ClubEventParticipant `json:"node" bson:"node"`
	Cursor string                `json:"cursor" bson:"cursor"`
}

type CreateClubEventInput struct {
	ClubID        primitive.ObjectID  `json:"clubId" bson:"clubId"`
	Title         string              `json:"title" bson:"title"`
	Description   *string             `json:"description,omitempty" bson:"description"`
	ClubEventType ClubEventType       `json:"clubEventType" bson:"clubEventType"`
	StartTime     time.Time           `json:"startTime" bson:"startTime"`
	GameConfig    *GameConfigInput    `json:"gameConfig,omitempty" bson:"gameConfig"`
	OpenToAll     *bool               `json:"openToAll,omitempty" bson:"openToAll"`
	PlayerSetting *PlayerSettingInput `json:"playerSetting,omitempty" bson:"playerSetting"`
	RatedEvent    *bool               `json:"ratedEvent,omitempty" bson:"ratedEvent"`
	Visibility    *Visibility         `json:"visibility,omitempty" bson:"visibility"`
}

type PlayerSetting struct {
	MinRating int `json:"minRating" bson:"minRating"`
	MaxRating int `json:"maxRating" bson:"maxRating"`
}

type PlayerSettingInput struct {
	MinRating int `json:"minRating" bson:"minRating"`
	MaxRating int `json:"maxRating" bson:"maxRating"`
}

type UpdateClubEventInput struct {
	EventID     primitive.ObjectID `json:"eventId" bson:"eventId"`
	Title       *string            `json:"title,omitempty" bson:"title"`
	Description *string            `json:"description,omitempty" bson:"description"`
	StartTime   *time.Time         `json:"startTime,omitempty" bson:"startTime"`
	EndTime     *time.Time         `json:"endTime,omitempty" bson:"endTime"`
}

type ClubEventType string

const (
	ClubEventTypeContest80In8   ClubEventType = "CONTEST_80_IN_8"
	ClubEventTypeSumdayShowdown ClubEventType = "SUMDAY_SHOWDOWN"
)

var AllClubEventType = []ClubEventType{
	ClubEventTypeContest80In8,
	ClubEventTypeSumdayShowdown,
}

func (e ClubEventType) IsValid() bool {
	switch e {
	case ClubEventTypeContest80In8, ClubEventTypeSumdayShowdown:
		return true
	}
	return false
}

func (e ClubEventType) String() string {
	return string(e)
}

func (e *ClubEventType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ClubEventType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ClubEventType", str)
	}
	return nil
}

func (e ClubEventType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}
