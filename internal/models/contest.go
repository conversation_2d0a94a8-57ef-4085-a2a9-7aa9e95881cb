package models

import (
	"fmt"
	"io"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Contest struct {
	ID                       primitive.ObjectID        `json:"_id" bson:"_id"`
	ClubID                   *primitive.ObjectID       `json:"clubId" bson:"clubId"`
	Name                     string                    `json:"name" bson:"name"`
	Description              *string                   `json:"description,omitempty" bson:"description"`
	HostedBy                 *string                   `json:"hostedBy,omitempty" bson:"hostedBy"`
	HostedByV2               *HostDetails              `json:"hostedByV2,omitempty" bson:"hostedByV2"`
	Details                  *ContestDetails           `json:"details,omitempty" bson:"details"`
	StartTime                time.Time                 `json:"startTime" bson:"startTime"`
	EndTime                  time.Time                 `json:"endTime" bson:"endTime"`
	RegistrationCount        *int                      `json:"registrationCount,omitempty" bson:"registrationCount"`
	ContestDuration          int                       `json:"contestDuration" bson:"contestDuration"`
	RegistrationStartTime    *time.Time                `json:"registrationStartTime,omitempty" bson:"registrationStartTime"`
	RegistrationEndTime      *time.Time                `json:"registrationEndTime,omitempty" bson:"registrationEndTime"`
	Questions                []*ContestQuestion        `json:"-" bson:"questions"`
	EncryptedQuestions       []*string                 `json:"encryptedQuestions,omitempty" bson:"-"`
	RegistrationForm         *RegistrationForm         `json:"registrationForm,omitempty" bson:"registrationForm"`
	Status                   ContestStatus             `json:"status" bson:"status"`
	CurrentUserParticipation *CurrentUserParticipation `json:"currentUserParticipation,omitempty" bson:"-"`
	RecentParticipants       []*UserPublicDetails      `json:"recentParticipants,omitempty" bson:"-"`
	CreatedAt                time.Time                 `json:"createdAt" bson:"createdAt"`
	UpdatedAt                time.Time                 `json:"updatedAt" bson:"updatedAt"`
}

type HostDetails struct {
	Name *string `json:"name,omitempty" bson:"name"`
	Logo *string `json:"logo,omitempty" bson:"logo"`
}

type ContestDetails struct {
	About        *string `json:"about,omitempty" bson:"about"`
	Requirements *string `json:"requirements,omitempty" bson:"requirements"`
	Instructions *string `json:"instructions,omitempty" bson:"instructions"`
}

type ContestQuestion struct {
	ID       string    `json:"_id" bson:"_id"`
	Question *Question `json:"question" bson:"question"`
	Points   int       `json:"points" bson:"points"`
}

type ContestParticipant struct {
	ID                   ObjectID                 `json:"_id" bson:"_id"`
	UserID               ObjectID                 `json:"-" bson:"userId"`
	User                 *UserPublicDetails       `json:"user" bson:"-"`
	ContestID            ObjectID                 `json:"-" bson:"contestId"`
	RegistrationData     []*RegistrationFieldData `json:"registrationData,omitempty" bson:"registrationData"`
	Score                int                      `json:"score" bson:"score"`
	StartTime            *time.Time               `json:"startTime,omitempty" bson:"startTime"`
	CorrectSubmission    *int                     `json:"correctSubmission,omitempty" bson:"correctSubmission"`
	IncorrectSubmission  *int                     `json:"incorrectSubmission,omitempty" bson:"incorrectSubmission"`
	IsVirtualParticipant *bool                    `json:"isVirtualParticipant,omitempty" bson:"isVirtualParticipant"`
	Rank                 *int                     `json:"rank,omitempty" bson:"rank"`
	LastSubmissionTime   *time.Time               `json:"lastSubmissionTime,omitempty" bson:"lastSubmissionTime"`
	CreatedAt            time.Time                `json:"createdAt" bson:"createdAt"`
	UpdatedAt            time.Time                `json:"updatedAt" bson:"updatedAt"`
	Submissions          []*ContestSubmission     `json:"submissions,omitempty" bson:"submissions"`
}

type CurrentUserParticipation struct {
	ContestID          *ObjectID                `json:"contestId,omitempty" bson:"contestId"`
	UserID             *ObjectID                `json:"userId,omitempty" bson:"userId"`
	RegistrationData   []*RegistrationFieldData `json:"registrationData,omitempty" bson:"registrationData"`
	Score              *int                     `json:"score,omitempty" bson:"score"`
	LastSubmissionTime *time.Time               `json:"lastSubmissionTime,omitempty" bson:"lastSubmissionTime"`
	StartTime          *time.Time               `json:"startTime,omitempty" bson:"startTime"`
}

type ContestStatus string

const (
	ContestStatusUpcoming         ContestStatus = "UPCOMING"
	ContestStatusRegistrationOpen ContestStatus = "REGISTRATION_OPEN"
	ContestStatusOngoing          ContestStatus = "ONGOING"
	ContestStatusEnded            ContestStatus = "ENDED"
)

var AllContestStatus = []ContestStatus{
	ContestStatusUpcoming,
	ContestStatusRegistrationOpen,
	ContestStatusOngoing,
	ContestStatusEnded,
}

func (e ContestStatus) IsValid() bool {
	switch e {
	case ContestStatusUpcoming, ContestStatusRegistrationOpen, ContestStatusOngoing, ContestStatusEnded:
		return true
	}
	return false
}

func (e ContestStatus) String() string {
	return string(e)
}

func (e *ContestStatus) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ContestStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ContestStatus", str)
	}
	return nil
}

func (e ContestStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type ContestDetailsInput struct {
	About        string `json:"about,omitempty" bson:"about"`
	Requirements string `json:"requirements,omitempty" bson:"requirements"`
	Instructions string `json:"instructions,omitempty" bson:"instructions"`
}

type CreateContestInput struct {
	Name                  string                `json:"name" bson:"name"`
	Description           string                `json:"description,omitempty" bson:"description"`
	HostName              string                `json:"hostName,omitempty" bson:"hostName"`
	StartTime             time.Time             `json:"startTime" bson:"startTime"`
	EndTime               time.Time             `json:"endTime" bson:"endTime"`
	ContestDuration       int                   `json:"contestDuration" bson:"contestDuration"`
	RegistrationStartTime time.Time             `json:"registrationStartTime,omitempty" bson:"registrationStartTime"`
	Details               ContestDetailsInput   `json:"details,omitempty" bson:"details"`
	RegistrationEndTime   time.Time             `json:"registrationEndTime,omitempty" bson:"registrationEndTime"`
	RegistrationForm      RegistrationFormInput `json:"registrationForm" bson:"registrationForm"`
	HostLogo              string                `json:"hostLogo,omitempty" bson:"hostLogo"`
}

type RegistrationFieldData struct {
	Name   string   `json:"name" bson:"name"`
	Values []string `json:"values,omitempty" bson:"values"`
}

type ContestQuestionInput struct {
	QuestionID ObjectID `json:"questionId" bson:"questionId"`
	Points     int      `json:"points" bson:"points"`
}

type RegistrationFormFieldValueInput struct {
	Name   string   `json:"name" bson:"name"`
	Values []string `json:"values,omitempty" bson:"values"`
}

type RegistrationFormValuesInput struct {
	ContestID ObjectID                           `json:"contestId" bson:"contestId"`
	FormData  []*RegistrationFormFieldValueInput `json:"formData" bson:"formData"`
}

type ContestLeaderboard struct {
	ContestID         ObjectID              `json:"contestId" bson:"contestId"`
	Participants      []*ContestParticipant `json:"participants" bson:"participants"`
	TotalParticipants int                   `json:"totalParticipants" bson:"totalParticipants"`
}

type ContestSubmission struct {
	QuestionID     string    `json:"questionId" bson:"questionId"`
	Answer         string    `json:"answer" bson:"answer"`
	IsCorrect      bool      `json:"isCorrect" bson:"isCorrect"`
	SubmissionTime time.Time `json:"submissionTime" bson:"submissionTime"`
	Points         float64   `json:"points" bson:"points"`
}

type UserContestSubmissions struct {
	TotalScore          float64              `json:"totalScore" bson:"totalScore"`
	StartTime           *time.Time           `json:"startTime,omitempty" bson:"startTime"`
	LastSubmissionTime  *time.Time           `json:"lastSubmissionTime,omitempty" bson:"lastSubmissionTime"`
	CorrectSubmission   *int                 `json:"correctSubmission,omitempty" bson:"correctSubmission"`
	IncorrectSubmission *int                 `json:"incorrectSubmission,omitempty" bson:"incorrectSubmission"`
	Submissions         []*ContestSubmission `json:"submissions" bson:"submissions"`
	User                *UserPublicDetails   `json:"user,omitempty" bson:"user"`
}

type UserContestResult struct {
	TotalScore           float64            `json:"totalScore" bson:"totalScore"`
	StartTime            *time.Time         `json:"startTime,omitempty" bson:"startTime"`
	LastSubmissionTime   *time.Time         `json:"lastSubmissionTime,omitempty" bson:"lastSubmissionTime"`
	CorrectSubmission    *int               `json:"correctSubmission,omitempty" bson:"correctSubmission"`
	IncorrectSubmission  *int               `json:"incorrectSubmission,omitempty" bson:"incorrectSubmission"`
	User                 *UserPublicDetails `json:"user,omitempty" bson:"user"`
	Rank                 *int               `json:"rank,omitempty" bson:"rank"`
	QuestionsSolved      *int               `json:"questionsSolved,omitempty" bson:"questionsSolved"`
	TotalParticipants    *int               `json:"totalParticipants,omitempty" bson:"totalParticipants"`
	IsVirtualParticipant *bool              `json:"isVirtualParticipant,omitempty" bson:"isVirtualParticipant"`
}

type PaginatedContests struct {
	Contests   []*Contest `json:"contests" bson:"contests"`
	TotalCount int        `json:"totalCount" bson:"totalCount"`
}
