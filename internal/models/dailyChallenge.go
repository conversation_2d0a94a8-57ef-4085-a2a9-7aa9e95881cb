package models

import (
	"fmt"
	"io"
	"strconv"
	"time"
)

type DailyChallenge struct {
	ID                 ObjectID            `json:"_id" bson:"_id"`
	ChallengeStatus    *string             `json:"challengeStatus,omitempty" bson:"challengeStatus,omitempty"`
	Questions          []*GameQuestion     `json:"questions" bson:"questions"`
	EncryptedQuestions []*string           `json:"encryptedQuestions,omitempty" bson:"-"`
	StartTime          *time.Time          `json:"startTime,omitempty" bson:"startTime,omitempty"`
	EndTime            *time.Time          `json:"endTime,omitempty" bson:"endTime,omitempty"`
	HasAttempted       *bool               `json:"hasAttempted,omitempty" bson:"hasAttempted,omitempty"`
	ChallengeNumber    int                 `json:"challengeNumber,omitempty" bson:"challengeNumber,omitempty"`
	CreatedAt          *time.Time          `json:"createdAt,omitempty" bson:"createdAt,omitempty"`
	UpdatedAt          *time.Time          `json:"updatedAt,omitempty" bson:"updatedAt,omitempty"`
	Division           ChallengeDivision   `json:"division,omitempty" bson:"division,omitempty"`
	Stats              *DailyChallengeStat `json:"stats,omitempty" bson:"stats,omitempty"`
}

type DailyChallengeStat struct {
	TotalAttempts   *int     `json:"totalAttempts,omitempty" bson:"totalAttempts"`
	TotalSubmission *int     `json:"totalSubmission,omitempty" bson:"totalSubmission"`
	AverageTime     *int     `json:"averageTime,omitempty" bson:"averageTime"`
	BestTime        *int     `json:"bestTime,omitempty" bson:"bestTime"`
	AverageAccuracy *float64 `json:"averageAccuracy,omitempty" bson:"averageAccuracy"`
}

type Result struct {
	UserID         ObjectID   `json:"userId" bson:"userId"`
	ChallengeID    ObjectID   `json:"challengeId" bson:"challengeId"`
	SubmittedTimes []*int     `json:"submittedTimes,omitempty" bson:"submittedTimes,omitempty"`
	Score          *int       `json:"score,omitempty" bson:"score,omitempty"`
	CompletedAt    *time.Time `json:"completedAt,omitempty" bson:"completedAt,omitempty"`
}

type ChallengeResult struct {
	User  *UserPublicDetails `json:"user" bson:"user"`
	Rank  int                `json:"rank" bson:"rank"`
	Score float64            `json:"score" bson:"score"`
}

// TODO: rename to DailyChallengeLeaderboardPage
type LeaderboardPage struct {
	Results      []ChallengeResult `json:"results" bson:"results"`
	PageNumber   int               `json:"pageNumber" bson:"pageNumber"`
	PageSize     int               `json:"pageSize" bson:"pageSize"`
	HasMore      bool              `json:"hasMore,omitempty" bson:"hasMore,omitempty"`
	TotalResults int               `json:"totalResults,omitempty" bson:"totalResults,omitempty"`
}

type ResultStatus string

const (
	ResultStatusAttempted ResultStatus = "ATTEMPTED"
	ResultStatusCompleted ResultStatus = "COMPLETED"
)

type DailyChallengeResult struct {
	ID                *ObjectID         `json:"_id,omitempty" bson:"_id,omitempty"`
	UserID            ObjectID          `json:"userId,omitempty" bson:"userId,omitempty"`
	ChallengeID       *ObjectID         `json:"challengeId,omitempty" bson:"challengeId,omitempty"`
	Score             *int              `json:"score,omitempty" bson:"score,omitempty"`
	StatikCoinsEarned *int              `json:"statikCoinsEarned,omitempty" bson:"statikCoinsEarned,omitempty"`
	SubmittedTimes    []*int            `json:"submittedTimes,omitempty" bson:"submittedTimes,omitempty"`
	ChallengeNumber   *int              `json:"challengeNumber,omitempty" bson:"challengeNumber,omitempty"`
	CompletedAt       *time.Time        `json:"completedAt,omitempty" bson:"completedAt,omitempty"`
	Rank              int               `json:"rank,omitempty" bson:"rank,omitempty"`
	ResultStatus      ResultStatus      `json:"resultStatus,omitempty" bson:"resultStatus,omitempty"`
	IP                *string           `json:"ip,omitempty" bson:"ip,omitempty"`
	Division          ChallengeDivision `json:"division,omitempty" bson:"division,omitempty"`
}

type UserResult struct {
	Success bool                  `json:"success,omitempty" bson:"success,omitempty"`
	Error   string                `json:"error,omitempty" bson:"error,omitempty"`
	Result  *DailyChallengeResult `json:"result,omitempty" bson:"result,omitempty"`
}

type DailyChallengeResultWithStats struct {
	Result *DailyChallengeResult    `json:"result,omitempty" bson:"result"`
	Stats  *UserDailyChallengeStats `json:"stats,omitempty" bson:"stats"`
}

type UserDailyChallengeResultWithStats struct {
	Success *bool                          `json:"success,omitempty" bson:"success"`
	Error   *string                        `json:"error,omitempty" bson:"error"`
	Result  *DailyChallengeResultWithStats `json:"result,omitempty" bson:"result"`
}

type SubmitSolutionInput struct {
	ChallengeID     ObjectID `json:"challengeId" bson:"challengeId"`
	SubmittedTimes  []*int   `json:"submittedTimes,omitempty" bson:"submittedTimes,omitempty"`
	ChallengeNumber *int     `json:"challengeNumber,omitempty" bson:"challengeNumber,omitempty"`
}

type AttemptChallengeResult struct{}

type SubmitChallengeResult struct {
	Success bool    `json:"success,omitempty" bson:"success,omitempty"`
	Message string  `json:"message,omitempty" bson:"message,omitempty"`
	Result  *Result `json:"result,omitempty" bson:"result,omitempty"`
}

type DailyChallengeResultResponse struct {
	Results      []RankedResult `json:"results"`
	PageNumber   int            `json:"pageNumber"`
	PageSize     int            `json:"pageSize"`
	HasMore      bool           `json:"hasMore"`
	TotalResults int            `json:"totalResults"`
}

type RankedResult struct {
	User  User `json:"user"`
	Score int  `json:"score"`
	Rank  int  `json:"rank"`
}

type ChallengeResultResponse struct {
	Success bool                  `json:"success"`
	Message string                `json:"message"`
	Result  *DailyChallengeResult `json:"result,omitempty"`
}

type BotDetectionResult struct {
	IsBotBehavior bool     `json:"isBotBehavior" bson:"isBotBehavior"`
	UserID        ObjectID `json:"userId" bson:"userId"`
	ChallengeID   ObjectID `json:"challengeId" bson:"challengeId"`
}

type UserDailyChallengeStats struct {
	ID              ObjectID                   `json:"_id,omitempty" bson:"_id"`
	UserID          ObjectID                   `json:"userId" bson:"userId"`
	Division        ChallengeDivision          `json:"division" bson:"division"`
	TotalAttempts   int                        `json:"totalAttempts" bson:"totalAttempts"`
	TotalSubmission int                        `json:"totalSubmission" bson:"totalSubmission"`
	AverageTime     int                        `json:"averageTime" bson:"averageTime"`
	BestTime        int                        `json:"bestTime" bson:"bestTime"`
	Streaks         *UserDailyChallengeStreaks `json:"streaks" bson:"streaks"`
	AverageAccuracy *float64                   `json:"averageAccuracy,omitempty" bson:"averageAccuracy"`
}

type UserDailyChallengeStreaks struct {
	Current        int        `json:"current" bson:"current"`
	Highest        int        `json:"highest" bson:"highest"`
	LastPlayedDate *time.Time `json:"lastPlayedDate,omitempty" bson:"lastPlayedDate"`
}

type ChallengeDivision string

const (
	ChallengeDivisionOpen ChallengeDivision = "OPEN"
	ChallengeDivisionDiv1 ChallengeDivision = "DIV1"
	ChallengeDivisionDiv2 ChallengeDivision = "DIV2"
	ChallengeDivisionDiv3 ChallengeDivision = "DIV3"
)

var AllChallengeDivision = []ChallengeDivision{
	ChallengeDivisionOpen,
	ChallengeDivisionDiv1,
	ChallengeDivisionDiv2,
	ChallengeDivisionDiv3,
}

func (e ChallengeDivision) IsValid() bool {
	switch e {
	case ChallengeDivisionOpen, ChallengeDivisionDiv1, ChallengeDivisionDiv2, ChallengeDivisionDiv3:
		return true
	}
	return false
}

func (e ChallengeDivision) String() string {
	return string(e)
}

func (e *ChallengeDivision) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ChallengeDivision(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid CHALLENGE_DIVISION", str)
	}
	return nil
}

func (e ChallengeDivision) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}
