package models

import (
	"encoding/json"
	"fmt"
	"io"
	"time"
)

type Date time.Time

const dateLayout = time.RFC3339Nano

func (d *Date) UnmarshalGQL(v interface{}) error {
	switch value := v.(type) {
	case string:
		// Handle string input
		t, err := time.Parse(dateLayout, value)
		if err != nil {
			return fmt.Errorf("invalid date format: %v", err)
		}
		*d = Date(t)
		return nil
	case int:
		// Handle integer timestamp (assumed to be milliseconds)
		seconds := int64(value) / 1000
		nanos := (int64(value) % 1000) * int64(time.Millisecond)
		*d = Date(time.Unix(seconds, nanos))
		return nil
	case int64:
		// Handle int64 timestamp (assumed to be milliseconds)
		seconds := value / 1000
		nanos := (value % 1000) * int64(time.Millisecond)
		*d = Date(time.Unix(seconds, nanos))
		return nil
	case float64:
		// Handle float64 timestamp (assumed to be milliseconds)
		seconds := int64(value) / 1000
		nanos := (int64(value) % 1000) * int64(time.Millisecond)
		*d = Date(time.Unix(seconds, nanos))
		return nil
	case json.Number:
		// Try parsing as int64
		numInt, err := value.Int64()
		if err == nil {
			seconds := numInt / 1000
			nanos := (numInt % 1000) * int64(time.Millisecond)
			*d = Date(time.Unix(seconds, nanos))
			return nil
		}
		// Try parsing as float64
		numFloat, err := value.Float64()
		if err != nil {
			return fmt.Errorf("invalid numeric value: %v", err)
		}
		seconds := int64(numFloat) / 1000
		nanos := (int64(numFloat) % 1000) * int64(time.Millisecond)
		*d = Date(time.Unix(seconds, nanos))
		return nil
	default:
		return fmt.Errorf("dates must be strings or numeric timestamps, got %T", v)
	}
}

func (d Date) MarshalGQL(w io.Writer) {
	t := time.Time(d)
	// Serialize the date as a string in the specified format
	fmt.Fprintf(w, `"%s"`, t.Format(dateLayout))
}

func (d Date) Time() time.Time {
	return time.Time(d)
}

func DateFromTime(t time.Time) Date {
	return Date(t)
}
