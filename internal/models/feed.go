package models

import (
	"fmt"
	"io"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type FeedNotificationParams struct {
	FeedType FeedType `json:"feedType" bson:"feedType"`
	// FeedAdditionalInfo *FeedAdditionalInfo `json:"feedAdditionalInfo,omitempty" bson:"feedAdditionalInfo,omitempty"`
	FeedForFriends *FeedForFriends `json:"feedForFriends,omitempty" bson:"feedForFriends,omitempty"`
}

type ConnectionRequest struct {
	SentBy primitive.ObjectID `json:"sentBy" bson:"sentBy"`
}

type FeedAdditionalInfo struct {
	ConnectionRequest *ConnectionRequest `json:"connectionRequest,omitempty" bson:"connectionRequest"`
}

type FeedData struct {
	ID                  primitive.ObjectID  `json:"_id" bson:"_id"`
	SentAt              time.Time           `json:"sentAt" bson:"sentAt"`
	Title               string              `json:"title" bson:"title"`
	Description         string              `json:"description" bson:"description"`
	ExpirationTime      time.Time           `json:"expirationTime" bson:"expirationTime"`
	SentFor             primitive.ObjectID  `json:"sentFor" bson:"sentFor"`
	LastLikedByUserName *string             `json:"lastLikedByUserName,omitempty" bson:"lastLikedByUserName"`
	LikesCount          int                 `json:"likesCount" bson:"likesCount"`
	AdditionalInfo      *FeedAdditionalInfo `json:"additionalInfo,omitempty" bson:"additionalInfo"`
	FeedForFriends      *FeedForFriends     `json:"feedForFriends,omitempty" bson:"feedForFriends"`
	CreatedAt           time.Time           `json:"createdAt" bson:"createdAt"`
	UpdatedAt           time.Time           `json:"updatedAt" bson:"updatedAt"`
}

type FeedForFriends struct {
	Title string `json:"title" bson:"title"`
	Body  string `json:"body" bson:"body"`
}

type FeedResponse struct {
	Feeds       []*UserFeed          `json:"feeds" bson:"feeds"`
	LastID      *primitive.ObjectID  `json:"lastId,omitempty" bson:"lastId"`
	HasMore     bool                 `json:"hasMore" bson:"hasMore"`
	UserDetails []*UserPublicDetails `json:"userDetails" bson:"userDetails"`
	IsRead      bool                 `json:"isRead" bson:"isRead"`
}

type UserFeed struct {
	ID              primitive.ObjectID `json:"_id" bson:"_id"`
	UserID          primitive.ObjectID `json:"userId" bson:"userId"`
	FeedType        FeedType           `json:"feedType" bson:"feedType"`
	FeedReferenceID primitive.ObjectID `json:"feedReferenceId" bson:"feedReferenceId"`
	IsLiked         bool               `json:"isLiked" bson:"isLiked"`
	ExpirationTime  time.Time          `json:"expirationTime" bson:"expirationTime"`
	FeedData        *FeedData          `json:"feedData" bson:"feedData,omitempty"`
	CreatedAt       time.Time          `json:"createdAt" bson:"createdAt"`
	ImageURL        *string            `json:"imageUrl,omitempty" bson:"imageUrl"`
}

type FeedType string

const (
	FeedTypeConnection     FeedType = "CONNECTION"
	FeedTypeMatiks         FeedType = "MATIKS"
	FeedTypeCelebration    FeedType = "CELEBRATION"
	FeedTypeDailyChallenge FeedType = "DAILY_CHALLENGE"
	FeedTypeLeague         FeedType = "LEAGUE"
)

var AllFeedType = []FeedType{
	FeedTypeConnection,
	FeedTypeMatiks,
	FeedTypeCelebration,
	FeedTypeDailyChallenge,
	FeedTypeLeague,
}

func (e FeedType) IsValid() bool {
	switch e {
	case FeedTypeConnection, FeedTypeMatiks, FeedTypeCelebration, FeedTypeDailyChallenge, FeedTypeLeague:
		return true
	}
	return false
}

func (e FeedType) String() string {
	return string(e)
}

func (e *FeedType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = FeedType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid FEED_TYPE", str)
	}
	return nil
}

func (e FeedType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}
