package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CreateForumInput struct {
	ClubID      primitive.ObjectID `json:"clubId" bson:"clubId"`
	Title       string             `json:"title" bson:"title"`
	Description *string            `json:"description,omitempty" bson:"description"`
}

type CreateForumReplyInput struct {
	ThreadID primitive.ObjectID `json:"threadId" bson:"threadId"`
	Content  string             `json:"content" bson:"content"`
}

type CreateForumThreadInput struct {
	ForumID primitive.ObjectID `json:"forumId" bson:"forumId"`
	Title   string             `json:"title" bson:"title"`
	Content string             `json:"content" bson:"content"`
}

type Forum struct {
	ID          primitive.ObjectID `json:"id" bson:"_id"`
	ClubID      primitive.ObjectID `json:"clubId" bson:"clubId"`
	Title       string             `json:"title" bson:"title"`
	Description *string            `json:"description,omitempty" bson:"description"`
	CreatedAt   time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt   time.Time          `json:"updatedAt" bson:"updatedAt"`
	CreatedBy   primitive.ObjectID `json:"createdBy" bson:"createdBy"`
	CreatorInfo *CreatorInfo       `json:"creatorInfo,omitempty" bson:"creatorInfo"`
}

type ForumPage struct {
	Results      []*Forum `json:"results" bson:"results"`
	PageNumber   int      `json:"pageNumber" bson:"pageNumber"`
	PageSize     int      `json:"pageSize" bson:"pageSize"`
	HasMore      *bool    `json:"hasMore,omitempty" bson:"hasMore"`
	TotalResults *int     `json:"totalResults,omitempty" bson:"totalResults"`
}

type ForumReply struct {
	ID          primitive.ObjectID `json:"id" bson:"_id"`
	ThreadID    primitive.ObjectID `json:"threadId" bson:"threadId"`
	Content     string             `json:"content" bson:"content"`
	CreatedAt   time.Time          `json:"createdAt" bson:"createdAt"`
	CreatedBy   primitive.ObjectID `json:"createdBy" bson:"createdBy"`
	CreatorInfo *CreatorInfo       `json:"creatorInfo,omitempty" bson:"creatorInfo"`
}

type ForumThread struct {
	ID          primitive.ObjectID `json:"id" bson:"_id"`
	ForumID     primitive.ObjectID `json:"forumId" bson:"forumId"`
	Title       string             `json:"title" bson:"title"`
	Content     string             `json:"content" bson:"content"`
	CreatedAt   time.Time          `json:"createdAt" bson:"createdAt"`
	CreatedBy   primitive.ObjectID `json:"createdBy" bson:"createdBy"`
	CreatorInfo *CreatorInfo       `json:"creatorInfo,omitempty" bson:"creatorInfo"`
}

type RepliesPage struct {
	Results      []*ForumReply `json:"results" bson:"results"`
	PageNumber   int           `json:"pageNumber" bson:"pageNumber"`
	PageSize     int           `json:"pageSize" bson:"pageSize"`
	HasMore      *bool         `json:"hasMore,omitempty" bson:"hasMore"`
	TotalResults *int          `json:"totalResults,omitempty" bson:"totalResults"`
}

type ThreadsPage struct {
	Results      []*ForumThread `json:"results" bson:"results"`
	PageNumber   int            `json:"pageNumber" bson:"pageNumber"`
	PageSize     int            `json:"pageSize" bson:"pageSize"`
	HasMore      *bool          `json:"hasMore,omitempty" bson:"hasMore"`
	TotalResults *int           `json:"totalResults,omitempty" bson:"totalResults"`
}
