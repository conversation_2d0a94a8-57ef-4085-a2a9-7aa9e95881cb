package models

import (
	"fmt"
	"io"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type FollowUserInput struct {
	UserID primitive.ObjectID `json:"userId" bson:"userId"`
}

type FollowersAndFollowee struct {
	ID         primitive.ObjectID `json:"_id" bson:"_id"`
	FollowerID primitive.ObjectID `json:"followerId" bson:"followerId"`
	FolloweeID primitive.ObjectID `json:"followeeId" bson:"followeeId"`
	FollowedAt *time.Time         `json:"followedAt,omitempty" bson:"followedAt"`
}

type FollowersAndFolloweePage struct {
	Results      []*FollowersAndFolloweeOutput `json:"results" bson:"results"`
	PageNumber   int                           `json:"pageNumber" bson:"pageNumber"`
	PageSize     int                           `json:"pageSize" bson:"pageSize"`
	HasMore      *bool                         `json:"hasMore,omitempty" bson:"hasMore"`
	TotalResults *int                          `json:"totalResults,omitempty" bson:"totalResults"`
}

type FollowersAndFolloweeOutput struct {
	ID         primitive.ObjectID `json:"_id" bson:"_id"`
	FollowerID primitive.ObjectID `json:"followerId" bson:"followerId"`
	FolloweeID primitive.ObjectID `json:"followeeId" bson:"followeeId"`
	FollowedAt *time.Time         `json:"followedAt,omitempty" bson:"followedAt"`
	UserInfo   *UserPublicDetails `json:"userInfo,omitempty" bson:"userInfo"`
}

type FriendsOutput struct {
	ID           primitive.ObjectID `json:"_id" bson:"_id"`
	SenderID     primitive.ObjectID `json:"senderId" bson:"senderId"`
	ReceiverID   primitive.ObjectID `json:"receiverId" bson:"receiverId"`
	AcceptedAt   *time.Time         `json:"acceptedAt,omitempty" bson:"acceptedAt"`
	FriendInfo   *UserPublicDetails `json:"friendInfo,omitempty" bson:"friendInfo"`
	IsOnline     bool               `json:"isOnline,omitempty" bson:"-"`
	CurrActivity *UserActivityType  `json:"currActivity,omitempty" bson:"-"`
}

type FriendRequest struct {
	ID          primitive.ObjectID   `json:"_id" bson:"_id"`
	SenderID    primitive.ObjectID   `json:"senderId" bson:"senderId"`
	ReceiverID  primitive.ObjectID   `json:"receiverId" bson:"receiverId"`
	Status      *FriendRequestStatus `json:"status,omitempty" bson:"status"`
	SentAt      *time.Time           `json:"sentAt,omitempty" bson:"sentAt"`
	RespondedAt *time.Time           `json:"respondedAt,omitempty" bson:"respondedAt"`
}

type FriendRequestInput struct {
	UserID primitive.ObjectID `json:"userId" bson:"userId"`
}

type FriendRequestOutput struct {
	ID          primitive.ObjectID   `json:"_id" bson:"_id"`
	SenderID    primitive.ObjectID   `json:"senderId" bson:"senderId"`
	ReceiverID  primitive.ObjectID   `json:"receiverId" bson:"receiverId"`
	Status      *FriendRequestStatus `json:"status,omitempty" bson:"status"`
	SentAt      *time.Time           `json:"sentAt,omitempty" bson:"sentAt"`
	RespondedAt *time.Time           `json:"respondedAt,omitempty" bson:"respondedAt"`
	Sender      *UserPublicDetails   `json:"sender,omitempty" bson:"sender"`
}

type FriendRequestPage struct {
	Results      []*FriendRequestOutput `json:"results" bson:"results"`
	PageNumber   int                    `json:"pageNumber" bson:"pageNumber"`
	PageSize     int                    `json:"pageSize" bson:"pageSize"`
	HasMore      *bool                  `json:"hasMore,omitempty" bson:"hasMore"`
	TotalResults *int                   `json:"totalResults,omitempty" bson:"totalResults"`
}

type Friends struct {
	ID         primitive.ObjectID `json:"_id" bson:"_id"`
	SenderID   primitive.ObjectID `json:"senderId" bson:"senderId"`
	ReceiverID primitive.ObjectID `json:"receiverId" bson:"receiverId"`
	AcceptedAt *time.Time         `json:"acceptedAt,omitempty" bson:"acceptedAt"`
}

type FriendsPage struct {
	Results      []*FriendsOutput `json:"results" bson:"results"`
	PageNumber   int              `json:"pageNumber" bson:"pageNumber"`
	PageSize     int              `json:"pageSize" bson:"pageSize"`
	HasMore      *bool            `json:"hasMore,omitempty" bson:"hasMore"`
	TotalResults *int             `json:"totalResults,omitempty" bson:"totalResults"`
}

type RemoveFriendInput struct {
	UserID primitive.ObjectID `json:"userId" bson:"userId"`
}

type SendFriendRequestInput struct {
	UserID primitive.ObjectID `json:"userId" bson:"userId"`
}

type UnFollowUserInput struct {
	UserID primitive.ObjectID `json:"userId" bson:"userId"`
}

type WithdrawFriendRequestInput struct {
	UserID primitive.ObjectID `json:"userId" bson:"userId"`
}

type RemoveFollowerInput struct {
	UserID primitive.ObjectID `json:"userId" bson:"userId"`
}

type SortOptions struct {
	SortBy        string `json:"sortBy" bson:"sortBy"`
	SortDirection int    `json:"sortDirection" bson:"sortDirection"`
}

type FriendRequestStatus string

const (
	FriendRequestStatusAccepted FriendRequestStatus = "ACCEPTED"
	FriendRequestStatusRejected FriendRequestStatus = "REJECTED"
	FriendRequestStatusPending  FriendRequestStatus = "PENDING"
)

var AllFriendRequestStatus = []FriendRequestStatus{
	FriendRequestStatusAccepted,
	FriendRequestStatusRejected,
	FriendRequestStatusPending,
}

func (e FriendRequestStatus) IsValid() bool {
	switch e {
	case FriendRequestStatusAccepted, FriendRequestStatusRejected, FriendRequestStatusPending:
		return true
	}
	return false
}

func (e FriendRequestStatus) String() string {
	return string(e)
}

func (e *FriendRequestStatus) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = FriendRequestStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid FRIEND_REQUEST_STATUS", str)
	}
	return nil
}

func (e FriendRequestStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type FriendshipStatus string

const (
	FriendshipStatusRequestSent    FriendshipStatus = "REQUEST_SENT"
	FriendshipStatusAccepted       FriendshipStatus = "ACCEPTED"
	FriendshipStatusPendingRequest FriendshipStatus = "PENDING_REQUEST"
	FriendshipStatusNotFriend      FriendshipStatus = "NOT_FRIEND"
)

var AllFriendshipStatus = []FriendshipStatus{
	FriendshipStatusRequestSent,
	FriendshipStatusAccepted,
	FriendshipStatusPendingRequest,
	FriendshipStatusNotFriend,
}

func (e FriendshipStatus) IsValid() bool {
	switch e {
	case FriendshipStatusRequestSent, FriendshipStatusAccepted, FriendshipStatusPendingRequest, FriendshipStatusNotFriend:
		return true
	}
	return false
}

func (e FriendshipStatus) String() string {
	return string(e)
}

func (e *FriendshipStatus) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = FriendshipStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid FRIENDSHIP_STATUS", str)
	}
	return nil
}

func (e FriendshipStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}
