package models

import (
	"fmt"
	"io"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Game struct {
	ID                 *ObjectID           `json:"_id" bson:"_id"`
	Players            []*Player           `json:"players" bson:"players"`
	GameStatus         GameStatus          `json:"gameStatus" bson:"gameStatus"`
	RematchRequestedBy *ObjectID           `json:"rematchRequestedBy,omitempty" bson:"rematchRequestedBy"`
	GameType           GameType            `json:"gameType" bson:"gameType"`
	CreatedBy          ObjectID            `json:"createdBy" bson:"createdBy"`
	Config             *GameConfig         `json:"config,omitempty" bson:"config"`
	Questions          []*GameQuestion     `json:"questions,omitempty" bson:"questions"`
	EncryptedQuestions []*string           `json:"encryptedQuestions,omitempty" bson:"-"`
	LeaderBoard        []*LeaderBoardEntry `json:"leaderBoard,omitempty" bson:"leaderBoard"`
	StartTime          *time.Time          `json:"startTime,omitempty" bson:"startTime"`
	EndTime            *time.Time          `json:"endTime,omitempty" bson:"endTime"`
	SeriesID           *ObjectID           `json:"seriesId,omitempty" bson:"seriesId"`
	MinifiedQuestions  []*string           `json:"minifiedQuestions" bson:"minifiedQuestions"`
	CreatedAt          *time.Time          `json:"createdAt,omitempty" bson:"createdAt"`
	UpdatedAt          *time.Time          `json:"updatedAt,omitempty" bson:"updatedAt"`
	ShowdownId         *ObjectID           `json:"showdownId,omitempty" bson:"showdownId,omitempty"`
	ShowdownGameConfig *ShowdownGameConfig `json:"showdownGameConfig" bson:"showdownGameConfig"`
}

type MinifiedGame struct {
	ID          ObjectID            `json:"_id" bson:"_id"`
	Players     []*Player           `json:"players" bson:"players"`
	Config      *GameConfig         `json:"config,omitempty" bson:"config,omitempty"`
	LeaderBoard []*LeaderBoardEntry `json:"leaderBoard,omitempty" bson:"leaderBoard,omitempty"`
	StartTime   *time.Time          `json:"startTime,omitempty" bson:"startTime,omitempty"`
	EndTime     *time.Time          `json:"endTime,omitempty" bson:"endTime,omitempty"`
}

type ShowdownGameConfig struct {
	IsRoundEnded        bool                  `json:"isRoundEnded" bson:"isRoundEnded"`
	NextGameID          *ObjectID             `json:"nextGameId" bson:"nextGameId"`
	NextGameStartsAt    *time.Time            `json:"nextGameStartsAt" bson:"nextGameStartsAt"`
	TotalGamesPlayed    int                   `json:"totalGamesPlayed" bson:"totalGamesPlayed"`
	NumOfGames          int                   `json:"numOfGames" bson:"numOfGames"`
	ShowdownGamePlayer  []*ShowdownGamePlayer `json:"showdownGamePlayer" bson:"showdownGamePlayer"`
	HasOpponentNotShown bool                  `json:"hasOpponentNotShown" bson:"hasOpponentNotShown"`
	Round               int                   `json:"round" bson:"round"`
}

type ShowdownGamePlayer struct {
	IsTie    bool     `json:"isTie" bson:"isTie"`
	IsWinner bool     `json:"isWinner" bson:"isWinner"`
	UserID   ObjectID `json:"userId" bson:"userId"`
	Wins     float64  `json:"wins" bson:"wins"`
	Score    float64  `json:"score" bson:"score"`
}

type Player struct {
	UserID      ObjectID     `json:"userId" bson:"userId"`
	Rating      *int         `json:"rating,omitempty" bson:"rating,omitempty"`
	StatikCoins int          `json:"statikCoins,omitempty" bson:"statikCoins,omitempty"`
	Status      PlayerStatus `json:"status,omitempty" bson:"status,omitempty"`
	TimeLeft    *int         `json:"timeLeft" bson:"timeLeft"`
}

type GameQuestion struct {
	Question    *Question         `json:"question,omitempty" bson:"question,omitempty"`
	Submissions []*Submission     `json:"submissions,omitempty" bson:"submissions,omitempty"`
	Stats       GameQuestionStats `json:"stats,omitempty" bson:"stats,omitempty"`
}

type GameQuestionStats struct {
	FastestTime *int        `json:"fastestTime,omitempty" bson:"fastestTime,omitempty"`
	UserIds     []*ObjectID `json:"userIds,omitempty" bson:"userIds,omitempty"`
}

type Submission struct {
	UserID            *ObjectID `json:"userId,omitempty" bson:"userId,omitempty"`
	TimeTaken         *int      `json:"timeTaken,omitempty" bson:"timeTaken,omitempty"`
	Points            *int      `json:"points,omitempty" bson:"points,omitempty"`
	SubmissionTime    *Date     `json:"submissionTime,omitempty" bson:"submissionTime,omitempty"`
	IsCorrect         *bool     `json:"isCorrect,omitempty" bson:"isCorrect,omitempty"`
	InCorrectAttempts *int      `json:"inCorrectAttempts,omitempty" bson:"inCorrectAttempts,omitempty"`
	SubmittedValues   []*string `json:"submittedValues,omitempty" bson:"submittedValues,omitempty"`
}

type LeaderBoardEntry struct {
	UserID            *ObjectID `json:"userId,omitempty" bson:"userId,omitempty"`
	Correct           *int      `json:"correct,omitempty" bson:"correct,omitempty"`
	Incorrect         *int      `json:"incorrect,omitempty" bson:"incorrect,omitempty"`
	TotalPoints       *float64  `json:"totalPoints,omitempty" bson:"totalPoints,omitempty"`
	RatingChange      *int      `json:"ratingChange,omitempty" bson:"ratingChange,omitempty"`
	StatikCoinsEarned *int      `json:"statikCoinsEarned,omitempty" bson:"statikCoinsEarned,omitempty"`
	Rank              *int      `json:"rank,omitempty" bson:"rank,omitempty"`
}

type GameConfig struct {
	// timeLimit in seconds
	TimeLimit          *int     `json:"timeLimit,omitempty" bson:"timeLimit"`
	NumPlayers         *int     `json:"numPlayers,omitempty" bson:"numPlayers"`
	QuestionTags       []string `json:"questionTags,omitempty" bson:"questionTags"`
	GameType           GameType `json:"gameType,omitempty" bson:"gameType"`
	DifficultyLevel    []int    `json:"difficultyLevel,omitempty" bson:"difficultyLevel"`
	MaxTimePerQuestion *int     `json:"maxTimePerQuestion,omitempty" bson:"maxTimePerQuestion"`
}

type SearchEntry struct {
	UserID     ObjectID    `json:"userId" bson:"userId"`
	GameConfig *GameConfig `json:"gameConfig,omitempty" bson:"gameConfig,omitempty"`
	CreatedAt  time.Time   `json:"createdAt" bson:"createdAt"`
}

type GameConfigInput struct {
	TimeLimit          int      `json:"timeLimit,omitempty" bson:"timeLimit"`
	NumPlayers         int      `json:"numPlayers,omitempty" bson:"numPlayers"`
	GameType           GameType `json:"gameType,omitempty" bson:"gameType"`
	QuestionTags       []string `json:"questionTags,omitempty" bson:"questionTags"`
	DifficultyLevel    []int    `json:"difficultyLevel,omitempty" bson:"difficultyLevel"`
	MaxTimePerQuestion *int     `json:"maxTimePerQuestion,omitempty" bson:"maxTimePerQuestion"`
}

type Players []*ObjectID

type AnswerResponseModel struct {
	ID                          *ObjectID                      `json:"_id" bson:"_id"`
	Config                      *GameConfig                    `json:"config,omitempty" bson:"config,omitempty"`
	GameStatus                  GameStatus                     `json:"gameStatus" bson:"gameStatus"`
	LeaderBoard                 []*LeaderBoardEntry            `json:"leaderBoard,omitempty" bson:"leaderBoard,omitempty"`
	EndTime                     *time.Time                     `json:"endTime,omitempty" bson:"endTime,omitempty"`
	Players                     []*Player                      `json:"players" bson:"players"`
	UserSubmissionsWithQuestion []*UserSubmissionsWithQuestion `json:"userSubmissionsWithQuestion" bson:"userSubmissionsWithQuestion"`
}

type UserSubmissionsWithQuestion struct {
	QuestionId  *string            `json:"questionId,omitempty" bson:"questionId,omitempty"`
	Submissions []*UserSubmissions `json:"submissions" bson:"submissions"`
}

type UserSubmissions struct {
	UserID *ObjectID `json:"userId,omitempty" bson:"userId,omitempty"`
	Points *int      `json:"points,omitempty" bson:"points,omitempty"`
}

type SubmitAnswerEvent struct {
	Game  *AnswerResponseModel `json:"game,omitempty"`
	Event *string              `json:"event,omitempty"`
}

type JoinGameInput struct {
	GameID ObjectID `json:"gameId,omitempty" bson:"gameId,omitempty"`
}

type SubmitAnswerInput struct {
	GameID            ObjectID `json:"gameId,omitempty" bson:"gameId,omitempty"`
	QuestionID        string   `json:"questionId,omitempty" bson:"questionId,omitempty"`
	SubmittedValue    string   `json:"submittedValue,omitempty" bson:"submittedValue,omitempty"`
	IsCorrect         bool     `json:"isCorrect,omitempty" bson:"isCorrect,omitempty"`
	IncorrectAttempts int      `json:"incorrectAttempts,omitempty" bson:"incorrectAttempts,omitempty"`
	TimeOfSubmission  Date     `json:"timeOfSubmission,omitempty" bson:"timeOfSubmission,omitempty"`
}

type StartGameInput struct {
	GameID *ObjectID `json:"gameId,omitempty" bson:"gameId,omitempty"`
}

type GetGamesOutput struct {
	Games []*MinifiedGame      `json:"games,omitempty" bson:"games,omitempty"`
	Users []*UserPublicDetails `json:"users,omitempty" bson:"users,omitempty"`
}

type TimeRangeInput struct {
	StartTime time.Time `json:"startTime,omitempty" bson:"startTime"`
	EndTime   time.Time `json:"endTime,omitempty" bson:"endTime"`
}

type PageInfoInput struct {
	PageNumber int `json:"pageNumber,omitempty" bson:"pageNumber,omitempty"`
	Rows       int `json:"rows,omitempty" bson:"rows,omitempty"`
}

type GetGamesInput struct {
	UserID    ObjectID       `json:"userId,omitempty" bson:"userId,omitempty"`
	TimeRange TimeRangeInput `json:"timeRange,omitempty" bson:"timeRange,omitempty"`
	PageInfo  PageInfoInput  `json:"pageInfo,omitempty" bson:"pageInfo,omitempty"`
}

type SubscriptionOutput struct {
	Game     *Game     `json:"game,omitempty" bson:"game,omitempty"`
	Event    *string   `json:"event,omitempty" bson:"event,omitempty"`
	Question *Question `json:"question,omitempty" bson:"question,omitempty"`
}

type SearchSubscriptionOutput struct {
	Game     *Game   `json:"game,omitempty" bson:"game,omitempty"`
	Event    *string `json:"event,omitempty" bson:"event,omitempty"`
	Opponent *User   `json:"opponent,omitempty" bson:"opponent,omitempty"`
}

type SubmitFlashAnzanAnswerInput struct {
	GameID             *primitive.ObjectID `json:"gameId,omitempty" bson:"gameId"`
	QuestionIdentifier *string             `json:"questionIdentifier,omitempty" bson:"questionIdentifier"`
	SubmittedValue     *string             `json:"submittedValue,omitempty" bson:"submittedValue"`
	IsCorrect          *bool               `json:"isCorrect,omitempty" bson:"isCorrect"`
	TimeOfSubmission   *Date               `json:"timeOfSubmission,omitempty" bson:"timeOfSubmission"`
	MaxScore           *int                `json:"maxScore,omitempty" bson:"maxScore"`
}

type GameDetailedAnalysis struct {
	Game      *Game                   `json:"game" bson:"game"`
	Questions []*GameQuestionAnalysis `json:"questions" bson:"questions"`
}

type GameQuestionAnalysis struct {
	Question       *Question      `json:"question" bson:"question"`
	AvgTimes       []*UserAvgTime `json:"avgTimes" bson:"avgTimes"`
	GlobalAvgTime  float64        `json:"globalAvgTime" bson:"globalAvgTime"`
	GlobalBestTime float64        `json:"globalBestTime" bson:"globalBestTime"`
}

type UserAvgTime struct {
	UserID          string  `json:"userId" bson:"userId"`
	QuestionAvgTime float64 `json:"questionAvgTime" bson:"questionAvgTime"`
	PresetAvgTime   float64 `json:"presetAvgTime" bson:"presetAvgTime"`
	PresetBestTime  float64 `json:"presetBestTime" bson:"presetBestTime"`
}

type ChallengeUserInput struct {
	UserID     *primitive.ObjectID `json:"userId" bson:"userId"`
	GameConfig *GameConfigInput    `json:"gameConfig,omitempty" bson:"gameConfig"`
}

type ChallengeOutput struct {
	GameID       primitive.ObjectID `json:"gameId" bson:"gameId"`
	ChallengedBy primitive.ObjectID `json:"challengedBy" bson:"challengedBy"`
	GameConfig   *GameConfig        `json:"gameConfig" bson:"gameConfig"`
	CreatedAt    time.Time          `json:"createdAt" bson:"createdAt"`
	Status       *ChallengeStatus   `json:"status,omitempty" bson:"status"`
	Opponent     *User              `json:"opponent,omitempty" bson:"opponent,omitempty"`
}

type ChallengeStatus string

const (
	ChallengeStatusChallengeSent     ChallengeStatus = "CHALLENGE_SENT"
	ChallengeStatusChallengeAccepted ChallengeStatus = "CHALLENGE_ACCEPTED"
	ChallengeStatusChallengeExpired  ChallengeStatus = "CHALLENGE_EXPIRED"
	ChallengeStatusChallengeRejected ChallengeStatus = "CHALLENGE_REJECTED"
	ChallengeStatuCancelled          ChallengeStatus = "GAME_CANCELLED"
)

var AllChallengeStatus = []ChallengeStatus{
	ChallengeStatusChallengeSent,
	ChallengeStatusChallengeAccepted,
	ChallengeStatusChallengeExpired,
	ChallengeStatusChallengeRejected,
	ChallengeStatuCancelled,
}

func (e ChallengeStatus) IsValid() bool {
	switch e {
	case ChallengeStatusChallengeSent, ChallengeStatuCancelled, ChallengeStatusChallengeAccepted, ChallengeStatusChallengeExpired, ChallengeStatusChallengeRejected:
		return true
	}
	return false
}

func (e ChallengeStatus) String() string {
	return string(e)
}

func (e *ChallengeStatus) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ChallengeStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ChallengeStatus", str)
	}
	return nil
}

func (e ChallengeStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type GameStatus string

const (
	GameStatusCreated   GameStatus = "CREATED"
	GameStatusReady     GameStatus = "READY"
	GameStatusStarted   GameStatus = "STARTED"
	GameStatusPaused    GameStatus = "PAUSED"
	GameStatusEnded     GameStatus = "ENDED"
	GameStatusCancelled GameStatus = "CANCELLED"
)

var AllGameStatus = []GameStatus{
	GameStatusCreated,
	GameStatusReady,
	GameStatusStarted,
	GameStatusPaused,
	GameStatusEnded,
	GameStatusCancelled,
}

func (e GameStatus) IsValid() bool {
	switch e {
	case GameStatusCreated, GameStatusReady, GameStatusStarted, GameStatusPaused, GameStatusEnded, GameStatusCancelled:
		return true
	}
	return false
}

func (e GameStatus) String() string {
	return string(e)
}

func (e *GameStatus) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = GameStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid GAME_STATUS", str)
	}
	return nil
}

func (e GameStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type GameType string

const (
	GameTypePlayOnline      GameType = "PLAY_ONLINE"
	GameTypePlayWithFriend  GameType = "PLAY_WITH_FRIEND"
	GameTypeOnlineChallenge GameType = "ONLINE_CHALLENGE"
	GameTypePractice        GameType = "PRACTICE"
	GameTypeFlashAnzan      GameType = "FLASH_ANZAN"
	GameTypeSumdayShowdown  GameType = "SUMDAY_SHOWDOWN"
	GameTypeFastestFinger   GameType = "FASTEST_FINGER"
	GameTypeGroupPlay       GameType = "GROUP_PLAY"
	GameTypeAbilityDuels    GameType = "ABILITY_DUELS"
)

var AllGameType = []GameType{
	GameTypePlayOnline,
	GameTypePlayWithFriend,
	GameTypeOnlineChallenge,
	GameTypePractice,
	GameTypeFlashAnzan,
	GameTypeFastestFinger,
	GameTypeSumdayShowdown,
	GameTypeAbilityDuels,
}

func (e GameType) IsValid() bool {
	switch e {
	case GameTypePlayOnline, GameTypePlayWithFriend, GameTypeOnlineChallenge, GameTypePractice, GameTypeAbilityDuels, GameTypeFlashAnzan, GameTypeFastestFinger, GameTypeSumdayShowdown, GameTypeGroupPlay:
		return true
	}
	return false
}

func (e GameType) String() string {
	return string(e)
}

func (e *GameType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = GameType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid GAME_TYPE", str)
	}
	return nil
}

func (e GameType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type PlayerStatus string

const (
	PlayerStatusInvited  PlayerStatus = "INVITED"
	PlayerStatusAccepted PlayerStatus = "ACCEPTED"
	PlayerStatusRejected PlayerStatus = "REJECTED"
)

var AllPlayerStatus = []PlayerStatus{
	PlayerStatusInvited,
	PlayerStatusAccepted,
	PlayerStatusRejected,
}

func (e PlayerStatus) IsValid() bool {
	switch e {
	case PlayerStatusInvited, PlayerStatusAccepted, PlayerStatusRejected:
		return true
	}
	return false
}

func (e PlayerStatus) String() string {
	return string(e)
}

func (e *PlayerStatus) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = PlayerStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid PLAYER_STATUS", str)
	}
	return nil
}

func (e PlayerStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type GroupPlayChatMessage struct {
	Message   string             `json:"message" bson:"message"`
	GameID    string             `json:"gameId" bson:"gameId"`
	UserName  string             `json:"userName" bson:"userName"`
	UserID    string             `json:"userId" bson:"userId"`
	SentAt    time.Time          `json:"sentAt" bson:"sentAt"`
	MessageID primitive.ObjectID `json:"messageId" bson:"messageId"`
}

type QuestionTypeWeightage struct {
	Type      int
	Weightage int
}

type GameConfigInterface interface {
	GetTimeLimit() *int
	GetNumPlayers() *int
	GetGameType() interface{}
	GetDifficultyLevel() []int
}

func (g GameConfig) GetTimeLimit() *int {
	return g.TimeLimit
}

func (g GameConfig) GetNumPlayers() *int {
	return g.NumPlayers
}

func (g GameConfig) GetGameType() interface{} {
	return g.GameType
}

func (g GameConfig) GetDifficultyLevel() []int {
	return g.DifficultyLevel
}

func (p PuzzleGameConfig) GetTimeLimit() *int {
	return p.TimeLimit
}

func (p PuzzleGameConfig) GetNumPlayers() *int {
	return p.NumPlayers
}

func (p PuzzleGameConfig) GetGameType() interface{} {
	return p.GameType
}

func (p PuzzleGameConfig) GetDifficultyLevel() []int {
	return p.DifficultyLevel
}

type GetGamesByRatingInput struct {
	UserID     *primitive.ObjectID `json:"userId,omitempty" bson:"userId"`
	PageInfo   *PageInfoInput      `json:"pageInfo,omitempty" bson:"pageInfo"`
	RatingType *string             `json:"ratingType,omitempty" bson:"ratingType"`
}

type GetGamesByRatingOutput struct {
	Games       []*MinifiedGame       `json:"games"`
	PuzzleGames []*MinifiedPuzzleGame `json:"puzzleGames"`
	Users       []*UserPublicDetails  `json:"users"`
	TotalCount  int                   `json:"totalCount"`
}
