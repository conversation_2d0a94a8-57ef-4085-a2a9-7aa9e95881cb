package models

import (
	"fmt"
	"io"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CreateLeagueInput struct {
	Name                  string                 `json:"name" bson:"name"`
	HostedBy              *string                `json:"hostedBy" bson:"hostedBy"`
	RegistrationStart     time.Time              `json:"registrationStart" bson:"registrationStart"`
	RegistrationEnd       time.Time              `json:"registrationEnd" bson:"registrationEnd"`
	LeagueStart           time.Time              `json:"leagueStart" bson:"leagueStart"`
	LeagueEnd             time.Time              `json:"leagueEnd" bson:"leagueEnd"`
	RegistrationFormInput *RegistrationFormInput `json:"registrationForm" bson:"registrationForm"`
	Details               *LeagueDetailsInput    `json:"details" bson:"details"`
	HostLogo              string                 `json:"hostLogo,omitempty" bson:"hostLogo"`
}

type League struct {
	ID                       primitive.ObjectID      `json:"id" bson:"_id"`
	Name                     string                  `json:"name" bson:"name"`
	HostedBy                 *string                 `json:"hostedBy" bson:"hostedBy"`
	HostedByV2               *HostDetails            `json:"hostedByV2,omitempty" bson:"hostedByV2"`
	RegistrationStart        time.Time               `json:"registrationStart" bson:"registrationStart"`
	RegistrationEnd          time.Time               `json:"registrationEnd" bson:"registrationEnd"`
	LeagueStart              time.Time               `json:"leagueStart" bson:"leagueStart"`
	LeagueEnd                time.Time               `json:"leagueEnd" bson:"leagueEnd"`
	Details                  *LeagueDetails          `json:"details" bson:"details"`
	RegistrationCount        *int64                  `json:"-" bson:"registrationCount"`
	ChatRoomId               primitive.ObjectID      `json:"chatRoomId" bson:"chatRoomId"`
	CurrentUserParticipation *LeagueParticipant      `json:"-" bson:"currentUserParticipation"`
	CurrentUserResult        *LeagueLeaderboardEntry `json:"-" bson:"currentUserResult"`
	RegistrationForm         *RegistrationForm       `json:"registrationForm" bson:"registrationForm"`
}

type LeagueDetails struct {
	About        *string `json:"about" bson:"about"`
	Instructions *string `json:"instructions" bson:"instructions"`
	Requirements *string `json:"requirements" bson:"requirements"`
	Awards       *string `json:"awards,omitempty" bson:"awards"`
}

type LeagueDetailsInput struct {
	About        *string `json:"about" bson:"about"`
	Instructions *string `json:"instructions" bson:"instructions"`
	Requirements *string `json:"requirements" bson:"requirements"`
	Awards       *string `json:"awards,omitempty" bson:"awards"`
}

type LeagueParticipant struct {
	ID               primitive.ObjectID       `json:"_id" bson:"_id"`
	UserID           primitive.ObjectID       `json:"userId" bson:"userId"`
	LeagueID         primitive.ObjectID       `json:"leagueId" bson:"leagueId"`
	RegistrationData []*RegistrationFieldData `json:"registrationData,omitempty" bson:"registrationData"`
	JoinedAt         time.Time                `json:"joinedAt" bson:"joinedAt"`
}

type LeagueLeaderboardPage struct {
	Participants []*LeagueLeaderboardEntry `json:"participants" bson:"participants"`
	TotalCount   *int                      `json:"totalCount,omitempty" bson:"totalCount"`
}

type PaginatedLeagues struct {
	League     []*League `json:"league" bson:"league"`
	TotalCount *int      `json:"totalCount,omitempty" bson:"totalCount"`
}

type JoinLeagueInput struct {
	LeagueID primitive.ObjectID                 `json:"leagueId" bson:"leagueId"`
	FormData []*RegistrationFormFieldValueInput `json:"formData" bson:"formData"`
}

type LeagueStatus string

const (
	LeagueStatusLive             LeagueStatus = "LIVE"
	LeagueStatusEnded            LeagueStatus = "ENDED"
	LeagueStatusRegistrationOpen LeagueStatus = "REGISTRATION_OPEN"
	LeagueStatusUpcoming         LeagueStatus = "UPCOMING"
)

var AllLeagueStatus = []LeagueStatus{
	LeagueStatusLive,
	LeagueStatusEnded,
	LeagueStatusRegistrationOpen,
	LeagueStatusUpcoming,
}

func (e LeagueStatus) IsValid() bool {
	switch e {
	case LeagueStatusLive, LeagueStatusEnded, LeagueStatusRegistrationOpen, LeagueStatusUpcoming:
		return true
	}
	return false
}

func (e LeagueStatus) String() string {
	return string(e)
}

func (e *LeagueStatus) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = LeagueStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid LeagueStatus", str)
	}
	return nil
}

func (e LeagueStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type ActivitySummaryEntry struct {
	Activity string `json:"activity" bson:"activity"`
	Coins    int    `json:"coins" bson:"coins"`
}

type LeagueLeaderboardEntry struct {
	User            *User                   `json:"user" bson:"user"`
	StatikCoins     int                     `json:"statikCoins" bson:"statikCoins"`
	Rank            int                     `json:"rank" bson:"rank"`
	ActivitySummary []*ActivitySummaryEntry `json:"activitySummary" bson:"activitySummary"`
}
