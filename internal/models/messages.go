package models

import (
	"fmt"
	"io"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Attachment struct {
	Type AttachmentType `json:"type" bson:"type"`
	URL  string         `json:"url" bson:"url"`
}

type SortDirection string

const (
	SortDirectionAsc  SortDirection = "ASC"
	SortDirectionDesc SortDirection = "DESC"
)

var AllSortDirection = []SortDirection{
	SortDirectionAsc,
	SortDirectionDesc,
}

func (e SortDirection) IsValid() bool {
	switch e {
	case SortDirectionAsc, SortDirectionDesc:
		return true
	}
	return false
}

func (e SortDirection) GetValue() (int, error) {
	if !e.IsValid() {
		return 0, fmt.Errorf("%s is not a valid SortDirection", e)
	}
	switch e {
	case SortDirectionAsc:
		return 1, nil
	case SortDirectionDesc:
		return -1, nil
	}
	return 0, nil
}

func (e SortDirection) String() string {
	return string(e)
}

func (e *SortDirection) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = SortDirection(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid SortDirection", str)
	}
	return nil
}

func (e SortDirection) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type CreateMessageInput struct {
	GroupID    primitive.ObjectID `json:"groupId" bson:"groupId"`
	Content    string             `json:"content" bson:"content"`
	Attachment []*Attachment      `json:"attachment,omitempty" bson:"attachment"`
	Sender     primitive.ObjectID `json:"sender" bson:"sender"`
}

type PaginatedMessage struct {
	Messages      []*Message          `json:"messages,omitempty" bson:"messages"`
	LastMessageID *primitive.ObjectID `json:"lastMessageId" bson:"lastMessageId"`
	HasMore       bool                `json:"hasMore" bson:"hasMore"`
}

type GetAllMessageGroupsInput struct {
	Page     int  `json:"page" bson:"page"`
	PageSize *int `json:"pageSize,omitempty" bson:"pageSize"`
}

type Message struct {
	ID         primitive.ObjectID     `json:"_id" bson:"_id"`
	GroupID    primitive.ObjectID     `json:"groupId" bson:"groupId"`
	Content    string                 `json:"content" bson:"content"`
	Attachment []*Attachment          `json:"attachment,omitempty" bson:"attachment"`
	Sender     primitive.ObjectID     `json:"sender" bson:"sender"`
	CreatedAt  time.Time              `json:"createdAt" bson:"createdAt"`
	SenderInfo *UserDetailsForMessage `json:"senderInfo" bson:"senderInfo"`
}

type MessageGroup struct {
	ID                   primitive.ObjectID     `json:"_id" bson:"_id"`
	GroupName            *string                `json:"groupName,omitempty" bson:"groupName"`
	Members              []*primitive.ObjectID  `json:"members" bson:"members"`
	Alias                string                 `json:"alias" bson:"alias"`
	Messages             []*Message             `json:"messages,omitempty" bson:"messages"`
	GroupType            GroupType              `json:"groupType" bson:"groupType"`
	CreatedAt            time.Time              `json:"createdAt" bson:"createdAt"`
	UpdatedAt            time.Time              `json:"updatedAt" bson:"updatedAt"`
	LastMessage          *Message               `json:"lastMessage,omitempty" bson:"lastMessage"`
	UserInfoIfIndividual *UserDetailsForMessage `json:"userInfoIfIndividual,omitempty" bson:"userInfoIfIndividual"`
	DeepLinkRoute        *string                `json:"deepLinkRoute,omitempty" bson:"deepLinkRoute"`
	LastMessageRead      []*MessageRead         `json:"lastMessageRead,omitempty" bson:"lastMessageRead"`
}

type MessageRead struct {
	UserID          primitive.ObjectID `json:"userId" bson:"userId"`
	LastMessageRead primitive.ObjectID `json:"lastMessageRead" bson:"lastMessageRead"`
}

type PaginatedMessageGroups struct {
	Groups   []*MessageGroup `json:"groups,omitempty" bson:"groups"`
	NextPage *int            `json:"nextPage,omitempty" bson:"nextPage"`
	HasMore  bool            `json:"hasMore" bson:"hasMore"`
	IsRead   bool            `json:"isRead" bson:"isRead"`
}

type UserDetailsForMessage struct {
	ID              primitive.ObjectID `json:"_id" bson:"_id"`
	Name            *string            `json:"name,omitempty" bson:"name"`
	Username        string             `json:"username" bson:"username"`
	ProfileImageURL *string            `json:"profileImageUrl,omitempty" bson:"profileImageUrl"`
	Rating          *int               `json:"rating,omitempty" bson:"rating"`
}

type AttachmentType string

const (
	AttachmentTypeImage AttachmentType = "IMAGE"
	AttachmentTypeFile  AttachmentType = "FILE"
)

var AllAttachmentType = []AttachmentType{
	AttachmentTypeImage,
	AttachmentTypeFile,
}

func (e AttachmentType) IsValid() bool {
	switch e {
	case AttachmentTypeImage, AttachmentTypeFile:
		return true
	}
	return false
}

func (e AttachmentType) String() string {
	return string(e)
}

func (e *AttachmentType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = AttachmentType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid AttachmentType", str)
	}
	return nil
}

func (e AttachmentType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type GroupType string

const (
	GroupTypeIndividual GroupType = "INDIVIDUAL"
	GroupTypeCommunity  GroupType = "COMMUNITY"
	GroupTypeGame       GroupType = "GAME"
	GroupTypeLeague     GroupType = "LEAGUE"
	GroupTypeShowdown   GroupType = "SHOWDOWN"
)

var AllGroupType = []GroupType{
	GroupTypeIndividual,
	GroupTypeCommunity,
	GroupTypeGame,
	GroupTypeShowdown,
	GroupTypeLeague,
}

func (e GroupType) IsValid() bool {
	switch e {
	case GroupTypeIndividual, GroupTypeCommunity, GroupTypeGame, GroupTypeShowdown, GroupTypeLeague:
		return true
	}
	return false
}

func (e GroupType) String() string {
	return string(e)
}

func (e *GroupType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = GroupType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid GroupType", str)
	}
	return nil
}

func (e GroupType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}
