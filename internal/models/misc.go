package models

import (
	"fmt"
	"io"
	"strconv"
)

type Mutation struct{}

type Query struct{}

type Subscription struct{}

type MapStringInt map[string]int

type PlatformStats struct {
	TotalUsers         int `json:"totalUsers" bson:"totalUsers"`
	TotalGames         int `json:"totalGames" bson:"totalGames"`
	TotalSignedInUsers int `json:"totalSignedInUsers" bson:"totalSignedInUsers"`
}

type File struct {
	Name        string `json:"name" bson:"name"`
	Content     string `json:"content" bson:"content"`
	ContentType string `json:"contentType" bson:"contentType"`
	URL         string `json:"url" bson:"url"`
}

type SortOrder string

const (
	SortOrderAsc  SortOrder = "ASC"
	SortOrderDesc SortOrder = "DESC"
)

var AllSortOrder = []SortOrder{
	SortOrderAsc,
	SortOrderDesc,
}

func (e SortOrder) IsValid() bool {
	switch e {
	case SortOrderAsc, SortOrderDesc:
		return true
	}
	return false
}

func (e SortOrder) String() string {
	return string(e)
}

func (e *SortOrder) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = SortOrder(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid SortOrder", str)
	}
	return nil
}

func (e SortOrder) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}
