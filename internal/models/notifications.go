package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type UserNotification struct {
	UserID         primitive.ObjectID      `json:"userId" bson:"userId"`
	Type           NotificationType        `json:"type" bson:"type"`
	Title          string                  `json:"title" bson:"title"`
	Body           string                  `json:"body" bson:"body"`
	Data           map[string]string       `json:"data" bson:"data"`
	SentAt         time.Time               `json:"sentAt" bson:"sentAt"`
	Feed           *FeedNotificationParams `json:"feed" bson:"feed"`
	AdditionalInfo *AdditionalInfo         `json:"additionalInfo,omitempty" bson:"additionalInfo,omitempty"`
	ImageUrl       *string                 `json:"imageUrl,omitempty" bson:"imageUrl,omitempty"`
}

type AdditionalInfo struct {
	ChallengeGameId         *primitive.ObjectID `json:"challengeGameId" bson:"challengeGameId"`
	FeedType                *FeedType           `json:"feedType" bson:"feedType"`
	InAppType               *InAppType          `json:"inAppType" bson:"inAppType"`
	ConnectionRequestSentBy *primitive.ObjectID `json:"connectionRequestSentBy" bson:"connectionRequestSentBy"`
}

type InAppType string

const (
	InAppTypeChallenge  InAppType = "CHALLENGE"
	InAppTypeConnection InAppType = "CONNECTION"
)

type InAppNotification struct {
	Title               string               `json:"title" bson:"title"`
	Body                string               `json:"body" bson:"body"`
	InAppAdditionalInfo *InAppAdditionalInfo `json:"inAppAdditionalInfo" bson:"inAppAdditionalInfo"`
	SentAt              time.Time            `json:"sentAt" bson:"sentAt"`
	ImageUrl            *string              `json:"imageUrl,omitempty" bson:"imageUrl,omitempty"`
}

type InAppAdditionalInfo struct {
	ChallengeGameId   *primitive.ObjectID `json:"challengeGameId" bson:"challengeGameId"`
	ConnectionRequest *ConnectionRequest  `json:"connectionRequest" bson:"connectionRequest"`
}

type Feedback struct {
	Email   string `json:"email" bson:"email"`
	Phone   string `json:"phone" bson:"phone"`
	Message string `json:"message" bson:"message"`
}

type NotificationType string

const (
	NotificationTypePushNotification        NotificationType = "PUSH_NOTIFICATION"
	NotificationTypeInAppPopup              NotificationType = "IN_APP_POPUP"
	NotificationTypeEmailNotification       NotificationType = "EMAIL_NOTIFICATION"
	NotificationTypeFeed                    NotificationType = "FEED"
	NotificationTypePushNotificationAndFeed NotificationType = "PUSH_NOTIFICATION_AND_FEED"
	NotificationTypeInAppPopupAndFeed       NotificationType = "IN_APP_POPUP_AND_FEED"
)

var AllNotificationType = []NotificationType{
	NotificationTypePushNotification,
	NotificationTypeInAppPopup,
	NotificationTypeEmailNotification,
	NotificationTypeFeed,
	NotificationTypePushNotificationAndFeed,
	NotificationTypeInAppPopupAndFeed,
}

func (e NotificationType) IsValid() bool {
	switch e {
	case NotificationTypePushNotification, NotificationTypeInAppPopup, NotificationTypeEmailNotification, NotificationTypeFeed, NotificationTypePushNotificationAndFeed, NotificationTypeInAppPopupAndFeed:
		return true
	}
	return false
}

func (e NotificationType) String() string {
	return string(e)
}
