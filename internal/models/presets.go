package models

import (
	"fmt"
	"io"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type GlobalPreset struct {
	ID                      primitive.ObjectID `json:"_id" bson:"_id"`
	Identifier              string             `json:"identifier" bson:"identifier"`
	GlobalAverageTime       float64            `json:"globalAverageTime,omitempty" bson:"globalAverageTime"`
	BestTime                float64            `json:"bestTime,omitempty" bson:"bestTime"`
	TotalQuestionsSolved    int64              `json:"totalQuestionsSolved,omitempty" bson:"totalQuestionsSolved"`
	BestStreak              int                `json:"bestStreak,omitempty" bson:"bestStreak"`
	NumOfCorrectSubmissions int                `json:"numOfCorrectSubmissions,omitempty" bson:"numOfCorrectSubmissions"`
	GlobalAccuracy          float64            `json:"globalAccuracy,omitempty" bson:"globalAccuracy"`
	IncorrectSubmissions    int                `json:"incorrectSubmissions,omitempty" bson:"incorrectSubmissions"`
	Top10Mathletes          []*Mathlete        `json:"top10Mathletes,omitempty" bson:"top10Mathletes"`
	CreatedAt               *time.Time         `json:"createdAt,omitempty" bson:"createdAt,omitempty"`
	UpdatedAt               *time.Time         `json:"updatedAt,omitempty" bson:"updatedAt,omitempty"`
}

type GlobalPresets struct {
	GlobalPresets []*GlobalPreset `json:"globalPresets,omitempty" bson:"globalPresets"`
	TotalCount    int             `json:"totalCount" bson:"totalCount"`
}

// TODO: implement this
type Mathlete struct {
	UserID                  ObjectID `json:"userId" bson:"userId"`
	QuestionsSolved         *int     `json:"questionsSolved,omitempty" bson:"questionsSolved"`
	BestTime                float64  `json:"bestTime,omitempty" bson:"bestTime"`
	BestStreak              int      `json:"bestStreak,omitempty" bson:"bestStreak"`
	NumOfCorrectSubmissions int      `json:"numOfCorrectSubmissions,omitempty" bson:"numOfCorrectSubmissions"`
}

type UserPreset struct {
	ID                      ObjectID   `json:"_id" bson:"_id"`
	GlobalPresetID          ObjectID   `json:"globalPresetId" bson:"globalPresetId"`
	UserID                  ObjectID   `json:"userId" bson:"userId"`
	Identifier              string     `json:"identifier" bson:"identifier"`
	Name                    *string    `json:"name,omitempty" bson:"name"`
	QuestionsSolved         int64      `json:"questionsSolved,omitempty" bson:"questionsSolved"`
	CurAvgTime              float64    `json:"curAvgTime,omitempty" bson:"curAvgTime"`
	CurAvgAccuracy          float64    `json:"curAvgAccuracy,omitempty" bson:"curAvgAccuracy"`
	IncorrectSubmissions    int        `json:"incorrectSubmissions,omitempty" bson:"incorrectSubmissions"`
	BestTime                float64    `json:"bestTime,omitempty" bson:"bestTime"`
	Last10Time              []int      `json:"last10Time,omitempty" bson:"last10Time"`
	Last10IncorrectAttempts []int      `json:"last10IncorrectAttempts,omitempty" bson:"last10IncorrectAttempts"`
	BestStreak              int        `json:"bestStreak,omitempty" bson:"bestStreak"`
	NumOfCorrectSubmissions int        `json:"numOfCorrectSubmissions,omitempty" bson:"numOfCorrectSubmissions"`
	Saved                   bool       `json:"saved,omitempty" bson:"saved"`
	SavedConfig             string     `json:"savedConfig,omitempty" bson:"savedConfig"`
	CreatedAt               *time.Time `json:"createdAt,omitempty" bson:"createdAt,omitempty"`
	UpdatedAt               *time.Time `json:"updatedAt,omitempty" bson:"updatedAt,omitempty"`
}

type UserPresets struct {
	UserPresets []*UserPreset `json:"userPresets,omitempty" bson:"userPresets"`
	TotalCount  int           `json:"totalCount" bson:"totalCount"`
}

type UserPresetDayStats struct {
	Date            time.Time        `json:"date" bson:"date"`
	UserPresetStats *UserPresetStats `json:"userPresetStats,omitempty" bson:"userPresetStats"`
}

type UserPresetResult struct {
	Identifier              string    `json:"identifier" bson:"identifier"`
	NumOfQuestions          int64     `json:"numOfQuestions,omitempty" bson:"numOfQuestions"`
	SubmittedTimes          []int     `json:"submittedTimes,omitempty" bson:"submittedTimes"`
	IncorrectAttempts       []int     `json:"incorrectAttempts,omitempty" bson:"incorrectAttempts"`
	BestStreak              int       `json:"bestStreak,omitempty" bson:"bestStreak"`
	NumOfCorrectSubmissions int       `json:"numOfCorrectSubmissions,omitempty" bson:"numOfCorrectSubmissions"`
	Date                    time.Time `json:"date,omitempty" bson:"date"`
	SavedConfig             string    `json:"savedConfig,omitempty" bson:"savedConfig"`
}

type UserPresetResultInput struct {
	UserPresetResults []*UserPresetResult `json:"userPresetResults,omitempty" bson:"userPresetResults"`
}

type UserPresetStats struct {
	ID                         ObjectID  `json:"_id" bson:"_id"`
	Date                       time.Time `json:"date,omitempty" bson:"date"`
	UserPresetID               ObjectID  `json:"userPresetId" bson:"userPresetId"`
	GlobalPresetID             ObjectID  `json:"globalPresetId" bson:"globalPresetId"`
	Identifier                 string    `json:"identifier" bson:"identifier"`
	BestStreak                 int       `json:"bestStreak,omitempty" bson:"bestStreak"`
	NumOfCorrectSubmissions    int       `json:"numOfCorrectSubmissions,omitempty" bson:"numOfCorrectSubmissions"`
	IncorrectSubmissions       int       `json:"incorrectSubmissions,omitempty" bson:"incorrectSubmissions"`
	UserID                     ObjectID  `json:"userId" bson:"userId"`
	AvgTime                    float64   `json:"avgTime,omitempty" bson:"avgTime"`
	AvgAccuracy                float64   `json:"avgAccuracy,omitempty" bson:"avgAccuracy"`
	QuestionsSolved            int64     `json:"questionsSolved,omitempty" bson:"questionsSolved"`
	TimePerformanceTrend       []int     `json:"timePerformanceTrend,omitempty" bson:"timePerformanceTrend"`
	InaccuracyPerformanceTrend []int     `json:"inaccuracyPerformanceTrend,omitempty" bson:"inaccuracyPerformanceTrend"`
}

type PresetCategory string

const (
	PresetCategoryAdd    PresetCategory = "ADD"
	PresetCategoryAddsub PresetCategory = "ADDSUB"
	PresetCategoryMult   PresetCategory = "MULT"
	PresetCategoryDiv    PresetCategory = "DIV"
	PresetCategoryMod    PresetCategory = "MOD"
	PresetCategoryRoot   PresetCategory = "ROOT"
	PresetCategoryExpo   PresetCategory = "EXPO"
	PresetCategoryHcf    PresetCategory = "HCF"
	PresetCategoryLcm    PresetCategory = "LCM"
	PresetCategoryPf     PresetCategory = "PF"
	PresetCategoryMulOp  PresetCategory = "MULOP"
	PresetCategorySOS    PresetCategory = "SOS"
	PresetCategoryFrac   PresetCategory = "FRAC"
)

var AllPresetCategory = []PresetCategory{
	PresetCategoryAdd,
	PresetCategoryAddsub,
	PresetCategoryMult,
	PresetCategoryDiv,
	PresetCategoryMod,
	PresetCategoryRoot,
	PresetCategoryExpo,
	PresetCategoryHcf,
	PresetCategoryLcm,
	PresetCategoryPf,
	PresetCategoryMulOp,
	PresetCategorySOS,
	PresetCategoryFrac,
}

func (e PresetCategory) IsValid() bool {
	switch e {
	case PresetCategoryAdd, PresetCategoryAddsub, PresetCategoryMult, PresetCategoryDiv:
		return true
	}
	return false
}

func (e PresetCategory) String() string {
	return string(e)
}

func (e *PresetCategory) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = PresetCategory(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid PresetCategory", str)
	}
	return nil
}

func (e PresetCategory) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type UserPresetStatsGraph struct {
	UserStats   []*UserPresetDayStats `json:"userStats,omitempty" bson:"userStats"`
	GlobalStats []*UserPresetDayStats `json:"globalStats,omitempty" bson:"globalStats"`
}

type PlayedPresets struct {
	Identifier *string  `json:"identifier,omitempty" bson:"identifier"`
	AvgTime    *float64 `json:"avgTime,omitempty" bson:"avgTime"`
}

type AllPlayedPresetsOutput struct {
	Presets []*PlayedPresets `json:"presets,omitempty" bson:"presets"`
	Count   *int             `json:"count,omitempty" bson:"count"`
}
