package models

import (
	"fmt"
	"io"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Difficulty string

const (
	Easy   Difficulty = "easy"
	Medium Difficulty = "medium"
	Hard   Difficulty = "hard"
)

func (e Difficulty) String() string {
	return string(e)
}

type Cell struct {
	IsVisible bool     `json:"isVisible" bson:"isVisible"`
	Value     string   `json:"value" bson:"value"`
	Type      CellType `json:"type" bson:"type"`
}

type CellInput struct {
	Pos   int    `json:"pos" bson:"pos"`
	Value string `json:"value" bson:"value"`
}

type CrossMathPuzzle struct {
	PuzzleString *string `json:"puzzleString,omitempty" bson:"puzzleString"`
}

type KenKenPuzzle struct {
	PuzzleString *string `json:"puzzleString,omitempty" bson:"puzzleString"`
}

type HectocPuzzle struct {
	PuzzleString *string `json:"puzzleString,omitempty" bson:"puzzleString"`
}

type Puzzle struct {
	ID                primitive.ObjectID         `json:"id" bson:"_id"`
	Difficulty        string                     `json:"difficulty" bson:"difficulty"`
	SolvedBy          *int                       `json:"solvedBy,omitempty" bson:"solvedBy"`
	Cells             [][]*Cell                  `json:"cells" bson:"cells"`
	PuzzleType        *PuzzleType                `json:"puzzleType,omitempty" bson:"puzzleType"`
	PuzzleDate        *string                    `json:"puzzleDate,omitempty" bson:"puzzleDate"`
	AvailableAnswers  []string                   `json:"availableAnswers,omitempty" bson:"availableAnswers"`
	TypeSpecific      *PuzzleTypeSpecificDetails `json:"typeSpecific,omitempty" bson:"typeSpecific"`
	HasAttempted      *bool                      `json:"hasAttempted,omitempty" bson:"-"`
	CurrentUserResult *PuzzleResult              `json:"currentUserResult,omitempty" bson:"-"`
	Stats             *PuzzleStats               `json:"stats,omitempty" bson:"stats"`
	UserStat          *PuzzleUserStats           `json:"userStat,omitempty" bson:"-"`
}

type PuzzleMonthlySubmissionReport struct {
	YearMonth         string          `json:"yearMonth" bson:"yearMonth"`
	PuzzleSubmissions []*PuzzleResult `json:"puzzleSubmissions,omitempty" bson:"puzzleSubmissions"`
}

type PuzzleResult struct {
	ID                primitive.ObjectID  `json:"id" bson:"_id"`
	UserID            *primitive.ObjectID `json:"userId,omitempty" bson:"userId"`
	PuzzleID          *primitive.ObjectID `json:"puzzleId,omitempty" bson:"puzzleId"`
	TimeSpent         *int                `json:"timeSpent,omitempty" bson:"timeSpent"`
	CompletedAt       *time.Time          `json:"completedAt,omitempty" bson:"completedAt"`
	StatikCoinsEarned *int                `json:"statikCoinsEarned,omitempty" bson:"statikCoinsEarned"`
	PuzzleDate        string              `json:"puzzleDate" bson:"puzzleDate"`
	PuzzleType        *PuzzleType         `json:"puzzleType,omitempty" bson:"puzzleType"`
}

type PuzzleStats struct {
	NumOfSubmission int `json:"numOfSubmission" bson:"numOfSubmission"`
	AverageTime     int `json:"averageTime" bson:"averageTime"`
	BestTime        int `json:"bestTime" bson:"bestTime"`
}

type PuzzleTypeSpecificDetails struct {
	PuzzleType PuzzleType       `json:"puzzleType" bson:"puzzleType"`
	CrossMath  *CrossMathPuzzle `json:"crossMath,omitempty" bson:"crossMath"`
	KenKen     *KenKenPuzzle    `json:"kenKen,omitempty" bson:"kenKen"`
	Hectoc     *HectocPuzzle    `json:"hectoc,omitempty" bson:"hectoc"`
}

type CellType string

const (
	CellTypeOperator   CellType = "Operator"
	CellTypeOperand    CellType = "Operand"
	CellTypeEmptyBlock CellType = "EmptyBlock"
)

var AllCellType = []CellType{
	CellTypeOperator,
	CellTypeOperand,
	CellTypeEmptyBlock,
}

func (e CellType) IsValid() bool {
	switch e {
	case CellTypeOperator, CellTypeOperand, CellTypeEmptyBlock:
		return true
	}
	return false
}

func (e CellType) String() string {
	return string(e)
}

func (e *CellType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = CellType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid CellType", str)
	}
	return nil
}

func (e CellType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type PuzzleType string

const (
	PuzzleTypeKenKen    PuzzleType = "KenKen"
	PuzzleTypeCrossMath PuzzleType = "CrossMath"
	PuzzleTypeHectoc    PuzzleType = "Hectoc"
)

var AllPuzzleType = []PuzzleType{
	PuzzleTypeKenKen,
	PuzzleTypeCrossMath,
	PuzzleTypeHectoc,
}

func (e PuzzleType) IsValid() bool {
	switch e {
	case PuzzleTypeKenKen, PuzzleTypeCrossMath, PuzzleTypeHectoc:
		return true
	}
	return false
}

func (e PuzzleType) String() string {
	return string(e)
}

func (e *PuzzleType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = PuzzleType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid PuzzleType", str)
	}
	return nil
}

func (e PuzzleType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}
