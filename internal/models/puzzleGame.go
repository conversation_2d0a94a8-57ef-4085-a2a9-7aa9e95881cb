package models

import (
	"fmt"
	"io"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ChallengeUserForPuzzleGameInput struct {
	UserID     *primitive.ObjectID    `json:"userId,omitempty" bson:"userId"`
	GameConfig *PuzzleGameConfigInput `json:"gameConfig,omitempty" bson:"gameConfig"`
}

type ChallengeForPuzzleGameOutput struct {
	GameID       primitive.ObjectID `json:"gameId" bson:"gameId"`
	ChallengedBy primitive.ObjectID `json:"challengedBy" bson:"challengedBy"`
	GameConfig   *PuzzleGameConfig  `json:"gameConfig" bson:"gameConfig"`
	CreatedAt    time.Time          `json:"createdAt" bson:"createdAt"`
	Status       *ChallengeStatus   `json:"status,omitempty" bson:"status"`
	Opponent     *User              `json:"opponent,omitempty" bson:"opponent,omitempty"`
}

type GetPuzzleGamesInput struct {
	UserID    *primitive.ObjectID `json:"userId,omitempty" bson:"userId"`
	TimeRange *TimeRangeInput     `json:"timeRange,omitempty" bson:"timeRange"`
	PageInfo  *PageInfoInput      `json:"pageInfo,omitempty" bson:"pageInfo"`
}

type GetPuzzleGamesOutput struct {
	Games []*MinifiedPuzzleGame `json:"games,omitempty" bson:"games"`
	Users []*UserPublicDetails  `json:"users,omitempty" bson:"users"`
}

type JoinPuzzleGameInput struct {
	GameID *primitive.ObjectID `json:"gameId,omitempty" bson:"gameId"`
}

type MinifiedPuzzleGame struct {
	ID          primitive.ObjectID        `json:"_id" bson:"_id"`
	Players     []*Player                 `json:"players" bson:"players"`
	Config      *PuzzleGameConfig         `json:"config,omitempty" bson:"config"`
	LeaderBoard []*PuzzleLeaderboardEntry `json:"leaderBoard,omitempty" bson:"leaderBoard"`
	StartTime   *time.Time                `json:"startTime,omitempty" bson:"startTime"`
	EndTime     *time.Time                `json:"endTime,omitempty" bson:"endTime"`
}

type PuzzleGame struct {
	ID                 primitive.ObjectID        `json:"_id" bson:"_id"`
	Players            []*Player                 `json:"players" bson:"players"`
	GameStatus         PuzzleGameStatus          `json:"gameStatus" bson:"gameStatus"`
	RematchRequestedBy *primitive.ObjectID       `json:"rematchRequestedBy,omitempty" bson:"rematchRequestedBy"`
	GameType           PuzzleGameType            `json:"gameType" bson:"gameType"`
	CreatedBy          primitive.ObjectID        `json:"createdBy" bson:"createdBy"`
	Config             *PuzzleGameConfig         `json:"config,omitempty" bson:"config"`
	Questions          []*PuzzleGameQuestion     `json:"questions,omitempty" bson:"questions"`
	LeaderBoard        []*PuzzleLeaderboardEntry `json:"leaderBoard,omitempty" bson:"leaderBoard"`
	IsRatedGame        *bool                     `json:"isRatedGame,omitempty" bson:"isRatedGame"`
	StartTime          *time.Time                `json:"startTime,omitempty" bson:"startTime"`
	EndTime            *time.Time                `json:"endTime,omitempty" bson:"endTime"`
	SeriesID           *primitive.ObjectID       `json:"seriesId,omitempty" bson:"seriesId"`
	CreatedAt          *time.Time                `json:"createdAt,omitempty" bson:"createdAt"`
	UpdatedAt          *time.Time                `json:"updatedAt,omitempty" bson:"updatedAt"`
}

type PuzzleGameConfig struct {
	TimeLimit          *int            `json:"timeLimit,omitempty" bson:"timeLimit"`
	NumPlayers         *int            `json:"numPlayers,omitempty" bson:"numPlayers"`
	NumOfQuestions     *int            `json:"numOfQuestions,omitempty" bson:"numOfQuestions"`
	GameType           *PuzzleGameType `json:"gameType,omitempty" bson:"gameType"`
	DifficultyLevel    []int           `json:"difficultyLevel,omitempty" bson:"difficultyLevel"`
	MaxTimePerQuestion *int            `json:"maxTimePerQuestion,omitempty" bson:"maxTimePerQuestion"`
}

type PuzzleGameConfigInput struct {
	TimeLimit          *int            `json:"timeLimit,omitempty" bson:"timeLimit"`
	NumPlayers         *int            `json:"numPlayers,omitempty" bson:"numPlayers"`
	NumOfQuestions     *int            `json:"numOfQuestions,omitempty" bson:"numOfQuestions"`
	GameType           *PuzzleGameType `json:"gameType,omitempty" bson:"gameType"`
	DifficultyLevel    []int           `json:"difficultyLevel,omitempty" bson:"difficultyLevel"`
	MaxTimePerQuestion *int            `json:"maxTimePerQuestion,omitempty" bson:"maxTimePerQuestion"`
}

type PuzzleGameQuestion struct {
	Id          primitive.ObjectID          `json:"_id,omitempty" bson:"_id"`
	Question    *string                     `json:"question,omitempty" bson:"question"`
	Submissions []*PuzzleQuestionSubmission `json:"submissions,omitempty" bson:"submissions"`
	Stats       *PuzzleGameQuestionStats    `json:"stats,omitempty" bson:"stats"`
}

type PuzzleGameQuestionStats struct {
	FastestTime *int                  `json:"fastestTime,omitempty" bson:"fastestTime"`
	UserIds     []*primitive.ObjectID `json:"userIds,omitempty" bson:"userIds"`
}

type PuzzleLeaderboardEntry struct {
	UserID            *primitive.ObjectID `json:"userId,omitempty" bson:"userId"`
	Correct           *int                `json:"correct,omitempty" bson:"correct"`
	Incorrect         *int                `json:"incorrect,omitempty" bson:"incorrect"`
	TotalPoints       *float64            `json:"totalPoints,omitempty" bson:"totalPoints"`
	RatingChange      *int                `json:"ratingChange,omitempty" bson:"ratingChange"`
	StatikCoinsEarned *int                `json:"statikCoinsEarned,omitempty" bson:"statikCoinsEarned"`
	Rank              *int                `json:"rank,omitempty" bson:"rank"`
}

type PuzzleQuestionSubmission struct {
	UserID            *primitive.ObjectID `json:"userId,omitempty" bson:"userId"`
	TimeTaken         *int                `json:"timeTaken,omitempty" bson:"timeTaken"`
	Points            *int                `json:"points,omitempty" bson:"points"`
	SubmissionTime    *Date               `json:"submissionTime,omitempty" bson:"submissionTime"`
	IsCorrect         *bool               `json:"isCorrect,omitempty" bson:"isCorrect"`
	InCorrectAttempts *int                `json:"inCorrectAttempts,omitempty" bson:"inCorrectAttempts"`
	SubmittedValues   []*string           `json:"submittedValues,omitempty" bson:"submittedValues"`
}

type StartPuzzleGameInput struct {
	GameID *primitive.ObjectID `json:"gameId,omitempty" bson:"gameId"`
}

type SubmitPuzzleGameAnswerInput struct {
	GameID            *primitive.ObjectID `json:"gameId,omitempty" bson:"gameId"`
	QuestionID        *string             `json:"questionId,omitempty" bson:"questionId"`
	IsCorrect         *bool               `json:"isCorrect,omitempty" bson:"isCorrect"`
	InCorrectAttempts *int                `json:"inCorrectAttempts,omitempty" bson:"inCorrectAttempts"`
	TimeOfSubmission  *Date               `json:"timeOfSubmission,omitempty" bson:"timeOfSubmission"`
}
type PuzzleGameEventOutput struct {
	PuzzleGame *PuzzleGame `json:"puzzleGame,omitempty" bson:"puzzleGame,omitempty"`
	Event      *string     `json:"event,omitempty" bson:"event,omitempty"`
	Question   *Question   `json:"question,omitempty" bson:"question,omitempty"`
}

type PuzzleGameEventWithOpponentOutput struct {
	PuzzleGame *PuzzleGame `json:"puzzleGame,omitempty" bson:"puzzleGame,omitempty"`
	Event      *string     `json:"event,omitempty" bson:"event,omitempty"`
	Opponent   *User       `json:"opponent,omitempty" bson:"opponent,omitempty"`
}

type SubmitPuzzleGameAnswerEvent struct {
	PuzzleGame *PuzzleGame `json:"puzzleGame,omitempty"`
	Event      *string     `json:"event,omitempty"`
}

type PuzzleGameStatus string

const (
	PuzzleGameStatusCreated   PuzzleGameStatus = "CREATED"
	PuzzleGameStatusReady     PuzzleGameStatus = "READY"
	PuzzleGameStatusStarted   PuzzleGameStatus = "STARTED"
	PuzzleGameStatusPaused    PuzzleGameStatus = "PAUSED"
	PuzzleGameStatusEnded     PuzzleGameStatus = "ENDED"
	PuzzleGameStatusCancelled PuzzleGameStatus = "CANCELLED"
)

var AllPuzzleGameStatus = []PuzzleGameStatus{
	PuzzleGameStatusCreated,
	PuzzleGameStatusReady,
	PuzzleGameStatusStarted,
	PuzzleGameStatusPaused,
	PuzzleGameStatusEnded,
	PuzzleGameStatusCancelled,
}

func (e PuzzleGameStatus) IsValid() bool {
	switch e {
	case PuzzleGameStatusCreated, PuzzleGameStatusReady, PuzzleGameStatusStarted, PuzzleGameStatusPaused, PuzzleGameStatusEnded, PuzzleGameStatusCancelled:
		return true
	}
	return false
}

func (e PuzzleGameStatus) String() string {
	return string(e)
}

func (e *PuzzleGameStatus) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = PuzzleGameStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid PUZZLE_GAME_STATUS", str)
	}
	return nil
}

func (e PuzzleGameStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type PuzzleGameType string

const (
	PuzzleGameTypeCrossMathPuzzleDuel       PuzzleGameType = "CROSS_MATH_PUZZLE_DUEL"
	PuzzleGameTypeCrossMathPuzzleWithFriend PuzzleGameType = "CROSS_MATH_PUZZLE_WITH_FRIEND"
)

var AllPuzzleGameType = []PuzzleGameType{
	PuzzleGameTypeCrossMathPuzzleDuel,
	PuzzleGameTypeCrossMathPuzzleWithFriend,
}

func (e PuzzleGameType) IsValid() bool {
	switch e {
	case PuzzleGameTypeCrossMathPuzzleDuel, PuzzleGameTypeCrossMathPuzzleWithFriend:
		return true
	}
	return false
}

func (e PuzzleGameType) String() string {
	return string(e)
}

func (e *PuzzleGameType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = PuzzleGameType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid PUZZLE_GAME_TYPE", str)
	}
	return nil
}

func (e PuzzleGameType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type CrossMathPuzzleRush struct {
	ID             primitive.ObjectID `json:"_id" bson:"_id"`
	UserID         primitive.ObjectID `json:"userId" bson:"userId"`
	BestAllTime    *int               `json:"bestAllTime,omitempty" bson:"bestAllTime"`
	IsNewBestScore *bool              `json:"isNewBestScore,omitempty" bson:"-"`
	CreatedAt      *time.Time         `json:"createdAt,omitempty" bson:"createdAt"`
	UpdatedAt      *time.Time         `json:"updatedAt,omitempty" bson:"updatedAt"`
}

type CrossMathPuzzleRushPlayerInfo struct {
	Rank     *int               `json:"rank,omitempty" bson:"rank"`
	Score    *int               `json:"score,omitempty" bson:"score"`
	UserInfo *UserPublicDetails `json:"userInfo,omitempty" bson:"userInfo"`
}

type SubmitPuzzleRushGame struct {
	Score     *int `json:"score,omitempty" bson:"score"`
	TimeSpent *int `json:"timeSpent,omitempty" bson:"timeSpent"`
}

type CrossMathPuzzleRushStats struct {
	BestAllTime *int `json:"bestAllTime,omitempty" bson:"bestAllTime"`
	GlobalRank  *int `json:"globalRank,omitempty" bson:"globalRank"`
	FriendsRank *int `json:"friendsRank,omitempty" bson:"friendsRank"`
}
