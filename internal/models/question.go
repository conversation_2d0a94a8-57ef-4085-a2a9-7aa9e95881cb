package models

import (
	"fmt"
	"io"
	"strconv"
	"time"
)

type Question struct {
	ID               *string       `json:"id,omitempty" bson:"id,omitempty"`
	Expression       []string      `json:"expression" bson:"expression"`
	Description      *string       `json:"description,omitempty" bson:"description,omitempty"`
	Options          []*string     `json:"options,omitempty" bson:"options,omitempty"`
	Answers          []string      `json:"answers,omitempty" bson:"answers"`
	QuestionType     *QuestionType `json:"questionType,omitempty" bson:"questionType,omitempty"`
	Rating           *int          `json:"rating,omitempty" bson:"rating,omitempty"`
	MaxTimeLimit     *int          `json:"maxTimeLimit,omitempty" bson:"maxTimeLimit,omitempty"`
	Tags             []string      `json:"tags,omitempty" bson:"tags,omitempty"`
	FastestTimeTaken *int          `json:"fastestTimeTaken,omitempty" bson:"fastestTimeTaken,omitempty"`
	CreatedAt        time.Time     `json:"createdAt" bson:"createdAt"`
	UpdatedAt        time.Time     `json:"updatedAt" bson:"updatedAt"`
	PresetIdentifier string        `json:"presetIdentifier,omitempty" bson:"presetIdentifier,omitempty"`
}

type QuestionType string

const (
	QuestionTypeSingleChoice    QuestionType = "SINGLE_CHOICE"
	QuestionTypeMultiChoice     QuestionType = "MULTI_CHOICE"
	QuestionTypeFillInTheBlanks QuestionType = "FILL_IN_THE_BLANKS"
)

var AllQuestionType = []QuestionType{
	QuestionTypeSingleChoice,
	QuestionTypeMultiChoice,
	QuestionTypeFillInTheBlanks,
}

func (e QuestionType) IsValid() bool {
	switch e {
	case QuestionTypeSingleChoice, QuestionTypeMultiChoice, QuestionTypeFillInTheBlanks:
		return true
	}
	return false
}

func (e QuestionType) String() string {
	return string(e)
}

func (e *QuestionType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = QuestionType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid QuestionType", str)
	}
	return nil
}

func (e QuestionType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}
