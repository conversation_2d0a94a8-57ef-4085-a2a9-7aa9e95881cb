package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Referral struct {
	ID         primitive.ObjectID `json:"_id,omitempty" bson:"_id"`
	Referrer   primitive.ObjectID `json:"referrer,omitempty" bson:"referrer"`
	ReferredTo primitive.ObjectID `json:"referredTo,omitempty" bson:"referredTo"`
	ReferredAt *time.Time         `json:"referredAt,omitempty" bson:"referredAt"`
}
