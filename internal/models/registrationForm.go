package models

import (
	"fmt"
	"io"
	"strconv"
	"time"
)

type RegistrationForm struct {
	ID        ObjectID     `json:"_id" bson:"_id"`
	Fields    []*FormField `json:"fields" bson:"fields"`
	CreatedAt time.Time    `json:"createdAt" bson:"createdAt"`
	UpdatedAt time.Time    `json:"updatedAt" bson:"updatedAt"`
}

type FormField struct {
	ID         ObjectID         `json:"_id" bson:"_id"`
	Name       string           `json:"name" bson:"name"`
	Type       FieldType        `json:"type" bson:"type"`
	Label      string           `json:"label" bson:"label"`
	Required   bool             `json:"required" bson:"required"`
	Options    []string         `json:"options,omitempty" bson:"options"`
	Validation *FieldValidation `json:"validation,omitempty" bson:"validation"`
}

type FieldType string

const (
	FieldTypeText         FieldType = "TEXT"
	FieldTypeNumber       FieldType = "NUMBER"
	FieldTypeEmail        FieldType = "EMAIL"
	FieldTypeMobile       FieldType = "MOBILE"
	FieldTypeSingleSelect FieldType = "SINGLE_SELECT"
	FieldTypeMultiSelect  FieldType = "MULTI_SELECT"
	FieldTypeCheckbox     FieldType = "CHECKBOX"
	FieldTypeRadio        FieldType = "RADIO"
	FieldTypeImageInput   FieldType = "IMAGE_INPUT"
	FieldTypeFileInput    FieldType = "FILE_INPUT"
)

var AllFieldType = []FieldType{
	FieldTypeText,
	FieldTypeNumber,
	FieldTypeEmail,
	FieldTypeMobile,
	FieldTypeSingleSelect,
	FieldTypeMultiSelect,
	FieldTypeCheckbox,
	FieldTypeRadio,
	FieldTypeImageInput,
	FieldTypeFileInput,
}

func (e FieldType) IsValid() bool {
	switch e {
	case FieldTypeText, FieldTypeNumber, FieldTypeEmail, FieldTypeMobile, FieldTypeSingleSelect, FieldTypeMultiSelect, FieldTypeCheckbox, FieldTypeRadio, FieldTypeImageInput, FieldTypeFileInput:
		return true
	}
	return false
}

func (e FieldType) String() string {
	return string(e)
}

func (e *FieldType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = FieldType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid FieldType", str)
	}
	return nil
}

func (e FieldType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type FieldValidation struct {
	Regex                  *string  `json:"regex,omitempty" bson:"regex"`
	Min                    *int     `json:"min,omitempty" bson:"min"`
	Max                    *int     `json:"max,omitempty" bson:"max"`
	EmailSuffix            *string  `json:"emailSuffix,omitempty" bson:"emailSuffix"`
	EmailSuffixes          []string `json:"emailSuffixes,omitempty" bson:"emailSuffixes"`
	NeedManualVerification *bool    `json:"needManualVerification,omitempty" bson:"needManualVerification"`
}

type RegistrationFormInput struct {
	Fields []FormFieldInput `json:"fields" bson:"fields"`
}

type FormFieldInput struct {
	Name       string               `json:"name" bson:"name"`
	Type       FieldType            `json:"type" bson:"type"`
	Label      string               `json:"label" bson:"label"`
	Required   bool                 `json:"required" bson:"required"`
	Options    []string             `json:"options,omitempty" bson:"options"`
	Validation FieldValidationInput `json:"validation,omitempty" bson:"validation"`
}

type FieldValidationInput struct {
	Regex                  string   `json:"regex,omitempty" bson:"regex"`
	Min                    int      `json:"min,omitempty" bson:"min"`
	Max                    int      `json:"max,omitempty" bson:"max"`
	EmailSuffix            *string  `json:"emailSuffix,omitempty" bson:"emailSuffix"`
	EmailSuffixes          []string `json:"emailSuffixes,omitempty" bson:"emailSuffixes"`
	NeedManualVerification bool     `json:"needManualVerification,omitempty" bson:"needManualVerification"`
}
