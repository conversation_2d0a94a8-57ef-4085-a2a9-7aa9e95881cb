package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// TransactionType represents the type of streak shield transaction
type TransactionType string

const (
	// TransactionTypeDebited represents a transaction where shields were used
	TransactionTypeDebited TransactionType = "DEBITED"
	// TransactionTypeCredited represents a transaction where shields were earned
	TransactionTypeCredited TransactionType = "CREDITED"
)

// EarnVia represents how the streak shields were earned
type EarnVia string

const (
	// EarnViaReferral represents shields earned through referrals
	EarnViaReferral EarnVia = "REFERRAL"
	// EarnViaPurchased represents shields earned through purchase
	EarnViaPurchased EarnVia = "PURCHASED"
	// EarnViaOther represents shields earned through other means
	EarnViaOther EarnVia = "OTHER"
)

// StreakShieldTransaction represents a transaction of streak shields
type StreakShieldTransaction struct {
	ID              primitive.ObjectID  `json:"_id,omitempty" bson:"_id"`
	UserID          primitive.ObjectID  `json:"userId,omitempty" bson:"userId"`
	Quantity        int                 `json:"quantity,omitempty" bson:"quantity"`
	TransactionType TransactionType     `json:"transactionType,omitempty" bson:"transactionType"`
	EarnVia         *EarnVia            `json:"earnVia,omitempty" bson:"earnVia,omitempty"`
	ReferralID      *primitive.ObjectID `json:"referralId,omitempty" bson:"referralId,omitempty"`
	TransactionID   *string             `json:"transactionId,omitempty" bson:"transactionId,omitempty"`
	RedeemedOn      []time.Time         `json:"redeemedOn,omitempty" bson:"redeemedOn,omitempty"`
	CreatedAt       time.Time           `json:"createdAt,omitempty" bson:"createdAt"`
	UpdatedAt       time.Time           `json:"updatedAt,omitempty" bson:"updatedAt"`
}

type CreateStreakShieldTransactionInput struct {
	UserID          primitive.ObjectID  `json:"userId" bson:"userId"`
	Quantity        int                 `json:"quantity" bson:"quantity"`
	TransactionType TransactionType     `json:"transactionType" bson:"transactionType"`
	EarnVia         *EarnVia            `json:"earnVia,omitempty" bson:"earnVia"`
	ReferralID      *primitive.ObjectID `json:"referralId,omitempty" bson:"referralId"`
	TransactionID   *string             `json:"transactionId,omitempty" bson:"transactionId"`
}

type ReferralDetails struct {
	Referral     *Referral          `json:"referral"`
	ReferredUser *UserPublicDetails `json:"referredUser"`
}

type StreakShieldTransactionOutput struct {
	Transaction     *StreakShieldTransaction `json:"transaction"`
	ReferralDetails *ReferralDetails         `json:"referralDetails,omitempty"`
}

type StreakShieldTransactionPage struct {
	Results      []*StreakShieldTransactionOutput `json:"results"`
	PageNumber   int                              `json:"pageNumber"`
	PageSize     int                              `json:"pageSize"`
	HasMore      *bool                            `json:"hasMore"`
	TotalResults *int                             `json:"totalResults"`
}
