package models

import (
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// UserActivity represents a single activity performed by a user
type UserActivity struct {
	ID           primitive.ObjectID     `json:"_id,omitempty" bson:"_id,omitempty"`
	UserID       primitive.ObjectID     `json:"userId" bson:"userId"`
	ActivityType constants.ActivityType `json:"activityType" bson:"activityType"`
	XPEarned     int                    `json:"xpEarned" bson:"xpEarned"`
	ActivityID   *primitive.ObjectID    `json:"activityId,omitempty" bson:"activityId,omitempty"`
	Timestamp    time.Time              `json:"timestamp" bson:"timestamp"`
	TimeSpent    int64                  `json:"timeSpent" bson:"timeSpent"` // in milliseconds
}
