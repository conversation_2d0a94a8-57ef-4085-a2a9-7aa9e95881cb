package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type UserGameBucket struct {
	ID        primitive.ObjectID   `json:"id" bson:"_id"`
	UserID    primitive.ObjectID   `json:"userId" bson:"userId"`
	BucketNum int                  `json:"bucketNum" bson:"bucketNum"`
	NumGames  int                  `json:"numGames" bson:"numGames"`
	StartTime time.Time            `json:"startTime" bson:"startTime"`
	EndTime   time.Time            `json:"endTime" bson:"endTime"`
	Games     []primitive.ObjectID `json:"games" bson:"games"`
}
