package models

import (
	"fmt"
	"io"
	"strconv"
	"time"
)

type UserRatingFixtureSubmission struct {
	ID             ObjectID   `json:"_id" bson:"_id"`
	UserID         ObjectID   `json:"userId" bson:"userId"`
	Submissions    []*int     `json:"submissions" bson:"submissions"`
	UserScore      int        `json:"userScore" bson:"userScore"`
	TimeTaken      int        `json:"timeTaken" bson:"timeTaken"`
	CreatedAt      time.Time  `json:"createdAt" bson:"createdAt"`
	UpdatedAt      time.Time  `json:"updatedAt" bson:"updatedAt"`
	CurrentRating  int        `json:"currentRating" bson:"currentRating"`
	ProposedRating int        `json:"proposedRating" bson:"proposedRating"`
	UserStance     UserStance `json:"userStance" bson:"userStance"`
}

type UserStance string

const (
	UserStancePending  UserStance = "PENDING"
	UserStanceAccepted UserStance = "ACCEPTED"
	UserStanceRejected UserStance = "REJECTED"
)

var AllUserStance = []UserStance{
	UserStancePending,
	UserStanceAccepted,
	UserStanceRejected,
}

func (e UserStance) IsValid() bool {
	switch e {
	case UserStancePending, UserStanceAccepted, UserStanceRejected:
		return true
	}
	return false
}

func (e UserStance) String() string {
	return string(e)
}

func (e *UserStance) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = UserStance(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid UserStance", str)
	}
	return nil
}

func (e UserStance) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}
