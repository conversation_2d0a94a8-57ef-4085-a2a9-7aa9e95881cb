package models

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type UserSettings struct {
	ID             primitive.ObjectID `json:"_id" bson:"_id"`
	UserID         primitive.ObjectID `json:"userId" bson:"userId"`
	PlaySound      bool               `json:"playSound" bson:"playSound"`
	HapticFeedback bool               `json:"hapticFeedback" bson:"hapticFeedback"`
}

type UpdateSettingsInput struct {
	PlaySound      *bool `json:"playSound" bson:"playSound"`
	HapticFeedback *bool `json:"hapticFeedback" bson:"hapticFeedback"`
}

type CreateSettingsInput struct {
	UserID         primitive.ObjectID `json:"userId" bson:"userId"`
	PlaySound      bool               `json:"playSound" bson:"playSound"`
	HapticFeedback bool               `json:"hapticFeedback" bson:"hapticFeedback"`
}
