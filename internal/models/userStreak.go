package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type StreakDay struct {
	Date         time.Time `json:"date" bson:"date"`
	Activity     bool      `json:"activity" bson:"activity"`
	IsShieldUsed bool      `json:"isShieldUsed" bson:"isShieldUsed"`
}

type StreakHistory struct {
	ID               primitive.ObjectID `json:"_id,omitempty" bson:"_id"`
	StreakHistoryObj []*StreakEntry     `json:"streakHistoryObj,omitempty" bson:"streakHistoryObj"`
}

type StreakEntry struct {
	Date         time.Time `json:"date,omitempty" bson:"date"`
	IsShieldUsed bool      `json:"isShieldUsed,omitempty" bson:"isShieldUsed"`
}

type StreakStatusResponse struct {
	HasStreak       bool        `json:"hasStreak" bson:"hasStreak"`
	StreakFreezers  int         `json:"streakFreezers" bson:"streakFreezers"`
	MissedDays      int         `json:"missedDays" bson:"missedDays"`
	CanSaveStreak   bool        `json:"canSaveStreak" bson:"canSaveStreak"`
	LostStreakCount int         `json:"lostStreakCount" bson:"lostStreakCount"`
	LastSevenDays   []StreakDay `json:"lastSevenDays,omitempty" bson:"lastSevenDays"`
}

type UserStreaks struct {
	CurrentStreak  int          `json:"currentStreak,omitempty" bson:"currentStreak"`
	LongestStreak  int          `json:"longestStreak,omitempty" bson:"longestStreak"`
	LastPlayedDate time.Time    `json:"lastPlayedDate,omitempty" bson:"lastPlayedDate"`
	LastSevenDays  []bool       `json:"lastSevenDays,omitempty" bson:"lastSevenDays"`
	StreakHistory  []*time.Time `json:"streakHistory,omitempty" bson:"streakHistory"`
	StreakFreezers int          `json:"streakFreezers,omitempty" bson:"streakFreezers"`
}
