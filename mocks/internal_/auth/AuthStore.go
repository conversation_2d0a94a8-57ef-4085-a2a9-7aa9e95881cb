// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package auth

import (
	"github.com/golang-jwt/jwt/v5"
	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"golang.org/x/oauth2"
)

// NewMockAuthStore creates a new instance of MockAuthStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockAuthStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockAuthStore {
	mock := &MockAuthStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockAuthStore is an autogenerated mock type for the AuthStore type
type MockAuthStore struct {
	mock.Mock
}

type MockAuthStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockAuthStore) EXPECT() *MockAuthStore_Expecter {
	return &MockAuthStore_Expecter{mock: &_m.Mock}
}

// ComparePasswords provides a mock function for the type MockAuthStore
func (_mock *MockAuthStore) ComparePasswords(hashedPassword string, password string) bool {
	ret := _mock.Called(hashedPassword, password)

	if len(ret) == 0 {
		panic("no return value specified for ComparePasswords")
	}

	var r0 bool
	if returnFunc, ok := ret.Get(0).(func(string, string) bool); ok {
		r0 = returnFunc(hashedPassword, password)
	} else {
		r0 = ret.Get(0).(bool)
	}
	return r0
}

// MockAuthStore_ComparePasswords_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ComparePasswords'
type MockAuthStore_ComparePasswords_Call struct {
	*mock.Call
}

// ComparePasswords is a helper method to define mock.On call
//   - hashedPassword
//   - password
func (_e *MockAuthStore_Expecter) ComparePasswords(hashedPassword interface{}, password interface{}) *MockAuthStore_ComparePasswords_Call {
	return &MockAuthStore_ComparePasswords_Call{Call: _e.mock.On("ComparePasswords", hashedPassword, password)}
}

func (_c *MockAuthStore_ComparePasswords_Call) Run(run func(hashedPassword string, password string)) *MockAuthStore_ComparePasswords_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockAuthStore_ComparePasswords_Call) Return(b bool) *MockAuthStore_ComparePasswords_Call {
	_c.Call.Return(b)
	return _c
}

func (_c *MockAuthStore_ComparePasswords_Call) RunAndReturn(run func(hashedPassword string, password string) bool) *MockAuthStore_ComparePasswords_Call {
	_c.Call.Return(run)
	return _c
}

// GenerateToken provides a mock function for the type MockAuthStore
func (_mock *MockAuthStore) GenerateToken(userID primitive.ObjectID) (string, error) {
	ret := _mock.Called(userID)

	if len(ret) == 0 {
		panic("no return value specified for GenerateToken")
	}

	var r0 string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(primitive.ObjectID) (string, error)); ok {
		return returnFunc(userID)
	}
	if returnFunc, ok := ret.Get(0).(func(primitive.ObjectID) string); ok {
		r0 = returnFunc(userID)
	} else {
		r0 = ret.Get(0).(string)
	}
	if returnFunc, ok := ret.Get(1).(func(primitive.ObjectID) error); ok {
		r1 = returnFunc(userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockAuthStore_GenerateToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GenerateToken'
type MockAuthStore_GenerateToken_Call struct {
	*mock.Call
}

// GenerateToken is a helper method to define mock.On call
//   - userID
func (_e *MockAuthStore_Expecter) GenerateToken(userID interface{}) *MockAuthStore_GenerateToken_Call {
	return &MockAuthStore_GenerateToken_Call{Call: _e.mock.On("GenerateToken", userID)}
}

func (_c *MockAuthStore_GenerateToken_Call) Run(run func(userID primitive.ObjectID)) *MockAuthStore_GenerateToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockAuthStore_GenerateToken_Call) Return(s string, err error) *MockAuthStore_GenerateToken_Call {
	_c.Call.Return(s, err)
	return _c
}

func (_c *MockAuthStore_GenerateToken_Call) RunAndReturn(run func(userID primitive.ObjectID) (string, error)) *MockAuthStore_GenerateToken_Call {
	_c.Call.Return(run)
	return _c
}

// HashPassword provides a mock function for the type MockAuthStore
func (_mock *MockAuthStore) HashPassword(password string) (string, error) {
	ret := _mock.Called(password)

	if len(ret) == 0 {
		panic("no return value specified for HashPassword")
	}

	var r0 string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(string) (string, error)); ok {
		return returnFunc(password)
	}
	if returnFunc, ok := ret.Get(0).(func(string) string); ok {
		r0 = returnFunc(password)
	} else {
		r0 = ret.Get(0).(string)
	}
	if returnFunc, ok := ret.Get(1).(func(string) error); ok {
		r1 = returnFunc(password)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockAuthStore_HashPassword_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HashPassword'
type MockAuthStore_HashPassword_Call struct {
	*mock.Call
}

// HashPassword is a helper method to define mock.On call
//   - password
func (_e *MockAuthStore_Expecter) HashPassword(password interface{}) *MockAuthStore_HashPassword_Call {
	return &MockAuthStore_HashPassword_Call{Call: _e.mock.On("HashPassword", password)}
}

func (_c *MockAuthStore_HashPassword_Call) Run(run func(password string)) *MockAuthStore_HashPassword_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockAuthStore_HashPassword_Call) Return(s string, err error) *MockAuthStore_HashPassword_Call {
	_c.Call.Return(s, err)
	return _c
}

func (_c *MockAuthStore_HashPassword_Call) RunAndReturn(run func(password string) (string, error)) *MockAuthStore_HashPassword_Call {
	_c.Call.Return(run)
	return _c
}

// OAuthClient provides a mock function for the type MockAuthStore
func (_mock *MockAuthStore) OAuthClient() *oauth2.Config {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for OAuthClient")
	}

	var r0 *oauth2.Config
	if returnFunc, ok := ret.Get(0).(func() *oauth2.Config); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*oauth2.Config)
		}
	}
	return r0
}

// MockAuthStore_OAuthClient_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OAuthClient'
type MockAuthStore_OAuthClient_Call struct {
	*mock.Call
}

// OAuthClient is a helper method to define mock.On call
func (_e *MockAuthStore_Expecter) OAuthClient() *MockAuthStore_OAuthClient_Call {
	return &MockAuthStore_OAuthClient_Call{Call: _e.mock.On("OAuthClient")}
}

func (_c *MockAuthStore_OAuthClient_Call) Run(run func()) *MockAuthStore_OAuthClient_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockAuthStore_OAuthClient_Call) Return(config *oauth2.Config) *MockAuthStore_OAuthClient_Call {
	_c.Call.Return(config)
	return _c
}

func (_c *MockAuthStore_OAuthClient_Call) RunAndReturn(run func() *oauth2.Config) *MockAuthStore_OAuthClient_Call {
	_c.Call.Return(run)
	return _c
}

// VerifyToken provides a mock function for the type MockAuthStore
func (_mock *MockAuthStore) VerifyToken(tokenString string) (jwt.MapClaims, error) {
	ret := _mock.Called(tokenString)

	if len(ret) == 0 {
		panic("no return value specified for VerifyToken")
	}

	var r0 jwt.MapClaims
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(string) (jwt.MapClaims, error)); ok {
		return returnFunc(tokenString)
	}
	if returnFunc, ok := ret.Get(0).(func(string) jwt.MapClaims); ok {
		r0 = returnFunc(tokenString)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(jwt.MapClaims)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(string) error); ok {
		r1 = returnFunc(tokenString)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockAuthStore_VerifyToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'VerifyToken'
type MockAuthStore_VerifyToken_Call struct {
	*mock.Call
}

// VerifyToken is a helper method to define mock.On call
//   - tokenString
func (_e *MockAuthStore_Expecter) VerifyToken(tokenString interface{}) *MockAuthStore_VerifyToken_Call {
	return &MockAuthStore_VerifyToken_Call{Call: _e.mock.On("VerifyToken", tokenString)}
}

func (_c *MockAuthStore_VerifyToken_Call) Run(run func(tokenString string)) *MockAuthStore_VerifyToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockAuthStore_VerifyToken_Call) Return(mapClaims jwt.MapClaims, err error) *MockAuthStore_VerifyToken_Call {
	_c.Call.Return(mapClaims, err)
	return _c
}

func (_c *MockAuthStore_VerifyToken_Call) RunAndReturn(run func(tokenString string) (jwt.MapClaims, error)) *MockAuthStore_VerifyToken_Call {
	_c.Call.Return(run)
	return _c
}
