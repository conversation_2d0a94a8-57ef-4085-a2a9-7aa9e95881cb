// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"
	"time"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockClubAnnouncementStore creates a new instance of MockClubAnnouncementStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockClubAnnouncementStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockClubAnnouncementStore {
	mock := &MockClubAnnouncementStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockClubAnnouncementStore is an autogenerated mock type for the ClubAnnouncementStore type
type MockClubAnnouncementStore struct {
	mock.Mock
}

type MockClubAnnouncementStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockClubAnnouncementStore) EXPECT() *MockClubAnnouncementStore_Expecter {
	return &MockClubAnnouncementStore_Expecter{mock: &_m.Mock}
}

// ClubAnnouncements provides a mock function for the type MockClubAnnouncementStore
func (_mock *MockClubAnnouncementStore) ClubAnnouncements(ctx context.Context, page *int, pageSize *int, clubID *primitive.ObjectID, from *time.Time, to *time.Time) (*models.ClubAnnouncementsPage, error) {
	ret := _mock.Called(ctx, page, pageSize, clubID, from, to)

	if len(ret) == 0 {
		panic("no return value specified for ClubAnnouncements")
	}

	var r0 *models.ClubAnnouncementsPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *primitive.ObjectID, *time.Time, *time.Time) (*models.ClubAnnouncementsPage, error)); ok {
		return returnFunc(ctx, page, pageSize, clubID, from, to)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *primitive.ObjectID, *time.Time, *time.Time) *models.ClubAnnouncementsPage); ok {
		r0 = returnFunc(ctx, page, pageSize, clubID, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubAnnouncementsPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int, *primitive.ObjectID, *time.Time, *time.Time) error); ok {
		r1 = returnFunc(ctx, page, pageSize, clubID, from, to)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubAnnouncementStore_ClubAnnouncements_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ClubAnnouncements'
type MockClubAnnouncementStore_ClubAnnouncements_Call struct {
	*mock.Call
}

// ClubAnnouncements is a helper method to define mock.On call
//   - ctx
//   - page
//   - pageSize
//   - clubID
//   - from
//   - to
func (_e *MockClubAnnouncementStore_Expecter) ClubAnnouncements(ctx interface{}, page interface{}, pageSize interface{}, clubID interface{}, from interface{}, to interface{}) *MockClubAnnouncementStore_ClubAnnouncements_Call {
	return &MockClubAnnouncementStore_ClubAnnouncements_Call{Call: _e.mock.On("ClubAnnouncements", ctx, page, pageSize, clubID, from, to)}
}

func (_c *MockClubAnnouncementStore_ClubAnnouncements_Call) Run(run func(ctx context.Context, page *int, pageSize *int, clubID *primitive.ObjectID, from *time.Time, to *time.Time)) *MockClubAnnouncementStore_ClubAnnouncements_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*int), args[2].(*int), args[3].(*primitive.ObjectID), args[4].(*time.Time), args[5].(*time.Time))
	})
	return _c
}

func (_c *MockClubAnnouncementStore_ClubAnnouncements_Call) Return(clubAnnouncementsPage *models.ClubAnnouncementsPage, err error) *MockClubAnnouncementStore_ClubAnnouncements_Call {
	_c.Call.Return(clubAnnouncementsPage, err)
	return _c
}

func (_c *MockClubAnnouncementStore_ClubAnnouncements_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int, clubID *primitive.ObjectID, from *time.Time, to *time.Time) (*models.ClubAnnouncementsPage, error)) *MockClubAnnouncementStore_ClubAnnouncements_Call {
	_c.Call.Return(run)
	return _c
}

// CreateClubAnnouncement provides a mock function for the type MockClubAnnouncementStore
func (_mock *MockClubAnnouncementStore) CreateClubAnnouncement(ctx context.Context, input models.CreateClubAnnouncementInput) (*models.ClubAnnouncement, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateClubAnnouncement")
	}

	var r0 *models.ClubAnnouncement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateClubAnnouncementInput) (*models.ClubAnnouncement, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateClubAnnouncementInput) *models.ClubAnnouncement); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubAnnouncement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateClubAnnouncementInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubAnnouncementStore_CreateClubAnnouncement_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateClubAnnouncement'
type MockClubAnnouncementStore_CreateClubAnnouncement_Call struct {
	*mock.Call
}

// CreateClubAnnouncement is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockClubAnnouncementStore_Expecter) CreateClubAnnouncement(ctx interface{}, input interface{}) *MockClubAnnouncementStore_CreateClubAnnouncement_Call {
	return &MockClubAnnouncementStore_CreateClubAnnouncement_Call{Call: _e.mock.On("CreateClubAnnouncement", ctx, input)}
}

func (_c *MockClubAnnouncementStore_CreateClubAnnouncement_Call) Run(run func(ctx context.Context, input models.CreateClubAnnouncementInput)) *MockClubAnnouncementStore_CreateClubAnnouncement_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.CreateClubAnnouncementInput))
	})
	return _c
}

func (_c *MockClubAnnouncementStore_CreateClubAnnouncement_Call) Return(clubAnnouncement *models.ClubAnnouncement, err error) *MockClubAnnouncementStore_CreateClubAnnouncement_Call {
	_c.Call.Return(clubAnnouncement, err)
	return _c
}

func (_c *MockClubAnnouncementStore_CreateClubAnnouncement_Call) RunAndReturn(run func(ctx context.Context, input models.CreateClubAnnouncementInput) (*models.ClubAnnouncement, error)) *MockClubAnnouncementStore_CreateClubAnnouncement_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteClubAnnouncement provides a mock function for the type MockClubAnnouncementStore
func (_mock *MockClubAnnouncementStore) DeleteClubAnnouncement(ctx context.Context, id primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteClubAnnouncement")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubAnnouncementStore_DeleteClubAnnouncement_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteClubAnnouncement'
type MockClubAnnouncementStore_DeleteClubAnnouncement_Call struct {
	*mock.Call
}

// DeleteClubAnnouncement is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockClubAnnouncementStore_Expecter) DeleteClubAnnouncement(ctx interface{}, id interface{}) *MockClubAnnouncementStore_DeleteClubAnnouncement_Call {
	return &MockClubAnnouncementStore_DeleteClubAnnouncement_Call{Call: _e.mock.On("DeleteClubAnnouncement", ctx, id)}
}

func (_c *MockClubAnnouncementStore_DeleteClubAnnouncement_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockClubAnnouncementStore_DeleteClubAnnouncement_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockClubAnnouncementStore_DeleteClubAnnouncement_Call) Return(b bool, err error) *MockClubAnnouncementStore_DeleteClubAnnouncement_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockClubAnnouncementStore_DeleteClubAnnouncement_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (bool, error)) *MockClubAnnouncementStore_DeleteClubAnnouncement_Call {
	_c.Call.Return(run)
	return _c
}
