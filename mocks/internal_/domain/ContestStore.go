// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockContestStore creates a new instance of MockContestStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockContestStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockContestStore {
	mock := &MockContestStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockContestStore is an autogenerated mock type for the ContestStore type
type MockContestStore struct {
	mock.Mock
}

type MockContestStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockContestStore) EXPECT() *MockContestStore_Expecter {
	return &MockContestStore_Expecter{mock: &_m.Mock}
}

// CreateContest provides a mock function for the type MockContestStore
func (_mock *MockContestStore) CreateContest(ctx context.Context, input models.CreateContestInput) (*models.Contest, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateContest")
	}

	var r0 *models.Contest
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateContestInput) (*models.Contest, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateContestInput) *models.Contest); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Contest)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateContestInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestStore_CreateContest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateContest'
type MockContestStore_CreateContest_Call struct {
	*mock.Call
}

// CreateContest is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockContestStore_Expecter) CreateContest(ctx interface{}, input interface{}) *MockContestStore_CreateContest_Call {
	return &MockContestStore_CreateContest_Call{Call: _e.mock.On("CreateContest", ctx, input)}
}

func (_c *MockContestStore_CreateContest_Call) Run(run func(ctx context.Context, input models.CreateContestInput)) *MockContestStore_CreateContest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.CreateContestInput))
	})
	return _c
}

func (_c *MockContestStore_CreateContest_Call) Return(contest *models.Contest, err error) *MockContestStore_CreateContest_Call {
	_c.Call.Return(contest, err)
	return _c
}

func (_c *MockContestStore_CreateContest_Call) RunAndReturn(run func(ctx context.Context, input models.CreateContestInput) (*models.Contest, error)) *MockContestStore_CreateContest_Call {
	_c.Call.Return(run)
	return _c
}

// GetContestByID provides a mock function for the type MockContestStore
func (_mock *MockContestStore) GetContestByID(ctx context.Context, contestID primitive.ObjectID) (*models.Contest, error) {
	ret := _mock.Called(ctx, contestID)

	if len(ret) == 0 {
		panic("no return value specified for GetContestByID")
	}

	var r0 *models.Contest
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Contest, error)); ok {
		return returnFunc(ctx, contestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Contest); ok {
		r0 = returnFunc(ctx, contestID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Contest)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, contestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestStore_GetContestByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetContestByID'
type MockContestStore_GetContestByID_Call struct {
	*mock.Call
}

// GetContestByID is a helper method to define mock.On call
//   - ctx
//   - contestID
func (_e *MockContestStore_Expecter) GetContestByID(ctx interface{}, contestID interface{}) *MockContestStore_GetContestByID_Call {
	return &MockContestStore_GetContestByID_Call{Call: _e.mock.On("GetContestByID", ctx, contestID)}
}

func (_c *MockContestStore_GetContestByID_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID)) *MockContestStore_GetContestByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockContestStore_GetContestByID_Call) Return(contest *models.Contest, err error) *MockContestStore_GetContestByID_Call {
	_c.Call.Return(contest, err)
	return _c
}

func (_c *MockContestStore_GetContestByID_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID) (*models.Contest, error)) *MockContestStore_GetContestByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetContestLeaderboard provides a mock function for the type MockContestStore
func (_mock *MockContestStore) GetContestLeaderboard(ctx context.Context, contestID primitive.ObjectID, pageNumber *int, pageSize *int) (*models.ContestLeaderboard, error) {
	ret := _mock.Called(ctx, contestID, pageNumber, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetContestLeaderboard")
	}

	var r0 *models.ContestLeaderboard
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) (*models.ContestLeaderboard, error)); ok {
		return returnFunc(ctx, contestID, pageNumber, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) *models.ContestLeaderboard); ok {
		r0 = returnFunc(ctx, contestID, pageNumber, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ContestLeaderboard)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *int, *int) error); ok {
		r1 = returnFunc(ctx, contestID, pageNumber, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestStore_GetContestLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetContestLeaderboard'
type MockContestStore_GetContestLeaderboard_Call struct {
	*mock.Call
}

// GetContestLeaderboard is a helper method to define mock.On call
//   - ctx
//   - contestID
//   - pageNumber
//   - pageSize
func (_e *MockContestStore_Expecter) GetContestLeaderboard(ctx interface{}, contestID interface{}, pageNumber interface{}, pageSize interface{}) *MockContestStore_GetContestLeaderboard_Call {
	return &MockContestStore_GetContestLeaderboard_Call{Call: _e.mock.On("GetContestLeaderboard", ctx, contestID, pageNumber, pageSize)}
}

func (_c *MockContestStore_GetContestLeaderboard_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID, pageNumber *int, pageSize *int)) *MockContestStore_GetContestLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(*int), args[3].(*int))
	})
	return _c
}

func (_c *MockContestStore_GetContestLeaderboard_Call) Return(contestLeaderboard *models.ContestLeaderboard, err error) *MockContestStore_GetContestLeaderboard_Call {
	_c.Call.Return(contestLeaderboard, err)
	return _c
}

func (_c *MockContestStore_GetContestLeaderboard_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID, pageNumber *int, pageSize *int) (*models.ContestLeaderboard, error)) *MockContestStore_GetContestLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetContestsByStatus provides a mock function for the type MockContestStore
func (_mock *MockContestStore) GetContestsByStatus(ctx context.Context, statuses []models.ContestStatus, page *int, pageSize *int, sortDirection *string) (*models.PaginatedContests, error) {
	ret := _mock.Called(ctx, statuses, page, pageSize, sortDirection)

	if len(ret) == 0 {
		panic("no return value specified for GetContestsByStatus")
	}

	var r0 *models.PaginatedContests
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []models.ContestStatus, *int, *int, *string) (*models.PaginatedContests, error)); ok {
		return returnFunc(ctx, statuses, page, pageSize, sortDirection)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []models.ContestStatus, *int, *int, *string) *models.PaginatedContests); ok {
		r0 = returnFunc(ctx, statuses, page, pageSize, sortDirection)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PaginatedContests)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []models.ContestStatus, *int, *int, *string) error); ok {
		r1 = returnFunc(ctx, statuses, page, pageSize, sortDirection)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestStore_GetContestsByStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetContestsByStatus'
type MockContestStore_GetContestsByStatus_Call struct {
	*mock.Call
}

// GetContestsByStatus is a helper method to define mock.On call
//   - ctx
//   - statuses
//   - page
//   - pageSize
//   - sortDirection
func (_e *MockContestStore_Expecter) GetContestsByStatus(ctx interface{}, statuses interface{}, page interface{}, pageSize interface{}, sortDirection interface{}) *MockContestStore_GetContestsByStatus_Call {
	return &MockContestStore_GetContestsByStatus_Call{Call: _e.mock.On("GetContestsByStatus", ctx, statuses, page, pageSize, sortDirection)}
}

func (_c *MockContestStore_GetContestsByStatus_Call) Run(run func(ctx context.Context, statuses []models.ContestStatus, page *int, pageSize *int, sortDirection *string)) *MockContestStore_GetContestsByStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]models.ContestStatus), args[2].(*int), args[3].(*int), args[4].(*string))
	})
	return _c
}

func (_c *MockContestStore_GetContestsByStatus_Call) Return(paginatedContests *models.PaginatedContests, err error) *MockContestStore_GetContestsByStatus_Call {
	_c.Call.Return(paginatedContests, err)
	return _c
}

func (_c *MockContestStore_GetContestsByStatus_Call) RunAndReturn(run func(ctx context.Context, statuses []models.ContestStatus, page *int, pageSize *int, sortDirection *string) (*models.PaginatedContests, error)) *MockContestStore_GetContestsByStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeaturedContests provides a mock function for the type MockContestStore
func (_mock *MockContestStore) GetFeaturedContests(ctx context.Context) ([]*models.Contest, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetFeaturedContests")
	}

	var r0 []*models.Contest
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.Contest, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.Contest); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Contest)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestStore_GetFeaturedContests_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeaturedContests'
type MockContestStore_GetFeaturedContests_Call struct {
	*mock.Call
}

// GetFeaturedContests is a helper method to define mock.On call
//   - ctx
func (_e *MockContestStore_Expecter) GetFeaturedContests(ctx interface{}) *MockContestStore_GetFeaturedContests_Call {
	return &MockContestStore_GetFeaturedContests_Call{Call: _e.mock.On("GetFeaturedContests", ctx)}
}

func (_c *MockContestStore_GetFeaturedContests_Call) Run(run func(ctx context.Context)) *MockContestStore_GetFeaturedContests_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockContestStore_GetFeaturedContests_Call) Return(contests []*models.Contest, err error) *MockContestStore_GetFeaturedContests_Call {
	_c.Call.Return(contests, err)
	return _c
}

func (_c *MockContestStore_GetFeaturedContests_Call) RunAndReturn(run func(ctx context.Context) ([]*models.Contest, error)) *MockContestStore_GetFeaturedContests_Call {
	_c.Call.Return(run)
	return _c
}

// GetRegisteredContests provides a mock function for the type MockContestStore
func (_mock *MockContestStore) GetRegisteredContests(ctx context.Context) ([]*models.Contest, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetRegisteredContests")
	}

	var r0 []*models.Contest
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.Contest, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.Contest); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Contest)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestStore_GetRegisteredContests_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRegisteredContests'
type MockContestStore_GetRegisteredContests_Call struct {
	*mock.Call
}

// GetRegisteredContests is a helper method to define mock.On call
//   - ctx
func (_e *MockContestStore_Expecter) GetRegisteredContests(ctx interface{}) *MockContestStore_GetRegisteredContests_Call {
	return &MockContestStore_GetRegisteredContests_Call{Call: _e.mock.On("GetRegisteredContests", ctx)}
}

func (_c *MockContestStore_GetRegisteredContests_Call) Run(run func(ctx context.Context)) *MockContestStore_GetRegisteredContests_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockContestStore_GetRegisteredContests_Call) Return(contests []*models.Contest, err error) *MockContestStore_GetRegisteredContests_Call {
	_c.Call.Return(contests, err)
	return _c
}

func (_c *MockContestStore_GetRegisteredContests_Call) RunAndReturn(run func(ctx context.Context) ([]*models.Contest, error)) *MockContestStore_GetRegisteredContests_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserContestResult provides a mock function for the type MockContestStore
func (_mock *MockContestStore) GetUserContestResult(ctx context.Context, contestID primitive.ObjectID) (*models.UserContestResult, error) {
	ret := _mock.Called(ctx, contestID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserContestResult")
	}

	var r0 *models.UserContestResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.UserContestResult, error)); ok {
		return returnFunc(ctx, contestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.UserContestResult); ok {
		r0 = returnFunc(ctx, contestID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserContestResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, contestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestStore_GetUserContestResult_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserContestResult'
type MockContestStore_GetUserContestResult_Call struct {
	*mock.Call
}

// GetUserContestResult is a helper method to define mock.On call
//   - ctx
//   - contestID
func (_e *MockContestStore_Expecter) GetUserContestResult(ctx interface{}, contestID interface{}) *MockContestStore_GetUserContestResult_Call {
	return &MockContestStore_GetUserContestResult_Call{Call: _e.mock.On("GetUserContestResult", ctx, contestID)}
}

func (_c *MockContestStore_GetUserContestResult_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID)) *MockContestStore_GetUserContestResult_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockContestStore_GetUserContestResult_Call) Return(userContestResult *models.UserContestResult, err error) *MockContestStore_GetUserContestResult_Call {
	_c.Call.Return(userContestResult, err)
	return _c
}

func (_c *MockContestStore_GetUserContestResult_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID) (*models.UserContestResult, error)) *MockContestStore_GetUserContestResult_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserContestSubmissions provides a mock function for the type MockContestStore
func (_mock *MockContestStore) GetUserContestSubmissions(ctx context.Context, userID *primitive.ObjectID, contestID primitive.ObjectID) (*models.UserContestSubmissions, error) {
	ret := _mock.Called(ctx, userID, contestID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserContestSubmissions")
	}

	var r0 *models.UserContestSubmissions
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID, primitive.ObjectID) (*models.UserContestSubmissions, error)); ok {
		return returnFunc(ctx, userID, contestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID, primitive.ObjectID) *models.UserContestSubmissions); ok {
		r0 = returnFunc(ctx, userID, contestID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserContestSubmissions)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID, contestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestStore_GetUserContestSubmissions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserContestSubmissions'
type MockContestStore_GetUserContestSubmissions_Call struct {
	*mock.Call
}

// GetUserContestSubmissions is a helper method to define mock.On call
//   - ctx
//   - userID
//   - contestID
func (_e *MockContestStore_Expecter) GetUserContestSubmissions(ctx interface{}, userID interface{}, contestID interface{}) *MockContestStore_GetUserContestSubmissions_Call {
	return &MockContestStore_GetUserContestSubmissions_Call{Call: _e.mock.On("GetUserContestSubmissions", ctx, userID, contestID)}
}

func (_c *MockContestStore_GetUserContestSubmissions_Call) Run(run func(ctx context.Context, userID *primitive.ObjectID, contestID primitive.ObjectID)) *MockContestStore_GetUserContestSubmissions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockContestStore_GetUserContestSubmissions_Call) Return(userContestSubmissions *models.UserContestSubmissions, err error) *MockContestStore_GetUserContestSubmissions_Call {
	_c.Call.Return(userContestSubmissions, err)
	return _c
}

func (_c *MockContestStore_GetUserContestSubmissions_Call) RunAndReturn(run func(ctx context.Context, userID *primitive.ObjectID, contestID primitive.ObjectID) (*models.UserContestSubmissions, error)) *MockContestStore_GetUserContestSubmissions_Call {
	_c.Call.Return(run)
	return _c
}

// JoinVirtualContest provides a mock function for the type MockContestStore
func (_mock *MockContestStore) JoinVirtualContest(ctx context.Context, contestID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, contestID)

	if len(ret) == 0 {
		panic("no return value specified for JoinVirtualContest")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, contestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, contestID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, contestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestStore_JoinVirtualContest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'JoinVirtualContest'
type MockContestStore_JoinVirtualContest_Call struct {
	*mock.Call
}

// JoinVirtualContest is a helper method to define mock.On call
//   - ctx
//   - contestID
func (_e *MockContestStore_Expecter) JoinVirtualContest(ctx interface{}, contestID interface{}) *MockContestStore_JoinVirtualContest_Call {
	return &MockContestStore_JoinVirtualContest_Call{Call: _e.mock.On("JoinVirtualContest", ctx, contestID)}
}

func (_c *MockContestStore_JoinVirtualContest_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID)) *MockContestStore_JoinVirtualContest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockContestStore_JoinVirtualContest_Call) Return(b bool, err error) *MockContestStore_JoinVirtualContest_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockContestStore_JoinVirtualContest_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID) (bool, error)) *MockContestStore_JoinVirtualContest_Call {
	_c.Call.Return(run)
	return _c
}

// RegisterForContest provides a mock function for the type MockContestStore
func (_mock *MockContestStore) RegisterForContest(ctx context.Context, input models.RegistrationFormValuesInput) (bool, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for RegisterForContest")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.RegistrationFormValuesInput) (bool, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.RegistrationFormValuesInput) bool); ok {
		r0 = returnFunc(ctx, input)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.RegistrationFormValuesInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestStore_RegisterForContest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RegisterForContest'
type MockContestStore_RegisterForContest_Call struct {
	*mock.Call
}

// RegisterForContest is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockContestStore_Expecter) RegisterForContest(ctx interface{}, input interface{}) *MockContestStore_RegisterForContest_Call {
	return &MockContestStore_RegisterForContest_Call{Call: _e.mock.On("RegisterForContest", ctx, input)}
}

func (_c *MockContestStore_RegisterForContest_Call) Run(run func(ctx context.Context, input models.RegistrationFormValuesInput)) *MockContestStore_RegisterForContest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.RegistrationFormValuesInput))
	})
	return _c
}

func (_c *MockContestStore_RegisterForContest_Call) Return(b bool, err error) *MockContestStore_RegisterForContest_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockContestStore_RegisterForContest_Call) RunAndReturn(run func(ctx context.Context, input models.RegistrationFormValuesInput) (bool, error)) *MockContestStore_RegisterForContest_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitContestAnswer provides a mock function for the type MockContestStore
func (_mock *MockContestStore) SubmitContestAnswer(ctx context.Context, contestID primitive.ObjectID, questionID string, answer string) (bool, error) {
	ret := _mock.Called(ctx, contestID, questionID, answer)

	if len(ret) == 0 {
		panic("no return value specified for SubmitContestAnswer")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, string, string) (bool, error)); ok {
		return returnFunc(ctx, contestID, questionID, answer)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, string, string) bool); ok {
		r0 = returnFunc(ctx, contestID, questionID, answer)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, string, string) error); ok {
		r1 = returnFunc(ctx, contestID, questionID, answer)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestStore_SubmitContestAnswer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitContestAnswer'
type MockContestStore_SubmitContestAnswer_Call struct {
	*mock.Call
}

// SubmitContestAnswer is a helper method to define mock.On call
//   - ctx
//   - contestID
//   - questionID
//   - answer
func (_e *MockContestStore_Expecter) SubmitContestAnswer(ctx interface{}, contestID interface{}, questionID interface{}, answer interface{}) *MockContestStore_SubmitContestAnswer_Call {
	return &MockContestStore_SubmitContestAnswer_Call{Call: _e.mock.On("SubmitContestAnswer", ctx, contestID, questionID, answer)}
}

func (_c *MockContestStore_SubmitContestAnswer_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID, questionID string, answer string)) *MockContestStore_SubmitContestAnswer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockContestStore_SubmitContestAnswer_Call) Return(b bool, err error) *MockContestStore_SubmitContestAnswer_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockContestStore_SubmitContestAnswer_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID, questionID string, answer string) (bool, error)) *MockContestStore_SubmitContestAnswer_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitVirtualContestAnswer provides a mock function for the type MockContestStore
func (_mock *MockContestStore) SubmitVirtualContestAnswer(ctx context.Context, contestID primitive.ObjectID, questionID string, answer string) (bool, error) {
	ret := _mock.Called(ctx, contestID, questionID, answer)

	if len(ret) == 0 {
		panic("no return value specified for SubmitVirtualContestAnswer")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, string, string) (bool, error)); ok {
		return returnFunc(ctx, contestID, questionID, answer)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, string, string) bool); ok {
		r0 = returnFunc(ctx, contestID, questionID, answer)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, string, string) error); ok {
		r1 = returnFunc(ctx, contestID, questionID, answer)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestStore_SubmitVirtualContestAnswer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitVirtualContestAnswer'
type MockContestStore_SubmitVirtualContestAnswer_Call struct {
	*mock.Call
}

// SubmitVirtualContestAnswer is a helper method to define mock.On call
//   - ctx
//   - contestID
//   - questionID
//   - answer
func (_e *MockContestStore_Expecter) SubmitVirtualContestAnswer(ctx interface{}, contestID interface{}, questionID interface{}, answer interface{}) *MockContestStore_SubmitVirtualContestAnswer_Call {
	return &MockContestStore_SubmitVirtualContestAnswer_Call{Call: _e.mock.On("SubmitVirtualContestAnswer", ctx, contestID, questionID, answer)}
}

func (_c *MockContestStore_SubmitVirtualContestAnswer_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID, questionID string, answer string)) *MockContestStore_SubmitVirtualContestAnswer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockContestStore_SubmitVirtualContestAnswer_Call) Return(b bool, err error) *MockContestStore_SubmitVirtualContestAnswer_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockContestStore_SubmitVirtualContestAnswer_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID, questionID string, answer string) (bool, error)) *MockContestStore_SubmitVirtualContestAnswer_Call {
	_c.Call.Return(run)
	return _c
}

// UnregisterFromContest provides a mock function for the type MockContestStore
func (_mock *MockContestStore) UnregisterFromContest(ctx context.Context, contestID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, contestID)

	if len(ret) == 0 {
		panic("no return value specified for UnregisterFromContest")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, contestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, contestID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, contestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestStore_UnregisterFromContest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UnregisterFromContest'
type MockContestStore_UnregisterFromContest_Call struct {
	*mock.Call
}

// UnregisterFromContest is a helper method to define mock.On call
//   - ctx
//   - contestID
func (_e *MockContestStore_Expecter) UnregisterFromContest(ctx interface{}, contestID interface{}) *MockContestStore_UnregisterFromContest_Call {
	return &MockContestStore_UnregisterFromContest_Call{Call: _e.mock.On("UnregisterFromContest", ctx, contestID)}
}

func (_c *MockContestStore_UnregisterFromContest_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID)) *MockContestStore_UnregisterFromContest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockContestStore_UnregisterFromContest_Call) Return(b bool, err error) *MockContestStore_UnregisterFromContest_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockContestStore_UnregisterFromContest_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID) (bool, error)) *MockContestStore_UnregisterFromContest_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateContestParticipantStartTime provides a mock function for the type MockContestStore
func (_mock *MockContestStore) UpdateContestParticipantStartTime(ctx context.Context, contestID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, contestID)

	if len(ret) == 0 {
		panic("no return value specified for UpdateContestParticipantStartTime")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, contestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, contestID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, contestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestStore_UpdateContestParticipantStartTime_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateContestParticipantStartTime'
type MockContestStore_UpdateContestParticipantStartTime_Call struct {
	*mock.Call
}

// UpdateContestParticipantStartTime is a helper method to define mock.On call
//   - ctx
//   - contestID
func (_e *MockContestStore_Expecter) UpdateContestParticipantStartTime(ctx interface{}, contestID interface{}) *MockContestStore_UpdateContestParticipantStartTime_Call {
	return &MockContestStore_UpdateContestParticipantStartTime_Call{Call: _e.mock.On("UpdateContestParticipantStartTime", ctx, contestID)}
}

func (_c *MockContestStore_UpdateContestParticipantStartTime_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID)) *MockContestStore_UpdateContestParticipantStartTime_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockContestStore_UpdateContestParticipantStartTime_Call) Return(b bool, err error) *MockContestStore_UpdateContestParticipantStartTime_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockContestStore_UpdateContestParticipantStartTime_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID) (bool, error)) *MockContestStore_UpdateContestParticipantStartTime_Call {
	_c.Call.Return(run)
	return _c
}
