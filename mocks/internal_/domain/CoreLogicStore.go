// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockCoreLogicStore creates a new instance of MockCoreLogicStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockCoreLogicStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockCoreLogicStore {
	mock := &MockCoreLogicStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockCoreLogicStore is an autogenerated mock type for the CoreLogicStore type
type MockCoreLogicStore struct {
	mock.Mock
}

type MockCoreLogicStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockCoreLogicStore) EXPECT() *MockCoreLogicStore_Expecter {
	return &MockCoreLogicStore_Expecter{mock: &_m.Mock}
}

// GetUserByID provides a mock function for the type MockCoreLogicStore
func (_mock *MockCoreLogicStore) GetUserByID(ctx context.Context, userId primitive.ObjectID) (*models.User, error) {
	ret := _mock.Called(ctx, userId)

	if len(ret) == 0 {
		panic("no return value specified for GetUserByID")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.User, error)); ok {
		return returnFunc(ctx, userId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.User); ok {
		r0 = returnFunc(ctx, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCoreLogicStore_GetUserByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserByID'
type MockCoreLogicStore_GetUserByID_Call struct {
	*mock.Call
}

// GetUserByID is a helper method to define mock.On call
//   - ctx
//   - userId
func (_e *MockCoreLogicStore_Expecter) GetUserByID(ctx interface{}, userId interface{}) *MockCoreLogicStore_GetUserByID_Call {
	return &MockCoreLogicStore_GetUserByID_Call{Call: _e.mock.On("GetUserByID", ctx, userId)}
}

func (_c *MockCoreLogicStore_GetUserByID_Call) Run(run func(ctx context.Context, userId primitive.ObjectID)) *MockCoreLogicStore_GetUserByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockCoreLogicStore_GetUserByID_Call) Return(user *models.User, err error) *MockCoreLogicStore_GetUserByID_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockCoreLogicStore_GetUserByID_Call) RunAndReturn(run func(ctx context.Context, userId primitive.ObjectID) (*models.User, error)) *MockCoreLogicStore_GetUserByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetUsersByIDs provides a mock function for the type MockCoreLogicStore
func (_mock *MockCoreLogicStore) GetUsersByIDs(ctx context.Context, userIds []primitive.ObjectID) ([]*models.User, error) {
	ret := _mock.Called(ctx, userIds)

	if len(ret) == 0 {
		panic("no return value specified for GetUsersByIDs")
	}

	var r0 []*models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []primitive.ObjectID) ([]*models.User, error)); ok {
		return returnFunc(ctx, userIds)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []primitive.ObjectID) []*models.User); ok {
		r0 = returnFunc(ctx, userIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userIds)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCoreLogicStore_GetUsersByIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUsersByIDs'
type MockCoreLogicStore_GetUsersByIDs_Call struct {
	*mock.Call
}

// GetUsersByIDs is a helper method to define mock.On call
//   - ctx
//   - userIds
func (_e *MockCoreLogicStore_Expecter) GetUsersByIDs(ctx interface{}, userIds interface{}) *MockCoreLogicStore_GetUsersByIDs_Call {
	return &MockCoreLogicStore_GetUsersByIDs_Call{Call: _e.mock.On("GetUsersByIDs", ctx, userIds)}
}

func (_c *MockCoreLogicStore_GetUsersByIDs_Call) Run(run func(ctx context.Context, userIds []primitive.ObjectID)) *MockCoreLogicStore_GetUsersByIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]primitive.ObjectID))
	})
	return _c
}

func (_c *MockCoreLogicStore_GetUsersByIDs_Call) Return(users []*models.User, err error) *MockCoreLogicStore_GetUsersByIDs_Call {
	_c.Call.Return(users, err)
	return _c
}

func (_c *MockCoreLogicStore_GetUsersByIDs_Call) RunAndReturn(run func(ctx context.Context, userIds []primitive.ObjectID) ([]*models.User, error)) *MockCoreLogicStore_GetUsersByIDs_Call {
	_c.Call.Return(run)
	return _c
}

// PublishUserEvent provides a mock function for the type MockCoreLogicStore
func (_mock *MockCoreLogicStore) PublishUserEvent(ctx context.Context, userID primitive.ObjectID, event models.UserEvent) error {
	ret := _mock.Called(ctx, userID, event)

	if len(ret) == 0 {
		panic("no return value specified for PublishUserEvent")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.UserEvent) error); ok {
		r0 = returnFunc(ctx, userID, event)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockCoreLogicStore_PublishUserEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PublishUserEvent'
type MockCoreLogicStore_PublishUserEvent_Call struct {
	*mock.Call
}

// PublishUserEvent is a helper method to define mock.On call
//   - ctx
//   - userID
//   - event
func (_e *MockCoreLogicStore_Expecter) PublishUserEvent(ctx interface{}, userID interface{}, event interface{}) *MockCoreLogicStore_PublishUserEvent_Call {
	return &MockCoreLogicStore_PublishUserEvent_Call{Call: _e.mock.On("PublishUserEvent", ctx, userID, event)}
}

func (_c *MockCoreLogicStore_PublishUserEvent_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, event models.UserEvent)) *MockCoreLogicStore_PublishUserEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(models.UserEvent))
	})
	return _c
}

func (_c *MockCoreLogicStore_PublishUserEvent_Call) Return(err error) *MockCoreLogicStore_PublishUserEvent_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockCoreLogicStore_PublishUserEvent_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, event models.UserEvent) error) *MockCoreLogicStore_PublishUserEvent_Call {
	_c.Call.Return(run)
	return _c
}
