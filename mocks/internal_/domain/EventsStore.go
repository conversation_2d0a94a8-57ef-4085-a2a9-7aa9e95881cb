// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/infrastructure/websocket"

	mock "github.com/stretchr/testify/mock"
)

// NewMockEventsStore creates a new instance of MockEventsStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockEventsStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockEventsStore {
	mock := &MockEventsStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockEventsStore is an autogenerated mock type for the EventsStore type
type MockEventsStore struct {
	mock.Mock
}

type MockEventsStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockEventsStore) EXPECT() *MockEventsStore_Expecter {
	return &MockEventsStore_Expecter{mock: &_m.Mock}
}

// AddMessage provides a mock function for the type MockEventsStore
func (_mock *MockEventsStore) AddMessage(ctx context.Context, client *websocket.Client, msg websocket.Message) error {
	ret := _mock.Called(ctx, client, msg)

	if len(ret) == 0 {
		panic("no return value specified for AddMessage")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *websocket.Client, websocket.Message) error); ok {
		r0 = returnFunc(ctx, client, msg)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockEventsStore_AddMessage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddMessage'
type MockEventsStore_AddMessage_Call struct {
	*mock.Call
}

// AddMessage is a helper method to define mock.On call
//   - ctx
//   - client
//   - msg
func (_e *MockEventsStore_Expecter) AddMessage(ctx interface{}, client interface{}, msg interface{}) *MockEventsStore_AddMessage_Call {
	return &MockEventsStore_AddMessage_Call{Call: _e.mock.On("AddMessage", ctx, client, msg)}
}

func (_c *MockEventsStore_AddMessage_Call) Run(run func(ctx context.Context, client *websocket.Client, msg websocket.Message)) *MockEventsStore_AddMessage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*websocket.Client), args[2].(websocket.Message))
	})
	return _c
}

func (_c *MockEventsStore_AddMessage_Call) Return(err error) *MockEventsStore_AddMessage_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockEventsStore_AddMessage_Call) RunAndReturn(run func(ctx context.Context, client *websocket.Client, msg websocket.Message) error) *MockEventsStore_AddMessage_Call {
	_c.Call.Return(run)
	return _c
}

// PingPong provides a mock function for the type MockEventsStore
func (_mock *MockEventsStore) PingPong(ctx context.Context, client *websocket.Client, msg websocket.Message) error {
	ret := _mock.Called(ctx, client, msg)

	if len(ret) == 0 {
		panic("no return value specified for PingPong")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *websocket.Client, websocket.Message) error); ok {
		r0 = returnFunc(ctx, client, msg)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockEventsStore_PingPong_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PingPong'
type MockEventsStore_PingPong_Call struct {
	*mock.Call
}

// PingPong is a helper method to define mock.On call
//   - ctx
//   - client
//   - msg
func (_e *MockEventsStore_Expecter) PingPong(ctx interface{}, client interface{}, msg interface{}) *MockEventsStore_PingPong_Call {
	return &MockEventsStore_PingPong_Call{Call: _e.mock.On("PingPong", ctx, client, msg)}
}

func (_c *MockEventsStore_PingPong_Call) Run(run func(ctx context.Context, client *websocket.Client, msg websocket.Message)) *MockEventsStore_PingPong_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*websocket.Client), args[2].(websocket.Message))
	})
	return _c
}

func (_c *MockEventsStore_PingPong_Call) Return(err error) *MockEventsStore_PingPong_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockEventsStore_PingPong_Call) RunAndReturn(run func(ctx context.Context, client *websocket.Client, msg websocket.Message) error) *MockEventsStore_PingPong_Call {
	_c.Call.Return(run)
	return _c
}

// SendMessage provides a mock function for the type MockEventsStore
func (_mock *MockEventsStore) SendMessage(ctx context.Context, client *websocket.Client, msg websocket.Message) error {
	ret := _mock.Called(ctx, client, msg)

	if len(ret) == 0 {
		panic("no return value specified for SendMessage")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *websocket.Client, websocket.Message) error); ok {
		r0 = returnFunc(ctx, client, msg)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockEventsStore_SendMessage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendMessage'
type MockEventsStore_SendMessage_Call struct {
	*mock.Call
}

// SendMessage is a helper method to define mock.On call
//   - ctx
//   - client
//   - msg
func (_e *MockEventsStore_Expecter) SendMessage(ctx interface{}, client interface{}, msg interface{}) *MockEventsStore_SendMessage_Call {
	return &MockEventsStore_SendMessage_Call{Call: _e.mock.On("SendMessage", ctx, client, msg)}
}

func (_c *MockEventsStore_SendMessage_Call) Run(run func(ctx context.Context, client *websocket.Client, msg websocket.Message)) *MockEventsStore_SendMessage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*websocket.Client), args[2].(websocket.Message))
	})
	return _c
}

func (_c *MockEventsStore_SendMessage_Call) Return(err error) *MockEventsStore_SendMessage_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockEventsStore_SendMessage_Call) RunAndReturn(run func(ctx context.Context, client *websocket.Client, msg websocket.Message) error) *MockEventsStore_SendMessage_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitAnswer provides a mock function for the type MockEventsStore
func (_mock *MockEventsStore) SubmitAnswer(ctx context.Context, client *websocket.Client, msg websocket.Message) error {
	ret := _mock.Called(ctx, client, msg)

	if len(ret) == 0 {
		panic("no return value specified for SubmitAnswer")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *websocket.Client, websocket.Message) error); ok {
		r0 = returnFunc(ctx, client, msg)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockEventsStore_SubmitAnswer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitAnswer'
type MockEventsStore_SubmitAnswer_Call struct {
	*mock.Call
}

// SubmitAnswer is a helper method to define mock.On call
//   - ctx
//   - client
//   - msg
func (_e *MockEventsStore_Expecter) SubmitAnswer(ctx interface{}, client interface{}, msg interface{}) *MockEventsStore_SubmitAnswer_Call {
	return &MockEventsStore_SubmitAnswer_Call{Call: _e.mock.On("SubmitAnswer", ctx, client, msg)}
}

func (_c *MockEventsStore_SubmitAnswer_Call) Run(run func(ctx context.Context, client *websocket.Client, msg websocket.Message)) *MockEventsStore_SubmitAnswer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*websocket.Client), args[2].(websocket.Message))
	})
	return _c
}

func (_c *MockEventsStore_SubmitAnswer_Call) Return(err error) *MockEventsStore_SubmitAnswer_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockEventsStore_SubmitAnswer_Call) RunAndReturn(run func(ctx context.Context, client *websocket.Client, msg websocket.Message) error) *MockEventsStore_SubmitAnswer_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitPuzzleGameAnswer provides a mock function for the type MockEventsStore
func (_mock *MockEventsStore) SubmitPuzzleGameAnswer(ctx context.Context, client *websocket.Client, msg websocket.Message) error {
	ret := _mock.Called(ctx, client, msg)

	if len(ret) == 0 {
		panic("no return value specified for SubmitPuzzleGameAnswer")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *websocket.Client, websocket.Message) error); ok {
		r0 = returnFunc(ctx, client, msg)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockEventsStore_SubmitPuzzleGameAnswer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitPuzzleGameAnswer'
type MockEventsStore_SubmitPuzzleGameAnswer_Call struct {
	*mock.Call
}

// SubmitPuzzleGameAnswer is a helper method to define mock.On call
//   - ctx
//   - client
//   - msg
func (_e *MockEventsStore_Expecter) SubmitPuzzleGameAnswer(ctx interface{}, client interface{}, msg interface{}) *MockEventsStore_SubmitPuzzleGameAnswer_Call {
	return &MockEventsStore_SubmitPuzzleGameAnswer_Call{Call: _e.mock.On("SubmitPuzzleGameAnswer", ctx, client, msg)}
}

func (_c *MockEventsStore_SubmitPuzzleGameAnswer_Call) Run(run func(ctx context.Context, client *websocket.Client, msg websocket.Message)) *MockEventsStore_SubmitPuzzleGameAnswer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*websocket.Client), args[2].(websocket.Message))
	})
	return _c
}

func (_c *MockEventsStore_SubmitPuzzleGameAnswer_Call) Return(err error) *MockEventsStore_SubmitPuzzleGameAnswer_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockEventsStore_SubmitPuzzleGameAnswer_Call) RunAndReturn(run func(ctx context.Context, client *websocket.Client, msg websocket.Message) error) *MockEventsStore_SubmitPuzzleGameAnswer_Call {
	_c.Call.Return(run)
	return _c
}
