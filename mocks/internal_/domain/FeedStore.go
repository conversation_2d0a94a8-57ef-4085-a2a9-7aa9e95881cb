// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockFeedStore creates a new instance of MockFeedStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFeedStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFeedStore {
	mock := &MockFeedStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockFeedStore is an autogenerated mock type for the FeedStore type
type MockFeedStore struct {
	mock.Mock
}

type MockFeedStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFeedStore) EXPECT() *MockFeedStore_Expecter {
	return &MockFeedStore_Expecter{mock: &_m.<PERSON>ck}
}

// AddFeed provides a mock function for the type MockFeedStore
func (_mock *MockFeedStore) AddFeed(ctx context.Context, notification *models.UserNotification) error {
	ret := _mock.Called(ctx, notification)

	if len(ret) == 0 {
		panic("no return value specified for AddFeed")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserNotification) error); ok {
		r0 = returnFunc(ctx, notification)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockFeedStore_AddFeed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddFeed'
type MockFeedStore_AddFeed_Call struct {
	*mock.Call
}

// AddFeed is a helper method to define mock.On call
//   - ctx
//   - notification
func (_e *MockFeedStore_Expecter) AddFeed(ctx interface{}, notification interface{}) *MockFeedStore_AddFeed_Call {
	return &MockFeedStore_AddFeed_Call{Call: _e.mock.On("AddFeed", ctx, notification)}
}

func (_c *MockFeedStore_AddFeed_Call) Run(run func(ctx context.Context, notification *models.UserNotification)) *MockFeedStore_AddFeed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.UserNotification))
	})
	return _c
}

func (_c *MockFeedStore_AddFeed_Call) Return(err error) *MockFeedStore_AddFeed_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockFeedStore_AddFeed_Call) RunAndReturn(run func(ctx context.Context, notification *models.UserNotification) error) *MockFeedStore_AddFeed_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserFeed provides a mock function for the type MockFeedStore
func (_mock *MockFeedStore) GetUserFeed(ctx context.Context, lastId *primitive.ObjectID, pageSize *int) (*models.FeedResponse, error) {
	ret := _mock.Called(ctx, lastId, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetUserFeed")
	}

	var r0 *models.FeedResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID, *int) (*models.FeedResponse, error)); ok {
		return returnFunc(ctx, lastId, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID, *int) *models.FeedResponse); ok {
		r0 = returnFunc(ctx, lastId, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.FeedResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID, *int) error); ok {
		r1 = returnFunc(ctx, lastId, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFeedStore_GetUserFeed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserFeed'
type MockFeedStore_GetUserFeed_Call struct {
	*mock.Call
}

// GetUserFeed is a helper method to define mock.On call
//   - ctx
//   - lastId
//   - pageSize
func (_e *MockFeedStore_Expecter) GetUserFeed(ctx interface{}, lastId interface{}, pageSize interface{}) *MockFeedStore_GetUserFeed_Call {
	return &MockFeedStore_GetUserFeed_Call{Call: _e.mock.On("GetUserFeed", ctx, lastId, pageSize)}
}

func (_c *MockFeedStore_GetUserFeed_Call) Run(run func(ctx context.Context, lastId *primitive.ObjectID, pageSize *int)) *MockFeedStore_GetUserFeed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*primitive.ObjectID), args[2].(*int))
	})
	return _c
}

func (_c *MockFeedStore_GetUserFeed_Call) Return(feedResponse *models.FeedResponse, err error) *MockFeedStore_GetUserFeed_Call {
	_c.Call.Return(feedResponse, err)
	return _c
}

func (_c *MockFeedStore_GetUserFeed_Call) RunAndReturn(run func(ctx context.Context, lastId *primitive.ObjectID, pageSize *int) (*models.FeedResponse, error)) *MockFeedStore_GetUserFeed_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateLastReadFeedId provides a mock function for the type MockFeedStore
func (_mock *MockFeedStore) UpdateLastReadFeedId(ctx context.Context, lastReadFeedID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, lastReadFeedID)

	if len(ret) == 0 {
		panic("no return value specified for UpdateLastReadFeedId")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, lastReadFeedID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, lastReadFeedID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, lastReadFeedID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFeedStore_UpdateLastReadFeedId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateLastReadFeedId'
type MockFeedStore_UpdateLastReadFeedId_Call struct {
	*mock.Call
}

// UpdateLastReadFeedId is a helper method to define mock.On call
//   - ctx
//   - lastReadFeedID
func (_e *MockFeedStore_Expecter) UpdateLastReadFeedId(ctx interface{}, lastReadFeedID interface{}) *MockFeedStore_UpdateLastReadFeedId_Call {
	return &MockFeedStore_UpdateLastReadFeedId_Call{Call: _e.mock.On("UpdateLastReadFeedId", ctx, lastReadFeedID)}
}

func (_c *MockFeedStore_UpdateLastReadFeedId_Call) Run(run func(ctx context.Context, lastReadFeedID primitive.ObjectID)) *MockFeedStore_UpdateLastReadFeedId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockFeedStore_UpdateLastReadFeedId_Call) Return(b bool, err error) *MockFeedStore_UpdateLastReadFeedId_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockFeedStore_UpdateLastReadFeedId_Call) RunAndReturn(run func(ctx context.Context, lastReadFeedID primitive.ObjectID) (bool, error)) *MockFeedStore_UpdateLastReadFeedId_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateLikeStatus provides a mock function for the type MockFeedStore
func (_mock *MockFeedStore) UpdateLikeStatus(ctx context.Context, feedID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, feedID)

	if len(ret) == 0 {
		panic("no return value specified for UpdateLikeStatus")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, feedID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, feedID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, feedID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFeedStore_UpdateLikeStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateLikeStatus'
type MockFeedStore_UpdateLikeStatus_Call struct {
	*mock.Call
}

// UpdateLikeStatus is a helper method to define mock.On call
//   - ctx
//   - feedID
func (_e *MockFeedStore_Expecter) UpdateLikeStatus(ctx interface{}, feedID interface{}) *MockFeedStore_UpdateLikeStatus_Call {
	return &MockFeedStore_UpdateLikeStatus_Call{Call: _e.mock.On("UpdateLikeStatus", ctx, feedID)}
}

func (_c *MockFeedStore_UpdateLikeStatus_Call) Run(run func(ctx context.Context, feedID primitive.ObjectID)) *MockFeedStore_UpdateLikeStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockFeedStore_UpdateLikeStatus_Call) Return(b bool, err error) *MockFeedStore_UpdateLikeStatus_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockFeedStore_UpdateLikeStatus_Call) RunAndReturn(run func(ctx context.Context, feedID primitive.ObjectID) (bool, error)) *MockFeedStore_UpdateLikeStatus_Call {
	_c.Call.Return(run)
	return _c
}
