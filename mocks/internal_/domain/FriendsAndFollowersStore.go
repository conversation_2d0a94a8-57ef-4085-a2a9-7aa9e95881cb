// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
)

// NewMockFriendsAndFollowersStore creates a new instance of MockFriendsAndFollowersStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFriendsAndFollowersStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFriendsAndFollowersStore {
	mock := &MockFriendsAndFollowersStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockFriendsAndFollowersStore is an autogenerated mock type for the FriendsAndFollowersStore type
type MockFriendsAndFollowersStore struct {
	mock.Mock
}

type MockFriendsAndFollowersStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFriendsAndFollowersStore) EXPECT() *MockFriendsAndFollowersStore_Expecter {
	return &MockFriendsAndFollowersStore_Expecter{mock: &_m.Mock}
}

// AcceptFriendRequest provides a mock function for the type MockFriendsAndFollowersStore
func (_mock *MockFriendsAndFollowersStore) AcceptFriendRequest(ctx context.Context, acceptRequestInput *models.FriendRequestInput) (bool, error) {
	ret := _mock.Called(ctx, acceptRequestInput)

	if len(ret) == 0 {
		panic("no return value specified for AcceptFriendRequest")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.FriendRequestInput) (bool, error)); ok {
		return returnFunc(ctx, acceptRequestInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.FriendRequestInput) bool); ok {
		r0 = returnFunc(ctx, acceptRequestInput)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.FriendRequestInput) error); ok {
		r1 = returnFunc(ctx, acceptRequestInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersStore_AcceptFriendRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcceptFriendRequest'
type MockFriendsAndFollowersStore_AcceptFriendRequest_Call struct {
	*mock.Call
}

// AcceptFriendRequest is a helper method to define mock.On call
//   - ctx
//   - acceptRequestInput
func (_e *MockFriendsAndFollowersStore_Expecter) AcceptFriendRequest(ctx interface{}, acceptRequestInput interface{}) *MockFriendsAndFollowersStore_AcceptFriendRequest_Call {
	return &MockFriendsAndFollowersStore_AcceptFriendRequest_Call{Call: _e.mock.On("AcceptFriendRequest", ctx, acceptRequestInput)}
}

func (_c *MockFriendsAndFollowersStore_AcceptFriendRequest_Call) Run(run func(ctx context.Context, acceptRequestInput *models.FriendRequestInput)) *MockFriendsAndFollowersStore_AcceptFriendRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.FriendRequestInput))
	})
	return _c
}

func (_c *MockFriendsAndFollowersStore_AcceptFriendRequest_Call) Return(b bool, err error) *MockFriendsAndFollowersStore_AcceptFriendRequest_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockFriendsAndFollowersStore_AcceptFriendRequest_Call) RunAndReturn(run func(ctx context.Context, acceptRequestInput *models.FriendRequestInput) (bool, error)) *MockFriendsAndFollowersStore_AcceptFriendRequest_Call {
	_c.Call.Return(run)
	return _c
}

// FollowUser provides a mock function for the type MockFriendsAndFollowersStore
func (_mock *MockFriendsAndFollowersStore) FollowUser(ctx context.Context, followUserInput *models.FollowUserInput) (bool, error) {
	ret := _mock.Called(ctx, followUserInput)

	if len(ret) == 0 {
		panic("no return value specified for FollowUser")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.FollowUserInput) (bool, error)); ok {
		return returnFunc(ctx, followUserInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.FollowUserInput) bool); ok {
		r0 = returnFunc(ctx, followUserInput)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.FollowUserInput) error); ok {
		r1 = returnFunc(ctx, followUserInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersStore_FollowUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FollowUser'
type MockFriendsAndFollowersStore_FollowUser_Call struct {
	*mock.Call
}

// FollowUser is a helper method to define mock.On call
//   - ctx
//   - followUserInput
func (_e *MockFriendsAndFollowersStore_Expecter) FollowUser(ctx interface{}, followUserInput interface{}) *MockFriendsAndFollowersStore_FollowUser_Call {
	return &MockFriendsAndFollowersStore_FollowUser_Call{Call: _e.mock.On("FollowUser", ctx, followUserInput)}
}

func (_c *MockFriendsAndFollowersStore_FollowUser_Call) Run(run func(ctx context.Context, followUserInput *models.FollowUserInput)) *MockFriendsAndFollowersStore_FollowUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.FollowUserInput))
	})
	return _c
}

func (_c *MockFriendsAndFollowersStore_FollowUser_Call) Return(b bool, err error) *MockFriendsAndFollowersStore_FollowUser_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockFriendsAndFollowersStore_FollowUser_Call) RunAndReturn(run func(ctx context.Context, followUserInput *models.FollowUserInput) (bool, error)) *MockFriendsAndFollowersStore_FollowUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetFollowers provides a mock function for the type MockFriendsAndFollowersStore
func (_mock *MockFriendsAndFollowersStore) GetFollowers(ctx context.Context, page *int, pageSize *int) (*models.FollowersAndFolloweePage, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetFollowers")
	}

	var r0 *models.FollowersAndFolloweePage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) (*models.FollowersAndFolloweePage, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) *models.FollowersAndFolloweePage); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.FollowersAndFolloweePage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersStore_GetFollowers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFollowers'
type MockFriendsAndFollowersStore_GetFollowers_Call struct {
	*mock.Call
}

// GetFollowers is a helper method to define mock.On call
//   - ctx
//   - page
//   - pageSize
func (_e *MockFriendsAndFollowersStore_Expecter) GetFollowers(ctx interface{}, page interface{}, pageSize interface{}) *MockFriendsAndFollowersStore_GetFollowers_Call {
	return &MockFriendsAndFollowersStore_GetFollowers_Call{Call: _e.mock.On("GetFollowers", ctx, page, pageSize)}
}

func (_c *MockFriendsAndFollowersStore_GetFollowers_Call) Run(run func(ctx context.Context, page *int, pageSize *int)) *MockFriendsAndFollowersStore_GetFollowers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*int), args[2].(*int))
	})
	return _c
}

func (_c *MockFriendsAndFollowersStore_GetFollowers_Call) Return(followersAndFolloweePage *models.FollowersAndFolloweePage, err error) *MockFriendsAndFollowersStore_GetFollowers_Call {
	_c.Call.Return(followersAndFolloweePage, err)
	return _c
}

func (_c *MockFriendsAndFollowersStore_GetFollowers_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int) (*models.FollowersAndFolloweePage, error)) *MockFriendsAndFollowersStore_GetFollowers_Call {
	_c.Call.Return(run)
	return _c
}

// GetFollowings provides a mock function for the type MockFriendsAndFollowersStore
func (_mock *MockFriendsAndFollowersStore) GetFollowings(ctx context.Context, page *int, pageSize *int) (*models.FollowersAndFolloweePage, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetFollowings")
	}

	var r0 *models.FollowersAndFolloweePage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) (*models.FollowersAndFolloweePage, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) *models.FollowersAndFolloweePage); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.FollowersAndFolloweePage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersStore_GetFollowings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFollowings'
type MockFriendsAndFollowersStore_GetFollowings_Call struct {
	*mock.Call
}

// GetFollowings is a helper method to define mock.On call
//   - ctx
//   - page
//   - pageSize
func (_e *MockFriendsAndFollowersStore_Expecter) GetFollowings(ctx interface{}, page interface{}, pageSize interface{}) *MockFriendsAndFollowersStore_GetFollowings_Call {
	return &MockFriendsAndFollowersStore_GetFollowings_Call{Call: _e.mock.On("GetFollowings", ctx, page, pageSize)}
}

func (_c *MockFriendsAndFollowersStore_GetFollowings_Call) Run(run func(ctx context.Context, page *int, pageSize *int)) *MockFriendsAndFollowersStore_GetFollowings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*int), args[2].(*int))
	})
	return _c
}

func (_c *MockFriendsAndFollowersStore_GetFollowings_Call) Return(followersAndFolloweePage *models.FollowersAndFolloweePage, err error) *MockFriendsAndFollowersStore_GetFollowings_Call {
	_c.Call.Return(followersAndFolloweePage, err)
	return _c
}

func (_c *MockFriendsAndFollowersStore_GetFollowings_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int) (*models.FollowersAndFolloweePage, error)) *MockFriendsAndFollowersStore_GetFollowings_Call {
	_c.Call.Return(run)
	return _c
}

// GetFriends provides a mock function for the type MockFriendsAndFollowersStore
func (_mock *MockFriendsAndFollowersStore) GetFriends(ctx context.Context, page *int, pageSize *int, sortOption *models.SortOptions) (*models.FriendsPage, error) {
	ret := _mock.Called(ctx, page, pageSize, sortOption)

	if len(ret) == 0 {
		panic("no return value specified for GetFriends")
	}

	var r0 *models.FriendsPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *models.SortOptions) (*models.FriendsPage, error)); ok {
		return returnFunc(ctx, page, pageSize, sortOption)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *models.SortOptions) *models.FriendsPage); ok {
		r0 = returnFunc(ctx, page, pageSize, sortOption)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.FriendsPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int, *models.SortOptions) error); ok {
		r1 = returnFunc(ctx, page, pageSize, sortOption)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersStore_GetFriends_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFriends'
type MockFriendsAndFollowersStore_GetFriends_Call struct {
	*mock.Call
}

// GetFriends is a helper method to define mock.On call
//   - ctx
//   - page
//   - pageSize
//   - sortOption
func (_e *MockFriendsAndFollowersStore_Expecter) GetFriends(ctx interface{}, page interface{}, pageSize interface{}, sortOption interface{}) *MockFriendsAndFollowersStore_GetFriends_Call {
	return &MockFriendsAndFollowersStore_GetFriends_Call{Call: _e.mock.On("GetFriends", ctx, page, pageSize, sortOption)}
}

func (_c *MockFriendsAndFollowersStore_GetFriends_Call) Run(run func(ctx context.Context, page *int, pageSize *int, sortOption *models.SortOptions)) *MockFriendsAndFollowersStore_GetFriends_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*int), args[2].(*int), args[3].(*models.SortOptions))
	})
	return _c
}

func (_c *MockFriendsAndFollowersStore_GetFriends_Call) Return(friendsPage *models.FriendsPage, err error) *MockFriendsAndFollowersStore_GetFriends_Call {
	_c.Call.Return(friendsPage, err)
	return _c
}

func (_c *MockFriendsAndFollowersStore_GetFriends_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int, sortOption *models.SortOptions) (*models.FriendsPage, error)) *MockFriendsAndFollowersStore_GetFriends_Call {
	_c.Call.Return(run)
	return _c
}

// GetPendingFriendRequests provides a mock function for the type MockFriendsAndFollowersStore
func (_mock *MockFriendsAndFollowersStore) GetPendingFriendRequests(ctx context.Context, page *int, pageSize *int) (*models.FriendRequestPage, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetPendingFriendRequests")
	}

	var r0 *models.FriendRequestPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) (*models.FriendRequestPage, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) *models.FriendRequestPage); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.FriendRequestPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersStore_GetPendingFriendRequests_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPendingFriendRequests'
type MockFriendsAndFollowersStore_GetPendingFriendRequests_Call struct {
	*mock.Call
}

// GetPendingFriendRequests is a helper method to define mock.On call
//   - ctx
//   - page
//   - pageSize
func (_e *MockFriendsAndFollowersStore_Expecter) GetPendingFriendRequests(ctx interface{}, page interface{}, pageSize interface{}) *MockFriendsAndFollowersStore_GetPendingFriendRequests_Call {
	return &MockFriendsAndFollowersStore_GetPendingFriendRequests_Call{Call: _e.mock.On("GetPendingFriendRequests", ctx, page, pageSize)}
}

func (_c *MockFriendsAndFollowersStore_GetPendingFriendRequests_Call) Run(run func(ctx context.Context, page *int, pageSize *int)) *MockFriendsAndFollowersStore_GetPendingFriendRequests_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*int), args[2].(*int))
	})
	return _c
}

func (_c *MockFriendsAndFollowersStore_GetPendingFriendRequests_Call) Return(friendRequestPage *models.FriendRequestPage, err error) *MockFriendsAndFollowersStore_GetPendingFriendRequests_Call {
	_c.Call.Return(friendRequestPage, err)
	return _c
}

func (_c *MockFriendsAndFollowersStore_GetPendingFriendRequests_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int) (*models.FriendRequestPage, error)) *MockFriendsAndFollowersStore_GetPendingFriendRequests_Call {
	_c.Call.Return(run)
	return _c
}

// RejectFriendRequest provides a mock function for the type MockFriendsAndFollowersStore
func (_mock *MockFriendsAndFollowersStore) RejectFriendRequest(ctx context.Context, rejectRequestInput *models.FriendRequestInput) (bool, error) {
	ret := _mock.Called(ctx, rejectRequestInput)

	if len(ret) == 0 {
		panic("no return value specified for RejectFriendRequest")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.FriendRequestInput) (bool, error)); ok {
		return returnFunc(ctx, rejectRequestInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.FriendRequestInput) bool); ok {
		r0 = returnFunc(ctx, rejectRequestInput)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.FriendRequestInput) error); ok {
		r1 = returnFunc(ctx, rejectRequestInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersStore_RejectFriendRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RejectFriendRequest'
type MockFriendsAndFollowersStore_RejectFriendRequest_Call struct {
	*mock.Call
}

// RejectFriendRequest is a helper method to define mock.On call
//   - ctx
//   - rejectRequestInput
func (_e *MockFriendsAndFollowersStore_Expecter) RejectFriendRequest(ctx interface{}, rejectRequestInput interface{}) *MockFriendsAndFollowersStore_RejectFriendRequest_Call {
	return &MockFriendsAndFollowersStore_RejectFriendRequest_Call{Call: _e.mock.On("RejectFriendRequest", ctx, rejectRequestInput)}
}

func (_c *MockFriendsAndFollowersStore_RejectFriendRequest_Call) Run(run func(ctx context.Context, rejectRequestInput *models.FriendRequestInput)) *MockFriendsAndFollowersStore_RejectFriendRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.FriendRequestInput))
	})
	return _c
}

func (_c *MockFriendsAndFollowersStore_RejectFriendRequest_Call) Return(b bool, err error) *MockFriendsAndFollowersStore_RejectFriendRequest_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockFriendsAndFollowersStore_RejectFriendRequest_Call) RunAndReturn(run func(ctx context.Context, rejectRequestInput *models.FriendRequestInput) (bool, error)) *MockFriendsAndFollowersStore_RejectFriendRequest_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveFollower provides a mock function for the type MockFriendsAndFollowersStore
func (_mock *MockFriendsAndFollowersStore) RemoveFollower(ctx context.Context, removeFollowerInput *models.RemoveFollowerInput) (bool, error) {
	ret := _mock.Called(ctx, removeFollowerInput)

	if len(ret) == 0 {
		panic("no return value specified for RemoveFollower")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.RemoveFollowerInput) (bool, error)); ok {
		return returnFunc(ctx, removeFollowerInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.RemoveFollowerInput) bool); ok {
		r0 = returnFunc(ctx, removeFollowerInput)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.RemoveFollowerInput) error); ok {
		r1 = returnFunc(ctx, removeFollowerInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersStore_RemoveFollower_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveFollower'
type MockFriendsAndFollowersStore_RemoveFollower_Call struct {
	*mock.Call
}

// RemoveFollower is a helper method to define mock.On call
//   - ctx
//   - removeFollowerInput
func (_e *MockFriendsAndFollowersStore_Expecter) RemoveFollower(ctx interface{}, removeFollowerInput interface{}) *MockFriendsAndFollowersStore_RemoveFollower_Call {
	return &MockFriendsAndFollowersStore_RemoveFollower_Call{Call: _e.mock.On("RemoveFollower", ctx, removeFollowerInput)}
}

func (_c *MockFriendsAndFollowersStore_RemoveFollower_Call) Run(run func(ctx context.Context, removeFollowerInput *models.RemoveFollowerInput)) *MockFriendsAndFollowersStore_RemoveFollower_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.RemoveFollowerInput))
	})
	return _c
}

func (_c *MockFriendsAndFollowersStore_RemoveFollower_Call) Return(b bool, err error) *MockFriendsAndFollowersStore_RemoveFollower_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockFriendsAndFollowersStore_RemoveFollower_Call) RunAndReturn(run func(ctx context.Context, removeFollowerInput *models.RemoveFollowerInput) (bool, error)) *MockFriendsAndFollowersStore_RemoveFollower_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveFriend provides a mock function for the type MockFriendsAndFollowersStore
func (_mock *MockFriendsAndFollowersStore) RemoveFriend(ctx context.Context, removeFriendInput *models.RemoveFriendInput) (bool, error) {
	ret := _mock.Called(ctx, removeFriendInput)

	if len(ret) == 0 {
		panic("no return value specified for RemoveFriend")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.RemoveFriendInput) (bool, error)); ok {
		return returnFunc(ctx, removeFriendInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.RemoveFriendInput) bool); ok {
		r0 = returnFunc(ctx, removeFriendInput)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.RemoveFriendInput) error); ok {
		r1 = returnFunc(ctx, removeFriendInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersStore_RemoveFriend_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveFriend'
type MockFriendsAndFollowersStore_RemoveFriend_Call struct {
	*mock.Call
}

// RemoveFriend is a helper method to define mock.On call
//   - ctx
//   - removeFriendInput
func (_e *MockFriendsAndFollowersStore_Expecter) RemoveFriend(ctx interface{}, removeFriendInput interface{}) *MockFriendsAndFollowersStore_RemoveFriend_Call {
	return &MockFriendsAndFollowersStore_RemoveFriend_Call{Call: _e.mock.On("RemoveFriend", ctx, removeFriendInput)}
}

func (_c *MockFriendsAndFollowersStore_RemoveFriend_Call) Run(run func(ctx context.Context, removeFriendInput *models.RemoveFriendInput)) *MockFriendsAndFollowersStore_RemoveFriend_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.RemoveFriendInput))
	})
	return _c
}

func (_c *MockFriendsAndFollowersStore_RemoveFriend_Call) Return(b bool, err error) *MockFriendsAndFollowersStore_RemoveFriend_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockFriendsAndFollowersStore_RemoveFriend_Call) RunAndReturn(run func(ctx context.Context, removeFriendInput *models.RemoveFriendInput) (bool, error)) *MockFriendsAndFollowersStore_RemoveFriend_Call {
	_c.Call.Return(run)
	return _c
}

// SendFriendRequest provides a mock function for the type MockFriendsAndFollowersStore
func (_mock *MockFriendsAndFollowersStore) SendFriendRequest(ctx context.Context, sendRequestInput *models.FriendRequestInput) (bool, error) {
	ret := _mock.Called(ctx, sendRequestInput)

	if len(ret) == 0 {
		panic("no return value specified for SendFriendRequest")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.FriendRequestInput) (bool, error)); ok {
		return returnFunc(ctx, sendRequestInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.FriendRequestInput) bool); ok {
		r0 = returnFunc(ctx, sendRequestInput)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.FriendRequestInput) error); ok {
		r1 = returnFunc(ctx, sendRequestInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersStore_SendFriendRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendFriendRequest'
type MockFriendsAndFollowersStore_SendFriendRequest_Call struct {
	*mock.Call
}

// SendFriendRequest is a helper method to define mock.On call
//   - ctx
//   - sendRequestInput
func (_e *MockFriendsAndFollowersStore_Expecter) SendFriendRequest(ctx interface{}, sendRequestInput interface{}) *MockFriendsAndFollowersStore_SendFriendRequest_Call {
	return &MockFriendsAndFollowersStore_SendFriendRequest_Call{Call: _e.mock.On("SendFriendRequest", ctx, sendRequestInput)}
}

func (_c *MockFriendsAndFollowersStore_SendFriendRequest_Call) Run(run func(ctx context.Context, sendRequestInput *models.FriendRequestInput)) *MockFriendsAndFollowersStore_SendFriendRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.FriendRequestInput))
	})
	return _c
}

func (_c *MockFriendsAndFollowersStore_SendFriendRequest_Call) Return(b bool, err error) *MockFriendsAndFollowersStore_SendFriendRequest_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockFriendsAndFollowersStore_SendFriendRequest_Call) RunAndReturn(run func(ctx context.Context, sendRequestInput *models.FriendRequestInput) (bool, error)) *MockFriendsAndFollowersStore_SendFriendRequest_Call {
	_c.Call.Return(run)
	return _c
}

// UnFollowUser provides a mock function for the type MockFriendsAndFollowersStore
func (_mock *MockFriendsAndFollowersStore) UnFollowUser(ctx context.Context, unFollowUserInput *models.UnFollowUserInput) (bool, error) {
	ret := _mock.Called(ctx, unFollowUserInput)

	if len(ret) == 0 {
		panic("no return value specified for UnFollowUser")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UnFollowUserInput) (bool, error)); ok {
		return returnFunc(ctx, unFollowUserInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UnFollowUserInput) bool); ok {
		r0 = returnFunc(ctx, unFollowUserInput)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.UnFollowUserInput) error); ok {
		r1 = returnFunc(ctx, unFollowUserInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersStore_UnFollowUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UnFollowUser'
type MockFriendsAndFollowersStore_UnFollowUser_Call struct {
	*mock.Call
}

// UnFollowUser is a helper method to define mock.On call
//   - ctx
//   - unFollowUserInput
func (_e *MockFriendsAndFollowersStore_Expecter) UnFollowUser(ctx interface{}, unFollowUserInput interface{}) *MockFriendsAndFollowersStore_UnFollowUser_Call {
	return &MockFriendsAndFollowersStore_UnFollowUser_Call{Call: _e.mock.On("UnFollowUser", ctx, unFollowUserInput)}
}

func (_c *MockFriendsAndFollowersStore_UnFollowUser_Call) Run(run func(ctx context.Context, unFollowUserInput *models.UnFollowUserInput)) *MockFriendsAndFollowersStore_UnFollowUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.UnFollowUserInput))
	})
	return _c
}

func (_c *MockFriendsAndFollowersStore_UnFollowUser_Call) Return(b bool, err error) *MockFriendsAndFollowersStore_UnFollowUser_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockFriendsAndFollowersStore_UnFollowUser_Call) RunAndReturn(run func(ctx context.Context, unFollowUserInput *models.UnFollowUserInput) (bool, error)) *MockFriendsAndFollowersStore_UnFollowUser_Call {
	_c.Call.Return(run)
	return _c
}

// WithdrawFriendRequest provides a mock function for the type MockFriendsAndFollowersStore
func (_mock *MockFriendsAndFollowersStore) WithdrawFriendRequest(ctx context.Context, withrawFriendRequestInput *models.WithdrawFriendRequestInput) (bool, error) {
	ret := _mock.Called(ctx, withrawFriendRequestInput)

	if len(ret) == 0 {
		panic("no return value specified for WithdrawFriendRequest")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.WithdrawFriendRequestInput) (bool, error)); ok {
		return returnFunc(ctx, withrawFriendRequestInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.WithdrawFriendRequestInput) bool); ok {
		r0 = returnFunc(ctx, withrawFriendRequestInput)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.WithdrawFriendRequestInput) error); ok {
		r1 = returnFunc(ctx, withrawFriendRequestInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersStore_WithdrawFriendRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WithdrawFriendRequest'
type MockFriendsAndFollowersStore_WithdrawFriendRequest_Call struct {
	*mock.Call
}

// WithdrawFriendRequest is a helper method to define mock.On call
//   - ctx
//   - withrawFriendRequestInput
func (_e *MockFriendsAndFollowersStore_Expecter) WithdrawFriendRequest(ctx interface{}, withrawFriendRequestInput interface{}) *MockFriendsAndFollowersStore_WithdrawFriendRequest_Call {
	return &MockFriendsAndFollowersStore_WithdrawFriendRequest_Call{Call: _e.mock.On("WithdrawFriendRequest", ctx, withrawFriendRequestInput)}
}

func (_c *MockFriendsAndFollowersStore_WithdrawFriendRequest_Call) Run(run func(ctx context.Context, withrawFriendRequestInput *models.WithdrawFriendRequestInput)) *MockFriendsAndFollowersStore_WithdrawFriendRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.WithdrawFriendRequestInput))
	})
	return _c
}

func (_c *MockFriendsAndFollowersStore_WithdrawFriendRequest_Call) Return(b bool, err error) *MockFriendsAndFollowersStore_WithdrawFriendRequest_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockFriendsAndFollowersStore_WithdrawFriendRequest_Call) RunAndReturn(run func(ctx context.Context, withrawFriendRequestInput *models.WithdrawFriendRequestInput) (bool, error)) *MockFriendsAndFollowersStore_WithdrawFriendRequest_Call {
	_c.Call.Return(run)
	return _c
}
