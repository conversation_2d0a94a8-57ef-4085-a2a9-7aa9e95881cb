// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockGameStore creates a new instance of MockGameStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockGameStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockGameStore {
	mock := &MockGameStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockGameStore is an autogenerated mock type for the GameStore type
type MockGameStore struct {
	mock.Mock
}

type MockGameStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockGameStore) EXPECT() *MockGameStore_Expecter {
	return &MockGameStore_Expecter{mock: &_m.Mock}
}

// AbortSearching provides a mock function for the type MockGameStore
func (_mock *MockGameStore) AbortSearching(ctx context.Context) (*bool, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for AbortSearching")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*bool, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *bool); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_AbortSearching_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AbortSearching'
type MockGameStore_AbortSearching_Call struct {
	*mock.Call
}

// AbortSearching is a helper method to define mock.On call
//   - ctx
func (_e *MockGameStore_Expecter) AbortSearching(ctx interface{}) *MockGameStore_AbortSearching_Call {
	return &MockGameStore_AbortSearching_Call{Call: _e.mock.On("AbortSearching", ctx)}
}

func (_c *MockGameStore_AbortSearching_Call) Run(run func(ctx context.Context)) *MockGameStore_AbortSearching_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockGameStore_AbortSearching_Call) Return(b *bool, err error) *MockGameStore_AbortSearching_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockGameStore_AbortSearching_Call) RunAndReturn(run func(ctx context.Context) (*bool, error)) *MockGameStore_AbortSearching_Call {
	_c.Call.Return(run)
	return _c
}

// AcceptChallenge provides a mock function for the type MockGameStore
func (_mock *MockGameStore) AcceptChallenge(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for AcceptChallenge")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Game, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Game); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_AcceptChallenge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcceptChallenge'
type MockGameStore_AcceptChallenge_Call struct {
	*mock.Call
}

// AcceptChallenge is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockGameStore_Expecter) AcceptChallenge(ctx interface{}, gameID interface{}) *MockGameStore_AcceptChallenge_Call {
	return &MockGameStore_AcceptChallenge_Call{Call: _e.mock.On("AcceptChallenge", ctx, gameID)}
}

func (_c *MockGameStore_AcceptChallenge_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockGameStore_AcceptChallenge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockGameStore_AcceptChallenge_Call) Return(game *models.Game, err error) *MockGameStore_AcceptChallenge_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockGameStore_AcceptChallenge_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error)) *MockGameStore_AcceptChallenge_Call {
	_c.Call.Return(run)
	return _c
}

// AcceptRematch provides a mock function for the type MockGameStore
func (_mock *MockGameStore) AcceptRematch(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for AcceptRematch")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Game, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Game); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_AcceptRematch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcceptRematch'
type MockGameStore_AcceptRematch_Call struct {
	*mock.Call
}

// AcceptRematch is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockGameStore_Expecter) AcceptRematch(ctx interface{}, gameID interface{}) *MockGameStore_AcceptRematch_Call {
	return &MockGameStore_AcceptRematch_Call{Call: _e.mock.On("AcceptRematch", ctx, gameID)}
}

func (_c *MockGameStore_AcceptRematch_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockGameStore_AcceptRematch_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockGameStore_AcceptRematch_Call) Return(game *models.Game, err error) *MockGameStore_AcceptRematch_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockGameStore_AcceptRematch_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error)) *MockGameStore_AcceptRematch_Call {
	_c.Call.Return(run)
	return _c
}

// CancelGame provides a mock function for the type MockGameStore
func (_mock *MockGameStore) CancelGame(ctx context.Context, gameID primitive.ObjectID) (*bool, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for CancelGame")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*bool, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *bool); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_CancelGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CancelGame'
type MockGameStore_CancelGame_Call struct {
	*mock.Call
}

// CancelGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockGameStore_Expecter) CancelGame(ctx interface{}, gameID interface{}) *MockGameStore_CancelGame_Call {
	return &MockGameStore_CancelGame_Call{Call: _e.mock.On("CancelGame", ctx, gameID)}
}

func (_c *MockGameStore_CancelGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockGameStore_CancelGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockGameStore_CancelGame_Call) Return(b *bool, err error) *MockGameStore_CancelGame_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockGameStore_CancelGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*bool, error)) *MockGameStore_CancelGame_Call {
	_c.Call.Return(run)
	return _c
}

// CancelRematchRequest provides a mock function for the type MockGameStore
func (_mock *MockGameStore) CancelRematchRequest(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for CancelRematchRequest")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_CancelRematchRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CancelRematchRequest'
type MockGameStore_CancelRematchRequest_Call struct {
	*mock.Call
}

// CancelRematchRequest is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockGameStore_Expecter) CancelRematchRequest(ctx interface{}, gameID interface{}) *MockGameStore_CancelRematchRequest_Call {
	return &MockGameStore_CancelRematchRequest_Call{Call: _e.mock.On("CancelRematchRequest", ctx, gameID)}
}

func (_c *MockGameStore_CancelRematchRequest_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockGameStore_CancelRematchRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockGameStore_CancelRematchRequest_Call) Return(b bool, err error) *MockGameStore_CancelRematchRequest_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockGameStore_CancelRematchRequest_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (bool, error)) *MockGameStore_CancelRematchRequest_Call {
	_c.Call.Return(run)
	return _c
}

// ChallengeUser provides a mock function for the type MockGameStore
func (_mock *MockGameStore) ChallengeUser(ctx context.Context, challengeUserInput *models.ChallengeUserInput) (*models.Game, error) {
	ret := _mock.Called(ctx, challengeUserInput)

	if len(ret) == 0 {
		panic("no return value specified for ChallengeUser")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ChallengeUserInput) (*models.Game, error)); ok {
		return returnFunc(ctx, challengeUserInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ChallengeUserInput) *models.Game); ok {
		r0 = returnFunc(ctx, challengeUserInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.ChallengeUserInput) error); ok {
		r1 = returnFunc(ctx, challengeUserInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_ChallengeUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ChallengeUser'
type MockGameStore_ChallengeUser_Call struct {
	*mock.Call
}

// ChallengeUser is a helper method to define mock.On call
//   - ctx
//   - challengeUserInput
func (_e *MockGameStore_Expecter) ChallengeUser(ctx interface{}, challengeUserInput interface{}) *MockGameStore_ChallengeUser_Call {
	return &MockGameStore_ChallengeUser_Call{Call: _e.mock.On("ChallengeUser", ctx, challengeUserInput)}
}

func (_c *MockGameStore_ChallengeUser_Call) Run(run func(ctx context.Context, challengeUserInput *models.ChallengeUserInput)) *MockGameStore_ChallengeUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.ChallengeUserInput))
	})
	return _c
}

func (_c *MockGameStore_ChallengeUser_Call) Return(game *models.Game, err error) *MockGameStore_ChallengeUser_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockGameStore_ChallengeUser_Call) RunAndReturn(run func(ctx context.Context, challengeUserInput *models.ChallengeUserInput) (*models.Game, error)) *MockGameStore_ChallengeUser_Call {
	_c.Call.Return(run)
	return _c
}

// CheckIfOpponentNotJoinedForShowdown provides a mock function for the type MockGameStore
func (_mock *MockGameStore) CheckIfOpponentNotJoinedForShowdown(ctx context.Context, showdownId primitive.ObjectID, gameId primitive.ObjectID) error {
	ret := _mock.Called(ctx, showdownId, gameId)

	if len(ret) == 0 {
		panic("no return value specified for CheckIfOpponentNotJoinedForShowdown")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, showdownId, gameId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockGameStore_CheckIfOpponentNotJoinedForShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckIfOpponentNotJoinedForShowdown'
type MockGameStore_CheckIfOpponentNotJoinedForShowdown_Call struct {
	*mock.Call
}

// CheckIfOpponentNotJoinedForShowdown is a helper method to define mock.On call
//   - ctx
//   - showdownId
//   - gameId
func (_e *MockGameStore_Expecter) CheckIfOpponentNotJoinedForShowdown(ctx interface{}, showdownId interface{}, gameId interface{}) *MockGameStore_CheckIfOpponentNotJoinedForShowdown_Call {
	return &MockGameStore_CheckIfOpponentNotJoinedForShowdown_Call{Call: _e.mock.On("CheckIfOpponentNotJoinedForShowdown", ctx, showdownId, gameId)}
}

func (_c *MockGameStore_CheckIfOpponentNotJoinedForShowdown_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, gameId primitive.ObjectID)) *MockGameStore_CheckIfOpponentNotJoinedForShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockGameStore_CheckIfOpponentNotJoinedForShowdown_Call) Return(err error) *MockGameStore_CheckIfOpponentNotJoinedForShowdown_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockGameStore_CheckIfOpponentNotJoinedForShowdown_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, gameId primitive.ObjectID) error) *MockGameStore_CheckIfOpponentNotJoinedForShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// CreateGame provides a mock function for the type MockGameStore
func (_mock *MockGameStore) CreateGame(ctx context.Context, gameConfig *models.GameConfigInput) (*models.Game, error) {
	ret := _mock.Called(ctx, gameConfig)

	if len(ret) == 0 {
		panic("no return value specified for CreateGame")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GameConfigInput) (*models.Game, error)); ok {
		return returnFunc(ctx, gameConfig)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GameConfigInput) *models.Game); ok {
		r0 = returnFunc(ctx, gameConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.GameConfigInput) error); ok {
		r1 = returnFunc(ctx, gameConfig)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_CreateGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateGame'
type MockGameStore_CreateGame_Call struct {
	*mock.Call
}

// CreateGame is a helper method to define mock.On call
//   - ctx
//   - gameConfig
func (_e *MockGameStore_Expecter) CreateGame(ctx interface{}, gameConfig interface{}) *MockGameStore_CreateGame_Call {
	return &MockGameStore_CreateGame_Call{Call: _e.mock.On("CreateGame", ctx, gameConfig)}
}

func (_c *MockGameStore_CreateGame_Call) Run(run func(ctx context.Context, gameConfig *models.GameConfigInput)) *MockGameStore_CreateGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.GameConfigInput))
	})
	return _c
}

func (_c *MockGameStore_CreateGame_Call) Return(game *models.Game, err error) *MockGameStore_CreateGame_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockGameStore_CreateGame_Call) RunAndReturn(run func(ctx context.Context, gameConfig *models.GameConfigInput) (*models.Game, error)) *MockGameStore_CreateGame_Call {
	_c.Call.Return(run)
	return _c
}

// CreateGameForShowdown provides a mock function for the type MockGameStore
func (_mock *MockGameStore) CreateGameForShowdown(ctx context.Context, showdownConfig *models.ShowdownConfig) ([]*models.Game, error) {
	ret := _mock.Called(ctx, showdownConfig)

	if len(ret) == 0 {
		panic("no return value specified for CreateGameForShowdown")
	}

	var r0 []*models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ShowdownConfig) ([]*models.Game, error)); ok {
		return returnFunc(ctx, showdownConfig)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ShowdownConfig) []*models.Game); ok {
		r0 = returnFunc(ctx, showdownConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.ShowdownConfig) error); ok {
		r1 = returnFunc(ctx, showdownConfig)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_CreateGameForShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateGameForShowdown'
type MockGameStore_CreateGameForShowdown_Call struct {
	*mock.Call
}

// CreateGameForShowdown is a helper method to define mock.On call
//   - ctx
//   - showdownConfig
func (_e *MockGameStore_Expecter) CreateGameForShowdown(ctx interface{}, showdownConfig interface{}) *MockGameStore_CreateGameForShowdown_Call {
	return &MockGameStore_CreateGameForShowdown_Call{Call: _e.mock.On("CreateGameForShowdown", ctx, showdownConfig)}
}

func (_c *MockGameStore_CreateGameForShowdown_Call) Run(run func(ctx context.Context, showdownConfig *models.ShowdownConfig)) *MockGameStore_CreateGameForShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.ShowdownConfig))
	})
	return _c
}

func (_c *MockGameStore_CreateGameForShowdown_Call) Return(games []*models.Game, err error) *MockGameStore_CreateGameForShowdown_Call {
	_c.Call.Return(games, err)
	return _c
}

func (_c *MockGameStore_CreateGameForShowdown_Call) RunAndReturn(run func(ctx context.Context, showdownConfig *models.ShowdownConfig) ([]*models.Game, error)) *MockGameStore_CreateGameForShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// EndAbilityDuelsGame provides a mock function for the type MockGameStore
func (_mock *MockGameStore) EndAbilityDuelsGame(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for EndAbilityDuelsGame")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) (*models.Game, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) *models.Game); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_EndAbilityDuelsGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EndAbilityDuelsGame'
type MockGameStore_EndAbilityDuelsGame_Call struct {
	*mock.Call
}

// EndAbilityDuelsGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockGameStore_Expecter) EndAbilityDuelsGame(ctx interface{}, gameID interface{}) *MockGameStore_EndAbilityDuelsGame_Call {
	return &MockGameStore_EndAbilityDuelsGame_Call{Call: _e.mock.On("EndAbilityDuelsGame", ctx, gameID)}
}

func (_c *MockGameStore_EndAbilityDuelsGame_Call) Run(run func(ctx context.Context, gameID *primitive.ObjectID)) *MockGameStore_EndAbilityDuelsGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*primitive.ObjectID))
	})
	return _c
}

func (_c *MockGameStore_EndAbilityDuelsGame_Call) Return(game *models.Game, err error) *MockGameStore_EndAbilityDuelsGame_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockGameStore_EndAbilityDuelsGame_Call) RunAndReturn(run func(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error)) *MockGameStore_EndAbilityDuelsGame_Call {
	_c.Call.Return(run)
	return _c
}

// EndGame provides a mock function for the type MockGameStore
func (_mock *MockGameStore) EndGame(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for EndGame")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) (*models.Game, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) *models.Game); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_EndGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EndGame'
type MockGameStore_EndGame_Call struct {
	*mock.Call
}

// EndGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockGameStore_Expecter) EndGame(ctx interface{}, gameID interface{}) *MockGameStore_EndGame_Call {
	return &MockGameStore_EndGame_Call{Call: _e.mock.On("EndGame", ctx, gameID)}
}

func (_c *MockGameStore_EndGame_Call) Run(run func(ctx context.Context, gameID *primitive.ObjectID)) *MockGameStore_EndGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*primitive.ObjectID))
	})
	return _c
}

func (_c *MockGameStore_EndGame_Call) Return(game *models.Game, err error) *MockGameStore_EndGame_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockGameStore_EndGame_Call) RunAndReturn(run func(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error)) *MockGameStore_EndGame_Call {
	_c.Call.Return(run)
	return _c
}

// EndGameForShowdown provides a mock function for the type MockGameStore
func (_mock *MockGameStore) EndGameForShowdown(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for EndGameForShowdown")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) (*models.Game, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) *models.Game); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_EndGameForShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EndGameForShowdown'
type MockGameStore_EndGameForShowdown_Call struct {
	*mock.Call
}

// EndGameForShowdown is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockGameStore_Expecter) EndGameForShowdown(ctx interface{}, gameID interface{}) *MockGameStore_EndGameForShowdown_Call {
	return &MockGameStore_EndGameForShowdown_Call{Call: _e.mock.On("EndGameForShowdown", ctx, gameID)}
}

func (_c *MockGameStore_EndGameForShowdown_Call) Run(run func(ctx context.Context, gameID *primitive.ObjectID)) *MockGameStore_EndGameForShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*primitive.ObjectID))
	})
	return _c
}

func (_c *MockGameStore_EndGameForShowdown_Call) Return(game *models.Game, err error) *MockGameStore_EndGameForShowdown_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockGameStore_EndGameForShowdown_Call) RunAndReturn(run func(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error)) *MockGameStore_EndGameForShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// GameEvent provides a mock function for the type MockGameStore
func (_mock *MockGameStore) GameEvent(ctx context.Context, gameID *primitive.ObjectID) (<-chan *models.SubscriptionOutput, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for GameEvent")
	}

	var r0 <-chan *models.SubscriptionOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) (<-chan *models.SubscriptionOutput, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) <-chan *models.SubscriptionOutput); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(<-chan *models.SubscriptionOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_GameEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GameEvent'
type MockGameStore_GameEvent_Call struct {
	*mock.Call
}

// GameEvent is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockGameStore_Expecter) GameEvent(ctx interface{}, gameID interface{}) *MockGameStore_GameEvent_Call {
	return &MockGameStore_GameEvent_Call{Call: _e.mock.On("GameEvent", ctx, gameID)}
}

func (_c *MockGameStore_GameEvent_Call) Run(run func(ctx context.Context, gameID *primitive.ObjectID)) *MockGameStore_GameEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*primitive.ObjectID))
	})
	return _c
}

func (_c *MockGameStore_GameEvent_Call) Return(subscriptionOutputCh <-chan *models.SubscriptionOutput, err error) *MockGameStore_GameEvent_Call {
	_c.Call.Return(subscriptionOutputCh, err)
	return _c
}

func (_c *MockGameStore_GameEvent_Call) RunAndReturn(run func(ctx context.Context, gameID *primitive.ObjectID) (<-chan *models.SubscriptionOutput, error)) *MockGameStore_GameEvent_Call {
	_c.Call.Return(run)
	return _c
}

// GetGameByID provides a mock function for the type MockGameStore
func (_mock *MockGameStore) GetGameByID(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for GetGameByID")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) (*models.Game, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) *models.Game); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_GetGameByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGameByID'
type MockGameStore_GetGameByID_Call struct {
	*mock.Call
}

// GetGameByID is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockGameStore_Expecter) GetGameByID(ctx interface{}, gameID interface{}) *MockGameStore_GetGameByID_Call {
	return &MockGameStore_GetGameByID_Call{Call: _e.mock.On("GetGameByID", ctx, gameID)}
}

func (_c *MockGameStore_GetGameByID_Call) Run(run func(ctx context.Context, gameID *primitive.ObjectID)) *MockGameStore_GetGameByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*primitive.ObjectID))
	})
	return _c
}

func (_c *MockGameStore_GetGameByID_Call) Return(game *models.Game, err error) *MockGameStore_GetGameByID_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockGameStore_GetGameByID_Call) RunAndReturn(run func(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error)) *MockGameStore_GetGameByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetGameDetailedAnalysis provides a mock function for the type MockGameStore
func (_mock *MockGameStore) GetGameDetailedAnalysis(ctx context.Context, gameID *primitive.ObjectID) (*models.GameDetailedAnalysis, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for GetGameDetailedAnalysis")
	}

	var r0 *models.GameDetailedAnalysis
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) (*models.GameDetailedAnalysis, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) *models.GameDetailedAnalysis); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GameDetailedAnalysis)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_GetGameDetailedAnalysis_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGameDetailedAnalysis'
type MockGameStore_GetGameDetailedAnalysis_Call struct {
	*mock.Call
}

// GetGameDetailedAnalysis is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockGameStore_Expecter) GetGameDetailedAnalysis(ctx interface{}, gameID interface{}) *MockGameStore_GetGameDetailedAnalysis_Call {
	return &MockGameStore_GetGameDetailedAnalysis_Call{Call: _e.mock.On("GetGameDetailedAnalysis", ctx, gameID)}
}

func (_c *MockGameStore_GetGameDetailedAnalysis_Call) Run(run func(ctx context.Context, gameID *primitive.ObjectID)) *MockGameStore_GetGameDetailedAnalysis_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*primitive.ObjectID))
	})
	return _c
}

func (_c *MockGameStore_GetGameDetailedAnalysis_Call) Return(gameDetailedAnalysis *models.GameDetailedAnalysis, err error) *MockGameStore_GetGameDetailedAnalysis_Call {
	_c.Call.Return(gameDetailedAnalysis, err)
	return _c
}

func (_c *MockGameStore_GetGameDetailedAnalysis_Call) RunAndReturn(run func(ctx context.Context, gameID *primitive.ObjectID) (*models.GameDetailedAnalysis, error)) *MockGameStore_GetGameDetailedAnalysis_Call {
	_c.Call.Return(run)
	return _c
}

// GetGamesByRatingType provides a mock function for the type MockGameStore
func (_mock *MockGameStore) GetGamesByRatingType(ctx context.Context, payload *models.GetGamesByRatingInput) (*models.GetGamesByRatingOutput, error) {
	ret := _mock.Called(ctx, payload)

	if len(ret) == 0 {
		panic("no return value specified for GetGamesByRatingType")
	}

	var r0 *models.GetGamesByRatingOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GetGamesByRatingInput) (*models.GetGamesByRatingOutput, error)); ok {
		return returnFunc(ctx, payload)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GetGamesByRatingInput) *models.GetGamesByRatingOutput); ok {
		r0 = returnFunc(ctx, payload)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GetGamesByRatingOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.GetGamesByRatingInput) error); ok {
		r1 = returnFunc(ctx, payload)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_GetGamesByRatingType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGamesByRatingType'
type MockGameStore_GetGamesByRatingType_Call struct {
	*mock.Call
}

// GetGamesByRatingType is a helper method to define mock.On call
//   - ctx
//   - payload
func (_e *MockGameStore_Expecter) GetGamesByRatingType(ctx interface{}, payload interface{}) *MockGameStore_GetGamesByRatingType_Call {
	return &MockGameStore_GetGamesByRatingType_Call{Call: _e.mock.On("GetGamesByRatingType", ctx, payload)}
}

func (_c *MockGameStore_GetGamesByRatingType_Call) Run(run func(ctx context.Context, payload *models.GetGamesByRatingInput)) *MockGameStore_GetGamesByRatingType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.GetGamesByRatingInput))
	})
	return _c
}

func (_c *MockGameStore_GetGamesByRatingType_Call) Return(getGamesByRatingOutput *models.GetGamesByRatingOutput, err error) *MockGameStore_GetGamesByRatingType_Call {
	_c.Call.Return(getGamesByRatingOutput, err)
	return _c
}

func (_c *MockGameStore_GetGamesByRatingType_Call) RunAndReturn(run func(ctx context.Context, payload *models.GetGamesByRatingInput) (*models.GetGamesByRatingOutput, error)) *MockGameStore_GetGamesByRatingType_Call {
	_c.Call.Return(run)
	return _c
}

// GetGamesByUser provides a mock function for the type MockGameStore
func (_mock *MockGameStore) GetGamesByUser(ctx context.Context, payload *models.GetGamesInput) (*models.GetGamesOutput, error) {
	ret := _mock.Called(ctx, payload)

	if len(ret) == 0 {
		panic("no return value specified for GetGamesByUser")
	}

	var r0 *models.GetGamesOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GetGamesInput) (*models.GetGamesOutput, error)); ok {
		return returnFunc(ctx, payload)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GetGamesInput) *models.GetGamesOutput); ok {
		r0 = returnFunc(ctx, payload)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GetGamesOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.GetGamesInput) error); ok {
		r1 = returnFunc(ctx, payload)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_GetGamesByUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGamesByUser'
type MockGameStore_GetGamesByUser_Call struct {
	*mock.Call
}

// GetGamesByUser is a helper method to define mock.On call
//   - ctx
//   - payload
func (_e *MockGameStore_Expecter) GetGamesByUser(ctx interface{}, payload interface{}) *MockGameStore_GetGamesByUser_Call {
	return &MockGameStore_GetGamesByUser_Call{Call: _e.mock.On("GetGamesByUser", ctx, payload)}
}

func (_c *MockGameStore_GetGamesByUser_Call) Run(run func(ctx context.Context, payload *models.GetGamesInput)) *MockGameStore_GetGamesByUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.GetGamesInput))
	})
	return _c
}

func (_c *MockGameStore_GetGamesByUser_Call) Return(getGamesOutput *models.GetGamesOutput, err error) *MockGameStore_GetGamesByUser_Call {
	_c.Call.Return(getGamesOutput, err)
	return _c
}

func (_c *MockGameStore_GetGamesByUser_Call) RunAndReturn(run func(ctx context.Context, payload *models.GetGamesInput) (*models.GetGamesOutput, error)) *MockGameStore_GetGamesByUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetPlatformStats provides a mock function for the type MockGameStore
func (_mock *MockGameStore) GetPlatformStats(ctx context.Context) (*models.PlatformStats, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetPlatformStats")
	}

	var r0 *models.PlatformStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.PlatformStats, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.PlatformStats); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PlatformStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_GetPlatformStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPlatformStats'
type MockGameStore_GetPlatformStats_Call struct {
	*mock.Call
}

// GetPlatformStats is a helper method to define mock.On call
//   - ctx
func (_e *MockGameStore_Expecter) GetPlatformStats(ctx interface{}) *MockGameStore_GetPlatformStats_Call {
	return &MockGameStore_GetPlatformStats_Call{Call: _e.mock.On("GetPlatformStats", ctx)}
}

func (_c *MockGameStore_GetPlatformStats_Call) Run(run func(ctx context.Context)) *MockGameStore_GetPlatformStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockGameStore_GetPlatformStats_Call) Return(platformStats *models.PlatformStats, err error) *MockGameStore_GetPlatformStats_Call {
	_c.Call.Return(platformStats, err)
	return _c
}

func (_c *MockGameStore_GetPlatformStats_Call) RunAndReturn(run func(ctx context.Context) (*models.PlatformStats, error)) *MockGameStore_GetPlatformStats_Call {
	_c.Call.Return(run)
	return _c
}

// GetPracticeGame provides a mock function for the type MockGameStore
func (_mock *MockGameStore) GetPracticeGame(ctx context.Context, gameConfigInput *models.GameConfigInput) (*models.Game, error) {
	ret := _mock.Called(ctx, gameConfigInput)

	if len(ret) == 0 {
		panic("no return value specified for GetPracticeGame")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GameConfigInput) (*models.Game, error)); ok {
		return returnFunc(ctx, gameConfigInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GameConfigInput) *models.Game); ok {
		r0 = returnFunc(ctx, gameConfigInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.GameConfigInput) error); ok {
		r1 = returnFunc(ctx, gameConfigInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_GetPracticeGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPracticeGame'
type MockGameStore_GetPracticeGame_Call struct {
	*mock.Call
}

// GetPracticeGame is a helper method to define mock.On call
//   - ctx
//   - gameConfigInput
func (_e *MockGameStore_Expecter) GetPracticeGame(ctx interface{}, gameConfigInput interface{}) *MockGameStore_GetPracticeGame_Call {
	return &MockGameStore_GetPracticeGame_Call{Call: _e.mock.On("GetPracticeGame", ctx, gameConfigInput)}
}

func (_c *MockGameStore_GetPracticeGame_Call) Run(run func(ctx context.Context, gameConfigInput *models.GameConfigInput)) *MockGameStore_GetPracticeGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.GameConfigInput))
	})
	return _c
}

func (_c *MockGameStore_GetPracticeGame_Call) Return(game *models.Game, err error) *MockGameStore_GetPracticeGame_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockGameStore_GetPracticeGame_Call) RunAndReturn(run func(ctx context.Context, gameConfigInput *models.GameConfigInput) (*models.Game, error)) *MockGameStore_GetPracticeGame_Call {
	_c.Call.Return(run)
	return _c
}

// HandleGroupPlayChat provides a mock function for the type MockGameStore
func (_mock *MockGameStore) HandleGroupPlayChat(ctx context.Context, message models.GroupPlayChatMessage) error {
	ret := _mock.Called(ctx, message)

	if len(ret) == 0 {
		panic("no return value specified for HandleGroupPlayChat")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.GroupPlayChatMessage) error); ok {
		r0 = returnFunc(ctx, message)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockGameStore_HandleGroupPlayChat_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HandleGroupPlayChat'
type MockGameStore_HandleGroupPlayChat_Call struct {
	*mock.Call
}

// HandleGroupPlayChat is a helper method to define mock.On call
//   - ctx
//   - message
func (_e *MockGameStore_Expecter) HandleGroupPlayChat(ctx interface{}, message interface{}) *MockGameStore_HandleGroupPlayChat_Call {
	return &MockGameStore_HandleGroupPlayChat_Call{Call: _e.mock.On("HandleGroupPlayChat", ctx, message)}
}

func (_c *MockGameStore_HandleGroupPlayChat_Call) Run(run func(ctx context.Context, message models.GroupPlayChatMessage)) *MockGameStore_HandleGroupPlayChat_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.GroupPlayChatMessage))
	})
	return _c
}

func (_c *MockGameStore_HandleGroupPlayChat_Call) Return(err error) *MockGameStore_HandleGroupPlayChat_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockGameStore_HandleGroupPlayChat_Call) RunAndReturn(run func(ctx context.Context, message models.GroupPlayChatMessage) error) *MockGameStore_HandleGroupPlayChat_Call {
	_c.Call.Return(run)
	return _c
}

// JoinGame provides a mock function for the type MockGameStore
func (_mock *MockGameStore) JoinGame(ctx context.Context, joinGameInput *models.JoinGameInput) (*models.Game, error) {
	ret := _mock.Called(ctx, joinGameInput)

	if len(ret) == 0 {
		panic("no return value specified for JoinGame")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.JoinGameInput) (*models.Game, error)); ok {
		return returnFunc(ctx, joinGameInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.JoinGameInput) *models.Game); ok {
		r0 = returnFunc(ctx, joinGameInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.JoinGameInput) error); ok {
		r1 = returnFunc(ctx, joinGameInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_JoinGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'JoinGame'
type MockGameStore_JoinGame_Call struct {
	*mock.Call
}

// JoinGame is a helper method to define mock.On call
//   - ctx
//   - joinGameInput
func (_e *MockGameStore_Expecter) JoinGame(ctx interface{}, joinGameInput interface{}) *MockGameStore_JoinGame_Call {
	return &MockGameStore_JoinGame_Call{Call: _e.mock.On("JoinGame", ctx, joinGameInput)}
}

func (_c *MockGameStore_JoinGame_Call) Run(run func(ctx context.Context, joinGameInput *models.JoinGameInput)) *MockGameStore_JoinGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.JoinGameInput))
	})
	return _c
}

func (_c *MockGameStore_JoinGame_Call) Return(game *models.Game, err error) *MockGameStore_JoinGame_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockGameStore_JoinGame_Call) RunAndReturn(run func(ctx context.Context, joinGameInput *models.JoinGameInput) (*models.Game, error)) *MockGameStore_JoinGame_Call {
	_c.Call.Return(run)
	return _c
}

// LeaveGame provides a mock function for the type MockGameStore
func (_mock *MockGameStore) LeaveGame(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for LeaveGame")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Game, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Game); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_LeaveGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LeaveGame'
type MockGameStore_LeaveGame_Call struct {
	*mock.Call
}

// LeaveGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockGameStore_Expecter) LeaveGame(ctx interface{}, gameID interface{}) *MockGameStore_LeaveGame_Call {
	return &MockGameStore_LeaveGame_Call{Call: _e.mock.On("LeaveGame", ctx, gameID)}
}

func (_c *MockGameStore_LeaveGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockGameStore_LeaveGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockGameStore_LeaveGame_Call) Return(game *models.Game, err error) *MockGameStore_LeaveGame_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockGameStore_LeaveGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error)) *MockGameStore_LeaveGame_Call {
	_c.Call.Return(run)
	return _c
}

// PublishChallengeEvent provides a mock function for the type MockGameStore
func (_mock *MockGameStore) PublishChallengeEvent(ctx context.Context, game *models.Game, eventType string) error {
	ret := _mock.Called(ctx, game, eventType)

	if len(ret) == 0 {
		panic("no return value specified for PublishChallengeEvent")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Game, string) error); ok {
		r0 = returnFunc(ctx, game, eventType)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockGameStore_PublishChallengeEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PublishChallengeEvent'
type MockGameStore_PublishChallengeEvent_Call struct {
	*mock.Call
}

// PublishChallengeEvent is a helper method to define mock.On call
//   - ctx
//   - game
//   - eventType
func (_e *MockGameStore_Expecter) PublishChallengeEvent(ctx interface{}, game interface{}, eventType interface{}) *MockGameStore_PublishChallengeEvent_Call {
	return &MockGameStore_PublishChallengeEvent_Call{Call: _e.mock.On("PublishChallengeEvent", ctx, game, eventType)}
}

func (_c *MockGameStore_PublishChallengeEvent_Call) Run(run func(ctx context.Context, game *models.Game, eventType string)) *MockGameStore_PublishChallengeEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.Game), args[2].(string))
	})
	return _c
}

func (_c *MockGameStore_PublishChallengeEvent_Call) Return(err error) *MockGameStore_PublishChallengeEvent_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockGameStore_PublishChallengeEvent_Call) RunAndReturn(run func(ctx context.Context, game *models.Game, eventType string) error) *MockGameStore_PublishChallengeEvent_Call {
	_c.Call.Return(run)
	return _c
}

// RejectChallenge provides a mock function for the type MockGameStore
func (_mock *MockGameStore) RejectChallenge(ctx context.Context, gameID primitive.ObjectID) (*bool, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for RejectChallenge")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*bool, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *bool); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_RejectChallenge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RejectChallenge'
type MockGameStore_RejectChallenge_Call struct {
	*mock.Call
}

// RejectChallenge is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockGameStore_Expecter) RejectChallenge(ctx interface{}, gameID interface{}) *MockGameStore_RejectChallenge_Call {
	return &MockGameStore_RejectChallenge_Call{Call: _e.mock.On("RejectChallenge", ctx, gameID)}
}

func (_c *MockGameStore_RejectChallenge_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockGameStore_RejectChallenge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockGameStore_RejectChallenge_Call) Return(b *bool, err error) *MockGameStore_RejectChallenge_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockGameStore_RejectChallenge_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*bool, error)) *MockGameStore_RejectChallenge_Call {
	_c.Call.Return(run)
	return _c
}

// RejectRematch provides a mock function for the type MockGameStore
func (_mock *MockGameStore) RejectRematch(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for RejectRematch")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_RejectRematch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RejectRematch'
type MockGameStore_RejectRematch_Call struct {
	*mock.Call
}

// RejectRematch is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockGameStore_Expecter) RejectRematch(ctx interface{}, gameID interface{}) *MockGameStore_RejectRematch_Call {
	return &MockGameStore_RejectRematch_Call{Call: _e.mock.On("RejectRematch", ctx, gameID)}
}

func (_c *MockGameStore_RejectRematch_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockGameStore_RejectRematch_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockGameStore_RejectRematch_Call) Return(b bool, err error) *MockGameStore_RejectRematch_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockGameStore_RejectRematch_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (bool, error)) *MockGameStore_RejectRematch_Call {
	_c.Call.Return(run)
	return _c
}

// RemovePlayer provides a mock function for the type MockGameStore
func (_mock *MockGameStore) RemovePlayer(ctx context.Context, gameID primitive.ObjectID, playerID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, gameID, playerID)

	if len(ret) == 0 {
		panic("no return value specified for RemovePlayer")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, gameID, playerID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, gameID, playerID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID, playerID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_RemovePlayer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemovePlayer'
type MockGameStore_RemovePlayer_Call struct {
	*mock.Call
}

// RemovePlayer is a helper method to define mock.On call
//   - ctx
//   - gameID
//   - playerID
func (_e *MockGameStore_Expecter) RemovePlayer(ctx interface{}, gameID interface{}, playerID interface{}) *MockGameStore_RemovePlayer_Call {
	return &MockGameStore_RemovePlayer_Call{Call: _e.mock.On("RemovePlayer", ctx, gameID, playerID)}
}

func (_c *MockGameStore_RemovePlayer_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID, playerID primitive.ObjectID)) *MockGameStore_RemovePlayer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockGameStore_RemovePlayer_Call) Return(b bool, err error) *MockGameStore_RemovePlayer_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockGameStore_RemovePlayer_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID, playerID primitive.ObjectID) (bool, error)) *MockGameStore_RemovePlayer_Call {
	_c.Call.Return(run)
	return _c
}

// RequestRematch provides a mock function for the type MockGameStore
func (_mock *MockGameStore) RequestRematch(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for RequestRematch")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_RequestRematch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RequestRematch'
type MockGameStore_RequestRematch_Call struct {
	*mock.Call
}

// RequestRematch is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockGameStore_Expecter) RequestRematch(ctx interface{}, gameID interface{}) *MockGameStore_RequestRematch_Call {
	return &MockGameStore_RequestRematch_Call{Call: _e.mock.On("RequestRematch", ctx, gameID)}
}

func (_c *MockGameStore_RequestRematch_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockGameStore_RequestRematch_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockGameStore_RequestRematch_Call) Return(b bool, err error) *MockGameStore_RequestRematch_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockGameStore_RequestRematch_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (bool, error)) *MockGameStore_RequestRematch_Call {
	_c.Call.Return(run)
	return _c
}

// SearchPlayer provides a mock function for the type MockGameStore
func (_mock *MockGameStore) SearchPlayer(ctx context.Context, userID *models.ObjectID) (<-chan *models.SearchSubscriptionOutput, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for SearchPlayer")
	}

	var r0 <-chan *models.SearchSubscriptionOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ObjectID) (<-chan *models.SearchSubscriptionOutput, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ObjectID) <-chan *models.SearchSubscriptionOutput); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(<-chan *models.SearchSubscriptionOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_SearchPlayer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SearchPlayer'
type MockGameStore_SearchPlayer_Call struct {
	*mock.Call
}

// SearchPlayer is a helper method to define mock.On call
//   - ctx
//   - userID
func (_e *MockGameStore_Expecter) SearchPlayer(ctx interface{}, userID interface{}) *MockGameStore_SearchPlayer_Call {
	return &MockGameStore_SearchPlayer_Call{Call: _e.mock.On("SearchPlayer", ctx, userID)}
}

func (_c *MockGameStore_SearchPlayer_Call) Run(run func(ctx context.Context, userID *models.ObjectID)) *MockGameStore_SearchPlayer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.ObjectID))
	})
	return _c
}

func (_c *MockGameStore_SearchPlayer_Call) Return(searchSubscriptionOutputCh <-chan *models.SearchSubscriptionOutput, err error) *MockGameStore_SearchPlayer_Call {
	_c.Call.Return(searchSubscriptionOutputCh, err)
	return _c
}

func (_c *MockGameStore_SearchPlayer_Call) RunAndReturn(run func(ctx context.Context, userID *models.ObjectID) (<-chan *models.SearchSubscriptionOutput, error)) *MockGameStore_SearchPlayer_Call {
	_c.Call.Return(run)
	return _c
}

// StartGame provides a mock function for the type MockGameStore
func (_mock *MockGameStore) StartGame(ctx context.Context, startGameInput *models.StartGameInput) (*models.Game, error) {
	ret := _mock.Called(ctx, startGameInput)

	if len(ret) == 0 {
		panic("no return value specified for StartGame")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.StartGameInput) (*models.Game, error)); ok {
		return returnFunc(ctx, startGameInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.StartGameInput) *models.Game); ok {
		r0 = returnFunc(ctx, startGameInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.StartGameInput) error); ok {
		r1 = returnFunc(ctx, startGameInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_StartGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartGame'
type MockGameStore_StartGame_Call struct {
	*mock.Call
}

// StartGame is a helper method to define mock.On call
//   - ctx
//   - startGameInput
func (_e *MockGameStore_Expecter) StartGame(ctx interface{}, startGameInput interface{}) *MockGameStore_StartGame_Call {
	return &MockGameStore_StartGame_Call{Call: _e.mock.On("StartGame", ctx, startGameInput)}
}

func (_c *MockGameStore_StartGame_Call) Run(run func(ctx context.Context, startGameInput *models.StartGameInput)) *MockGameStore_StartGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.StartGameInput))
	})
	return _c
}

func (_c *MockGameStore_StartGame_Call) Return(game *models.Game, err error) *MockGameStore_StartGame_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockGameStore_StartGame_Call) RunAndReturn(run func(ctx context.Context, startGameInput *models.StartGameInput) (*models.Game, error)) *MockGameStore_StartGame_Call {
	_c.Call.Return(run)
	return _c
}

// StartSearching provides a mock function for the type MockGameStore
func (_mock *MockGameStore) StartSearching(ctx context.Context, gameConfig *models.GameConfigInput) (*bool, error) {
	ret := _mock.Called(ctx, gameConfig)

	if len(ret) == 0 {
		panic("no return value specified for StartSearching")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GameConfigInput) (*bool, error)); ok {
		return returnFunc(ctx, gameConfig)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GameConfigInput) *bool); ok {
		r0 = returnFunc(ctx, gameConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.GameConfigInput) error); ok {
		r1 = returnFunc(ctx, gameConfig)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_StartSearching_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartSearching'
type MockGameStore_StartSearching_Call struct {
	*mock.Call
}

// StartSearching is a helper method to define mock.On call
//   - ctx
//   - gameConfig
func (_e *MockGameStore_Expecter) StartSearching(ctx interface{}, gameConfig interface{}) *MockGameStore_StartSearching_Call {
	return &MockGameStore_StartSearching_Call{Call: _e.mock.On("StartSearching", ctx, gameConfig)}
}

func (_c *MockGameStore_StartSearching_Call) Run(run func(ctx context.Context, gameConfig *models.GameConfigInput)) *MockGameStore_StartSearching_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.GameConfigInput))
	})
	return _c
}

func (_c *MockGameStore_StartSearching_Call) Return(b *bool, err error) *MockGameStore_StartSearching_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockGameStore_StartSearching_Call) RunAndReturn(run func(ctx context.Context, gameConfig *models.GameConfigInput) (*bool, error)) *MockGameStore_StartSearching_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitAnswer provides a mock function for the type MockGameStore
func (_mock *MockGameStore) SubmitAnswer(ctx context.Context, answerInput *models.SubmitAnswerInput) (*models.Game, error) {
	ret := _mock.Called(ctx, answerInput)

	if len(ret) == 0 {
		panic("no return value specified for SubmitAnswer")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.SubmitAnswerInput) (*models.Game, error)); ok {
		return returnFunc(ctx, answerInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.SubmitAnswerInput) *models.Game); ok {
		r0 = returnFunc(ctx, answerInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.SubmitAnswerInput) error); ok {
		r1 = returnFunc(ctx, answerInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_SubmitAnswer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitAnswer'
type MockGameStore_SubmitAnswer_Call struct {
	*mock.Call
}

// SubmitAnswer is a helper method to define mock.On call
//   - ctx
//   - answerInput
func (_e *MockGameStore_Expecter) SubmitAnswer(ctx interface{}, answerInput interface{}) *MockGameStore_SubmitAnswer_Call {
	return &MockGameStore_SubmitAnswer_Call{Call: _e.mock.On("SubmitAnswer", ctx, answerInput)}
}

func (_c *MockGameStore_SubmitAnswer_Call) Run(run func(ctx context.Context, answerInput *models.SubmitAnswerInput)) *MockGameStore_SubmitAnswer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.SubmitAnswerInput))
	})
	return _c
}

func (_c *MockGameStore_SubmitAnswer_Call) Return(game *models.Game, err error) *MockGameStore_SubmitAnswer_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockGameStore_SubmitAnswer_Call) RunAndReturn(run func(ctx context.Context, answerInput *models.SubmitAnswerInput) (*models.Game, error)) *MockGameStore_SubmitAnswer_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitFlashAnzanAnswer provides a mock function for the type MockGameStore
func (_mock *MockGameStore) SubmitFlashAnzanAnswer(ctx context.Context, answerInput *models.SubmitFlashAnzanAnswerInput) (*models.Game, error) {
	ret := _mock.Called(ctx, answerInput)

	if len(ret) == 0 {
		panic("no return value specified for SubmitFlashAnzanAnswer")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.SubmitFlashAnzanAnswerInput) (*models.Game, error)); ok {
		return returnFunc(ctx, answerInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.SubmitFlashAnzanAnswerInput) *models.Game); ok {
		r0 = returnFunc(ctx, answerInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.SubmitFlashAnzanAnswerInput) error); ok {
		r1 = returnFunc(ctx, answerInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameStore_SubmitFlashAnzanAnswer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitFlashAnzanAnswer'
type MockGameStore_SubmitFlashAnzanAnswer_Call struct {
	*mock.Call
}

// SubmitFlashAnzanAnswer is a helper method to define mock.On call
//   - ctx
//   - answerInput
func (_e *MockGameStore_Expecter) SubmitFlashAnzanAnswer(ctx interface{}, answerInput interface{}) *MockGameStore_SubmitFlashAnzanAnswer_Call {
	return &MockGameStore_SubmitFlashAnzanAnswer_Call{Call: _e.mock.On("SubmitFlashAnzanAnswer", ctx, answerInput)}
}

func (_c *MockGameStore_SubmitFlashAnzanAnswer_Call) Run(run func(ctx context.Context, answerInput *models.SubmitFlashAnzanAnswerInput)) *MockGameStore_SubmitFlashAnzanAnswer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.SubmitFlashAnzanAnswerInput))
	})
	return _c
}

func (_c *MockGameStore_SubmitFlashAnzanAnswer_Call) Return(game *models.Game, err error) *MockGameStore_SubmitFlashAnzanAnswer_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockGameStore_SubmitFlashAnzanAnswer_Call) RunAndReturn(run func(ctx context.Context, answerInput *models.SubmitFlashAnzanAnswerInput) (*models.Game, error)) *MockGameStore_SubmitFlashAnzanAnswer_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateShowdownParticipant provides a mock function for the type MockGameStore
func (_mock *MockGameStore) UpdateShowdownParticipant(ctx context.Context, participant *models.ShowdownParticipant, round int) error {
	ret := _mock.Called(ctx, participant, round)

	if len(ret) == 0 {
		panic("no return value specified for UpdateShowdownParticipant")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ShowdownParticipant, int) error); ok {
		r0 = returnFunc(ctx, participant, round)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockGameStore_UpdateShowdownParticipant_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateShowdownParticipant'
type MockGameStore_UpdateShowdownParticipant_Call struct {
	*mock.Call
}

// UpdateShowdownParticipant is a helper method to define mock.On call
//   - ctx
//   - participant
//   - round
func (_e *MockGameStore_Expecter) UpdateShowdownParticipant(ctx interface{}, participant interface{}, round interface{}) *MockGameStore_UpdateShowdownParticipant_Call {
	return &MockGameStore_UpdateShowdownParticipant_Call{Call: _e.mock.On("UpdateShowdownParticipant", ctx, participant, round)}
}

func (_c *MockGameStore_UpdateShowdownParticipant_Call) Run(run func(ctx context.Context, participant *models.ShowdownParticipant, round int)) *MockGameStore_UpdateShowdownParticipant_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.ShowdownParticipant), args[2].(int))
	})
	return _c
}

func (_c *MockGameStore_UpdateShowdownParticipant_Call) Return(err error) *MockGameStore_UpdateShowdownParticipant_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockGameStore_UpdateShowdownParticipant_Call) RunAndReturn(run func(ctx context.Context, participant *models.ShowdownParticipant, round int) error) *MockGameStore_UpdateShowdownParticipant_Call {
	_c.Call.Return(run)
	return _c
}

// UserRepo provides a mock function for the type MockGameStore
func (_mock *MockGameStore) UserRepo() repository.UserRepository {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for UserRepo")
	}

	var r0 repository.UserRepository
	if returnFunc, ok := ret.Get(0).(func() repository.UserRepository); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(repository.UserRepository)
		}
	}
	return r0
}

// MockGameStore_UserRepo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UserRepo'
type MockGameStore_UserRepo_Call struct {
	*mock.Call
}

// UserRepo is a helper method to define mock.On call
func (_e *MockGameStore_Expecter) UserRepo() *MockGameStore_UserRepo_Call {
	return &MockGameStore_UserRepo_Call{Call: _e.mock.On("UserRepo")}
}

func (_c *MockGameStore_UserRepo_Call) Run(run func()) *MockGameStore_UserRepo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockGameStore_UserRepo_Call) Return(userRepository repository.UserRepository) *MockGameStore_UserRepo_Call {
	_c.Call.Return(userRepository)
	return _c
}

func (_c *MockGameStore_UserRepo_Call) RunAndReturn(run func() repository.UserRepository) *MockGameStore_UserRepo_Call {
	_c.Call.Return(run)
	return _c
}
