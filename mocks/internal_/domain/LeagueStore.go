// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockLeagueStore creates a new instance of MockLeagueStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockLeagueStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockLeagueStore {
	mock := &MockLeagueStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockLeagueStore is an autogenerated mock type for the LeagueStore type
type MockLeagueStore struct {
	mock.Mock
}

type MockLeagueStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockLeagueStore) EXPECT() *MockLeagueStore_Expecter {
	return &MockLeagueStore_Expecter{mock: &_m.Mock}
}

// CreateLeague provides a mock function for the type MockLeagueStore
func (_mock *MockLeagueStore) CreateLeague(ctx context.Context, input models.CreateLeagueInput) (*models.League, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateLeague")
	}

	var r0 *models.League
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateLeagueInput) (*models.League, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateLeagueInput) *models.League); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.League)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateLeagueInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeagueStore_CreateLeague_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateLeague'
type MockLeagueStore_CreateLeague_Call struct {
	*mock.Call
}

// CreateLeague is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockLeagueStore_Expecter) CreateLeague(ctx interface{}, input interface{}) *MockLeagueStore_CreateLeague_Call {
	return &MockLeagueStore_CreateLeague_Call{Call: _e.mock.On("CreateLeague", ctx, input)}
}

func (_c *MockLeagueStore_CreateLeague_Call) Run(run func(ctx context.Context, input models.CreateLeagueInput)) *MockLeagueStore_CreateLeague_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.CreateLeagueInput))
	})
	return _c
}

func (_c *MockLeagueStore_CreateLeague_Call) Return(league *models.League, err error) *MockLeagueStore_CreateLeague_Call {
	_c.Call.Return(league, err)
	return _c
}

func (_c *MockLeagueStore_CreateLeague_Call) RunAndReturn(run func(ctx context.Context, input models.CreateLeagueInput) (*models.League, error)) *MockLeagueStore_CreateLeague_Call {
	_c.Call.Return(run)
	return _c
}

// GetLeague provides a mock function for the type MockLeagueStore
func (_mock *MockLeagueStore) GetLeague(ctx context.Context, id primitive.ObjectID) (*models.League, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetLeague")
	}

	var r0 *models.League
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.League, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.League); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.League)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeagueStore_GetLeague_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLeague'
type MockLeagueStore_GetLeague_Call struct {
	*mock.Call
}

// GetLeague is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockLeagueStore_Expecter) GetLeague(ctx interface{}, id interface{}) *MockLeagueStore_GetLeague_Call {
	return &MockLeagueStore_GetLeague_Call{Call: _e.mock.On("GetLeague", ctx, id)}
}

func (_c *MockLeagueStore_GetLeague_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockLeagueStore_GetLeague_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockLeagueStore_GetLeague_Call) Return(league *models.League, err error) *MockLeagueStore_GetLeague_Call {
	_c.Call.Return(league, err)
	return _c
}

func (_c *MockLeagueStore_GetLeague_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.League, error)) *MockLeagueStore_GetLeague_Call {
	_c.Call.Return(run)
	return _c
}

// GetLeagueLeaderboard provides a mock function for the type MockLeagueStore
func (_mock *MockLeagueStore) GetLeagueLeaderboard(ctx context.Context, leagueID primitive.ObjectID, page int, pageSize int) (*models.LeagueLeaderboardPage, error) {
	ret := _mock.Called(ctx, leagueID, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetLeagueLeaderboard")
	}

	var r0 *models.LeagueLeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) (*models.LeagueLeaderboardPage, error)); ok {
		return returnFunc(ctx, leagueID, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) *models.LeagueLeaderboardPage); ok {
		r0 = returnFunc(ctx, leagueID, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.LeagueLeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int, int) error); ok {
		r1 = returnFunc(ctx, leagueID, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeagueStore_GetLeagueLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLeagueLeaderboard'
type MockLeagueStore_GetLeagueLeaderboard_Call struct {
	*mock.Call
}

// GetLeagueLeaderboard is a helper method to define mock.On call
//   - ctx
//   - leagueID
//   - page
//   - pageSize
func (_e *MockLeagueStore_Expecter) GetLeagueLeaderboard(ctx interface{}, leagueID interface{}, page interface{}, pageSize interface{}) *MockLeagueStore_GetLeagueLeaderboard_Call {
	return &MockLeagueStore_GetLeagueLeaderboard_Call{Call: _e.mock.On("GetLeagueLeaderboard", ctx, leagueID, page, pageSize)}
}

func (_c *MockLeagueStore_GetLeagueLeaderboard_Call) Run(run func(ctx context.Context, leagueID primitive.ObjectID, page int, pageSize int)) *MockLeagueStore_GetLeagueLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int), args[3].(int))
	})
	return _c
}

func (_c *MockLeagueStore_GetLeagueLeaderboard_Call) Return(leagueLeaderboardPage *models.LeagueLeaderboardPage, err error) *MockLeagueStore_GetLeagueLeaderboard_Call {
	_c.Call.Return(leagueLeaderboardPage, err)
	return _c
}

func (_c *MockLeagueStore_GetLeagueLeaderboard_Call) RunAndReturn(run func(ctx context.Context, leagueID primitive.ObjectID, page int, pageSize int) (*models.LeagueLeaderboardPage, error)) *MockLeagueStore_GetLeagueLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetLeaguesByStatus provides a mock function for the type MockLeagueStore
func (_mock *MockLeagueStore) GetLeaguesByStatus(ctx context.Context, statuses []models.LeagueStatus, page *int, pageSize *int, sortDirection *string) (*models.PaginatedLeagues, error) {
	ret := _mock.Called(ctx, statuses, page, pageSize, sortDirection)

	if len(ret) == 0 {
		panic("no return value specified for GetLeaguesByStatus")
	}

	var r0 *models.PaginatedLeagues
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []models.LeagueStatus, *int, *int, *string) (*models.PaginatedLeagues, error)); ok {
		return returnFunc(ctx, statuses, page, pageSize, sortDirection)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []models.LeagueStatus, *int, *int, *string) *models.PaginatedLeagues); ok {
		r0 = returnFunc(ctx, statuses, page, pageSize, sortDirection)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PaginatedLeagues)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []models.LeagueStatus, *int, *int, *string) error); ok {
		r1 = returnFunc(ctx, statuses, page, pageSize, sortDirection)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeagueStore_GetLeaguesByStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLeaguesByStatus'
type MockLeagueStore_GetLeaguesByStatus_Call struct {
	*mock.Call
}

// GetLeaguesByStatus is a helper method to define mock.On call
//   - ctx
//   - statuses
//   - page
//   - pageSize
//   - sortDirection
func (_e *MockLeagueStore_Expecter) GetLeaguesByStatus(ctx interface{}, statuses interface{}, page interface{}, pageSize interface{}, sortDirection interface{}) *MockLeagueStore_GetLeaguesByStatus_Call {
	return &MockLeagueStore_GetLeaguesByStatus_Call{Call: _e.mock.On("GetLeaguesByStatus", ctx, statuses, page, pageSize, sortDirection)}
}

func (_c *MockLeagueStore_GetLeaguesByStatus_Call) Run(run func(ctx context.Context, statuses []models.LeagueStatus, page *int, pageSize *int, sortDirection *string)) *MockLeagueStore_GetLeaguesByStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]models.LeagueStatus), args[2].(*int), args[3].(*int), args[4].(*string))
	})
	return _c
}

func (_c *MockLeagueStore_GetLeaguesByStatus_Call) Return(paginatedLeagues *models.PaginatedLeagues, err error) *MockLeagueStore_GetLeaguesByStatus_Call {
	_c.Call.Return(paginatedLeagues, err)
	return _c
}

func (_c *MockLeagueStore_GetLeaguesByStatus_Call) RunAndReturn(run func(ctx context.Context, statuses []models.LeagueStatus, page *int, pageSize *int, sortDirection *string) (*models.PaginatedLeagues, error)) *MockLeagueStore_GetLeaguesByStatus_Call {
	_c.Call.Return(run)
	return _c
}

// JoinLeague provides a mock function for the type MockLeagueStore
func (_mock *MockLeagueStore) JoinLeague(ctx context.Context, joinLeagueInput *models.JoinLeagueInput) (bool, error) {
	ret := _mock.Called(ctx, joinLeagueInput)

	if len(ret) == 0 {
		panic("no return value specified for JoinLeague")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.JoinLeagueInput) (bool, error)); ok {
		return returnFunc(ctx, joinLeagueInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.JoinLeagueInput) bool); ok {
		r0 = returnFunc(ctx, joinLeagueInput)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.JoinLeagueInput) error); ok {
		r1 = returnFunc(ctx, joinLeagueInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeagueStore_JoinLeague_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'JoinLeague'
type MockLeagueStore_JoinLeague_Call struct {
	*mock.Call
}

// JoinLeague is a helper method to define mock.On call
//   - ctx
//   - joinLeagueInput
func (_e *MockLeagueStore_Expecter) JoinLeague(ctx interface{}, joinLeagueInput interface{}) *MockLeagueStore_JoinLeague_Call {
	return &MockLeagueStore_JoinLeague_Call{Call: _e.mock.On("JoinLeague", ctx, joinLeagueInput)}
}

func (_c *MockLeagueStore_JoinLeague_Call) Run(run func(ctx context.Context, joinLeagueInput *models.JoinLeagueInput)) *MockLeagueStore_JoinLeague_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.JoinLeagueInput))
	})
	return _c
}

func (_c *MockLeagueStore_JoinLeague_Call) Return(b bool, err error) *MockLeagueStore_JoinLeague_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockLeagueStore_JoinLeague_Call) RunAndReturn(run func(ctx context.Context, joinLeagueInput *models.JoinLeagueInput) (bool, error)) *MockLeagueStore_JoinLeague_Call {
	_c.Call.Return(run)
	return _c
}
