// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockPuzzleStore creates a new instance of MockPuzzleStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPuzzleStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPuzzleStore {
	mock := &MockPuzzleStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockPuzzleStore is an autogenerated mock type for the PuzzleStore type
type MockPuzzleStore struct {
	mock.Mock
}

type MockPuzzleStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPuzzleStore) EXPECT() *MockPuzzleStore_Expecter {
	return &MockPuzzleStore_Expecter{mock: &_m.Mock}
}

// GenerateDailyPuzzle provides a mock function for the type MockPuzzleStore
func (_mock *MockPuzzleStore) GenerateDailyPuzzle(ctx context.Context, puzzleType models.PuzzleType) (*models.Puzzle, error) {
	ret := _mock.Called(ctx, puzzleType)

	if len(ret) == 0 {
		panic("no return value specified for GenerateDailyPuzzle")
	}

	var r0 *models.Puzzle
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.PuzzleType) (*models.Puzzle, error)); ok {
		return returnFunc(ctx, puzzleType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.PuzzleType) *models.Puzzle); ok {
		r0 = returnFunc(ctx, puzzleType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Puzzle)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.PuzzleType) error); ok {
		r1 = returnFunc(ctx, puzzleType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleStore_GenerateDailyPuzzle_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GenerateDailyPuzzle'
type MockPuzzleStore_GenerateDailyPuzzle_Call struct {
	*mock.Call
}

// GenerateDailyPuzzle is a helper method to define mock.On call
//   - ctx
//   - puzzleType
func (_e *MockPuzzleStore_Expecter) GenerateDailyPuzzle(ctx interface{}, puzzleType interface{}) *MockPuzzleStore_GenerateDailyPuzzle_Call {
	return &MockPuzzleStore_GenerateDailyPuzzle_Call{Call: _e.mock.On("GenerateDailyPuzzle", ctx, puzzleType)}
}

func (_c *MockPuzzleStore_GenerateDailyPuzzle_Call) Run(run func(ctx context.Context, puzzleType models.PuzzleType)) *MockPuzzleStore_GenerateDailyPuzzle_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.PuzzleType))
	})
	return _c
}

func (_c *MockPuzzleStore_GenerateDailyPuzzle_Call) Return(puzzle *models.Puzzle, err error) *MockPuzzleStore_GenerateDailyPuzzle_Call {
	_c.Call.Return(puzzle, err)
	return _c
}

func (_c *MockPuzzleStore_GenerateDailyPuzzle_Call) RunAndReturn(run func(ctx context.Context, puzzleType models.PuzzleType) (*models.Puzzle, error)) *MockPuzzleStore_GenerateDailyPuzzle_Call {
	_c.Call.Return(run)
	return _c
}

// GetDailyPuzzle provides a mock function for the type MockPuzzleStore
func (_mock *MockPuzzleStore) GetDailyPuzzle(ctx context.Context, date string) (*models.Puzzle, error) {
	ret := _mock.Called(ctx, date)

	if len(ret) == 0 {
		panic("no return value specified for GetDailyPuzzle")
	}

	var r0 *models.Puzzle
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*models.Puzzle, error)); ok {
		return returnFunc(ctx, date)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *models.Puzzle); ok {
		r0 = returnFunc(ctx, date)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Puzzle)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, date)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleStore_GetDailyPuzzle_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDailyPuzzle'
type MockPuzzleStore_GetDailyPuzzle_Call struct {
	*mock.Call
}

// GetDailyPuzzle is a helper method to define mock.On call
//   - ctx
//   - date
func (_e *MockPuzzleStore_Expecter) GetDailyPuzzle(ctx interface{}, date interface{}) *MockPuzzleStore_GetDailyPuzzle_Call {
	return &MockPuzzleStore_GetDailyPuzzle_Call{Call: _e.mock.On("GetDailyPuzzle", ctx, date)}
}

func (_c *MockPuzzleStore_GetDailyPuzzle_Call) Run(run func(ctx context.Context, date string)) *MockPuzzleStore_GetDailyPuzzle_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockPuzzleStore_GetDailyPuzzle_Call) Return(puzzle *models.Puzzle, err error) *MockPuzzleStore_GetDailyPuzzle_Call {
	_c.Call.Return(puzzle, err)
	return _c
}

func (_c *MockPuzzleStore_GetDailyPuzzle_Call) RunAndReturn(run func(ctx context.Context, date string) (*models.Puzzle, error)) *MockPuzzleStore_GetDailyPuzzle_Call {
	_c.Call.Return(run)
	return _c
}

// GetDailyPuzzleByType provides a mock function for the type MockPuzzleStore
func (_mock *MockPuzzleStore) GetDailyPuzzleByType(ctx context.Context, date string, puzzleType models.PuzzleType) (*models.Puzzle, error) {
	ret := _mock.Called(ctx, date, puzzleType)

	if len(ret) == 0 {
		panic("no return value specified for GetDailyPuzzleByType")
	}

	var r0 *models.Puzzle
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, models.PuzzleType) (*models.Puzzle, error)); ok {
		return returnFunc(ctx, date, puzzleType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, models.PuzzleType) *models.Puzzle); ok {
		r0 = returnFunc(ctx, date, puzzleType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Puzzle)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, models.PuzzleType) error); ok {
		r1 = returnFunc(ctx, date, puzzleType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleStore_GetDailyPuzzleByType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDailyPuzzleByType'
type MockPuzzleStore_GetDailyPuzzleByType_Call struct {
	*mock.Call
}

// GetDailyPuzzleByType is a helper method to define mock.On call
//   - ctx
//   - date
//   - puzzleType
func (_e *MockPuzzleStore_Expecter) GetDailyPuzzleByType(ctx interface{}, date interface{}, puzzleType interface{}) *MockPuzzleStore_GetDailyPuzzleByType_Call {
	return &MockPuzzleStore_GetDailyPuzzleByType_Call{Call: _e.mock.On("GetDailyPuzzleByType", ctx, date, puzzleType)}
}

func (_c *MockPuzzleStore_GetDailyPuzzleByType_Call) Run(run func(ctx context.Context, date string, puzzleType models.PuzzleType)) *MockPuzzleStore_GetDailyPuzzleByType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(models.PuzzleType))
	})
	return _c
}

func (_c *MockPuzzleStore_GetDailyPuzzleByType_Call) Return(puzzle *models.Puzzle, err error) *MockPuzzleStore_GetDailyPuzzleByType_Call {
	_c.Call.Return(puzzle, err)
	return _c
}

func (_c *MockPuzzleStore_GetDailyPuzzleByType_Call) RunAndReturn(run func(ctx context.Context, date string, puzzleType models.PuzzleType) (*models.Puzzle, error)) *MockPuzzleStore_GetDailyPuzzleByType_Call {
	_c.Call.Return(run)
	return _c
}

// GetPuzzleSubmissionsByMonth provides a mock function for the type MockPuzzleStore
func (_mock *MockPuzzleStore) GetPuzzleSubmissionsByMonth(ctx context.Context, months []string) ([]*models.PuzzleMonthlySubmissionReport, error) {
	ret := _mock.Called(ctx, months)

	if len(ret) == 0 {
		panic("no return value specified for GetPuzzleSubmissionsByMonth")
	}

	var r0 []*models.PuzzleMonthlySubmissionReport
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string) ([]*models.PuzzleMonthlySubmissionReport, error)); ok {
		return returnFunc(ctx, months)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string) []*models.PuzzleMonthlySubmissionReport); ok {
		r0 = returnFunc(ctx, months)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.PuzzleMonthlySubmissionReport)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = returnFunc(ctx, months)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleStore_GetPuzzleSubmissionsByMonth_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPuzzleSubmissionsByMonth'
type MockPuzzleStore_GetPuzzleSubmissionsByMonth_Call struct {
	*mock.Call
}

// GetPuzzleSubmissionsByMonth is a helper method to define mock.On call
//   - ctx
//   - months
func (_e *MockPuzzleStore_Expecter) GetPuzzleSubmissionsByMonth(ctx interface{}, months interface{}) *MockPuzzleStore_GetPuzzleSubmissionsByMonth_Call {
	return &MockPuzzleStore_GetPuzzleSubmissionsByMonth_Call{Call: _e.mock.On("GetPuzzleSubmissionsByMonth", ctx, months)}
}

func (_c *MockPuzzleStore_GetPuzzleSubmissionsByMonth_Call) Run(run func(ctx context.Context, months []string)) *MockPuzzleStore_GetPuzzleSubmissionsByMonth_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]string))
	})
	return _c
}

func (_c *MockPuzzleStore_GetPuzzleSubmissionsByMonth_Call) Return(puzzleMonthlySubmissionReports []*models.PuzzleMonthlySubmissionReport, err error) *MockPuzzleStore_GetPuzzleSubmissionsByMonth_Call {
	_c.Call.Return(puzzleMonthlySubmissionReports, err)
	return _c
}

func (_c *MockPuzzleStore_GetPuzzleSubmissionsByMonth_Call) RunAndReturn(run func(ctx context.Context, months []string) ([]*models.PuzzleMonthlySubmissionReport, error)) *MockPuzzleStore_GetPuzzleSubmissionsByMonth_Call {
	_c.Call.Return(run)
	return _c
}

// GetPuzzleSubmissionsByMonthByType provides a mock function for the type MockPuzzleStore
func (_mock *MockPuzzleStore) GetPuzzleSubmissionsByMonthByType(ctx context.Context, months []string, puzzleType models.PuzzleType) ([]*models.PuzzleMonthlySubmissionReport, error) {
	ret := _mock.Called(ctx, months, puzzleType)

	if len(ret) == 0 {
		panic("no return value specified for GetPuzzleSubmissionsByMonthByType")
	}

	var r0 []*models.PuzzleMonthlySubmissionReport
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string, models.PuzzleType) ([]*models.PuzzleMonthlySubmissionReport, error)); ok {
		return returnFunc(ctx, months, puzzleType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string, models.PuzzleType) []*models.PuzzleMonthlySubmissionReport); ok {
		r0 = returnFunc(ctx, months, puzzleType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.PuzzleMonthlySubmissionReport)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []string, models.PuzzleType) error); ok {
		r1 = returnFunc(ctx, months, puzzleType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleStore_GetPuzzleSubmissionsByMonthByType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPuzzleSubmissionsByMonthByType'
type MockPuzzleStore_GetPuzzleSubmissionsByMonthByType_Call struct {
	*mock.Call
}

// GetPuzzleSubmissionsByMonthByType is a helper method to define mock.On call
//   - ctx
//   - months
//   - puzzleType
func (_e *MockPuzzleStore_Expecter) GetPuzzleSubmissionsByMonthByType(ctx interface{}, months interface{}, puzzleType interface{}) *MockPuzzleStore_GetPuzzleSubmissionsByMonthByType_Call {
	return &MockPuzzleStore_GetPuzzleSubmissionsByMonthByType_Call{Call: _e.mock.On("GetPuzzleSubmissionsByMonthByType", ctx, months, puzzleType)}
}

func (_c *MockPuzzleStore_GetPuzzleSubmissionsByMonthByType_Call) Run(run func(ctx context.Context, months []string, puzzleType models.PuzzleType)) *MockPuzzleStore_GetPuzzleSubmissionsByMonthByType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]string), args[2].(models.PuzzleType))
	})
	return _c
}

func (_c *MockPuzzleStore_GetPuzzleSubmissionsByMonthByType_Call) Return(puzzleMonthlySubmissionReports []*models.PuzzleMonthlySubmissionReport, err error) *MockPuzzleStore_GetPuzzleSubmissionsByMonthByType_Call {
	_c.Call.Return(puzzleMonthlySubmissionReports, err)
	return _c
}

func (_c *MockPuzzleStore_GetPuzzleSubmissionsByMonthByType_Call) RunAndReturn(run func(ctx context.Context, months []string, puzzleType models.PuzzleType) ([]*models.PuzzleMonthlySubmissionReport, error)) *MockPuzzleStore_GetPuzzleSubmissionsByMonthByType_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserPuzzleStats provides a mock function for the type MockPuzzleStore
func (_mock *MockPuzzleStore) GetUserPuzzleStats(ctx context.Context) (*models.PuzzleUserStats, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPuzzleStats")
	}

	var r0 *models.PuzzleUserStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.PuzzleUserStats, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.PuzzleUserStats); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleUserStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleStore_GetUserPuzzleStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserPuzzleStats'
type MockPuzzleStore_GetUserPuzzleStats_Call struct {
	*mock.Call
}

// GetUserPuzzleStats is a helper method to define mock.On call
//   - ctx
func (_e *MockPuzzleStore_Expecter) GetUserPuzzleStats(ctx interface{}) *MockPuzzleStore_GetUserPuzzleStats_Call {
	return &MockPuzzleStore_GetUserPuzzleStats_Call{Call: _e.mock.On("GetUserPuzzleStats", ctx)}
}

func (_c *MockPuzzleStore_GetUserPuzzleStats_Call) Run(run func(ctx context.Context)) *MockPuzzleStore_GetUserPuzzleStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockPuzzleStore_GetUserPuzzleStats_Call) Return(puzzleUserStats *models.PuzzleUserStats, err error) *MockPuzzleStore_GetUserPuzzleStats_Call {
	_c.Call.Return(puzzleUserStats, err)
	return _c
}

func (_c *MockPuzzleStore_GetUserPuzzleStats_Call) RunAndReturn(run func(ctx context.Context) (*models.PuzzleUserStats, error)) *MockPuzzleStore_GetUserPuzzleStats_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserPuzzleStatsByType provides a mock function for the type MockPuzzleStore
func (_mock *MockPuzzleStore) GetUserPuzzleStatsByType(ctx context.Context, puzzleType models.PuzzleType) (*models.PuzzleUserStats, error) {
	ret := _mock.Called(ctx, puzzleType)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPuzzleStatsByType")
	}

	var r0 *models.PuzzleUserStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.PuzzleType) (*models.PuzzleUserStats, error)); ok {
		return returnFunc(ctx, puzzleType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.PuzzleType) *models.PuzzleUserStats); ok {
		r0 = returnFunc(ctx, puzzleType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleUserStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.PuzzleType) error); ok {
		r1 = returnFunc(ctx, puzzleType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleStore_GetUserPuzzleStatsByType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserPuzzleStatsByType'
type MockPuzzleStore_GetUserPuzzleStatsByType_Call struct {
	*mock.Call
}

// GetUserPuzzleStatsByType is a helper method to define mock.On call
//   - ctx
//   - puzzleType
func (_e *MockPuzzleStore_Expecter) GetUserPuzzleStatsByType(ctx interface{}, puzzleType interface{}) *MockPuzzleStore_GetUserPuzzleStatsByType_Call {
	return &MockPuzzleStore_GetUserPuzzleStatsByType_Call{Call: _e.mock.On("GetUserPuzzleStatsByType", ctx, puzzleType)}
}

func (_c *MockPuzzleStore_GetUserPuzzleStatsByType_Call) Run(run func(ctx context.Context, puzzleType models.PuzzleType)) *MockPuzzleStore_GetUserPuzzleStatsByType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.PuzzleType))
	})
	return _c
}

func (_c *MockPuzzleStore_GetUserPuzzleStatsByType_Call) Return(puzzleUserStats *models.PuzzleUserStats, err error) *MockPuzzleStore_GetUserPuzzleStatsByType_Call {
	_c.Call.Return(puzzleUserStats, err)
	return _c
}

func (_c *MockPuzzleStore_GetUserPuzzleStatsByType_Call) RunAndReturn(run func(ctx context.Context, puzzleType models.PuzzleType) (*models.PuzzleUserStats, error)) *MockPuzzleStore_GetUserPuzzleStatsByType_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitPuzzleSolution provides a mock function for the type MockPuzzleStore
func (_mock *MockPuzzleStore) SubmitPuzzleSolution(ctx context.Context, puzzleID primitive.ObjectID, timeSpent int) (*models.PuzzleResult, error) {
	ret := _mock.Called(ctx, puzzleID, timeSpent)

	if len(ret) == 0 {
		panic("no return value specified for SubmitPuzzleSolution")
	}

	var r0 *models.PuzzleResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) (*models.PuzzleResult, error)); ok {
		return returnFunc(ctx, puzzleID, timeSpent)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) *models.PuzzleResult); ok {
		r0 = returnFunc(ctx, puzzleID, timeSpent)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int) error); ok {
		r1 = returnFunc(ctx, puzzleID, timeSpent)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleStore_SubmitPuzzleSolution_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitPuzzleSolution'
type MockPuzzleStore_SubmitPuzzleSolution_Call struct {
	*mock.Call
}

// SubmitPuzzleSolution is a helper method to define mock.On call
//   - ctx
//   - puzzleID
//   - timeSpent
func (_e *MockPuzzleStore_Expecter) SubmitPuzzleSolution(ctx interface{}, puzzleID interface{}, timeSpent interface{}) *MockPuzzleStore_SubmitPuzzleSolution_Call {
	return &MockPuzzleStore_SubmitPuzzleSolution_Call{Call: _e.mock.On("SubmitPuzzleSolution", ctx, puzzleID, timeSpent)}
}

func (_c *MockPuzzleStore_SubmitPuzzleSolution_Call) Run(run func(ctx context.Context, puzzleID primitive.ObjectID, timeSpent int)) *MockPuzzleStore_SubmitPuzzleSolution_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int))
	})
	return _c
}

func (_c *MockPuzzleStore_SubmitPuzzleSolution_Call) Return(puzzleResult *models.PuzzleResult, err error) *MockPuzzleStore_SubmitPuzzleSolution_Call {
	_c.Call.Return(puzzleResult, err)
	return _c
}

func (_c *MockPuzzleStore_SubmitPuzzleSolution_Call) RunAndReturn(run func(ctx context.Context, puzzleID primitive.ObjectID, timeSpent int) (*models.PuzzleResult, error)) *MockPuzzleStore_SubmitPuzzleSolution_Call {
	_c.Call.Return(run)
	return _c
}
