// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	mock "github.com/stretchr/testify/mock"
)

// NewMockSchedulerStore creates a new instance of MockSchedulerStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockSchedulerStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockSchedulerStore {
	mock := &MockSchedulerStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockSchedulerStore is an autogenerated mock type for the SchedulerStore type
type MockSchedulerStore struct {
	mock.Mock
}

type MockSchedulerStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockSchedulerStore) EXPECT() *MockSchedulerStore_Expecter {
	return &MockSchedulerStore_Expecter{mock: &_m.Mock}
}

// ProcessDueTasks provides a mock function for the type MockSchedulerStore
func (_mock *MockSchedulerStore) ProcessDueTasks() {
	_mock.Called()
	return
}

// MockSchedulerStore_ProcessDueTasks_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ProcessDueTasks'
type MockSchedulerStore_ProcessDueTasks_Call struct {
	*mock.Call
}

// ProcessDueTasks is a helper method to define mock.On call
func (_e *MockSchedulerStore_Expecter) ProcessDueTasks() *MockSchedulerStore_ProcessDueTasks_Call {
	return &MockSchedulerStore_ProcessDueTasks_Call{Call: _e.mock.On("ProcessDueTasks")}
}

func (_c *MockSchedulerStore_ProcessDueTasks_Call) Run(run func()) *MockSchedulerStore_ProcessDueTasks_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockSchedulerStore_ProcessDueTasks_Call) Return() *MockSchedulerStore_ProcessDueTasks_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockSchedulerStore_ProcessDueTasks_Call) RunAndReturn(run func()) *MockSchedulerStore_ProcessDueTasks_Call {
	_c.Run(run)
	return _c
}
