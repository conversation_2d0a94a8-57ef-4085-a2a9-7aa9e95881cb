// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockShowdownStore creates a new instance of MockShowdownStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockShowdownStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockShowdownStore {
	mock := &MockShowdownStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockShowdownStore is an autogenerated mock type for the ShowdownStore type
type MockShowdownStore struct {
	mock.Mock
}

type MockShowdownStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockShowdownStore) EXPECT() *MockShowdownStore_Expecter {
	return &MockShowdownStore_Expecter{mock: &_m.Mock}
}

// CheckShowdownPlayersStatus provides a mock function for the type MockShowdownStore
func (_mock *MockShowdownStore) CheckShowdownPlayersStatus(ctx context.Context, showdownId primitive.ObjectID, currentRound int) error {
	ret := _mock.Called(ctx, showdownId, currentRound)

	if len(ret) == 0 {
		panic("no return value specified for CheckShowdownPlayersStatus")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) error); ok {
		r0 = returnFunc(ctx, showdownId, currentRound)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownStore_CheckShowdownPlayersStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckShowdownPlayersStatus'
type MockShowdownStore_CheckShowdownPlayersStatus_Call struct {
	*mock.Call
}

// CheckShowdownPlayersStatus is a helper method to define mock.On call
//   - ctx
//   - showdownId
//   - currentRound
func (_e *MockShowdownStore_Expecter) CheckShowdownPlayersStatus(ctx interface{}, showdownId interface{}, currentRound interface{}) *MockShowdownStore_CheckShowdownPlayersStatus_Call {
	return &MockShowdownStore_CheckShowdownPlayersStatus_Call{Call: _e.mock.On("CheckShowdownPlayersStatus", ctx, showdownId, currentRound)}
}

func (_c *MockShowdownStore_CheckShowdownPlayersStatus_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, currentRound int)) *MockShowdownStore_CheckShowdownPlayersStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int))
	})
	return _c
}

func (_c *MockShowdownStore_CheckShowdownPlayersStatus_Call) Return(err error) *MockShowdownStore_CheckShowdownPlayersStatus_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownStore_CheckShowdownPlayersStatus_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, currentRound int) error) *MockShowdownStore_CheckShowdownPlayersStatus_Call {
	_c.Call.Return(run)
	return _c
}

// CreateFixtures provides a mock function for the type MockShowdownStore
func (_mock *MockShowdownStore) CreateFixtures(ctx context.Context, showdownId primitive.ObjectID, currentRound int) error {
	ret := _mock.Called(ctx, showdownId, currentRound)

	if len(ret) == 0 {
		panic("no return value specified for CreateFixtures")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) error); ok {
		r0 = returnFunc(ctx, showdownId, currentRound)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownStore_CreateFixtures_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFixtures'
type MockShowdownStore_CreateFixtures_Call struct {
	*mock.Call
}

// CreateFixtures is a helper method to define mock.On call
//   - ctx
//   - showdownId
//   - currentRound
func (_e *MockShowdownStore_Expecter) CreateFixtures(ctx interface{}, showdownId interface{}, currentRound interface{}) *MockShowdownStore_CreateFixtures_Call {
	return &MockShowdownStore_CreateFixtures_Call{Call: _e.mock.On("CreateFixtures", ctx, showdownId, currentRound)}
}

func (_c *MockShowdownStore_CreateFixtures_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, currentRound int)) *MockShowdownStore_CreateFixtures_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int))
	})
	return _c
}

func (_c *MockShowdownStore_CreateFixtures_Call) Return(err error) *MockShowdownStore_CreateFixtures_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownStore_CreateFixtures_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, currentRound int) error) *MockShowdownStore_CreateFixtures_Call {
	_c.Call.Return(run)
	return _c
}

// CreateShowdown provides a mock function for the type MockShowdownStore
func (_mock *MockShowdownStore) CreateShowdown(ctx context.Context, input models.CreateShowdownInput) (*models.Showdown, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateShowdown")
	}

	var r0 *models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateShowdownInput) (*models.Showdown, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateShowdownInput) *models.Showdown); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateShowdownInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownStore_CreateShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateShowdown'
type MockShowdownStore_CreateShowdown_Call struct {
	*mock.Call
}

// CreateShowdown is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockShowdownStore_Expecter) CreateShowdown(ctx interface{}, input interface{}) *MockShowdownStore_CreateShowdown_Call {
	return &MockShowdownStore_CreateShowdown_Call{Call: _e.mock.On("CreateShowdown", ctx, input)}
}

func (_c *MockShowdownStore_CreateShowdown_Call) Run(run func(ctx context.Context, input models.CreateShowdownInput)) *MockShowdownStore_CreateShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.CreateShowdownInput))
	})
	return _c
}

func (_c *MockShowdownStore_CreateShowdown_Call) Return(showdown *models.Showdown, err error) *MockShowdownStore_CreateShowdown_Call {
	_c.Call.Return(showdown, err)
	return _c
}

func (_c *MockShowdownStore_CreateShowdown_Call) RunAndReturn(run func(ctx context.Context, input models.CreateShowdownInput) (*models.Showdown, error)) *MockShowdownStore_CreateShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeaturedShowdown provides a mock function for the type MockShowdownStore
func (_mock *MockShowdownStore) GetFeaturedShowdown(ctx context.Context) ([]*models.Showdown, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetFeaturedShowdown")
	}

	var r0 []*models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.Showdown, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.Showdown); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownStore_GetFeaturedShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeaturedShowdown'
type MockShowdownStore_GetFeaturedShowdown_Call struct {
	*mock.Call
}

// GetFeaturedShowdown is a helper method to define mock.On call
//   - ctx
func (_e *MockShowdownStore_Expecter) GetFeaturedShowdown(ctx interface{}) *MockShowdownStore_GetFeaturedShowdown_Call {
	return &MockShowdownStore_GetFeaturedShowdown_Call{Call: _e.mock.On("GetFeaturedShowdown", ctx)}
}

func (_c *MockShowdownStore_GetFeaturedShowdown_Call) Run(run func(ctx context.Context)) *MockShowdownStore_GetFeaturedShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockShowdownStore_GetFeaturedShowdown_Call) Return(showdowns []*models.Showdown, err error) *MockShowdownStore_GetFeaturedShowdown_Call {
	_c.Call.Return(showdowns, err)
	return _c
}

func (_c *MockShowdownStore_GetFeaturedShowdown_Call) RunAndReturn(run func(ctx context.Context) ([]*models.Showdown, error)) *MockShowdownStore_GetFeaturedShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// GetFixturesByShowdownId provides a mock function for the type MockShowdownStore
func (_mock *MockShowdownStore) GetFixturesByShowdownId(ctx context.Context, showdownID primitive.ObjectID) (*models.FicturesCollection, error) {
	ret := _mock.Called(ctx, showdownID)

	if len(ret) == 0 {
		panic("no return value specified for GetFixturesByShowdownId")
	}

	var r0 *models.FicturesCollection
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.FicturesCollection, error)); ok {
		return returnFunc(ctx, showdownID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.FicturesCollection); ok {
		r0 = returnFunc(ctx, showdownID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.FicturesCollection)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownStore_GetFixturesByShowdownId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFixturesByShowdownId'
type MockShowdownStore_GetFixturesByShowdownId_Call struct {
	*mock.Call
}

// GetFixturesByShowdownId is a helper method to define mock.On call
//   - ctx
//   - showdownID
func (_e *MockShowdownStore_Expecter) GetFixturesByShowdownId(ctx interface{}, showdownID interface{}) *MockShowdownStore_GetFixturesByShowdownId_Call {
	return &MockShowdownStore_GetFixturesByShowdownId_Call{Call: _e.mock.On("GetFixturesByShowdownId", ctx, showdownID)}
}

func (_c *MockShowdownStore_GetFixturesByShowdownId_Call) Run(run func(ctx context.Context, showdownID primitive.ObjectID)) *MockShowdownStore_GetFixturesByShowdownId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownStore_GetFixturesByShowdownId_Call) Return(ficturesCollection *models.FicturesCollection, err error) *MockShowdownStore_GetFixturesByShowdownId_Call {
	_c.Call.Return(ficturesCollection, err)
	return _c
}

func (_c *MockShowdownStore_GetFixturesByShowdownId_Call) RunAndReturn(run func(ctx context.Context, showdownID primitive.ObjectID) (*models.FicturesCollection, error)) *MockShowdownStore_GetFixturesByShowdownId_Call {
	_c.Call.Return(run)
	return _c
}

// GetPaginatedLeaderboard provides a mock function for the type MockShowdownStore
func (_mock *MockShowdownStore) GetPaginatedLeaderboard(ctx context.Context, input *models.PaginatedLeaderboardInput) (*models.PaginatedLeaderboard, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetPaginatedLeaderboard")
	}

	var r0 *models.PaginatedLeaderboard
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PaginatedLeaderboardInput) (*models.PaginatedLeaderboard, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PaginatedLeaderboardInput) *models.PaginatedLeaderboard); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PaginatedLeaderboard)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.PaginatedLeaderboardInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownStore_GetPaginatedLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPaginatedLeaderboard'
type MockShowdownStore_GetPaginatedLeaderboard_Call struct {
	*mock.Call
}

// GetPaginatedLeaderboard is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockShowdownStore_Expecter) GetPaginatedLeaderboard(ctx interface{}, input interface{}) *MockShowdownStore_GetPaginatedLeaderboard_Call {
	return &MockShowdownStore_GetPaginatedLeaderboard_Call{Call: _e.mock.On("GetPaginatedLeaderboard", ctx, input)}
}

func (_c *MockShowdownStore_GetPaginatedLeaderboard_Call) Run(run func(ctx context.Context, input *models.PaginatedLeaderboardInput)) *MockShowdownStore_GetPaginatedLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.PaginatedLeaderboardInput))
	})
	return _c
}

func (_c *MockShowdownStore_GetPaginatedLeaderboard_Call) Return(paginatedLeaderboard *models.PaginatedLeaderboard, err error) *MockShowdownStore_GetPaginatedLeaderboard_Call {
	_c.Call.Return(paginatedLeaderboard, err)
	return _c
}

func (_c *MockShowdownStore_GetPaginatedLeaderboard_Call) RunAndReturn(run func(ctx context.Context, input *models.PaginatedLeaderboardInput) (*models.PaginatedLeaderboard, error)) *MockShowdownStore_GetPaginatedLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetShowdownByID provides a mock function for the type MockShowdownStore
func (_mock *MockShowdownStore) GetShowdownByID(ctx context.Context, showdownID primitive.ObjectID) (*models.Showdown, error) {
	ret := _mock.Called(ctx, showdownID)

	if len(ret) == 0 {
		panic("no return value specified for GetShowdownByID")
	}

	var r0 *models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Showdown, error)); ok {
		return returnFunc(ctx, showdownID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Showdown); ok {
		r0 = returnFunc(ctx, showdownID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownStore_GetShowdownByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShowdownByID'
type MockShowdownStore_GetShowdownByID_Call struct {
	*mock.Call
}

// GetShowdownByID is a helper method to define mock.On call
//   - ctx
//   - showdownID
func (_e *MockShowdownStore_Expecter) GetShowdownByID(ctx interface{}, showdownID interface{}) *MockShowdownStore_GetShowdownByID_Call {
	return &MockShowdownStore_GetShowdownByID_Call{Call: _e.mock.On("GetShowdownByID", ctx, showdownID)}
}

func (_c *MockShowdownStore_GetShowdownByID_Call) Run(run func(ctx context.Context, showdownID primitive.ObjectID)) *MockShowdownStore_GetShowdownByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownStore_GetShowdownByID_Call) Return(showdown *models.Showdown, err error) *MockShowdownStore_GetShowdownByID_Call {
	_c.Call.Return(showdown, err)
	return _c
}

func (_c *MockShowdownStore_GetShowdownByID_Call) RunAndReturn(run func(ctx context.Context, showdownID primitive.ObjectID) (*models.Showdown, error)) *MockShowdownStore_GetShowdownByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetShowdownsByStatus provides a mock function for the type MockShowdownStore
func (_mock *MockShowdownStore) GetShowdownsByStatus(ctx context.Context, statuses []models.ShowdownContestStatus, page *int, pageSize *int, sortDirection *string) (*models.PaginatedShowdowns, error) {
	ret := _mock.Called(ctx, statuses, page, pageSize, sortDirection)

	if len(ret) == 0 {
		panic("no return value specified for GetShowdownsByStatus")
	}

	var r0 *models.PaginatedShowdowns
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []models.ShowdownContestStatus, *int, *int, *string) (*models.PaginatedShowdowns, error)); ok {
		return returnFunc(ctx, statuses, page, pageSize, sortDirection)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []models.ShowdownContestStatus, *int, *int, *string) *models.PaginatedShowdowns); ok {
		r0 = returnFunc(ctx, statuses, page, pageSize, sortDirection)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PaginatedShowdowns)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []models.ShowdownContestStatus, *int, *int, *string) error); ok {
		r1 = returnFunc(ctx, statuses, page, pageSize, sortDirection)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownStore_GetShowdownsByStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShowdownsByStatus'
type MockShowdownStore_GetShowdownsByStatus_Call struct {
	*mock.Call
}

// GetShowdownsByStatus is a helper method to define mock.On call
//   - ctx
//   - statuses
//   - page
//   - pageSize
//   - sortDirection
func (_e *MockShowdownStore_Expecter) GetShowdownsByStatus(ctx interface{}, statuses interface{}, page interface{}, pageSize interface{}, sortDirection interface{}) *MockShowdownStore_GetShowdownsByStatus_Call {
	return &MockShowdownStore_GetShowdownsByStatus_Call{Call: _e.mock.On("GetShowdownsByStatus", ctx, statuses, page, pageSize, sortDirection)}
}

func (_c *MockShowdownStore_GetShowdownsByStatus_Call) Run(run func(ctx context.Context, statuses []models.ShowdownContestStatus, page *int, pageSize *int, sortDirection *string)) *MockShowdownStore_GetShowdownsByStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]models.ShowdownContestStatus), args[2].(*int), args[3].(*int), args[4].(*string))
	})
	return _c
}

func (_c *MockShowdownStore_GetShowdownsByStatus_Call) Return(paginatedShowdowns *models.PaginatedShowdowns, err error) *MockShowdownStore_GetShowdownsByStatus_Call {
	_c.Call.Return(paginatedShowdowns, err)
	return _c
}

func (_c *MockShowdownStore_GetShowdownsByStatus_Call) RunAndReturn(run func(ctx context.Context, statuses []models.ShowdownContestStatus, page *int, pageSize *int, sortDirection *string) (*models.PaginatedShowdowns, error)) *MockShowdownStore_GetShowdownsByStatus_Call {
	_c.Call.Return(run)
	return _c
}

// NotifyUsersAsFixturesCreated provides a mock function for the type MockShowdownStore
func (_mock *MockShowdownStore) NotifyUsersAsFixturesCreated(ctx context.Context, showdownId primitive.ObjectID, round int, byeParticipantId *primitive.ObjectID) error {
	ret := _mock.Called(ctx, showdownId, round, byeParticipantId)

	if len(ret) == 0 {
		panic("no return value specified for NotifyUsersAsFixturesCreated")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, *primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, showdownId, round, byeParticipantId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownStore_NotifyUsersAsFixturesCreated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NotifyUsersAsFixturesCreated'
type MockShowdownStore_NotifyUsersAsFixturesCreated_Call struct {
	*mock.Call
}

// NotifyUsersAsFixturesCreated is a helper method to define mock.On call
//   - ctx
//   - showdownId
//   - round
//   - byeParticipantId
func (_e *MockShowdownStore_Expecter) NotifyUsersAsFixturesCreated(ctx interface{}, showdownId interface{}, round interface{}, byeParticipantId interface{}) *MockShowdownStore_NotifyUsersAsFixturesCreated_Call {
	return &MockShowdownStore_NotifyUsersAsFixturesCreated_Call{Call: _e.mock.On("NotifyUsersAsFixturesCreated", ctx, showdownId, round, byeParticipantId)}
}

func (_c *MockShowdownStore_NotifyUsersAsFixturesCreated_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, round int, byeParticipantId *primitive.ObjectID)) *MockShowdownStore_NotifyUsersAsFixturesCreated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int), args[3].(*primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownStore_NotifyUsersAsFixturesCreated_Call) Return(err error) *MockShowdownStore_NotifyUsersAsFixturesCreated_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownStore_NotifyUsersAsFixturesCreated_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, round int, byeParticipantId *primitive.ObjectID) error) *MockShowdownStore_NotifyUsersAsFixturesCreated_Call {
	_c.Call.Return(run)
	return _c
}

// NotifyUsersAsShowdownStarts provides a mock function for the type MockShowdownStore
func (_mock *MockShowdownStore) NotifyUsersAsShowdownStarts(ctx context.Context, showdownId primitive.ObjectID, round int) error {
	ret := _mock.Called(ctx, showdownId, round)

	if len(ret) == 0 {
		panic("no return value specified for NotifyUsersAsShowdownStarts")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) error); ok {
		r0 = returnFunc(ctx, showdownId, round)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownStore_NotifyUsersAsShowdownStarts_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NotifyUsersAsShowdownStarts'
type MockShowdownStore_NotifyUsersAsShowdownStarts_Call struct {
	*mock.Call
}

// NotifyUsersAsShowdownStarts is a helper method to define mock.On call
//   - ctx
//   - showdownId
//   - round
func (_e *MockShowdownStore_Expecter) NotifyUsersAsShowdownStarts(ctx interface{}, showdownId interface{}, round interface{}) *MockShowdownStore_NotifyUsersAsShowdownStarts_Call {
	return &MockShowdownStore_NotifyUsersAsShowdownStarts_Call{Call: _e.mock.On("NotifyUsersAsShowdownStarts", ctx, showdownId, round)}
}

func (_c *MockShowdownStore_NotifyUsersAsShowdownStarts_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, round int)) *MockShowdownStore_NotifyUsersAsShowdownStarts_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int))
	})
	return _c
}

func (_c *MockShowdownStore_NotifyUsersAsShowdownStarts_Call) Return(err error) *MockShowdownStore_NotifyUsersAsShowdownStarts_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownStore_NotifyUsersAsShowdownStarts_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, round int) error) *MockShowdownStore_NotifyUsersAsShowdownStarts_Call {
	_c.Call.Return(run)
	return _c
}

// OnFixtureCreation provides a mock function for the type MockShowdownStore
func (_mock *MockShowdownStore) OnFixtureCreation(ctx context.Context, showdownId primitive.ObjectID, currentRound int) error {
	ret := _mock.Called(ctx, showdownId, currentRound)

	if len(ret) == 0 {
		panic("no return value specified for OnFixtureCreation")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) error); ok {
		r0 = returnFunc(ctx, showdownId, currentRound)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownStore_OnFixtureCreation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OnFixtureCreation'
type MockShowdownStore_OnFixtureCreation_Call struct {
	*mock.Call
}

// OnFixtureCreation is a helper method to define mock.On call
//   - ctx
//   - showdownId
//   - currentRound
func (_e *MockShowdownStore_Expecter) OnFixtureCreation(ctx interface{}, showdownId interface{}, currentRound interface{}) *MockShowdownStore_OnFixtureCreation_Call {
	return &MockShowdownStore_OnFixtureCreation_Call{Call: _e.mock.On("OnFixtureCreation", ctx, showdownId, currentRound)}
}

func (_c *MockShowdownStore_OnFixtureCreation_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, currentRound int)) *MockShowdownStore_OnFixtureCreation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int))
	})
	return _c
}

func (_c *MockShowdownStore_OnFixtureCreation_Call) Return(err error) *MockShowdownStore_OnFixtureCreation_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownStore_OnFixtureCreation_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, currentRound int) error) *MockShowdownStore_OnFixtureCreation_Call {
	_c.Call.Return(run)
	return _c
}

// OnShowdownEnd provides a mock function for the type MockShowdownStore
func (_mock *MockShowdownStore) OnShowdownEnd(ctx context.Context, showdownId primitive.ObjectID) error {
	ret := _mock.Called(ctx, showdownId)

	if len(ret) == 0 {
		panic("no return value specified for OnShowdownEnd")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, showdownId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownStore_OnShowdownEnd_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OnShowdownEnd'
type MockShowdownStore_OnShowdownEnd_Call struct {
	*mock.Call
}

// OnShowdownEnd is a helper method to define mock.On call
//   - ctx
//   - showdownId
func (_e *MockShowdownStore_Expecter) OnShowdownEnd(ctx interface{}, showdownId interface{}) *MockShowdownStore_OnShowdownEnd_Call {
	return &MockShowdownStore_OnShowdownEnd_Call{Call: _e.mock.On("OnShowdownEnd", ctx, showdownId)}
}

func (_c *MockShowdownStore_OnShowdownEnd_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID)) *MockShowdownStore_OnShowdownEnd_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownStore_OnShowdownEnd_Call) Return(err error) *MockShowdownStore_OnShowdownEnd_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownStore_OnShowdownEnd_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID) error) *MockShowdownStore_OnShowdownEnd_Call {
	_c.Call.Return(run)
	return _c
}

// RegisterForShowdown provides a mock function for the type MockShowdownStore
func (_mock *MockShowdownStore) RegisterForShowdown(ctx context.Context, input models.ShowdownRegistrationFormValuesInput) (bool, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for RegisterForShowdown")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.ShowdownRegistrationFormValuesInput) (bool, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.ShowdownRegistrationFormValuesInput) bool); ok {
		r0 = returnFunc(ctx, input)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.ShowdownRegistrationFormValuesInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownStore_RegisterForShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RegisterForShowdown'
type MockShowdownStore_RegisterForShowdown_Call struct {
	*mock.Call
}

// RegisterForShowdown is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockShowdownStore_Expecter) RegisterForShowdown(ctx interface{}, input interface{}) *MockShowdownStore_RegisterForShowdown_Call {
	return &MockShowdownStore_RegisterForShowdown_Call{Call: _e.mock.On("RegisterForShowdown", ctx, input)}
}

func (_c *MockShowdownStore_RegisterForShowdown_Call) Run(run func(ctx context.Context, input models.ShowdownRegistrationFormValuesInput)) *MockShowdownStore_RegisterForShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.ShowdownRegistrationFormValuesInput))
	})
	return _c
}

func (_c *MockShowdownStore_RegisterForShowdown_Call) Return(b bool, err error) *MockShowdownStore_RegisterForShowdown_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockShowdownStore_RegisterForShowdown_Call) RunAndReturn(run func(ctx context.Context, input models.ShowdownRegistrationFormValuesInput) (bool, error)) *MockShowdownStore_RegisterForShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// UnregisterFromShowdown provides a mock function for the type MockShowdownStore
func (_mock *MockShowdownStore) UnregisterFromShowdown(ctx context.Context, showdownID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, showdownID)

	if len(ret) == 0 {
		panic("no return value specified for UnregisterFromShowdown")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, showdownID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, showdownID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownStore_UnregisterFromShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UnregisterFromShowdown'
type MockShowdownStore_UnregisterFromShowdown_Call struct {
	*mock.Call
}

// UnregisterFromShowdown is a helper method to define mock.On call
//   - ctx
//   - showdownID
func (_e *MockShowdownStore_Expecter) UnregisterFromShowdown(ctx interface{}, showdownID interface{}) *MockShowdownStore_UnregisterFromShowdown_Call {
	return &MockShowdownStore_UnregisterFromShowdown_Call{Call: _e.mock.On("UnregisterFromShowdown", ctx, showdownID)}
}

func (_c *MockShowdownStore_UnregisterFromShowdown_Call) Run(run func(ctx context.Context, showdownID primitive.ObjectID)) *MockShowdownStore_UnregisterFromShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownStore_UnregisterFromShowdown_Call) Return(b bool, err error) *MockShowdownStore_UnregisterFromShowdown_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockShowdownStore_UnregisterFromShowdown_Call) RunAndReturn(run func(ctx context.Context, showdownID primitive.ObjectID) (bool, error)) *MockShowdownStore_UnregisterFromShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateShowdownCurrentRound provides a mock function for the type MockShowdownStore
func (_mock *MockShowdownStore) UpdateShowdownCurrentRound(ctx context.Context, showdownId primitive.ObjectID, round int) error {
	ret := _mock.Called(ctx, showdownId, round)

	if len(ret) == 0 {
		panic("no return value specified for UpdateShowdownCurrentRound")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) error); ok {
		r0 = returnFunc(ctx, showdownId, round)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownStore_UpdateShowdownCurrentRound_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateShowdownCurrentRound'
type MockShowdownStore_UpdateShowdownCurrentRound_Call struct {
	*mock.Call
}

// UpdateShowdownCurrentRound is a helper method to define mock.On call
//   - ctx
//   - showdownId
//   - round
func (_e *MockShowdownStore_Expecter) UpdateShowdownCurrentRound(ctx interface{}, showdownId interface{}, round interface{}) *MockShowdownStore_UpdateShowdownCurrentRound_Call {
	return &MockShowdownStore_UpdateShowdownCurrentRound_Call{Call: _e.mock.On("UpdateShowdownCurrentRound", ctx, showdownId, round)}
}

func (_c *MockShowdownStore_UpdateShowdownCurrentRound_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, round int)) *MockShowdownStore_UpdateShowdownCurrentRound_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int))
	})
	return _c
}

func (_c *MockShowdownStore_UpdateShowdownCurrentRound_Call) Return(err error) *MockShowdownStore_UpdateShowdownCurrentRound_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownStore_UpdateShowdownCurrentRound_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, round int) error) *MockShowdownStore_UpdateShowdownCurrentRound_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateShowdownStatus provides a mock function for the type MockShowdownStore
func (_mock *MockShowdownStore) UpdateShowdownStatus(ctx context.Context, showdownId primitive.ObjectID, status models.ShowdownContestStatus) error {
	ret := _mock.Called(ctx, showdownId, status)

	if len(ret) == 0 {
		panic("no return value specified for UpdateShowdownStatus")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.ShowdownContestStatus) error); ok {
		r0 = returnFunc(ctx, showdownId, status)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownStore_UpdateShowdownStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateShowdownStatus'
type MockShowdownStore_UpdateShowdownStatus_Call struct {
	*mock.Call
}

// UpdateShowdownStatus is a helper method to define mock.On call
//   - ctx
//   - showdownId
//   - status
func (_e *MockShowdownStore_Expecter) UpdateShowdownStatus(ctx interface{}, showdownId interface{}, status interface{}) *MockShowdownStore_UpdateShowdownStatus_Call {
	return &MockShowdownStore_UpdateShowdownStatus_Call{Call: _e.mock.On("UpdateShowdownStatus", ctx, showdownId, status)}
}

func (_c *MockShowdownStore_UpdateShowdownStatus_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, status models.ShowdownContestStatus)) *MockShowdownStore_UpdateShowdownStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(models.ShowdownContestStatus))
	})
	return _c
}

func (_c *MockShowdownStore_UpdateShowdownStatus_Call) Return(err error) *MockShowdownStore_UpdateShowdownStatus_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownStore_UpdateShowdownStatus_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, status models.ShowdownContestStatus) error) *MockShowdownStore_UpdateShowdownStatus_Call {
	_c.Call.Return(run)
	return _c
}
