// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"
	"time"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockStreakShieldTransactionStore creates a new instance of MockStreakShieldTransactionStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockStreakShieldTransactionStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockStreakShieldTransactionStore {
	mock := &MockStreakShieldTransactionStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockStreakShieldTransactionStore is an autogenerated mock type for the StreakShieldTransactionStore type
type MockStreakShieldTransactionStore struct {
	mock.Mock
}

type MockStreakShieldTransactionStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockStreakShieldTransactionStore) EXPECT() *MockStreakShieldTransactionStore_Expecter {
	return &MockStreakShieldTransactionStore_Expecter{mock: &_m.Mock}
}

// AddRedemptionDate provides a mock function for the type MockStreakShieldTransactionStore
func (_mock *MockStreakShieldTransactionStore) AddRedemptionDate(ctx context.Context, transactionID primitive.ObjectID, redemptionDate time.Time) error {
	ret := _mock.Called(ctx, transactionID, redemptionDate)

	if len(ret) == 0 {
		panic("no return value specified for AddRedemptionDate")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, time.Time) error); ok {
		r0 = returnFunc(ctx, transactionID, redemptionDate)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockStreakShieldTransactionStore_AddRedemptionDate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddRedemptionDate'
type MockStreakShieldTransactionStore_AddRedemptionDate_Call struct {
	*mock.Call
}

// AddRedemptionDate is a helper method to define mock.On call
//   - ctx
//   - transactionID
//   - redemptionDate
func (_e *MockStreakShieldTransactionStore_Expecter) AddRedemptionDate(ctx interface{}, transactionID interface{}, redemptionDate interface{}) *MockStreakShieldTransactionStore_AddRedemptionDate_Call {
	return &MockStreakShieldTransactionStore_AddRedemptionDate_Call{Call: _e.mock.On("AddRedemptionDate", ctx, transactionID, redemptionDate)}
}

func (_c *MockStreakShieldTransactionStore_AddRedemptionDate_Call) Run(run func(ctx context.Context, transactionID primitive.ObjectID, redemptionDate time.Time)) *MockStreakShieldTransactionStore_AddRedemptionDate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(time.Time))
	})
	return _c
}

func (_c *MockStreakShieldTransactionStore_AddRedemptionDate_Call) Return(err error) *MockStreakShieldTransactionStore_AddRedemptionDate_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockStreakShieldTransactionStore_AddRedemptionDate_Call) RunAndReturn(run func(ctx context.Context, transactionID primitive.ObjectID, redemptionDate time.Time) error) *MockStreakShieldTransactionStore_AddRedemptionDate_Call {
	_c.Call.Return(run)
	return _c
}

// CreateTransaction provides a mock function for the type MockStreakShieldTransactionStore
func (_mock *MockStreakShieldTransactionStore) CreateTransaction(ctx context.Context, userID primitive.ObjectID, quantity int, transactionType models.TransactionType, earnVia *models.EarnVia, referralID *primitive.ObjectID, transactionID *string) (*models.StreakShieldTransaction, error) {
	ret := _mock.Called(ctx, userID, quantity, transactionType, earnVia, referralID, transactionID)

	if len(ret) == 0 {
		panic("no return value specified for CreateTransaction")
	}

	var r0 *models.StreakShieldTransaction
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, models.TransactionType, *models.EarnVia, *primitive.ObjectID, *string) (*models.StreakShieldTransaction, error)); ok {
		return returnFunc(ctx, userID, quantity, transactionType, earnVia, referralID, transactionID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, models.TransactionType, *models.EarnVia, *primitive.ObjectID, *string) *models.StreakShieldTransaction); ok {
		r0 = returnFunc(ctx, userID, quantity, transactionType, earnVia, referralID, transactionID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.StreakShieldTransaction)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int, models.TransactionType, *models.EarnVia, *primitive.ObjectID, *string) error); ok {
		r1 = returnFunc(ctx, userID, quantity, transactionType, earnVia, referralID, transactionID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockStreakShieldTransactionStore_CreateTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateTransaction'
type MockStreakShieldTransactionStore_CreateTransaction_Call struct {
	*mock.Call
}

// CreateTransaction is a helper method to define mock.On call
//   - ctx
//   - userID
//   - quantity
//   - transactionType
//   - earnVia
//   - referralID
//   - transactionID
func (_e *MockStreakShieldTransactionStore_Expecter) CreateTransaction(ctx interface{}, userID interface{}, quantity interface{}, transactionType interface{}, earnVia interface{}, referralID interface{}, transactionID interface{}) *MockStreakShieldTransactionStore_CreateTransaction_Call {
	return &MockStreakShieldTransactionStore_CreateTransaction_Call{Call: _e.mock.On("CreateTransaction", ctx, userID, quantity, transactionType, earnVia, referralID, transactionID)}
}

func (_c *MockStreakShieldTransactionStore_CreateTransaction_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, quantity int, transactionType models.TransactionType, earnVia *models.EarnVia, referralID *primitive.ObjectID, transactionID *string)) *MockStreakShieldTransactionStore_CreateTransaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int), args[3].(models.TransactionType), args[4].(*models.EarnVia), args[5].(*primitive.ObjectID), args[6].(*string))
	})
	return _c
}

func (_c *MockStreakShieldTransactionStore_CreateTransaction_Call) Return(streakShieldTransaction *models.StreakShieldTransaction, err error) *MockStreakShieldTransactionStore_CreateTransaction_Call {
	_c.Call.Return(streakShieldTransaction, err)
	return _c
}

func (_c *MockStreakShieldTransactionStore_CreateTransaction_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, quantity int, transactionType models.TransactionType, earnVia *models.EarnVia, referralID *primitive.ObjectID, transactionID *string) (*models.StreakShieldTransaction, error)) *MockStreakShieldTransactionStore_CreateTransaction_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllTransactions provides a mock function for the type MockStreakShieldTransactionStore
func (_mock *MockStreakShieldTransactionStore) GetAllTransactions(ctx context.Context, page int, pageSize int) ([]*models.StreakShieldTransaction, int, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetAllTransactions")
	}

	var r0 []*models.StreakShieldTransaction
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, int) ([]*models.StreakShieldTransaction, int, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, int) []*models.StreakShieldTransaction); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.StreakShieldTransaction)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int, int) int); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, int, int) error); ok {
		r2 = returnFunc(ctx, page, pageSize)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// MockStreakShieldTransactionStore_GetAllTransactions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllTransactions'
type MockStreakShieldTransactionStore_GetAllTransactions_Call struct {
	*mock.Call
}

// GetAllTransactions is a helper method to define mock.On call
//   - ctx
//   - page
//   - pageSize
func (_e *MockStreakShieldTransactionStore_Expecter) GetAllTransactions(ctx interface{}, page interface{}, pageSize interface{}) *MockStreakShieldTransactionStore_GetAllTransactions_Call {
	return &MockStreakShieldTransactionStore_GetAllTransactions_Call{Call: _e.mock.On("GetAllTransactions", ctx, page, pageSize)}
}

func (_c *MockStreakShieldTransactionStore_GetAllTransactions_Call) Run(run func(ctx context.Context, page int, pageSize int)) *MockStreakShieldTransactionStore_GetAllTransactions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(int))
	})
	return _c
}

func (_c *MockStreakShieldTransactionStore_GetAllTransactions_Call) Return(streakShieldTransactions []*models.StreakShieldTransaction, n int, err error) *MockStreakShieldTransactionStore_GetAllTransactions_Call {
	_c.Call.Return(streakShieldTransactions, n, err)
	return _c
}

func (_c *MockStreakShieldTransactionStore_GetAllTransactions_Call) RunAndReturn(run func(ctx context.Context, page int, pageSize int) ([]*models.StreakShieldTransaction, int, error)) *MockStreakShieldTransactionStore_GetAllTransactions_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserStreakShieldTransactions provides a mock function for the type MockStreakShieldTransactionStore
func (_mock *MockStreakShieldTransactionStore) GetUserStreakShieldTransactions(ctx context.Context, page int, pageSize int) (*models.StreakShieldTransactionPage, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetUserStreakShieldTransactions")
	}

	var r0 *models.StreakShieldTransactionPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, int) (*models.StreakShieldTransactionPage, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, int) *models.StreakShieldTransactionPage); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.StreakShieldTransactionPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int, int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockStreakShieldTransactionStore_GetUserStreakShieldTransactions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserStreakShieldTransactions'
type MockStreakShieldTransactionStore_GetUserStreakShieldTransactions_Call struct {
	*mock.Call
}

// GetUserStreakShieldTransactions is a helper method to define mock.On call
//   - ctx
//   - page
//   - pageSize
func (_e *MockStreakShieldTransactionStore_Expecter) GetUserStreakShieldTransactions(ctx interface{}, page interface{}, pageSize interface{}) *MockStreakShieldTransactionStore_GetUserStreakShieldTransactions_Call {
	return &MockStreakShieldTransactionStore_GetUserStreakShieldTransactions_Call{Call: _e.mock.On("GetUserStreakShieldTransactions", ctx, page, pageSize)}
}

func (_c *MockStreakShieldTransactionStore_GetUserStreakShieldTransactions_Call) Run(run func(ctx context.Context, page int, pageSize int)) *MockStreakShieldTransactionStore_GetUserStreakShieldTransactions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(int))
	})
	return _c
}

func (_c *MockStreakShieldTransactionStore_GetUserStreakShieldTransactions_Call) Return(streakShieldTransactionPage *models.StreakShieldTransactionPage, err error) *MockStreakShieldTransactionStore_GetUserStreakShieldTransactions_Call {
	_c.Call.Return(streakShieldTransactionPage, err)
	return _c
}

func (_c *MockStreakShieldTransactionStore_GetUserStreakShieldTransactions_Call) RunAndReturn(run func(ctx context.Context, page int, pageSize int) (*models.StreakShieldTransactionPage, error)) *MockStreakShieldTransactionStore_GetUserStreakShieldTransactions_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserTransactions provides a mock function for the type MockStreakShieldTransactionStore
func (_mock *MockStreakShieldTransactionStore) GetUserTransactions(ctx context.Context, userID primitive.ObjectID, page int, pageSize int) ([]*models.StreakShieldTransaction, int, error) {
	ret := _mock.Called(ctx, userID, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetUserTransactions")
	}

	var r0 []*models.StreakShieldTransaction
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) ([]*models.StreakShieldTransaction, int, error)); ok {
		return returnFunc(ctx, userID, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) []*models.StreakShieldTransaction); ok {
		r0 = returnFunc(ctx, userID, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.StreakShieldTransaction)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int, int) int); ok {
		r1 = returnFunc(ctx, userID, page, pageSize)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, primitive.ObjectID, int, int) error); ok {
		r2 = returnFunc(ctx, userID, page, pageSize)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// MockStreakShieldTransactionStore_GetUserTransactions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserTransactions'
type MockStreakShieldTransactionStore_GetUserTransactions_Call struct {
	*mock.Call
}

// GetUserTransactions is a helper method to define mock.On call
//   - ctx
//   - userID
//   - page
//   - pageSize
func (_e *MockStreakShieldTransactionStore_Expecter) GetUserTransactions(ctx interface{}, userID interface{}, page interface{}, pageSize interface{}) *MockStreakShieldTransactionStore_GetUserTransactions_Call {
	return &MockStreakShieldTransactionStore_GetUserTransactions_Call{Call: _e.mock.On("GetUserTransactions", ctx, userID, page, pageSize)}
}

func (_c *MockStreakShieldTransactionStore_GetUserTransactions_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, page int, pageSize int)) *MockStreakShieldTransactionStore_GetUserTransactions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int), args[3].(int))
	})
	return _c
}

func (_c *MockStreakShieldTransactionStore_GetUserTransactions_Call) Return(streakShieldTransactions []*models.StreakShieldTransaction, n int, err error) *MockStreakShieldTransactionStore_GetUserTransactions_Call {
	_c.Call.Return(streakShieldTransactions, n, err)
	return _c
}

func (_c *MockStreakShieldTransactionStore_GetUserTransactions_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, page int, pageSize int) ([]*models.StreakShieldTransaction, int, error)) *MockStreakShieldTransactionStore_GetUserTransactions_Call {
	_c.Call.Return(run)
	return _c
}
