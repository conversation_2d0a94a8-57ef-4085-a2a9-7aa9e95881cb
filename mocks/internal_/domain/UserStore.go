// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/models"

	"github.com/99designs/gqlgen/graphql"
	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockUserStore creates a new instance of MockUserStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserStore {
	mock := &MockUserStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockUserStore is an autogenerated mock type for the UserStore type
type MockUserStore struct {
	mock.Mock
}

type MockUserStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserStore) EXPECT() *MockUserStore_Expecter {
	return &MockUserStore_Expecter{mock: &_m.Mock}
}

// CountUsersWithHigherRating provides a mock function for the type MockUserStore
func (_mock *MockUserStore) CountUsersWithHigherRating(ctx context.Context, userRepo repository.UserRepository, rating int) (int, error) {
	ret := _mock.Called(ctx, userRepo, rating)

	if len(ret) == 0 {
		panic("no return value specified for CountUsersWithHigherRating")
	}

	var r0 int
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, repository.UserRepository, int) (int, error)); ok {
		return returnFunc(ctx, userRepo, rating)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, repository.UserRepository, int) int); ok {
		r0 = returnFunc(ctx, userRepo, rating)
	} else {
		r0 = ret.Get(0).(int)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, repository.UserRepository, int) error); ok {
		r1 = returnFunc(ctx, userRepo, rating)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_CountUsersWithHigherRating_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountUsersWithHigherRating'
type MockUserStore_CountUsersWithHigherRating_Call struct {
	*mock.Call
}

// CountUsersWithHigherRating is a helper method to define mock.On call
//   - ctx
//   - userRepo
//   - rating
func (_e *MockUserStore_Expecter) CountUsersWithHigherRating(ctx interface{}, userRepo interface{}, rating interface{}) *MockUserStore_CountUsersWithHigherRating_Call {
	return &MockUserStore_CountUsersWithHigherRating_Call{Call: _e.mock.On("CountUsersWithHigherRating", ctx, userRepo, rating)}
}

func (_c *MockUserStore_CountUsersWithHigherRating_Call) Run(run func(ctx context.Context, userRepo repository.UserRepository, rating int)) *MockUserStore_CountUsersWithHigherRating_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.UserRepository), args[2].(int))
	})
	return _c
}

func (_c *MockUserStore_CountUsersWithHigherRating_Call) Return(n int, err error) *MockUserStore_CountUsersWithHigherRating_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockUserStore_CountUsersWithHigherRating_Call) RunAndReturn(run func(ctx context.Context, userRepo repository.UserRepository, rating int) (int, error)) *MockUserStore_CountUsersWithHigherRating_Call {
	_c.Call.Return(run)
	return _c
}

// CreateUser provides a mock function for the type MockUserStore
func (_mock *MockUserStore) CreateUser(ctx context.Context, input *models.UserInput) (*models.User, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateUser")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserInput) (*models.User, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserInput) *models.User); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.UserInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_CreateUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUser'
type MockUserStore_CreateUser_Call struct {
	*mock.Call
}

// CreateUser is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockUserStore_Expecter) CreateUser(ctx interface{}, input interface{}) *MockUserStore_CreateUser_Call {
	return &MockUserStore_CreateUser_Call{Call: _e.mock.On("CreateUser", ctx, input)}
}

func (_c *MockUserStore_CreateUser_Call) Run(run func(ctx context.Context, input *models.UserInput)) *MockUserStore_CreateUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.UserInput))
	})
	return _c
}

func (_c *MockUserStore_CreateUser_Call) Return(user *models.User, err error) *MockUserStore_CreateUser_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockUserStore_CreateUser_Call) RunAndReturn(run func(ctx context.Context, input *models.UserInput) (*models.User, error)) *MockUserStore_CreateUser_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUser provides a mock function for the type MockUserStore
func (_mock *MockUserStore) DeleteUser(ctx context.Context) (bool, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUser")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (bool, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) bool); ok {
		r0 = returnFunc(ctx)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_DeleteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUser'
type MockUserStore_DeleteUser_Call struct {
	*mock.Call
}

// DeleteUser is a helper method to define mock.On call
//   - ctx
func (_e *MockUserStore_Expecter) DeleteUser(ctx interface{}) *MockUserStore_DeleteUser_Call {
	return &MockUserStore_DeleteUser_Call{Call: _e.mock.On("DeleteUser", ctx)}
}

func (_c *MockUserStore_DeleteUser_Call) Run(run func(ctx context.Context)) *MockUserStore_DeleteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockUserStore_DeleteUser_Call) Return(b bool, err error) *MockUserStore_DeleteUser_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockUserStore_DeleteUser_Call) RunAndReturn(run func(ctx context.Context) (bool, error)) *MockUserStore_DeleteUser_Call {
	_c.Call.Return(run)
	return _c
}

// GenerateUniqueReferralCode provides a mock function for the type MockUserStore
func (_mock *MockUserStore) GenerateUniqueReferralCode(ctx context.Context) (string, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GenerateUniqueReferralCode")
	}

	var r0 string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (string, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) string); ok {
		r0 = returnFunc(ctx)
	} else {
		r0 = ret.Get(0).(string)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_GenerateUniqueReferralCode_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GenerateUniqueReferralCode'
type MockUserStore_GenerateUniqueReferralCode_Call struct {
	*mock.Call
}

// GenerateUniqueReferralCode is a helper method to define mock.On call
//   - ctx
func (_e *MockUserStore_Expecter) GenerateUniqueReferralCode(ctx interface{}) *MockUserStore_GenerateUniqueReferralCode_Call {
	return &MockUserStore_GenerateUniqueReferralCode_Call{Call: _e.mock.On("GenerateUniqueReferralCode", ctx)}
}

func (_c *MockUserStore_GenerateUniqueReferralCode_Call) Run(run func(ctx context.Context)) *MockUserStore_GenerateUniqueReferralCode_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockUserStore_GenerateUniqueReferralCode_Call) Return(s string, err error) *MockUserStore_GenerateUniqueReferralCode_Call {
	_c.Call.Return(s, err)
	return _c
}

func (_c *MockUserStore_GenerateUniqueReferralCode_Call) RunAndReturn(run func(ctx context.Context) (string, error)) *MockUserStore_GenerateUniqueReferralCode_Call {
	_c.Call.Return(run)
	return _c
}

// GenerateUserName provides a mock function for the type MockUserStore
func (_mock *MockUserStore) GenerateUserName(ctx context.Context, name string) (string, error) {
	ret := _mock.Called(ctx, name)

	if len(ret) == 0 {
		panic("no return value specified for GenerateUserName")
	}

	var r0 string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (string, error)); ok {
		return returnFunc(ctx, name)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) string); ok {
		r0 = returnFunc(ctx, name)
	} else {
		r0 = ret.Get(0).(string)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, name)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_GenerateUserName_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GenerateUserName'
type MockUserStore_GenerateUserName_Call struct {
	*mock.Call
}

// GenerateUserName is a helper method to define mock.On call
//   - ctx
//   - name
func (_e *MockUserStore_Expecter) GenerateUserName(ctx interface{}, name interface{}) *MockUserStore_GenerateUserName_Call {
	return &MockUserStore_GenerateUserName_Call{Call: _e.mock.On("GenerateUserName", ctx, name)}
}

func (_c *MockUserStore_GenerateUserName_Call) Run(run func(ctx context.Context, name string)) *MockUserStore_GenerateUserName_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockUserStore_GenerateUserName_Call) Return(s string, err error) *MockUserStore_GenerateUserName_Call {
	_c.Call.Return(s, err)
	return _c
}

func (_c *MockUserStore_GenerateUserName_Call) RunAndReturn(run func(ctx context.Context, name string) (string, error)) *MockUserStore_GenerateUserName_Call {
	_c.Call.Return(run)
	return _c
}

// GetCurrentUser provides a mock function for the type MockUserStore
func (_mock *MockUserStore) GetCurrentUser(ctx context.Context) (*models.User, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetCurrentUser")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.User, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.User); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_GetCurrentUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCurrentUser'
type MockUserStore_GetCurrentUser_Call struct {
	*mock.Call
}

// GetCurrentUser is a helper method to define mock.On call
//   - ctx
func (_e *MockUserStore_Expecter) GetCurrentUser(ctx interface{}) *MockUserStore_GetCurrentUser_Call {
	return &MockUserStore_GetCurrentUser_Call{Call: _e.mock.On("GetCurrentUser", ctx)}
}

func (_c *MockUserStore_GetCurrentUser_Call) Run(run func(ctx context.Context)) *MockUserStore_GetCurrentUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockUserStore_GetCurrentUser_Call) Return(user *models.User, err error) *MockUserStore_GetCurrentUser_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockUserStore_GetCurrentUser_Call) RunAndReturn(run func(ctx context.Context) (*models.User, error)) *MockUserStore_GetCurrentUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetFriendsLeaderboard provides a mock function for the type MockUserStore
func (_mock *MockUserStore) GetFriendsLeaderboard(ctx context.Context, page *int, pageSize *int, ratingType *string) (*models.UserLeaderboardPage, error) {
	ret := _mock.Called(ctx, page, pageSize, ratingType)

	if len(ret) == 0 {
		panic("no return value specified for GetFriendsLeaderboard")
	}

	var r0 *models.UserLeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *string) (*models.UserLeaderboardPage, error)); ok {
		return returnFunc(ctx, page, pageSize, ratingType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *string) *models.UserLeaderboardPage); ok {
		r0 = returnFunc(ctx, page, pageSize, ratingType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserLeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int, *string) error); ok {
		r1 = returnFunc(ctx, page, pageSize, ratingType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_GetFriendsLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFriendsLeaderboard'
type MockUserStore_GetFriendsLeaderboard_Call struct {
	*mock.Call
}

// GetFriendsLeaderboard is a helper method to define mock.On call
//   - ctx
//   - page
//   - pageSize
//   - ratingType
func (_e *MockUserStore_Expecter) GetFriendsLeaderboard(ctx interface{}, page interface{}, pageSize interface{}, ratingType interface{}) *MockUserStore_GetFriendsLeaderboard_Call {
	return &MockUserStore_GetFriendsLeaderboard_Call{Call: _e.mock.On("GetFriendsLeaderboard", ctx, page, pageSize, ratingType)}
}

func (_c *MockUserStore_GetFriendsLeaderboard_Call) Run(run func(ctx context.Context, page *int, pageSize *int, ratingType *string)) *MockUserStore_GetFriendsLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*int), args[2].(*int), args[3].(*string))
	})
	return _c
}

func (_c *MockUserStore_GetFriendsLeaderboard_Call) Return(userLeaderboardPage *models.UserLeaderboardPage, err error) *MockUserStore_GetFriendsLeaderboard_Call {
	_c.Call.Return(userLeaderboardPage, err)
	return _c
}

func (_c *MockUserStore_GetFriendsLeaderboard_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int, ratingType *string) (*models.UserLeaderboardPage, error)) *MockUserStore_GetFriendsLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetFriendsTopPlayers provides a mock function for the type MockUserStore
func (_mock *MockUserStore) GetFriendsTopPlayers(ctx context.Context) (*models.TopPlayersLeaderboard, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetFriendsTopPlayers")
	}

	var r0 *models.TopPlayersLeaderboard
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.TopPlayersLeaderboard, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.TopPlayersLeaderboard); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.TopPlayersLeaderboard)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_GetFriendsTopPlayers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFriendsTopPlayers'
type MockUserStore_GetFriendsTopPlayers_Call struct {
	*mock.Call
}

// GetFriendsTopPlayers is a helper method to define mock.On call
//   - ctx
func (_e *MockUserStore_Expecter) GetFriendsTopPlayers(ctx interface{}) *MockUserStore_GetFriendsTopPlayers_Call {
	return &MockUserStore_GetFriendsTopPlayers_Call{Call: _e.mock.On("GetFriendsTopPlayers", ctx)}
}

func (_c *MockUserStore_GetFriendsTopPlayers_Call) Run(run func(ctx context.Context)) *MockUserStore_GetFriendsTopPlayers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockUserStore_GetFriendsTopPlayers_Call) Return(topPlayersLeaderboard *models.TopPlayersLeaderboard, err error) *MockUserStore_GetFriendsTopPlayers_Call {
	_c.Call.Return(topPlayersLeaderboard, err)
	return _c
}

func (_c *MockUserStore_GetFriendsTopPlayers_Call) RunAndReturn(run func(ctx context.Context) (*models.TopPlayersLeaderboard, error)) *MockUserStore_GetFriendsTopPlayers_Call {
	_c.Call.Return(run)
	return _c
}

// GetGlobalTopPlayers provides a mock function for the type MockUserStore
func (_mock *MockUserStore) GetGlobalTopPlayers(ctx context.Context) (*models.TopPlayersLeaderboard, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetGlobalTopPlayers")
	}

	var r0 *models.TopPlayersLeaderboard
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.TopPlayersLeaderboard, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.TopPlayersLeaderboard); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.TopPlayersLeaderboard)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_GetGlobalTopPlayers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGlobalTopPlayers'
type MockUserStore_GetGlobalTopPlayers_Call struct {
	*mock.Call
}

// GetGlobalTopPlayers is a helper method to define mock.On call
//   - ctx
func (_e *MockUserStore_Expecter) GetGlobalTopPlayers(ctx interface{}) *MockUserStore_GetGlobalTopPlayers_Call {
	return &MockUserStore_GetGlobalTopPlayers_Call{Call: _e.mock.On("GetGlobalTopPlayers", ctx)}
}

func (_c *MockUserStore_GetGlobalTopPlayers_Call) Run(run func(ctx context.Context)) *MockUserStore_GetGlobalTopPlayers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockUserStore_GetGlobalTopPlayers_Call) Return(topPlayersLeaderboard *models.TopPlayersLeaderboard, err error) *MockUserStore_GetGlobalTopPlayers_Call {
	_c.Call.Return(topPlayersLeaderboard, err)
	return _c
}

func (_c *MockUserStore_GetGlobalTopPlayers_Call) RunAndReturn(run func(ctx context.Context) (*models.TopPlayersLeaderboard, error)) *MockUserStore_GetGlobalTopPlayers_Call {
	_c.Call.Return(run)
	return _c
}

// GetRatingFixtureQuestions provides a mock function for the type MockUserStore
func (_mock *MockUserStore) GetRatingFixtureQuestions(cts context.Context) ([]*models.Question, error) {
	ret := _mock.Called(cts)

	if len(ret) == 0 {
		panic("no return value specified for GetRatingFixtureQuestions")
	}

	var r0 []*models.Question
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.Question, error)); ok {
		return returnFunc(cts)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.Question); ok {
		r0 = returnFunc(cts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Question)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(cts)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_GetRatingFixtureQuestions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRatingFixtureQuestions'
type MockUserStore_GetRatingFixtureQuestions_Call struct {
	*mock.Call
}

// GetRatingFixtureQuestions is a helper method to define mock.On call
//   - cts
func (_e *MockUserStore_Expecter) GetRatingFixtureQuestions(cts interface{}) *MockUserStore_GetRatingFixtureQuestions_Call {
	return &MockUserStore_GetRatingFixtureQuestions_Call{Call: _e.mock.On("GetRatingFixtureQuestions", cts)}
}

func (_c *MockUserStore_GetRatingFixtureQuestions_Call) Run(run func(cts context.Context)) *MockUserStore_GetRatingFixtureQuestions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockUserStore_GetRatingFixtureQuestions_Call) Return(questions []*models.Question, err error) *MockUserStore_GetRatingFixtureQuestions_Call {
	_c.Call.Return(questions, err)
	return _c
}

func (_c *MockUserStore_GetRatingFixtureQuestions_Call) RunAndReturn(run func(cts context.Context) ([]*models.Question, error)) *MockUserStore_GetRatingFixtureQuestions_Call {
	_c.Call.Return(run)
	return _c
}

// GetRatingFixtureSubmission provides a mock function for the type MockUserStore
func (_mock *MockUserStore) GetRatingFixtureSubmission(cts context.Context) (*models.UserRatingFixtureSubmission, error) {
	ret := _mock.Called(cts)

	if len(ret) == 0 {
		panic("no return value specified for GetRatingFixtureSubmission")
	}

	var r0 *models.UserRatingFixtureSubmission
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.UserRatingFixtureSubmission, error)); ok {
		return returnFunc(cts)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.UserRatingFixtureSubmission); ok {
		r0 = returnFunc(cts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserRatingFixtureSubmission)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(cts)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_GetRatingFixtureSubmission_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRatingFixtureSubmission'
type MockUserStore_GetRatingFixtureSubmission_Call struct {
	*mock.Call
}

// GetRatingFixtureSubmission is a helper method to define mock.On call
//   - cts
func (_e *MockUserStore_Expecter) GetRatingFixtureSubmission(cts interface{}) *MockUserStore_GetRatingFixtureSubmission_Call {
	return &MockUserStore_GetRatingFixtureSubmission_Call{Call: _e.mock.On("GetRatingFixtureSubmission", cts)}
}

func (_c *MockUserStore_GetRatingFixtureSubmission_Call) Run(run func(cts context.Context)) *MockUserStore_GetRatingFixtureSubmission_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockUserStore_GetRatingFixtureSubmission_Call) Return(userRatingFixtureSubmission *models.UserRatingFixtureSubmission, err error) *MockUserStore_GetRatingFixtureSubmission_Call {
	_c.Call.Return(userRatingFixtureSubmission, err)
	return _c
}

func (_c *MockUserStore_GetRatingFixtureSubmission_Call) RunAndReturn(run func(cts context.Context) (*models.UserRatingFixtureSubmission, error)) *MockUserStore_GetRatingFixtureSubmission_Call {
	_c.Call.Return(run)
	return _c
}

// GetStatikCoinsLeaderboard provides a mock function for the type MockUserStore
func (_mock *MockUserStore) GetStatikCoinsLeaderboard(ctx context.Context, page int, pageSize *int, leaderboardType *models.StatikCoinLeaderboardType) (*models.StatikCoinLeaderboardPage, error) {
	ret := _mock.Called(ctx, page, pageSize, leaderboardType)

	if len(ret) == 0 {
		panic("no return value specified for GetStatikCoinsLeaderboard")
	}

	var r0 *models.StatikCoinLeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, *int, *models.StatikCoinLeaderboardType) (*models.StatikCoinLeaderboardPage, error)); ok {
		return returnFunc(ctx, page, pageSize, leaderboardType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, *int, *models.StatikCoinLeaderboardType) *models.StatikCoinLeaderboardPage); ok {
		r0 = returnFunc(ctx, page, pageSize, leaderboardType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.StatikCoinLeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int, *int, *models.StatikCoinLeaderboardType) error); ok {
		r1 = returnFunc(ctx, page, pageSize, leaderboardType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_GetStatikCoinsLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetStatikCoinsLeaderboard'
type MockUserStore_GetStatikCoinsLeaderboard_Call struct {
	*mock.Call
}

// GetStatikCoinsLeaderboard is a helper method to define mock.On call
//   - ctx
//   - page
//   - pageSize
//   - leaderboardType
func (_e *MockUserStore_Expecter) GetStatikCoinsLeaderboard(ctx interface{}, page interface{}, pageSize interface{}, leaderboardType interface{}) *MockUserStore_GetStatikCoinsLeaderboard_Call {
	return &MockUserStore_GetStatikCoinsLeaderboard_Call{Call: _e.mock.On("GetStatikCoinsLeaderboard", ctx, page, pageSize, leaderboardType)}
}

func (_c *MockUserStore_GetStatikCoinsLeaderboard_Call) Run(run func(ctx context.Context, page int, pageSize *int, leaderboardType *models.StatikCoinLeaderboardType)) *MockUserStore_GetStatikCoinsLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(*int), args[3].(*models.StatikCoinLeaderboardType))
	})
	return _c
}

func (_c *MockUserStore_GetStatikCoinsLeaderboard_Call) Return(statikCoinLeaderboardPage *models.StatikCoinLeaderboardPage, err error) *MockUserStore_GetStatikCoinsLeaderboard_Call {
	_c.Call.Return(statikCoinLeaderboardPage, err)
	return _c
}

func (_c *MockUserStore_GetStatikCoinsLeaderboard_Call) RunAndReturn(run func(ctx context.Context, page int, pageSize *int, leaderboardType *models.StatikCoinLeaderboardType) (*models.StatikCoinLeaderboardPage, error)) *MockUserStore_GetStatikCoinsLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetTimeSpentByUser provides a mock function for the type MockUserStore
func (_mock *MockUserStore) GetTimeSpentByUser(ctx context.Context, date *string) (int, error) {
	ret := _mock.Called(ctx, date)

	if len(ret) == 0 {
		panic("no return value specified for GetTimeSpentByUser")
	}

	var r0 int
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (int, error)); ok {
		return returnFunc(ctx, date)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) int); ok {
		r0 = returnFunc(ctx, date)
	} else {
		r0 = ret.Get(0).(int)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, date)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_GetTimeSpentByUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTimeSpentByUser'
type MockUserStore_GetTimeSpentByUser_Call struct {
	*mock.Call
}

// GetTimeSpentByUser is a helper method to define mock.On call
//   - ctx
//   - date
func (_e *MockUserStore_Expecter) GetTimeSpentByUser(ctx interface{}, date interface{}) *MockUserStore_GetTimeSpentByUser_Call {
	return &MockUserStore_GetTimeSpentByUser_Call{Call: _e.mock.On("GetTimeSpentByUser", ctx, date)}
}

func (_c *MockUserStore_GetTimeSpentByUser_Call) Run(run func(ctx context.Context, date *string)) *MockUserStore_GetTimeSpentByUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *MockUserStore_GetTimeSpentByUser_Call) Return(n int, err error) *MockUserStore_GetTimeSpentByUser_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockUserStore_GetTimeSpentByUser_Call) RunAndReturn(run func(ctx context.Context, date *string) (int, error)) *MockUserStore_GetTimeSpentByUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserByID provides a mock function for the type MockUserStore
func (_mock *MockUserStore) GetUserByID(ctx context.Context, id models.ObjectID) (*models.User, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetUserByID")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.ObjectID) (*models.User, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.ObjectID) *models.User); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_GetUserByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserByID'
type MockUserStore_GetUserByID_Call struct {
	*mock.Call
}

// GetUserByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockUserStore_Expecter) GetUserByID(ctx interface{}, id interface{}) *MockUserStore_GetUserByID_Call {
	return &MockUserStore_GetUserByID_Call{Call: _e.mock.On("GetUserByID", ctx, id)}
}

func (_c *MockUserStore_GetUserByID_Call) Run(run func(ctx context.Context, id models.ObjectID)) *MockUserStore_GetUserByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.ObjectID))
	})
	return _c
}

func (_c *MockUserStore_GetUserByID_Call) Return(user *models.User, err error) *MockUserStore_GetUserByID_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockUserStore_GetUserByID_Call) RunAndReturn(run func(ctx context.Context, id models.ObjectID) (*models.User, error)) *MockUserStore_GetUserByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserByUsername provides a mock function for the type MockUserStore
func (_mock *MockUserStore) GetUserByUsername(ctx context.Context, username *string) (*models.SearchUserOutput, error) {
	ret := _mock.Called(ctx, username)

	if len(ret) == 0 {
		panic("no return value specified for GetUserByUsername")
	}

	var r0 *models.SearchUserOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*models.SearchUserOutput, error)); ok {
		return returnFunc(ctx, username)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *models.SearchUserOutput); ok {
		r0 = returnFunc(ctx, username)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.SearchUserOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, username)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_GetUserByUsername_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserByUsername'
type MockUserStore_GetUserByUsername_Call struct {
	*mock.Call
}

// GetUserByUsername is a helper method to define mock.On call
//   - ctx
//   - username
func (_e *MockUserStore_Expecter) GetUserByUsername(ctx interface{}, username interface{}) *MockUserStore_GetUserByUsername_Call {
	return &MockUserStore_GetUserByUsername_Call{Call: _e.mock.On("GetUserByUsername", ctx, username)}
}

func (_c *MockUserStore_GetUserByUsername_Call) Run(run func(ctx context.Context, username *string)) *MockUserStore_GetUserByUsername_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *MockUserStore_GetUserByUsername_Call) Return(searchUserOutput *models.SearchUserOutput, err error) *MockUserStore_GetUserByUsername_Call {
	_c.Call.Return(searchUserOutput, err)
	return _c
}

func (_c *MockUserStore_GetUserByUsername_Call) RunAndReturn(run func(ctx context.Context, username *string) (*models.SearchUserOutput, error)) *MockUserStore_GetUserByUsername_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserLeagueGroupLeaderboard provides a mock function for the type MockUserStore
func (_mock *MockUserStore) GetUserLeagueGroupLeaderboard(ctx context.Context, page *int, pageSize *int) (*models.WeeklyLeagueLeaderboardPage, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetUserLeagueGroupLeaderboard")
	}

	var r0 *models.WeeklyLeagueLeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) (*models.WeeklyLeagueLeaderboardPage, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) *models.WeeklyLeagueLeaderboardPage); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.WeeklyLeagueLeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_GetUserLeagueGroupLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserLeagueGroupLeaderboard'
type MockUserStore_GetUserLeagueGroupLeaderboard_Call struct {
	*mock.Call
}

// GetUserLeagueGroupLeaderboard is a helper method to define mock.On call
//   - ctx
//   - page
//   - pageSize
func (_e *MockUserStore_Expecter) GetUserLeagueGroupLeaderboard(ctx interface{}, page interface{}, pageSize interface{}) *MockUserStore_GetUserLeagueGroupLeaderboard_Call {
	return &MockUserStore_GetUserLeagueGroupLeaderboard_Call{Call: _e.mock.On("GetUserLeagueGroupLeaderboard", ctx, page, pageSize)}
}

func (_c *MockUserStore_GetUserLeagueGroupLeaderboard_Call) Run(run func(ctx context.Context, page *int, pageSize *int)) *MockUserStore_GetUserLeagueGroupLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*int), args[2].(*int))
	})
	return _c
}

func (_c *MockUserStore_GetUserLeagueGroupLeaderboard_Call) Return(weeklyLeagueLeaderboardPage *models.WeeklyLeagueLeaderboardPage, err error) *MockUserStore_GetUserLeagueGroupLeaderboard_Call {
	_c.Call.Return(weeklyLeagueLeaderboardPage, err)
	return _c
}

func (_c *MockUserStore_GetUserLeagueGroupLeaderboard_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int) (*models.WeeklyLeagueLeaderboardPage, error)) *MockUserStore_GetUserLeagueGroupLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetUsersOfMyInstitute provides a mock function for the type MockUserStore
func (_mock *MockUserStore) GetUsersOfMyInstitute(ctx context.Context, page *int, pageSize *int) (*models.MyInstituteUsersPage, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetUsersOfMyInstitute")
	}

	var r0 *models.MyInstituteUsersPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) (*models.MyInstituteUsersPage, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) *models.MyInstituteUsersPage); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.MyInstituteUsersPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_GetUsersOfMyInstitute_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUsersOfMyInstitute'
type MockUserStore_GetUsersOfMyInstitute_Call struct {
	*mock.Call
}

// GetUsersOfMyInstitute is a helper method to define mock.On call
//   - ctx
//   - page
//   - pageSize
func (_e *MockUserStore_Expecter) GetUsersOfMyInstitute(ctx interface{}, page interface{}, pageSize interface{}) *MockUserStore_GetUsersOfMyInstitute_Call {
	return &MockUserStore_GetUsersOfMyInstitute_Call{Call: _e.mock.On("GetUsersOfMyInstitute", ctx, page, pageSize)}
}

func (_c *MockUserStore_GetUsersOfMyInstitute_Call) Run(run func(ctx context.Context, page *int, pageSize *int)) *MockUserStore_GetUsersOfMyInstitute_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*int), args[2].(*int))
	})
	return _c
}

func (_c *MockUserStore_GetUsersOfMyInstitute_Call) Return(myInstituteUsersPage *models.MyInstituteUsersPage, err error) *MockUserStore_GetUsersOfMyInstitute_Call {
	_c.Call.Return(myInstituteUsersPage, err)
	return _c
}

func (_c *MockUserStore_GetUsersOfMyInstitute_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int) (*models.MyInstituteUsersPage, error)) *MockUserStore_GetUsersOfMyInstitute_Call {
	_c.Call.Return(run)
	return _c
}

// GetUsersWeeklyStatikCoins provides a mock function for the type MockUserStore
func (_mock *MockUserStore) GetUsersWeeklyStatikCoins(ctx context.Context) (int, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUsersWeeklyStatikCoins")
	}

	var r0 int
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (int, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) int); ok {
		r0 = returnFunc(ctx)
	} else {
		r0 = ret.Get(0).(int)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_GetUsersWeeklyStatikCoins_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUsersWeeklyStatikCoins'
type MockUserStore_GetUsersWeeklyStatikCoins_Call struct {
	*mock.Call
}

// GetUsersWeeklyStatikCoins is a helper method to define mock.On call
//   - ctx
func (_e *MockUserStore_Expecter) GetUsersWeeklyStatikCoins(ctx interface{}) *MockUserStore_GetUsersWeeklyStatikCoins_Call {
	return &MockUserStore_GetUsersWeeklyStatikCoins_Call{Call: _e.mock.On("GetUsersWeeklyStatikCoins", ctx)}
}

func (_c *MockUserStore_GetUsersWeeklyStatikCoins_Call) Run(run func(ctx context.Context)) *MockUserStore_GetUsersWeeklyStatikCoins_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockUserStore_GetUsersWeeklyStatikCoins_Call) Return(n int, err error) *MockUserStore_GetUsersWeeklyStatikCoins_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockUserStore_GetUsersWeeklyStatikCoins_Call) RunAndReturn(run func(ctx context.Context) (int, error)) *MockUserStore_GetUsersWeeklyStatikCoins_Call {
	_c.Call.Return(run)
	return _c
}

// GetUsersWeeklyStatikCoinsV2 provides a mock function for the type MockUserStore
func (_mock *MockUserStore) GetUsersWeeklyStatikCoinsV2(ctx context.Context, userID primitive.ObjectID) (*models.UsersWeeklyStatikCoinsOutput, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetUsersWeeklyStatikCoinsV2")
	}

	var r0 *models.UsersWeeklyStatikCoinsOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.UsersWeeklyStatikCoinsOutput, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.UsersWeeklyStatikCoinsOutput); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UsersWeeklyStatikCoinsOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_GetUsersWeeklyStatikCoinsV2_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUsersWeeklyStatikCoinsV2'
type MockUserStore_GetUsersWeeklyStatikCoinsV2_Call struct {
	*mock.Call
}

// GetUsersWeeklyStatikCoinsV2 is a helper method to define mock.On call
//   - ctx
//   - userID
func (_e *MockUserStore_Expecter) GetUsersWeeklyStatikCoinsV2(ctx interface{}, userID interface{}) *MockUserStore_GetUsersWeeklyStatikCoinsV2_Call {
	return &MockUserStore_GetUsersWeeklyStatikCoinsV2_Call{Call: _e.mock.On("GetUsersWeeklyStatikCoinsV2", ctx, userID)}
}

func (_c *MockUserStore_GetUsersWeeklyStatikCoinsV2_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockUserStore_GetUsersWeeklyStatikCoinsV2_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserStore_GetUsersWeeklyStatikCoinsV2_Call) Return(usersWeeklyStatikCoinsOutput *models.UsersWeeklyStatikCoinsOutput, err error) *MockUserStore_GetUsersWeeklyStatikCoinsV2_Call {
	_c.Call.Return(usersWeeklyStatikCoinsOutput, err)
	return _c
}

func (_c *MockUserStore_GetUsersWeeklyStatikCoinsV2_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) (*models.UsersWeeklyStatikCoinsOutput, error)) *MockUserStore_GetUsersWeeklyStatikCoinsV2_Call {
	_c.Call.Return(run)
	return _c
}

// GoogleLogin provides a mock function for the type MockUserStore
func (_mock *MockUserStore) GoogleLogin(ctx context.Context, authCode string, tokenType *string, expiresIn *string, guestID *models.ObjectID) (*models.User, error) {
	ret := _mock.Called(ctx, authCode, tokenType, expiresIn, guestID)

	if len(ret) == 0 {
		panic("no return value specified for GoogleLogin")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *string, *string, *models.ObjectID) (*models.User, error)); ok {
		return returnFunc(ctx, authCode, tokenType, expiresIn, guestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *string, *string, *models.ObjectID) *models.User); ok {
		r0 = returnFunc(ctx, authCode, tokenType, expiresIn, guestID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, *string, *string, *models.ObjectID) error); ok {
		r1 = returnFunc(ctx, authCode, tokenType, expiresIn, guestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_GoogleLogin_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GoogleLogin'
type MockUserStore_GoogleLogin_Call struct {
	*mock.Call
}

// GoogleLogin is a helper method to define mock.On call
//   - ctx
//   - authCode
//   - tokenType
//   - expiresIn
//   - guestID
func (_e *MockUserStore_Expecter) GoogleLogin(ctx interface{}, authCode interface{}, tokenType interface{}, expiresIn interface{}, guestID interface{}) *MockUserStore_GoogleLogin_Call {
	return &MockUserStore_GoogleLogin_Call{Call: _e.mock.On("GoogleLogin", ctx, authCode, tokenType, expiresIn, guestID)}
}

func (_c *MockUserStore_GoogleLogin_Call) Run(run func(ctx context.Context, authCode string, tokenType *string, expiresIn *string, guestID *models.ObjectID)) *MockUserStore_GoogleLogin_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*string), args[3].(*string), args[4].(*models.ObjectID))
	})
	return _c
}

func (_c *MockUserStore_GoogleLogin_Call) Return(user *models.User, err error) *MockUserStore_GoogleLogin_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockUserStore_GoogleLogin_Call) RunAndReturn(run func(ctx context.Context, authCode string, tokenType *string, expiresIn *string, guestID *models.ObjectID) (*models.User, error)) *MockUserStore_GoogleLogin_Call {
	_c.Call.Return(run)
	return _c
}

// IsUsernameAvailable provides a mock function for the type MockUserStore
func (_mock *MockUserStore) IsUsernameAvailable(ctx context.Context, username *string) (bool, error) {
	ret := _mock.Called(ctx, username)

	if len(ret) == 0 {
		panic("no return value specified for IsUsernameAvailable")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (bool, error)); ok {
		return returnFunc(ctx, username)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) bool); ok {
		r0 = returnFunc(ctx, username)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, username)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_IsUsernameAvailable_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsUsernameAvailable'
type MockUserStore_IsUsernameAvailable_Call struct {
	*mock.Call
}

// IsUsernameAvailable is a helper method to define mock.On call
//   - ctx
//   - username
func (_e *MockUserStore_Expecter) IsUsernameAvailable(ctx interface{}, username interface{}) *MockUserStore_IsUsernameAvailable_Call {
	return &MockUserStore_IsUsernameAvailable_Call{Call: _e.mock.On("IsUsernameAvailable", ctx, username)}
}

func (_c *MockUserStore_IsUsernameAvailable_Call) Run(run func(ctx context.Context, username *string)) *MockUserStore_IsUsernameAvailable_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string))
	})
	return _c
}

func (_c *MockUserStore_IsUsernameAvailable_Call) Return(b bool, err error) *MockUserStore_IsUsernameAvailable_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockUserStore_IsUsernameAvailable_Call) RunAndReturn(run func(ctx context.Context, username *string) (bool, error)) *MockUserStore_IsUsernameAvailable_Call {
	_c.Call.Return(run)
	return _c
}

// LeaderBoardV3 provides a mock function for the type MockUserStore
func (_mock *MockUserStore) LeaderBoardV3(ctx context.Context, countryCode *string, searchKey *string, page *int, pageSize *int) (*models.UserLeaderboardPage, error) {
	ret := _mock.Called(ctx, countryCode, searchKey, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for LeaderBoardV3")
	}

	var r0 *models.UserLeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string, *int, *int) (*models.UserLeaderboardPage, error)); ok {
		return returnFunc(ctx, countryCode, searchKey, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string, *int, *int) *models.UserLeaderboardPage); ok {
		r0 = returnFunc(ctx, countryCode, searchKey, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserLeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *string, *int, *int) error); ok {
		r1 = returnFunc(ctx, countryCode, searchKey, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_LeaderBoardV3_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LeaderBoardV3'
type MockUserStore_LeaderBoardV3_Call struct {
	*mock.Call
}

// LeaderBoardV3 is a helper method to define mock.On call
//   - ctx
//   - countryCode
//   - searchKey
//   - page
//   - pageSize
func (_e *MockUserStore_Expecter) LeaderBoardV3(ctx interface{}, countryCode interface{}, searchKey interface{}, page interface{}, pageSize interface{}) *MockUserStore_LeaderBoardV3_Call {
	return &MockUserStore_LeaderBoardV3_Call{Call: _e.mock.On("LeaderBoardV3", ctx, countryCode, searchKey, page, pageSize)}
}

func (_c *MockUserStore_LeaderBoardV3_Call) Run(run func(ctx context.Context, countryCode *string, searchKey *string, page *int, pageSize *int)) *MockUserStore_LeaderBoardV3_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string), args[2].(*string), args[3].(*int), args[4].(*int))
	})
	return _c
}

func (_c *MockUserStore_LeaderBoardV3_Call) Return(userLeaderboardPage *models.UserLeaderboardPage, err error) *MockUserStore_LeaderBoardV3_Call {
	_c.Call.Return(userLeaderboardPage, err)
	return _c
}

func (_c *MockUserStore_LeaderBoardV3_Call) RunAndReturn(run func(ctx context.Context, countryCode *string, searchKey *string, page *int, pageSize *int) (*models.UserLeaderboardPage, error)) *MockUserStore_LeaderBoardV3_Call {
	_c.Call.Return(run)
	return _c
}

// Leaderboard provides a mock function for the type MockUserStore
func (_mock *MockUserStore) Leaderboard(ctx context.Context, countryCode *string, searchKey *string, first *int, after *string) (*models.LeaderboardConnection, error) {
	ret := _mock.Called(ctx, countryCode, searchKey, first, after)

	if len(ret) == 0 {
		panic("no return value specified for Leaderboard")
	}

	var r0 *models.LeaderboardConnection
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string, *int, *string) (*models.LeaderboardConnection, error)); ok {
		return returnFunc(ctx, countryCode, searchKey, first, after)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string, *int, *string) *models.LeaderboardConnection); ok {
		r0 = returnFunc(ctx, countryCode, searchKey, first, after)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.LeaderboardConnection)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *string, *int, *string) error); ok {
		r1 = returnFunc(ctx, countryCode, searchKey, first, after)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_Leaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Leaderboard'
type MockUserStore_Leaderboard_Call struct {
	*mock.Call
}

// Leaderboard is a helper method to define mock.On call
//   - ctx
//   - countryCode
//   - searchKey
//   - first
//   - after
func (_e *MockUserStore_Expecter) Leaderboard(ctx interface{}, countryCode interface{}, searchKey interface{}, first interface{}, after interface{}) *MockUserStore_Leaderboard_Call {
	return &MockUserStore_Leaderboard_Call{Call: _e.mock.On("Leaderboard", ctx, countryCode, searchKey, first, after)}
}

func (_c *MockUserStore_Leaderboard_Call) Run(run func(ctx context.Context, countryCode *string, searchKey *string, first *int, after *string)) *MockUserStore_Leaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string), args[2].(*string), args[3].(*int), args[4].(*string))
	})
	return _c
}

func (_c *MockUserStore_Leaderboard_Call) Return(leaderboardConnection *models.LeaderboardConnection, err error) *MockUserStore_Leaderboard_Call {
	_c.Call.Return(leaderboardConnection, err)
	return _c
}

func (_c *MockUserStore_Leaderboard_Call) RunAndReturn(run func(ctx context.Context, countryCode *string, searchKey *string, first *int, after *string) (*models.LeaderboardConnection, error)) *MockUserStore_Leaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// LeaderboardNew provides a mock function for the type MockUserStore
func (_mock *MockUserStore) LeaderboardNew(ctx context.Context, countryCode *string, searchKey *string, page *int, pageSize *int, ratingType *string) (*models.UserLeaderboardPage, error) {
	ret := _mock.Called(ctx, countryCode, searchKey, page, pageSize, ratingType)

	if len(ret) == 0 {
		panic("no return value specified for LeaderboardNew")
	}

	var r0 *models.UserLeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string, *int, *int, *string) (*models.UserLeaderboardPage, error)); ok {
		return returnFunc(ctx, countryCode, searchKey, page, pageSize, ratingType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string, *int, *int, *string) *models.UserLeaderboardPage); ok {
		r0 = returnFunc(ctx, countryCode, searchKey, page, pageSize, ratingType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserLeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *string, *int, *int, *string) error); ok {
		r1 = returnFunc(ctx, countryCode, searchKey, page, pageSize, ratingType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_LeaderboardNew_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LeaderboardNew'
type MockUserStore_LeaderboardNew_Call struct {
	*mock.Call
}

// LeaderboardNew is a helper method to define mock.On call
//   - ctx
//   - countryCode
//   - searchKey
//   - page
//   - pageSize
//   - ratingType
func (_e *MockUserStore_Expecter) LeaderboardNew(ctx interface{}, countryCode interface{}, searchKey interface{}, page interface{}, pageSize interface{}, ratingType interface{}) *MockUserStore_LeaderboardNew_Call {
	return &MockUserStore_LeaderboardNew_Call{Call: _e.mock.On("LeaderboardNew", ctx, countryCode, searchKey, page, pageSize, ratingType)}
}

func (_c *MockUserStore_LeaderboardNew_Call) Run(run func(ctx context.Context, countryCode *string, searchKey *string, page *int, pageSize *int, ratingType *string)) *MockUserStore_LeaderboardNew_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string), args[2].(*string), args[3].(*int), args[4].(*int), args[5].(*string))
	})
	return _c
}

func (_c *MockUserStore_LeaderboardNew_Call) Return(userLeaderboardPage *models.UserLeaderboardPage, err error) *MockUserStore_LeaderboardNew_Call {
	_c.Call.Return(userLeaderboardPage, err)
	return _c
}

func (_c *MockUserStore_LeaderboardNew_Call) RunAndReturn(run func(ctx context.Context, countryCode *string, searchKey *string, page *int, pageSize *int, ratingType *string) (*models.UserLeaderboardPage, error)) *MockUserStore_LeaderboardNew_Call {
	_c.Call.Return(run)
	return _c
}

// LegacyGoogleLogin provides a mock function for the type MockUserStore
func (_mock *MockUserStore) LegacyGoogleLogin(ctx context.Context, idToken string, guestID *models.ObjectID) (*models.User, error) {
	ret := _mock.Called(ctx, idToken, guestID)

	if len(ret) == 0 {
		panic("no return value specified for LegacyGoogleLogin")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *models.ObjectID) (*models.User, error)); ok {
		return returnFunc(ctx, idToken, guestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *models.ObjectID) *models.User); ok {
		r0 = returnFunc(ctx, idToken, guestID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, *models.ObjectID) error); ok {
		r1 = returnFunc(ctx, idToken, guestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_LegacyGoogleLogin_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LegacyGoogleLogin'
type MockUserStore_LegacyGoogleLogin_Call struct {
	*mock.Call
}

// LegacyGoogleLogin is a helper method to define mock.On call
//   - ctx
//   - idToken
//   - guestID
func (_e *MockUserStore_Expecter) LegacyGoogleLogin(ctx interface{}, idToken interface{}, guestID interface{}) *MockUserStore_LegacyGoogleLogin_Call {
	return &MockUserStore_LegacyGoogleLogin_Call{Call: _e.mock.On("LegacyGoogleLogin", ctx, idToken, guestID)}
}

func (_c *MockUserStore_LegacyGoogleLogin_Call) Run(run func(ctx context.Context, idToken string, guestID *models.ObjectID)) *MockUserStore_LegacyGoogleLogin_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*models.ObjectID))
	})
	return _c
}

func (_c *MockUserStore_LegacyGoogleLogin_Call) Return(user *models.User, err error) *MockUserStore_LegacyGoogleLogin_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockUserStore_LegacyGoogleLogin_Call) RunAndReturn(run func(ctx context.Context, idToken string, guestID *models.ObjectID) (*models.User, error)) *MockUserStore_LegacyGoogleLogin_Call {
	_c.Call.Return(run)
	return _c
}

// Login provides a mock function for the type MockUserStore
func (_mock *MockUserStore) Login(ctx context.Context, email string, password string) (*models.User, error) {
	ret := _mock.Called(ctx, email, password)

	if len(ret) == 0 {
		panic("no return value specified for Login")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) (*models.User, error)); ok {
		return returnFunc(ctx, email, password)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) *models.User); ok {
		r0 = returnFunc(ctx, email, password)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = returnFunc(ctx, email, password)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_Login_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Login'
type MockUserStore_Login_Call struct {
	*mock.Call
}

// Login is a helper method to define mock.On call
//   - ctx
//   - email
//   - password
func (_e *MockUserStore_Expecter) Login(ctx interface{}, email interface{}, password interface{}) *MockUserStore_Login_Call {
	return &MockUserStore_Login_Call{Call: _e.mock.On("Login", ctx, email, password)}
}

func (_c *MockUserStore_Login_Call) Run(run func(ctx context.Context, email string, password string)) *MockUserStore_Login_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockUserStore_Login_Call) Return(user *models.User, err error) *MockUserStore_Login_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockUserStore_Login_Call) RunAndReturn(run func(ctx context.Context, email string, password string) (*models.User, error)) *MockUserStore_Login_Call {
	_c.Call.Return(run)
	return _c
}

// LoginAsGuest provides a mock function for the type MockUserStore
func (_mock *MockUserStore) LoginAsGuest(ctx context.Context, id models.ObjectID) (*models.User, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for LoginAsGuest")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.ObjectID) (*models.User, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.ObjectID) *models.User); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_LoginAsGuest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LoginAsGuest'
type MockUserStore_LoginAsGuest_Call struct {
	*mock.Call
}

// LoginAsGuest is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockUserStore_Expecter) LoginAsGuest(ctx interface{}, id interface{}) *MockUserStore_LoginAsGuest_Call {
	return &MockUserStore_LoginAsGuest_Call{Call: _e.mock.On("LoginAsGuest", ctx, id)}
}

func (_c *MockUserStore_LoginAsGuest_Call) Run(run func(ctx context.Context, id models.ObjectID)) *MockUserStore_LoginAsGuest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.ObjectID))
	})
	return _c
}

func (_c *MockUserStore_LoginAsGuest_Call) Return(user *models.User, err error) *MockUserStore_LoginAsGuest_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockUserStore_LoginAsGuest_Call) RunAndReturn(run func(ctx context.Context, id models.ObjectID) (*models.User, error)) *MockUserStore_LoginAsGuest_Call {
	_c.Call.Return(run)
	return _c
}

// OnlineUsers provides a mock function for the type MockUserStore
func (_mock *MockUserStore) OnlineUsers(ctx context.Context, page int, pageSize int) (*models.OnlineUsersPage, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for OnlineUsers")
	}

	var r0 *models.OnlineUsersPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, int) (*models.OnlineUsersPage, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, int) *models.OnlineUsersPage); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.OnlineUsersPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int, int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_OnlineUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OnlineUsers'
type MockUserStore_OnlineUsers_Call struct {
	*mock.Call
}

// OnlineUsers is a helper method to define mock.On call
//   - ctx
//   - page
//   - pageSize
func (_e *MockUserStore_Expecter) OnlineUsers(ctx interface{}, page interface{}, pageSize interface{}) *MockUserStore_OnlineUsers_Call {
	return &MockUserStore_OnlineUsers_Call{Call: _e.mock.On("OnlineUsers", ctx, page, pageSize)}
}

func (_c *MockUserStore_OnlineUsers_Call) Run(run func(ctx context.Context, page int, pageSize int)) *MockUserStore_OnlineUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(int))
	})
	return _c
}

func (_c *MockUserStore_OnlineUsers_Call) Return(onlineUsersPage *models.OnlineUsersPage, err error) *MockUserStore_OnlineUsers_Call {
	_c.Call.Return(onlineUsersPage, err)
	return _c
}

func (_c *MockUserStore_OnlineUsers_Call) RunAndReturn(run func(ctx context.Context, page int, pageSize int) (*models.OnlineUsersPage, error)) *MockUserStore_OnlineUsers_Call {
	_c.Call.Return(run)
	return _c
}

// SearchUsersInMyInstitute provides a mock function for the type MockUserStore
func (_mock *MockUserStore) SearchUsersInMyInstitute(ctx context.Context, searchKey *string, page *int, pageSize *int) (*models.MyInstituteUsersPage, error) {
	ret := _mock.Called(ctx, searchKey, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for SearchUsersInMyInstitute")
	}

	var r0 *models.MyInstituteUsersPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *int, *int) (*models.MyInstituteUsersPage, error)); ok {
		return returnFunc(ctx, searchKey, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *int, *int) *models.MyInstituteUsersPage); ok {
		r0 = returnFunc(ctx, searchKey, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.MyInstituteUsersPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *int, *int) error); ok {
		r1 = returnFunc(ctx, searchKey, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_SearchUsersInMyInstitute_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SearchUsersInMyInstitute'
type MockUserStore_SearchUsersInMyInstitute_Call struct {
	*mock.Call
}

// SearchUsersInMyInstitute is a helper method to define mock.On call
//   - ctx
//   - searchKey
//   - page
//   - pageSize
func (_e *MockUserStore_Expecter) SearchUsersInMyInstitute(ctx interface{}, searchKey interface{}, page interface{}, pageSize interface{}) *MockUserStore_SearchUsersInMyInstitute_Call {
	return &MockUserStore_SearchUsersInMyInstitute_Call{Call: _e.mock.On("SearchUsersInMyInstitute", ctx, searchKey, page, pageSize)}
}

func (_c *MockUserStore_SearchUsersInMyInstitute_Call) Run(run func(ctx context.Context, searchKey *string, page *int, pageSize *int)) *MockUserStore_SearchUsersInMyInstitute_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string), args[2].(*int), args[3].(*int))
	})
	return _c
}

func (_c *MockUserStore_SearchUsersInMyInstitute_Call) Return(myInstituteUsersPage *models.MyInstituteUsersPage, err error) *MockUserStore_SearchUsersInMyInstitute_Call {
	_c.Call.Return(myInstituteUsersPage, err)
	return _c
}

func (_c *MockUserStore_SearchUsersInMyInstitute_Call) RunAndReturn(run func(ctx context.Context, searchKey *string, page *int, pageSize *int) (*models.MyInstituteUsersPage, error)) *MockUserStore_SearchUsersInMyInstitute_Call {
	_c.Call.Return(run)
	return _c
}

// SendOtp provides a mock function for the type MockUserStore
func (_mock *MockUserStore) SendOtp(ctx context.Context, email string) (bool, error) {
	ret := _mock.Called(ctx, email)

	if len(ret) == 0 {
		panic("no return value specified for SendOtp")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return returnFunc(ctx, email)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = returnFunc(ctx, email)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, email)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_SendOtp_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendOtp'
type MockUserStore_SendOtp_Call struct {
	*mock.Call
}

// SendOtp is a helper method to define mock.On call
//   - ctx
//   - email
func (_e *MockUserStore_Expecter) SendOtp(ctx interface{}, email interface{}) *MockUserStore_SendOtp_Call {
	return &MockUserStore_SendOtp_Call{Call: _e.mock.On("SendOtp", ctx, email)}
}

func (_c *MockUserStore_SendOtp_Call) Run(run func(ctx context.Context, email string)) *MockUserStore_SendOtp_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockUserStore_SendOtp_Call) Return(b bool, err error) *MockUserStore_SendOtp_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockUserStore_SendOtp_Call) RunAndReturn(run func(ctx context.Context, email string) (bool, error)) *MockUserStore_SendOtp_Call {
	_c.Call.Return(run)
	return _c
}

// SignInWithApple provides a mock function for the type MockUserStore
func (_mock *MockUserStore) SignInWithApple(ctx context.Context, input *models.AppleSignInInput) (*models.User, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for SignInWithApple")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.AppleSignInInput) (*models.User, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.AppleSignInInput) *models.User); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.AppleSignInInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_SignInWithApple_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SignInWithApple'
type MockUserStore_SignInWithApple_Call struct {
	*mock.Call
}

// SignInWithApple is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockUserStore_Expecter) SignInWithApple(ctx interface{}, input interface{}) *MockUserStore_SignInWithApple_Call {
	return &MockUserStore_SignInWithApple_Call{Call: _e.mock.On("SignInWithApple", ctx, input)}
}

func (_c *MockUserStore_SignInWithApple_Call) Run(run func(ctx context.Context, input *models.AppleSignInInput)) *MockUserStore_SignInWithApple_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.AppleSignInInput))
	})
	return _c
}

func (_c *MockUserStore_SignInWithApple_Call) Return(user *models.User, err error) *MockUserStore_SignInWithApple_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockUserStore_SignInWithApple_Call) RunAndReturn(run func(ctx context.Context, input *models.AppleSignInInput) (*models.User, error)) *MockUserStore_SignInWithApple_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitRatingFixtureResponses provides a mock function for the type MockUserStore
func (_mock *MockUserStore) SubmitRatingFixtureResponses(ctx context.Context, submission []*int, timeTaken int) (*models.UserRatingFixtureSubmission, error) {
	ret := _mock.Called(ctx, submission, timeTaken)

	if len(ret) == 0 {
		panic("no return value specified for SubmitRatingFixtureResponses")
	}

	var r0 *models.UserRatingFixtureSubmission
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*int, int) (*models.UserRatingFixtureSubmission, error)); ok {
		return returnFunc(ctx, submission, timeTaken)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*int, int) *models.UserRatingFixtureSubmission); ok {
		r0 = returnFunc(ctx, submission, timeTaken)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserRatingFixtureSubmission)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []*int, int) error); ok {
		r1 = returnFunc(ctx, submission, timeTaken)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_SubmitRatingFixtureResponses_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitRatingFixtureResponses'
type MockUserStore_SubmitRatingFixtureResponses_Call struct {
	*mock.Call
}

// SubmitRatingFixtureResponses is a helper method to define mock.On call
//   - ctx
//   - submission
//   - timeTaken
func (_e *MockUserStore_Expecter) SubmitRatingFixtureResponses(ctx interface{}, submission interface{}, timeTaken interface{}) *MockUserStore_SubmitRatingFixtureResponses_Call {
	return &MockUserStore_SubmitRatingFixtureResponses_Call{Call: _e.mock.On("SubmitRatingFixtureResponses", ctx, submission, timeTaken)}
}

func (_c *MockUserStore_SubmitRatingFixtureResponses_Call) Run(run func(ctx context.Context, submission []*int, timeTaken int)) *MockUserStore_SubmitRatingFixtureResponses_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*int), args[2].(int))
	})
	return _c
}

func (_c *MockUserStore_SubmitRatingFixtureResponses_Call) Return(userRatingFixtureSubmission *models.UserRatingFixtureSubmission, err error) *MockUserStore_SubmitRatingFixtureResponses_Call {
	_c.Call.Return(userRatingFixtureSubmission, err)
	return _c
}

func (_c *MockUserStore_SubmitRatingFixtureResponses_Call) RunAndReturn(run func(ctx context.Context, submission []*int, timeTaken int) (*models.UserRatingFixtureSubmission, error)) *MockUserStore_SubmitRatingFixtureResponses_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitReferral provides a mock function for the type MockUserStore
func (_mock *MockUserStore) SubmitReferral(ctx context.Context, referralCode string) (bool, error) {
	ret := _mock.Called(ctx, referralCode)

	if len(ret) == 0 {
		panic("no return value specified for SubmitReferral")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return returnFunc(ctx, referralCode)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = returnFunc(ctx, referralCode)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, referralCode)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_SubmitReferral_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitReferral'
type MockUserStore_SubmitReferral_Call struct {
	*mock.Call
}

// SubmitReferral is a helper method to define mock.On call
//   - ctx
//   - referralCode
func (_e *MockUserStore_Expecter) SubmitReferral(ctx interface{}, referralCode interface{}) *MockUserStore_SubmitReferral_Call {
	return &MockUserStore_SubmitReferral_Call{Call: _e.mock.On("SubmitReferral", ctx, referralCode)}
}

func (_c *MockUserStore_SubmitReferral_Call) Run(run func(ctx context.Context, referralCode string)) *MockUserStore_SubmitReferral_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockUserStore_SubmitReferral_Call) Return(b bool, err error) *MockUserStore_SubmitReferral_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockUserStore_SubmitReferral_Call) RunAndReturn(run func(ctx context.Context, referralCode string) (bool, error)) *MockUserStore_SubmitReferral_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function for the type MockUserStore
func (_mock *MockUserStore) UpdateOne(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions) error {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, update, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter, update)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, bson.M, ...*options.UpdateOptions) error); ok {
		r0 = returnFunc(ctx, filter, update, opts...)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserStore_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockUserStore_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx
//   - filter
//   - update
//   - opts
func (_e *MockUserStore_Expecter) UpdateOne(ctx interface{}, filter interface{}, update interface{}, opts ...interface{}) *MockUserStore_UpdateOne_Call {
	return &MockUserStore_UpdateOne_Call{Call: _e.mock.On("UpdateOne",
		append([]interface{}{ctx, filter, update}, opts...)...)}
}

func (_c *MockUserStore_UpdateOne_Call) Run(run func(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions)) *MockUserStore_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[3].([]*options.UpdateOptions)
		run(args[0].(context.Context), args[1].(bson.M), args[2].(bson.M), variadicArgs...)
	})
	return _c
}

func (_c *MockUserStore_UpdateOne_Call) Return(err error) *MockUserStore_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserStore_UpdateOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions) error) *MockUserStore_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateRatingBasedOnFixtureResponse provides a mock function for the type MockUserStore
func (_mock *MockUserStore) UpdateRatingBasedOnFixtureResponse(ctx context.Context, userStance models.UserStance) (*models.User, error) {
	ret := _mock.Called(ctx, userStance)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRatingBasedOnFixtureResponse")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.UserStance) (*models.User, error)); ok {
		return returnFunc(ctx, userStance)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.UserStance) *models.User); ok {
		r0 = returnFunc(ctx, userStance)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.UserStance) error); ok {
		r1 = returnFunc(ctx, userStance)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_UpdateRatingBasedOnFixtureResponse_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRatingBasedOnFixtureResponse'
type MockUserStore_UpdateRatingBasedOnFixtureResponse_Call struct {
	*mock.Call
}

// UpdateRatingBasedOnFixtureResponse is a helper method to define mock.On call
//   - ctx
//   - userStance
func (_e *MockUserStore_Expecter) UpdateRatingBasedOnFixtureResponse(ctx interface{}, userStance interface{}) *MockUserStore_UpdateRatingBasedOnFixtureResponse_Call {
	return &MockUserStore_UpdateRatingBasedOnFixtureResponse_Call{Call: _e.mock.On("UpdateRatingBasedOnFixtureResponse", ctx, userStance)}
}

func (_c *MockUserStore_UpdateRatingBasedOnFixtureResponse_Call) Run(run func(ctx context.Context, userStance models.UserStance)) *MockUserStore_UpdateRatingBasedOnFixtureResponse_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.UserStance))
	})
	return _c
}

func (_c *MockUserStore_UpdateRatingBasedOnFixtureResponse_Call) Return(user *models.User, err error) *MockUserStore_UpdateRatingBasedOnFixtureResponse_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockUserStore_UpdateRatingBasedOnFixtureResponse_Call) RunAndReturn(run func(ctx context.Context, userStance models.UserStance) (*models.User, error)) *MockUserStore_UpdateRatingBasedOnFixtureResponse_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUser provides a mock function for the type MockUserStore
func (_mock *MockUserStore) UpdateUser(ctx context.Context, updateUserInput *models.UpdateUserInput) (*models.User, error) {
	ret := _mock.Called(ctx, updateUserInput)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUser")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UpdateUserInput) (*models.User, error)); ok {
		return returnFunc(ctx, updateUserInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UpdateUserInput) *models.User); ok {
		r0 = returnFunc(ctx, updateUserInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.UpdateUserInput) error); ok {
		r1 = returnFunc(ctx, updateUserInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_UpdateUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUser'
type MockUserStore_UpdateUser_Call struct {
	*mock.Call
}

// UpdateUser is a helper method to define mock.On call
//   - ctx
//   - updateUserInput
func (_e *MockUserStore_Expecter) UpdateUser(ctx interface{}, updateUserInput interface{}) *MockUserStore_UpdateUser_Call {
	return &MockUserStore_UpdateUser_Call{Call: _e.mock.On("UpdateUser", ctx, updateUserInput)}
}

func (_c *MockUserStore_UpdateUser_Call) Run(run func(ctx context.Context, updateUserInput *models.UpdateUserInput)) *MockUserStore_UpdateUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.UpdateUserInput))
	})
	return _c
}

func (_c *MockUserStore_UpdateUser_Call) Return(user *models.User, err error) *MockUserStore_UpdateUser_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockUserStore_UpdateUser_Call) RunAndReturn(run func(ctx context.Context, updateUserInput *models.UpdateUserInput) (*models.User, error)) *MockUserStore_UpdateUser_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserFromObject provides a mock function for the type MockUserStore
func (_mock *MockUserStore) UpdateUserFromObject(ctx context.Context, user *models.User) error {
	ret := _mock.Called(ctx, user)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserFromObject")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.User) error); ok {
		r0 = returnFunc(ctx, user)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserStore_UpdateUserFromObject_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserFromObject'
type MockUserStore_UpdateUserFromObject_Call struct {
	*mock.Call
}

// UpdateUserFromObject is a helper method to define mock.On call
//   - ctx
//   - user
func (_e *MockUserStore_Expecter) UpdateUserFromObject(ctx interface{}, user interface{}) *MockUserStore_UpdateUserFromObject_Call {
	return &MockUserStore_UpdateUserFromObject_Call{Call: _e.mock.On("UpdateUserFromObject", ctx, user)}
}

func (_c *MockUserStore_UpdateUserFromObject_Call) Run(run func(ctx context.Context, user *models.User)) *MockUserStore_UpdateUserFromObject_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.User))
	})
	return _c
}

func (_c *MockUserStore_UpdateUserFromObject_Call) Return(err error) *MockUserStore_UpdateUserFromObject_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserStore_UpdateUserFromObject_Call) RunAndReturn(run func(ctx context.Context, user *models.User) error) *MockUserStore_UpdateUserFromObject_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserStatikCoinsAndTimeSpent provides a mock function for the type MockUserStore
func (_mock *MockUserStore) UpdateUserStatikCoinsAndTimeSpent(ctx context.Context, userID primitive.ObjectID, activityType constants.ActivityType, coins int, duration int64, activityID *primitive.ObjectID) error {
	ret := _mock.Called(ctx, userID, activityType, coins, duration, activityID)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserStatikCoinsAndTimeSpent")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, constants.ActivityType, int, int64, *primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, userID, activityType, coins, duration, activityID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserStore_UpdateUserStatikCoinsAndTimeSpent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserStatikCoinsAndTimeSpent'
type MockUserStore_UpdateUserStatikCoinsAndTimeSpent_Call struct {
	*mock.Call
}

// UpdateUserStatikCoinsAndTimeSpent is a helper method to define mock.On call
//   - ctx
//   - userID
//   - activityType
//   - coins
//   - duration
//   - activityID
func (_e *MockUserStore_Expecter) UpdateUserStatikCoinsAndTimeSpent(ctx interface{}, userID interface{}, activityType interface{}, coins interface{}, duration interface{}, activityID interface{}) *MockUserStore_UpdateUserStatikCoinsAndTimeSpent_Call {
	return &MockUserStore_UpdateUserStatikCoinsAndTimeSpent_Call{Call: _e.mock.On("UpdateUserStatikCoinsAndTimeSpent", ctx, userID, activityType, coins, duration, activityID)}
}

func (_c *MockUserStore_UpdateUserStatikCoinsAndTimeSpent_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, activityType constants.ActivityType, coins int, duration int64, activityID *primitive.ObjectID)) *MockUserStore_UpdateUserStatikCoinsAndTimeSpent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(constants.ActivityType), args[3].(int), args[4].(int64), args[5].(*primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserStore_UpdateUserStatikCoinsAndTimeSpent_Call) Return(err error) *MockUserStore_UpdateUserStatikCoinsAndTimeSpent_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserStore_UpdateUserStatikCoinsAndTimeSpent_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, activityType constants.ActivityType, coins int, duration int64, activityID *primitive.ObjectID) error) *MockUserStore_UpdateUserStatikCoinsAndTimeSpent_Call {
	_c.Call.Return(run)
	return _c
}

// UploadFiles provides a mock function for the type MockUserStore
func (_mock *MockUserStore) UploadFiles(ctx context.Context, files []*graphql.Upload) ([]*models.File, error) {
	ret := _mock.Called(ctx, files)

	if len(ret) == 0 {
		panic("no return value specified for UploadFiles")
	}

	var r0 []*models.File
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*graphql.Upload) ([]*models.File, error)); ok {
		return returnFunc(ctx, files)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*graphql.Upload) []*models.File); ok {
		r0 = returnFunc(ctx, files)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.File)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []*graphql.Upload) error); ok {
		r1 = returnFunc(ctx, files)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_UploadFiles_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadFiles'
type MockUserStore_UploadFiles_Call struct {
	*mock.Call
}

// UploadFiles is a helper method to define mock.On call
//   - ctx
//   - files
func (_e *MockUserStore_Expecter) UploadFiles(ctx interface{}, files interface{}) *MockUserStore_UploadFiles_Call {
	return &MockUserStore_UploadFiles_Call{Call: _e.mock.On("UploadFiles", ctx, files)}
}

func (_c *MockUserStore_UploadFiles_Call) Run(run func(ctx context.Context, files []*graphql.Upload)) *MockUserStore_UploadFiles_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*graphql.Upload))
	})
	return _c
}

func (_c *MockUserStore_UploadFiles_Call) Return(files1 []*models.File, err error) *MockUserStore_UploadFiles_Call {
	_c.Call.Return(files1, err)
	return _c
}

func (_c *MockUserStore_UploadFiles_Call) RunAndReturn(run func(ctx context.Context, files []*graphql.Upload) ([]*models.File, error)) *MockUserStore_UploadFiles_Call {
	_c.Call.Return(run)
	return _c
}

// UploadProfilePicture provides a mock function for the type MockUserStore
func (_mock *MockUserStore) UploadProfilePicture(ctx context.Context, upload graphql.Upload) (*models.File, error) {
	ret := _mock.Called(ctx, upload)

	if len(ret) == 0 {
		panic("no return value specified for UploadProfilePicture")
	}

	var r0 *models.File
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, graphql.Upload) (*models.File, error)); ok {
		return returnFunc(ctx, upload)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, graphql.Upload) *models.File); ok {
		r0 = returnFunc(ctx, upload)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.File)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, graphql.Upload) error); ok {
		r1 = returnFunc(ctx, upload)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_UploadProfilePicture_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadProfilePicture'
type MockUserStore_UploadProfilePicture_Call struct {
	*mock.Call
}

// UploadProfilePicture is a helper method to define mock.On call
//   - ctx
//   - upload
func (_e *MockUserStore_Expecter) UploadProfilePicture(ctx interface{}, upload interface{}) *MockUserStore_UploadProfilePicture_Call {
	return &MockUserStore_UploadProfilePicture_Call{Call: _e.mock.On("UploadProfilePicture", ctx, upload)}
}

func (_c *MockUserStore_UploadProfilePicture_Call) Run(run func(ctx context.Context, upload graphql.Upload)) *MockUserStore_UploadProfilePicture_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(graphql.Upload))
	})
	return _c
}

func (_c *MockUserStore_UploadProfilePicture_Call) Return(file *models.File, err error) *MockUserStore_UploadProfilePicture_Call {
	_c.Call.Return(file, err)
	return _c
}

func (_c *MockUserStore_UploadProfilePicture_Call) RunAndReturn(run func(ctx context.Context, upload graphql.Upload) (*models.File, error)) *MockUserStore_UploadProfilePicture_Call {
	_c.Call.Return(run)
	return _c
}

// UserEventsSubscription provides a mock function for the type MockUserStore
func (_mock *MockUserStore) UserEventsSubscription(ctx context.Context, userID *primitive.ObjectID) (<-chan models.UserEvent, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for UserEventsSubscription")
	}

	var r0 <-chan models.UserEvent
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) (<-chan models.UserEvent, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) <-chan models.UserEvent); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(<-chan models.UserEvent)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_UserEventsSubscription_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UserEventsSubscription'
type MockUserStore_UserEventsSubscription_Call struct {
	*mock.Call
}

// UserEventsSubscription is a helper method to define mock.On call
//   - ctx
//   - userID
func (_e *MockUserStore_Expecter) UserEventsSubscription(ctx interface{}, userID interface{}) *MockUserStore_UserEventsSubscription_Call {
	return &MockUserStore_UserEventsSubscription_Call{Call: _e.mock.On("UserEventsSubscription", ctx, userID)}
}

func (_c *MockUserStore_UserEventsSubscription_Call) Run(run func(ctx context.Context, userID *primitive.ObjectID)) *MockUserStore_UserEventsSubscription_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserStore_UserEventsSubscription_Call) Return(userEventCh <-chan models.UserEvent, err error) *MockUserStore_UserEventsSubscription_Call {
	_c.Call.Return(userEventCh, err)
	return _c
}

func (_c *MockUserStore_UserEventsSubscription_Call) RunAndReturn(run func(ctx context.Context, userID *primitive.ObjectID) (<-chan models.UserEvent, error)) *MockUserStore_UserEventsSubscription_Call {
	_c.Call.Return(run)
	return _c
}

// VerifyOTP provides a mock function for the type MockUserStore
func (_mock *MockUserStore) VerifyOTP(ctx context.Context, otp string) (bool, error) {
	ret := _mock.Called(ctx, otp)

	if len(ret) == 0 {
		panic("no return value specified for VerifyOTP")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return returnFunc(ctx, otp)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = returnFunc(ctx, otp)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, otp)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_VerifyOTP_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'VerifyOTP'
type MockUserStore_VerifyOTP_Call struct {
	*mock.Call
}

// VerifyOTP is a helper method to define mock.On call
//   - ctx
//   - otp
func (_e *MockUserStore_Expecter) VerifyOTP(ctx interface{}, otp interface{}) *MockUserStore_VerifyOTP_Call {
	return &MockUserStore_VerifyOTP_Call{Call: _e.mock.On("VerifyOTP", ctx, otp)}
}

func (_c *MockUserStore_VerifyOTP_Call) Run(run func(ctx context.Context, otp string)) *MockUserStore_VerifyOTP_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockUserStore_VerifyOTP_Call) Return(b bool, err error) *MockUserStore_VerifyOTP_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockUserStore_VerifyOTP_Call) RunAndReturn(run func(ctx context.Context, otp string) (bool, error)) *MockUserStore_VerifyOTP_Call {
	_c.Call.Return(run)
	return _c
}

// VerifyToken provides a mock function for the type MockUserStore
func (_mock *MockUserStore) VerifyToken(ctx context.Context, token string) (*models.User, error) {
	ret := _mock.Called(ctx, token)

	if len(ret) == 0 {
		panic("no return value specified for VerifyToken")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*models.User, error)); ok {
		return returnFunc(ctx, token)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *models.User); ok {
		r0 = returnFunc(ctx, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, token)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStore_VerifyToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'VerifyToken'
type MockUserStore_VerifyToken_Call struct {
	*mock.Call
}

// VerifyToken is a helper method to define mock.On call
//   - ctx
//   - token
func (_e *MockUserStore_Expecter) VerifyToken(ctx interface{}, token interface{}) *MockUserStore_VerifyToken_Call {
	return &MockUserStore_VerifyToken_Call{Call: _e.mock.On("VerifyToken", ctx, token)}
}

func (_c *MockUserStore_VerifyToken_Call) Run(run func(ctx context.Context, token string)) *MockUserStore_VerifyToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockUserStore_VerifyToken_Call) Return(user *models.User, err error) *MockUserStore_VerifyToken_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockUserStore_VerifyToken_Call) RunAndReturn(run func(ctx context.Context, token string) (*models.User, error)) *MockUserStore_VerifyToken_Call {
	_c.Call.Return(run)
	return _c
}
