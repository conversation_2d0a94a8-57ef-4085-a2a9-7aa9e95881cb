// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package generated

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	"github.com/99designs/gqlgen/graphql"
	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockMutationResolver creates a new instance of MockMutationResolver. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockMutationResolver(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockMutationResolver {
	mock := &MockMutationResolver{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockMutationResolver is an autogenerated mock type for the MutationResolver type
type MockMutationResolver struct {
	mock.Mock
}

type MockMutationResolver_Expecter struct {
	mock *mock.Mock
}

func (_m *MockMutationResolver) EXPECT() *MockMutationResolver_Expecter {
	return &MockMutationResolver_Expecter{mock: &_m.Mock}
}

// AbortSearching provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) AbortSearching(ctx context.Context) (*bool, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for AbortSearching")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*bool, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *bool); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_AbortSearching_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AbortSearching'
type MockMutationResolver_AbortSearching_Call struct {
	*mock.Call
}

// AbortSearching is a helper method to define mock.On call
//   - ctx
func (_e *MockMutationResolver_Expecter) AbortSearching(ctx interface{}) *MockMutationResolver_AbortSearching_Call {
	return &MockMutationResolver_AbortSearching_Call{Call: _e.mock.On("AbortSearching", ctx)}
}

func (_c *MockMutationResolver_AbortSearching_Call) Run(run func(ctx context.Context)) *MockMutationResolver_AbortSearching_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockMutationResolver_AbortSearching_Call) Return(b *bool, err error) *MockMutationResolver_AbortSearching_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_AbortSearching_Call) RunAndReturn(run func(ctx context.Context) (*bool, error)) *MockMutationResolver_AbortSearching_Call {
	_c.Call.Return(run)
	return _c
}

// AbortSearchingForPuzzleGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) AbortSearchingForPuzzleGame(ctx context.Context) (*bool, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for AbortSearchingForPuzzleGame")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*bool, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *bool); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_AbortSearchingForPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AbortSearchingForPuzzleGame'
type MockMutationResolver_AbortSearchingForPuzzleGame_Call struct {
	*mock.Call
}

// AbortSearchingForPuzzleGame is a helper method to define mock.On call
//   - ctx
func (_e *MockMutationResolver_Expecter) AbortSearchingForPuzzleGame(ctx interface{}) *MockMutationResolver_AbortSearchingForPuzzleGame_Call {
	return &MockMutationResolver_AbortSearchingForPuzzleGame_Call{Call: _e.mock.On("AbortSearchingForPuzzleGame", ctx)}
}

func (_c *MockMutationResolver_AbortSearchingForPuzzleGame_Call) Run(run func(ctx context.Context)) *MockMutationResolver_AbortSearchingForPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockMutationResolver_AbortSearchingForPuzzleGame_Call) Return(b *bool, err error) *MockMutationResolver_AbortSearchingForPuzzleGame_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_AbortSearchingForPuzzleGame_Call) RunAndReturn(run func(ctx context.Context) (*bool, error)) *MockMutationResolver_AbortSearchingForPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// AcceptChallenge provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) AcceptChallenge(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for AcceptChallenge")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Game, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Game); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_AcceptChallenge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcceptChallenge'
type MockMutationResolver_AcceptChallenge_Call struct {
	*mock.Call
}

// AcceptChallenge is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) AcceptChallenge(ctx interface{}, gameID interface{}) *MockMutationResolver_AcceptChallenge_Call {
	return &MockMutationResolver_AcceptChallenge_Call{Call: _e.mock.On("AcceptChallenge", ctx, gameID)}
}

func (_c *MockMutationResolver_AcceptChallenge_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockMutationResolver_AcceptChallenge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_AcceptChallenge_Call) Return(game *models.Game, err error) *MockMutationResolver_AcceptChallenge_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockMutationResolver_AcceptChallenge_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error)) *MockMutationResolver_AcceptChallenge_Call {
	_c.Call.Return(run)
	return _c
}

// AcceptChallengeOfPuzzleGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) AcceptChallengeOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for AcceptChallengeOfPuzzleGame")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_AcceptChallengeOfPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcceptChallengeOfPuzzleGame'
type MockMutationResolver_AcceptChallengeOfPuzzleGame_Call struct {
	*mock.Call
}

// AcceptChallengeOfPuzzleGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) AcceptChallengeOfPuzzleGame(ctx interface{}, gameID interface{}) *MockMutationResolver_AcceptChallengeOfPuzzleGame_Call {
	return &MockMutationResolver_AcceptChallengeOfPuzzleGame_Call{Call: _e.mock.On("AcceptChallengeOfPuzzleGame", ctx, gameID)}
}

func (_c *MockMutationResolver_AcceptChallengeOfPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockMutationResolver_AcceptChallengeOfPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_AcceptChallengeOfPuzzleGame_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockMutationResolver_AcceptChallengeOfPuzzleGame_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockMutationResolver_AcceptChallengeOfPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)) *MockMutationResolver_AcceptChallengeOfPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// AcceptFriendRequest provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) AcceptFriendRequest(ctx context.Context, acceptRequestInput *models.FriendRequestInput) (bool, error) {
	ret := _mock.Called(ctx, acceptRequestInput)

	if len(ret) == 0 {
		panic("no return value specified for AcceptFriendRequest")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.FriendRequestInput) (bool, error)); ok {
		return returnFunc(ctx, acceptRequestInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.FriendRequestInput) bool); ok {
		r0 = returnFunc(ctx, acceptRequestInput)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.FriendRequestInput) error); ok {
		r1 = returnFunc(ctx, acceptRequestInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_AcceptFriendRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcceptFriendRequest'
type MockMutationResolver_AcceptFriendRequest_Call struct {
	*mock.Call
}

// AcceptFriendRequest is a helper method to define mock.On call
//   - ctx
//   - acceptRequestInput
func (_e *MockMutationResolver_Expecter) AcceptFriendRequest(ctx interface{}, acceptRequestInput interface{}) *MockMutationResolver_AcceptFriendRequest_Call {
	return &MockMutationResolver_AcceptFriendRequest_Call{Call: _e.mock.On("AcceptFriendRequest", ctx, acceptRequestInput)}
}

func (_c *MockMutationResolver_AcceptFriendRequest_Call) Run(run func(ctx context.Context, acceptRequestInput *models.FriendRequestInput)) *MockMutationResolver_AcceptFriendRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.FriendRequestInput))
	})
	return _c
}

func (_c *MockMutationResolver_AcceptFriendRequest_Call) Return(b bool, err error) *MockMutationResolver_AcceptFriendRequest_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_AcceptFriendRequest_Call) RunAndReturn(run func(ctx context.Context, acceptRequestInput *models.FriendRequestInput) (bool, error)) *MockMutationResolver_AcceptFriendRequest_Call {
	_c.Call.Return(run)
	return _c
}

// AcceptRematch provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) AcceptRematch(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for AcceptRematch")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Game, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Game); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_AcceptRematch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcceptRematch'
type MockMutationResolver_AcceptRematch_Call struct {
	*mock.Call
}

// AcceptRematch is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) AcceptRematch(ctx interface{}, gameID interface{}) *MockMutationResolver_AcceptRematch_Call {
	return &MockMutationResolver_AcceptRematch_Call{Call: _e.mock.On("AcceptRematch", ctx, gameID)}
}

func (_c *MockMutationResolver_AcceptRematch_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockMutationResolver_AcceptRematch_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_AcceptRematch_Call) Return(game *models.Game, err error) *MockMutationResolver_AcceptRematch_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockMutationResolver_AcceptRematch_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error)) *MockMutationResolver_AcceptRematch_Call {
	_c.Call.Return(run)
	return _c
}

// AcceptRematchOfPuzzleGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) AcceptRematchOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for AcceptRematchOfPuzzleGame")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_AcceptRematchOfPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcceptRematchOfPuzzleGame'
type MockMutationResolver_AcceptRematchOfPuzzleGame_Call struct {
	*mock.Call
}

// AcceptRematchOfPuzzleGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) AcceptRematchOfPuzzleGame(ctx interface{}, gameID interface{}) *MockMutationResolver_AcceptRematchOfPuzzleGame_Call {
	return &MockMutationResolver_AcceptRematchOfPuzzleGame_Call{Call: _e.mock.On("AcceptRematchOfPuzzleGame", ctx, gameID)}
}

func (_c *MockMutationResolver_AcceptRematchOfPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockMutationResolver_AcceptRematchOfPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_AcceptRematchOfPuzzleGame_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockMutationResolver_AcceptRematchOfPuzzleGame_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockMutationResolver_AcceptRematchOfPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)) *MockMutationResolver_AcceptRematchOfPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// AddClubMember provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) AddClubMember(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, clubID, userID)

	if len(ret) == 0 {
		panic("no return value specified for AddClubMember")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, clubID, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, clubID, userID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, clubID, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_AddClubMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddClubMember'
type MockMutationResolver_AddClubMember_Call struct {
	*mock.Call
}

// AddClubMember is a helper method to define mock.On call
//   - ctx
//   - clubID
//   - userID
func (_e *MockMutationResolver_Expecter) AddClubMember(ctx interface{}, clubID interface{}, userID interface{}) *MockMutationResolver_AddClubMember_Call {
	return &MockMutationResolver_AddClubMember_Call{Call: _e.mock.On("AddClubMember", ctx, clubID, userID)}
}

func (_c *MockMutationResolver_AddClubMember_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID)) *MockMutationResolver_AddClubMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_AddClubMember_Call) Return(b bool, err error) *MockMutationResolver_AddClubMember_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_AddClubMember_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID) (bool, error)) *MockMutationResolver_AddClubMember_Call {
	_c.Call.Return(run)
	return _c
}

// AttemptDailyChallenge provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) AttemptDailyChallenge(ctx context.Context, challengeID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, challengeID)

	if len(ret) == 0 {
		panic("no return value specified for AttemptDailyChallenge")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, challengeID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, challengeID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, challengeID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_AttemptDailyChallenge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AttemptDailyChallenge'
type MockMutationResolver_AttemptDailyChallenge_Call struct {
	*mock.Call
}

// AttemptDailyChallenge is a helper method to define mock.On call
//   - ctx
//   - challengeID
func (_e *MockMutationResolver_Expecter) AttemptDailyChallenge(ctx interface{}, challengeID interface{}) *MockMutationResolver_AttemptDailyChallenge_Call {
	return &MockMutationResolver_AttemptDailyChallenge_Call{Call: _e.mock.On("AttemptDailyChallenge", ctx, challengeID)}
}

func (_c *MockMutationResolver_AttemptDailyChallenge_Call) Run(run func(ctx context.Context, challengeID primitive.ObjectID)) *MockMutationResolver_AttemptDailyChallenge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_AttemptDailyChallenge_Call) Return(b bool, err error) *MockMutationResolver_AttemptDailyChallenge_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_AttemptDailyChallenge_Call) RunAndReturn(run func(ctx context.Context, challengeID primitive.ObjectID) (bool, error)) *MockMutationResolver_AttemptDailyChallenge_Call {
	_c.Call.Return(run)
	return _c
}

// CancelGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) CancelGame(ctx context.Context, gameID primitive.ObjectID) (*bool, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for CancelGame")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*bool, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *bool); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_CancelGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CancelGame'
type MockMutationResolver_CancelGame_Call struct {
	*mock.Call
}

// CancelGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) CancelGame(ctx interface{}, gameID interface{}) *MockMutationResolver_CancelGame_Call {
	return &MockMutationResolver_CancelGame_Call{Call: _e.mock.On("CancelGame", ctx, gameID)}
}

func (_c *MockMutationResolver_CancelGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockMutationResolver_CancelGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_CancelGame_Call) Return(b *bool, err error) *MockMutationResolver_CancelGame_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_CancelGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*bool, error)) *MockMutationResolver_CancelGame_Call {
	_c.Call.Return(run)
	return _c
}

// CancelPuzzleGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) CancelPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*bool, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for CancelPuzzleGame")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*bool, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *bool); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_CancelPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CancelPuzzleGame'
type MockMutationResolver_CancelPuzzleGame_Call struct {
	*mock.Call
}

// CancelPuzzleGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) CancelPuzzleGame(ctx interface{}, gameID interface{}) *MockMutationResolver_CancelPuzzleGame_Call {
	return &MockMutationResolver_CancelPuzzleGame_Call{Call: _e.mock.On("CancelPuzzleGame", ctx, gameID)}
}

func (_c *MockMutationResolver_CancelPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockMutationResolver_CancelPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_CancelPuzzleGame_Call) Return(b *bool, err error) *MockMutationResolver_CancelPuzzleGame_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_CancelPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*bool, error)) *MockMutationResolver_CancelPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// CancelRematchRequest provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) CancelRematchRequest(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for CancelRematchRequest")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_CancelRematchRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CancelRematchRequest'
type MockMutationResolver_CancelRematchRequest_Call struct {
	*mock.Call
}

// CancelRematchRequest is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) CancelRematchRequest(ctx interface{}, gameID interface{}) *MockMutationResolver_CancelRematchRequest_Call {
	return &MockMutationResolver_CancelRematchRequest_Call{Call: _e.mock.On("CancelRematchRequest", ctx, gameID)}
}

func (_c *MockMutationResolver_CancelRematchRequest_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockMutationResolver_CancelRematchRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_CancelRematchRequest_Call) Return(b bool, err error) *MockMutationResolver_CancelRematchRequest_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_CancelRematchRequest_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (bool, error)) *MockMutationResolver_CancelRematchRequest_Call {
	_c.Call.Return(run)
	return _c
}

// ChallengeUser provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) ChallengeUser(ctx context.Context, challengeUserInput *models.ChallengeUserInput) (*models.Game, error) {
	ret := _mock.Called(ctx, challengeUserInput)

	if len(ret) == 0 {
		panic("no return value specified for ChallengeUser")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ChallengeUserInput) (*models.Game, error)); ok {
		return returnFunc(ctx, challengeUserInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ChallengeUserInput) *models.Game); ok {
		r0 = returnFunc(ctx, challengeUserInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.ChallengeUserInput) error); ok {
		r1 = returnFunc(ctx, challengeUserInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_ChallengeUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ChallengeUser'
type MockMutationResolver_ChallengeUser_Call struct {
	*mock.Call
}

// ChallengeUser is a helper method to define mock.On call
//   - ctx
//   - challengeUserInput
func (_e *MockMutationResolver_Expecter) ChallengeUser(ctx interface{}, challengeUserInput interface{}) *MockMutationResolver_ChallengeUser_Call {
	return &MockMutationResolver_ChallengeUser_Call{Call: _e.mock.On("ChallengeUser", ctx, challengeUserInput)}
}

func (_c *MockMutationResolver_ChallengeUser_Call) Run(run func(ctx context.Context, challengeUserInput *models.ChallengeUserInput)) *MockMutationResolver_ChallengeUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.ChallengeUserInput))
	})
	return _c
}

func (_c *MockMutationResolver_ChallengeUser_Call) Return(game *models.Game, err error) *MockMutationResolver_ChallengeUser_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockMutationResolver_ChallengeUser_Call) RunAndReturn(run func(ctx context.Context, challengeUserInput *models.ChallengeUserInput) (*models.Game, error)) *MockMutationResolver_ChallengeUser_Call {
	_c.Call.Return(run)
	return _c
}

// ChallengeUserForPuzzleGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) ChallengeUserForPuzzleGame(ctx context.Context, challengeUserInput *models.ChallengeUserForPuzzleGameInput) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, challengeUserInput)

	if len(ret) == 0 {
		panic("no return value specified for ChallengeUserForPuzzleGame")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ChallengeUserForPuzzleGameInput) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, challengeUserInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ChallengeUserForPuzzleGameInput) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, challengeUserInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.ChallengeUserForPuzzleGameInput) error); ok {
		r1 = returnFunc(ctx, challengeUserInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_ChallengeUserForPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ChallengeUserForPuzzleGame'
type MockMutationResolver_ChallengeUserForPuzzleGame_Call struct {
	*mock.Call
}

// ChallengeUserForPuzzleGame is a helper method to define mock.On call
//   - ctx
//   - challengeUserInput
func (_e *MockMutationResolver_Expecter) ChallengeUserForPuzzleGame(ctx interface{}, challengeUserInput interface{}) *MockMutationResolver_ChallengeUserForPuzzleGame_Call {
	return &MockMutationResolver_ChallengeUserForPuzzleGame_Call{Call: _e.mock.On("ChallengeUserForPuzzleGame", ctx, challengeUserInput)}
}

func (_c *MockMutationResolver_ChallengeUserForPuzzleGame_Call) Run(run func(ctx context.Context, challengeUserInput *models.ChallengeUserForPuzzleGameInput)) *MockMutationResolver_ChallengeUserForPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.ChallengeUserForPuzzleGameInput))
	})
	return _c
}

func (_c *MockMutationResolver_ChallengeUserForPuzzleGame_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockMutationResolver_ChallengeUserForPuzzleGame_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockMutationResolver_ChallengeUserForPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, challengeUserInput *models.ChallengeUserForPuzzleGameInput) (*models.PuzzleGame, error)) *MockMutationResolver_ChallengeUserForPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// CreateAnnouncement provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) CreateAnnouncement(ctx context.Context, input models.CreateAnnouncementInput) (*models.Announcement, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateAnnouncement")
	}

	var r0 *models.Announcement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateAnnouncementInput) (*models.Announcement, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateAnnouncementInput) *models.Announcement); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Announcement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateAnnouncementInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_CreateAnnouncement_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateAnnouncement'
type MockMutationResolver_CreateAnnouncement_Call struct {
	*mock.Call
}

// CreateAnnouncement is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) CreateAnnouncement(ctx interface{}, input interface{}) *MockMutationResolver_CreateAnnouncement_Call {
	return &MockMutationResolver_CreateAnnouncement_Call{Call: _e.mock.On("CreateAnnouncement", ctx, input)}
}

func (_c *MockMutationResolver_CreateAnnouncement_Call) Run(run func(ctx context.Context, input models.CreateAnnouncementInput)) *MockMutationResolver_CreateAnnouncement_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.CreateAnnouncementInput))
	})
	return _c
}

func (_c *MockMutationResolver_CreateAnnouncement_Call) Return(announcement *models.Announcement, err error) *MockMutationResolver_CreateAnnouncement_Call {
	_c.Call.Return(announcement, err)
	return _c
}

func (_c *MockMutationResolver_CreateAnnouncement_Call) RunAndReturn(run func(ctx context.Context, input models.CreateAnnouncementInput) (*models.Announcement, error)) *MockMutationResolver_CreateAnnouncement_Call {
	_c.Call.Return(run)
	return _c
}

// CreateClub provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) CreateClub(ctx context.Context, input models.CreateClubInput) (*models.Club, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateClub")
	}

	var r0 *models.Club
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateClubInput) (*models.Club, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateClubInput) *models.Club); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Club)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateClubInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_CreateClub_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateClub'
type MockMutationResolver_CreateClub_Call struct {
	*mock.Call
}

// CreateClub is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) CreateClub(ctx interface{}, input interface{}) *MockMutationResolver_CreateClub_Call {
	return &MockMutationResolver_CreateClub_Call{Call: _e.mock.On("CreateClub", ctx, input)}
}

func (_c *MockMutationResolver_CreateClub_Call) Run(run func(ctx context.Context, input models.CreateClubInput)) *MockMutationResolver_CreateClub_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.CreateClubInput))
	})
	return _c
}

func (_c *MockMutationResolver_CreateClub_Call) Return(club *models.Club, err error) *MockMutationResolver_CreateClub_Call {
	_c.Call.Return(club, err)
	return _c
}

func (_c *MockMutationResolver_CreateClub_Call) RunAndReturn(run func(ctx context.Context, input models.CreateClubInput) (*models.Club, error)) *MockMutationResolver_CreateClub_Call {
	_c.Call.Return(run)
	return _c
}

// CreateClubAnnouncement provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) CreateClubAnnouncement(ctx context.Context, input models.CreateClubAnnouncementInput) (*models.ClubAnnouncement, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateClubAnnouncement")
	}

	var r0 *models.ClubAnnouncement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateClubAnnouncementInput) (*models.ClubAnnouncement, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateClubAnnouncementInput) *models.ClubAnnouncement); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubAnnouncement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateClubAnnouncementInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_CreateClubAnnouncement_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateClubAnnouncement'
type MockMutationResolver_CreateClubAnnouncement_Call struct {
	*mock.Call
}

// CreateClubAnnouncement is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) CreateClubAnnouncement(ctx interface{}, input interface{}) *MockMutationResolver_CreateClubAnnouncement_Call {
	return &MockMutationResolver_CreateClubAnnouncement_Call{Call: _e.mock.On("CreateClubAnnouncement", ctx, input)}
}

func (_c *MockMutationResolver_CreateClubAnnouncement_Call) Run(run func(ctx context.Context, input models.CreateClubAnnouncementInput)) *MockMutationResolver_CreateClubAnnouncement_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.CreateClubAnnouncementInput))
	})
	return _c
}

func (_c *MockMutationResolver_CreateClubAnnouncement_Call) Return(clubAnnouncement *models.ClubAnnouncement, err error) *MockMutationResolver_CreateClubAnnouncement_Call {
	_c.Call.Return(clubAnnouncement, err)
	return _c
}

func (_c *MockMutationResolver_CreateClubAnnouncement_Call) RunAndReturn(run func(ctx context.Context, input models.CreateClubAnnouncementInput) (*models.ClubAnnouncement, error)) *MockMutationResolver_CreateClubAnnouncement_Call {
	_c.Call.Return(run)
	return _c
}

// CreateClubEvent provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) CreateClubEvent(ctx context.Context, input models.CreateClubEventInput) (*models.ClubEvent, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateClubEvent")
	}

	var r0 *models.ClubEvent
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateClubEventInput) (*models.ClubEvent, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateClubEventInput) *models.ClubEvent); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubEvent)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateClubEventInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_CreateClubEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateClubEvent'
type MockMutationResolver_CreateClubEvent_Call struct {
	*mock.Call
}

// CreateClubEvent is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) CreateClubEvent(ctx interface{}, input interface{}) *MockMutationResolver_CreateClubEvent_Call {
	return &MockMutationResolver_CreateClubEvent_Call{Call: _e.mock.On("CreateClubEvent", ctx, input)}
}

func (_c *MockMutationResolver_CreateClubEvent_Call) Run(run func(ctx context.Context, input models.CreateClubEventInput)) *MockMutationResolver_CreateClubEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.CreateClubEventInput))
	})
	return _c
}

func (_c *MockMutationResolver_CreateClubEvent_Call) Return(clubEvent *models.ClubEvent, err error) *MockMutationResolver_CreateClubEvent_Call {
	_c.Call.Return(clubEvent, err)
	return _c
}

func (_c *MockMutationResolver_CreateClubEvent_Call) RunAndReturn(run func(ctx context.Context, input models.CreateClubEventInput) (*models.ClubEvent, error)) *MockMutationResolver_CreateClubEvent_Call {
	_c.Call.Return(run)
	return _c
}

// CreateContest provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) CreateContest(ctx context.Context, input models.CreateContestInput) (*models.Contest, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateContest")
	}

	var r0 *models.Contest
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateContestInput) (*models.Contest, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateContestInput) *models.Contest); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Contest)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateContestInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_CreateContest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateContest'
type MockMutationResolver_CreateContest_Call struct {
	*mock.Call
}

// CreateContest is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) CreateContest(ctx interface{}, input interface{}) *MockMutationResolver_CreateContest_Call {
	return &MockMutationResolver_CreateContest_Call{Call: _e.mock.On("CreateContest", ctx, input)}
}

func (_c *MockMutationResolver_CreateContest_Call) Run(run func(ctx context.Context, input models.CreateContestInput)) *MockMutationResolver_CreateContest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.CreateContestInput))
	})
	return _c
}

func (_c *MockMutationResolver_CreateContest_Call) Return(contest *models.Contest, err error) *MockMutationResolver_CreateContest_Call {
	_c.Call.Return(contest, err)
	return _c
}

func (_c *MockMutationResolver_CreateContest_Call) RunAndReturn(run func(ctx context.Context, input models.CreateContestInput) (*models.Contest, error)) *MockMutationResolver_CreateContest_Call {
	_c.Call.Return(run)
	return _c
}

// CreateForum provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) CreateForum(ctx context.Context, input models.CreateForumInput) (*models.Forum, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateForum")
	}

	var r0 *models.Forum
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateForumInput) (*models.Forum, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateForumInput) *models.Forum); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Forum)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateForumInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_CreateForum_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateForum'
type MockMutationResolver_CreateForum_Call struct {
	*mock.Call
}

// CreateForum is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) CreateForum(ctx interface{}, input interface{}) *MockMutationResolver_CreateForum_Call {
	return &MockMutationResolver_CreateForum_Call{Call: _e.mock.On("CreateForum", ctx, input)}
}

func (_c *MockMutationResolver_CreateForum_Call) Run(run func(ctx context.Context, input models.CreateForumInput)) *MockMutationResolver_CreateForum_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.CreateForumInput))
	})
	return _c
}

func (_c *MockMutationResolver_CreateForum_Call) Return(forum *models.Forum, err error) *MockMutationResolver_CreateForum_Call {
	_c.Call.Return(forum, err)
	return _c
}

func (_c *MockMutationResolver_CreateForum_Call) RunAndReturn(run func(ctx context.Context, input models.CreateForumInput) (*models.Forum, error)) *MockMutationResolver_CreateForum_Call {
	_c.Call.Return(run)
	return _c
}

// CreateForumReply provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) CreateForumReply(ctx context.Context, input models.CreateForumReplyInput) (*models.ForumReply, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateForumReply")
	}

	var r0 *models.ForumReply
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateForumReplyInput) (*models.ForumReply, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateForumReplyInput) *models.ForumReply); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ForumReply)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateForumReplyInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_CreateForumReply_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateForumReply'
type MockMutationResolver_CreateForumReply_Call struct {
	*mock.Call
}

// CreateForumReply is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) CreateForumReply(ctx interface{}, input interface{}) *MockMutationResolver_CreateForumReply_Call {
	return &MockMutationResolver_CreateForumReply_Call{Call: _e.mock.On("CreateForumReply", ctx, input)}
}

func (_c *MockMutationResolver_CreateForumReply_Call) Run(run func(ctx context.Context, input models.CreateForumReplyInput)) *MockMutationResolver_CreateForumReply_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.CreateForumReplyInput))
	})
	return _c
}

func (_c *MockMutationResolver_CreateForumReply_Call) Return(forumReply *models.ForumReply, err error) *MockMutationResolver_CreateForumReply_Call {
	_c.Call.Return(forumReply, err)
	return _c
}

func (_c *MockMutationResolver_CreateForumReply_Call) RunAndReturn(run func(ctx context.Context, input models.CreateForumReplyInput) (*models.ForumReply, error)) *MockMutationResolver_CreateForumReply_Call {
	_c.Call.Return(run)
	return _c
}

// CreateForumThread provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) CreateForumThread(ctx context.Context, input models.CreateForumThreadInput) (*models.ForumThread, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateForumThread")
	}

	var r0 *models.ForumThread
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateForumThreadInput) (*models.ForumThread, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateForumThreadInput) *models.ForumThread); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ForumThread)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateForumThreadInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_CreateForumThread_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateForumThread'
type MockMutationResolver_CreateForumThread_Call struct {
	*mock.Call
}

// CreateForumThread is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) CreateForumThread(ctx interface{}, input interface{}) *MockMutationResolver_CreateForumThread_Call {
	return &MockMutationResolver_CreateForumThread_Call{Call: _e.mock.On("CreateForumThread", ctx, input)}
}

func (_c *MockMutationResolver_CreateForumThread_Call) Run(run func(ctx context.Context, input models.CreateForumThreadInput)) *MockMutationResolver_CreateForumThread_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.CreateForumThreadInput))
	})
	return _c
}

func (_c *MockMutationResolver_CreateForumThread_Call) Return(forumThread *models.ForumThread, err error) *MockMutationResolver_CreateForumThread_Call {
	_c.Call.Return(forumThread, err)
	return _c
}

func (_c *MockMutationResolver_CreateForumThread_Call) RunAndReturn(run func(ctx context.Context, input models.CreateForumThreadInput) (*models.ForumThread, error)) *MockMutationResolver_CreateForumThread_Call {
	_c.Call.Return(run)
	return _c
}

// CreateGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) CreateGame(ctx context.Context, gameConfig *models.GameConfigInput) (*models.Game, error) {
	ret := _mock.Called(ctx, gameConfig)

	if len(ret) == 0 {
		panic("no return value specified for CreateGame")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GameConfigInput) (*models.Game, error)); ok {
		return returnFunc(ctx, gameConfig)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GameConfigInput) *models.Game); ok {
		r0 = returnFunc(ctx, gameConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.GameConfigInput) error); ok {
		r1 = returnFunc(ctx, gameConfig)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_CreateGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateGame'
type MockMutationResolver_CreateGame_Call struct {
	*mock.Call
}

// CreateGame is a helper method to define mock.On call
//   - ctx
//   - gameConfig
func (_e *MockMutationResolver_Expecter) CreateGame(ctx interface{}, gameConfig interface{}) *MockMutationResolver_CreateGame_Call {
	return &MockMutationResolver_CreateGame_Call{Call: _e.mock.On("CreateGame", ctx, gameConfig)}
}

func (_c *MockMutationResolver_CreateGame_Call) Run(run func(ctx context.Context, gameConfig *models.GameConfigInput)) *MockMutationResolver_CreateGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.GameConfigInput))
	})
	return _c
}

func (_c *MockMutationResolver_CreateGame_Call) Return(game *models.Game, err error) *MockMutationResolver_CreateGame_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockMutationResolver_CreateGame_Call) RunAndReturn(run func(ctx context.Context, gameConfig *models.GameConfigInput) (*models.Game, error)) *MockMutationResolver_CreateGame_Call {
	_c.Call.Return(run)
	return _c
}

// CreateInstitution provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) CreateInstitution(ctx context.Context, input models.CreateInstitutionInput) (*models.Institution, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateInstitution")
	}

	var r0 *models.Institution
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateInstitutionInput) (*models.Institution, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateInstitutionInput) *models.Institution); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Institution)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateInstitutionInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_CreateInstitution_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateInstitution'
type MockMutationResolver_CreateInstitution_Call struct {
	*mock.Call
}

// CreateInstitution is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) CreateInstitution(ctx interface{}, input interface{}) *MockMutationResolver_CreateInstitution_Call {
	return &MockMutationResolver_CreateInstitution_Call{Call: _e.mock.On("CreateInstitution", ctx, input)}
}

func (_c *MockMutationResolver_CreateInstitution_Call) Run(run func(ctx context.Context, input models.CreateInstitutionInput)) *MockMutationResolver_CreateInstitution_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.CreateInstitutionInput))
	})
	return _c
}

func (_c *MockMutationResolver_CreateInstitution_Call) Return(institution *models.Institution, err error) *MockMutationResolver_CreateInstitution_Call {
	_c.Call.Return(institution, err)
	return _c
}

func (_c *MockMutationResolver_CreateInstitution_Call) RunAndReturn(run func(ctx context.Context, input models.CreateInstitutionInput) (*models.Institution, error)) *MockMutationResolver_CreateInstitution_Call {
	_c.Call.Return(run)
	return _c
}

// CreateLeague provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) CreateLeague(ctx context.Context, input models.CreateLeagueInput) (*models.League, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateLeague")
	}

	var r0 *models.League
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateLeagueInput) (*models.League, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateLeagueInput) *models.League); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.League)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateLeagueInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_CreateLeague_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateLeague'
type MockMutationResolver_CreateLeague_Call struct {
	*mock.Call
}

// CreateLeague is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) CreateLeague(ctx interface{}, input interface{}) *MockMutationResolver_CreateLeague_Call {
	return &MockMutationResolver_CreateLeague_Call{Call: _e.mock.On("CreateLeague", ctx, input)}
}

func (_c *MockMutationResolver_CreateLeague_Call) Run(run func(ctx context.Context, input models.CreateLeagueInput)) *MockMutationResolver_CreateLeague_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.CreateLeagueInput))
	})
	return _c
}

func (_c *MockMutationResolver_CreateLeague_Call) Return(league *models.League, err error) *MockMutationResolver_CreateLeague_Call {
	_c.Call.Return(league, err)
	return _c
}

func (_c *MockMutationResolver_CreateLeague_Call) RunAndReturn(run func(ctx context.Context, input models.CreateLeagueInput) (*models.League, error)) *MockMutationResolver_CreateLeague_Call {
	_c.Call.Return(run)
	return _c
}

// CreatePuzzleGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) CreatePuzzleGame(ctx context.Context, gameConfig *models.PuzzleGameConfigInput) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, gameConfig)

	if len(ret) == 0 {
		panic("no return value specified for CreatePuzzleGame")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PuzzleGameConfigInput) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, gameConfig)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PuzzleGameConfigInput) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, gameConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.PuzzleGameConfigInput) error); ok {
		r1 = returnFunc(ctx, gameConfig)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_CreatePuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreatePuzzleGame'
type MockMutationResolver_CreatePuzzleGame_Call struct {
	*mock.Call
}

// CreatePuzzleGame is a helper method to define mock.On call
//   - ctx
//   - gameConfig
func (_e *MockMutationResolver_Expecter) CreatePuzzleGame(ctx interface{}, gameConfig interface{}) *MockMutationResolver_CreatePuzzleGame_Call {
	return &MockMutationResolver_CreatePuzzleGame_Call{Call: _e.mock.On("CreatePuzzleGame", ctx, gameConfig)}
}

func (_c *MockMutationResolver_CreatePuzzleGame_Call) Run(run func(ctx context.Context, gameConfig *models.PuzzleGameConfigInput)) *MockMutationResolver_CreatePuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.PuzzleGameConfigInput))
	})
	return _c
}

func (_c *MockMutationResolver_CreatePuzzleGame_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockMutationResolver_CreatePuzzleGame_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockMutationResolver_CreatePuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameConfig *models.PuzzleGameConfigInput) (*models.PuzzleGame, error)) *MockMutationResolver_CreatePuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// CreateShowdown provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) CreateShowdown(ctx context.Context, input models.CreateShowdownInput) (*models.Showdown, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateShowdown")
	}

	var r0 *models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateShowdownInput) (*models.Showdown, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateShowdownInput) *models.Showdown); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateShowdownInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_CreateShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateShowdown'
type MockMutationResolver_CreateShowdown_Call struct {
	*mock.Call
}

// CreateShowdown is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) CreateShowdown(ctx interface{}, input interface{}) *MockMutationResolver_CreateShowdown_Call {
	return &MockMutationResolver_CreateShowdown_Call{Call: _e.mock.On("CreateShowdown", ctx, input)}
}

func (_c *MockMutationResolver_CreateShowdown_Call) Run(run func(ctx context.Context, input models.CreateShowdownInput)) *MockMutationResolver_CreateShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.CreateShowdownInput))
	})
	return _c
}

func (_c *MockMutationResolver_CreateShowdown_Call) Return(showdown *models.Showdown, err error) *MockMutationResolver_CreateShowdown_Call {
	_c.Call.Return(showdown, err)
	return _c
}

func (_c *MockMutationResolver_CreateShowdown_Call) RunAndReturn(run func(ctx context.Context, input models.CreateShowdownInput) (*models.Showdown, error)) *MockMutationResolver_CreateShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// CreateUser provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) CreateUser(ctx context.Context, userInput *models.UserInput) (*models.User, error) {
	ret := _mock.Called(ctx, userInput)

	if len(ret) == 0 {
		panic("no return value specified for CreateUser")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserInput) (*models.User, error)); ok {
		return returnFunc(ctx, userInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserInput) *models.User); ok {
		r0 = returnFunc(ctx, userInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.UserInput) error); ok {
		r1 = returnFunc(ctx, userInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_CreateUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUser'
type MockMutationResolver_CreateUser_Call struct {
	*mock.Call
}

// CreateUser is a helper method to define mock.On call
//   - ctx
//   - userInput
func (_e *MockMutationResolver_Expecter) CreateUser(ctx interface{}, userInput interface{}) *MockMutationResolver_CreateUser_Call {
	return &MockMutationResolver_CreateUser_Call{Call: _e.mock.On("CreateUser", ctx, userInput)}
}

func (_c *MockMutationResolver_CreateUser_Call) Run(run func(ctx context.Context, userInput *models.UserInput)) *MockMutationResolver_CreateUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.UserInput))
	})
	return _c
}

func (_c *MockMutationResolver_CreateUser_Call) Return(user *models.User, err error) *MockMutationResolver_CreateUser_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockMutationResolver_CreateUser_Call) RunAndReturn(run func(ctx context.Context, userInput *models.UserInput) (*models.User, error)) *MockMutationResolver_CreateUser_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAnnouncement provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) DeleteAnnouncement(ctx context.Context, id primitive.ObjectID) (*models.AnnouncementMutationResponse, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAnnouncement")
	}

	var r0 *models.AnnouncementMutationResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.AnnouncementMutationResponse, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.AnnouncementMutationResponse); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.AnnouncementMutationResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_DeleteAnnouncement_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAnnouncement'
type MockMutationResolver_DeleteAnnouncement_Call struct {
	*mock.Call
}

// DeleteAnnouncement is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockMutationResolver_Expecter) DeleteAnnouncement(ctx interface{}, id interface{}) *MockMutationResolver_DeleteAnnouncement_Call {
	return &MockMutationResolver_DeleteAnnouncement_Call{Call: _e.mock.On("DeleteAnnouncement", ctx, id)}
}

func (_c *MockMutationResolver_DeleteAnnouncement_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockMutationResolver_DeleteAnnouncement_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_DeleteAnnouncement_Call) Return(announcementMutationResponse *models.AnnouncementMutationResponse, err error) *MockMutationResolver_DeleteAnnouncement_Call {
	_c.Call.Return(announcementMutationResponse, err)
	return _c
}

func (_c *MockMutationResolver_DeleteAnnouncement_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.AnnouncementMutationResponse, error)) *MockMutationResolver_DeleteAnnouncement_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteClub provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) DeleteClub(ctx context.Context, id primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteClub")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_DeleteClub_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteClub'
type MockMutationResolver_DeleteClub_Call struct {
	*mock.Call
}

// DeleteClub is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockMutationResolver_Expecter) DeleteClub(ctx interface{}, id interface{}) *MockMutationResolver_DeleteClub_Call {
	return &MockMutationResolver_DeleteClub_Call{Call: _e.mock.On("DeleteClub", ctx, id)}
}

func (_c *MockMutationResolver_DeleteClub_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockMutationResolver_DeleteClub_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_DeleteClub_Call) Return(b bool, err error) *MockMutationResolver_DeleteClub_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_DeleteClub_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (bool, error)) *MockMutationResolver_DeleteClub_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteClubAnnouncement provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) DeleteClubAnnouncement(ctx context.Context, id primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteClubAnnouncement")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_DeleteClubAnnouncement_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteClubAnnouncement'
type MockMutationResolver_DeleteClubAnnouncement_Call struct {
	*mock.Call
}

// DeleteClubAnnouncement is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockMutationResolver_Expecter) DeleteClubAnnouncement(ctx interface{}, id interface{}) *MockMutationResolver_DeleteClubAnnouncement_Call {
	return &MockMutationResolver_DeleteClubAnnouncement_Call{Call: _e.mock.On("DeleteClubAnnouncement", ctx, id)}
}

func (_c *MockMutationResolver_DeleteClubAnnouncement_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockMutationResolver_DeleteClubAnnouncement_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_DeleteClubAnnouncement_Call) Return(b bool, err error) *MockMutationResolver_DeleteClubAnnouncement_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_DeleteClubAnnouncement_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (bool, error)) *MockMutationResolver_DeleteClubAnnouncement_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteClubEvent provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) DeleteClubEvent(ctx context.Context, id primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteClubEvent")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_DeleteClubEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteClubEvent'
type MockMutationResolver_DeleteClubEvent_Call struct {
	*mock.Call
}

// DeleteClubEvent is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockMutationResolver_Expecter) DeleteClubEvent(ctx interface{}, id interface{}) *MockMutationResolver_DeleteClubEvent_Call {
	return &MockMutationResolver_DeleteClubEvent_Call{Call: _e.mock.On("DeleteClubEvent", ctx, id)}
}

func (_c *MockMutationResolver_DeleteClubEvent_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockMutationResolver_DeleteClubEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_DeleteClubEvent_Call) Return(b bool, err error) *MockMutationResolver_DeleteClubEvent_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_DeleteClubEvent_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (bool, error)) *MockMutationResolver_DeleteClubEvent_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUser provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) DeleteUser(ctx context.Context) (bool, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUser")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (bool, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) bool); ok {
		r0 = returnFunc(ctx)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_DeleteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUser'
type MockMutationResolver_DeleteUser_Call struct {
	*mock.Call
}

// DeleteUser is a helper method to define mock.On call
//   - ctx
func (_e *MockMutationResolver_Expecter) DeleteUser(ctx interface{}) *MockMutationResolver_DeleteUser_Call {
	return &MockMutationResolver_DeleteUser_Call{Call: _e.mock.On("DeleteUser", ctx)}
}

func (_c *MockMutationResolver_DeleteUser_Call) Run(run func(ctx context.Context)) *MockMutationResolver_DeleteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockMutationResolver_DeleteUser_Call) Return(b bool, err error) *MockMutationResolver_DeleteUser_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_DeleteUser_Call) RunAndReturn(run func(ctx context.Context) (bool, error)) *MockMutationResolver_DeleteUser_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUserSavedPreset provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) DeleteUserSavedPreset(ctx context.Context, presetID primitive.ObjectID) (*bool, error) {
	ret := _mock.Called(ctx, presetID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUserSavedPreset")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*bool, error)); ok {
		return returnFunc(ctx, presetID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *bool); ok {
		r0 = returnFunc(ctx, presetID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, presetID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_DeleteUserSavedPreset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUserSavedPreset'
type MockMutationResolver_DeleteUserSavedPreset_Call struct {
	*mock.Call
}

// DeleteUserSavedPreset is a helper method to define mock.On call
//   - ctx
//   - presetID
func (_e *MockMutationResolver_Expecter) DeleteUserSavedPreset(ctx interface{}, presetID interface{}) *MockMutationResolver_DeleteUserSavedPreset_Call {
	return &MockMutationResolver_DeleteUserSavedPreset_Call{Call: _e.mock.On("DeleteUserSavedPreset", ctx, presetID)}
}

func (_c *MockMutationResolver_DeleteUserSavedPreset_Call) Run(run func(ctx context.Context, presetID primitive.ObjectID)) *MockMutationResolver_DeleteUserSavedPreset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_DeleteUserSavedPreset_Call) Return(b *bool, err error) *MockMutationResolver_DeleteUserSavedPreset_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_DeleteUserSavedPreset_Call) RunAndReturn(run func(ctx context.Context, presetID primitive.ObjectID) (*bool, error)) *MockMutationResolver_DeleteUserSavedPreset_Call {
	_c.Call.Return(run)
	return _c
}

// EndAbilityDuelsGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) EndAbilityDuelsGame(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for EndAbilityDuelsGame")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) (*models.Game, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) *models.Game); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_EndAbilityDuelsGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EndAbilityDuelsGame'
type MockMutationResolver_EndAbilityDuelsGame_Call struct {
	*mock.Call
}

// EndAbilityDuelsGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) EndAbilityDuelsGame(ctx interface{}, gameID interface{}) *MockMutationResolver_EndAbilityDuelsGame_Call {
	return &MockMutationResolver_EndAbilityDuelsGame_Call{Call: _e.mock.On("EndAbilityDuelsGame", ctx, gameID)}
}

func (_c *MockMutationResolver_EndAbilityDuelsGame_Call) Run(run func(ctx context.Context, gameID *primitive.ObjectID)) *MockMutationResolver_EndAbilityDuelsGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_EndAbilityDuelsGame_Call) Return(game *models.Game, err error) *MockMutationResolver_EndAbilityDuelsGame_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockMutationResolver_EndAbilityDuelsGame_Call) RunAndReturn(run func(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error)) *MockMutationResolver_EndAbilityDuelsGame_Call {
	_c.Call.Return(run)
	return _c
}

// EndGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) EndGame(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for EndGame")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) (*models.Game, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) *models.Game); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_EndGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EndGame'
type MockMutationResolver_EndGame_Call struct {
	*mock.Call
}

// EndGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) EndGame(ctx interface{}, gameID interface{}) *MockMutationResolver_EndGame_Call {
	return &MockMutationResolver_EndGame_Call{Call: _e.mock.On("EndGame", ctx, gameID)}
}

func (_c *MockMutationResolver_EndGame_Call) Run(run func(ctx context.Context, gameID *primitive.ObjectID)) *MockMutationResolver_EndGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_EndGame_Call) Return(game *models.Game, err error) *MockMutationResolver_EndGame_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockMutationResolver_EndGame_Call) RunAndReturn(run func(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error)) *MockMutationResolver_EndGame_Call {
	_c.Call.Return(run)
	return _c
}

// EndGameForShowdown provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) EndGameForShowdown(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for EndGameForShowdown")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) (*models.Game, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) *models.Game); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_EndGameForShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EndGameForShowdown'
type MockMutationResolver_EndGameForShowdown_Call struct {
	*mock.Call
}

// EndGameForShowdown is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) EndGameForShowdown(ctx interface{}, gameID interface{}) *MockMutationResolver_EndGameForShowdown_Call {
	return &MockMutationResolver_EndGameForShowdown_Call{Call: _e.mock.On("EndGameForShowdown", ctx, gameID)}
}

func (_c *MockMutationResolver_EndGameForShowdown_Call) Run(run func(ctx context.Context, gameID *primitive.ObjectID)) *MockMutationResolver_EndGameForShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_EndGameForShowdown_Call) Return(game *models.Game, err error) *MockMutationResolver_EndGameForShowdown_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockMutationResolver_EndGameForShowdown_Call) RunAndReturn(run func(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error)) *MockMutationResolver_EndGameForShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// EndPuzzleGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) EndPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for EndPuzzleGame")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_EndPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EndPuzzleGame'
type MockMutationResolver_EndPuzzleGame_Call struct {
	*mock.Call
}

// EndPuzzleGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) EndPuzzleGame(ctx interface{}, gameID interface{}) *MockMutationResolver_EndPuzzleGame_Call {
	return &MockMutationResolver_EndPuzzleGame_Call{Call: _e.mock.On("EndPuzzleGame", ctx, gameID)}
}

func (_c *MockMutationResolver_EndPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockMutationResolver_EndPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_EndPuzzleGame_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockMutationResolver_EndPuzzleGame_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockMutationResolver_EndPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)) *MockMutationResolver_EndPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// FollowUser provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) FollowUser(ctx context.Context, followUserInput *models.FollowUserInput) (bool, error) {
	ret := _mock.Called(ctx, followUserInput)

	if len(ret) == 0 {
		panic("no return value specified for FollowUser")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.FollowUserInput) (bool, error)); ok {
		return returnFunc(ctx, followUserInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.FollowUserInput) bool); ok {
		r0 = returnFunc(ctx, followUserInput)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.FollowUserInput) error); ok {
		r1 = returnFunc(ctx, followUserInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_FollowUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FollowUser'
type MockMutationResolver_FollowUser_Call struct {
	*mock.Call
}

// FollowUser is a helper method to define mock.On call
//   - ctx
//   - followUserInput
func (_e *MockMutationResolver_Expecter) FollowUser(ctx interface{}, followUserInput interface{}) *MockMutationResolver_FollowUser_Call {
	return &MockMutationResolver_FollowUser_Call{Call: _e.mock.On("FollowUser", ctx, followUserInput)}
}

func (_c *MockMutationResolver_FollowUser_Call) Run(run func(ctx context.Context, followUserInput *models.FollowUserInput)) *MockMutationResolver_FollowUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.FollowUserInput))
	})
	return _c
}

func (_c *MockMutationResolver_FollowUser_Call) Return(b bool, err error) *MockMutationResolver_FollowUser_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_FollowUser_Call) RunAndReturn(run func(ctx context.Context, followUserInput *models.FollowUserInput) (bool, error)) *MockMutationResolver_FollowUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetUpdatedUserStreaks provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) GetUpdatedUserStreaks(ctx context.Context) (*models.UserStreaks, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUpdatedUserStreaks")
	}

	var r0 *models.UserStreaks
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.UserStreaks, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.UserStreaks); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserStreaks)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_GetUpdatedUserStreaks_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUpdatedUserStreaks'
type MockMutationResolver_GetUpdatedUserStreaks_Call struct {
	*mock.Call
}

// GetUpdatedUserStreaks is a helper method to define mock.On call
//   - ctx
func (_e *MockMutationResolver_Expecter) GetUpdatedUserStreaks(ctx interface{}) *MockMutationResolver_GetUpdatedUserStreaks_Call {
	return &MockMutationResolver_GetUpdatedUserStreaks_Call{Call: _e.mock.On("GetUpdatedUserStreaks", ctx)}
}

func (_c *MockMutationResolver_GetUpdatedUserStreaks_Call) Run(run func(ctx context.Context)) *MockMutationResolver_GetUpdatedUserStreaks_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockMutationResolver_GetUpdatedUserStreaks_Call) Return(userStreaks *models.UserStreaks, err error) *MockMutationResolver_GetUpdatedUserStreaks_Call {
	_c.Call.Return(userStreaks, err)
	return _c
}

func (_c *MockMutationResolver_GetUpdatedUserStreaks_Call) RunAndReturn(run func(ctx context.Context) (*models.UserStreaks, error)) *MockMutationResolver_GetUpdatedUserStreaks_Call {
	_c.Call.Return(run)
	return _c
}

// GoogleLogin provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) GoogleLogin(ctx context.Context, authCode string, tokenType *string, expiresIn *string, guestID *primitive.ObjectID) (*models.User, error) {
	ret := _mock.Called(ctx, authCode, tokenType, expiresIn, guestID)

	if len(ret) == 0 {
		panic("no return value specified for GoogleLogin")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *string, *string, *primitive.ObjectID) (*models.User, error)); ok {
		return returnFunc(ctx, authCode, tokenType, expiresIn, guestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *string, *string, *primitive.ObjectID) *models.User); ok {
		r0 = returnFunc(ctx, authCode, tokenType, expiresIn, guestID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, *string, *string, *primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, authCode, tokenType, expiresIn, guestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_GoogleLogin_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GoogleLogin'
type MockMutationResolver_GoogleLogin_Call struct {
	*mock.Call
}

// GoogleLogin is a helper method to define mock.On call
//   - ctx
//   - authCode
//   - tokenType
//   - expiresIn
//   - guestID
func (_e *MockMutationResolver_Expecter) GoogleLogin(ctx interface{}, authCode interface{}, tokenType interface{}, expiresIn interface{}, guestID interface{}) *MockMutationResolver_GoogleLogin_Call {
	return &MockMutationResolver_GoogleLogin_Call{Call: _e.mock.On("GoogleLogin", ctx, authCode, tokenType, expiresIn, guestID)}
}

func (_c *MockMutationResolver_GoogleLogin_Call) Run(run func(ctx context.Context, authCode string, tokenType *string, expiresIn *string, guestID *primitive.ObjectID)) *MockMutationResolver_GoogleLogin_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*string), args[3].(*string), args[4].(*primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_GoogleLogin_Call) Return(user *models.User, err error) *MockMutationResolver_GoogleLogin_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockMutationResolver_GoogleLogin_Call) RunAndReturn(run func(ctx context.Context, authCode string, tokenType *string, expiresIn *string, guestID *primitive.ObjectID) (*models.User, error)) *MockMutationResolver_GoogleLogin_Call {
	_c.Call.Return(run)
	return _c
}

// JoinClub provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) JoinClub(ctx context.Context, clubID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, clubID)

	if len(ret) == 0 {
		panic("no return value specified for JoinClub")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, clubID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, clubID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, clubID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_JoinClub_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'JoinClub'
type MockMutationResolver_JoinClub_Call struct {
	*mock.Call
}

// JoinClub is a helper method to define mock.On call
//   - ctx
//   - clubID
func (_e *MockMutationResolver_Expecter) JoinClub(ctx interface{}, clubID interface{}) *MockMutationResolver_JoinClub_Call {
	return &MockMutationResolver_JoinClub_Call{Call: _e.mock.On("JoinClub", ctx, clubID)}
}

func (_c *MockMutationResolver_JoinClub_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID)) *MockMutationResolver_JoinClub_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_JoinClub_Call) Return(b bool, err error) *MockMutationResolver_JoinClub_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_JoinClub_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID) (bool, error)) *MockMutationResolver_JoinClub_Call {
	_c.Call.Return(run)
	return _c
}

// JoinClubEvent provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) JoinClubEvent(ctx context.Context, eventID primitive.ObjectID) (*models.ClubEventParticipant, error) {
	ret := _mock.Called(ctx, eventID)

	if len(ret) == 0 {
		panic("no return value specified for JoinClubEvent")
	}

	var r0 *models.ClubEventParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.ClubEventParticipant, error)); ok {
		return returnFunc(ctx, eventID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.ClubEventParticipant); ok {
		r0 = returnFunc(ctx, eventID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubEventParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, eventID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_JoinClubEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'JoinClubEvent'
type MockMutationResolver_JoinClubEvent_Call struct {
	*mock.Call
}

// JoinClubEvent is a helper method to define mock.On call
//   - ctx
//   - eventID
func (_e *MockMutationResolver_Expecter) JoinClubEvent(ctx interface{}, eventID interface{}) *MockMutationResolver_JoinClubEvent_Call {
	return &MockMutationResolver_JoinClubEvent_Call{Call: _e.mock.On("JoinClubEvent", ctx, eventID)}
}

func (_c *MockMutationResolver_JoinClubEvent_Call) Run(run func(ctx context.Context, eventID primitive.ObjectID)) *MockMutationResolver_JoinClubEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_JoinClubEvent_Call) Return(clubEventParticipant *models.ClubEventParticipant, err error) *MockMutationResolver_JoinClubEvent_Call {
	_c.Call.Return(clubEventParticipant, err)
	return _c
}

func (_c *MockMutationResolver_JoinClubEvent_Call) RunAndReturn(run func(ctx context.Context, eventID primitive.ObjectID) (*models.ClubEventParticipant, error)) *MockMutationResolver_JoinClubEvent_Call {
	_c.Call.Return(run)
	return _c
}

// JoinGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) JoinGame(ctx context.Context, joinGameInput *models.JoinGameInput) (*models.Game, error) {
	ret := _mock.Called(ctx, joinGameInput)

	if len(ret) == 0 {
		panic("no return value specified for JoinGame")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.JoinGameInput) (*models.Game, error)); ok {
		return returnFunc(ctx, joinGameInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.JoinGameInput) *models.Game); ok {
		r0 = returnFunc(ctx, joinGameInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.JoinGameInput) error); ok {
		r1 = returnFunc(ctx, joinGameInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_JoinGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'JoinGame'
type MockMutationResolver_JoinGame_Call struct {
	*mock.Call
}

// JoinGame is a helper method to define mock.On call
//   - ctx
//   - joinGameInput
func (_e *MockMutationResolver_Expecter) JoinGame(ctx interface{}, joinGameInput interface{}) *MockMutationResolver_JoinGame_Call {
	return &MockMutationResolver_JoinGame_Call{Call: _e.mock.On("JoinGame", ctx, joinGameInput)}
}

func (_c *MockMutationResolver_JoinGame_Call) Run(run func(ctx context.Context, joinGameInput *models.JoinGameInput)) *MockMutationResolver_JoinGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.JoinGameInput))
	})
	return _c
}

func (_c *MockMutationResolver_JoinGame_Call) Return(game *models.Game, err error) *MockMutationResolver_JoinGame_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockMutationResolver_JoinGame_Call) RunAndReturn(run func(ctx context.Context, joinGameInput *models.JoinGameInput) (*models.Game, error)) *MockMutationResolver_JoinGame_Call {
	_c.Call.Return(run)
	return _c
}

// JoinLeague provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) JoinLeague(ctx context.Context, joinLeagueInput *models.JoinLeagueInput) (bool, error) {
	ret := _mock.Called(ctx, joinLeagueInput)

	if len(ret) == 0 {
		panic("no return value specified for JoinLeague")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.JoinLeagueInput) (bool, error)); ok {
		return returnFunc(ctx, joinLeagueInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.JoinLeagueInput) bool); ok {
		r0 = returnFunc(ctx, joinLeagueInput)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.JoinLeagueInput) error); ok {
		r1 = returnFunc(ctx, joinLeagueInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_JoinLeague_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'JoinLeague'
type MockMutationResolver_JoinLeague_Call struct {
	*mock.Call
}

// JoinLeague is a helper method to define mock.On call
//   - ctx
//   - joinLeagueInput
func (_e *MockMutationResolver_Expecter) JoinLeague(ctx interface{}, joinLeagueInput interface{}) *MockMutationResolver_JoinLeague_Call {
	return &MockMutationResolver_JoinLeague_Call{Call: _e.mock.On("JoinLeague", ctx, joinLeagueInput)}
}

func (_c *MockMutationResolver_JoinLeague_Call) Run(run func(ctx context.Context, joinLeagueInput *models.JoinLeagueInput)) *MockMutationResolver_JoinLeague_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.JoinLeagueInput))
	})
	return _c
}

func (_c *MockMutationResolver_JoinLeague_Call) Return(b bool, err error) *MockMutationResolver_JoinLeague_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_JoinLeague_Call) RunAndReturn(run func(ctx context.Context, joinLeagueInput *models.JoinLeagueInput) (bool, error)) *MockMutationResolver_JoinLeague_Call {
	_c.Call.Return(run)
	return _c
}

// JoinPuzzleGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) JoinPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for JoinPuzzleGame")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_JoinPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'JoinPuzzleGame'
type MockMutationResolver_JoinPuzzleGame_Call struct {
	*mock.Call
}

// JoinPuzzleGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) JoinPuzzleGame(ctx interface{}, gameID interface{}) *MockMutationResolver_JoinPuzzleGame_Call {
	return &MockMutationResolver_JoinPuzzleGame_Call{Call: _e.mock.On("JoinPuzzleGame", ctx, gameID)}
}

func (_c *MockMutationResolver_JoinPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockMutationResolver_JoinPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_JoinPuzzleGame_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockMutationResolver_JoinPuzzleGame_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockMutationResolver_JoinPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)) *MockMutationResolver_JoinPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// JoinVirtualContest provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) JoinVirtualContest(ctx context.Context, contestID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, contestID)

	if len(ret) == 0 {
		panic("no return value specified for JoinVirtualContest")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, contestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, contestID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, contestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_JoinVirtualContest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'JoinVirtualContest'
type MockMutationResolver_JoinVirtualContest_Call struct {
	*mock.Call
}

// JoinVirtualContest is a helper method to define mock.On call
//   - ctx
//   - contestID
func (_e *MockMutationResolver_Expecter) JoinVirtualContest(ctx interface{}, contestID interface{}) *MockMutationResolver_JoinVirtualContest_Call {
	return &MockMutationResolver_JoinVirtualContest_Call{Call: _e.mock.On("JoinVirtualContest", ctx, contestID)}
}

func (_c *MockMutationResolver_JoinVirtualContest_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID)) *MockMutationResolver_JoinVirtualContest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_JoinVirtualContest_Call) Return(b bool, err error) *MockMutationResolver_JoinVirtualContest_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_JoinVirtualContest_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID) (bool, error)) *MockMutationResolver_JoinVirtualContest_Call {
	_c.Call.Return(run)
	return _c
}

// LeaveClub provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) LeaveClub(ctx context.Context, clubID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, clubID)

	if len(ret) == 0 {
		panic("no return value specified for LeaveClub")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, clubID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, clubID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, clubID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_LeaveClub_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LeaveClub'
type MockMutationResolver_LeaveClub_Call struct {
	*mock.Call
}

// LeaveClub is a helper method to define mock.On call
//   - ctx
//   - clubID
func (_e *MockMutationResolver_Expecter) LeaveClub(ctx interface{}, clubID interface{}) *MockMutationResolver_LeaveClub_Call {
	return &MockMutationResolver_LeaveClub_Call{Call: _e.mock.On("LeaveClub", ctx, clubID)}
}

func (_c *MockMutationResolver_LeaveClub_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID)) *MockMutationResolver_LeaveClub_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_LeaveClub_Call) Return(b bool, err error) *MockMutationResolver_LeaveClub_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_LeaveClub_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID) (bool, error)) *MockMutationResolver_LeaveClub_Call {
	_c.Call.Return(run)
	return _c
}

// LeaveClubEvent provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) LeaveClubEvent(ctx context.Context, eventID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, eventID)

	if len(ret) == 0 {
		panic("no return value specified for LeaveClubEvent")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, eventID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, eventID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, eventID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_LeaveClubEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LeaveClubEvent'
type MockMutationResolver_LeaveClubEvent_Call struct {
	*mock.Call
}

// LeaveClubEvent is a helper method to define mock.On call
//   - ctx
//   - eventID
func (_e *MockMutationResolver_Expecter) LeaveClubEvent(ctx interface{}, eventID interface{}) *MockMutationResolver_LeaveClubEvent_Call {
	return &MockMutationResolver_LeaveClubEvent_Call{Call: _e.mock.On("LeaveClubEvent", ctx, eventID)}
}

func (_c *MockMutationResolver_LeaveClubEvent_Call) Run(run func(ctx context.Context, eventID primitive.ObjectID)) *MockMutationResolver_LeaveClubEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_LeaveClubEvent_Call) Return(b bool, err error) *MockMutationResolver_LeaveClubEvent_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_LeaveClubEvent_Call) RunAndReturn(run func(ctx context.Context, eventID primitive.ObjectID) (bool, error)) *MockMutationResolver_LeaveClubEvent_Call {
	_c.Call.Return(run)
	return _c
}

// LeaveGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) LeaveGame(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for LeaveGame")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Game, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Game); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_LeaveGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LeaveGame'
type MockMutationResolver_LeaveGame_Call struct {
	*mock.Call
}

// LeaveGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) LeaveGame(ctx interface{}, gameID interface{}) *MockMutationResolver_LeaveGame_Call {
	return &MockMutationResolver_LeaveGame_Call{Call: _e.mock.On("LeaveGame", ctx, gameID)}
}

func (_c *MockMutationResolver_LeaveGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockMutationResolver_LeaveGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_LeaveGame_Call) Return(game *models.Game, err error) *MockMutationResolver_LeaveGame_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockMutationResolver_LeaveGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error)) *MockMutationResolver_LeaveGame_Call {
	_c.Call.Return(run)
	return _c
}

// LeavePuzzleGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) LeavePuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for LeavePuzzleGame")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_LeavePuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LeavePuzzleGame'
type MockMutationResolver_LeavePuzzleGame_Call struct {
	*mock.Call
}

// LeavePuzzleGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) LeavePuzzleGame(ctx interface{}, gameID interface{}) *MockMutationResolver_LeavePuzzleGame_Call {
	return &MockMutationResolver_LeavePuzzleGame_Call{Call: _e.mock.On("LeavePuzzleGame", ctx, gameID)}
}

func (_c *MockMutationResolver_LeavePuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockMutationResolver_LeavePuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_LeavePuzzleGame_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockMutationResolver_LeavePuzzleGame_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockMutationResolver_LeavePuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)) *MockMutationResolver_LeavePuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// LegacyGoogleLogin provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) LegacyGoogleLogin(ctx context.Context, idToken string, guestID *primitive.ObjectID) (*models.User, error) {
	ret := _mock.Called(ctx, idToken, guestID)

	if len(ret) == 0 {
		panic("no return value specified for LegacyGoogleLogin")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *primitive.ObjectID) (*models.User, error)); ok {
		return returnFunc(ctx, idToken, guestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *primitive.ObjectID) *models.User); ok {
		r0 = returnFunc(ctx, idToken, guestID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, *primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, idToken, guestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_LegacyGoogleLogin_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LegacyGoogleLogin'
type MockMutationResolver_LegacyGoogleLogin_Call struct {
	*mock.Call
}

// LegacyGoogleLogin is a helper method to define mock.On call
//   - ctx
//   - idToken
//   - guestID
func (_e *MockMutationResolver_Expecter) LegacyGoogleLogin(ctx interface{}, idToken interface{}, guestID interface{}) *MockMutationResolver_LegacyGoogleLogin_Call {
	return &MockMutationResolver_LegacyGoogleLogin_Call{Call: _e.mock.On("LegacyGoogleLogin", ctx, idToken, guestID)}
}

func (_c *MockMutationResolver_LegacyGoogleLogin_Call) Run(run func(ctx context.Context, idToken string, guestID *primitive.ObjectID)) *MockMutationResolver_LegacyGoogleLogin_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_LegacyGoogleLogin_Call) Return(user *models.User, err error) *MockMutationResolver_LegacyGoogleLogin_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockMutationResolver_LegacyGoogleLogin_Call) RunAndReturn(run func(ctx context.Context, idToken string, guestID *primitive.ObjectID) (*models.User, error)) *MockMutationResolver_LegacyGoogleLogin_Call {
	_c.Call.Return(run)
	return _c
}

// LoginAsGuest provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) LoginAsGuest(ctx context.Context, guestID primitive.ObjectID) (*models.User, error) {
	ret := _mock.Called(ctx, guestID)

	if len(ret) == 0 {
		panic("no return value specified for LoginAsGuest")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.User, error)); ok {
		return returnFunc(ctx, guestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.User); ok {
		r0 = returnFunc(ctx, guestID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, guestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_LoginAsGuest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LoginAsGuest'
type MockMutationResolver_LoginAsGuest_Call struct {
	*mock.Call
}

// LoginAsGuest is a helper method to define mock.On call
//   - ctx
//   - guestID
func (_e *MockMutationResolver_Expecter) LoginAsGuest(ctx interface{}, guestID interface{}) *MockMutationResolver_LoginAsGuest_Call {
	return &MockMutationResolver_LoginAsGuest_Call{Call: _e.mock.On("LoginAsGuest", ctx, guestID)}
}

func (_c *MockMutationResolver_LoginAsGuest_Call) Run(run func(ctx context.Context, guestID primitive.ObjectID)) *MockMutationResolver_LoginAsGuest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_LoginAsGuest_Call) Return(user *models.User, err error) *MockMutationResolver_LoginAsGuest_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockMutationResolver_LoginAsGuest_Call) RunAndReturn(run func(ctx context.Context, guestID primitive.ObjectID) (*models.User, error)) *MockMutationResolver_LoginAsGuest_Call {
	_c.Call.Return(run)
	return _c
}

// MarkAllAnnouncementsAsRead provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) MarkAllAnnouncementsAsRead(ctx context.Context) (*models.AnnouncementMutationResponse, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for MarkAllAnnouncementsAsRead")
	}

	var r0 *models.AnnouncementMutationResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.AnnouncementMutationResponse, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.AnnouncementMutationResponse); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.AnnouncementMutationResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_MarkAllAnnouncementsAsRead_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MarkAllAnnouncementsAsRead'
type MockMutationResolver_MarkAllAnnouncementsAsRead_Call struct {
	*mock.Call
}

// MarkAllAnnouncementsAsRead is a helper method to define mock.On call
//   - ctx
func (_e *MockMutationResolver_Expecter) MarkAllAnnouncementsAsRead(ctx interface{}) *MockMutationResolver_MarkAllAnnouncementsAsRead_Call {
	return &MockMutationResolver_MarkAllAnnouncementsAsRead_Call{Call: _e.mock.On("MarkAllAnnouncementsAsRead", ctx)}
}

func (_c *MockMutationResolver_MarkAllAnnouncementsAsRead_Call) Run(run func(ctx context.Context)) *MockMutationResolver_MarkAllAnnouncementsAsRead_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockMutationResolver_MarkAllAnnouncementsAsRead_Call) Return(announcementMutationResponse *models.AnnouncementMutationResponse, err error) *MockMutationResolver_MarkAllAnnouncementsAsRead_Call {
	_c.Call.Return(announcementMutationResponse, err)
	return _c
}

func (_c *MockMutationResolver_MarkAllAnnouncementsAsRead_Call) RunAndReturn(run func(ctx context.Context) (*models.AnnouncementMutationResponse, error)) *MockMutationResolver_MarkAllAnnouncementsAsRead_Call {
	_c.Call.Return(run)
	return _c
}

// MarkAnnouncementAsRead provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) MarkAnnouncementAsRead(ctx context.Context, announcementID primitive.ObjectID) (*models.AnnouncementMutationResponse, error) {
	ret := _mock.Called(ctx, announcementID)

	if len(ret) == 0 {
		panic("no return value specified for MarkAnnouncementAsRead")
	}

	var r0 *models.AnnouncementMutationResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.AnnouncementMutationResponse, error)); ok {
		return returnFunc(ctx, announcementID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.AnnouncementMutationResponse); ok {
		r0 = returnFunc(ctx, announcementID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.AnnouncementMutationResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, announcementID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_MarkAnnouncementAsRead_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MarkAnnouncementAsRead'
type MockMutationResolver_MarkAnnouncementAsRead_Call struct {
	*mock.Call
}

// MarkAnnouncementAsRead is a helper method to define mock.On call
//   - ctx
//   - announcementID
func (_e *MockMutationResolver_Expecter) MarkAnnouncementAsRead(ctx interface{}, announcementID interface{}) *MockMutationResolver_MarkAnnouncementAsRead_Call {
	return &MockMutationResolver_MarkAnnouncementAsRead_Call{Call: _e.mock.On("MarkAnnouncementAsRead", ctx, announcementID)}
}

func (_c *MockMutationResolver_MarkAnnouncementAsRead_Call) Run(run func(ctx context.Context, announcementID primitive.ObjectID)) *MockMutationResolver_MarkAnnouncementAsRead_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_MarkAnnouncementAsRead_Call) Return(announcementMutationResponse *models.AnnouncementMutationResponse, err error) *MockMutationResolver_MarkAnnouncementAsRead_Call {
	_c.Call.Return(announcementMutationResponse, err)
	return _c
}

func (_c *MockMutationResolver_MarkAnnouncementAsRead_Call) RunAndReturn(run func(ctx context.Context, announcementID primitive.ObjectID) (*models.AnnouncementMutationResponse, error)) *MockMutationResolver_MarkAnnouncementAsRead_Call {
	_c.Call.Return(run)
	return _c
}

// RegisterDeviceToken provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) RegisterDeviceToken(ctx context.Context, pushNotificationToken string, deviceID *string, platform *string) (*models.DeviceTokenRegistrationResponse, error) {
	ret := _mock.Called(ctx, pushNotificationToken, deviceID, platform)

	if len(ret) == 0 {
		panic("no return value specified for RegisterDeviceToken")
	}

	var r0 *models.DeviceTokenRegistrationResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *string, *string) (*models.DeviceTokenRegistrationResponse, error)); ok {
		return returnFunc(ctx, pushNotificationToken, deviceID, platform)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *string, *string) *models.DeviceTokenRegistrationResponse); ok {
		r0 = returnFunc(ctx, pushNotificationToken, deviceID, platform)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.DeviceTokenRegistrationResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, *string, *string) error); ok {
		r1 = returnFunc(ctx, pushNotificationToken, deviceID, platform)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_RegisterDeviceToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RegisterDeviceToken'
type MockMutationResolver_RegisterDeviceToken_Call struct {
	*mock.Call
}

// RegisterDeviceToken is a helper method to define mock.On call
//   - ctx
//   - pushNotificationToken
//   - deviceID
//   - platform
func (_e *MockMutationResolver_Expecter) RegisterDeviceToken(ctx interface{}, pushNotificationToken interface{}, deviceID interface{}, platform interface{}) *MockMutationResolver_RegisterDeviceToken_Call {
	return &MockMutationResolver_RegisterDeviceToken_Call{Call: _e.mock.On("RegisterDeviceToken", ctx, pushNotificationToken, deviceID, platform)}
}

func (_c *MockMutationResolver_RegisterDeviceToken_Call) Run(run func(ctx context.Context, pushNotificationToken string, deviceID *string, platform *string)) *MockMutationResolver_RegisterDeviceToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*string), args[3].(*string))
	})
	return _c
}

func (_c *MockMutationResolver_RegisterDeviceToken_Call) Return(deviceTokenRegistrationResponse *models.DeviceTokenRegistrationResponse, err error) *MockMutationResolver_RegisterDeviceToken_Call {
	_c.Call.Return(deviceTokenRegistrationResponse, err)
	return _c
}

func (_c *MockMutationResolver_RegisterDeviceToken_Call) RunAndReturn(run func(ctx context.Context, pushNotificationToken string, deviceID *string, platform *string) (*models.DeviceTokenRegistrationResponse, error)) *MockMutationResolver_RegisterDeviceToken_Call {
	_c.Call.Return(run)
	return _c
}

// RegisterForContest provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) RegisterForContest(ctx context.Context, input models.RegistrationFormValuesInput) (bool, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for RegisterForContest")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.RegistrationFormValuesInput) (bool, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.RegistrationFormValuesInput) bool); ok {
		r0 = returnFunc(ctx, input)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.RegistrationFormValuesInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_RegisterForContest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RegisterForContest'
type MockMutationResolver_RegisterForContest_Call struct {
	*mock.Call
}

// RegisterForContest is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) RegisterForContest(ctx interface{}, input interface{}) *MockMutationResolver_RegisterForContest_Call {
	return &MockMutationResolver_RegisterForContest_Call{Call: _e.mock.On("RegisterForContest", ctx, input)}
}

func (_c *MockMutationResolver_RegisterForContest_Call) Run(run func(ctx context.Context, input models.RegistrationFormValuesInput)) *MockMutationResolver_RegisterForContest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.RegistrationFormValuesInput))
	})
	return _c
}

func (_c *MockMutationResolver_RegisterForContest_Call) Return(b bool, err error) *MockMutationResolver_RegisterForContest_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_RegisterForContest_Call) RunAndReturn(run func(ctx context.Context, input models.RegistrationFormValuesInput) (bool, error)) *MockMutationResolver_RegisterForContest_Call {
	_c.Call.Return(run)
	return _c
}

// RegisterForShowdown provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) RegisterForShowdown(ctx context.Context, input models.ShowdownRegistrationFormValuesInput) (bool, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for RegisterForShowdown")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.ShowdownRegistrationFormValuesInput) (bool, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.ShowdownRegistrationFormValuesInput) bool); ok {
		r0 = returnFunc(ctx, input)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.ShowdownRegistrationFormValuesInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_RegisterForShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RegisterForShowdown'
type MockMutationResolver_RegisterForShowdown_Call struct {
	*mock.Call
}

// RegisterForShowdown is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) RegisterForShowdown(ctx interface{}, input interface{}) *MockMutationResolver_RegisterForShowdown_Call {
	return &MockMutationResolver_RegisterForShowdown_Call{Call: _e.mock.On("RegisterForShowdown", ctx, input)}
}

func (_c *MockMutationResolver_RegisterForShowdown_Call) Run(run func(ctx context.Context, input models.ShowdownRegistrationFormValuesInput)) *MockMutationResolver_RegisterForShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.ShowdownRegistrationFormValuesInput))
	})
	return _c
}

func (_c *MockMutationResolver_RegisterForShowdown_Call) Return(b bool, err error) *MockMutationResolver_RegisterForShowdown_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_RegisterForShowdown_Call) RunAndReturn(run func(ctx context.Context, input models.ShowdownRegistrationFormValuesInput) (bool, error)) *MockMutationResolver_RegisterForShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// RejectChallenge provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) RejectChallenge(ctx context.Context, gameID primitive.ObjectID) (*bool, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for RejectChallenge")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*bool, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *bool); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_RejectChallenge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RejectChallenge'
type MockMutationResolver_RejectChallenge_Call struct {
	*mock.Call
}

// RejectChallenge is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) RejectChallenge(ctx interface{}, gameID interface{}) *MockMutationResolver_RejectChallenge_Call {
	return &MockMutationResolver_RejectChallenge_Call{Call: _e.mock.On("RejectChallenge", ctx, gameID)}
}

func (_c *MockMutationResolver_RejectChallenge_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockMutationResolver_RejectChallenge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_RejectChallenge_Call) Return(b *bool, err error) *MockMutationResolver_RejectChallenge_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_RejectChallenge_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*bool, error)) *MockMutationResolver_RejectChallenge_Call {
	_c.Call.Return(run)
	return _c
}

// RejectChallengeOfPuzzleGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) RejectChallengeOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*bool, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for RejectChallengeOfPuzzleGame")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*bool, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *bool); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_RejectChallengeOfPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RejectChallengeOfPuzzleGame'
type MockMutationResolver_RejectChallengeOfPuzzleGame_Call struct {
	*mock.Call
}

// RejectChallengeOfPuzzleGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) RejectChallengeOfPuzzleGame(ctx interface{}, gameID interface{}) *MockMutationResolver_RejectChallengeOfPuzzleGame_Call {
	return &MockMutationResolver_RejectChallengeOfPuzzleGame_Call{Call: _e.mock.On("RejectChallengeOfPuzzleGame", ctx, gameID)}
}

func (_c *MockMutationResolver_RejectChallengeOfPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockMutationResolver_RejectChallengeOfPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_RejectChallengeOfPuzzleGame_Call) Return(b *bool, err error) *MockMutationResolver_RejectChallengeOfPuzzleGame_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_RejectChallengeOfPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*bool, error)) *MockMutationResolver_RejectChallengeOfPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// RejectFriendRequest provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) RejectFriendRequest(ctx context.Context, rejectRequestInput *models.FriendRequestInput) (bool, error) {
	ret := _mock.Called(ctx, rejectRequestInput)

	if len(ret) == 0 {
		panic("no return value specified for RejectFriendRequest")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.FriendRequestInput) (bool, error)); ok {
		return returnFunc(ctx, rejectRequestInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.FriendRequestInput) bool); ok {
		r0 = returnFunc(ctx, rejectRequestInput)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.FriendRequestInput) error); ok {
		r1 = returnFunc(ctx, rejectRequestInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_RejectFriendRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RejectFriendRequest'
type MockMutationResolver_RejectFriendRequest_Call struct {
	*mock.Call
}

// RejectFriendRequest is a helper method to define mock.On call
//   - ctx
//   - rejectRequestInput
func (_e *MockMutationResolver_Expecter) RejectFriendRequest(ctx interface{}, rejectRequestInput interface{}) *MockMutationResolver_RejectFriendRequest_Call {
	return &MockMutationResolver_RejectFriendRequest_Call{Call: _e.mock.On("RejectFriendRequest", ctx, rejectRequestInput)}
}

func (_c *MockMutationResolver_RejectFriendRequest_Call) Run(run func(ctx context.Context, rejectRequestInput *models.FriendRequestInput)) *MockMutationResolver_RejectFriendRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.FriendRequestInput))
	})
	return _c
}

func (_c *MockMutationResolver_RejectFriendRequest_Call) Return(b bool, err error) *MockMutationResolver_RejectFriendRequest_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_RejectFriendRequest_Call) RunAndReturn(run func(ctx context.Context, rejectRequestInput *models.FriendRequestInput) (bool, error)) *MockMutationResolver_RejectFriendRequest_Call {
	_c.Call.Return(run)
	return _c
}

// RejectRematch provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) RejectRematch(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for RejectRematch")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_RejectRematch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RejectRematch'
type MockMutationResolver_RejectRematch_Call struct {
	*mock.Call
}

// RejectRematch is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) RejectRematch(ctx interface{}, gameID interface{}) *MockMutationResolver_RejectRematch_Call {
	return &MockMutationResolver_RejectRematch_Call{Call: _e.mock.On("RejectRematch", ctx, gameID)}
}

func (_c *MockMutationResolver_RejectRematch_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockMutationResolver_RejectRematch_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_RejectRematch_Call) Return(b bool, err error) *MockMutationResolver_RejectRematch_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_RejectRematch_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (bool, error)) *MockMutationResolver_RejectRematch_Call {
	_c.Call.Return(run)
	return _c
}

// RejectRematchOfPuzzleGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) RejectRematchOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for RejectRematchOfPuzzleGame")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_RejectRematchOfPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RejectRematchOfPuzzleGame'
type MockMutationResolver_RejectRematchOfPuzzleGame_Call struct {
	*mock.Call
}

// RejectRematchOfPuzzleGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) RejectRematchOfPuzzleGame(ctx interface{}, gameID interface{}) *MockMutationResolver_RejectRematchOfPuzzleGame_Call {
	return &MockMutationResolver_RejectRematchOfPuzzleGame_Call{Call: _e.mock.On("RejectRematchOfPuzzleGame", ctx, gameID)}
}

func (_c *MockMutationResolver_RejectRematchOfPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockMutationResolver_RejectRematchOfPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_RejectRematchOfPuzzleGame_Call) Return(b bool, err error) *MockMutationResolver_RejectRematchOfPuzzleGame_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_RejectRematchOfPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (bool, error)) *MockMutationResolver_RejectRematchOfPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveClubMember provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) RemoveClubMember(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, clubID, userID)

	if len(ret) == 0 {
		panic("no return value specified for RemoveClubMember")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, clubID, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, clubID, userID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, clubID, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_RemoveClubMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveClubMember'
type MockMutationResolver_RemoveClubMember_Call struct {
	*mock.Call
}

// RemoveClubMember is a helper method to define mock.On call
//   - ctx
//   - clubID
//   - userID
func (_e *MockMutationResolver_Expecter) RemoveClubMember(ctx interface{}, clubID interface{}, userID interface{}) *MockMutationResolver_RemoveClubMember_Call {
	return &MockMutationResolver_RemoveClubMember_Call{Call: _e.mock.On("RemoveClubMember", ctx, clubID, userID)}
}

func (_c *MockMutationResolver_RemoveClubMember_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID)) *MockMutationResolver_RemoveClubMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_RemoveClubMember_Call) Return(b bool, err error) *MockMutationResolver_RemoveClubMember_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_RemoveClubMember_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID) (bool, error)) *MockMutationResolver_RemoveClubMember_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveFollower provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) RemoveFollower(ctx context.Context, removeFollowerInput *models.RemoveFollowerInput) (bool, error) {
	ret := _mock.Called(ctx, removeFollowerInput)

	if len(ret) == 0 {
		panic("no return value specified for RemoveFollower")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.RemoveFollowerInput) (bool, error)); ok {
		return returnFunc(ctx, removeFollowerInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.RemoveFollowerInput) bool); ok {
		r0 = returnFunc(ctx, removeFollowerInput)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.RemoveFollowerInput) error); ok {
		r1 = returnFunc(ctx, removeFollowerInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_RemoveFollower_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveFollower'
type MockMutationResolver_RemoveFollower_Call struct {
	*mock.Call
}

// RemoveFollower is a helper method to define mock.On call
//   - ctx
//   - removeFollowerInput
func (_e *MockMutationResolver_Expecter) RemoveFollower(ctx interface{}, removeFollowerInput interface{}) *MockMutationResolver_RemoveFollower_Call {
	return &MockMutationResolver_RemoveFollower_Call{Call: _e.mock.On("RemoveFollower", ctx, removeFollowerInput)}
}

func (_c *MockMutationResolver_RemoveFollower_Call) Run(run func(ctx context.Context, removeFollowerInput *models.RemoveFollowerInput)) *MockMutationResolver_RemoveFollower_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.RemoveFollowerInput))
	})
	return _c
}

func (_c *MockMutationResolver_RemoveFollower_Call) Return(b bool, err error) *MockMutationResolver_RemoveFollower_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_RemoveFollower_Call) RunAndReturn(run func(ctx context.Context, removeFollowerInput *models.RemoveFollowerInput) (bool, error)) *MockMutationResolver_RemoveFollower_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveFriend provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) RemoveFriend(ctx context.Context, removeFriendInput *models.RemoveFriendInput) (bool, error) {
	ret := _mock.Called(ctx, removeFriendInput)

	if len(ret) == 0 {
		panic("no return value specified for RemoveFriend")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.RemoveFriendInput) (bool, error)); ok {
		return returnFunc(ctx, removeFriendInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.RemoveFriendInput) bool); ok {
		r0 = returnFunc(ctx, removeFriendInput)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.RemoveFriendInput) error); ok {
		r1 = returnFunc(ctx, removeFriendInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_RemoveFriend_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveFriend'
type MockMutationResolver_RemoveFriend_Call struct {
	*mock.Call
}

// RemoveFriend is a helper method to define mock.On call
//   - ctx
//   - removeFriendInput
func (_e *MockMutationResolver_Expecter) RemoveFriend(ctx interface{}, removeFriendInput interface{}) *MockMutationResolver_RemoveFriend_Call {
	return &MockMutationResolver_RemoveFriend_Call{Call: _e.mock.On("RemoveFriend", ctx, removeFriendInput)}
}

func (_c *MockMutationResolver_RemoveFriend_Call) Run(run func(ctx context.Context, removeFriendInput *models.RemoveFriendInput)) *MockMutationResolver_RemoveFriend_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.RemoveFriendInput))
	})
	return _c
}

func (_c *MockMutationResolver_RemoveFriend_Call) Return(b bool, err error) *MockMutationResolver_RemoveFriend_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_RemoveFriend_Call) RunAndReturn(run func(ctx context.Context, removeFriendInput *models.RemoveFriendInput) (bool, error)) *MockMutationResolver_RemoveFriend_Call {
	_c.Call.Return(run)
	return _c
}

// RemovePlayer provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) RemovePlayer(ctx context.Context, gameID primitive.ObjectID, playerID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, gameID, playerID)

	if len(ret) == 0 {
		panic("no return value specified for RemovePlayer")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, gameID, playerID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, gameID, playerID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID, playerID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_RemovePlayer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemovePlayer'
type MockMutationResolver_RemovePlayer_Call struct {
	*mock.Call
}

// RemovePlayer is a helper method to define mock.On call
//   - ctx
//   - gameID
//   - playerID
func (_e *MockMutationResolver_Expecter) RemovePlayer(ctx interface{}, gameID interface{}, playerID interface{}) *MockMutationResolver_RemovePlayer_Call {
	return &MockMutationResolver_RemovePlayer_Call{Call: _e.mock.On("RemovePlayer", ctx, gameID, playerID)}
}

func (_c *MockMutationResolver_RemovePlayer_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID, playerID primitive.ObjectID)) *MockMutationResolver_RemovePlayer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_RemovePlayer_Call) Return(b bool, err error) *MockMutationResolver_RemovePlayer_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_RemovePlayer_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID, playerID primitive.ObjectID) (bool, error)) *MockMutationResolver_RemovePlayer_Call {
	_c.Call.Return(run)
	return _c
}

// RemovePlayerFromPuzzleGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) RemovePlayerFromPuzzleGame(ctx context.Context, gameID primitive.ObjectID, playerID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, gameID, playerID)

	if len(ret) == 0 {
		panic("no return value specified for RemovePlayerFromPuzzleGame")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, gameID, playerID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, gameID, playerID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID, playerID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_RemovePlayerFromPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemovePlayerFromPuzzleGame'
type MockMutationResolver_RemovePlayerFromPuzzleGame_Call struct {
	*mock.Call
}

// RemovePlayerFromPuzzleGame is a helper method to define mock.On call
//   - ctx
//   - gameID
//   - playerID
func (_e *MockMutationResolver_Expecter) RemovePlayerFromPuzzleGame(ctx interface{}, gameID interface{}, playerID interface{}) *MockMutationResolver_RemovePlayerFromPuzzleGame_Call {
	return &MockMutationResolver_RemovePlayerFromPuzzleGame_Call{Call: _e.mock.On("RemovePlayerFromPuzzleGame", ctx, gameID, playerID)}
}

func (_c *MockMutationResolver_RemovePlayerFromPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID, playerID primitive.ObjectID)) *MockMutationResolver_RemovePlayerFromPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_RemovePlayerFromPuzzleGame_Call) Return(b bool, err error) *MockMutationResolver_RemovePlayerFromPuzzleGame_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_RemovePlayerFromPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID, playerID primitive.ObjectID) (bool, error)) *MockMutationResolver_RemovePlayerFromPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// RequestRematch provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) RequestRematch(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for RequestRematch")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_RequestRematch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RequestRematch'
type MockMutationResolver_RequestRematch_Call struct {
	*mock.Call
}

// RequestRematch is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) RequestRematch(ctx interface{}, gameID interface{}) *MockMutationResolver_RequestRematch_Call {
	return &MockMutationResolver_RequestRematch_Call{Call: _e.mock.On("RequestRematch", ctx, gameID)}
}

func (_c *MockMutationResolver_RequestRematch_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockMutationResolver_RequestRematch_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_RequestRematch_Call) Return(b bool, err error) *MockMutationResolver_RequestRematch_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_RequestRematch_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (bool, error)) *MockMutationResolver_RequestRematch_Call {
	_c.Call.Return(run)
	return _c
}

// RequestRematchForPuzzleGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) RequestRematchForPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for RequestRematchForPuzzleGame")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_RequestRematchForPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RequestRematchForPuzzleGame'
type MockMutationResolver_RequestRematchForPuzzleGame_Call struct {
	*mock.Call
}

// RequestRematchForPuzzleGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) RequestRematchForPuzzleGame(ctx interface{}, gameID interface{}) *MockMutationResolver_RequestRematchForPuzzleGame_Call {
	return &MockMutationResolver_RequestRematchForPuzzleGame_Call{Call: _e.mock.On("RequestRematchForPuzzleGame", ctx, gameID)}
}

func (_c *MockMutationResolver_RequestRematchForPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockMutationResolver_RequestRematchForPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_RequestRematchForPuzzleGame_Call) Return(b bool, err error) *MockMutationResolver_RequestRematchForPuzzleGame_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_RequestRematchForPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (bool, error)) *MockMutationResolver_RequestRematchForPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// SaveUserPreset provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) SaveUserPreset(ctx context.Context, identifier *string, name *string) (*models.UserPreset, error) {
	ret := _mock.Called(ctx, identifier, name)

	if len(ret) == 0 {
		panic("no return value specified for SaveUserPreset")
	}

	var r0 *models.UserPreset
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string) (*models.UserPreset, error)); ok {
		return returnFunc(ctx, identifier, name)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string) *models.UserPreset); ok {
		r0 = returnFunc(ctx, identifier, name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserPreset)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *string) error); ok {
		r1 = returnFunc(ctx, identifier, name)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_SaveUserPreset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SaveUserPreset'
type MockMutationResolver_SaveUserPreset_Call struct {
	*mock.Call
}

// SaveUserPreset is a helper method to define mock.On call
//   - ctx
//   - identifier
//   - name
func (_e *MockMutationResolver_Expecter) SaveUserPreset(ctx interface{}, identifier interface{}, name interface{}) *MockMutationResolver_SaveUserPreset_Call {
	return &MockMutationResolver_SaveUserPreset_Call{Call: _e.mock.On("SaveUserPreset", ctx, identifier, name)}
}

func (_c *MockMutationResolver_SaveUserPreset_Call) Run(run func(ctx context.Context, identifier *string, name *string)) *MockMutationResolver_SaveUserPreset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string), args[2].(*string))
	})
	return _c
}

func (_c *MockMutationResolver_SaveUserPreset_Call) Return(userPreset *models.UserPreset, err error) *MockMutationResolver_SaveUserPreset_Call {
	_c.Call.Return(userPreset, err)
	return _c
}

func (_c *MockMutationResolver_SaveUserPreset_Call) RunAndReturn(run func(ctx context.Context, identifier *string, name *string) (*models.UserPreset, error)) *MockMutationResolver_SaveUserPreset_Call {
	_c.Call.Return(run)
	return _c
}

// SendFeedback provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) SendFeedback(ctx context.Context, input models.Feedback) (*bool, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for SendFeedback")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.Feedback) (*bool, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.Feedback) *bool); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.Feedback) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_SendFeedback_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendFeedback'
type MockMutationResolver_SendFeedback_Call struct {
	*mock.Call
}

// SendFeedback is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) SendFeedback(ctx interface{}, input interface{}) *MockMutationResolver_SendFeedback_Call {
	return &MockMutationResolver_SendFeedback_Call{Call: _e.mock.On("SendFeedback", ctx, input)}
}

func (_c *MockMutationResolver_SendFeedback_Call) Run(run func(ctx context.Context, input models.Feedback)) *MockMutationResolver_SendFeedback_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.Feedback))
	})
	return _c
}

func (_c *MockMutationResolver_SendFeedback_Call) Return(b *bool, err error) *MockMutationResolver_SendFeedback_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_SendFeedback_Call) RunAndReturn(run func(ctx context.Context, input models.Feedback) (*bool, error)) *MockMutationResolver_SendFeedback_Call {
	_c.Call.Return(run)
	return _c
}

// SendFriendRequest provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) SendFriendRequest(ctx context.Context, sendRequestInput *models.FriendRequestInput) (bool, error) {
	ret := _mock.Called(ctx, sendRequestInput)

	if len(ret) == 0 {
		panic("no return value specified for SendFriendRequest")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.FriendRequestInput) (bool, error)); ok {
		return returnFunc(ctx, sendRequestInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.FriendRequestInput) bool); ok {
		r0 = returnFunc(ctx, sendRequestInput)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.FriendRequestInput) error); ok {
		r1 = returnFunc(ctx, sendRequestInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_SendFriendRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendFriendRequest'
type MockMutationResolver_SendFriendRequest_Call struct {
	*mock.Call
}

// SendFriendRequest is a helper method to define mock.On call
//   - ctx
//   - sendRequestInput
func (_e *MockMutationResolver_Expecter) SendFriendRequest(ctx interface{}, sendRequestInput interface{}) *MockMutationResolver_SendFriendRequest_Call {
	return &MockMutationResolver_SendFriendRequest_Call{Call: _e.mock.On("SendFriendRequest", ctx, sendRequestInput)}
}

func (_c *MockMutationResolver_SendFriendRequest_Call) Run(run func(ctx context.Context, sendRequestInput *models.FriendRequestInput)) *MockMutationResolver_SendFriendRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.FriendRequestInput))
	})
	return _c
}

func (_c *MockMutationResolver_SendFriendRequest_Call) Return(b bool, err error) *MockMutationResolver_SendFriendRequest_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_SendFriendRequest_Call) RunAndReturn(run func(ctx context.Context, sendRequestInput *models.FriendRequestInput) (bool, error)) *MockMutationResolver_SendFriendRequest_Call {
	_c.Call.Return(run)
	return _c
}

// SendOtp provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) SendOtp(ctx context.Context, email string) (bool, error) {
	ret := _mock.Called(ctx, email)

	if len(ret) == 0 {
		panic("no return value specified for SendOtp")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return returnFunc(ctx, email)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = returnFunc(ctx, email)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, email)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_SendOtp_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendOtp'
type MockMutationResolver_SendOtp_Call struct {
	*mock.Call
}

// SendOtp is a helper method to define mock.On call
//   - ctx
//   - email
func (_e *MockMutationResolver_Expecter) SendOtp(ctx interface{}, email interface{}) *MockMutationResolver_SendOtp_Call {
	return &MockMutationResolver_SendOtp_Call{Call: _e.mock.On("SendOtp", ctx, email)}
}

func (_c *MockMutationResolver_SendOtp_Call) Run(run func(ctx context.Context, email string)) *MockMutationResolver_SendOtp_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockMutationResolver_SendOtp_Call) Return(b bool, err error) *MockMutationResolver_SendOtp_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_SendOtp_Call) RunAndReturn(run func(ctx context.Context, email string) (bool, error)) *MockMutationResolver_SendOtp_Call {
	_c.Call.Return(run)
	return _c
}

// SignInWithApple provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) SignInWithApple(ctx context.Context, input models.AppleSignInInput) (*models.User, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for SignInWithApple")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.AppleSignInInput) (*models.User, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.AppleSignInInput) *models.User); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.AppleSignInInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_SignInWithApple_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SignInWithApple'
type MockMutationResolver_SignInWithApple_Call struct {
	*mock.Call
}

// SignInWithApple is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) SignInWithApple(ctx interface{}, input interface{}) *MockMutationResolver_SignInWithApple_Call {
	return &MockMutationResolver_SignInWithApple_Call{Call: _e.mock.On("SignInWithApple", ctx, input)}
}

func (_c *MockMutationResolver_SignInWithApple_Call) Run(run func(ctx context.Context, input models.AppleSignInInput)) *MockMutationResolver_SignInWithApple_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.AppleSignInInput))
	})
	return _c
}

func (_c *MockMutationResolver_SignInWithApple_Call) Return(user *models.User, err error) *MockMutationResolver_SignInWithApple_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockMutationResolver_SignInWithApple_Call) RunAndReturn(run func(ctx context.Context, input models.AppleSignInInput) (*models.User, error)) *MockMutationResolver_SignInWithApple_Call {
	_c.Call.Return(run)
	return _c
}

// StartGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) StartGame(ctx context.Context, startGameInput *models.StartGameInput) (*models.Game, error) {
	ret := _mock.Called(ctx, startGameInput)

	if len(ret) == 0 {
		panic("no return value specified for StartGame")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.StartGameInput) (*models.Game, error)); ok {
		return returnFunc(ctx, startGameInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.StartGameInput) *models.Game); ok {
		r0 = returnFunc(ctx, startGameInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.StartGameInput) error); ok {
		r1 = returnFunc(ctx, startGameInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_StartGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartGame'
type MockMutationResolver_StartGame_Call struct {
	*mock.Call
}

// StartGame is a helper method to define mock.On call
//   - ctx
//   - startGameInput
func (_e *MockMutationResolver_Expecter) StartGame(ctx interface{}, startGameInput interface{}) *MockMutationResolver_StartGame_Call {
	return &MockMutationResolver_StartGame_Call{Call: _e.mock.On("StartGame", ctx, startGameInput)}
}

func (_c *MockMutationResolver_StartGame_Call) Run(run func(ctx context.Context, startGameInput *models.StartGameInput)) *MockMutationResolver_StartGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.StartGameInput))
	})
	return _c
}

func (_c *MockMutationResolver_StartGame_Call) Return(game *models.Game, err error) *MockMutationResolver_StartGame_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockMutationResolver_StartGame_Call) RunAndReturn(run func(ctx context.Context, startGameInput *models.StartGameInput) (*models.Game, error)) *MockMutationResolver_StartGame_Call {
	_c.Call.Return(run)
	return _c
}

// StartGameForShowdown provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) StartGameForShowdown(ctx context.Context, input *models.StartGameForShowdownInput) (*models.Game, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for StartGameForShowdown")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.StartGameForShowdownInput) (*models.Game, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.StartGameForShowdownInput) *models.Game); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.StartGameForShowdownInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_StartGameForShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartGameForShowdown'
type MockMutationResolver_StartGameForShowdown_Call struct {
	*mock.Call
}

// StartGameForShowdown is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) StartGameForShowdown(ctx interface{}, input interface{}) *MockMutationResolver_StartGameForShowdown_Call {
	return &MockMutationResolver_StartGameForShowdown_Call{Call: _e.mock.On("StartGameForShowdown", ctx, input)}
}

func (_c *MockMutationResolver_StartGameForShowdown_Call) Run(run func(ctx context.Context, input *models.StartGameForShowdownInput)) *MockMutationResolver_StartGameForShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.StartGameForShowdownInput))
	})
	return _c
}

func (_c *MockMutationResolver_StartGameForShowdown_Call) Return(game *models.Game, err error) *MockMutationResolver_StartGameForShowdown_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockMutationResolver_StartGameForShowdown_Call) RunAndReturn(run func(ctx context.Context, input *models.StartGameForShowdownInput) (*models.Game, error)) *MockMutationResolver_StartGameForShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// StartPuzzleGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) StartPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for StartPuzzleGame")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_StartPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartPuzzleGame'
type MockMutationResolver_StartPuzzleGame_Call struct {
	*mock.Call
}

// StartPuzzleGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockMutationResolver_Expecter) StartPuzzleGame(ctx interface{}, gameID interface{}) *MockMutationResolver_StartPuzzleGame_Call {
	return &MockMutationResolver_StartPuzzleGame_Call{Call: _e.mock.On("StartPuzzleGame", ctx, gameID)}
}

func (_c *MockMutationResolver_StartPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockMutationResolver_StartPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_StartPuzzleGame_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockMutationResolver_StartPuzzleGame_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockMutationResolver_StartPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)) *MockMutationResolver_StartPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// StartSearching provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) StartSearching(ctx context.Context, gameConfig *models.GameConfigInput) (*bool, error) {
	ret := _mock.Called(ctx, gameConfig)

	if len(ret) == 0 {
		panic("no return value specified for StartSearching")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GameConfigInput) (*bool, error)); ok {
		return returnFunc(ctx, gameConfig)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GameConfigInput) *bool); ok {
		r0 = returnFunc(ctx, gameConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.GameConfigInput) error); ok {
		r1 = returnFunc(ctx, gameConfig)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_StartSearching_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartSearching'
type MockMutationResolver_StartSearching_Call struct {
	*mock.Call
}

// StartSearching is a helper method to define mock.On call
//   - ctx
//   - gameConfig
func (_e *MockMutationResolver_Expecter) StartSearching(ctx interface{}, gameConfig interface{}) *MockMutationResolver_StartSearching_Call {
	return &MockMutationResolver_StartSearching_Call{Call: _e.mock.On("StartSearching", ctx, gameConfig)}
}

func (_c *MockMutationResolver_StartSearching_Call) Run(run func(ctx context.Context, gameConfig *models.GameConfigInput)) *MockMutationResolver_StartSearching_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.GameConfigInput))
	})
	return _c
}

func (_c *MockMutationResolver_StartSearching_Call) Return(b *bool, err error) *MockMutationResolver_StartSearching_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_StartSearching_Call) RunAndReturn(run func(ctx context.Context, gameConfig *models.GameConfigInput) (*bool, error)) *MockMutationResolver_StartSearching_Call {
	_c.Call.Return(run)
	return _c
}

// StartSearchingForPuzzleGame provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) StartSearchingForPuzzleGame(ctx context.Context, gameConfig *models.PuzzleGameConfigInput) (*bool, error) {
	ret := _mock.Called(ctx, gameConfig)

	if len(ret) == 0 {
		panic("no return value specified for StartSearchingForPuzzleGame")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PuzzleGameConfigInput) (*bool, error)); ok {
		return returnFunc(ctx, gameConfig)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PuzzleGameConfigInput) *bool); ok {
		r0 = returnFunc(ctx, gameConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.PuzzleGameConfigInput) error); ok {
		r1 = returnFunc(ctx, gameConfig)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_StartSearchingForPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartSearchingForPuzzleGame'
type MockMutationResolver_StartSearchingForPuzzleGame_Call struct {
	*mock.Call
}

// StartSearchingForPuzzleGame is a helper method to define mock.On call
//   - ctx
//   - gameConfig
func (_e *MockMutationResolver_Expecter) StartSearchingForPuzzleGame(ctx interface{}, gameConfig interface{}) *MockMutationResolver_StartSearchingForPuzzleGame_Call {
	return &MockMutationResolver_StartSearchingForPuzzleGame_Call{Call: _e.mock.On("StartSearchingForPuzzleGame", ctx, gameConfig)}
}

func (_c *MockMutationResolver_StartSearchingForPuzzleGame_Call) Run(run func(ctx context.Context, gameConfig *models.PuzzleGameConfigInput)) *MockMutationResolver_StartSearchingForPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.PuzzleGameConfigInput))
	})
	return _c
}

func (_c *MockMutationResolver_StartSearchingForPuzzleGame_Call) Return(b *bool, err error) *MockMutationResolver_StartSearchingForPuzzleGame_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_StartSearchingForPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameConfig *models.PuzzleGameConfigInput) (*bool, error)) *MockMutationResolver_StartSearchingForPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitAnswer provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) SubmitAnswer(ctx context.Context, answerInput *models.SubmitAnswerInput) (*models.Game, error) {
	ret := _mock.Called(ctx, answerInput)

	if len(ret) == 0 {
		panic("no return value specified for SubmitAnswer")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.SubmitAnswerInput) (*models.Game, error)); ok {
		return returnFunc(ctx, answerInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.SubmitAnswerInput) *models.Game); ok {
		r0 = returnFunc(ctx, answerInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.SubmitAnswerInput) error); ok {
		r1 = returnFunc(ctx, answerInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_SubmitAnswer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitAnswer'
type MockMutationResolver_SubmitAnswer_Call struct {
	*mock.Call
}

// SubmitAnswer is a helper method to define mock.On call
//   - ctx
//   - answerInput
func (_e *MockMutationResolver_Expecter) SubmitAnswer(ctx interface{}, answerInput interface{}) *MockMutationResolver_SubmitAnswer_Call {
	return &MockMutationResolver_SubmitAnswer_Call{Call: _e.mock.On("SubmitAnswer", ctx, answerInput)}
}

func (_c *MockMutationResolver_SubmitAnswer_Call) Run(run func(ctx context.Context, answerInput *models.SubmitAnswerInput)) *MockMutationResolver_SubmitAnswer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.SubmitAnswerInput))
	})
	return _c
}

func (_c *MockMutationResolver_SubmitAnswer_Call) Return(game *models.Game, err error) *MockMutationResolver_SubmitAnswer_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockMutationResolver_SubmitAnswer_Call) RunAndReturn(run func(ctx context.Context, answerInput *models.SubmitAnswerInput) (*models.Game, error)) *MockMutationResolver_SubmitAnswer_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitChallengeResult provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) SubmitChallengeResult(ctx context.Context, input models.SubmitSolutionInput) (*models.SubmitChallengeResult, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for SubmitChallengeResult")
	}

	var r0 *models.SubmitChallengeResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.SubmitSolutionInput) (*models.SubmitChallengeResult, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.SubmitSolutionInput) *models.SubmitChallengeResult); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.SubmitChallengeResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.SubmitSolutionInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_SubmitChallengeResult_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitChallengeResult'
type MockMutationResolver_SubmitChallengeResult_Call struct {
	*mock.Call
}

// SubmitChallengeResult is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) SubmitChallengeResult(ctx interface{}, input interface{}) *MockMutationResolver_SubmitChallengeResult_Call {
	return &MockMutationResolver_SubmitChallengeResult_Call{Call: _e.mock.On("SubmitChallengeResult", ctx, input)}
}

func (_c *MockMutationResolver_SubmitChallengeResult_Call) Run(run func(ctx context.Context, input models.SubmitSolutionInput)) *MockMutationResolver_SubmitChallengeResult_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.SubmitSolutionInput))
	})
	return _c
}

func (_c *MockMutationResolver_SubmitChallengeResult_Call) Return(submitChallengeResult *models.SubmitChallengeResult, err error) *MockMutationResolver_SubmitChallengeResult_Call {
	_c.Call.Return(submitChallengeResult, err)
	return _c
}

func (_c *MockMutationResolver_SubmitChallengeResult_Call) RunAndReturn(run func(ctx context.Context, input models.SubmitSolutionInput) (*models.SubmitChallengeResult, error)) *MockMutationResolver_SubmitChallengeResult_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitContestAnswer provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) SubmitContestAnswer(ctx context.Context, contestID primitive.ObjectID, questionID string, answer string) (bool, error) {
	ret := _mock.Called(ctx, contestID, questionID, answer)

	if len(ret) == 0 {
		panic("no return value specified for SubmitContestAnswer")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, string, string) (bool, error)); ok {
		return returnFunc(ctx, contestID, questionID, answer)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, string, string) bool); ok {
		r0 = returnFunc(ctx, contestID, questionID, answer)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, string, string) error); ok {
		r1 = returnFunc(ctx, contestID, questionID, answer)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_SubmitContestAnswer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitContestAnswer'
type MockMutationResolver_SubmitContestAnswer_Call struct {
	*mock.Call
}

// SubmitContestAnswer is a helper method to define mock.On call
//   - ctx
//   - contestID
//   - questionID
//   - answer
func (_e *MockMutationResolver_Expecter) SubmitContestAnswer(ctx interface{}, contestID interface{}, questionID interface{}, answer interface{}) *MockMutationResolver_SubmitContestAnswer_Call {
	return &MockMutationResolver_SubmitContestAnswer_Call{Call: _e.mock.On("SubmitContestAnswer", ctx, contestID, questionID, answer)}
}

func (_c *MockMutationResolver_SubmitContestAnswer_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID, questionID string, answer string)) *MockMutationResolver_SubmitContestAnswer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockMutationResolver_SubmitContestAnswer_Call) Return(b bool, err error) *MockMutationResolver_SubmitContestAnswer_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_SubmitContestAnswer_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID, questionID string, answer string) (bool, error)) *MockMutationResolver_SubmitContestAnswer_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitFlashAnzanAnswer provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) SubmitFlashAnzanAnswer(ctx context.Context, answerInput *models.SubmitFlashAnzanAnswerInput) (*models.Game, error) {
	ret := _mock.Called(ctx, answerInput)

	if len(ret) == 0 {
		panic("no return value specified for SubmitFlashAnzanAnswer")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.SubmitFlashAnzanAnswerInput) (*models.Game, error)); ok {
		return returnFunc(ctx, answerInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.SubmitFlashAnzanAnswerInput) *models.Game); ok {
		r0 = returnFunc(ctx, answerInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.SubmitFlashAnzanAnswerInput) error); ok {
		r1 = returnFunc(ctx, answerInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_SubmitFlashAnzanAnswer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitFlashAnzanAnswer'
type MockMutationResolver_SubmitFlashAnzanAnswer_Call struct {
	*mock.Call
}

// SubmitFlashAnzanAnswer is a helper method to define mock.On call
//   - ctx
//   - answerInput
func (_e *MockMutationResolver_Expecter) SubmitFlashAnzanAnswer(ctx interface{}, answerInput interface{}) *MockMutationResolver_SubmitFlashAnzanAnswer_Call {
	return &MockMutationResolver_SubmitFlashAnzanAnswer_Call{Call: _e.mock.On("SubmitFlashAnzanAnswer", ctx, answerInput)}
}

func (_c *MockMutationResolver_SubmitFlashAnzanAnswer_Call) Run(run func(ctx context.Context, answerInput *models.SubmitFlashAnzanAnswerInput)) *MockMutationResolver_SubmitFlashAnzanAnswer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.SubmitFlashAnzanAnswerInput))
	})
	return _c
}

func (_c *MockMutationResolver_SubmitFlashAnzanAnswer_Call) Return(game *models.Game, err error) *MockMutationResolver_SubmitFlashAnzanAnswer_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockMutationResolver_SubmitFlashAnzanAnswer_Call) RunAndReturn(run func(ctx context.Context, answerInput *models.SubmitFlashAnzanAnswerInput) (*models.Game, error)) *MockMutationResolver_SubmitFlashAnzanAnswer_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitPuzzleGameAnswer provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) SubmitPuzzleGameAnswer(ctx context.Context, answerInput *models.SubmitPuzzleGameAnswerInput) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, answerInput)

	if len(ret) == 0 {
		panic("no return value specified for SubmitPuzzleGameAnswer")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.SubmitPuzzleGameAnswerInput) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, answerInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.SubmitPuzzleGameAnswerInput) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, answerInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.SubmitPuzzleGameAnswerInput) error); ok {
		r1 = returnFunc(ctx, answerInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_SubmitPuzzleGameAnswer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitPuzzleGameAnswer'
type MockMutationResolver_SubmitPuzzleGameAnswer_Call struct {
	*mock.Call
}

// SubmitPuzzleGameAnswer is a helper method to define mock.On call
//   - ctx
//   - answerInput
func (_e *MockMutationResolver_Expecter) SubmitPuzzleGameAnswer(ctx interface{}, answerInput interface{}) *MockMutationResolver_SubmitPuzzleGameAnswer_Call {
	return &MockMutationResolver_SubmitPuzzleGameAnswer_Call{Call: _e.mock.On("SubmitPuzzleGameAnswer", ctx, answerInput)}
}

func (_c *MockMutationResolver_SubmitPuzzleGameAnswer_Call) Run(run func(ctx context.Context, answerInput *models.SubmitPuzzleGameAnswerInput)) *MockMutationResolver_SubmitPuzzleGameAnswer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.SubmitPuzzleGameAnswerInput))
	})
	return _c
}

func (_c *MockMutationResolver_SubmitPuzzleGameAnswer_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockMutationResolver_SubmitPuzzleGameAnswer_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockMutationResolver_SubmitPuzzleGameAnswer_Call) RunAndReturn(run func(ctx context.Context, answerInput *models.SubmitPuzzleGameAnswerInput) (*models.PuzzleGame, error)) *MockMutationResolver_SubmitPuzzleGameAnswer_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitPuzzleGameRush provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) SubmitPuzzleGameRush(ctx context.Context, input models.SubmitPuzzleRushGame) (*models.CrossMathPuzzleRush, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for SubmitPuzzleGameRush")
	}

	var r0 *models.CrossMathPuzzleRush
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.SubmitPuzzleRushGame) (*models.CrossMathPuzzleRush, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.SubmitPuzzleRushGame) *models.CrossMathPuzzleRush); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.CrossMathPuzzleRush)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.SubmitPuzzleRushGame) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_SubmitPuzzleGameRush_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitPuzzleGameRush'
type MockMutationResolver_SubmitPuzzleGameRush_Call struct {
	*mock.Call
}

// SubmitPuzzleGameRush is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) SubmitPuzzleGameRush(ctx interface{}, input interface{}) *MockMutationResolver_SubmitPuzzleGameRush_Call {
	return &MockMutationResolver_SubmitPuzzleGameRush_Call{Call: _e.mock.On("SubmitPuzzleGameRush", ctx, input)}
}

func (_c *MockMutationResolver_SubmitPuzzleGameRush_Call) Run(run func(ctx context.Context, input models.SubmitPuzzleRushGame)) *MockMutationResolver_SubmitPuzzleGameRush_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.SubmitPuzzleRushGame))
	})
	return _c
}

func (_c *MockMutationResolver_SubmitPuzzleGameRush_Call) Return(crossMathPuzzleRush *models.CrossMathPuzzleRush, err error) *MockMutationResolver_SubmitPuzzleGameRush_Call {
	_c.Call.Return(crossMathPuzzleRush, err)
	return _c
}

func (_c *MockMutationResolver_SubmitPuzzleGameRush_Call) RunAndReturn(run func(ctx context.Context, input models.SubmitPuzzleRushGame) (*models.CrossMathPuzzleRush, error)) *MockMutationResolver_SubmitPuzzleGameRush_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitPuzzleSolution provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) SubmitPuzzleSolution(ctx context.Context, puzzleID primitive.ObjectID, timeSpent int) (*models.PuzzleResult, error) {
	ret := _mock.Called(ctx, puzzleID, timeSpent)

	if len(ret) == 0 {
		panic("no return value specified for SubmitPuzzleSolution")
	}

	var r0 *models.PuzzleResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) (*models.PuzzleResult, error)); ok {
		return returnFunc(ctx, puzzleID, timeSpent)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) *models.PuzzleResult); ok {
		r0 = returnFunc(ctx, puzzleID, timeSpent)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int) error); ok {
		r1 = returnFunc(ctx, puzzleID, timeSpent)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_SubmitPuzzleSolution_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitPuzzleSolution'
type MockMutationResolver_SubmitPuzzleSolution_Call struct {
	*mock.Call
}

// SubmitPuzzleSolution is a helper method to define mock.On call
//   - ctx
//   - puzzleID
//   - timeSpent
func (_e *MockMutationResolver_Expecter) SubmitPuzzleSolution(ctx interface{}, puzzleID interface{}, timeSpent interface{}) *MockMutationResolver_SubmitPuzzleSolution_Call {
	return &MockMutationResolver_SubmitPuzzleSolution_Call{Call: _e.mock.On("SubmitPuzzleSolution", ctx, puzzleID, timeSpent)}
}

func (_c *MockMutationResolver_SubmitPuzzleSolution_Call) Run(run func(ctx context.Context, puzzleID primitive.ObjectID, timeSpent int)) *MockMutationResolver_SubmitPuzzleSolution_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int))
	})
	return _c
}

func (_c *MockMutationResolver_SubmitPuzzleSolution_Call) Return(puzzleResult *models.PuzzleResult, err error) *MockMutationResolver_SubmitPuzzleSolution_Call {
	_c.Call.Return(puzzleResult, err)
	return _c
}

func (_c *MockMutationResolver_SubmitPuzzleSolution_Call) RunAndReturn(run func(ctx context.Context, puzzleID primitive.ObjectID, timeSpent int) (*models.PuzzleResult, error)) *MockMutationResolver_SubmitPuzzleSolution_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitRatingFixtureResponses provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) SubmitRatingFixtureResponses(ctx context.Context, submission []*int, timeTaken int) (*models.UserRatingFixtureSubmission, error) {
	ret := _mock.Called(ctx, submission, timeTaken)

	if len(ret) == 0 {
		panic("no return value specified for SubmitRatingFixtureResponses")
	}

	var r0 *models.UserRatingFixtureSubmission
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*int, int) (*models.UserRatingFixtureSubmission, error)); ok {
		return returnFunc(ctx, submission, timeTaken)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*int, int) *models.UserRatingFixtureSubmission); ok {
		r0 = returnFunc(ctx, submission, timeTaken)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserRatingFixtureSubmission)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []*int, int) error); ok {
		r1 = returnFunc(ctx, submission, timeTaken)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_SubmitRatingFixtureResponses_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitRatingFixtureResponses'
type MockMutationResolver_SubmitRatingFixtureResponses_Call struct {
	*mock.Call
}

// SubmitRatingFixtureResponses is a helper method to define mock.On call
//   - ctx
//   - submission
//   - timeTaken
func (_e *MockMutationResolver_Expecter) SubmitRatingFixtureResponses(ctx interface{}, submission interface{}, timeTaken interface{}) *MockMutationResolver_SubmitRatingFixtureResponses_Call {
	return &MockMutationResolver_SubmitRatingFixtureResponses_Call{Call: _e.mock.On("SubmitRatingFixtureResponses", ctx, submission, timeTaken)}
}

func (_c *MockMutationResolver_SubmitRatingFixtureResponses_Call) Run(run func(ctx context.Context, submission []*int, timeTaken int)) *MockMutationResolver_SubmitRatingFixtureResponses_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*int), args[2].(int))
	})
	return _c
}

func (_c *MockMutationResolver_SubmitRatingFixtureResponses_Call) Return(userRatingFixtureSubmission *models.UserRatingFixtureSubmission, err error) *MockMutationResolver_SubmitRatingFixtureResponses_Call {
	_c.Call.Return(userRatingFixtureSubmission, err)
	return _c
}

func (_c *MockMutationResolver_SubmitRatingFixtureResponses_Call) RunAndReturn(run func(ctx context.Context, submission []*int, timeTaken int) (*models.UserRatingFixtureSubmission, error)) *MockMutationResolver_SubmitRatingFixtureResponses_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitReferral provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) SubmitReferral(ctx context.Context, referralCode string) (bool, error) {
	ret := _mock.Called(ctx, referralCode)

	if len(ret) == 0 {
		panic("no return value specified for SubmitReferral")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return returnFunc(ctx, referralCode)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = returnFunc(ctx, referralCode)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, referralCode)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_SubmitReferral_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitReferral'
type MockMutationResolver_SubmitReferral_Call struct {
	*mock.Call
}

// SubmitReferral is a helper method to define mock.On call
//   - ctx
//   - referralCode
func (_e *MockMutationResolver_Expecter) SubmitReferral(ctx interface{}, referralCode interface{}) *MockMutationResolver_SubmitReferral_Call {
	return &MockMutationResolver_SubmitReferral_Call{Call: _e.mock.On("SubmitReferral", ctx, referralCode)}
}

func (_c *MockMutationResolver_SubmitReferral_Call) Run(run func(ctx context.Context, referralCode string)) *MockMutationResolver_SubmitReferral_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockMutationResolver_SubmitReferral_Call) Return(b bool, err error) *MockMutationResolver_SubmitReferral_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_SubmitReferral_Call) RunAndReturn(run func(ctx context.Context, referralCode string) (bool, error)) *MockMutationResolver_SubmitReferral_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitUserPresetResult provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) SubmitUserPresetResult(ctx context.Context, userPresetResultInput *models.UserPresetResultInput) (*bool, error) {
	ret := _mock.Called(ctx, userPresetResultInput)

	if len(ret) == 0 {
		panic("no return value specified for SubmitUserPresetResult")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserPresetResultInput) (*bool, error)); ok {
		return returnFunc(ctx, userPresetResultInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserPresetResultInput) *bool); ok {
		r0 = returnFunc(ctx, userPresetResultInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.UserPresetResultInput) error); ok {
		r1 = returnFunc(ctx, userPresetResultInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_SubmitUserPresetResult_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitUserPresetResult'
type MockMutationResolver_SubmitUserPresetResult_Call struct {
	*mock.Call
}

// SubmitUserPresetResult is a helper method to define mock.On call
//   - ctx
//   - userPresetResultInput
func (_e *MockMutationResolver_Expecter) SubmitUserPresetResult(ctx interface{}, userPresetResultInput interface{}) *MockMutationResolver_SubmitUserPresetResult_Call {
	return &MockMutationResolver_SubmitUserPresetResult_Call{Call: _e.mock.On("SubmitUserPresetResult", ctx, userPresetResultInput)}
}

func (_c *MockMutationResolver_SubmitUserPresetResult_Call) Run(run func(ctx context.Context, userPresetResultInput *models.UserPresetResultInput)) *MockMutationResolver_SubmitUserPresetResult_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.UserPresetResultInput))
	})
	return _c
}

func (_c *MockMutationResolver_SubmitUserPresetResult_Call) Return(b *bool, err error) *MockMutationResolver_SubmitUserPresetResult_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_SubmitUserPresetResult_Call) RunAndReturn(run func(ctx context.Context, userPresetResultInput *models.UserPresetResultInput) (*bool, error)) *MockMutationResolver_SubmitUserPresetResult_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitVirtualContestAnswer provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) SubmitVirtualContestAnswer(ctx context.Context, contestID primitive.ObjectID, questionID string, answer string) (bool, error) {
	ret := _mock.Called(ctx, contestID, questionID, answer)

	if len(ret) == 0 {
		panic("no return value specified for SubmitVirtualContestAnswer")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, string, string) (bool, error)); ok {
		return returnFunc(ctx, contestID, questionID, answer)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, string, string) bool); ok {
		r0 = returnFunc(ctx, contestID, questionID, answer)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, string, string) error); ok {
		r1 = returnFunc(ctx, contestID, questionID, answer)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_SubmitVirtualContestAnswer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitVirtualContestAnswer'
type MockMutationResolver_SubmitVirtualContestAnswer_Call struct {
	*mock.Call
}

// SubmitVirtualContestAnswer is a helper method to define mock.On call
//   - ctx
//   - contestID
//   - questionID
//   - answer
func (_e *MockMutationResolver_Expecter) SubmitVirtualContestAnswer(ctx interface{}, contestID interface{}, questionID interface{}, answer interface{}) *MockMutationResolver_SubmitVirtualContestAnswer_Call {
	return &MockMutationResolver_SubmitVirtualContestAnswer_Call{Call: _e.mock.On("SubmitVirtualContestAnswer", ctx, contestID, questionID, answer)}
}

func (_c *MockMutationResolver_SubmitVirtualContestAnswer_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID, questionID string, answer string)) *MockMutationResolver_SubmitVirtualContestAnswer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockMutationResolver_SubmitVirtualContestAnswer_Call) Return(b bool, err error) *MockMutationResolver_SubmitVirtualContestAnswer_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_SubmitVirtualContestAnswer_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID, questionID string, answer string) (bool, error)) *MockMutationResolver_SubmitVirtualContestAnswer_Call {
	_c.Call.Return(run)
	return _c
}

// TakePledge provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) TakePledge(ctx context.Context, duration *int) (*bool, error) {
	ret := _mock.Called(ctx, duration)

	if len(ret) == 0 {
		panic("no return value specified for TakePledge")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int) (*bool, error)); ok {
		return returnFunc(ctx, duration)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int) *bool); ok {
		r0 = returnFunc(ctx, duration)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int) error); ok {
		r1 = returnFunc(ctx, duration)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_TakePledge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TakePledge'
type MockMutationResolver_TakePledge_Call struct {
	*mock.Call
}

// TakePledge is a helper method to define mock.On call
//   - ctx
//   - duration
func (_e *MockMutationResolver_Expecter) TakePledge(ctx interface{}, duration interface{}) *MockMutationResolver_TakePledge_Call {
	return &MockMutationResolver_TakePledge_Call{Call: _e.mock.On("TakePledge", ctx, duration)}
}

func (_c *MockMutationResolver_TakePledge_Call) Run(run func(ctx context.Context, duration *int)) *MockMutationResolver_TakePledge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*int))
	})
	return _c
}

func (_c *MockMutationResolver_TakePledge_Call) Return(b *bool, err error) *MockMutationResolver_TakePledge_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_TakePledge_Call) RunAndReturn(run func(ctx context.Context, duration *int) (*bool, error)) *MockMutationResolver_TakePledge_Call {
	_c.Call.Return(run)
	return _c
}

// UnFollowUser provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UnFollowUser(ctx context.Context, unFollowUserInput *models.UnFollowUserInput) (bool, error) {
	ret := _mock.Called(ctx, unFollowUserInput)

	if len(ret) == 0 {
		panic("no return value specified for UnFollowUser")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UnFollowUserInput) (bool, error)); ok {
		return returnFunc(ctx, unFollowUserInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UnFollowUserInput) bool); ok {
		r0 = returnFunc(ctx, unFollowUserInput)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.UnFollowUserInput) error); ok {
		r1 = returnFunc(ctx, unFollowUserInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UnFollowUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UnFollowUser'
type MockMutationResolver_UnFollowUser_Call struct {
	*mock.Call
}

// UnFollowUser is a helper method to define mock.On call
//   - ctx
//   - unFollowUserInput
func (_e *MockMutationResolver_Expecter) UnFollowUser(ctx interface{}, unFollowUserInput interface{}) *MockMutationResolver_UnFollowUser_Call {
	return &MockMutationResolver_UnFollowUser_Call{Call: _e.mock.On("UnFollowUser", ctx, unFollowUserInput)}
}

func (_c *MockMutationResolver_UnFollowUser_Call) Run(run func(ctx context.Context, unFollowUserInput *models.UnFollowUserInput)) *MockMutationResolver_UnFollowUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.UnFollowUserInput))
	})
	return _c
}

func (_c *MockMutationResolver_UnFollowUser_Call) Return(b bool, err error) *MockMutationResolver_UnFollowUser_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_UnFollowUser_Call) RunAndReturn(run func(ctx context.Context, unFollowUserInput *models.UnFollowUserInput) (bool, error)) *MockMutationResolver_UnFollowUser_Call {
	_c.Call.Return(run)
	return _c
}

// UnregisterDeviceToken provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UnregisterDeviceToken(ctx context.Context, pushNotificationToken *string, deviceID *string) (*models.DeviceTokenRegistrationResponse, error) {
	ret := _mock.Called(ctx, pushNotificationToken, deviceID)

	if len(ret) == 0 {
		panic("no return value specified for UnregisterDeviceToken")
	}

	var r0 *models.DeviceTokenRegistrationResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string) (*models.DeviceTokenRegistrationResponse, error)); ok {
		return returnFunc(ctx, pushNotificationToken, deviceID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string) *models.DeviceTokenRegistrationResponse); ok {
		r0 = returnFunc(ctx, pushNotificationToken, deviceID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.DeviceTokenRegistrationResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *string) error); ok {
		r1 = returnFunc(ctx, pushNotificationToken, deviceID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UnregisterDeviceToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UnregisterDeviceToken'
type MockMutationResolver_UnregisterDeviceToken_Call struct {
	*mock.Call
}

// UnregisterDeviceToken is a helper method to define mock.On call
//   - ctx
//   - pushNotificationToken
//   - deviceID
func (_e *MockMutationResolver_Expecter) UnregisterDeviceToken(ctx interface{}, pushNotificationToken interface{}, deviceID interface{}) *MockMutationResolver_UnregisterDeviceToken_Call {
	return &MockMutationResolver_UnregisterDeviceToken_Call{Call: _e.mock.On("UnregisterDeviceToken", ctx, pushNotificationToken, deviceID)}
}

func (_c *MockMutationResolver_UnregisterDeviceToken_Call) Run(run func(ctx context.Context, pushNotificationToken *string, deviceID *string)) *MockMutationResolver_UnregisterDeviceToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*string), args[2].(*string))
	})
	return _c
}

func (_c *MockMutationResolver_UnregisterDeviceToken_Call) Return(deviceTokenRegistrationResponse *models.DeviceTokenRegistrationResponse, err error) *MockMutationResolver_UnregisterDeviceToken_Call {
	_c.Call.Return(deviceTokenRegistrationResponse, err)
	return _c
}

func (_c *MockMutationResolver_UnregisterDeviceToken_Call) RunAndReturn(run func(ctx context.Context, pushNotificationToken *string, deviceID *string) (*models.DeviceTokenRegistrationResponse, error)) *MockMutationResolver_UnregisterDeviceToken_Call {
	_c.Call.Return(run)
	return _c
}

// UnregisterFromContest provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UnregisterFromContest(ctx context.Context, contestID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, contestID)

	if len(ret) == 0 {
		panic("no return value specified for UnregisterFromContest")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, contestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, contestID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, contestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UnregisterFromContest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UnregisterFromContest'
type MockMutationResolver_UnregisterFromContest_Call struct {
	*mock.Call
}

// UnregisterFromContest is a helper method to define mock.On call
//   - ctx
//   - contestID
func (_e *MockMutationResolver_Expecter) UnregisterFromContest(ctx interface{}, contestID interface{}) *MockMutationResolver_UnregisterFromContest_Call {
	return &MockMutationResolver_UnregisterFromContest_Call{Call: _e.mock.On("UnregisterFromContest", ctx, contestID)}
}

func (_c *MockMutationResolver_UnregisterFromContest_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID)) *MockMutationResolver_UnregisterFromContest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_UnregisterFromContest_Call) Return(b bool, err error) *MockMutationResolver_UnregisterFromContest_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_UnregisterFromContest_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID) (bool, error)) *MockMutationResolver_UnregisterFromContest_Call {
	_c.Call.Return(run)
	return _c
}

// UnregisterFromShowdown provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UnregisterFromShowdown(ctx context.Context, showdownID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, showdownID)

	if len(ret) == 0 {
		panic("no return value specified for UnregisterFromShowdown")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, showdownID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, showdownID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UnregisterFromShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UnregisterFromShowdown'
type MockMutationResolver_UnregisterFromShowdown_Call struct {
	*mock.Call
}

// UnregisterFromShowdown is a helper method to define mock.On call
//   - ctx
//   - showdownID
func (_e *MockMutationResolver_Expecter) UnregisterFromShowdown(ctx interface{}, showdownID interface{}) *MockMutationResolver_UnregisterFromShowdown_Call {
	return &MockMutationResolver_UnregisterFromShowdown_Call{Call: _e.mock.On("UnregisterFromShowdown", ctx, showdownID)}
}

func (_c *MockMutationResolver_UnregisterFromShowdown_Call) Run(run func(ctx context.Context, showdownID primitive.ObjectID)) *MockMutationResolver_UnregisterFromShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_UnregisterFromShowdown_Call) Return(b bool, err error) *MockMutationResolver_UnregisterFromShowdown_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_UnregisterFromShowdown_Call) RunAndReturn(run func(ctx context.Context, showdownID primitive.ObjectID) (bool, error)) *MockMutationResolver_UnregisterFromShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateAnnouncement provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UpdateAnnouncement(ctx context.Context, id primitive.ObjectID, input models.UpdateAnnouncementInput) (*models.Announcement, error) {
	ret := _mock.Called(ctx, id, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateAnnouncement")
	}

	var r0 *models.Announcement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.UpdateAnnouncementInput) (*models.Announcement, error)); ok {
		return returnFunc(ctx, id, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.UpdateAnnouncementInput) *models.Announcement); ok {
		r0 = returnFunc(ctx, id, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Announcement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, models.UpdateAnnouncementInput) error); ok {
		r1 = returnFunc(ctx, id, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UpdateAnnouncement_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateAnnouncement'
type MockMutationResolver_UpdateAnnouncement_Call struct {
	*mock.Call
}

// UpdateAnnouncement is a helper method to define mock.On call
//   - ctx
//   - id
//   - input
func (_e *MockMutationResolver_Expecter) UpdateAnnouncement(ctx interface{}, id interface{}, input interface{}) *MockMutationResolver_UpdateAnnouncement_Call {
	return &MockMutationResolver_UpdateAnnouncement_Call{Call: _e.mock.On("UpdateAnnouncement", ctx, id, input)}
}

func (_c *MockMutationResolver_UpdateAnnouncement_Call) Run(run func(ctx context.Context, id primitive.ObjectID, input models.UpdateAnnouncementInput)) *MockMutationResolver_UpdateAnnouncement_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(models.UpdateAnnouncementInput))
	})
	return _c
}

func (_c *MockMutationResolver_UpdateAnnouncement_Call) Return(announcement *models.Announcement, err error) *MockMutationResolver_UpdateAnnouncement_Call {
	_c.Call.Return(announcement, err)
	return _c
}

func (_c *MockMutationResolver_UpdateAnnouncement_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID, input models.UpdateAnnouncementInput) (*models.Announcement, error)) *MockMutationResolver_UpdateAnnouncement_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateClub provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UpdateClub(ctx context.Context, input models.UpdateClubInput) (bool, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateClub")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.UpdateClubInput) (bool, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.UpdateClubInput) bool); ok {
		r0 = returnFunc(ctx, input)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.UpdateClubInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UpdateClub_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateClub'
type MockMutationResolver_UpdateClub_Call struct {
	*mock.Call
}

// UpdateClub is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) UpdateClub(ctx interface{}, input interface{}) *MockMutationResolver_UpdateClub_Call {
	return &MockMutationResolver_UpdateClub_Call{Call: _e.mock.On("UpdateClub", ctx, input)}
}

func (_c *MockMutationResolver_UpdateClub_Call) Run(run func(ctx context.Context, input models.UpdateClubInput)) *MockMutationResolver_UpdateClub_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.UpdateClubInput))
	})
	return _c
}

func (_c *MockMutationResolver_UpdateClub_Call) Return(b bool, err error) *MockMutationResolver_UpdateClub_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_UpdateClub_Call) RunAndReturn(run func(ctx context.Context, input models.UpdateClubInput) (bool, error)) *MockMutationResolver_UpdateClub_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateClubEvent provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UpdateClubEvent(ctx context.Context, input models.UpdateClubEventInput) (*models.ClubEvent, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateClubEvent")
	}

	var r0 *models.ClubEvent
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.UpdateClubEventInput) (*models.ClubEvent, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.UpdateClubEventInput) *models.ClubEvent); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubEvent)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.UpdateClubEventInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UpdateClubEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateClubEvent'
type MockMutationResolver_UpdateClubEvent_Call struct {
	*mock.Call
}

// UpdateClubEvent is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockMutationResolver_Expecter) UpdateClubEvent(ctx interface{}, input interface{}) *MockMutationResolver_UpdateClubEvent_Call {
	return &MockMutationResolver_UpdateClubEvent_Call{Call: _e.mock.On("UpdateClubEvent", ctx, input)}
}

func (_c *MockMutationResolver_UpdateClubEvent_Call) Run(run func(ctx context.Context, input models.UpdateClubEventInput)) *MockMutationResolver_UpdateClubEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.UpdateClubEventInput))
	})
	return _c
}

func (_c *MockMutationResolver_UpdateClubEvent_Call) Return(clubEvent *models.ClubEvent, err error) *MockMutationResolver_UpdateClubEvent_Call {
	_c.Call.Return(clubEvent, err)
	return _c
}

func (_c *MockMutationResolver_UpdateClubEvent_Call) RunAndReturn(run func(ctx context.Context, input models.UpdateClubEventInput) (*models.ClubEvent, error)) *MockMutationResolver_UpdateClubEvent_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateContestParticipantStartTime provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UpdateContestParticipantStartTime(ctx context.Context, contestID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, contestID)

	if len(ret) == 0 {
		panic("no return value specified for UpdateContestParticipantStartTime")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, contestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, contestID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, contestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UpdateContestParticipantStartTime_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateContestParticipantStartTime'
type MockMutationResolver_UpdateContestParticipantStartTime_Call struct {
	*mock.Call
}

// UpdateContestParticipantStartTime is a helper method to define mock.On call
//   - ctx
//   - contestID
func (_e *MockMutationResolver_Expecter) UpdateContestParticipantStartTime(ctx interface{}, contestID interface{}) *MockMutationResolver_UpdateContestParticipantStartTime_Call {
	return &MockMutationResolver_UpdateContestParticipantStartTime_Call{Call: _e.mock.On("UpdateContestParticipantStartTime", ctx, contestID)}
}

func (_c *MockMutationResolver_UpdateContestParticipantStartTime_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID)) *MockMutationResolver_UpdateContestParticipantStartTime_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_UpdateContestParticipantStartTime_Call) Return(b bool, err error) *MockMutationResolver_UpdateContestParticipantStartTime_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_UpdateContestParticipantStartTime_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID) (bool, error)) *MockMutationResolver_UpdateContestParticipantStartTime_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateLastMessageRead provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UpdateLastMessageRead(ctx context.Context, groupID primitive.ObjectID, lastMessageReadID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, groupID, lastMessageReadID)

	if len(ret) == 0 {
		panic("no return value specified for UpdateLastMessageRead")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, groupID, lastMessageReadID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, groupID, lastMessageReadID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, groupID, lastMessageReadID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UpdateLastMessageRead_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateLastMessageRead'
type MockMutationResolver_UpdateLastMessageRead_Call struct {
	*mock.Call
}

// UpdateLastMessageRead is a helper method to define mock.On call
//   - ctx
//   - groupID
//   - lastMessageReadID
func (_e *MockMutationResolver_Expecter) UpdateLastMessageRead(ctx interface{}, groupID interface{}, lastMessageReadID interface{}) *MockMutationResolver_UpdateLastMessageRead_Call {
	return &MockMutationResolver_UpdateLastMessageRead_Call{Call: _e.mock.On("UpdateLastMessageRead", ctx, groupID, lastMessageReadID)}
}

func (_c *MockMutationResolver_UpdateLastMessageRead_Call) Run(run func(ctx context.Context, groupID primitive.ObjectID, lastMessageReadID primitive.ObjectID)) *MockMutationResolver_UpdateLastMessageRead_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_UpdateLastMessageRead_Call) Return(b bool, err error) *MockMutationResolver_UpdateLastMessageRead_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_UpdateLastMessageRead_Call) RunAndReturn(run func(ctx context.Context, groupID primitive.ObjectID, lastMessageReadID primitive.ObjectID) (bool, error)) *MockMutationResolver_UpdateLastMessageRead_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateLastReadFeedID provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UpdateLastReadFeedID(ctx context.Context, lastReadFeedID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, lastReadFeedID)

	if len(ret) == 0 {
		panic("no return value specified for UpdateLastReadFeedID")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, lastReadFeedID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, lastReadFeedID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, lastReadFeedID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UpdateLastReadFeedID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateLastReadFeedID'
type MockMutationResolver_UpdateLastReadFeedID_Call struct {
	*mock.Call
}

// UpdateLastReadFeedID is a helper method to define mock.On call
//   - ctx
//   - lastReadFeedID
func (_e *MockMutationResolver_Expecter) UpdateLastReadFeedID(ctx interface{}, lastReadFeedID interface{}) *MockMutationResolver_UpdateLastReadFeedID_Call {
	return &MockMutationResolver_UpdateLastReadFeedID_Call{Call: _e.mock.On("UpdateLastReadFeedID", ctx, lastReadFeedID)}
}

func (_c *MockMutationResolver_UpdateLastReadFeedID_Call) Run(run func(ctx context.Context, lastReadFeedID primitive.ObjectID)) *MockMutationResolver_UpdateLastReadFeedID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_UpdateLastReadFeedID_Call) Return(b bool, err error) *MockMutationResolver_UpdateLastReadFeedID_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_UpdateLastReadFeedID_Call) RunAndReturn(run func(ctx context.Context, lastReadFeedID primitive.ObjectID) (bool, error)) *MockMutationResolver_UpdateLastReadFeedID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateLikeStatus provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UpdateLikeStatus(ctx context.Context, feedID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, feedID)

	if len(ret) == 0 {
		panic("no return value specified for UpdateLikeStatus")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, feedID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, feedID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, feedID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UpdateLikeStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateLikeStatus'
type MockMutationResolver_UpdateLikeStatus_Call struct {
	*mock.Call
}

// UpdateLikeStatus is a helper method to define mock.On call
//   - ctx
//   - feedID
func (_e *MockMutationResolver_Expecter) UpdateLikeStatus(ctx interface{}, feedID interface{}) *MockMutationResolver_UpdateLikeStatus_Call {
	return &MockMutationResolver_UpdateLikeStatus_Call{Call: _e.mock.On("UpdateLikeStatus", ctx, feedID)}
}

func (_c *MockMutationResolver_UpdateLikeStatus_Call) Run(run func(ctx context.Context, feedID primitive.ObjectID)) *MockMutationResolver_UpdateLikeStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_UpdateLikeStatus_Call) Return(b bool, err error) *MockMutationResolver_UpdateLikeStatus_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_UpdateLikeStatus_Call) RunAndReturn(run func(ctx context.Context, feedID primitive.ObjectID) (bool, error)) *MockMutationResolver_UpdateLikeStatus_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateMemberRole provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UpdateMemberRole(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID, role models.ClubMemberRole) (*models.ClubMember, error) {
	ret := _mock.Called(ctx, clubID, userID, role)

	if len(ret) == 0 {
		panic("no return value specified for UpdateMemberRole")
	}

	var r0 *models.ClubMember
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID, models.ClubMemberRole) (*models.ClubMember, error)); ok {
		return returnFunc(ctx, clubID, userID, role)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID, models.ClubMemberRole) *models.ClubMember); ok {
		r0 = returnFunc(ctx, clubID, userID, role)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubMember)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID, models.ClubMemberRole) error); ok {
		r1 = returnFunc(ctx, clubID, userID, role)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UpdateMemberRole_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateMemberRole'
type MockMutationResolver_UpdateMemberRole_Call struct {
	*mock.Call
}

// UpdateMemberRole is a helper method to define mock.On call
//   - ctx
//   - clubID
//   - userID
//   - role
func (_e *MockMutationResolver_Expecter) UpdateMemberRole(ctx interface{}, clubID interface{}, userID interface{}, role interface{}) *MockMutationResolver_UpdateMemberRole_Call {
	return &MockMutationResolver_UpdateMemberRole_Call{Call: _e.mock.On("UpdateMemberRole", ctx, clubID, userID, role)}
}

func (_c *MockMutationResolver_UpdateMemberRole_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID, role models.ClubMemberRole)) *MockMutationResolver_UpdateMemberRole_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID), args[3].(models.ClubMemberRole))
	})
	return _c
}

func (_c *MockMutationResolver_UpdateMemberRole_Call) Return(clubMember *models.ClubMember, err error) *MockMutationResolver_UpdateMemberRole_Call {
	_c.Call.Return(clubMember, err)
	return _c
}

func (_c *MockMutationResolver_UpdateMemberRole_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID, role models.ClubMemberRole) (*models.ClubMember, error)) *MockMutationResolver_UpdateMemberRole_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateRatingBasedOnFixtureResponse provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UpdateRatingBasedOnFixtureResponse(ctx context.Context, userStance models.UserStance) (*models.User, error) {
	ret := _mock.Called(ctx, userStance)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRatingBasedOnFixtureResponse")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.UserStance) (*models.User, error)); ok {
		return returnFunc(ctx, userStance)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.UserStance) *models.User); ok {
		r0 = returnFunc(ctx, userStance)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.UserStance) error); ok {
		r1 = returnFunc(ctx, userStance)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UpdateRatingBasedOnFixtureResponse_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRatingBasedOnFixtureResponse'
type MockMutationResolver_UpdateRatingBasedOnFixtureResponse_Call struct {
	*mock.Call
}

// UpdateRatingBasedOnFixtureResponse is a helper method to define mock.On call
//   - ctx
//   - userStance
func (_e *MockMutationResolver_Expecter) UpdateRatingBasedOnFixtureResponse(ctx interface{}, userStance interface{}) *MockMutationResolver_UpdateRatingBasedOnFixtureResponse_Call {
	return &MockMutationResolver_UpdateRatingBasedOnFixtureResponse_Call{Call: _e.mock.On("UpdateRatingBasedOnFixtureResponse", ctx, userStance)}
}

func (_c *MockMutationResolver_UpdateRatingBasedOnFixtureResponse_Call) Run(run func(ctx context.Context, userStance models.UserStance)) *MockMutationResolver_UpdateRatingBasedOnFixtureResponse_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.UserStance))
	})
	return _c
}

func (_c *MockMutationResolver_UpdateRatingBasedOnFixtureResponse_Call) Return(user *models.User, err error) *MockMutationResolver_UpdateRatingBasedOnFixtureResponse_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockMutationResolver_UpdateRatingBasedOnFixtureResponse_Call) RunAndReturn(run func(ctx context.Context, userStance models.UserStance) (*models.User, error)) *MockMutationResolver_UpdateRatingBasedOnFixtureResponse_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUser provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UpdateUser(ctx context.Context, updateUserInput *models.UpdateUserInput) (*models.User, error) {
	ret := _mock.Called(ctx, updateUserInput)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUser")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UpdateUserInput) (*models.User, error)); ok {
		return returnFunc(ctx, updateUserInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UpdateUserInput) *models.User); ok {
		r0 = returnFunc(ctx, updateUserInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.UpdateUserInput) error); ok {
		r1 = returnFunc(ctx, updateUserInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UpdateUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUser'
type MockMutationResolver_UpdateUser_Call struct {
	*mock.Call
}

// UpdateUser is a helper method to define mock.On call
//   - ctx
//   - updateUserInput
func (_e *MockMutationResolver_Expecter) UpdateUser(ctx interface{}, updateUserInput interface{}) *MockMutationResolver_UpdateUser_Call {
	return &MockMutationResolver_UpdateUser_Call{Call: _e.mock.On("UpdateUser", ctx, updateUserInput)}
}

func (_c *MockMutationResolver_UpdateUser_Call) Run(run func(ctx context.Context, updateUserInput *models.UpdateUserInput)) *MockMutationResolver_UpdateUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.UpdateUserInput))
	})
	return _c
}

func (_c *MockMutationResolver_UpdateUser_Call) Return(user *models.User, err error) *MockMutationResolver_UpdateUser_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockMutationResolver_UpdateUser_Call) RunAndReturn(run func(ctx context.Context, updateUserInput *models.UpdateUserInput) (*models.User, error)) *MockMutationResolver_UpdateUser_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserSettings provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UpdateUserSettings(ctx context.Context, settings *models.UpdateSettingsInput) (*models.UserSettings, error) {
	ret := _mock.Called(ctx, settings)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserSettings")
	}

	var r0 *models.UserSettings
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UpdateSettingsInput) (*models.UserSettings, error)); ok {
		return returnFunc(ctx, settings)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UpdateSettingsInput) *models.UserSettings); ok {
		r0 = returnFunc(ctx, settings)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserSettings)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.UpdateSettingsInput) error); ok {
		r1 = returnFunc(ctx, settings)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UpdateUserSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserSettings'
type MockMutationResolver_UpdateUserSettings_Call struct {
	*mock.Call
}

// UpdateUserSettings is a helper method to define mock.On call
//   - ctx
//   - settings
func (_e *MockMutationResolver_Expecter) UpdateUserSettings(ctx interface{}, settings interface{}) *MockMutationResolver_UpdateUserSettings_Call {
	return &MockMutationResolver_UpdateUserSettings_Call{Call: _e.mock.On("UpdateUserSettings", ctx, settings)}
}

func (_c *MockMutationResolver_UpdateUserSettings_Call) Run(run func(ctx context.Context, settings *models.UpdateSettingsInput)) *MockMutationResolver_UpdateUserSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.UpdateSettingsInput))
	})
	return _c
}

func (_c *MockMutationResolver_UpdateUserSettings_Call) Return(userSettings *models.UserSettings, err error) *MockMutationResolver_UpdateUserSettings_Call {
	_c.Call.Return(userSettings, err)
	return _c
}

func (_c *MockMutationResolver_UpdateUserSettings_Call) RunAndReturn(run func(ctx context.Context, settings *models.UpdateSettingsInput) (*models.UserSettings, error)) *MockMutationResolver_UpdateUserSettings_Call {
	_c.Call.Return(run)
	return _c
}

// UploadClubBannerImage provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UploadClubBannerImage(ctx context.Context, file graphql.Upload, clubID primitive.ObjectID) (*models.File, error) {
	ret := _mock.Called(ctx, file, clubID)

	if len(ret) == 0 {
		panic("no return value specified for UploadClubBannerImage")
	}

	var r0 *models.File
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, graphql.Upload, primitive.ObjectID) (*models.File, error)); ok {
		return returnFunc(ctx, file, clubID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, graphql.Upload, primitive.ObjectID) *models.File); ok {
		r0 = returnFunc(ctx, file, clubID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.File)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, graphql.Upload, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, file, clubID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UploadClubBannerImage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadClubBannerImage'
type MockMutationResolver_UploadClubBannerImage_Call struct {
	*mock.Call
}

// UploadClubBannerImage is a helper method to define mock.On call
//   - ctx
//   - file
//   - clubID
func (_e *MockMutationResolver_Expecter) UploadClubBannerImage(ctx interface{}, file interface{}, clubID interface{}) *MockMutationResolver_UploadClubBannerImage_Call {
	return &MockMutationResolver_UploadClubBannerImage_Call{Call: _e.mock.On("UploadClubBannerImage", ctx, file, clubID)}
}

func (_c *MockMutationResolver_UploadClubBannerImage_Call) Run(run func(ctx context.Context, file graphql.Upload, clubID primitive.ObjectID)) *MockMutationResolver_UploadClubBannerImage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(graphql.Upload), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_UploadClubBannerImage_Call) Return(file1 *models.File, err error) *MockMutationResolver_UploadClubBannerImage_Call {
	_c.Call.Return(file1, err)
	return _c
}

func (_c *MockMutationResolver_UploadClubBannerImage_Call) RunAndReturn(run func(ctx context.Context, file graphql.Upload, clubID primitive.ObjectID) (*models.File, error)) *MockMutationResolver_UploadClubBannerImage_Call {
	_c.Call.Return(run)
	return _c
}

// UploadClubLogoImage provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UploadClubLogoImage(ctx context.Context, file graphql.Upload, clubID primitive.ObjectID) (*models.File, error) {
	ret := _mock.Called(ctx, file, clubID)

	if len(ret) == 0 {
		panic("no return value specified for UploadClubLogoImage")
	}

	var r0 *models.File
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, graphql.Upload, primitive.ObjectID) (*models.File, error)); ok {
		return returnFunc(ctx, file, clubID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, graphql.Upload, primitive.ObjectID) *models.File); ok {
		r0 = returnFunc(ctx, file, clubID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.File)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, graphql.Upload, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, file, clubID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UploadClubLogoImage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadClubLogoImage'
type MockMutationResolver_UploadClubLogoImage_Call struct {
	*mock.Call
}

// UploadClubLogoImage is a helper method to define mock.On call
//   - ctx
//   - file
//   - clubID
func (_e *MockMutationResolver_Expecter) UploadClubLogoImage(ctx interface{}, file interface{}, clubID interface{}) *MockMutationResolver_UploadClubLogoImage_Call {
	return &MockMutationResolver_UploadClubLogoImage_Call{Call: _e.mock.On("UploadClubLogoImage", ctx, file, clubID)}
}

func (_c *MockMutationResolver_UploadClubLogoImage_Call) Run(run func(ctx context.Context, file graphql.Upload, clubID primitive.ObjectID)) *MockMutationResolver_UploadClubLogoImage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(graphql.Upload), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMutationResolver_UploadClubLogoImage_Call) Return(file1 *models.File, err error) *MockMutationResolver_UploadClubLogoImage_Call {
	_c.Call.Return(file1, err)
	return _c
}

func (_c *MockMutationResolver_UploadClubLogoImage_Call) RunAndReturn(run func(ctx context.Context, file graphql.Upload, clubID primitive.ObjectID) (*models.File, error)) *MockMutationResolver_UploadClubLogoImage_Call {
	_c.Call.Return(run)
	return _c
}

// UploadFiles provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UploadFiles(ctx context.Context, files []*graphql.Upload) ([]*models.File, error) {
	ret := _mock.Called(ctx, files)

	if len(ret) == 0 {
		panic("no return value specified for UploadFiles")
	}

	var r0 []*models.File
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*graphql.Upload) ([]*models.File, error)); ok {
		return returnFunc(ctx, files)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*graphql.Upload) []*models.File); ok {
		r0 = returnFunc(ctx, files)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.File)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []*graphql.Upload) error); ok {
		r1 = returnFunc(ctx, files)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UploadFiles_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadFiles'
type MockMutationResolver_UploadFiles_Call struct {
	*mock.Call
}

// UploadFiles is a helper method to define mock.On call
//   - ctx
//   - files
func (_e *MockMutationResolver_Expecter) UploadFiles(ctx interface{}, files interface{}) *MockMutationResolver_UploadFiles_Call {
	return &MockMutationResolver_UploadFiles_Call{Call: _e.mock.On("UploadFiles", ctx, files)}
}

func (_c *MockMutationResolver_UploadFiles_Call) Run(run func(ctx context.Context, files []*graphql.Upload)) *MockMutationResolver_UploadFiles_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*graphql.Upload))
	})
	return _c
}

func (_c *MockMutationResolver_UploadFiles_Call) Return(files1 []*models.File, err error) *MockMutationResolver_UploadFiles_Call {
	_c.Call.Return(files1, err)
	return _c
}

func (_c *MockMutationResolver_UploadFiles_Call) RunAndReturn(run func(ctx context.Context, files []*graphql.Upload) ([]*models.File, error)) *MockMutationResolver_UploadFiles_Call {
	_c.Call.Return(run)
	return _c
}

// UploadProfilePicture provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UploadProfilePicture(ctx context.Context, file graphql.Upload) (*models.File, error) {
	ret := _mock.Called(ctx, file)

	if len(ret) == 0 {
		panic("no return value specified for UploadProfilePicture")
	}

	var r0 *models.File
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, graphql.Upload) (*models.File, error)); ok {
		return returnFunc(ctx, file)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, graphql.Upload) *models.File); ok {
		r0 = returnFunc(ctx, file)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.File)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, graphql.Upload) error); ok {
		r1 = returnFunc(ctx, file)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UploadProfilePicture_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadProfilePicture'
type MockMutationResolver_UploadProfilePicture_Call struct {
	*mock.Call
}

// UploadProfilePicture is a helper method to define mock.On call
//   - ctx
//   - file
func (_e *MockMutationResolver_Expecter) UploadProfilePicture(ctx interface{}, file interface{}) *MockMutationResolver_UploadProfilePicture_Call {
	return &MockMutationResolver_UploadProfilePicture_Call{Call: _e.mock.On("UploadProfilePicture", ctx, file)}
}

func (_c *MockMutationResolver_UploadProfilePicture_Call) Run(run func(ctx context.Context, file graphql.Upload)) *MockMutationResolver_UploadProfilePicture_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(graphql.Upload))
	})
	return _c
}

func (_c *MockMutationResolver_UploadProfilePicture_Call) Return(file1 *models.File, err error) *MockMutationResolver_UploadProfilePicture_Call {
	_c.Call.Return(file1, err)
	return _c
}

func (_c *MockMutationResolver_UploadProfilePicture_Call) RunAndReturn(run func(ctx context.Context, file graphql.Upload) (*models.File, error)) *MockMutationResolver_UploadProfilePicture_Call {
	_c.Call.Return(run)
	return _c
}

// UseStreakFreezer provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) UseStreakFreezer(ctx context.Context) (bool, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for UseStreakFreezer")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (bool, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) bool); ok {
		r0 = returnFunc(ctx)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_UseStreakFreezer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UseStreakFreezer'
type MockMutationResolver_UseStreakFreezer_Call struct {
	*mock.Call
}

// UseStreakFreezer is a helper method to define mock.On call
//   - ctx
func (_e *MockMutationResolver_Expecter) UseStreakFreezer(ctx interface{}) *MockMutationResolver_UseStreakFreezer_Call {
	return &MockMutationResolver_UseStreakFreezer_Call{Call: _e.mock.On("UseStreakFreezer", ctx)}
}

func (_c *MockMutationResolver_UseStreakFreezer_Call) Run(run func(ctx context.Context)) *MockMutationResolver_UseStreakFreezer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockMutationResolver_UseStreakFreezer_Call) Return(b bool, err error) *MockMutationResolver_UseStreakFreezer_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_UseStreakFreezer_Call) RunAndReturn(run func(ctx context.Context) (bool, error)) *MockMutationResolver_UseStreakFreezer_Call {
	_c.Call.Return(run)
	return _c
}

// VerifyOtp provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) VerifyOtp(ctx context.Context, otp string) (bool, error) {
	ret := _mock.Called(ctx, otp)

	if len(ret) == 0 {
		panic("no return value specified for VerifyOtp")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return returnFunc(ctx, otp)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = returnFunc(ctx, otp)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, otp)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_VerifyOtp_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'VerifyOtp'
type MockMutationResolver_VerifyOtp_Call struct {
	*mock.Call
}

// VerifyOtp is a helper method to define mock.On call
//   - ctx
//   - otp
func (_e *MockMutationResolver_Expecter) VerifyOtp(ctx interface{}, otp interface{}) *MockMutationResolver_VerifyOtp_Call {
	return &MockMutationResolver_VerifyOtp_Call{Call: _e.mock.On("VerifyOtp", ctx, otp)}
}

func (_c *MockMutationResolver_VerifyOtp_Call) Run(run func(ctx context.Context, otp string)) *MockMutationResolver_VerifyOtp_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockMutationResolver_VerifyOtp_Call) Return(b bool, err error) *MockMutationResolver_VerifyOtp_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_VerifyOtp_Call) RunAndReturn(run func(ctx context.Context, otp string) (bool, error)) *MockMutationResolver_VerifyOtp_Call {
	_c.Call.Return(run)
	return _c
}

// WithdrawFriendRequest provides a mock function for the type MockMutationResolver
func (_mock *MockMutationResolver) WithdrawFriendRequest(ctx context.Context, withdrawFriendRequestInput *models.WithdrawFriendRequestInput) (bool, error) {
	ret := _mock.Called(ctx, withdrawFriendRequestInput)

	if len(ret) == 0 {
		panic("no return value specified for WithdrawFriendRequest")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.WithdrawFriendRequestInput) (bool, error)); ok {
		return returnFunc(ctx, withdrawFriendRequestInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.WithdrawFriendRequestInput) bool); ok {
		r0 = returnFunc(ctx, withdrawFriendRequestInput)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.WithdrawFriendRequestInput) error); ok {
		r1 = returnFunc(ctx, withdrawFriendRequestInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMutationResolver_WithdrawFriendRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WithdrawFriendRequest'
type MockMutationResolver_WithdrawFriendRequest_Call struct {
	*mock.Call
}

// WithdrawFriendRequest is a helper method to define mock.On call
//   - ctx
//   - withdrawFriendRequestInput
func (_e *MockMutationResolver_Expecter) WithdrawFriendRequest(ctx interface{}, withdrawFriendRequestInput interface{}) *MockMutationResolver_WithdrawFriendRequest_Call {
	return &MockMutationResolver_WithdrawFriendRequest_Call{Call: _e.mock.On("WithdrawFriendRequest", ctx, withdrawFriendRequestInput)}
}

func (_c *MockMutationResolver_WithdrawFriendRequest_Call) Run(run func(ctx context.Context, withdrawFriendRequestInput *models.WithdrawFriendRequestInput)) *MockMutationResolver_WithdrawFriendRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.WithdrawFriendRequestInput))
	})
	return _c
}

func (_c *MockMutationResolver_WithdrawFriendRequest_Call) Return(b bool, err error) *MockMutationResolver_WithdrawFriendRequest_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMutationResolver_WithdrawFriendRequest_Call) RunAndReturn(run func(ctx context.Context, withdrawFriendRequestInput *models.WithdrawFriendRequestInput) (bool, error)) *MockMutationResolver_WithdrawFriendRequest_Call {
	_c.Call.Return(run)
	return _c
}
