// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package generated

import (
	"matiksOfficial/matiks-server-go/internal/graph/generated"

	mock "github.com/stretchr/testify/mock"
)

// NewMockResolverRoot creates a new instance of MockResolverRoot. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockResolverRoot(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockResolverRoot {
	mock := &MockResolverRoot{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockResolverRoot is an autogenerated mock type for the ResolverRoot type
type MockResolverRoot struct {
	mock.Mock
}

type MockResolverRoot_Expecter struct {
	mock *mock.Mock
}

func (_m *MockResolverRoot) EXPECT() *MockResolverRoot_Expecter {
	return &MockResolverRoot_Expecter{mock: &_m.Mock}
}

// Mutation provides a mock function for the type MockResolverRoot
func (_mock *MockResolverRoot) Mutation() generated.MutationResolver {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Mutation")
	}

	var r0 generated.MutationResolver
	if returnFunc, ok := ret.Get(0).(func() generated.MutationResolver); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(generated.MutationResolver)
		}
	}
	return r0
}

// MockResolverRoot_Mutation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Mutation'
type MockResolverRoot_Mutation_Call struct {
	*mock.Call
}

// Mutation is a helper method to define mock.On call
func (_e *MockResolverRoot_Expecter) Mutation() *MockResolverRoot_Mutation_Call {
	return &MockResolverRoot_Mutation_Call{Call: _e.mock.On("Mutation")}
}

func (_c *MockResolverRoot_Mutation_Call) Run(run func()) *MockResolverRoot_Mutation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockResolverRoot_Mutation_Call) Return(mutationResolver generated.MutationResolver) *MockResolverRoot_Mutation_Call {
	_c.Call.Return(mutationResolver)
	return _c
}

func (_c *MockResolverRoot_Mutation_Call) RunAndReturn(run func() generated.MutationResolver) *MockResolverRoot_Mutation_Call {
	_c.Call.Return(run)
	return _c
}

// Query provides a mock function for the type MockResolverRoot
func (_mock *MockResolverRoot) Query() generated.QueryResolver {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Query")
	}

	var r0 generated.QueryResolver
	if returnFunc, ok := ret.Get(0).(func() generated.QueryResolver); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(generated.QueryResolver)
		}
	}
	return r0
}

// MockResolverRoot_Query_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Query'
type MockResolverRoot_Query_Call struct {
	*mock.Call
}

// Query is a helper method to define mock.On call
func (_e *MockResolverRoot_Expecter) Query() *MockResolverRoot_Query_Call {
	return &MockResolverRoot_Query_Call{Call: _e.mock.On("Query")}
}

func (_c *MockResolverRoot_Query_Call) Run(run func()) *MockResolverRoot_Query_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockResolverRoot_Query_Call) Return(queryResolver generated.QueryResolver) *MockResolverRoot_Query_Call {
	_c.Call.Return(queryResolver)
	return _c
}

func (_c *MockResolverRoot_Query_Call) RunAndReturn(run func() generated.QueryResolver) *MockResolverRoot_Query_Call {
	_c.Call.Return(run)
	return _c
}

// Subscription provides a mock function for the type MockResolverRoot
func (_mock *MockResolverRoot) Subscription() generated.SubscriptionResolver {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Subscription")
	}

	var r0 generated.SubscriptionResolver
	if returnFunc, ok := ret.Get(0).(func() generated.SubscriptionResolver); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(generated.SubscriptionResolver)
		}
	}
	return r0
}

// MockResolverRoot_Subscription_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Subscription'
type MockResolverRoot_Subscription_Call struct {
	*mock.Call
}

// Subscription is a helper method to define mock.On call
func (_e *MockResolverRoot_Expecter) Subscription() *MockResolverRoot_Subscription_Call {
	return &MockResolverRoot_Subscription_Call{Call: _e.mock.On("Subscription")}
}

func (_c *MockResolverRoot_Subscription_Call) Run(run func()) *MockResolverRoot_Subscription_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockResolverRoot_Subscription_Call) Return(subscriptionResolver generated.SubscriptionResolver) *MockResolverRoot_Subscription_Call {
	_c.Call.Return(subscriptionResolver)
	return _c
}

func (_c *MockResolverRoot_Subscription_Call) RunAndReturn(run func() generated.SubscriptionResolver) *MockResolverRoot_Subscription_Call {
	_c.Call.Return(run)
	return _c
}
