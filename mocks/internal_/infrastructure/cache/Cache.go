// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package cache

import (
	"context"
	"time"

	"github.com/redis/go-redis/v9"
	mock "github.com/stretchr/testify/mock"
)

// NewMockCache creates a new instance of MockCache. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockCache(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockCache {
	mock := &MockCache{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockCache is an autogenerated mock type for the Cache type
type MockCache struct {
	mock.Mock
}

type MockCache_Expecter struct {
	mock *mock.Mock
}

func (_m *MockCache) EXPECT() *MockCache_Expecter {
	return &MockCache_Expecter{mock: &_m.Mock}
}

// ClearAll provides a mock function for the type MockCache
func (_mock *MockCache) ClearAll(ctx context.Context) error {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ClearAll")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = returnFunc(ctx)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockCache_ClearAll_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ClearAll'
type MockCache_ClearAll_Call struct {
	*mock.Call
}

// ClearAll is a helper method to define mock.On call
//   - ctx
func (_e *MockCache_Expecter) ClearAll(ctx interface{}) *MockCache_ClearAll_Call {
	return &MockCache_ClearAll_Call{Call: _e.mock.On("ClearAll", ctx)}
}

func (_c *MockCache_ClearAll_Call) Run(run func(ctx context.Context)) *MockCache_ClearAll_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockCache_ClearAll_Call) Return(err error) *MockCache_ClearAll_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockCache_ClearAll_Call) RunAndReturn(run func(ctx context.Context) error) *MockCache_ClearAll_Call {
	_c.Call.Return(run)
	return _c
}

// Decr provides a mock function for the type MockCache
func (_mock *MockCache) Decr(ctx context.Context, key string) (int64, error) {
	ret := _mock.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for Decr")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (int64, error)); ok {
		return returnFunc(ctx, key)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) int64); ok {
		r0 = returnFunc(ctx, key)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, key)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCache_Decr_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Decr'
type MockCache_Decr_Call struct {
	*mock.Call
}

// Decr is a helper method to define mock.On call
//   - ctx
//   - key
func (_e *MockCache_Expecter) Decr(ctx interface{}, key interface{}) *MockCache_Decr_Call {
	return &MockCache_Decr_Call{Call: _e.mock.On("Decr", ctx, key)}
}

func (_c *MockCache_Decr_Call) Run(run func(ctx context.Context, key string)) *MockCache_Decr_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockCache_Decr_Call) Return(n int64, err error) *MockCache_Decr_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockCache_Decr_Call) RunAndReturn(run func(ctx context.Context, key string) (int64, error)) *MockCache_Decr_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function for the type MockCache
func (_mock *MockCache) Delete(ctx context.Context, key string) error {
	ret := _mock.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = returnFunc(ctx, key)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockCache_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockCache_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx
//   - key
func (_e *MockCache_Expecter) Delete(ctx interface{}, key interface{}) *MockCache_Delete_Call {
	return &MockCache_Delete_Call{Call: _e.mock.On("Delete", ctx, key)}
}

func (_c *MockCache_Delete_Call) Run(run func(ctx context.Context, key string)) *MockCache_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockCache_Delete_Call) Return(err error) *MockCache_Delete_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockCache_Delete_Call) RunAndReturn(run func(ctx context.Context, key string) error) *MockCache_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAll provides a mock function for the type MockCache
func (_mock *MockCache) DeleteAll(ctx context.Context, pattern string) error {
	ret := _mock.Called(ctx, pattern)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAll")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = returnFunc(ctx, pattern)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockCache_DeleteAll_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAll'
type MockCache_DeleteAll_Call struct {
	*mock.Call
}

// DeleteAll is a helper method to define mock.On call
//   - ctx
//   - pattern
func (_e *MockCache_Expecter) DeleteAll(ctx interface{}, pattern interface{}) *MockCache_DeleteAll_Call {
	return &MockCache_DeleteAll_Call{Call: _e.mock.On("DeleteAll", ctx, pattern)}
}

func (_c *MockCache_DeleteAll_Call) Run(run func(ctx context.Context, pattern string)) *MockCache_DeleteAll_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockCache_DeleteAll_Call) Return(err error) *MockCache_DeleteAll_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockCache_DeleteAll_Call) RunAndReturn(run func(ctx context.Context, pattern string) error) *MockCache_DeleteAll_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteMany provides a mock function for the type MockCache
func (_mock *MockCache) DeleteMany(ctx context.Context, keys ...string) error {
	var tmpRet mock.Arguments
	if len(keys) > 0 {
		tmpRet = _mock.Called(ctx, keys)
	} else {
		tmpRet = _mock.Called(ctx)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for DeleteMany")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, ...string) error); ok {
		r0 = returnFunc(ctx, keys...)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockCache_DeleteMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteMany'
type MockCache_DeleteMany_Call struct {
	*mock.Call
}

// DeleteMany is a helper method to define mock.On call
//   - ctx
//   - keys
func (_e *MockCache_Expecter) DeleteMany(ctx interface{}, keys ...interface{}) *MockCache_DeleteMany_Call {
	return &MockCache_DeleteMany_Call{Call: _e.mock.On("DeleteMany",
		append([]interface{}{ctx}, keys...)...)}
}

func (_c *MockCache_DeleteMany_Call) Run(run func(ctx context.Context, keys ...string)) *MockCache_DeleteMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[1].([]string)
		run(args[0].(context.Context), variadicArgs...)
	})
	return _c
}

func (_c *MockCache_DeleteMany_Call) Return(err error) *MockCache_DeleteMany_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockCache_DeleteMany_Call) RunAndReturn(run func(ctx context.Context, keys ...string) error) *MockCache_DeleteMany_Call {
	_c.Call.Return(run)
	return _c
}

// Expire provides a mock function for the type MockCache
func (_mock *MockCache) Expire(ctx context.Context, key string, expiration time.Duration) error {
	ret := _mock.Called(ctx, key, expiration)

	if len(ret) == 0 {
		panic("no return value specified for Expire")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, time.Duration) error); ok {
		r0 = returnFunc(ctx, key, expiration)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockCache_Expire_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Expire'
type MockCache_Expire_Call struct {
	*mock.Call
}

// Expire is a helper method to define mock.On call
//   - ctx
//   - key
//   - expiration
func (_e *MockCache_Expecter) Expire(ctx interface{}, key interface{}, expiration interface{}) *MockCache_Expire_Call {
	return &MockCache_Expire_Call{Call: _e.mock.On("Expire", ctx, key, expiration)}
}

func (_c *MockCache_Expire_Call) Run(run func(ctx context.Context, key string, expiration time.Duration)) *MockCache_Expire_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(time.Duration))
	})
	return _c
}

func (_c *MockCache_Expire_Call) Return(err error) *MockCache_Expire_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockCache_Expire_Call) RunAndReturn(run func(ctx context.Context, key string, expiration time.Duration) error) *MockCache_Expire_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function for the type MockCache
func (_mock *MockCache) Get(ctx context.Context, key string) ([]byte, error) {
	ret := _mock.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 []byte
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) ([]byte, error)); ok {
		return returnFunc(ctx, key)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) []byte); ok {
		r0 = returnFunc(ctx, key)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, key)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCache_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type MockCache_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - ctx
//   - key
func (_e *MockCache_Expecter) Get(ctx interface{}, key interface{}) *MockCache_Get_Call {
	return &MockCache_Get_Call{Call: _e.mock.On("Get", ctx, key)}
}

func (_c *MockCache_Get_Call) Run(run func(ctx context.Context, key string)) *MockCache_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockCache_Get_Call) Return(bytes []byte, err error) *MockCache_Get_Call {
	_c.Call.Return(bytes, err)
	return _c
}

func (_c *MockCache_Get_Call) RunAndReturn(run func(ctx context.Context, key string) ([]byte, error)) *MockCache_Get_Call {
	_c.Call.Return(run)
	return _c
}

// GetAll provides a mock function for the type MockCache
func (_mock *MockCache) GetAll(ctx context.Context, pattern string) (map[string][]byte, error) {
	ret := _mock.Called(ctx, pattern)

	if len(ret) == 0 {
		panic("no return value specified for GetAll")
	}

	var r0 map[string][]byte
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (map[string][]byte, error)); ok {
		return returnFunc(ctx, pattern)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) map[string][]byte); ok {
		r0 = returnFunc(ctx, pattern)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string][]byte)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, pattern)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCache_GetAll_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAll'
type MockCache_GetAll_Call struct {
	*mock.Call
}

// GetAll is a helper method to define mock.On call
//   - ctx
//   - pattern
func (_e *MockCache_Expecter) GetAll(ctx interface{}, pattern interface{}) *MockCache_GetAll_Call {
	return &MockCache_GetAll_Call{Call: _e.mock.On("GetAll", ctx, pattern)}
}

func (_c *MockCache_GetAll_Call) Run(run func(ctx context.Context, pattern string)) *MockCache_GetAll_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockCache_GetAll_Call) Return(stringToBytes map[string][]byte, err error) *MockCache_GetAll_Call {
	_c.Call.Return(stringToBytes, err)
	return _c
}

func (_c *MockCache_GetAll_Call) RunAndReturn(run func(ctx context.Context, pattern string) (map[string][]byte, error)) *MockCache_GetAll_Call {
	_c.Call.Return(run)
	return _c
}

// HGetAll provides a mock function for the type MockCache
func (_mock *MockCache) HGetAll(ctx context.Context, key string) (map[string]string, error) {
	ret := _mock.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for HGetAll")
	}

	var r0 map[string]string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (map[string]string, error)); ok {
		return returnFunc(ctx, key)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) map[string]string); ok {
		r0 = returnFunc(ctx, key)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]string)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, key)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCache_HGetAll_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HGetAll'
type MockCache_HGetAll_Call struct {
	*mock.Call
}

// HGetAll is a helper method to define mock.On call
//   - ctx
//   - key
func (_e *MockCache_Expecter) HGetAll(ctx interface{}, key interface{}) *MockCache_HGetAll_Call {
	return &MockCache_HGetAll_Call{Call: _e.mock.On("HGetAll", ctx, key)}
}

func (_c *MockCache_HGetAll_Call) Run(run func(ctx context.Context, key string)) *MockCache_HGetAll_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockCache_HGetAll_Call) Return(stringToString map[string]string, err error) *MockCache_HGetAll_Call {
	_c.Call.Return(stringToString, err)
	return _c
}

func (_c *MockCache_HGetAll_Call) RunAndReturn(run func(ctx context.Context, key string) (map[string]string, error)) *MockCache_HGetAll_Call {
	_c.Call.Return(run)
	return _c
}

// HSet provides a mock function for the type MockCache
func (_mock *MockCache) HSet(ctx context.Context, key string, field string, value interface{}) error {
	ret := _mock.Called(ctx, key, field, value)

	if len(ret) == 0 {
		panic("no return value specified for HSet")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, interface{}) error); ok {
		r0 = returnFunc(ctx, key, field, value)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockCache_HSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HSet'
type MockCache_HSet_Call struct {
	*mock.Call
}

// HSet is a helper method to define mock.On call
//   - ctx
//   - key
//   - field
//   - value
func (_e *MockCache_Expecter) HSet(ctx interface{}, key interface{}, field interface{}, value interface{}) *MockCache_HSet_Call {
	return &MockCache_HSet_Call{Call: _e.mock.On("HSet", ctx, key, field, value)}
}

func (_c *MockCache_HSet_Call) Run(run func(ctx context.Context, key string, field string, value interface{})) *MockCache_HSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(interface{}))
	})
	return _c
}

func (_c *MockCache_HSet_Call) Return(err error) *MockCache_HSet_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockCache_HSet_Call) RunAndReturn(run func(ctx context.Context, key string, field string, value interface{}) error) *MockCache_HSet_Call {
	_c.Call.Return(run)
	return _c
}

// Incr provides a mock function for the type MockCache
func (_mock *MockCache) Incr(ctx context.Context, key string) (int64, error) {
	ret := _mock.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for Incr")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (int64, error)); ok {
		return returnFunc(ctx, key)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) int64); ok {
		r0 = returnFunc(ctx, key)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, key)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCache_Incr_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Incr'
type MockCache_Incr_Call struct {
	*mock.Call
}

// Incr is a helper method to define mock.On call
//   - ctx
//   - key
func (_e *MockCache_Expecter) Incr(ctx interface{}, key interface{}) *MockCache_Incr_Call {
	return &MockCache_Incr_Call{Call: _e.mock.On("Incr", ctx, key)}
}

func (_c *MockCache_Incr_Call) Run(run func(ctx context.Context, key string)) *MockCache_Incr_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockCache_Incr_Call) Return(n int64, err error) *MockCache_Incr_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockCache_Incr_Call) RunAndReturn(run func(ctx context.Context, key string) (int64, error)) *MockCache_Incr_Call {
	_c.Call.Return(run)
	return _c
}

// MGet provides a mock function for the type MockCache
func (_mock *MockCache) MGet(ctx context.Context, keys ...string) ([]interface{}, error) {
	var tmpRet mock.Arguments
	if len(keys) > 0 {
		tmpRet = _mock.Called(ctx, keys)
	} else {
		tmpRet = _mock.Called(ctx)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for MGet")
	}

	var r0 []interface{}
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, ...string) ([]interface{}, error)); ok {
		return returnFunc(ctx, keys...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, ...string) []interface{}); ok {
		r0 = returnFunc(ctx, keys...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]interface{})
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, ...string) error); ok {
		r1 = returnFunc(ctx, keys...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCache_MGet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MGet'
type MockCache_MGet_Call struct {
	*mock.Call
}

// MGet is a helper method to define mock.On call
//   - ctx
//   - keys
func (_e *MockCache_Expecter) MGet(ctx interface{}, keys ...interface{}) *MockCache_MGet_Call {
	return &MockCache_MGet_Call{Call: _e.mock.On("MGet",
		append([]interface{}{ctx}, keys...)...)}
}

func (_c *MockCache_MGet_Call) Run(run func(ctx context.Context, keys ...string)) *MockCache_MGet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[1].([]string)
		run(args[0].(context.Context), variadicArgs...)
	})
	return _c
}

func (_c *MockCache_MGet_Call) Return(ifaceVals []interface{}, err error) *MockCache_MGet_Call {
	_c.Call.Return(ifaceVals, err)
	return _c
}

func (_c *MockCache_MGet_Call) RunAndReturn(run func(ctx context.Context, keys ...string) ([]interface{}, error)) *MockCache_MGet_Call {
	_c.Call.Return(run)
	return _c
}

// Set provides a mock function for the type MockCache
func (_mock *MockCache) Set(ctx context.Context, key string, value []byte, expiration time.Duration) error {
	ret := _mock.Called(ctx, key, value, expiration)

	if len(ret) == 0 {
		panic("no return value specified for Set")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, []byte, time.Duration) error); ok {
		r0 = returnFunc(ctx, key, value, expiration)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockCache_Set_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Set'
type MockCache_Set_Call struct {
	*mock.Call
}

// Set is a helper method to define mock.On call
//   - ctx
//   - key
//   - value
//   - expiration
func (_e *MockCache_Expecter) Set(ctx interface{}, key interface{}, value interface{}, expiration interface{}) *MockCache_Set_Call {
	return &MockCache_Set_Call{Call: _e.mock.On("Set", ctx, key, value, expiration)}
}

func (_c *MockCache_Set_Call) Run(run func(ctx context.Context, key string, value []byte, expiration time.Duration)) *MockCache_Set_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].([]byte), args[3].(time.Duration))
	})
	return _c
}

func (_c *MockCache_Set_Call) Return(err error) *MockCache_Set_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockCache_Set_Call) RunAndReturn(run func(ctx context.Context, key string, value []byte, expiration time.Duration) error) *MockCache_Set_Call {
	_c.Call.Return(run)
	return _c
}

// ZAdd provides a mock function for the type MockCache
func (_mock *MockCache) ZAdd(ctx context.Context, key string, members ...redis.Z) error {
	var tmpRet mock.Arguments
	if len(members) > 0 {
		tmpRet = _mock.Called(ctx, key, members)
	} else {
		tmpRet = _mock.Called(ctx, key)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for ZAdd")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, ...redis.Z) error); ok {
		r0 = returnFunc(ctx, key, members...)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockCache_ZAdd_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZAdd'
type MockCache_ZAdd_Call struct {
	*mock.Call
}

// ZAdd is a helper method to define mock.On call
//   - ctx
//   - key
//   - members
func (_e *MockCache_Expecter) ZAdd(ctx interface{}, key interface{}, members ...interface{}) *MockCache_ZAdd_Call {
	return &MockCache_ZAdd_Call{Call: _e.mock.On("ZAdd",
		append([]interface{}{ctx, key}, members...)...)}
}

func (_c *MockCache_ZAdd_Call) Run(run func(ctx context.Context, key string, members ...redis.Z)) *MockCache_ZAdd_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]redis.Z)
		run(args[0].(context.Context), args[1].(string), variadicArgs...)
	})
	return _c
}

func (_c *MockCache_ZAdd_Call) Return(err error) *MockCache_ZAdd_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockCache_ZAdd_Call) RunAndReturn(run func(ctx context.Context, key string, members ...redis.Z) error) *MockCache_ZAdd_Call {
	_c.Call.Return(run)
	return _c
}

// ZCount provides a mock function for the type MockCache
func (_mock *MockCache) ZCount(ctx context.Context, key string, min string, max string) (int64, error) {
	ret := _mock.Called(ctx, key, min, max)

	if len(ret) == 0 {
		panic("no return value specified for ZCount")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, string) (int64, error)); ok {
		return returnFunc(ctx, key, min, max)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, string) int64); ok {
		r0 = returnFunc(ctx, key, min, max)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string, string) error); ok {
		r1 = returnFunc(ctx, key, min, max)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCache_ZCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZCount'
type MockCache_ZCount_Call struct {
	*mock.Call
}

// ZCount is a helper method to define mock.On call
//   - ctx
//   - key
//   - min
//   - max
func (_e *MockCache_Expecter) ZCount(ctx interface{}, key interface{}, min interface{}, max interface{}) *MockCache_ZCount_Call {
	return &MockCache_ZCount_Call{Call: _e.mock.On("ZCount", ctx, key, min, max)}
}

func (_c *MockCache_ZCount_Call) Run(run func(ctx context.Context, key string, min string, max string)) *MockCache_ZCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockCache_ZCount_Call) Return(n int64, err error) *MockCache_ZCount_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockCache_ZCount_Call) RunAndReturn(run func(ctx context.Context, key string, min string, max string) (int64, error)) *MockCache_ZCount_Call {
	_c.Call.Return(run)
	return _c
}

// ZRangeByScore provides a mock function for the type MockCache
func (_mock *MockCache) ZRangeByScore(ctx context.Context, key string, opt *redis.ZRangeBy) ([]string, error) {
	ret := _mock.Called(ctx, key, opt)

	if len(ret) == 0 {
		panic("no return value specified for ZRangeByScore")
	}

	var r0 []string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *redis.ZRangeBy) ([]string, error)); ok {
		return returnFunc(ctx, key, opt)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *redis.ZRangeBy) []string); ok {
		r0 = returnFunc(ctx, key, opt)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, *redis.ZRangeBy) error); ok {
		r1 = returnFunc(ctx, key, opt)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCache_ZRangeByScore_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZRangeByScore'
type MockCache_ZRangeByScore_Call struct {
	*mock.Call
}

// ZRangeByScore is a helper method to define mock.On call
//   - ctx
//   - key
//   - opt
func (_e *MockCache_Expecter) ZRangeByScore(ctx interface{}, key interface{}, opt interface{}) *MockCache_ZRangeByScore_Call {
	return &MockCache_ZRangeByScore_Call{Call: _e.mock.On("ZRangeByScore", ctx, key, opt)}
}

func (_c *MockCache_ZRangeByScore_Call) Run(run func(ctx context.Context, key string, opt *redis.ZRangeBy)) *MockCache_ZRangeByScore_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*redis.ZRangeBy))
	})
	return _c
}

func (_c *MockCache_ZRangeByScore_Call) Return(strings []string, err error) *MockCache_ZRangeByScore_Call {
	_c.Call.Return(strings, err)
	return _c
}

func (_c *MockCache_ZRangeByScore_Call) RunAndReturn(run func(ctx context.Context, key string, opt *redis.ZRangeBy) ([]string, error)) *MockCache_ZRangeByScore_Call {
	_c.Call.Return(run)
	return _c
}

// ZRem provides a mock function for the type MockCache
func (_mock *MockCache) ZRem(ctx context.Context, key string, entry string) error {
	ret := _mock.Called(ctx, key, entry)

	if len(ret) == 0 {
		panic("no return value specified for ZRem")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = returnFunc(ctx, key, entry)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockCache_ZRem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZRem'
type MockCache_ZRem_Call struct {
	*mock.Call
}

// ZRem is a helper method to define mock.On call
//   - ctx
//   - key
//   - entry
func (_e *MockCache_Expecter) ZRem(ctx interface{}, key interface{}, entry interface{}) *MockCache_ZRem_Call {
	return &MockCache_ZRem_Call{Call: _e.mock.On("ZRem", ctx, key, entry)}
}

func (_c *MockCache_ZRem_Call) Run(run func(ctx context.Context, key string, entry string)) *MockCache_ZRem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockCache_ZRem_Call) Return(err error) *MockCache_ZRem_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockCache_ZRem_Call) RunAndReturn(run func(ctx context.Context, key string, entry string) error) *MockCache_ZRem_Call {
	_c.Call.Return(run)
	return _c
}

// ZRemRangeByScore provides a mock function for the type MockCache
func (_mock *MockCache) ZRemRangeByScore(ctx context.Context, key string, min string, max string) error {
	ret := _mock.Called(ctx, key, min, max)

	if len(ret) == 0 {
		panic("no return value specified for ZRemRangeByScore")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, string) error); ok {
		r0 = returnFunc(ctx, key, min, max)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockCache_ZRemRangeByScore_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZRemRangeByScore'
type MockCache_ZRemRangeByScore_Call struct {
	*mock.Call
}

// ZRemRangeByScore is a helper method to define mock.On call
//   - ctx
//   - key
//   - min
//   - max
func (_e *MockCache_Expecter) ZRemRangeByScore(ctx interface{}, key interface{}, min interface{}, max interface{}) *MockCache_ZRemRangeByScore_Call {
	return &MockCache_ZRemRangeByScore_Call{Call: _e.mock.On("ZRemRangeByScore", ctx, key, min, max)}
}

func (_c *MockCache_ZRemRangeByScore_Call) Run(run func(ctx context.Context, key string, min string, max string)) *MockCache_ZRemRangeByScore_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockCache_ZRemRangeByScore_Call) Return(err error) *MockCache_ZRemRangeByScore_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockCache_ZRemRangeByScore_Call) RunAndReturn(run func(ctx context.Context, key string, min string, max string) error) *MockCache_ZRemRangeByScore_Call {
	_c.Call.Return(run)
	return _c
}

// ZRevRangeWithScores provides a mock function for the type MockCache
func (_mock *MockCache) ZRevRangeWithScores(ctx context.Context, key string, start int64, stop int64) ([]redis.Z, error) {
	ret := _mock.Called(ctx, key, start, stop)

	if len(ret) == 0 {
		panic("no return value specified for ZRevRangeWithScores")
	}

	var r0 []redis.Z
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64, int64) ([]redis.Z, error)); ok {
		return returnFunc(ctx, key, start, stop)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64, int64) []redis.Z); ok {
		r0 = returnFunc(ctx, key, start, stop)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]redis.Z)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, int64, int64) error); ok {
		r1 = returnFunc(ctx, key, start, stop)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCache_ZRevRangeWithScores_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZRevRangeWithScores'
type MockCache_ZRevRangeWithScores_Call struct {
	*mock.Call
}

// ZRevRangeWithScores is a helper method to define mock.On call
//   - ctx
//   - key
//   - start
//   - stop
func (_e *MockCache_Expecter) ZRevRangeWithScores(ctx interface{}, key interface{}, start interface{}, stop interface{}) *MockCache_ZRevRangeWithScores_Call {
	return &MockCache_ZRevRangeWithScores_Call{Call: _e.mock.On("ZRevRangeWithScores", ctx, key, start, stop)}
}

func (_c *MockCache_ZRevRangeWithScores_Call) Run(run func(ctx context.Context, key string, start int64, stop int64)) *MockCache_ZRevRangeWithScores_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int64), args[3].(int64))
	})
	return _c
}

func (_c *MockCache_ZRevRangeWithScores_Call) Return(zs []redis.Z, err error) *MockCache_ZRevRangeWithScores_Call {
	_c.Call.Return(zs, err)
	return _c
}

func (_c *MockCache_ZRevRangeWithScores_Call) RunAndReturn(run func(ctx context.Context, key string, start int64, stop int64) ([]redis.Z, error)) *MockCache_ZRevRangeWithScores_Call {
	_c.Call.Return(run)
	return _c
}

// ZScore provides a mock function for the type MockCache
func (_mock *MockCache) ZScore(ctx context.Context, key string, member string) (float64, error) {
	ret := _mock.Called(ctx, key, member)

	if len(ret) == 0 {
		panic("no return value specified for ZScore")
	}

	var r0 float64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) (float64, error)); ok {
		return returnFunc(ctx, key, member)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) float64); ok {
		r0 = returnFunc(ctx, key, member)
	} else {
		r0 = ret.Get(0).(float64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = returnFunc(ctx, key, member)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCache_ZScore_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZScore'
type MockCache_ZScore_Call struct {
	*mock.Call
}

// ZScore is a helper method to define mock.On call
//   - ctx
//   - key
//   - member
func (_e *MockCache_Expecter) ZScore(ctx interface{}, key interface{}, member interface{}) *MockCache_ZScore_Call {
	return &MockCache_ZScore_Call{Call: _e.mock.On("ZScore", ctx, key, member)}
}

func (_c *MockCache_ZScore_Call) Run(run func(ctx context.Context, key string, member string)) *MockCache_ZScore_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockCache_ZScore_Call) Return(f float64, err error) *MockCache_ZScore_Call {
	_c.Call.Return(f, err)
	return _c
}

func (_c *MockCache_ZScore_Call) RunAndReturn(run func(ctx context.Context, key string, member string) (float64, error)) *MockCache_ZScore_Call {
	_c.Call.Return(run)
	return _c
}
