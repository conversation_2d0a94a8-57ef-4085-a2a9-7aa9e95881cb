// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package cache

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"

	"github.com/redis/go-redis/v9"
	mock "github.com/stretchr/testify/mock"
)

// NewMockLeaderboardCache creates a new instance of MockLeaderboardCache. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockLeaderboardCache(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockLeaderboardCache {
	mock := &MockLeaderboardCache{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockLeaderboardCache is an autogenerated mock type for the LeaderboardCache type
type MockLeaderboardCache struct {
	mock.Mock
}

type MockLeaderboardCache_Expecter struct {
	mock *mock.Mock
}

func (_m *MockLeaderboardCache) EXPECT() *MockLeaderboardCache_Expecter {
	return &MockLeaderboardCache_Expecter{mock: &_m.Mock}
}

// AddOrUpdateScore provides a mock function for the type MockLeaderboardCache
func (_mock *MockLeaderboardCache) AddOrUpdateScore(ctx context.Context, leaderboardKey string, userID string, score float64) error {
	ret := _mock.Called(ctx, leaderboardKey, userID, score)

	if len(ret) == 0 {
		panic("no return value specified for AddOrUpdateScore")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, float64) error); ok {
		r0 = returnFunc(ctx, leaderboardKey, userID, score)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockLeaderboardCache_AddOrUpdateScore_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddOrUpdateScore'
type MockLeaderboardCache_AddOrUpdateScore_Call struct {
	*mock.Call
}

// AddOrUpdateScore is a helper method to define mock.On call
//   - ctx
//   - leaderboardKey
//   - userID
//   - score
func (_e *MockLeaderboardCache_Expecter) AddOrUpdateScore(ctx interface{}, leaderboardKey interface{}, userID interface{}, score interface{}) *MockLeaderboardCache_AddOrUpdateScore_Call {
	return &MockLeaderboardCache_AddOrUpdateScore_Call{Call: _e.mock.On("AddOrUpdateScore", ctx, leaderboardKey, userID, score)}
}

func (_c *MockLeaderboardCache_AddOrUpdateScore_Call) Run(run func(ctx context.Context, leaderboardKey string, userID string, score float64)) *MockLeaderboardCache_AddOrUpdateScore_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(float64))
	})
	return _c
}

func (_c *MockLeaderboardCache_AddOrUpdateScore_Call) Return(err error) *MockLeaderboardCache_AddOrUpdateScore_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockLeaderboardCache_AddOrUpdateScore_Call) RunAndReturn(run func(ctx context.Context, leaderboardKey string, userID string, score float64) error) *MockLeaderboardCache_AddOrUpdateScore_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteLeaderboard provides a mock function for the type MockLeaderboardCache
func (_mock *MockLeaderboardCache) DeleteLeaderboard(ctx context.Context, leaderboardKey string) error {
	ret := _mock.Called(ctx, leaderboardKey)

	if len(ret) == 0 {
		panic("no return value specified for DeleteLeaderboard")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = returnFunc(ctx, leaderboardKey)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockLeaderboardCache_DeleteLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteLeaderboard'
type MockLeaderboardCache_DeleteLeaderboard_Call struct {
	*mock.Call
}

// DeleteLeaderboard is a helper method to define mock.On call
//   - ctx
//   - leaderboardKey
func (_e *MockLeaderboardCache_Expecter) DeleteLeaderboard(ctx interface{}, leaderboardKey interface{}) *MockLeaderboardCache_DeleteLeaderboard_Call {
	return &MockLeaderboardCache_DeleteLeaderboard_Call{Call: _e.mock.On("DeleteLeaderboard", ctx, leaderboardKey)}
}

func (_c *MockLeaderboardCache_DeleteLeaderboard_Call) Run(run func(ctx context.Context, leaderboardKey string)) *MockLeaderboardCache_DeleteLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockLeaderboardCache_DeleteLeaderboard_Call) Return(err error) *MockLeaderboardCache_DeleteLeaderboard_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockLeaderboardCache_DeleteLeaderboard_Call) RunAndReturn(run func(ctx context.Context, leaderboardKey string) error) *MockLeaderboardCache_DeleteLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetPaginatedLeaderboard provides a mock function for the type MockLeaderboardCache
func (_mock *MockLeaderboardCache) GetPaginatedLeaderboard(ctx context.Context, leaderboardKey string, start int64, limit int64) ([]*cache.LeaderboardEntity, error) {
	ret := _mock.Called(ctx, leaderboardKey, start, limit)

	if len(ret) == 0 {
		panic("no return value specified for GetPaginatedLeaderboard")
	}

	var r0 []*cache.LeaderboardEntity
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64, int64) ([]*cache.LeaderboardEntity, error)); ok {
		return returnFunc(ctx, leaderboardKey, start, limit)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64, int64) []*cache.LeaderboardEntity); ok {
		r0 = returnFunc(ctx, leaderboardKey, start, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*cache.LeaderboardEntity)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, int64, int64) error); ok {
		r1 = returnFunc(ctx, leaderboardKey, start, limit)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeaderboardCache_GetPaginatedLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPaginatedLeaderboard'
type MockLeaderboardCache_GetPaginatedLeaderboard_Call struct {
	*mock.Call
}

// GetPaginatedLeaderboard is a helper method to define mock.On call
//   - ctx
//   - leaderboardKey
//   - start
//   - limit
func (_e *MockLeaderboardCache_Expecter) GetPaginatedLeaderboard(ctx interface{}, leaderboardKey interface{}, start interface{}, limit interface{}) *MockLeaderboardCache_GetPaginatedLeaderboard_Call {
	return &MockLeaderboardCache_GetPaginatedLeaderboard_Call{Call: _e.mock.On("GetPaginatedLeaderboard", ctx, leaderboardKey, start, limit)}
}

func (_c *MockLeaderboardCache_GetPaginatedLeaderboard_Call) Run(run func(ctx context.Context, leaderboardKey string, start int64, limit int64)) *MockLeaderboardCache_GetPaginatedLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int64), args[3].(int64))
	})
	return _c
}

func (_c *MockLeaderboardCache_GetPaginatedLeaderboard_Call) Return(leaderboardEntitys []*cache.LeaderboardEntity, err error) *MockLeaderboardCache_GetPaginatedLeaderboard_Call {
	_c.Call.Return(leaderboardEntitys, err)
	return _c
}

func (_c *MockLeaderboardCache_GetPaginatedLeaderboard_Call) RunAndReturn(run func(ctx context.Context, leaderboardKey string, start int64, limit int64) ([]*cache.LeaderboardEntity, error)) *MockLeaderboardCache_GetPaginatedLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetPaginatedLeaderboardRev provides a mock function for the type MockLeaderboardCache
func (_mock *MockLeaderboardCache) GetPaginatedLeaderboardRev(ctx context.Context, leaderboardKey string, start int64, limit int64) ([]*cache.LeaderboardEntity, error) {
	ret := _mock.Called(ctx, leaderboardKey, start, limit)

	if len(ret) == 0 {
		panic("no return value specified for GetPaginatedLeaderboardRev")
	}

	var r0 []*cache.LeaderboardEntity
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64, int64) ([]*cache.LeaderboardEntity, error)); ok {
		return returnFunc(ctx, leaderboardKey, start, limit)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64, int64) []*cache.LeaderboardEntity); ok {
		r0 = returnFunc(ctx, leaderboardKey, start, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*cache.LeaderboardEntity)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, int64, int64) error); ok {
		r1 = returnFunc(ctx, leaderboardKey, start, limit)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeaderboardCache_GetPaginatedLeaderboardRev_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPaginatedLeaderboardRev'
type MockLeaderboardCache_GetPaginatedLeaderboardRev_Call struct {
	*mock.Call
}

// GetPaginatedLeaderboardRev is a helper method to define mock.On call
//   - ctx
//   - leaderboardKey
//   - start
//   - limit
func (_e *MockLeaderboardCache_Expecter) GetPaginatedLeaderboardRev(ctx interface{}, leaderboardKey interface{}, start interface{}, limit interface{}) *MockLeaderboardCache_GetPaginatedLeaderboardRev_Call {
	return &MockLeaderboardCache_GetPaginatedLeaderboardRev_Call{Call: _e.mock.On("GetPaginatedLeaderboardRev", ctx, leaderboardKey, start, limit)}
}

func (_c *MockLeaderboardCache_GetPaginatedLeaderboardRev_Call) Run(run func(ctx context.Context, leaderboardKey string, start int64, limit int64)) *MockLeaderboardCache_GetPaginatedLeaderboardRev_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int64), args[3].(int64))
	})
	return _c
}

func (_c *MockLeaderboardCache_GetPaginatedLeaderboardRev_Call) Return(leaderboardEntitys []*cache.LeaderboardEntity, err error) *MockLeaderboardCache_GetPaginatedLeaderboardRev_Call {
	_c.Call.Return(leaderboardEntitys, err)
	return _c
}

func (_c *MockLeaderboardCache_GetPaginatedLeaderboardRev_Call) RunAndReturn(run func(ctx context.Context, leaderboardKey string, start int64, limit int64) ([]*cache.LeaderboardEntity, error)) *MockLeaderboardCache_GetPaginatedLeaderboardRev_Call {
	_c.Call.Return(run)
	return _c
}

// GetRank provides a mock function for the type MockLeaderboardCache
func (_mock *MockLeaderboardCache) GetRank(ctx context.Context, leaderboardKey string, userID string) (int64, error) {
	ret := _mock.Called(ctx, leaderboardKey, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetRank")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) (int64, error)); ok {
		return returnFunc(ctx, leaderboardKey, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) int64); ok {
		r0 = returnFunc(ctx, leaderboardKey, userID)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = returnFunc(ctx, leaderboardKey, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeaderboardCache_GetRank_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRank'
type MockLeaderboardCache_GetRank_Call struct {
	*mock.Call
}

// GetRank is a helper method to define mock.On call
//   - ctx
//   - leaderboardKey
//   - userID
func (_e *MockLeaderboardCache_Expecter) GetRank(ctx interface{}, leaderboardKey interface{}, userID interface{}) *MockLeaderboardCache_GetRank_Call {
	return &MockLeaderboardCache_GetRank_Call{Call: _e.mock.On("GetRank", ctx, leaderboardKey, userID)}
}

func (_c *MockLeaderboardCache_GetRank_Call) Run(run func(ctx context.Context, leaderboardKey string, userID string)) *MockLeaderboardCache_GetRank_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockLeaderboardCache_GetRank_Call) Return(n int64, err error) *MockLeaderboardCache_GetRank_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockLeaderboardCache_GetRank_Call) RunAndReturn(run func(ctx context.Context, leaderboardKey string, userID string) (int64, error)) *MockLeaderboardCache_GetRank_Call {
	_c.Call.Return(run)
	return _c
}

// GetScore provides a mock function for the type MockLeaderboardCache
func (_mock *MockLeaderboardCache) GetScore(ctx context.Context, leaderboardKey string, userID string) (float64, error) {
	ret := _mock.Called(ctx, leaderboardKey, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetScore")
	}

	var r0 float64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) (float64, error)); ok {
		return returnFunc(ctx, leaderboardKey, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) float64); ok {
		r0 = returnFunc(ctx, leaderboardKey, userID)
	} else {
		r0 = ret.Get(0).(float64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = returnFunc(ctx, leaderboardKey, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeaderboardCache_GetScore_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetScore'
type MockLeaderboardCache_GetScore_Call struct {
	*mock.Call
}

// GetScore is a helper method to define mock.On call
//   - ctx
//   - leaderboardKey
//   - userID
func (_e *MockLeaderboardCache_Expecter) GetScore(ctx interface{}, leaderboardKey interface{}, userID interface{}) *MockLeaderboardCache_GetScore_Call {
	return &MockLeaderboardCache_GetScore_Call{Call: _e.mock.On("GetScore", ctx, leaderboardKey, userID)}
}

func (_c *MockLeaderboardCache_GetScore_Call) Run(run func(ctx context.Context, leaderboardKey string, userID string)) *MockLeaderboardCache_GetScore_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockLeaderboardCache_GetScore_Call) Return(f float64, err error) *MockLeaderboardCache_GetScore_Call {
	_c.Call.Return(f, err)
	return _c
}

func (_c *MockLeaderboardCache_GetScore_Call) RunAndReturn(run func(ctx context.Context, leaderboardKey string, userID string) (float64, error)) *MockLeaderboardCache_GetScore_Call {
	_c.Call.Return(run)
	return _c
}

// GetTopUsers provides a mock function for the type MockLeaderboardCache
func (_mock *MockLeaderboardCache) GetTopUsers(ctx context.Context, leaderboardKey string, count int64) ([]redis.Z, error) {
	ret := _mock.Called(ctx, leaderboardKey, count)

	if len(ret) == 0 {
		panic("no return value specified for GetTopUsers")
	}

	var r0 []redis.Z
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64) ([]redis.Z, error)); ok {
		return returnFunc(ctx, leaderboardKey, count)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64) []redis.Z); ok {
		r0 = returnFunc(ctx, leaderboardKey, count)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]redis.Z)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, int64) error); ok {
		r1 = returnFunc(ctx, leaderboardKey, count)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeaderboardCache_GetTopUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTopUsers'
type MockLeaderboardCache_GetTopUsers_Call struct {
	*mock.Call
}

// GetTopUsers is a helper method to define mock.On call
//   - ctx
//   - leaderboardKey
//   - count
func (_e *MockLeaderboardCache_Expecter) GetTopUsers(ctx interface{}, leaderboardKey interface{}, count interface{}) *MockLeaderboardCache_GetTopUsers_Call {
	return &MockLeaderboardCache_GetTopUsers_Call{Call: _e.mock.On("GetTopUsers", ctx, leaderboardKey, count)}
}

func (_c *MockLeaderboardCache_GetTopUsers_Call) Run(run func(ctx context.Context, leaderboardKey string, count int64)) *MockLeaderboardCache_GetTopUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int64))
	})
	return _c
}

func (_c *MockLeaderboardCache_GetTopUsers_Call) Return(zs []redis.Z, err error) *MockLeaderboardCache_GetTopUsers_Call {
	_c.Call.Return(zs, err)
	return _c
}

func (_c *MockLeaderboardCache_GetTopUsers_Call) RunAndReturn(run func(ctx context.Context, leaderboardKey string, count int64) ([]redis.Z, error)) *MockLeaderboardCache_GetTopUsers_Call {
	_c.Call.Return(run)
	return _c
}

// GetTotalEntityCount provides a mock function for the type MockLeaderboardCache
func (_mock *MockLeaderboardCache) GetTotalEntityCount(ctx context.Context, leaderboardKey string) (int64, error) {
	ret := _mock.Called(ctx, leaderboardKey)

	if len(ret) == 0 {
		panic("no return value specified for GetTotalEntityCount")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (int64, error)); ok {
		return returnFunc(ctx, leaderboardKey)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) int64); ok {
		r0 = returnFunc(ctx, leaderboardKey)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, leaderboardKey)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeaderboardCache_GetTotalEntityCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTotalEntityCount'
type MockLeaderboardCache_GetTotalEntityCount_Call struct {
	*mock.Call
}

// GetTotalEntityCount is a helper method to define mock.On call
//   - ctx
//   - leaderboardKey
func (_e *MockLeaderboardCache_Expecter) GetTotalEntityCount(ctx interface{}, leaderboardKey interface{}) *MockLeaderboardCache_GetTotalEntityCount_Call {
	return &MockLeaderboardCache_GetTotalEntityCount_Call{Call: _e.mock.On("GetTotalEntityCount", ctx, leaderboardKey)}
}

func (_c *MockLeaderboardCache_GetTotalEntityCount_Call) Run(run func(ctx context.Context, leaderboardKey string)) *MockLeaderboardCache_GetTotalEntityCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockLeaderboardCache_GetTotalEntityCount_Call) Return(n int64, err error) *MockLeaderboardCache_GetTotalEntityCount_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockLeaderboardCache_GetTotalEntityCount_Call) RunAndReturn(run func(ctx context.Context, leaderboardKey string) (int64, error)) *MockLeaderboardCache_GetTotalEntityCount_Call {
	_c.Call.Return(run)
	return _c
}

// GetUsersByPreviousID provides a mock function for the type MockLeaderboardCache
func (_mock *MockLeaderboardCache) GetUsersByPreviousID(ctx context.Context, leaderboardKey string, previousID string, pageSize int64) ([]redis.Z, error) {
	ret := _mock.Called(ctx, leaderboardKey, previousID, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetUsersByPreviousID")
	}

	var r0 []redis.Z
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, int64) ([]redis.Z, error)); ok {
		return returnFunc(ctx, leaderboardKey, previousID, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, int64) []redis.Z); ok {
		r0 = returnFunc(ctx, leaderboardKey, previousID, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]redis.Z)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string, int64) error); ok {
		r1 = returnFunc(ctx, leaderboardKey, previousID, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeaderboardCache_GetUsersByPreviousID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUsersByPreviousID'
type MockLeaderboardCache_GetUsersByPreviousID_Call struct {
	*mock.Call
}

// GetUsersByPreviousID is a helper method to define mock.On call
//   - ctx
//   - leaderboardKey
//   - previousID
//   - pageSize
func (_e *MockLeaderboardCache_Expecter) GetUsersByPreviousID(ctx interface{}, leaderboardKey interface{}, previousID interface{}, pageSize interface{}) *MockLeaderboardCache_GetUsersByPreviousID_Call {
	return &MockLeaderboardCache_GetUsersByPreviousID_Call{Call: _e.mock.On("GetUsersByPreviousID", ctx, leaderboardKey, previousID, pageSize)}
}

func (_c *MockLeaderboardCache_GetUsersByPreviousID_Call) Run(run func(ctx context.Context, leaderboardKey string, previousID string, pageSize int64)) *MockLeaderboardCache_GetUsersByPreviousID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(int64))
	})
	return _c
}

func (_c *MockLeaderboardCache_GetUsersByPreviousID_Call) Return(zs []redis.Z, err error) *MockLeaderboardCache_GetUsersByPreviousID_Call {
	_c.Call.Return(zs, err)
	return _c
}

func (_c *MockLeaderboardCache_GetUsersByPreviousID_Call) RunAndReturn(run func(ctx context.Context, leaderboardKey string, previousID string, pageSize int64) ([]redis.Z, error)) *MockLeaderboardCache_GetUsersByPreviousID_Call {
	_c.Call.Return(run)
	return _c
}
