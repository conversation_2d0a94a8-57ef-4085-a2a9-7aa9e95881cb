// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package cache

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
)

// NewMockPuzzleGameCache creates a new instance of MockPuzzleGameCache. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPuzzleGameCache(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPuzzleGameCache {
	mock := &MockPuzzleGameCache{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockPuzzleGameCache is an autogenerated mock type for the PuzzleGameCache type
type MockPuzzleGameCache struct {
	mock.Mock
}

type MockPuzzleGameCache_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPuzzleGameCache) EXPECT() *MockPuzzleGameCache_Expecter {
	return &MockPuzzleGameCache_Expecter{mock: &_m.Mock}
}

// DeletePuzzleGame provides a mock function for the type MockPuzzleGameCache
func (_mock *MockPuzzleGameCache) DeletePuzzleGame(ctx context.Context, gameID string) error {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for DeletePuzzleGame")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPuzzleGameCache_DeletePuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeletePuzzleGame'
type MockPuzzleGameCache_DeletePuzzleGame_Call struct {
	*mock.Call
}

// DeletePuzzleGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockPuzzleGameCache_Expecter) DeletePuzzleGame(ctx interface{}, gameID interface{}) *MockPuzzleGameCache_DeletePuzzleGame_Call {
	return &MockPuzzleGameCache_DeletePuzzleGame_Call{Call: _e.mock.On("DeletePuzzleGame", ctx, gameID)}
}

func (_c *MockPuzzleGameCache_DeletePuzzleGame_Call) Run(run func(ctx context.Context, gameID string)) *MockPuzzleGameCache_DeletePuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockPuzzleGameCache_DeletePuzzleGame_Call) Return(err error) *MockPuzzleGameCache_DeletePuzzleGame_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPuzzleGameCache_DeletePuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID string) error) *MockPuzzleGameCache_DeletePuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// DeletePuzzleGameSearchEntry provides a mock function for the type MockPuzzleGameCache
func (_mock *MockPuzzleGameCache) DeletePuzzleGameSearchEntry(ctx context.Context, userID string) error {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeletePuzzleGameSearchEntry")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPuzzleGameCache_DeletePuzzleGameSearchEntry_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeletePuzzleGameSearchEntry'
type MockPuzzleGameCache_DeletePuzzleGameSearchEntry_Call struct {
	*mock.Call
}

// DeletePuzzleGameSearchEntry is a helper method to define mock.On call
//   - ctx
//   - userID
func (_e *MockPuzzleGameCache_Expecter) DeletePuzzleGameSearchEntry(ctx interface{}, userID interface{}) *MockPuzzleGameCache_DeletePuzzleGameSearchEntry_Call {
	return &MockPuzzleGameCache_DeletePuzzleGameSearchEntry_Call{Call: _e.mock.On("DeletePuzzleGameSearchEntry", ctx, userID)}
}

func (_c *MockPuzzleGameCache_DeletePuzzleGameSearchEntry_Call) Run(run func(ctx context.Context, userID string)) *MockPuzzleGameCache_DeletePuzzleGameSearchEntry_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockPuzzleGameCache_DeletePuzzleGameSearchEntry_Call) Return(err error) *MockPuzzleGameCache_DeletePuzzleGameSearchEntry_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPuzzleGameCache_DeletePuzzleGameSearchEntry_Call) RunAndReturn(run func(ctx context.Context, userID string) error) *MockPuzzleGameCache_DeletePuzzleGameSearchEntry_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllPuzzleGameSearchEntries provides a mock function for the type MockPuzzleGameCache
func (_mock *MockPuzzleGameCache) GetAllPuzzleGameSearchEntries(ctx context.Context) ([]*models.SearchEntry, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAllPuzzleGameSearchEntries")
	}

	var r0 []*models.SearchEntry
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.SearchEntry, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.SearchEntry); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.SearchEntry)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameCache_GetAllPuzzleGameSearchEntries_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllPuzzleGameSearchEntries'
type MockPuzzleGameCache_GetAllPuzzleGameSearchEntries_Call struct {
	*mock.Call
}

// GetAllPuzzleGameSearchEntries is a helper method to define mock.On call
//   - ctx
func (_e *MockPuzzleGameCache_Expecter) GetAllPuzzleGameSearchEntries(ctx interface{}) *MockPuzzleGameCache_GetAllPuzzleGameSearchEntries_Call {
	return &MockPuzzleGameCache_GetAllPuzzleGameSearchEntries_Call{Call: _e.mock.On("GetAllPuzzleGameSearchEntries", ctx)}
}

func (_c *MockPuzzleGameCache_GetAllPuzzleGameSearchEntries_Call) Run(run func(ctx context.Context)) *MockPuzzleGameCache_GetAllPuzzleGameSearchEntries_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockPuzzleGameCache_GetAllPuzzleGameSearchEntries_Call) Return(searchEntrys []*models.SearchEntry, err error) *MockPuzzleGameCache_GetAllPuzzleGameSearchEntries_Call {
	_c.Call.Return(searchEntrys, err)
	return _c
}

func (_c *MockPuzzleGameCache_GetAllPuzzleGameSearchEntries_Call) RunAndReturn(run func(ctx context.Context) ([]*models.SearchEntry, error)) *MockPuzzleGameCache_GetAllPuzzleGameSearchEntries_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllPuzzleGames provides a mock function for the type MockPuzzleGameCache
func (_mock *MockPuzzleGameCache) GetAllPuzzleGames(ctx context.Context) ([]*models.PuzzleGame, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAllPuzzleGames")
	}

	var r0 []*models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.PuzzleGame, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.PuzzleGame); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameCache_GetAllPuzzleGames_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllPuzzleGames'
type MockPuzzleGameCache_GetAllPuzzleGames_Call struct {
	*mock.Call
}

// GetAllPuzzleGames is a helper method to define mock.On call
//   - ctx
func (_e *MockPuzzleGameCache_Expecter) GetAllPuzzleGames(ctx interface{}) *MockPuzzleGameCache_GetAllPuzzleGames_Call {
	return &MockPuzzleGameCache_GetAllPuzzleGames_Call{Call: _e.mock.On("GetAllPuzzleGames", ctx)}
}

func (_c *MockPuzzleGameCache_GetAllPuzzleGames_Call) Run(run func(ctx context.Context)) *MockPuzzleGameCache_GetAllPuzzleGames_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockPuzzleGameCache_GetAllPuzzleGames_Call) Return(puzzleGames []*models.PuzzleGame, err error) *MockPuzzleGameCache_GetAllPuzzleGames_Call {
	_c.Call.Return(puzzleGames, err)
	return _c
}

func (_c *MockPuzzleGameCache_GetAllPuzzleGames_Call) RunAndReturn(run func(ctx context.Context) ([]*models.PuzzleGame, error)) *MockPuzzleGameCache_GetAllPuzzleGames_Call {
	_c.Call.Return(run)
	return _c
}

// GetPuzzleGame provides a mock function for the type MockPuzzleGameCache
func (_mock *MockPuzzleGameCache) GetPuzzleGame(ctx context.Context, gameID string) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for GetPuzzleGame")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameCache_GetPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPuzzleGame'
type MockPuzzleGameCache_GetPuzzleGame_Call struct {
	*mock.Call
}

// GetPuzzleGame is a helper method to define mock.On call
//   - ctx
//   - gameID
func (_e *MockPuzzleGameCache_Expecter) GetPuzzleGame(ctx interface{}, gameID interface{}) *MockPuzzleGameCache_GetPuzzleGame_Call {
	return &MockPuzzleGameCache_GetPuzzleGame_Call{Call: _e.mock.On("GetPuzzleGame", ctx, gameID)}
}

func (_c *MockPuzzleGameCache_GetPuzzleGame_Call) Run(run func(ctx context.Context, gameID string)) *MockPuzzleGameCache_GetPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockPuzzleGameCache_GetPuzzleGame_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockPuzzleGameCache_GetPuzzleGame_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockPuzzleGameCache_GetPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID string) (*models.PuzzleGame, error)) *MockPuzzleGameCache_GetPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// GetPuzzleGameSearchEntry provides a mock function for the type MockPuzzleGameCache
func (_mock *MockPuzzleGameCache) GetPuzzleGameSearchEntry(ctx context.Context, userID string) (*models.SearchEntry, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetPuzzleGameSearchEntry")
	}

	var r0 *models.SearchEntry
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*models.SearchEntry, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *models.SearchEntry); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.SearchEntry)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameCache_GetPuzzleGameSearchEntry_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPuzzleGameSearchEntry'
type MockPuzzleGameCache_GetPuzzleGameSearchEntry_Call struct {
	*mock.Call
}

// GetPuzzleGameSearchEntry is a helper method to define mock.On call
//   - ctx
//   - userID
func (_e *MockPuzzleGameCache_Expecter) GetPuzzleGameSearchEntry(ctx interface{}, userID interface{}) *MockPuzzleGameCache_GetPuzzleGameSearchEntry_Call {
	return &MockPuzzleGameCache_GetPuzzleGameSearchEntry_Call{Call: _e.mock.On("GetPuzzleGameSearchEntry", ctx, userID)}
}

func (_c *MockPuzzleGameCache_GetPuzzleGameSearchEntry_Call) Run(run func(ctx context.Context, userID string)) *MockPuzzleGameCache_GetPuzzleGameSearchEntry_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockPuzzleGameCache_GetPuzzleGameSearchEntry_Call) Return(searchEntry *models.SearchEntry, err error) *MockPuzzleGameCache_GetPuzzleGameSearchEntry_Call {
	_c.Call.Return(searchEntry, err)
	return _c
}

func (_c *MockPuzzleGameCache_GetPuzzleGameSearchEntry_Call) RunAndReturn(run func(ctx context.Context, userID string) (*models.SearchEntry, error)) *MockPuzzleGameCache_GetPuzzleGameSearchEntry_Call {
	_c.Call.Return(run)
	return _c
}

// SetPuzzleGame provides a mock function for the type MockPuzzleGameCache
func (_mock *MockPuzzleGameCache) SetPuzzleGame(ctx context.Context, game *models.PuzzleGame) error {
	ret := _mock.Called(ctx, game)

	if len(ret) == 0 {
		panic("no return value specified for SetPuzzleGame")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PuzzleGame) error); ok {
		r0 = returnFunc(ctx, game)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPuzzleGameCache_SetPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetPuzzleGame'
type MockPuzzleGameCache_SetPuzzleGame_Call struct {
	*mock.Call
}

// SetPuzzleGame is a helper method to define mock.On call
//   - ctx
//   - game
func (_e *MockPuzzleGameCache_Expecter) SetPuzzleGame(ctx interface{}, game interface{}) *MockPuzzleGameCache_SetPuzzleGame_Call {
	return &MockPuzzleGameCache_SetPuzzleGame_Call{Call: _e.mock.On("SetPuzzleGame", ctx, game)}
}

func (_c *MockPuzzleGameCache_SetPuzzleGame_Call) Run(run func(ctx context.Context, game *models.PuzzleGame)) *MockPuzzleGameCache_SetPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.PuzzleGame))
	})
	return _c
}

func (_c *MockPuzzleGameCache_SetPuzzleGame_Call) Return(err error) *MockPuzzleGameCache_SetPuzzleGame_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPuzzleGameCache_SetPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, game *models.PuzzleGame) error) *MockPuzzleGameCache_SetPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// SetPuzzleGameSearchEntry provides a mock function for the type MockPuzzleGameCache
func (_mock *MockPuzzleGameCache) SetPuzzleGameSearchEntry(ctx context.Context, searchEntry *models.SearchEntry) error {
	ret := _mock.Called(ctx, searchEntry)

	if len(ret) == 0 {
		panic("no return value specified for SetPuzzleGameSearchEntry")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.SearchEntry) error); ok {
		r0 = returnFunc(ctx, searchEntry)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPuzzleGameCache_SetPuzzleGameSearchEntry_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetPuzzleGameSearchEntry'
type MockPuzzleGameCache_SetPuzzleGameSearchEntry_Call struct {
	*mock.Call
}

// SetPuzzleGameSearchEntry is a helper method to define mock.On call
//   - ctx
//   - searchEntry
func (_e *MockPuzzleGameCache_Expecter) SetPuzzleGameSearchEntry(ctx interface{}, searchEntry interface{}) *MockPuzzleGameCache_SetPuzzleGameSearchEntry_Call {
	return &MockPuzzleGameCache_SetPuzzleGameSearchEntry_Call{Call: _e.mock.On("SetPuzzleGameSearchEntry", ctx, searchEntry)}
}

func (_c *MockPuzzleGameCache_SetPuzzleGameSearchEntry_Call) Run(run func(ctx context.Context, searchEntry *models.SearchEntry)) *MockPuzzleGameCache_SetPuzzleGameSearchEntry_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.SearchEntry))
	})
	return _c
}

func (_c *MockPuzzleGameCache_SetPuzzleGameSearchEntry_Call) Return(err error) *MockPuzzleGameCache_SetPuzzleGameSearchEntry_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPuzzleGameCache_SetPuzzleGameSearchEntry_Call) RunAndReturn(run func(ctx context.Context, searchEntry *models.SearchEntry) error) *MockPuzzleGameCache_SetPuzzleGameSearchEntry_Call {
	_c.Call.Return(run)
	return _c
}
