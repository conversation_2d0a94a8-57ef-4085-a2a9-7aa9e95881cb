// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package cache

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockShowdownCache creates a new instance of MockShowdownCache. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockShowdownCache(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockShowdownCache {
	mock := &MockShowdownCache{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockShowdownCache is an autogenerated mock type for the ShowdownCache type
type MockShowdownCache struct {
	mock.Mock
}

type MockShowdownCache_Expecter struct {
	mock *mock.Mock
}

func (_m *MockShowdownCache) EXPECT() *MockShowdownCache_Expecter {
	return &MockShowdownCache_Expecter{mock: &_m.Mock}
}

// DeleteAllshowdowns provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) DeleteAllshowdowns(ctx context.Context) error {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAllshowdowns")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = returnFunc(ctx)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownCache_DeleteAllshowdowns_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAllshowdowns'
type MockShowdownCache_DeleteAllshowdowns_Call struct {
	*mock.Call
}

// DeleteAllshowdowns is a helper method to define mock.On call
//   - ctx
func (_e *MockShowdownCache_Expecter) DeleteAllshowdowns(ctx interface{}) *MockShowdownCache_DeleteAllshowdowns_Call {
	return &MockShowdownCache_DeleteAllshowdowns_Call{Call: _e.mock.On("DeleteAllshowdowns", ctx)}
}

func (_c *MockShowdownCache_DeleteAllshowdowns_Call) Run(run func(ctx context.Context)) *MockShowdownCache_DeleteAllshowdowns_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockShowdownCache_DeleteAllshowdowns_Call) Return(err error) *MockShowdownCache_DeleteAllshowdowns_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownCache_DeleteAllshowdowns_Call) RunAndReturn(run func(ctx context.Context) error) *MockShowdownCache_DeleteAllshowdowns_Call {
	_c.Call.Return(run)
	return _c
}

// Deleteshowdown provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) Deleteshowdown(ctx context.Context, showdownId primitive.ObjectID) error {
	ret := _mock.Called(ctx, showdownId)

	if len(ret) == 0 {
		panic("no return value specified for Deleteshowdown")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, showdownId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownCache_Deleteshowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Deleteshowdown'
type MockShowdownCache_Deleteshowdown_Call struct {
	*mock.Call
}

// Deleteshowdown is a helper method to define mock.On call
//   - ctx
//   - showdownId
func (_e *MockShowdownCache_Expecter) Deleteshowdown(ctx interface{}, showdownId interface{}) *MockShowdownCache_Deleteshowdown_Call {
	return &MockShowdownCache_Deleteshowdown_Call{Call: _e.mock.On("Deleteshowdown", ctx, showdownId)}
}

func (_c *MockShowdownCache_Deleteshowdown_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID)) *MockShowdownCache_Deleteshowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownCache_Deleteshowdown_Call) Return(err error) *MockShowdownCache_Deleteshowdown_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownCache_Deleteshowdown_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID) error) *MockShowdownCache_Deleteshowdown_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteshowdownParticipant provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) DeleteshowdownParticipant(ctx context.Context, showdownId primitive.ObjectID, userId primitive.ObjectID) error {
	ret := _mock.Called(ctx, showdownId, userId)

	if len(ret) == 0 {
		panic("no return value specified for DeleteshowdownParticipant")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, showdownId, userId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownCache_DeleteshowdownParticipant_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteshowdownParticipant'
type MockShowdownCache_DeleteshowdownParticipant_Call struct {
	*mock.Call
}

// DeleteshowdownParticipant is a helper method to define mock.On call
//   - ctx
//   - showdownId
//   - userId
func (_e *MockShowdownCache_Expecter) DeleteshowdownParticipant(ctx interface{}, showdownId interface{}, userId interface{}) *MockShowdownCache_DeleteshowdownParticipant_Call {
	return &MockShowdownCache_DeleteshowdownParticipant_Call{Call: _e.mock.On("DeleteshowdownParticipant", ctx, showdownId, userId)}
}

func (_c *MockShowdownCache_DeleteshowdownParticipant_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, userId primitive.ObjectID)) *MockShowdownCache_DeleteshowdownParticipant_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownCache_DeleteshowdownParticipant_Call) Return(err error) *MockShowdownCache_DeleteshowdownParticipant_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownCache_DeleteshowdownParticipant_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, userId primitive.ObjectID) error) *MockShowdownCache_DeleteshowdownParticipant_Call {
	_c.Call.Return(run)
	return _c
}

// GetFictures provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) GetFictures(ctx context.Context, showdownId primitive.ObjectID) (*models.FicturesCollection, error) {
	ret := _mock.Called(ctx, showdownId)

	if len(ret) == 0 {
		panic("no return value specified for GetFictures")
	}

	var r0 *models.FicturesCollection
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.FicturesCollection, error)); ok {
		return returnFunc(ctx, showdownId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.FicturesCollection); ok {
		r0 = returnFunc(ctx, showdownId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.FicturesCollection)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownCache_GetFictures_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFictures'
type MockShowdownCache_GetFictures_Call struct {
	*mock.Call
}

// GetFictures is a helper method to define mock.On call
//   - ctx
//   - showdownId
func (_e *MockShowdownCache_Expecter) GetFictures(ctx interface{}, showdownId interface{}) *MockShowdownCache_GetFictures_Call {
	return &MockShowdownCache_GetFictures_Call{Call: _e.mock.On("GetFictures", ctx, showdownId)}
}

func (_c *MockShowdownCache_GetFictures_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID)) *MockShowdownCache_GetFictures_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownCache_GetFictures_Call) Return(ficturesCollection *models.FicturesCollection, err error) *MockShowdownCache_GetFictures_Call {
	_c.Call.Return(ficturesCollection, err)
	return _c
}

func (_c *MockShowdownCache_GetFictures_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID) (*models.FicturesCollection, error)) *MockShowdownCache_GetFictures_Call {
	_c.Call.Return(run)
	return _c
}

// GetLeaderboardEntitiesCount provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) GetLeaderboardEntitiesCount(ctx context.Context, showdownId primitive.ObjectID) (int64, error) {
	ret := _mock.Called(ctx, showdownId)

	if len(ret) == 0 {
		panic("no return value specified for GetLeaderboardEntitiesCount")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (int64, error)); ok {
		return returnFunc(ctx, showdownId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) int64); ok {
		r0 = returnFunc(ctx, showdownId)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownCache_GetLeaderboardEntitiesCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLeaderboardEntitiesCount'
type MockShowdownCache_GetLeaderboardEntitiesCount_Call struct {
	*mock.Call
}

// GetLeaderboardEntitiesCount is a helper method to define mock.On call
//   - ctx
//   - showdownId
func (_e *MockShowdownCache_Expecter) GetLeaderboardEntitiesCount(ctx interface{}, showdownId interface{}) *MockShowdownCache_GetLeaderboardEntitiesCount_Call {
	return &MockShowdownCache_GetLeaderboardEntitiesCount_Call{Call: _e.mock.On("GetLeaderboardEntitiesCount", ctx, showdownId)}
}

func (_c *MockShowdownCache_GetLeaderboardEntitiesCount_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID)) *MockShowdownCache_GetLeaderboardEntitiesCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownCache_GetLeaderboardEntitiesCount_Call) Return(n int64, err error) *MockShowdownCache_GetLeaderboardEntitiesCount_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockShowdownCache_GetLeaderboardEntitiesCount_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID) (int64, error)) *MockShowdownCache_GetLeaderboardEntitiesCount_Call {
	_c.Call.Return(run)
	return _c
}

// GetPaginatedLeaderboard provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) GetPaginatedLeaderboard(ctx context.Context, showdownId primitive.ObjectID, page int, pageSize int) ([]*cache.LeaderboardEntity, error) {
	ret := _mock.Called(ctx, showdownId, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetPaginatedLeaderboard")
	}

	var r0 []*cache.LeaderboardEntity
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) ([]*cache.LeaderboardEntity, error)); ok {
		return returnFunc(ctx, showdownId, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) []*cache.LeaderboardEntity); ok {
		r0 = returnFunc(ctx, showdownId, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*cache.LeaderboardEntity)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int, int) error); ok {
		r1 = returnFunc(ctx, showdownId, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownCache_GetPaginatedLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPaginatedLeaderboard'
type MockShowdownCache_GetPaginatedLeaderboard_Call struct {
	*mock.Call
}

// GetPaginatedLeaderboard is a helper method to define mock.On call
//   - ctx
//   - showdownId
//   - page
//   - pageSize
func (_e *MockShowdownCache_Expecter) GetPaginatedLeaderboard(ctx interface{}, showdownId interface{}, page interface{}, pageSize interface{}) *MockShowdownCache_GetPaginatedLeaderboard_Call {
	return &MockShowdownCache_GetPaginatedLeaderboard_Call{Call: _e.mock.On("GetPaginatedLeaderboard", ctx, showdownId, page, pageSize)}
}

func (_c *MockShowdownCache_GetPaginatedLeaderboard_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, page int, pageSize int)) *MockShowdownCache_GetPaginatedLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int), args[3].(int))
	})
	return _c
}

func (_c *MockShowdownCache_GetPaginatedLeaderboard_Call) Return(leaderboardEntitys []*cache.LeaderboardEntity, err error) *MockShowdownCache_GetPaginatedLeaderboard_Call {
	_c.Call.Return(leaderboardEntitys, err)
	return _c
}

func (_c *MockShowdownCache_GetPaginatedLeaderboard_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, page int, pageSize int) ([]*cache.LeaderboardEntity, error)) *MockShowdownCache_GetPaginatedLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetPaginatedLeaderboardRev provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) GetPaginatedLeaderboardRev(ctx context.Context, showdownId primitive.ObjectID, page int, pageSize int) ([]*cache.LeaderboardEntity, error) {
	ret := _mock.Called(ctx, showdownId, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetPaginatedLeaderboardRev")
	}

	var r0 []*cache.LeaderboardEntity
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) ([]*cache.LeaderboardEntity, error)); ok {
		return returnFunc(ctx, showdownId, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) []*cache.LeaderboardEntity); ok {
		r0 = returnFunc(ctx, showdownId, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*cache.LeaderboardEntity)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int, int) error); ok {
		r1 = returnFunc(ctx, showdownId, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownCache_GetPaginatedLeaderboardRev_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPaginatedLeaderboardRev'
type MockShowdownCache_GetPaginatedLeaderboardRev_Call struct {
	*mock.Call
}

// GetPaginatedLeaderboardRev is a helper method to define mock.On call
//   - ctx
//   - showdownId
//   - page
//   - pageSize
func (_e *MockShowdownCache_Expecter) GetPaginatedLeaderboardRev(ctx interface{}, showdownId interface{}, page interface{}, pageSize interface{}) *MockShowdownCache_GetPaginatedLeaderboardRev_Call {
	return &MockShowdownCache_GetPaginatedLeaderboardRev_Call{Call: _e.mock.On("GetPaginatedLeaderboardRev", ctx, showdownId, page, pageSize)}
}

func (_c *MockShowdownCache_GetPaginatedLeaderboardRev_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, page int, pageSize int)) *MockShowdownCache_GetPaginatedLeaderboardRev_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int), args[3].(int))
	})
	return _c
}

func (_c *MockShowdownCache_GetPaginatedLeaderboardRev_Call) Return(leaderboardEntitys []*cache.LeaderboardEntity, err error) *MockShowdownCache_GetPaginatedLeaderboardRev_Call {
	_c.Call.Return(leaderboardEntitys, err)
	return _c
}

func (_c *MockShowdownCache_GetPaginatedLeaderboardRev_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, page int, pageSize int) ([]*cache.LeaderboardEntity, error)) *MockShowdownCache_GetPaginatedLeaderboardRev_Call {
	_c.Call.Return(run)
	return _c
}

// GetShowdown provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) GetShowdown(ctx context.Context, showdownId primitive.ObjectID) (*models.Showdown, error) {
	ret := _mock.Called(ctx, showdownId)

	if len(ret) == 0 {
		panic("no return value specified for GetShowdown")
	}

	var r0 *models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Showdown, error)); ok {
		return returnFunc(ctx, showdownId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Showdown); ok {
		r0 = returnFunc(ctx, showdownId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownCache_GetShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShowdown'
type MockShowdownCache_GetShowdown_Call struct {
	*mock.Call
}

// GetShowdown is a helper method to define mock.On call
//   - ctx
//   - showdownId
func (_e *MockShowdownCache_Expecter) GetShowdown(ctx interface{}, showdownId interface{}) *MockShowdownCache_GetShowdown_Call {
	return &MockShowdownCache_GetShowdown_Call{Call: _e.mock.On("GetShowdown", ctx, showdownId)}
}

func (_c *MockShowdownCache_GetShowdown_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID)) *MockShowdownCache_GetShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownCache_GetShowdown_Call) Return(showdown *models.Showdown, err error) *MockShowdownCache_GetShowdown_Call {
	_c.Call.Return(showdown, err)
	return _c
}

func (_c *MockShowdownCache_GetShowdown_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID) (*models.Showdown, error)) *MockShowdownCache_GetShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// GetShowdownParticipant provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) GetShowdownParticipant(ctx context.Context, showdownId primitive.ObjectID, userId string) (*models.ShowdownParticipant, error) {
	ret := _mock.Called(ctx, showdownId, userId)

	if len(ret) == 0 {
		panic("no return value specified for GetShowdownParticipant")
	}

	var r0 *models.ShowdownParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, string) (*models.ShowdownParticipant, error)); ok {
		return returnFunc(ctx, showdownId, userId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, string) *models.ShowdownParticipant); ok {
		r0 = returnFunc(ctx, showdownId, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ShowdownParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, string) error); ok {
		r1 = returnFunc(ctx, showdownId, userId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownCache_GetShowdownParticipant_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShowdownParticipant'
type MockShowdownCache_GetShowdownParticipant_Call struct {
	*mock.Call
}

// GetShowdownParticipant is a helper method to define mock.On call
//   - ctx
//   - showdownId
//   - userId
func (_e *MockShowdownCache_Expecter) GetShowdownParticipant(ctx interface{}, showdownId interface{}, userId interface{}) *MockShowdownCache_GetShowdownParticipant_Call {
	return &MockShowdownCache_GetShowdownParticipant_Call{Call: _e.mock.On("GetShowdownParticipant", ctx, showdownId, userId)}
}

func (_c *MockShowdownCache_GetShowdownParticipant_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, userId string)) *MockShowdownCache_GetShowdownParticipant_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(string))
	})
	return _c
}

func (_c *MockShowdownCache_GetShowdownParticipant_Call) Return(showdownParticipant *models.ShowdownParticipant, err error) *MockShowdownCache_GetShowdownParticipant_Call {
	_c.Call.Return(showdownParticipant, err)
	return _c
}

func (_c *MockShowdownCache_GetShowdownParticipant_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, userId string) (*models.ShowdownParticipant, error)) *MockShowdownCache_GetShowdownParticipant_Call {
	_c.Call.Return(run)
	return _c
}

// SetFictures provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) SetFictures(ctx context.Context, showdownId primitive.ObjectID, fictures *models.FicturesCollection) error {
	ret := _mock.Called(ctx, showdownId, fictures)

	if len(ret) == 0 {
		panic("no return value specified for SetFictures")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *models.FicturesCollection) error); ok {
		r0 = returnFunc(ctx, showdownId, fictures)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownCache_SetFictures_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetFictures'
type MockShowdownCache_SetFictures_Call struct {
	*mock.Call
}

// SetFictures is a helper method to define mock.On call
//   - ctx
//   - showdownId
//   - fictures
func (_e *MockShowdownCache_Expecter) SetFictures(ctx interface{}, showdownId interface{}, fictures interface{}) *MockShowdownCache_SetFictures_Call {
	return &MockShowdownCache_SetFictures_Call{Call: _e.mock.On("SetFictures", ctx, showdownId, fictures)}
}

func (_c *MockShowdownCache_SetFictures_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, fictures *models.FicturesCollection)) *MockShowdownCache_SetFictures_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(*models.FicturesCollection))
	})
	return _c
}

func (_c *MockShowdownCache_SetFictures_Call) Return(err error) *MockShowdownCache_SetFictures_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownCache_SetFictures_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, fictures *models.FicturesCollection) error) *MockShowdownCache_SetFictures_Call {
	_c.Call.Return(run)
	return _c
}

// SetShowdown provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) SetShowdown(ctx context.Context, showdown *models.Showdown) error {
	ret := _mock.Called(ctx, showdown)

	if len(ret) == 0 {
		panic("no return value specified for SetShowdown")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Showdown) error); ok {
		r0 = returnFunc(ctx, showdown)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownCache_SetShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetShowdown'
type MockShowdownCache_SetShowdown_Call struct {
	*mock.Call
}

// SetShowdown is a helper method to define mock.On call
//   - ctx
//   - showdown
func (_e *MockShowdownCache_Expecter) SetShowdown(ctx interface{}, showdown interface{}) *MockShowdownCache_SetShowdown_Call {
	return &MockShowdownCache_SetShowdown_Call{Call: _e.mock.On("SetShowdown", ctx, showdown)}
}

func (_c *MockShowdownCache_SetShowdown_Call) Run(run func(ctx context.Context, showdown *models.Showdown)) *MockShowdownCache_SetShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.Showdown))
	})
	return _c
}

func (_c *MockShowdownCache_SetShowdown_Call) Return(err error) *MockShowdownCache_SetShowdown_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownCache_SetShowdown_Call) RunAndReturn(run func(ctx context.Context, showdown *models.Showdown) error) *MockShowdownCache_SetShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// SetShowdownParticipant provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) SetShowdownParticipant(ctx context.Context, showdownParticipant *models.ShowdownParticipant) (int64, error) {
	ret := _mock.Called(ctx, showdownParticipant)

	if len(ret) == 0 {
		panic("no return value specified for SetShowdownParticipant")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ShowdownParticipant) (int64, error)); ok {
		return returnFunc(ctx, showdownParticipant)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ShowdownParticipant) int64); ok {
		r0 = returnFunc(ctx, showdownParticipant)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.ShowdownParticipant) error); ok {
		r1 = returnFunc(ctx, showdownParticipant)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownCache_SetShowdownParticipant_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetShowdownParticipant'
type MockShowdownCache_SetShowdownParticipant_Call struct {
	*mock.Call
}

// SetShowdownParticipant is a helper method to define mock.On call
//   - ctx
//   - showdownParticipant
func (_e *MockShowdownCache_Expecter) SetShowdownParticipant(ctx interface{}, showdownParticipant interface{}) *MockShowdownCache_SetShowdownParticipant_Call {
	return &MockShowdownCache_SetShowdownParticipant_Call{Call: _e.mock.On("SetShowdownParticipant", ctx, showdownParticipant)}
}

func (_c *MockShowdownCache_SetShowdownParticipant_Call) Run(run func(ctx context.Context, showdownParticipant *models.ShowdownParticipant)) *MockShowdownCache_SetShowdownParticipant_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.ShowdownParticipant))
	})
	return _c
}

func (_c *MockShowdownCache_SetShowdownParticipant_Call) Return(n int64, err error) *MockShowdownCache_SetShowdownParticipant_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockShowdownCache_SetShowdownParticipant_Call) RunAndReturn(run func(ctx context.Context, showdownParticipant *models.ShowdownParticipant) (int64, error)) *MockShowdownCache_SetShowdownParticipant_Call {
	_c.Call.Return(run)
	return _c
}
