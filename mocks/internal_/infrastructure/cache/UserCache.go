// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package cache

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
)

// NewMockUserCache creates a new instance of MockUserCache. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserCache(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserCache {
	mock := &MockUserCache{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockUserCache is an autogenerated mock type for the UserCache type
type MockUserCache struct {
	mock.Mock
}

type MockUserCache_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserCache) EXPECT() *MockUserCache_Expecter {
	return &MockUserCache_Expecter{mock: &_m.<PERSON>ck}
}

// DeleteUser provides a mock function for the type MockUserCache
func (_mock *MockUserCache) DeleteUser(ctx context.Context, key string) error {
	ret := _mock.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUser")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = returnFunc(ctx, key)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserCache_DeleteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUser'
type MockUserCache_DeleteUser_Call struct {
	*mock.Call
}

// DeleteUser is a helper method to define mock.On call
//   - ctx
//   - key
func (_e *MockUserCache_Expecter) DeleteUser(ctx interface{}, key interface{}) *MockUserCache_DeleteUser_Call {
	return &MockUserCache_DeleteUser_Call{Call: _e.mock.On("DeleteUser", ctx, key)}
}

func (_c *MockUserCache_DeleteUser_Call) Run(run func(ctx context.Context, key string)) *MockUserCache_DeleteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockUserCache_DeleteUser_Call) Return(err error) *MockUserCache_DeleteUser_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserCache_DeleteUser_Call) RunAndReturn(run func(ctx context.Context, key string) error) *MockUserCache_DeleteUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetUser provides a mock function for the type MockUserCache
func (_mock *MockUserCache) GetUser(ctx context.Context, key string) (*models.User, error) {
	ret := _mock.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for GetUser")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*models.User, error)); ok {
		return returnFunc(ctx, key)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *models.User); ok {
		r0 = returnFunc(ctx, key)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, key)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserCache_GetUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUser'
type MockUserCache_GetUser_Call struct {
	*mock.Call
}

// GetUser is a helper method to define mock.On call
//   - ctx
//   - key
func (_e *MockUserCache_Expecter) GetUser(ctx interface{}, key interface{}) *MockUserCache_GetUser_Call {
	return &MockUserCache_GetUser_Call{Call: _e.mock.On("GetUser", ctx, key)}
}

func (_c *MockUserCache_GetUser_Call) Run(run func(ctx context.Context, key string)) *MockUserCache_GetUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockUserCache_GetUser_Call) Return(user *models.User, err error) *MockUserCache_GetUser_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockUserCache_GetUser_Call) RunAndReturn(run func(ctx context.Context, key string) (*models.User, error)) *MockUserCache_GetUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetUsersByIDs provides a mock function for the type MockUserCache
func (_mock *MockUserCache) GetUsersByIDs(ctx context.Context, keys []string) ([]*models.User, error) {
	ret := _mock.Called(ctx, keys)

	if len(ret) == 0 {
		panic("no return value specified for GetUsersByIDs")
	}

	var r0 []*models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string) ([]*models.User, error)); ok {
		return returnFunc(ctx, keys)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string) []*models.User); ok {
		r0 = returnFunc(ctx, keys)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = returnFunc(ctx, keys)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserCache_GetUsersByIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUsersByIDs'
type MockUserCache_GetUsersByIDs_Call struct {
	*mock.Call
}

// GetUsersByIDs is a helper method to define mock.On call
//   - ctx
//   - keys
func (_e *MockUserCache_Expecter) GetUsersByIDs(ctx interface{}, keys interface{}) *MockUserCache_GetUsersByIDs_Call {
	return &MockUserCache_GetUsersByIDs_Call{Call: _e.mock.On("GetUsersByIDs", ctx, keys)}
}

func (_c *MockUserCache_GetUsersByIDs_Call) Run(run func(ctx context.Context, keys []string)) *MockUserCache_GetUsersByIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]string))
	})
	return _c
}

func (_c *MockUserCache_GetUsersByIDs_Call) Return(users []*models.User, err error) *MockUserCache_GetUsersByIDs_Call {
	_c.Call.Return(users, err)
	return _c
}

func (_c *MockUserCache_GetUsersByIDs_Call) RunAndReturn(run func(ctx context.Context, keys []string) ([]*models.User, error)) *MockUserCache_GetUsersByIDs_Call {
	_c.Call.Return(run)
	return _c
}

// SetUser provides a mock function for the type MockUserCache
func (_mock *MockUserCache) SetUser(ctx context.Context, key string, value *models.User) error {
	ret := _mock.Called(ctx, key, value)

	if len(ret) == 0 {
		panic("no return value specified for SetUser")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *models.User) error); ok {
		r0 = returnFunc(ctx, key, value)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserCache_SetUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetUser'
type MockUserCache_SetUser_Call struct {
	*mock.Call
}

// SetUser is a helper method to define mock.On call
//   - ctx
//   - key
//   - value
func (_e *MockUserCache_Expecter) SetUser(ctx interface{}, key interface{}, value interface{}) *MockUserCache_SetUser_Call {
	return &MockUserCache_SetUser_Call{Call: _e.mock.On("SetUser", ctx, key, value)}
}

func (_c *MockUserCache_SetUser_Call) Run(run func(ctx context.Context, key string, value *models.User)) *MockUserCache_SetUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*models.User))
	})
	return _c
}

func (_c *MockUserCache_SetUser_Call) Return(err error) *MockUserCache_SetUser_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserCache_SetUser_Call) RunAndReturn(run func(ctx context.Context, key string, value *models.User) error) *MockUserCache_SetUser_Call {
	_c.Call.Return(run)
	return _c
}
