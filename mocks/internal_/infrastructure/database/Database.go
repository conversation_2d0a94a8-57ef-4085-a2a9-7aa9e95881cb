// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package database

import (
	"context"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/mongo"
)

// NewMockDatabase creates a new instance of MockDatabase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDatabase(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDatabase {
	mock := &MockDatabase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockDatabase is an autogenerated mock type for the Database type
type MockDatabase struct {
	mock.Mock
}

type MockDatabase_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDatabase) EXPECT() *MockDatabase_Expecter {
	return &MockDatabase_Expecter{mock: &_m.Mock}
}

// Collection provides a mock function for the type MockDatabase
func (_mock *MockDatabase) Collection(s string) *mongo.Collection {
	ret := _mock.Called(s)

	if len(ret) == 0 {
		panic("no return value specified for Collection")
	}

	var r0 *mongo.Collection
	if returnFunc, ok := ret.Get(0).(func(string) *mongo.Collection); ok {
		r0 = returnFunc(s)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.Collection)
		}
	}
	return r0
}

// MockDatabase_Collection_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Collection'
type MockDatabase_Collection_Call struct {
	*mock.Call
}

// Collection is a helper method to define mock.On call
//   - s
func (_e *MockDatabase_Expecter) Collection(s interface{}) *MockDatabase_Collection_Call {
	return &MockDatabase_Collection_Call{Call: _e.mock.On("Collection", s)}
}

func (_c *MockDatabase_Collection_Call) Run(run func(s string)) *MockDatabase_Collection_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockDatabase_Collection_Call) Return(collection *mongo.Collection) *MockDatabase_Collection_Call {
	_c.Call.Return(collection)
	return _c
}

func (_c *MockDatabase_Collection_Call) RunAndReturn(run func(s string) *mongo.Collection) *MockDatabase_Collection_Call {
	_c.Call.Return(run)
	return _c
}

// Ping provides a mock function for the type MockDatabase
func (_mock *MockDatabase) Ping(context1 context.Context) error {
	ret := _mock.Called(context1)

	if len(ret) == 0 {
		panic("no return value specified for Ping")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = returnFunc(context1)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDatabase_Ping_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Ping'
type MockDatabase_Ping_Call struct {
	*mock.Call
}

// Ping is a helper method to define mock.On call
//   - context1
func (_e *MockDatabase_Expecter) Ping(context1 interface{}) *MockDatabase_Ping_Call {
	return &MockDatabase_Ping_Call{Call: _e.mock.On("Ping", context1)}
}

func (_c *MockDatabase_Ping_Call) Run(run func(context1 context.Context)) *MockDatabase_Ping_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockDatabase_Ping_Call) Return(err error) *MockDatabase_Ping_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDatabase_Ping_Call) RunAndReturn(run func(context1 context.Context) error) *MockDatabase_Ping_Call {
	_c.Call.Return(run)
	return _c
}
