// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package firebase

import (
	"context"

	"firebase.google.com/go/v4/messaging"
	mock "github.com/stretchr/testify/mock"
)

// NewMockApp creates a new instance of <PERSON>ck<PERSON><PERSON>. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockApp(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockApp {
	mock := &MockApp{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockApp is an autogenerated mock type for the App type
type MockApp struct {
	mock.Mock
}

type MockApp_Expecter struct {
	mock *mock.Mock
}

func (_m *MockApp) EXPECT() *MockApp_Expecter {
	return &MockApp_Expecter{mock: &_m.Mock}
}

// Messaging provides a mock function for the type MockApp
func (_mock *MockApp) Messaging(ctx context.Context) (*messaging.Client, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Messaging")
	}

	var r0 *messaging.Client
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*messaging.Client, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *messaging.Client); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*messaging.Client)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockApp_Messaging_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Messaging'
type MockApp_Messaging_Call struct {
	*mock.Call
}

// Messaging is a helper method to define mock.On call
//   - ctx
func (_e *MockApp_Expecter) Messaging(ctx interface{}) *MockApp_Messaging_Call {
	return &MockApp_Messaging_Call{Call: _e.mock.On("Messaging", ctx)}
}

func (_c *MockApp_Messaging_Call) Run(run func(ctx context.Context)) *MockApp_Messaging_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockApp_Messaging_Call) Return(client *messaging.Client, err error) *MockApp_Messaging_Call {
	_c.Call.Return(client, err)
	return _c
}

func (_c *MockApp_Messaging_Call) RunAndReturn(run func(ctx context.Context) (*messaging.Client, error)) *MockApp_Messaging_Call {
	_c.Call.Return(run)
	return _c
}
