// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package firebase

import (
	"context"

	"firebase.google.com/go/v4/messaging"
	mock "github.com/stretchr/testify/mock"
)

// NewMockFirebaseClient creates a new instance of MockFirebaseClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFirebaseClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFirebaseClient {
	mock := &MockFirebaseClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockFirebaseClient is an autogenerated mock type for the FirebaseClient type
type MockFirebaseClient struct {
	mock.Mock
}

type MockFirebaseClient_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFirebaseClient) EXPECT() *MockFirebaseClient_Expecter {
	return &MockFirebaseClient_Expecter{mock: &_m.Mock}
}

// Send provides a mock function for the type MockFirebaseClient
func (_mock *MockFirebaseClient) Send(ctx context.Context, message *messaging.Message) error {
	ret := _mock.Called(ctx, message)

	if len(ret) == 0 {
		panic("no return value specified for Send")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *messaging.Message) error); ok {
		r0 = returnFunc(ctx, message)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockFirebaseClient_Send_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Send'
type MockFirebaseClient_Send_Call struct {
	*mock.Call
}

// Send is a helper method to define mock.On call
//   - ctx
//   - message
func (_e *MockFirebaseClient_Expecter) Send(ctx interface{}, message interface{}) *MockFirebaseClient_Send_Call {
	return &MockFirebaseClient_Send_Call{Call: _e.mock.On("Send", ctx, message)}
}

func (_c *MockFirebaseClient_Send_Call) Run(run func(ctx context.Context, message *messaging.Message)) *MockFirebaseClient_Send_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*messaging.Message))
	})
	return _c
}

func (_c *MockFirebaseClient_Send_Call) Return(err error) *MockFirebaseClient_Send_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockFirebaseClient_Send_Call) RunAndReturn(run func(ctx context.Context, message *messaging.Message) error) *MockFirebaseClient_Send_Call {
	_c.Call.Return(run)
	return _c
}

// SendBatch provides a mock function for the type MockFirebaseClient
func (_mock *MockFirebaseClient) SendBatch(ctx context.Context, messages []*messaging.Message) error {
	ret := _mock.Called(ctx, messages)

	if len(ret) == 0 {
		panic("no return value specified for SendBatch")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*messaging.Message) error); ok {
		r0 = returnFunc(ctx, messages)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockFirebaseClient_SendBatch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendBatch'
type MockFirebaseClient_SendBatch_Call struct {
	*mock.Call
}

// SendBatch is a helper method to define mock.On call
//   - ctx
//   - messages
func (_e *MockFirebaseClient_Expecter) SendBatch(ctx interface{}, messages interface{}) *MockFirebaseClient_SendBatch_Call {
	return &MockFirebaseClient_SendBatch_Call{Call: _e.mock.On("SendBatch", ctx, messages)}
}

func (_c *MockFirebaseClient_SendBatch_Call) Run(run func(ctx context.Context, messages []*messaging.Message)) *MockFirebaseClient_SendBatch_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*messaging.Message))
	})
	return _c
}

func (_c *MockFirebaseClient_SendBatch_Call) Return(err error) *MockFirebaseClient_SendBatch_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockFirebaseClient_SendBatch_Call) RunAndReturn(run func(ctx context.Context, messages []*messaging.Message) error) *MockFirebaseClient_SendBatch_Call {
	_c.Call.Return(run)
	return _c
}
