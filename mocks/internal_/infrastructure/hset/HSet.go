// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package hset

import (
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewMockHSet creates a new instance of MockHSet. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockHSet(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockHSet {
	mock := &MockHSet{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockHSet is an autogenerated mock type for the HSet type
type MockHSet struct {
	mock.Mock
}

type MockHSet_Expecter struct {
	mock *mock.Mock
}

func (_m *MockHSet) EXPECT() *MockHSet_Expecter {
	return &MockHSet_Expecter{mock: &_m.Mock}
}

// DeleteAll provides a mock function for the type MockHSet
func (_mock *MockHSet) DeleteAll(ctx context.Context, keys ...string) error {
	var tmpRet mock.Arguments
	if len(keys) > 0 {
		tmpRet = _mock.Called(ctx, keys)
	} else {
		tmpRet = _mock.Called(ctx)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for DeleteAll")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, ...string) error); ok {
		r0 = returnFunc(ctx, keys...)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockHSet_DeleteAll_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAll'
type MockHSet_DeleteAll_Call struct {
	*mock.Call
}

// DeleteAll is a helper method to define mock.On call
//   - ctx
//   - keys
func (_e *MockHSet_Expecter) DeleteAll(ctx interface{}, keys ...interface{}) *MockHSet_DeleteAll_Call {
	return &MockHSet_DeleteAll_Call{Call: _e.mock.On("DeleteAll",
		append([]interface{}{ctx}, keys...)...)}
}

func (_c *MockHSet_DeleteAll_Call) Run(run func(ctx context.Context, keys ...string)) *MockHSet_DeleteAll_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[1].([]string)
		run(args[0].(context.Context), variadicArgs...)
	})
	return _c
}

func (_c *MockHSet_DeleteAll_Call) Return(err error) *MockHSet_DeleteAll_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockHSet_DeleteAll_Call) RunAndReturn(run func(ctx context.Context, keys ...string) error) *MockHSet_DeleteAll_Call {
	_c.Call.Return(run)
	return _c
}

// HGet provides a mock function for the type MockHSet
func (_mock *MockHSet) HGet(ctx context.Context, key string, field string) (string, error) {
	ret := _mock.Called(ctx, key, field)

	if len(ret) == 0 {
		panic("no return value specified for HGet")
	}

	var r0 string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) (string, error)); ok {
		return returnFunc(ctx, key, field)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) string); ok {
		r0 = returnFunc(ctx, key, field)
	} else {
		r0 = ret.Get(0).(string)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = returnFunc(ctx, key, field)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockHSet_HGet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HGet'
type MockHSet_HGet_Call struct {
	*mock.Call
}

// HGet is a helper method to define mock.On call
//   - ctx
//   - key
//   - field
func (_e *MockHSet_Expecter) HGet(ctx interface{}, key interface{}, field interface{}) *MockHSet_HGet_Call {
	return &MockHSet_HGet_Call{Call: _e.mock.On("HGet", ctx, key, field)}
}

func (_c *MockHSet_HGet_Call) Run(run func(ctx context.Context, key string, field string)) *MockHSet_HGet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockHSet_HGet_Call) Return(s string, err error) *MockHSet_HGet_Call {
	_c.Call.Return(s, err)
	return _c
}

func (_c *MockHSet_HGet_Call) RunAndReturn(run func(ctx context.Context, key string, field string) (string, error)) *MockHSet_HGet_Call {
	_c.Call.Return(run)
	return _c
}

// HGetAll provides a mock function for the type MockHSet
func (_mock *MockHSet) HGetAll(ctx context.Context, key string) (map[string]string, error) {
	ret := _mock.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for HGetAll")
	}

	var r0 map[string]string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (map[string]string, error)); ok {
		return returnFunc(ctx, key)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) map[string]string); ok {
		r0 = returnFunc(ctx, key)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]string)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, key)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockHSet_HGetAll_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HGetAll'
type MockHSet_HGetAll_Call struct {
	*mock.Call
}

// HGetAll is a helper method to define mock.On call
//   - ctx
//   - key
func (_e *MockHSet_Expecter) HGetAll(ctx interface{}, key interface{}) *MockHSet_HGetAll_Call {
	return &MockHSet_HGetAll_Call{Call: _e.mock.On("HGetAll", ctx, key)}
}

func (_c *MockHSet_HGetAll_Call) Run(run func(ctx context.Context, key string)) *MockHSet_HGetAll_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockHSet_HGetAll_Call) Return(stringToString map[string]string, err error) *MockHSet_HGetAll_Call {
	_c.Call.Return(stringToString, err)
	return _c
}

func (_c *MockHSet_HGetAll_Call) RunAndReturn(run func(ctx context.Context, key string) (map[string]string, error)) *MockHSet_HGetAll_Call {
	_c.Call.Return(run)
	return _c
}

// HIncrBy provides a mock function for the type MockHSet
func (_mock *MockHSet) HIncrBy(ctx context.Context, key string, field string, value int64) error {
	ret := _mock.Called(ctx, key, field, value)

	if len(ret) == 0 {
		panic("no return value specified for HIncrBy")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, int64) error); ok {
		r0 = returnFunc(ctx, key, field, value)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockHSet_HIncrBy_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HIncrBy'
type MockHSet_HIncrBy_Call struct {
	*mock.Call
}

// HIncrBy is a helper method to define mock.On call
//   - ctx
//   - key
//   - field
//   - value
func (_e *MockHSet_Expecter) HIncrBy(ctx interface{}, key interface{}, field interface{}, value interface{}) *MockHSet_HIncrBy_Call {
	return &MockHSet_HIncrBy_Call{Call: _e.mock.On("HIncrBy", ctx, key, field, value)}
}

func (_c *MockHSet_HIncrBy_Call) Run(run func(ctx context.Context, key string, field string, value int64)) *MockHSet_HIncrBy_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(int64))
	})
	return _c
}

func (_c *MockHSet_HIncrBy_Call) Return(err error) *MockHSet_HIncrBy_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockHSet_HIncrBy_Call) RunAndReturn(run func(ctx context.Context, key string, field string, value int64) error) *MockHSet_HIncrBy_Call {
	_c.Call.Return(run)
	return _c
}

// HMGet provides a mock function for the type MockHSet
func (_mock *MockHSet) HMGet(ctx context.Context, key string, fields ...string) ([]interface{}, error) {
	var tmpRet mock.Arguments
	if len(fields) > 0 {
		tmpRet = _mock.Called(ctx, key, fields)
	} else {
		tmpRet = _mock.Called(ctx, key)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for HMGet")
	}

	var r0 []interface{}
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, ...string) ([]interface{}, error)); ok {
		return returnFunc(ctx, key, fields...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, ...string) []interface{}); ok {
		r0 = returnFunc(ctx, key, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]interface{})
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, ...string) error); ok {
		r1 = returnFunc(ctx, key, fields...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockHSet_HMGet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HMGet'
type MockHSet_HMGet_Call struct {
	*mock.Call
}

// HMGet is a helper method to define mock.On call
//   - ctx
//   - key
//   - fields
func (_e *MockHSet_Expecter) HMGet(ctx interface{}, key interface{}, fields ...interface{}) *MockHSet_HMGet_Call {
	return &MockHSet_HMGet_Call{Call: _e.mock.On("HMGet",
		append([]interface{}{ctx, key}, fields...)...)}
}

func (_c *MockHSet_HMGet_Call) Run(run func(ctx context.Context, key string, fields ...string)) *MockHSet_HMGet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]string)
		run(args[0].(context.Context), args[1].(string), variadicArgs...)
	})
	return _c
}

func (_c *MockHSet_HMGet_Call) Return(ifaceVals []interface{}, err error) *MockHSet_HMGet_Call {
	_c.Call.Return(ifaceVals, err)
	return _c
}

func (_c *MockHSet_HMGet_Call) RunAndReturn(run func(ctx context.Context, key string, fields ...string) ([]interface{}, error)) *MockHSet_HMGet_Call {
	_c.Call.Return(run)
	return _c
}

// HSet provides a mock function for the type MockHSet
func (_mock *MockHSet) HSet(ctx context.Context, key string, field string, value interface{}) error {
	ret := _mock.Called(ctx, key, field, value)

	if len(ret) == 0 {
		panic("no return value specified for HSet")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, interface{}) error); ok {
		r0 = returnFunc(ctx, key, field, value)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockHSet_HSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HSet'
type MockHSet_HSet_Call struct {
	*mock.Call
}

// HSet is a helper method to define mock.On call
//   - ctx
//   - key
//   - field
//   - value
func (_e *MockHSet_Expecter) HSet(ctx interface{}, key interface{}, field interface{}, value interface{}) *MockHSet_HSet_Call {
	return &MockHSet_HSet_Call{Call: _e.mock.On("HSet", ctx, key, field, value)}
}

func (_c *MockHSet_HSet_Call) Run(run func(ctx context.Context, key string, field string, value interface{})) *MockHSet_HSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(interface{}))
	})
	return _c
}

func (_c *MockHSet_HSet_Call) Return(err error) *MockHSet_HSet_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockHSet_HSet_Call) RunAndReturn(run func(ctx context.Context, key string, field string, value interface{}) error) *MockHSet_HSet_Call {
	_c.Call.Return(run)
	return _c
}

// IncrBy provides a mock function for the type MockHSet
func (_mock *MockHSet) IncrBy(ctx context.Context, key string, value int64) error {
	ret := _mock.Called(ctx, key, value)

	if len(ret) == 0 {
		panic("no return value specified for IncrBy")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64) error); ok {
		r0 = returnFunc(ctx, key, value)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockHSet_IncrBy_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IncrBy'
type MockHSet_IncrBy_Call struct {
	*mock.Call
}

// IncrBy is a helper method to define mock.On call
//   - ctx
//   - key
//   - value
func (_e *MockHSet_Expecter) IncrBy(ctx interface{}, key interface{}, value interface{}) *MockHSet_IncrBy_Call {
	return &MockHSet_IncrBy_Call{Call: _e.mock.On("IncrBy", ctx, key, value)}
}

func (_c *MockHSet_IncrBy_Call) Run(run func(ctx context.Context, key string, value int64)) *MockHSet_IncrBy_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int64))
	})
	return _c
}

func (_c *MockHSet_IncrBy_Call) Return(err error) *MockHSet_IncrBy_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockHSet_IncrBy_Call) RunAndReturn(run func(ctx context.Context, key string, value int64) error) *MockHSet_IncrBy_Call {
	_c.Call.Return(run)
	return _c
}
