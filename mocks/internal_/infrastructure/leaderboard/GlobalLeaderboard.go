// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package leaderboard

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
)

// NewMockGlobalLeaderboard creates a new instance of MockGlobalLeaderboard. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockGlobalLeaderboard(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockGlobalLeaderboard {
	mock := &MockGlobalLeaderboard{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockGlobalLeaderboard is an autogenerated mock type for the GlobalLeaderboard type
type MockGlobalLeaderboard struct {
	mock.Mock
}

type MockGlobalLeaderboard_Expecter struct {
	mock *mock.Mock
}

func (_m *MockGlobalLeaderboard) EXPECT() *MockGlobalLeaderboard_Expecter {
	return &MockGlobalLeaderboard_Expecter{mock: &_m.Mock}
}

// AddUser provides a mock function for the type MockGlobalLeaderboard
func (_mock *MockGlobalLeaderboard) AddUser(ctx context.Context, userRating int) error {
	ret := _mock.Called(ctx, userRating)

	if len(ret) == 0 {
		panic("no return value specified for AddUser")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int) error); ok {
		r0 = returnFunc(ctx, userRating)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockGlobalLeaderboard_AddUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddUser'
type MockGlobalLeaderboard_AddUser_Call struct {
	*mock.Call
}

// AddUser is a helper method to define mock.On call
//   - ctx
//   - userRating
func (_e *MockGlobalLeaderboard_Expecter) AddUser(ctx interface{}, userRating interface{}) *MockGlobalLeaderboard_AddUser_Call {
	return &MockGlobalLeaderboard_AddUser_Call{Call: _e.mock.On("AddUser", ctx, userRating)}
}

func (_c *MockGlobalLeaderboard_AddUser_Call) Run(run func(ctx context.Context, userRating int)) *MockGlobalLeaderboard_AddUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int))
	})
	return _c
}

func (_c *MockGlobalLeaderboard_AddUser_Call) Return(err error) *MockGlobalLeaderboard_AddUser_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockGlobalLeaderboard_AddUser_Call) RunAndReturn(run func(ctx context.Context, userRating int) error) *MockGlobalLeaderboard_AddUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetLeaderboard provides a mock function for the type MockGlobalLeaderboard
func (_mock *MockGlobalLeaderboard) GetLeaderboard(ctx context.Context, params models.LeaderboardParams) (*models.UserLeaderboardPage, error) {
	ret := _mock.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetLeaderboard")
	}

	var r0 *models.UserLeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.LeaderboardParams) (*models.UserLeaderboardPage, error)); ok {
		return returnFunc(ctx, params)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.LeaderboardParams) *models.UserLeaderboardPage); ok {
		r0 = returnFunc(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserLeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.LeaderboardParams) error); ok {
		r1 = returnFunc(ctx, params)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGlobalLeaderboard_GetLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLeaderboard'
type MockGlobalLeaderboard_GetLeaderboard_Call struct {
	*mock.Call
}

// GetLeaderboard is a helper method to define mock.On call
//   - ctx
//   - params
func (_e *MockGlobalLeaderboard_Expecter) GetLeaderboard(ctx interface{}, params interface{}) *MockGlobalLeaderboard_GetLeaderboard_Call {
	return &MockGlobalLeaderboard_GetLeaderboard_Call{Call: _e.mock.On("GetLeaderboard", ctx, params)}
}

func (_c *MockGlobalLeaderboard_GetLeaderboard_Call) Run(run func(ctx context.Context, params models.LeaderboardParams)) *MockGlobalLeaderboard_GetLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.LeaderboardParams))
	})
	return _c
}

func (_c *MockGlobalLeaderboard_GetLeaderboard_Call) Return(userLeaderboardPage *models.UserLeaderboardPage, err error) *MockGlobalLeaderboard_GetLeaderboard_Call {
	_c.Call.Return(userLeaderboardPage, err)
	return _c
}

func (_c *MockGlobalLeaderboard_GetLeaderboard_Call) RunAndReturn(run func(ctx context.Context, params models.LeaderboardParams) (*models.UserLeaderboardPage, error)) *MockGlobalLeaderboard_GetLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserRank provides a mock function for the type MockGlobalLeaderboard
func (_mock *MockGlobalLeaderboard) GetUserRank(ctx context.Context, userRating int) (int64, error) {
	ret := _mock.Called(ctx, userRating)

	if len(ret) == 0 {
		panic("no return value specified for GetUserRank")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int) (int64, error)); ok {
		return returnFunc(ctx, userRating)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int) int64); ok {
		r0 = returnFunc(ctx, userRating)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int) error); ok {
		r1 = returnFunc(ctx, userRating)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGlobalLeaderboard_GetUserRank_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserRank'
type MockGlobalLeaderboard_GetUserRank_Call struct {
	*mock.Call
}

// GetUserRank is a helper method to define mock.On call
//   - ctx
//   - userRating
func (_e *MockGlobalLeaderboard_Expecter) GetUserRank(ctx interface{}, userRating interface{}) *MockGlobalLeaderboard_GetUserRank_Call {
	return &MockGlobalLeaderboard_GetUserRank_Call{Call: _e.mock.On("GetUserRank", ctx, userRating)}
}

func (_c *MockGlobalLeaderboard_GetUserRank_Call) Run(run func(ctx context.Context, userRating int)) *MockGlobalLeaderboard_GetUserRank_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int))
	})
	return _c
}

func (_c *MockGlobalLeaderboard_GetUserRank_Call) Return(n int64, err error) *MockGlobalLeaderboard_GetUserRank_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockGlobalLeaderboard_GetUserRank_Call) RunAndReturn(run func(ctx context.Context, userRating int) (int64, error)) *MockGlobalLeaderboard_GetUserRank_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserRating provides a mock function for the type MockGlobalLeaderboard
func (_mock *MockGlobalLeaderboard) UpdateUserRating(ctx context.Context, oldRating *int, newRating int) error {
	ret := _mock.Called(ctx, oldRating, newRating)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserRating")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, int) error); ok {
		r0 = returnFunc(ctx, oldRating, newRating)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockGlobalLeaderboard_UpdateUserRating_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserRating'
type MockGlobalLeaderboard_UpdateUserRating_Call struct {
	*mock.Call
}

// UpdateUserRating is a helper method to define mock.On call
//   - ctx
//   - oldRating
//   - newRating
func (_e *MockGlobalLeaderboard_Expecter) UpdateUserRating(ctx interface{}, oldRating interface{}, newRating interface{}) *MockGlobalLeaderboard_UpdateUserRating_Call {
	return &MockGlobalLeaderboard_UpdateUserRating_Call{Call: _e.mock.On("UpdateUserRating", ctx, oldRating, newRating)}
}

func (_c *MockGlobalLeaderboard_UpdateUserRating_Call) Run(run func(ctx context.Context, oldRating *int, newRating int)) *MockGlobalLeaderboard_UpdateUserRating_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*int), args[2].(int))
	})
	return _c
}

func (_c *MockGlobalLeaderboard_UpdateUserRating_Call) Return(err error) *MockGlobalLeaderboard_UpdateUserRating_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockGlobalLeaderboard_UpdateUserRating_Call) RunAndReturn(run func(ctx context.Context, oldRating *int, newRating int) error) *MockGlobalLeaderboard_UpdateUserRating_Call {
	_c.Call.Return(run)
	return _c
}
