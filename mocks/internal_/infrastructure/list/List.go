// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package list

import (
	"context"
	"time"

	"github.com/redis/go-redis/v9"
	mock "github.com/stretchr/testify/mock"
)

// NewMockList creates a new instance of <PERSON>ck<PERSON>ist. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockList(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockList {
	mock := &MockList{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockList is an autogenerated mock type for the List type
type MockList struct {
	mock.Mock
}

type MockList_Expecter struct {
	mock *mock.Mock
}

func (_m *MockList) EXPECT() *MockList_Expecter {
	return &MockList_Expecter{mock: &_m.Mock}
}

// BLPop provides a mock function for the type MockList
func (_mock *MockList) BLPop(ctx context.Context, timeout time.Duration, keys ...string) ([]string, error) {
	var tmpRet mock.Arguments
	if len(keys) > 0 {
		tmpRet = _mock.Called(ctx, timeout, keys)
	} else {
		tmpRet = _mock.Called(ctx, timeout)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for BLPop")
	}

	var r0 []string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, time.Duration, ...string) ([]string, error)); ok {
		return returnFunc(ctx, timeout, keys...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, time.Duration, ...string) []string); ok {
		r0 = returnFunc(ctx, timeout, keys...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, time.Duration, ...string) error); ok {
		r1 = returnFunc(ctx, timeout, keys...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockList_BLPop_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BLPop'
type MockList_BLPop_Call struct {
	*mock.Call
}

// BLPop is a helper method to define mock.On call
//   - ctx
//   - timeout
//   - keys
func (_e *MockList_Expecter) BLPop(ctx interface{}, timeout interface{}, keys ...interface{}) *MockList_BLPop_Call {
	return &MockList_BLPop_Call{Call: _e.mock.On("BLPop",
		append([]interface{}{ctx, timeout}, keys...)...)}
}

func (_c *MockList_BLPop_Call) Run(run func(ctx context.Context, timeout time.Duration, keys ...string)) *MockList_BLPop_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]string)
		run(args[0].(context.Context), args[1].(time.Duration), variadicArgs...)
	})
	return _c
}

func (_c *MockList_BLPop_Call) Return(strings []string, err error) *MockList_BLPop_Call {
	_c.Call.Return(strings, err)
	return _c
}

func (_c *MockList_BLPop_Call) RunAndReturn(run func(ctx context.Context, timeout time.Duration, keys ...string) ([]string, error)) *MockList_BLPop_Call {
	_c.Call.Return(run)
	return _c
}

// BRPop provides a mock function for the type MockList
func (_mock *MockList) BRPop(ctx context.Context, timeout time.Duration, keys ...string) ([]string, error) {
	var tmpRet mock.Arguments
	if len(keys) > 0 {
		tmpRet = _mock.Called(ctx, timeout, keys)
	} else {
		tmpRet = _mock.Called(ctx, timeout)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for BRPop")
	}

	var r0 []string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, time.Duration, ...string) ([]string, error)); ok {
		return returnFunc(ctx, timeout, keys...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, time.Duration, ...string) []string); ok {
		r0 = returnFunc(ctx, timeout, keys...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, time.Duration, ...string) error); ok {
		r1 = returnFunc(ctx, timeout, keys...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockList_BRPop_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BRPop'
type MockList_BRPop_Call struct {
	*mock.Call
}

// BRPop is a helper method to define mock.On call
//   - ctx
//   - timeout
//   - keys
func (_e *MockList_Expecter) BRPop(ctx interface{}, timeout interface{}, keys ...interface{}) *MockList_BRPop_Call {
	return &MockList_BRPop_Call{Call: _e.mock.On("BRPop",
		append([]interface{}{ctx, timeout}, keys...)...)}
}

func (_c *MockList_BRPop_Call) Run(run func(ctx context.Context, timeout time.Duration, keys ...string)) *MockList_BRPop_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]string)
		run(args[0].(context.Context), args[1].(time.Duration), variadicArgs...)
	})
	return _c
}

func (_c *MockList_BRPop_Call) Return(strings []string, err error) *MockList_BRPop_Call {
	_c.Call.Return(strings, err)
	return _c
}

func (_c *MockList_BRPop_Call) RunAndReturn(run func(ctx context.Context, timeout time.Duration, keys ...string) ([]string, error)) *MockList_BRPop_Call {
	_c.Call.Return(run)
	return _c
}

// LIndex provides a mock function for the type MockList
func (_mock *MockList) LIndex(ctx context.Context, key string, index int64) (string, error) {
	ret := _mock.Called(ctx, key, index)

	if len(ret) == 0 {
		panic("no return value specified for LIndex")
	}

	var r0 string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64) (string, error)); ok {
		return returnFunc(ctx, key, index)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64) string); ok {
		r0 = returnFunc(ctx, key, index)
	} else {
		r0 = ret.Get(0).(string)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, int64) error); ok {
		r1 = returnFunc(ctx, key, index)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockList_LIndex_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LIndex'
type MockList_LIndex_Call struct {
	*mock.Call
}

// LIndex is a helper method to define mock.On call
//   - ctx
//   - key
//   - index
func (_e *MockList_Expecter) LIndex(ctx interface{}, key interface{}, index interface{}) *MockList_LIndex_Call {
	return &MockList_LIndex_Call{Call: _e.mock.On("LIndex", ctx, key, index)}
}

func (_c *MockList_LIndex_Call) Run(run func(ctx context.Context, key string, index int64)) *MockList_LIndex_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int64))
	})
	return _c
}

func (_c *MockList_LIndex_Call) Return(s string, err error) *MockList_LIndex_Call {
	_c.Call.Return(s, err)
	return _c
}

func (_c *MockList_LIndex_Call) RunAndReturn(run func(ctx context.Context, key string, index int64) (string, error)) *MockList_LIndex_Call {
	_c.Call.Return(run)
	return _c
}

// LInsertAfter provides a mock function for the type MockList
func (_mock *MockList) LInsertAfter(ctx context.Context, key string, pivot interface{}, value interface{}) (int64, error) {
	ret := _mock.Called(ctx, key, pivot, value)

	if len(ret) == 0 {
		panic("no return value specified for LInsertAfter")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, interface{}, interface{}) (int64, error)); ok {
		return returnFunc(ctx, key, pivot, value)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, interface{}, interface{}) int64); ok {
		r0 = returnFunc(ctx, key, pivot, value)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, interface{}, interface{}) error); ok {
		r1 = returnFunc(ctx, key, pivot, value)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockList_LInsertAfter_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LInsertAfter'
type MockList_LInsertAfter_Call struct {
	*mock.Call
}

// LInsertAfter is a helper method to define mock.On call
//   - ctx
//   - key
//   - pivot
//   - value
func (_e *MockList_Expecter) LInsertAfter(ctx interface{}, key interface{}, pivot interface{}, value interface{}) *MockList_LInsertAfter_Call {
	return &MockList_LInsertAfter_Call{Call: _e.mock.On("LInsertAfter", ctx, key, pivot, value)}
}

func (_c *MockList_LInsertAfter_Call) Run(run func(ctx context.Context, key string, pivot interface{}, value interface{})) *MockList_LInsertAfter_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(interface{}), args[3].(interface{}))
	})
	return _c
}

func (_c *MockList_LInsertAfter_Call) Return(n int64, err error) *MockList_LInsertAfter_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockList_LInsertAfter_Call) RunAndReturn(run func(ctx context.Context, key string, pivot interface{}, value interface{}) (int64, error)) *MockList_LInsertAfter_Call {
	_c.Call.Return(run)
	return _c
}

// LInsertBefore provides a mock function for the type MockList
func (_mock *MockList) LInsertBefore(ctx context.Context, key string, pivot interface{}, value interface{}) (int64, error) {
	ret := _mock.Called(ctx, key, pivot, value)

	if len(ret) == 0 {
		panic("no return value specified for LInsertBefore")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, interface{}, interface{}) (int64, error)); ok {
		return returnFunc(ctx, key, pivot, value)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, interface{}, interface{}) int64); ok {
		r0 = returnFunc(ctx, key, pivot, value)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, interface{}, interface{}) error); ok {
		r1 = returnFunc(ctx, key, pivot, value)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockList_LInsertBefore_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LInsertBefore'
type MockList_LInsertBefore_Call struct {
	*mock.Call
}

// LInsertBefore is a helper method to define mock.On call
//   - ctx
//   - key
//   - pivot
//   - value
func (_e *MockList_Expecter) LInsertBefore(ctx interface{}, key interface{}, pivot interface{}, value interface{}) *MockList_LInsertBefore_Call {
	return &MockList_LInsertBefore_Call{Call: _e.mock.On("LInsertBefore", ctx, key, pivot, value)}
}

func (_c *MockList_LInsertBefore_Call) Run(run func(ctx context.Context, key string, pivot interface{}, value interface{})) *MockList_LInsertBefore_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(interface{}), args[3].(interface{}))
	})
	return _c
}

func (_c *MockList_LInsertBefore_Call) Return(n int64, err error) *MockList_LInsertBefore_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockList_LInsertBefore_Call) RunAndReturn(run func(ctx context.Context, key string, pivot interface{}, value interface{}) (int64, error)) *MockList_LInsertBefore_Call {
	_c.Call.Return(run)
	return _c
}

// LLen provides a mock function for the type MockList
func (_mock *MockList) LLen(ctx context.Context, key string) (int64, error) {
	ret := _mock.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for LLen")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (int64, error)); ok {
		return returnFunc(ctx, key)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) int64); ok {
		r0 = returnFunc(ctx, key)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, key)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockList_LLen_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LLen'
type MockList_LLen_Call struct {
	*mock.Call
}

// LLen is a helper method to define mock.On call
//   - ctx
//   - key
func (_e *MockList_Expecter) LLen(ctx interface{}, key interface{}) *MockList_LLen_Call {
	return &MockList_LLen_Call{Call: _e.mock.On("LLen", ctx, key)}
}

func (_c *MockList_LLen_Call) Run(run func(ctx context.Context, key string)) *MockList_LLen_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockList_LLen_Call) Return(n int64, err error) *MockList_LLen_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockList_LLen_Call) RunAndReturn(run func(ctx context.Context, key string) (int64, error)) *MockList_LLen_Call {
	_c.Call.Return(run)
	return _c
}

// LPop provides a mock function for the type MockList
func (_mock *MockList) LPop(ctx context.Context, key string) (string, error) {
	ret := _mock.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for LPop")
	}

	var r0 string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (string, error)); ok {
		return returnFunc(ctx, key)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) string); ok {
		r0 = returnFunc(ctx, key)
	} else {
		r0 = ret.Get(0).(string)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, key)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockList_LPop_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LPop'
type MockList_LPop_Call struct {
	*mock.Call
}

// LPop is a helper method to define mock.On call
//   - ctx
//   - key
func (_e *MockList_Expecter) LPop(ctx interface{}, key interface{}) *MockList_LPop_Call {
	return &MockList_LPop_Call{Call: _e.mock.On("LPop", ctx, key)}
}

func (_c *MockList_LPop_Call) Run(run func(ctx context.Context, key string)) *MockList_LPop_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockList_LPop_Call) Return(s string, err error) *MockList_LPop_Call {
	_c.Call.Return(s, err)
	return _c
}

func (_c *MockList_LPop_Call) RunAndReturn(run func(ctx context.Context, key string) (string, error)) *MockList_LPop_Call {
	_c.Call.Return(run)
	return _c
}

// LPopCount provides a mock function for the type MockList
func (_mock *MockList) LPopCount(ctx context.Context, key string, count int) ([]string, error) {
	ret := _mock.Called(ctx, key, count)

	if len(ret) == 0 {
		panic("no return value specified for LPopCount")
	}

	var r0 []string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int) ([]string, error)); ok {
		return returnFunc(ctx, key, count)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int) []string); ok {
		r0 = returnFunc(ctx, key, count)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, int) error); ok {
		r1 = returnFunc(ctx, key, count)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockList_LPopCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LPopCount'
type MockList_LPopCount_Call struct {
	*mock.Call
}

// LPopCount is a helper method to define mock.On call
//   - ctx
//   - key
//   - count
func (_e *MockList_Expecter) LPopCount(ctx interface{}, key interface{}, count interface{}) *MockList_LPopCount_Call {
	return &MockList_LPopCount_Call{Call: _e.mock.On("LPopCount", ctx, key, count)}
}

func (_c *MockList_LPopCount_Call) Run(run func(ctx context.Context, key string, count int)) *MockList_LPopCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int))
	})
	return _c
}

func (_c *MockList_LPopCount_Call) Return(strings []string, err error) *MockList_LPopCount_Call {
	_c.Call.Return(strings, err)
	return _c
}

func (_c *MockList_LPopCount_Call) RunAndReturn(run func(ctx context.Context, key string, count int) ([]string, error)) *MockList_LPopCount_Call {
	_c.Call.Return(run)
	return _c
}

// LPos provides a mock function for the type MockList
func (_mock *MockList) LPos(ctx context.Context, key string, value string, args redis.LPosArgs) (int64, error) {
	ret := _mock.Called(ctx, key, value, args)

	if len(ret) == 0 {
		panic("no return value specified for LPos")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, redis.LPosArgs) (int64, error)); ok {
		return returnFunc(ctx, key, value, args)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, redis.LPosArgs) int64); ok {
		r0 = returnFunc(ctx, key, value, args)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string, redis.LPosArgs) error); ok {
		r1 = returnFunc(ctx, key, value, args)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockList_LPos_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LPos'
type MockList_LPos_Call struct {
	*mock.Call
}

// LPos is a helper method to define mock.On call
//   - ctx
//   - key
//   - value
//   - args
func (_e *MockList_Expecter) LPos(ctx interface{}, key interface{}, value interface{}, args interface{}) *MockList_LPos_Call {
	return &MockList_LPos_Call{Call: _e.mock.On("LPos", ctx, key, value, args)}
}

func (_c *MockList_LPos_Call) Run(run func(ctx context.Context, key string, value string, args redis.LPosArgs)) *MockList_LPos_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(redis.LPosArgs))
	})
	return _c
}

func (_c *MockList_LPos_Call) Return(n int64, err error) *MockList_LPos_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockList_LPos_Call) RunAndReturn(run func(ctx context.Context, key string, value string, args redis.LPosArgs) (int64, error)) *MockList_LPos_Call {
	_c.Call.Return(run)
	return _c
}

// LPosCount provides a mock function for the type MockList
func (_mock *MockList) LPosCount(ctx context.Context, key string, value string, count int64, args redis.LPosArgs) ([]int64, error) {
	ret := _mock.Called(ctx, key, value, count, args)

	if len(ret) == 0 {
		panic("no return value specified for LPosCount")
	}

	var r0 []int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, int64, redis.LPosArgs) ([]int64, error)); ok {
		return returnFunc(ctx, key, value, count, args)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, int64, redis.LPosArgs) []int64); ok {
		r0 = returnFunc(ctx, key, value, count, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int64)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string, int64, redis.LPosArgs) error); ok {
		r1 = returnFunc(ctx, key, value, count, args)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockList_LPosCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LPosCount'
type MockList_LPosCount_Call struct {
	*mock.Call
}

// LPosCount is a helper method to define mock.On call
//   - ctx
//   - key
//   - value
//   - count
//   - args
func (_e *MockList_Expecter) LPosCount(ctx interface{}, key interface{}, value interface{}, count interface{}, args interface{}) *MockList_LPosCount_Call {
	return &MockList_LPosCount_Call{Call: _e.mock.On("LPosCount", ctx, key, value, count, args)}
}

func (_c *MockList_LPosCount_Call) Run(run func(ctx context.Context, key string, value string, count int64, args redis.LPosArgs)) *MockList_LPosCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(int64), args[4].(redis.LPosArgs))
	})
	return _c
}

func (_c *MockList_LPosCount_Call) Return(int64s []int64, err error) *MockList_LPosCount_Call {
	_c.Call.Return(int64s, err)
	return _c
}

func (_c *MockList_LPosCount_Call) RunAndReturn(run func(ctx context.Context, key string, value string, count int64, args redis.LPosArgs) ([]int64, error)) *MockList_LPosCount_Call {
	_c.Call.Return(run)
	return _c
}

// LPush provides a mock function for the type MockList
func (_mock *MockList) LPush(ctx context.Context, key string, values ...interface{}) error {
	var tmpRet mock.Arguments
	if len(values) > 0 {
		tmpRet = _mock.Called(ctx, key, values)
	} else {
		tmpRet = _mock.Called(ctx, key)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for LPush")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, ...interface{}) error); ok {
		r0 = returnFunc(ctx, key, values...)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockList_LPush_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LPush'
type MockList_LPush_Call struct {
	*mock.Call
}

// LPush is a helper method to define mock.On call
//   - ctx
//   - key
//   - values
func (_e *MockList_Expecter) LPush(ctx interface{}, key interface{}, values ...interface{}) *MockList_LPush_Call {
	return &MockList_LPush_Call{Call: _e.mock.On("LPush",
		append([]interface{}{ctx, key}, values...)...)}
}

func (_c *MockList_LPush_Call) Run(run func(ctx context.Context, key string, values ...interface{})) *MockList_LPush_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]interface{})
		run(args[0].(context.Context), args[1].(string), variadicArgs...)
	})
	return _c
}

func (_c *MockList_LPush_Call) Return(err error) *MockList_LPush_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockList_LPush_Call) RunAndReturn(run func(ctx context.Context, key string, values ...interface{}) error) *MockList_LPush_Call {
	_c.Call.Return(run)
	return _c
}

// LRange provides a mock function for the type MockList
func (_mock *MockList) LRange(ctx context.Context, key string, start int64, stop int64) ([]string, error) {
	ret := _mock.Called(ctx, key, start, stop)

	if len(ret) == 0 {
		panic("no return value specified for LRange")
	}

	var r0 []string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64, int64) ([]string, error)); ok {
		return returnFunc(ctx, key, start, stop)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64, int64) []string); ok {
		r0 = returnFunc(ctx, key, start, stop)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, int64, int64) error); ok {
		r1 = returnFunc(ctx, key, start, stop)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockList_LRange_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LRange'
type MockList_LRange_Call struct {
	*mock.Call
}

// LRange is a helper method to define mock.On call
//   - ctx
//   - key
//   - start
//   - stop
func (_e *MockList_Expecter) LRange(ctx interface{}, key interface{}, start interface{}, stop interface{}) *MockList_LRange_Call {
	return &MockList_LRange_Call{Call: _e.mock.On("LRange", ctx, key, start, stop)}
}

func (_c *MockList_LRange_Call) Run(run func(ctx context.Context, key string, start int64, stop int64)) *MockList_LRange_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int64), args[3].(int64))
	})
	return _c
}

func (_c *MockList_LRange_Call) Return(strings []string, err error) *MockList_LRange_Call {
	_c.Call.Return(strings, err)
	return _c
}

func (_c *MockList_LRange_Call) RunAndReturn(run func(ctx context.Context, key string, start int64, stop int64) ([]string, error)) *MockList_LRange_Call {
	_c.Call.Return(run)
	return _c
}

// LRem provides a mock function for the type MockList
func (_mock *MockList) LRem(ctx context.Context, key string, count int64, value interface{}) (int64, error) {
	ret := _mock.Called(ctx, key, count, value)

	if len(ret) == 0 {
		panic("no return value specified for LRem")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64, interface{}) (int64, error)); ok {
		return returnFunc(ctx, key, count, value)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64, interface{}) int64); ok {
		r0 = returnFunc(ctx, key, count, value)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, int64, interface{}) error); ok {
		r1 = returnFunc(ctx, key, count, value)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockList_LRem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LRem'
type MockList_LRem_Call struct {
	*mock.Call
}

// LRem is a helper method to define mock.On call
//   - ctx
//   - key
//   - count
//   - value
func (_e *MockList_Expecter) LRem(ctx interface{}, key interface{}, count interface{}, value interface{}) *MockList_LRem_Call {
	return &MockList_LRem_Call{Call: _e.mock.On("LRem", ctx, key, count, value)}
}

func (_c *MockList_LRem_Call) Run(run func(ctx context.Context, key string, count int64, value interface{})) *MockList_LRem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int64), args[3].(interface{}))
	})
	return _c
}

func (_c *MockList_LRem_Call) Return(n int64, err error) *MockList_LRem_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockList_LRem_Call) RunAndReturn(run func(ctx context.Context, key string, count int64, value interface{}) (int64, error)) *MockList_LRem_Call {
	_c.Call.Return(run)
	return _c
}

// LSet provides a mock function for the type MockList
func (_mock *MockList) LSet(ctx context.Context, key string, index int64, value interface{}) error {
	ret := _mock.Called(ctx, key, index, value)

	if len(ret) == 0 {
		panic("no return value specified for LSet")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64, interface{}) error); ok {
		r0 = returnFunc(ctx, key, index, value)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockList_LSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LSet'
type MockList_LSet_Call struct {
	*mock.Call
}

// LSet is a helper method to define mock.On call
//   - ctx
//   - key
//   - index
//   - value
func (_e *MockList_Expecter) LSet(ctx interface{}, key interface{}, index interface{}, value interface{}) *MockList_LSet_Call {
	return &MockList_LSet_Call{Call: _e.mock.On("LSet", ctx, key, index, value)}
}

func (_c *MockList_LSet_Call) Run(run func(ctx context.Context, key string, index int64, value interface{})) *MockList_LSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int64), args[3].(interface{}))
	})
	return _c
}

func (_c *MockList_LSet_Call) Return(err error) *MockList_LSet_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockList_LSet_Call) RunAndReturn(run func(ctx context.Context, key string, index int64, value interface{}) error) *MockList_LSet_Call {
	_c.Call.Return(run)
	return _c
}

// LTrim provides a mock function for the type MockList
func (_mock *MockList) LTrim(ctx context.Context, key string, start int64, stop int64) error {
	ret := _mock.Called(ctx, key, start, stop)

	if len(ret) == 0 {
		panic("no return value specified for LTrim")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64, int64) error); ok {
		r0 = returnFunc(ctx, key, start, stop)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockList_LTrim_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LTrim'
type MockList_LTrim_Call struct {
	*mock.Call
}

// LTrim is a helper method to define mock.On call
//   - ctx
//   - key
//   - start
//   - stop
func (_e *MockList_Expecter) LTrim(ctx interface{}, key interface{}, start interface{}, stop interface{}) *MockList_LTrim_Call {
	return &MockList_LTrim_Call{Call: _e.mock.On("LTrim", ctx, key, start, stop)}
}

func (_c *MockList_LTrim_Call) Run(run func(ctx context.Context, key string, start int64, stop int64)) *MockList_LTrim_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int64), args[3].(int64))
	})
	return _c
}

func (_c *MockList_LTrim_Call) Return(err error) *MockList_LTrim_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockList_LTrim_Call) RunAndReturn(run func(ctx context.Context, key string, start int64, stop int64) error) *MockList_LTrim_Call {
	_c.Call.Return(run)
	return _c
}

// RPop provides a mock function for the type MockList
func (_mock *MockList) RPop(ctx context.Context, key string) (string, error) {
	ret := _mock.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for RPop")
	}

	var r0 string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (string, error)); ok {
		return returnFunc(ctx, key)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) string); ok {
		r0 = returnFunc(ctx, key)
	} else {
		r0 = ret.Get(0).(string)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, key)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockList_RPop_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RPop'
type MockList_RPop_Call struct {
	*mock.Call
}

// RPop is a helper method to define mock.On call
//   - ctx
//   - key
func (_e *MockList_Expecter) RPop(ctx interface{}, key interface{}) *MockList_RPop_Call {
	return &MockList_RPop_Call{Call: _e.mock.On("RPop", ctx, key)}
}

func (_c *MockList_RPop_Call) Run(run func(ctx context.Context, key string)) *MockList_RPop_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockList_RPop_Call) Return(s string, err error) *MockList_RPop_Call {
	_c.Call.Return(s, err)
	return _c
}

func (_c *MockList_RPop_Call) RunAndReturn(run func(ctx context.Context, key string) (string, error)) *MockList_RPop_Call {
	_c.Call.Return(run)
	return _c
}

// RPopCount provides a mock function for the type MockList
func (_mock *MockList) RPopCount(ctx context.Context, key string, count int) ([]string, error) {
	ret := _mock.Called(ctx, key, count)

	if len(ret) == 0 {
		panic("no return value specified for RPopCount")
	}

	var r0 []string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int) ([]string, error)); ok {
		return returnFunc(ctx, key, count)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int) []string); ok {
		r0 = returnFunc(ctx, key, count)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, int) error); ok {
		r1 = returnFunc(ctx, key, count)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockList_RPopCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RPopCount'
type MockList_RPopCount_Call struct {
	*mock.Call
}

// RPopCount is a helper method to define mock.On call
//   - ctx
//   - key
//   - count
func (_e *MockList_Expecter) RPopCount(ctx interface{}, key interface{}, count interface{}) *MockList_RPopCount_Call {
	return &MockList_RPopCount_Call{Call: _e.mock.On("RPopCount", ctx, key, count)}
}

func (_c *MockList_RPopCount_Call) Run(run func(ctx context.Context, key string, count int)) *MockList_RPopCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int))
	})
	return _c
}

func (_c *MockList_RPopCount_Call) Return(strings []string, err error) *MockList_RPopCount_Call {
	_c.Call.Return(strings, err)
	return _c
}

func (_c *MockList_RPopCount_Call) RunAndReturn(run func(ctx context.Context, key string, count int) ([]string, error)) *MockList_RPopCount_Call {
	_c.Call.Return(run)
	return _c
}

// RPush provides a mock function for the type MockList
func (_mock *MockList) RPush(ctx context.Context, key string, values ...interface{}) error {
	var tmpRet mock.Arguments
	if len(values) > 0 {
		tmpRet = _mock.Called(ctx, key, values)
	} else {
		tmpRet = _mock.Called(ctx, key)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for RPush")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, ...interface{}) error); ok {
		r0 = returnFunc(ctx, key, values...)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockList_RPush_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RPush'
type MockList_RPush_Call struct {
	*mock.Call
}

// RPush is a helper method to define mock.On call
//   - ctx
//   - key
//   - values
func (_e *MockList_Expecter) RPush(ctx interface{}, key interface{}, values ...interface{}) *MockList_RPush_Call {
	return &MockList_RPush_Call{Call: _e.mock.On("RPush",
		append([]interface{}{ctx, key}, values...)...)}
}

func (_c *MockList_RPush_Call) Run(run func(ctx context.Context, key string, values ...interface{})) *MockList_RPush_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]interface{})
		run(args[0].(context.Context), args[1].(string), variadicArgs...)
	})
	return _c
}

func (_c *MockList_RPush_Call) Return(err error) *MockList_RPush_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockList_RPush_Call) RunAndReturn(run func(ctx context.Context, key string, values ...interface{}) error) *MockList_RPush_Call {
	_c.Call.Return(run)
	return _c
}
