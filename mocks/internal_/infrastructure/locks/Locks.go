// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package locks

import (
	"context"
	"time"

	mock "github.com/stretchr/testify/mock"
)

// NewMockLocks creates a new instance of MockLocks. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockLocks(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockLocks {
	mock := &MockLocks{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockLocks is an autogenerated mock type for the Locks type
type MockLocks struct {
	mock.Mock
}

type MockLocks_Expecter struct {
	mock *mock.Mock
}

func (_m *MockLocks) EXPECT() *MockLocks_Expecter {
	return &MockLocks_Expecter{mock: &_m.Mock}
}

// AcquireLock provides a mock function for the type MockLocks
func (_mock *MockLocks) AcquireLock(ctx context.Context, lockKey string, lockValue string, ttl time.Duration) (bool, error) {
	ret := _mock.Called(ctx, lockKey, lockValue, ttl)

	if len(ret) == 0 {
		panic("no return value specified for AcquireLock")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, time.Duration) (bool, error)); ok {
		return returnFunc(ctx, lockKey, lockValue, ttl)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, time.Duration) bool); ok {
		r0 = returnFunc(ctx, lockKey, lockValue, ttl)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string, time.Duration) error); ok {
		r1 = returnFunc(ctx, lockKey, lockValue, ttl)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLocks_AcquireLock_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcquireLock'
type MockLocks_AcquireLock_Call struct {
	*mock.Call
}

// AcquireLock is a helper method to define mock.On call
//   - ctx
//   - lockKey
//   - lockValue
//   - ttl
func (_e *MockLocks_Expecter) AcquireLock(ctx interface{}, lockKey interface{}, lockValue interface{}, ttl interface{}) *MockLocks_AcquireLock_Call {
	return &MockLocks_AcquireLock_Call{Call: _e.mock.On("AcquireLock", ctx, lockKey, lockValue, ttl)}
}

func (_c *MockLocks_AcquireLock_Call) Run(run func(ctx context.Context, lockKey string, lockValue string, ttl time.Duration)) *MockLocks_AcquireLock_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(time.Duration))
	})
	return _c
}

func (_c *MockLocks_AcquireLock_Call) Return(b bool, err error) *MockLocks_AcquireLock_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockLocks_AcquireLock_Call) RunAndReturn(run func(ctx context.Context, lockKey string, lockValue string, ttl time.Duration) (bool, error)) *MockLocks_AcquireLock_Call {
	_c.Call.Return(run)
	return _c
}

// ReleaseLock provides a mock function for the type MockLocks
func (_mock *MockLocks) ReleaseLock(ctx context.Context, lockKey string, lockValue string) error {
	ret := _mock.Called(ctx, lockKey, lockValue)

	if len(ret) == 0 {
		panic("no return value specified for ReleaseLock")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = returnFunc(ctx, lockKey, lockValue)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockLocks_ReleaseLock_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ReleaseLock'
type MockLocks_ReleaseLock_Call struct {
	*mock.Call
}

// ReleaseLock is a helper method to define mock.On call
//   - ctx
//   - lockKey
//   - lockValue
func (_e *MockLocks_Expecter) ReleaseLock(ctx interface{}, lockKey interface{}, lockValue interface{}) *MockLocks_ReleaseLock_Call {
	return &MockLocks_ReleaseLock_Call{Call: _e.mock.On("ReleaseLock", ctx, lockKey, lockValue)}
}

func (_c *MockLocks_ReleaseLock_Call) Run(run func(ctx context.Context, lockKey string, lockValue string)) *MockLocks_ReleaseLock_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockLocks_ReleaseLock_Call) Return(err error) *MockLocks_ReleaseLock_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockLocks_ReleaseLock_Call) RunAndReturn(run func(ctx context.Context, lockKey string, lockValue string) error) *MockLocks_ReleaseLock_Call {
	_c.Call.Return(run)
	return _c
}
