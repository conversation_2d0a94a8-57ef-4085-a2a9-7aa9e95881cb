// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package pubsub

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/infrastructure/pubsub"

	mock "github.com/stretchr/testify/mock"
)

// NewMockPubSub creates a new instance of MockPubSub. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPubSub(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPubSub {
	mock := &MockPubSub{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockPubSub is an autogenerated mock type for the PubSub type
type MockPubSub struct {
	mock.Mock
}

type MockPubSub_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPubSub) EXPECT() *MockPubSub_Expecter {
	return &MockPubSub_Expecter{mock: &_m.Mock}
}

// Publish provides a mock function for the type MockPubSub
func (_mock *MockPubSub) Publish(ctx context.Context, channel string, message interface{}) error {
	ret := _mock.Called(ctx, channel, message)

	if len(ret) == 0 {
		panic("no return value specified for Publish")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, interface{}) error); ok {
		r0 = returnFunc(ctx, channel, message)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPubSub_Publish_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Publish'
type MockPubSub_Publish_Call struct {
	*mock.Call
}

// Publish is a helper method to define mock.On call
//   - ctx
//   - channel
//   - message
func (_e *MockPubSub_Expecter) Publish(ctx interface{}, channel interface{}, message interface{}) *MockPubSub_Publish_Call {
	return &MockPubSub_Publish_Call{Call: _e.mock.On("Publish", ctx, channel, message)}
}

func (_c *MockPubSub_Publish_Call) Run(run func(ctx context.Context, channel string, message interface{})) *MockPubSub_Publish_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(interface{}))
	})
	return _c
}

func (_c *MockPubSub_Publish_Call) Return(err error) *MockPubSub_Publish_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPubSub_Publish_Call) RunAndReturn(run func(ctx context.Context, channel string, message interface{}) error) *MockPubSub_Publish_Call {
	_c.Call.Return(run)
	return _c
}

// Subscribe provides a mock function for the type MockPubSub
func (_mock *MockPubSub) Subscribe(ctx context.Context, channel string) (pubsub.Subscription, error) {
	ret := _mock.Called(ctx, channel)

	if len(ret) == 0 {
		panic("no return value specified for Subscribe")
	}

	var r0 pubsub.Subscription
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (pubsub.Subscription, error)); ok {
		return returnFunc(ctx, channel)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) pubsub.Subscription); ok {
		r0 = returnFunc(ctx, channel)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(pubsub.Subscription)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, channel)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPubSub_Subscribe_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Subscribe'
type MockPubSub_Subscribe_Call struct {
	*mock.Call
}

// Subscribe is a helper method to define mock.On call
//   - ctx
//   - channel
func (_e *MockPubSub_Expecter) Subscribe(ctx interface{}, channel interface{}) *MockPubSub_Subscribe_Call {
	return &MockPubSub_Subscribe_Call{Call: _e.mock.On("Subscribe", ctx, channel)}
}

func (_c *MockPubSub_Subscribe_Call) Run(run func(ctx context.Context, channel string)) *MockPubSub_Subscribe_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockPubSub_Subscribe_Call) Return(subscription pubsub.Subscription, err error) *MockPubSub_Subscribe_Call {
	_c.Call.Return(subscription, err)
	return _c
}

func (_c *MockPubSub_Subscribe_Call) RunAndReturn(run func(ctx context.Context, channel string) (pubsub.Subscription, error)) *MockPubSub_Subscribe_Call {
	_c.Call.Return(run)
	return _c
}
