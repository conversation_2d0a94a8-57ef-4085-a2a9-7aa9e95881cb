// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package pubsub

import (
	mock "github.com/stretchr/testify/mock"
)

// NewMockSubscription creates a new instance of MockSubscription. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockSubscription(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockSubscription {
	mock := &MockSubscription{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockSubscription is an autogenerated mock type for the Subscription type
type MockSubscription struct {
	mock.Mock
}

type MockSubscription_Expecter struct {
	mock *mock.Mock
}

func (_m *MockSubscription) EXPECT() *MockSubscription_Expecter {
	return &MockSubscription_Expecter{mock: &_m.Mock}
}

// Channel provides a mock function for the type MockSubscription
func (_mock *MockSubscription) Channel() <-chan interface{} {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Channel")
	}

	var r0 <-chan interface{}
	if returnFunc, ok := ret.Get(0).(func() <-chan interface{}); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(<-chan interface{})
		}
	}
	return r0
}

// MockSubscription_Channel_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Channel'
type MockSubscription_Channel_Call struct {
	*mock.Call
}

// Channel is a helper method to define mock.On call
func (_e *MockSubscription_Expecter) Channel() *MockSubscription_Channel_Call {
	return &MockSubscription_Channel_Call{Call: _e.mock.On("Channel")}
}

func (_c *MockSubscription_Channel_Call) Run(run func()) *MockSubscription_Channel_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockSubscription_Channel_Call) Return(ifaceValCh <-chan interface{}) *MockSubscription_Channel_Call {
	_c.Call.Return(ifaceValCh)
	return _c
}

func (_c *MockSubscription_Channel_Call) RunAndReturn(run func() <-chan interface{}) *MockSubscription_Channel_Call {
	_c.Call.Return(run)
	return _c
}

// Close provides a mock function for the type MockSubscription
func (_mock *MockSubscription) Close() error {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func() error); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockSubscription_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type MockSubscription_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *MockSubscription_Expecter) Close() *MockSubscription_Close_Call {
	return &MockSubscription_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *MockSubscription_Close_Call) Run(run func()) *MockSubscription_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockSubscription_Close_Call) Return(err error) *MockSubscription_Close_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockSubscription_Close_Call) RunAndReturn(run func() error) *MockSubscription_Close_Call {
	_c.Call.Return(run)
	return _c
}
