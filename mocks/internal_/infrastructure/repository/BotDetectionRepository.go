// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"
	"time"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockBotDetectionRepository creates a new instance of MockBotDetectionRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockBotDetectionRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockBotDetectionRepository {
	mock := &MockBotDetectionRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockBotDetectionRepository is an autogenerated mock type for the BotDetectionRepository type
type MockBotDetectionRepository struct {
	mock.Mock
}

type MockBotDetectionRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockBotDetectionRepository) EXPECT() *MockBotDetectionRepository_Expecter {
	return &MockBotDetectionRepository_Expecter{mock: &_m.Mock}
}

// CountByUserID provides a mock function for the type MockBotDetectionRepository
func (_mock *MockBotDetectionRepository) CountByUserID(ctx context.Context, userID primitive.ObjectID) (int64, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for CountByUserID")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (int64, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) int64); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockBotDetectionRepository_CountByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountByUserID'
type MockBotDetectionRepository_CountByUserID_Call struct {
	*mock.Call
}

// CountByUserID is a helper method to define mock.On call
//   - ctx
//   - userID
func (_e *MockBotDetectionRepository_Expecter) CountByUserID(ctx interface{}, userID interface{}) *MockBotDetectionRepository_CountByUserID_Call {
	return &MockBotDetectionRepository_CountByUserID_Call{Call: _e.mock.On("CountByUserID", ctx, userID)}
}

func (_c *MockBotDetectionRepository_CountByUserID_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockBotDetectionRepository_CountByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockBotDetectionRepository_CountByUserID_Call) Return(n int64, err error) *MockBotDetectionRepository_CountByUserID_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockBotDetectionRepository_CountByUserID_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) (int64, error)) *MockBotDetectionRepository_CountByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function for the type MockBotDetectionRepository
func (_mock *MockBotDetectionRepository) Create(ctx context.Context, detection *models.BotDetection) error {
	ret := _mock.Called(ctx, detection)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.BotDetection) error); ok {
		r0 = returnFunc(ctx, detection)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockBotDetectionRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockBotDetectionRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - detection
func (_e *MockBotDetectionRepository_Expecter) Create(ctx interface{}, detection interface{}) *MockBotDetectionRepository_Create_Call {
	return &MockBotDetectionRepository_Create_Call{Call: _e.mock.On("Create", ctx, detection)}
}

func (_c *MockBotDetectionRepository_Create_Call) Run(run func(ctx context.Context, detection *models.BotDetection)) *MockBotDetectionRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.BotDetection))
	})
	return _c
}

func (_c *MockBotDetectionRepository_Create_Call) Return(err error) *MockBotDetectionRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockBotDetectionRepository_Create_Call) RunAndReturn(run func(ctx context.Context, detection *models.BotDetection) error) *MockBotDetectionRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// FindByID provides a mock function for the type MockBotDetectionRepository
func (_mock *MockBotDetectionRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.BotDetection, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindByID")
	}

	var r0 *models.BotDetection
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.BotDetection, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.BotDetection); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.BotDetection)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockBotDetectionRepository_FindByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByID'
type MockBotDetectionRepository_FindByID_Call struct {
	*mock.Call
}

// FindByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockBotDetectionRepository_Expecter) FindByID(ctx interface{}, id interface{}) *MockBotDetectionRepository_FindByID_Call {
	return &MockBotDetectionRepository_FindByID_Call{Call: _e.mock.On("FindByID", ctx, id)}
}

func (_c *MockBotDetectionRepository_FindByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockBotDetectionRepository_FindByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockBotDetectionRepository_FindByID_Call) Return(botDetection *models.BotDetection, err error) *MockBotDetectionRepository_FindByID_Call {
	_c.Call.Return(botDetection, err)
	return _c
}

func (_c *MockBotDetectionRepository_FindByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.BotDetection, error)) *MockBotDetectionRepository_FindByID_Call {
	_c.Call.Return(run)
	return _c
}

// FindByUserID provides a mock function for the type MockBotDetectionRepository
func (_mock *MockBotDetectionRepository) FindByUserID(ctx context.Context, userID primitive.ObjectID) ([]*models.BotDetection, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for FindByUserID")
	}

	var r0 []*models.BotDetection
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) ([]*models.BotDetection, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) []*models.BotDetection); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.BotDetection)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockBotDetectionRepository_FindByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByUserID'
type MockBotDetectionRepository_FindByUserID_Call struct {
	*mock.Call
}

// FindByUserID is a helper method to define mock.On call
//   - ctx
//   - userID
func (_e *MockBotDetectionRepository_Expecter) FindByUserID(ctx interface{}, userID interface{}) *MockBotDetectionRepository_FindByUserID_Call {
	return &MockBotDetectionRepository_FindByUserID_Call{Call: _e.mock.On("FindByUserID", ctx, userID)}
}

func (_c *MockBotDetectionRepository_FindByUserID_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockBotDetectionRepository_FindByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockBotDetectionRepository_FindByUserID_Call) Return(botDetections []*models.BotDetection, err error) *MockBotDetectionRepository_FindByUserID_Call {
	_c.Call.Return(botDetections, err)
	return _c
}

func (_c *MockBotDetectionRepository_FindByUserID_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) ([]*models.BotDetection, error)) *MockBotDetectionRepository_FindByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// FindRecentByUserID provides a mock function for the type MockBotDetectionRepository
func (_mock *MockBotDetectionRepository) FindRecentByUserID(ctx context.Context, userID primitive.ObjectID, since time.Time) ([]*models.BotDetection, error) {
	ret := _mock.Called(ctx, userID, since)

	if len(ret) == 0 {
		panic("no return value specified for FindRecentByUserID")
	}

	var r0 []*models.BotDetection
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, time.Time) ([]*models.BotDetection, error)); ok {
		return returnFunc(ctx, userID, since)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, time.Time) []*models.BotDetection); ok {
		r0 = returnFunc(ctx, userID, since)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.BotDetection)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, time.Time) error); ok {
		r1 = returnFunc(ctx, userID, since)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockBotDetectionRepository_FindRecentByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindRecentByUserID'
type MockBotDetectionRepository_FindRecentByUserID_Call struct {
	*mock.Call
}

// FindRecentByUserID is a helper method to define mock.On call
//   - ctx
//   - userID
//   - since
func (_e *MockBotDetectionRepository_Expecter) FindRecentByUserID(ctx interface{}, userID interface{}, since interface{}) *MockBotDetectionRepository_FindRecentByUserID_Call {
	return &MockBotDetectionRepository_FindRecentByUserID_Call{Call: _e.mock.On("FindRecentByUserID", ctx, userID, since)}
}

func (_c *MockBotDetectionRepository_FindRecentByUserID_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, since time.Time)) *MockBotDetectionRepository_FindRecentByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(time.Time))
	})
	return _c
}

func (_c *MockBotDetectionRepository_FindRecentByUserID_Call) Return(botDetections []*models.BotDetection, err error) *MockBotDetectionRepository_FindRecentByUserID_Call {
	_c.Call.Return(botDetections, err)
	return _c
}

func (_c *MockBotDetectionRepository_FindRecentByUserID_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, since time.Time) ([]*models.BotDetection, error)) *MockBotDetectionRepository_FindRecentByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateStatus provides a mock function for the type MockBotDetectionRepository
func (_mock *MockBotDetectionRepository) UpdateStatus(ctx context.Context, id primitive.ObjectID, status models.BotDetectionStatus) error {
	ret := _mock.Called(ctx, id, status)

	if len(ret) == 0 {
		panic("no return value specified for UpdateStatus")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.BotDetectionStatus) error); ok {
		r0 = returnFunc(ctx, id, status)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockBotDetectionRepository_UpdateStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateStatus'
type MockBotDetectionRepository_UpdateStatus_Call struct {
	*mock.Call
}

// UpdateStatus is a helper method to define mock.On call
//   - ctx
//   - id
//   - status
func (_e *MockBotDetectionRepository_Expecter) UpdateStatus(ctx interface{}, id interface{}, status interface{}) *MockBotDetectionRepository_UpdateStatus_Call {
	return &MockBotDetectionRepository_UpdateStatus_Call{Call: _e.mock.On("UpdateStatus", ctx, id, status)}
}

func (_c *MockBotDetectionRepository_UpdateStatus_Call) Run(run func(ctx context.Context, id primitive.ObjectID, status models.BotDetectionStatus)) *MockBotDetectionRepository_UpdateStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(models.BotDetectionStatus))
	})
	return _c
}

func (_c *MockBotDetectionRepository_UpdateStatus_Call) Return(err error) *MockBotDetectionRepository_UpdateStatus_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockBotDetectionRepository_UpdateStatus_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID, status models.BotDetectionStatus) error) *MockBotDetectionRepository_UpdateStatus_Call {
	_c.Call.Return(run)
	return _c
}
