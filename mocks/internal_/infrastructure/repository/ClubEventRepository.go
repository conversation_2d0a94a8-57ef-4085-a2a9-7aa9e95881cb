// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockClubEventRepository creates a new instance of MockClubEventRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockClubEventRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockClubEventRepository {
	mock := &MockClubEventRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockClubEventRepository is an autogenerated mock type for the ClubEventRepository type
type MockClubEventRepository struct {
	mock.Mock
}

type MockClubEventRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockClubEventRepository) EXPECT() *MockClubEventRepository_Expecter {
	return &MockClubEventRepository_Expecter{mock: &_m.Mock}
}

// Count provides a mock function for the type MockClubEventRepository
func (_mock *MockClubEventRepository) Count(ctx context.Context, filter bson.M) (int64, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for Count")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) (int64, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) int64); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M) error); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubEventRepository_Count_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Count'
type MockClubEventRepository_Count_Call struct {
	*mock.Call
}

// Count is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *MockClubEventRepository_Expecter) Count(ctx interface{}, filter interface{}) *MockClubEventRepository_Count_Call {
	return &MockClubEventRepository_Count_Call{Call: _e.mock.On("Count", ctx, filter)}
}

func (_c *MockClubEventRepository_Count_Call) Run(run func(ctx context.Context, filter bson.M)) *MockClubEventRepository_Count_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M))
	})
	return _c
}

func (_c *MockClubEventRepository_Count_Call) Return(n int64, err error) *MockClubEventRepository_Count_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockClubEventRepository_Count_Call) RunAndReturn(run func(ctx context.Context, filter bson.M) (int64, error)) *MockClubEventRepository_Count_Call {
	_c.Call.Return(run)
	return _c
}

// CountEvents provides a mock function for the type MockClubEventRepository
func (_mock *MockClubEventRepository) CountEvents(ctx context.Context, clubID primitive.ObjectID) (int64, error) {
	ret := _mock.Called(ctx, clubID)

	if len(ret) == 0 {
		panic("no return value specified for CountEvents")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (int64, error)); ok {
		return returnFunc(ctx, clubID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) int64); ok {
		r0 = returnFunc(ctx, clubID)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, clubID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubEventRepository_CountEvents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountEvents'
type MockClubEventRepository_CountEvents_Call struct {
	*mock.Call
}

// CountEvents is a helper method to define mock.On call
//   - ctx
//   - clubID
func (_e *MockClubEventRepository_Expecter) CountEvents(ctx interface{}, clubID interface{}) *MockClubEventRepository_CountEvents_Call {
	return &MockClubEventRepository_CountEvents_Call{Call: _e.mock.On("CountEvents", ctx, clubID)}
}

func (_c *MockClubEventRepository_CountEvents_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID)) *MockClubEventRepository_CountEvents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockClubEventRepository_CountEvents_Call) Return(n int64, err error) *MockClubEventRepository_CountEvents_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockClubEventRepository_CountEvents_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID) (int64, error)) *MockClubEventRepository_CountEvents_Call {
	_c.Call.Return(run)
	return _c
}

// CreateEvent provides a mock function for the type MockClubEventRepository
func (_mock *MockClubEventRepository) CreateEvent(ctx context.Context, event *models.ClubEvent) error {
	ret := _mock.Called(ctx, event)

	if len(ret) == 0 {
		panic("no return value specified for CreateEvent")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ClubEvent) error); ok {
		r0 = returnFunc(ctx, event)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubEventRepository_CreateEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateEvent'
type MockClubEventRepository_CreateEvent_Call struct {
	*mock.Call
}

// CreateEvent is a helper method to define mock.On call
//   - ctx
//   - event
func (_e *MockClubEventRepository_Expecter) CreateEvent(ctx interface{}, event interface{}) *MockClubEventRepository_CreateEvent_Call {
	return &MockClubEventRepository_CreateEvent_Call{Call: _e.mock.On("CreateEvent", ctx, event)}
}

func (_c *MockClubEventRepository_CreateEvent_Call) Run(run func(ctx context.Context, event *models.ClubEvent)) *MockClubEventRepository_CreateEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.ClubEvent))
	})
	return _c
}

func (_c *MockClubEventRepository_CreateEvent_Call) Return(err error) *MockClubEventRepository_CreateEvent_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubEventRepository_CreateEvent_Call) RunAndReturn(run func(ctx context.Context, event *models.ClubEvent) error) *MockClubEventRepository_CreateEvent_Call {
	_c.Call.Return(run)
	return _c
}

// DecrementEventParticipationCount provides a mock function for the type MockClubEventRepository
func (_mock *MockClubEventRepository) DecrementEventParticipationCount(ctx context.Context, clubID primitive.ObjectID, clubEventPlayId primitive.ObjectID) error {
	ret := _mock.Called(ctx, clubID, clubEventPlayId)

	if len(ret) == 0 {
		panic("no return value specified for DecrementEventParticipationCount")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, clubID, clubEventPlayId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubEventRepository_DecrementEventParticipationCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DecrementEventParticipationCount'
type MockClubEventRepository_DecrementEventParticipationCount_Call struct {
	*mock.Call
}

// DecrementEventParticipationCount is a helper method to define mock.On call
//   - ctx
//   - clubID
//   - clubEventPlayId
func (_e *MockClubEventRepository_Expecter) DecrementEventParticipationCount(ctx interface{}, clubID interface{}, clubEventPlayId interface{}) *MockClubEventRepository_DecrementEventParticipationCount_Call {
	return &MockClubEventRepository_DecrementEventParticipationCount_Call{Call: _e.mock.On("DecrementEventParticipationCount", ctx, clubID, clubEventPlayId)}
}

func (_c *MockClubEventRepository_DecrementEventParticipationCount_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, clubEventPlayId primitive.ObjectID)) *MockClubEventRepository_DecrementEventParticipationCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockClubEventRepository_DecrementEventParticipationCount_Call) Return(err error) *MockClubEventRepository_DecrementEventParticipationCount_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubEventRepository_DecrementEventParticipationCount_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, clubEventPlayId primitive.ObjectID) error) *MockClubEventRepository_DecrementEventParticipationCount_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteEvent provides a mock function for the type MockClubEventRepository
func (_mock *MockClubEventRepository) DeleteEvent(ctx context.Context, id primitive.ObjectID) error {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteEvent")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubEventRepository_DeleteEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteEvent'
type MockClubEventRepository_DeleteEvent_Call struct {
	*mock.Call
}

// DeleteEvent is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockClubEventRepository_Expecter) DeleteEvent(ctx interface{}, id interface{}) *MockClubEventRepository_DeleteEvent_Call {
	return &MockClubEventRepository_DeleteEvent_Call{Call: _e.mock.On("DeleteEvent", ctx, id)}
}

func (_c *MockClubEventRepository_DeleteEvent_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockClubEventRepository_DeleteEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockClubEventRepository_DeleteEvent_Call) Return(err error) *MockClubEventRepository_DeleteEvent_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubEventRepository_DeleteEvent_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) error) *MockClubEventRepository_DeleteEvent_Call {
	_c.Call.Return(run)
	return _c
}

// Find provides a mock function for the type MockClubEventRepository
func (_mock *MockClubEventRepository) Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.ClubEvent, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Find")
	}

	var r0 []*models.ClubEvent
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) ([]*models.ClubEvent, error)); ok {
		return returnFunc(ctx, filter, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) []*models.ClubEvent); ok {
		r0 = returnFunc(ctx, filter, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.ClubEvent)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, ...*options.FindOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubEventRepository_Find_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Find'
type MockClubEventRepository_Find_Call struct {
	*mock.Call
}

// Find is a helper method to define mock.On call
//   - ctx
//   - filter
//   - opts
func (_e *MockClubEventRepository_Expecter) Find(ctx interface{}, filter interface{}, opts ...interface{}) *MockClubEventRepository_Find_Call {
	return &MockClubEventRepository_Find_Call{Call: _e.mock.On("Find",
		append([]interface{}{ctx, filter}, opts...)...)}
}

func (_c *MockClubEventRepository_Find_Call) Run(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions)) *MockClubEventRepository_Find_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]*options.FindOptions)
		run(args[0].(context.Context), args[1].(bson.M), variadicArgs...)
	})
	return _c
}

func (_c *MockClubEventRepository_Find_Call) Return(clubEvents []*models.ClubEvent, err error) *MockClubEventRepository_Find_Call {
	_c.Call.Return(clubEvents, err)
	return _c
}

func (_c *MockClubEventRepository_Find_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.ClubEvent, error)) *MockClubEventRepository_Find_Call {
	_c.Call.Return(run)
	return _c
}

// FindEventByID provides a mock function for the type MockClubEventRepository
func (_mock *MockClubEventRepository) FindEventByID(ctx context.Context, id primitive.ObjectID) (*models.ClubEvent, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindEventByID")
	}

	var r0 *models.ClubEvent
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.ClubEvent, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.ClubEvent); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubEvent)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubEventRepository_FindEventByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindEventByID'
type MockClubEventRepository_FindEventByID_Call struct {
	*mock.Call
}

// FindEventByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockClubEventRepository_Expecter) FindEventByID(ctx interface{}, id interface{}) *MockClubEventRepository_FindEventByID_Call {
	return &MockClubEventRepository_FindEventByID_Call{Call: _e.mock.On("FindEventByID", ctx, id)}
}

func (_c *MockClubEventRepository_FindEventByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockClubEventRepository_FindEventByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockClubEventRepository_FindEventByID_Call) Return(clubEvent *models.ClubEvent, err error) *MockClubEventRepository_FindEventByID_Call {
	_c.Call.Return(clubEvent, err)
	return _c
}

func (_c *MockClubEventRepository_FindEventByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.ClubEvent, error)) *MockClubEventRepository_FindEventByID_Call {
	_c.Call.Return(run)
	return _c
}

// IncrementEventParticipationCount provides a mock function for the type MockClubEventRepository
func (_mock *MockClubEventRepository) IncrementEventParticipationCount(ctx context.Context, clubID primitive.ObjectID, clubEventPlayId primitive.ObjectID) error {
	ret := _mock.Called(ctx, clubID, clubEventPlayId)

	if len(ret) == 0 {
		panic("no return value specified for IncrementEventParticipationCount")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, clubID, clubEventPlayId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubEventRepository_IncrementEventParticipationCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IncrementEventParticipationCount'
type MockClubEventRepository_IncrementEventParticipationCount_Call struct {
	*mock.Call
}

// IncrementEventParticipationCount is a helper method to define mock.On call
//   - ctx
//   - clubID
//   - clubEventPlayId
func (_e *MockClubEventRepository_Expecter) IncrementEventParticipationCount(ctx interface{}, clubID interface{}, clubEventPlayId interface{}) *MockClubEventRepository_IncrementEventParticipationCount_Call {
	return &MockClubEventRepository_IncrementEventParticipationCount_Call{Call: _e.mock.On("IncrementEventParticipationCount", ctx, clubID, clubEventPlayId)}
}

func (_c *MockClubEventRepository_IncrementEventParticipationCount_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, clubEventPlayId primitive.ObjectID)) *MockClubEventRepository_IncrementEventParticipationCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockClubEventRepository_IncrementEventParticipationCount_Call) Return(err error) *MockClubEventRepository_IncrementEventParticipationCount_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubEventRepository_IncrementEventParticipationCount_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, clubEventPlayId primitive.ObjectID) error) *MockClubEventRepository_IncrementEventParticipationCount_Call {
	_c.Call.Return(run)
	return _c
}

// ListEvents provides a mock function for the type MockClubEventRepository
func (_mock *MockClubEventRepository) ListEvents(ctx context.Context, clubID primitive.ObjectID, opts *options.FindOptions) ([]*models.ClubEvent, error) {
	ret := _mock.Called(ctx, clubID, opts)

	if len(ret) == 0 {
		panic("no return value specified for ListEvents")
	}

	var r0 []*models.ClubEvent
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *options.FindOptions) ([]*models.ClubEvent, error)); ok {
		return returnFunc(ctx, clubID, opts)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *options.FindOptions) []*models.ClubEvent); ok {
		r0 = returnFunc(ctx, clubID, opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.ClubEvent)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *options.FindOptions) error); ok {
		r1 = returnFunc(ctx, clubID, opts)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubEventRepository_ListEvents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListEvents'
type MockClubEventRepository_ListEvents_Call struct {
	*mock.Call
}

// ListEvents is a helper method to define mock.On call
//   - ctx
//   - clubID
//   - opts
func (_e *MockClubEventRepository_Expecter) ListEvents(ctx interface{}, clubID interface{}, opts interface{}) *MockClubEventRepository_ListEvents_Call {
	return &MockClubEventRepository_ListEvents_Call{Call: _e.mock.On("ListEvents", ctx, clubID, opts)}
}

func (_c *MockClubEventRepository_ListEvents_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, opts *options.FindOptions)) *MockClubEventRepository_ListEvents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(*options.FindOptions))
	})
	return _c
}

func (_c *MockClubEventRepository_ListEvents_Call) Return(clubEvents []*models.ClubEvent, err error) *MockClubEventRepository_ListEvents_Call {
	_c.Call.Return(clubEvents, err)
	return _c
}

func (_c *MockClubEventRepository_ListEvents_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, opts *options.FindOptions) ([]*models.ClubEvent, error)) *MockClubEventRepository_ListEvents_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateEvent provides a mock function for the type MockClubEventRepository
func (_mock *MockClubEventRepository) UpdateEvent(ctx context.Context, id primitive.ObjectID, update bson.M) error {
	ret := _mock.Called(ctx, id, update)

	if len(ret) == 0 {
		panic("no return value specified for UpdateEvent")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, bson.M) error); ok {
		r0 = returnFunc(ctx, id, update)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubEventRepository_UpdateEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateEvent'
type MockClubEventRepository_UpdateEvent_Call struct {
	*mock.Call
}

// UpdateEvent is a helper method to define mock.On call
//   - ctx
//   - id
//   - update
func (_e *MockClubEventRepository_Expecter) UpdateEvent(ctx interface{}, id interface{}, update interface{}) *MockClubEventRepository_UpdateEvent_Call {
	return &MockClubEventRepository_UpdateEvent_Call{Call: _e.mock.On("UpdateEvent", ctx, id, update)}
}

func (_c *MockClubEventRepository_UpdateEvent_Call) Run(run func(ctx context.Context, id primitive.ObjectID, update bson.M)) *MockClubEventRepository_UpdateEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(bson.M))
	})
	return _c
}

func (_c *MockClubEventRepository_UpdateEvent_Call) Return(err error) *MockClubEventRepository_UpdateEvent_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubEventRepository_UpdateEvent_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID, update bson.M) error) *MockClubEventRepository_UpdateEvent_Call {
	_c.Call.Return(run)
	return _c
}
