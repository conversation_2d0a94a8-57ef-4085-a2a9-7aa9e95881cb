// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockClubMemberRepository creates a new instance of MockClubMemberRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockClubMemberRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockClubMemberRepository {
	mock := &MockClubMemberRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockClubMemberRepository is an autogenerated mock type for the ClubMemberRepository type
type MockClubMemberRepository struct {
	mock.Mock
}

type MockClubMemberRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockClubMemberRepository) EXPECT() *MockClubMemberRepository_Expecter {
	return &MockClubMemberRepository_Expecter{mock: &_m.Mock}
}

// AcceptMemberJoinRequest provides a mock function for the type MockClubMemberRepository
func (_mock *MockClubMemberRepository) AcceptMemberJoinRequest(ctx context.Context, clubId primitive.ObjectID, userId primitive.ObjectID) error {
	ret := _mock.Called(ctx, clubId, userId)

	if len(ret) == 0 {
		panic("no return value specified for AcceptMemberJoinRequest")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, clubId, userId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubMemberRepository_AcceptMemberJoinRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcceptMemberJoinRequest'
type MockClubMemberRepository_AcceptMemberJoinRequest_Call struct {
	*mock.Call
}

// AcceptMemberJoinRequest is a helper method to define mock.On call
//   - ctx
//   - clubId
//   - userId
func (_e *MockClubMemberRepository_Expecter) AcceptMemberJoinRequest(ctx interface{}, clubId interface{}, userId interface{}) *MockClubMemberRepository_AcceptMemberJoinRequest_Call {
	return &MockClubMemberRepository_AcceptMemberJoinRequest_Call{Call: _e.mock.On("AcceptMemberJoinRequest", ctx, clubId, userId)}
}

func (_c *MockClubMemberRepository_AcceptMemberJoinRequest_Call) Run(run func(ctx context.Context, clubId primitive.ObjectID, userId primitive.ObjectID)) *MockClubMemberRepository_AcceptMemberJoinRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockClubMemberRepository_AcceptMemberJoinRequest_Call) Return(err error) *MockClubMemberRepository_AcceptMemberJoinRequest_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubMemberRepository_AcceptMemberJoinRequest_Call) RunAndReturn(run func(ctx context.Context, clubId primitive.ObjectID, userId primitive.ObjectID) error) *MockClubMemberRepository_AcceptMemberJoinRequest_Call {
	_c.Call.Return(run)
	return _c
}

// AddMember provides a mock function for the type MockClubMemberRepository
func (_mock *MockClubMemberRepository) AddMember(ctx context.Context, member *models.ClubMember) error {
	ret := _mock.Called(ctx, member)

	if len(ret) == 0 {
		panic("no return value specified for AddMember")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ClubMember) error); ok {
		r0 = returnFunc(ctx, member)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubMemberRepository_AddMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddMember'
type MockClubMemberRepository_AddMember_Call struct {
	*mock.Call
}

// AddMember is a helper method to define mock.On call
//   - ctx
//   - member
func (_e *MockClubMemberRepository_Expecter) AddMember(ctx interface{}, member interface{}) *MockClubMemberRepository_AddMember_Call {
	return &MockClubMemberRepository_AddMember_Call{Call: _e.mock.On("AddMember", ctx, member)}
}

func (_c *MockClubMemberRepository_AddMember_Call) Run(run func(ctx context.Context, member *models.ClubMember)) *MockClubMemberRepository_AddMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.ClubMember))
	})
	return _c
}

func (_c *MockClubMemberRepository_AddMember_Call) Return(err error) *MockClubMemberRepository_AddMember_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubMemberRepository_AddMember_Call) RunAndReturn(run func(ctx context.Context, member *models.ClubMember) error) *MockClubMemberRepository_AddMember_Call {
	_c.Call.Return(run)
	return _c
}

// Aggregate provides a mock function for the type MockClubMemberRepository
func (_mock *MockClubMemberRepository) Aggregate(ctx context.Context, pipeline mongo.Pipeline) ([]*models.ClubMember, error) {
	ret := _mock.Called(ctx, pipeline)

	if len(ret) == 0 {
		panic("no return value specified for Aggregate")
	}

	var r0 []*models.ClubMember
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) ([]*models.ClubMember, error)); ok {
		return returnFunc(ctx, pipeline)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) []*models.ClubMember); ok {
		r0 = returnFunc(ctx, pipeline)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.ClubMember)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, mongo.Pipeline) error); ok {
		r1 = returnFunc(ctx, pipeline)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubMemberRepository_Aggregate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Aggregate'
type MockClubMemberRepository_Aggregate_Call struct {
	*mock.Call
}

// Aggregate is a helper method to define mock.On call
//   - ctx
//   - pipeline
func (_e *MockClubMemberRepository_Expecter) Aggregate(ctx interface{}, pipeline interface{}) *MockClubMemberRepository_Aggregate_Call {
	return &MockClubMemberRepository_Aggregate_Call{Call: _e.mock.On("Aggregate", ctx, pipeline)}
}

func (_c *MockClubMemberRepository_Aggregate_Call) Run(run func(ctx context.Context, pipeline mongo.Pipeline)) *MockClubMemberRepository_Aggregate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(mongo.Pipeline))
	})
	return _c
}

func (_c *MockClubMemberRepository_Aggregate_Call) Return(clubMembers []*models.ClubMember, err error) *MockClubMemberRepository_Aggregate_Call {
	_c.Call.Return(clubMembers, err)
	return _c
}

func (_c *MockClubMemberRepository_Aggregate_Call) RunAndReturn(run func(ctx context.Context, pipeline mongo.Pipeline) ([]*models.ClubMember, error)) *MockClubMemberRepository_Aggregate_Call {
	_c.Call.Return(run)
	return _c
}

// AggregateProjected provides a mock function for the type MockClubMemberRepository
func (_mock *MockClubMemberRepository) AggregateProjected(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error) {
	ret := _mock.Called(ctx, pipeline)

	if len(ret) == 0 {
		panic("no return value specified for AggregateProjected")
	}

	var r0 *mongo.Cursor
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) (*mongo.Cursor, error)); ok {
		return returnFunc(ctx, pipeline)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) *mongo.Cursor); ok {
		r0 = returnFunc(ctx, pipeline)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.Cursor)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, mongo.Pipeline) error); ok {
		r1 = returnFunc(ctx, pipeline)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubMemberRepository_AggregateProjected_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AggregateProjected'
type MockClubMemberRepository_AggregateProjected_Call struct {
	*mock.Call
}

// AggregateProjected is a helper method to define mock.On call
//   - ctx
//   - pipeline
func (_e *MockClubMemberRepository_Expecter) AggregateProjected(ctx interface{}, pipeline interface{}) *MockClubMemberRepository_AggregateProjected_Call {
	return &MockClubMemberRepository_AggregateProjected_Call{Call: _e.mock.On("AggregateProjected", ctx, pipeline)}
}

func (_c *MockClubMemberRepository_AggregateProjected_Call) Run(run func(ctx context.Context, pipeline mongo.Pipeline)) *MockClubMemberRepository_AggregateProjected_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(mongo.Pipeline))
	})
	return _c
}

func (_c *MockClubMemberRepository_AggregateProjected_Call) Return(cursor *mongo.Cursor, err error) *MockClubMemberRepository_AggregateProjected_Call {
	_c.Call.Return(cursor, err)
	return _c
}

func (_c *MockClubMemberRepository_AggregateProjected_Call) RunAndReturn(run func(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error)) *MockClubMemberRepository_AggregateProjected_Call {
	_c.Call.Return(run)
	return _c
}

// CountMembers provides a mock function for the type MockClubMemberRepository
func (_mock *MockClubMemberRepository) CountMembers(ctx context.Context, clubID primitive.ObjectID) (int64, error) {
	ret := _mock.Called(ctx, clubID)

	if len(ret) == 0 {
		panic("no return value specified for CountMembers")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (int64, error)); ok {
		return returnFunc(ctx, clubID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) int64); ok {
		r0 = returnFunc(ctx, clubID)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, clubID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubMemberRepository_CountMembers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountMembers'
type MockClubMemberRepository_CountMembers_Call struct {
	*mock.Call
}

// CountMembers is a helper method to define mock.On call
//   - ctx
//   - clubID
func (_e *MockClubMemberRepository_Expecter) CountMembers(ctx interface{}, clubID interface{}) *MockClubMemberRepository_CountMembers_Call {
	return &MockClubMemberRepository_CountMembers_Call{Call: _e.mock.On("CountMembers", ctx, clubID)}
}

func (_c *MockClubMemberRepository_CountMembers_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID)) *MockClubMemberRepository_CountMembers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockClubMemberRepository_CountMembers_Call) Return(n int64, err error) *MockClubMemberRepository_CountMembers_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockClubMemberRepository_CountMembers_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID) (int64, error)) *MockClubMemberRepository_CountMembers_Call {
	_c.Call.Return(run)
	return _c
}

// GetClubLeaderboard provides a mock function for the type MockClubMemberRepository
func (_mock *MockClubMemberRepository) GetClubLeaderboard(ctx context.Context, clubID primitive.ObjectID, page int, pageSize int) (*models.ClubLeaderboard, error) {
	ret := _mock.Called(ctx, clubID, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetClubLeaderboard")
	}

	var r0 *models.ClubLeaderboard
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) (*models.ClubLeaderboard, error)); ok {
		return returnFunc(ctx, clubID, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) *models.ClubLeaderboard); ok {
		r0 = returnFunc(ctx, clubID, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubLeaderboard)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int, int) error); ok {
		r1 = returnFunc(ctx, clubID, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubMemberRepository_GetClubLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetClubLeaderboard'
type MockClubMemberRepository_GetClubLeaderboard_Call struct {
	*mock.Call
}

// GetClubLeaderboard is a helper method to define mock.On call
//   - ctx
//   - clubID
//   - page
//   - pageSize
func (_e *MockClubMemberRepository_Expecter) GetClubLeaderboard(ctx interface{}, clubID interface{}, page interface{}, pageSize interface{}) *MockClubMemberRepository_GetClubLeaderboard_Call {
	return &MockClubMemberRepository_GetClubLeaderboard_Call{Call: _e.mock.On("GetClubLeaderboard", ctx, clubID, page, pageSize)}
}

func (_c *MockClubMemberRepository_GetClubLeaderboard_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, page int, pageSize int)) *MockClubMemberRepository_GetClubLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int), args[3].(int))
	})
	return _c
}

func (_c *MockClubMemberRepository_GetClubLeaderboard_Call) Return(clubLeaderboard *models.ClubLeaderboard, err error) *MockClubMemberRepository_GetClubLeaderboard_Call {
	_c.Call.Return(clubLeaderboard, err)
	return _c
}

func (_c *MockClubMemberRepository_GetClubLeaderboard_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, page int, pageSize int) (*models.ClubLeaderboard, error)) *MockClubMemberRepository_GetClubLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetMemberInfo provides a mock function for the type MockClubMemberRepository
func (_mock *MockClubMemberRepository) GetMemberInfo(ctx context.Context, memberId primitive.ObjectID, clubId primitive.ObjectID) (*models.ClubMember, error) {
	ret := _mock.Called(ctx, memberId, clubId)

	if len(ret) == 0 {
		panic("no return value specified for GetMemberInfo")
	}

	var r0 *models.ClubMember
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (*models.ClubMember, error)); ok {
		return returnFunc(ctx, memberId, clubId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) *models.ClubMember); ok {
		r0 = returnFunc(ctx, memberId, clubId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubMember)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, memberId, clubId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubMemberRepository_GetMemberInfo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMemberInfo'
type MockClubMemberRepository_GetMemberInfo_Call struct {
	*mock.Call
}

// GetMemberInfo is a helper method to define mock.On call
//   - ctx
//   - memberId
//   - clubId
func (_e *MockClubMemberRepository_Expecter) GetMemberInfo(ctx interface{}, memberId interface{}, clubId interface{}) *MockClubMemberRepository_GetMemberInfo_Call {
	return &MockClubMemberRepository_GetMemberInfo_Call{Call: _e.mock.On("GetMemberInfo", ctx, memberId, clubId)}
}

func (_c *MockClubMemberRepository_GetMemberInfo_Call) Run(run func(ctx context.Context, memberId primitive.ObjectID, clubId primitive.ObjectID)) *MockClubMemberRepository_GetMemberInfo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockClubMemberRepository_GetMemberInfo_Call) Return(clubMember *models.ClubMember, err error) *MockClubMemberRepository_GetMemberInfo_Call {
	_c.Call.Return(clubMember, err)
	return _c
}

func (_c *MockClubMemberRepository_GetMemberInfo_Call) RunAndReturn(run func(ctx context.Context, memberId primitive.ObjectID, clubId primitive.ObjectID) (*models.ClubMember, error)) *MockClubMemberRepository_GetMemberInfo_Call {
	_c.Call.Return(run)
	return _c
}

// ListMembers provides a mock function for the type MockClubMemberRepository
func (_mock *MockClubMemberRepository) ListMembers(ctx context.Context, clubID primitive.ObjectID, opts *options.FindOptions) ([]*models.ClubMember, error) {
	ret := _mock.Called(ctx, clubID, opts)

	if len(ret) == 0 {
		panic("no return value specified for ListMembers")
	}

	var r0 []*models.ClubMember
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *options.FindOptions) ([]*models.ClubMember, error)); ok {
		return returnFunc(ctx, clubID, opts)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *options.FindOptions) []*models.ClubMember); ok {
		r0 = returnFunc(ctx, clubID, opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.ClubMember)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *options.FindOptions) error); ok {
		r1 = returnFunc(ctx, clubID, opts)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubMemberRepository_ListMembers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListMembers'
type MockClubMemberRepository_ListMembers_Call struct {
	*mock.Call
}

// ListMembers is a helper method to define mock.On call
//   - ctx
//   - clubID
//   - opts
func (_e *MockClubMemberRepository_Expecter) ListMembers(ctx interface{}, clubID interface{}, opts interface{}) *MockClubMemberRepository_ListMembers_Call {
	return &MockClubMemberRepository_ListMembers_Call{Call: _e.mock.On("ListMembers", ctx, clubID, opts)}
}

func (_c *MockClubMemberRepository_ListMembers_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, opts *options.FindOptions)) *MockClubMemberRepository_ListMembers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(*options.FindOptions))
	})
	return _c
}

func (_c *MockClubMemberRepository_ListMembers_Call) Return(clubMembers []*models.ClubMember, err error) *MockClubMemberRepository_ListMembers_Call {
	_c.Call.Return(clubMembers, err)
	return _c
}

func (_c *MockClubMemberRepository_ListMembers_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, opts *options.FindOptions) ([]*models.ClubMember, error)) *MockClubMemberRepository_ListMembers_Call {
	_c.Call.Return(run)
	return _c
}

// RejectMemberJoinRequest provides a mock function for the type MockClubMemberRepository
func (_mock *MockClubMemberRepository) RejectMemberJoinRequest(ctx context.Context, clubId primitive.ObjectID, userId primitive.ObjectID) error {
	ret := _mock.Called(ctx, clubId, userId)

	if len(ret) == 0 {
		panic("no return value specified for RejectMemberJoinRequest")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, clubId, userId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubMemberRepository_RejectMemberJoinRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RejectMemberJoinRequest'
type MockClubMemberRepository_RejectMemberJoinRequest_Call struct {
	*mock.Call
}

// RejectMemberJoinRequest is a helper method to define mock.On call
//   - ctx
//   - clubId
//   - userId
func (_e *MockClubMemberRepository_Expecter) RejectMemberJoinRequest(ctx interface{}, clubId interface{}, userId interface{}) *MockClubMemberRepository_RejectMemberJoinRequest_Call {
	return &MockClubMemberRepository_RejectMemberJoinRequest_Call{Call: _e.mock.On("RejectMemberJoinRequest", ctx, clubId, userId)}
}

func (_c *MockClubMemberRepository_RejectMemberJoinRequest_Call) Run(run func(ctx context.Context, clubId primitive.ObjectID, userId primitive.ObjectID)) *MockClubMemberRepository_RejectMemberJoinRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockClubMemberRepository_RejectMemberJoinRequest_Call) Return(err error) *MockClubMemberRepository_RejectMemberJoinRequest_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubMemberRepository_RejectMemberJoinRequest_Call) RunAndReturn(run func(ctx context.Context, clubId primitive.ObjectID, userId primitive.ObjectID) error) *MockClubMemberRepository_RejectMemberJoinRequest_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveMember provides a mock function for the type MockClubMemberRepository
func (_mock *MockClubMemberRepository) RemoveMember(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID) error {
	ret := _mock.Called(ctx, clubID, userID)

	if len(ret) == 0 {
		panic("no return value specified for RemoveMember")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, clubID, userID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubMemberRepository_RemoveMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveMember'
type MockClubMemberRepository_RemoveMember_Call struct {
	*mock.Call
}

// RemoveMember is a helper method to define mock.On call
//   - ctx
//   - clubID
//   - userID
func (_e *MockClubMemberRepository_Expecter) RemoveMember(ctx interface{}, clubID interface{}, userID interface{}) *MockClubMemberRepository_RemoveMember_Call {
	return &MockClubMemberRepository_RemoveMember_Call{Call: _e.mock.On("RemoveMember", ctx, clubID, userID)}
}

func (_c *MockClubMemberRepository_RemoveMember_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID)) *MockClubMemberRepository_RemoveMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockClubMemberRepository_RemoveMember_Call) Return(err error) *MockClubMemberRepository_RemoveMember_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubMemberRepository_RemoveMember_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID) error) *MockClubMemberRepository_RemoveMember_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateMemberRole provides a mock function for the type MockClubMemberRepository
func (_mock *MockClubMemberRepository) UpdateMemberRole(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID, role string) error {
	ret := _mock.Called(ctx, clubID, userID, role)

	if len(ret) == 0 {
		panic("no return value specified for UpdateMemberRole")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID, string) error); ok {
		r0 = returnFunc(ctx, clubID, userID, role)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubMemberRepository_UpdateMemberRole_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateMemberRole'
type MockClubMemberRepository_UpdateMemberRole_Call struct {
	*mock.Call
}

// UpdateMemberRole is a helper method to define mock.On call
//   - ctx
//   - clubID
//   - userID
//   - role
func (_e *MockClubMemberRepository_Expecter) UpdateMemberRole(ctx interface{}, clubID interface{}, userID interface{}, role interface{}) *MockClubMemberRepository_UpdateMemberRole_Call {
	return &MockClubMemberRepository_UpdateMemberRole_Call{Call: _e.mock.On("UpdateMemberRole", ctx, clubID, userID, role)}
}

func (_c *MockClubMemberRepository_UpdateMemberRole_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID, role string)) *MockClubMemberRepository_UpdateMemberRole_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID), args[3].(string))
	})
	return _c
}

func (_c *MockClubMemberRepository_UpdateMemberRole_Call) Return(err error) *MockClubMemberRepository_UpdateMemberRole_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubMemberRepository_UpdateMemberRole_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID, role string) error) *MockClubMemberRepository_UpdateMemberRole_Call {
	_c.Call.Return(run)
	return _c
}
