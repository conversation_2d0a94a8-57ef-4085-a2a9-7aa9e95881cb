// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockContestRepository creates a new instance of MockContestRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockContestRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockContestRepository {
	mock := &MockContestRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockContestRepository is an autogenerated mock type for the ContestRepository type
type MockContestRepository struct {
	mock.Mock
}

type MockContestRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockContestRepository) EXPECT() *MockContestRepository_Expecter {
	return &MockContestRepository_Expecter{mock: &_m.Mock}
}

// Aggregate provides a mock function for the type MockContestRepository
func (_mock *MockContestRepository) Aggregate(ctx context.Context, pipeline mongo.Pipeline) ([]*models.Contest, error) {
	ret := _mock.Called(ctx, pipeline)

	if len(ret) == 0 {
		panic("no return value specified for Aggregate")
	}

	var r0 []*models.Contest
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) ([]*models.Contest, error)); ok {
		return returnFunc(ctx, pipeline)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) []*models.Contest); ok {
		r0 = returnFunc(ctx, pipeline)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Contest)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, mongo.Pipeline) error); ok {
		r1 = returnFunc(ctx, pipeline)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestRepository_Aggregate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Aggregate'
type MockContestRepository_Aggregate_Call struct {
	*mock.Call
}

// Aggregate is a helper method to define mock.On call
//   - ctx
//   - pipeline
func (_e *MockContestRepository_Expecter) Aggregate(ctx interface{}, pipeline interface{}) *MockContestRepository_Aggregate_Call {
	return &MockContestRepository_Aggregate_Call{Call: _e.mock.On("Aggregate", ctx, pipeline)}
}

func (_c *MockContestRepository_Aggregate_Call) Run(run func(ctx context.Context, pipeline mongo.Pipeline)) *MockContestRepository_Aggregate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(mongo.Pipeline))
	})
	return _c
}

func (_c *MockContestRepository_Aggregate_Call) Return(contests []*models.Contest, err error) *MockContestRepository_Aggregate_Call {
	_c.Call.Return(contests, err)
	return _c
}

func (_c *MockContestRepository_Aggregate_Call) RunAndReturn(run func(ctx context.Context, pipeline mongo.Pipeline) ([]*models.Contest, error)) *MockContestRepository_Aggregate_Call {
	_c.Call.Return(run)
	return _c
}

// AggregateProjected provides a mock function for the type MockContestRepository
func (_mock *MockContestRepository) AggregateProjected(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error) {
	ret := _mock.Called(ctx, pipeline)

	if len(ret) == 0 {
		panic("no return value specified for AggregateProjected")
	}

	var r0 *mongo.Cursor
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) (*mongo.Cursor, error)); ok {
		return returnFunc(ctx, pipeline)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) *mongo.Cursor); ok {
		r0 = returnFunc(ctx, pipeline)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.Cursor)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, mongo.Pipeline) error); ok {
		r1 = returnFunc(ctx, pipeline)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestRepository_AggregateProjected_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AggregateProjected'
type MockContestRepository_AggregateProjected_Call struct {
	*mock.Call
}

// AggregateProjected is a helper method to define mock.On call
//   - ctx
//   - pipeline
func (_e *MockContestRepository_Expecter) AggregateProjected(ctx interface{}, pipeline interface{}) *MockContestRepository_AggregateProjected_Call {
	return &MockContestRepository_AggregateProjected_Call{Call: _e.mock.On("AggregateProjected", ctx, pipeline)}
}

func (_c *MockContestRepository_AggregateProjected_Call) Run(run func(ctx context.Context, pipeline mongo.Pipeline)) *MockContestRepository_AggregateProjected_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(mongo.Pipeline))
	})
	return _c
}

func (_c *MockContestRepository_AggregateProjected_Call) Return(cursor *mongo.Cursor, err error) *MockContestRepository_AggregateProjected_Call {
	_c.Call.Return(cursor, err)
	return _c
}

func (_c *MockContestRepository_AggregateProjected_Call) RunAndReturn(run func(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error)) *MockContestRepository_AggregateProjected_Call {
	_c.Call.Return(run)
	return _c
}

// Count provides a mock function for the type MockContestRepository
func (_mock *MockContestRepository) Count(ctx context.Context, filter bson.M) (int64, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for Count")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) (int64, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) int64); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M) error); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestRepository_Count_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Count'
type MockContestRepository_Count_Call struct {
	*mock.Call
}

// Count is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *MockContestRepository_Expecter) Count(ctx interface{}, filter interface{}) *MockContestRepository_Count_Call {
	return &MockContestRepository_Count_Call{Call: _e.mock.On("Count", ctx, filter)}
}

func (_c *MockContestRepository_Count_Call) Run(run func(ctx context.Context, filter bson.M)) *MockContestRepository_Count_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M))
	})
	return _c
}

func (_c *MockContestRepository_Count_Call) Return(n int64, err error) *MockContestRepository_Count_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockContestRepository_Count_Call) RunAndReturn(run func(ctx context.Context, filter bson.M) (int64, error)) *MockContestRepository_Count_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function for the type MockContestRepository
func (_mock *MockContestRepository) Create(ctx context.Context, contest *models.Contest) error {
	ret := _mock.Called(ctx, contest)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Contest) error); ok {
		r0 = returnFunc(ctx, contest)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockContestRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockContestRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - contest
func (_e *MockContestRepository_Expecter) Create(ctx interface{}, contest interface{}) *MockContestRepository_Create_Call {
	return &MockContestRepository_Create_Call{Call: _e.mock.On("Create", ctx, contest)}
}

func (_c *MockContestRepository_Create_Call) Run(run func(ctx context.Context, contest *models.Contest)) *MockContestRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.Contest))
	})
	return _c
}

func (_c *MockContestRepository_Create_Call) Return(err error) *MockContestRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockContestRepository_Create_Call) RunAndReturn(run func(ctx context.Context, contest *models.Contest) error) *MockContestRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// DecrementRegistrationCount provides a mock function for the type MockContestRepository
func (_mock *MockContestRepository) DecrementRegistrationCount(ctx context.Context, contestID primitive.ObjectID) error {
	ret := _mock.Called(ctx, contestID)

	if len(ret) == 0 {
		panic("no return value specified for DecrementRegistrationCount")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, contestID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockContestRepository_DecrementRegistrationCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DecrementRegistrationCount'
type MockContestRepository_DecrementRegistrationCount_Call struct {
	*mock.Call
}

// DecrementRegistrationCount is a helper method to define mock.On call
//   - ctx
//   - contestID
func (_e *MockContestRepository_Expecter) DecrementRegistrationCount(ctx interface{}, contestID interface{}) *MockContestRepository_DecrementRegistrationCount_Call {
	return &MockContestRepository_DecrementRegistrationCount_Call{Call: _e.mock.On("DecrementRegistrationCount", ctx, contestID)}
}

func (_c *MockContestRepository_DecrementRegistrationCount_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID)) *MockContestRepository_DecrementRegistrationCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockContestRepository_DecrementRegistrationCount_Call) Return(err error) *MockContestRepository_DecrementRegistrationCount_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockContestRepository_DecrementRegistrationCount_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID) error) *MockContestRepository_DecrementRegistrationCount_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function for the type MockContestRepository
func (_mock *MockContestRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockContestRepository_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockContestRepository_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockContestRepository_Expecter) Delete(ctx interface{}, id interface{}) *MockContestRepository_Delete_Call {
	return &MockContestRepository_Delete_Call{Call: _e.mock.On("Delete", ctx, id)}
}

func (_c *MockContestRepository_Delete_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockContestRepository_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockContestRepository_Delete_Call) Return(err error) *MockContestRepository_Delete_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockContestRepository_Delete_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) error) *MockContestRepository_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// FindByID provides a mock function for the type MockContestRepository
func (_mock *MockContestRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.Contest, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindByID")
	}

	var r0 *models.Contest
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Contest, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Contest); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Contest)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestRepository_FindByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByID'
type MockContestRepository_FindByID_Call struct {
	*mock.Call
}

// FindByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockContestRepository_Expecter) FindByID(ctx interface{}, id interface{}) *MockContestRepository_FindByID_Call {
	return &MockContestRepository_FindByID_Call{Call: _e.mock.On("FindByID", ctx, id)}
}

func (_c *MockContestRepository_FindByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockContestRepository_FindByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockContestRepository_FindByID_Call) Return(contest *models.Contest, err error) *MockContestRepository_FindByID_Call {
	_c.Call.Return(contest, err)
	return _c
}

func (_c *MockContestRepository_FindByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.Contest, error)) *MockContestRepository_FindByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetLatestWeeklyContest provides a mock function for the type MockContestRepository
func (_mock *MockContestRepository) GetLatestWeeklyContest(background context.Context) (*models.Contest, error) {
	ret := _mock.Called(background)

	if len(ret) == 0 {
		panic("no return value specified for GetLatestWeeklyContest")
	}

	var r0 *models.Contest
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.Contest, error)); ok {
		return returnFunc(background)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.Contest); ok {
		r0 = returnFunc(background)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Contest)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(background)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestRepository_GetLatestWeeklyContest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLatestWeeklyContest'
type MockContestRepository_GetLatestWeeklyContest_Call struct {
	*mock.Call
}

// GetLatestWeeklyContest is a helper method to define mock.On call
//   - background
func (_e *MockContestRepository_Expecter) GetLatestWeeklyContest(background interface{}) *MockContestRepository_GetLatestWeeklyContest_Call {
	return &MockContestRepository_GetLatestWeeklyContest_Call{Call: _e.mock.On("GetLatestWeeklyContest", background)}
}

func (_c *MockContestRepository_GetLatestWeeklyContest_Call) Run(run func(background context.Context)) *MockContestRepository_GetLatestWeeklyContest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockContestRepository_GetLatestWeeklyContest_Call) Return(contest *models.Contest, err error) *MockContestRepository_GetLatestWeeklyContest_Call {
	_c.Call.Return(contest, err)
	return _c
}

func (_c *MockContestRepository_GetLatestWeeklyContest_Call) RunAndReturn(run func(background context.Context) (*models.Contest, error)) *MockContestRepository_GetLatestWeeklyContest_Call {
	_c.Call.Return(run)
	return _c
}

// IncrementRegistrationCount provides a mock function for the type MockContestRepository
func (_mock *MockContestRepository) IncrementRegistrationCount(ctx context.Context, contestID primitive.ObjectID) error {
	ret := _mock.Called(ctx, contestID)

	if len(ret) == 0 {
		panic("no return value specified for IncrementRegistrationCount")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, contestID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockContestRepository_IncrementRegistrationCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IncrementRegistrationCount'
type MockContestRepository_IncrementRegistrationCount_Call struct {
	*mock.Call
}

// IncrementRegistrationCount is a helper method to define mock.On call
//   - ctx
//   - contestID
func (_e *MockContestRepository_Expecter) IncrementRegistrationCount(ctx interface{}, contestID interface{}) *MockContestRepository_IncrementRegistrationCount_Call {
	return &MockContestRepository_IncrementRegistrationCount_Call{Call: _e.mock.On("IncrementRegistrationCount", ctx, contestID)}
}

func (_c *MockContestRepository_IncrementRegistrationCount_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID)) *MockContestRepository_IncrementRegistrationCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockContestRepository_IncrementRegistrationCount_Call) Return(err error) *MockContestRepository_IncrementRegistrationCount_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockContestRepository_IncrementRegistrationCount_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID) error) *MockContestRepository_IncrementRegistrationCount_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function for the type MockContestRepository
func (_mock *MockContestRepository) List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.Contest, error) {
	ret := _mock.Called(ctx, filter, opts)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*models.Contest
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, *options.FindOptions) ([]*models.Contest, error)); ok {
		return returnFunc(ctx, filter, opts)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, *options.FindOptions) []*models.Contest); ok {
		r0 = returnFunc(ctx, filter, opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Contest)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, *options.FindOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestRepository_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type MockContestRepository_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - ctx
//   - filter
//   - opts
func (_e *MockContestRepository_Expecter) List(ctx interface{}, filter interface{}, opts interface{}) *MockContestRepository_List_Call {
	return &MockContestRepository_List_Call{Call: _e.mock.On("List", ctx, filter, opts)}
}

func (_c *MockContestRepository_List_Call) Run(run func(ctx context.Context, filter bson.M, opts *options.FindOptions)) *MockContestRepository_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M), args[2].(*options.FindOptions))
	})
	return _c
}

func (_c *MockContestRepository_List_Call) Return(contests []*models.Contest, err error) *MockContestRepository_List_Call {
	_c.Call.Return(contests, err)
	return _c
}

func (_c *MockContestRepository_List_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.Contest, error)) *MockContestRepository_List_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function for the type MockContestRepository
func (_mock *MockContestRepository) Update(ctx context.Context, filter bson.M, update bson.M) error {
	ret := _mock.Called(ctx, filter, update)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, bson.M) error); ok {
		r0 = returnFunc(ctx, filter, update)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockContestRepository_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockContestRepository_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx
//   - filter
//   - update
func (_e *MockContestRepository_Expecter) Update(ctx interface{}, filter interface{}, update interface{}) *MockContestRepository_Update_Call {
	return &MockContestRepository_Update_Call{Call: _e.mock.On("Update", ctx, filter, update)}
}

func (_c *MockContestRepository_Update_Call) Run(run func(ctx context.Context, filter bson.M, update bson.M)) *MockContestRepository_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M), args[2].(bson.M))
	})
	return _c
}

func (_c *MockContestRepository_Update_Call) Return(err error) *MockContestRepository_Update_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockContestRepository_Update_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, update bson.M) error) *MockContestRepository_Update_Call {
	_c.Call.Return(run)
	return _c
}
