// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewMockCounterRepository creates a new instance of MockCounterRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockCounterRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockCounterRepository {
	mock := &MockCounterRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockCounterRepository is an autogenerated mock type for the CounterRepository type
type MockCounterRepository struct {
	mock.Mock
}

type MockCounterRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockCounterRepository) EXPECT() *MockCounterRepository_Expecter {
	return &MockCounterRepository_Expecter{mock: &_m.Mock}
}

// GetNextSequence provides a mock function for the type MockCounterRepository
func (_mock *MockCounterRepository) GetNextSequence(ctx context.Context, counterName string) (int64, error) {
	ret := _mock.Called(ctx, counterName)

	if len(ret) == 0 {
		panic("no return value specified for GetNextSequence")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (int64, error)); ok {
		return returnFunc(ctx, counterName)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) int64); ok {
		r0 = returnFunc(ctx, counterName)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, counterName)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCounterRepository_GetNextSequence_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetNextSequence'
type MockCounterRepository_GetNextSequence_Call struct {
	*mock.Call
}

// GetNextSequence is a helper method to define mock.On call
//   - ctx
//   - counterName
func (_e *MockCounterRepository_Expecter) GetNextSequence(ctx interface{}, counterName interface{}) *MockCounterRepository_GetNextSequence_Call {
	return &MockCounterRepository_GetNextSequence_Call{Call: _e.mock.On("GetNextSequence", ctx, counterName)}
}

func (_c *MockCounterRepository_GetNextSequence_Call) Run(run func(ctx context.Context, counterName string)) *MockCounterRepository_GetNextSequence_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockCounterRepository_GetNextSequence_Call) Return(n int64, err error) *MockCounterRepository_GetNextSequence_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockCounterRepository_GetNextSequence_Call) RunAndReturn(run func(ctx context.Context, counterName string) (int64, error)) *MockCounterRepository_GetNextSequence_Call {
	_c.Call.Return(run)
	return _c
}
