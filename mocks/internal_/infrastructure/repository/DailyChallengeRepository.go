// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockDailyChallengeRepository creates a new instance of MockDailyChallengeRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDailyChallengeRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDailyChallengeRepository {
	mock := &MockDailyChallengeRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockDailyChallengeRepository is an autogenerated mock type for the DailyChallengeRepository type
type MockDailyChallengeRepository struct {
	mock.Mock
}

type MockDailyChallengeRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDailyChallengeRepository) EXPECT() *MockDailyChallengeRepository_Expecter {
	return &MockDailyChallengeRepository_Expecter{mock: &_m.Mock}
}

// Count provides a mock function for the type MockDailyChallengeRepository
func (_mock *MockDailyChallengeRepository) Count(ctx context.Context, filter bson.M) (int64, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for Count")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) (int64, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) int64); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M) error); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeRepository_Count_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Count'
type MockDailyChallengeRepository_Count_Call struct {
	*mock.Call
}

// Count is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *MockDailyChallengeRepository_Expecter) Count(ctx interface{}, filter interface{}) *MockDailyChallengeRepository_Count_Call {
	return &MockDailyChallengeRepository_Count_Call{Call: _e.mock.On("Count", ctx, filter)}
}

func (_c *MockDailyChallengeRepository_Count_Call) Run(run func(ctx context.Context, filter bson.M)) *MockDailyChallengeRepository_Count_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M))
	})
	return _c
}

func (_c *MockDailyChallengeRepository_Count_Call) Return(n int64, err error) *MockDailyChallengeRepository_Count_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockDailyChallengeRepository_Count_Call) RunAndReturn(run func(ctx context.Context, filter bson.M) (int64, error)) *MockDailyChallengeRepository_Count_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function for the type MockDailyChallengeRepository
func (_mock *MockDailyChallengeRepository) Create(ctx context.Context, dailyChallenge *models.DailyChallenge) error {
	ret := _mock.Called(ctx, dailyChallenge)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.DailyChallenge) error); ok {
		r0 = returnFunc(ctx, dailyChallenge)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDailyChallengeRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockDailyChallengeRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - dailyChallenge
func (_e *MockDailyChallengeRepository_Expecter) Create(ctx interface{}, dailyChallenge interface{}) *MockDailyChallengeRepository_Create_Call {
	return &MockDailyChallengeRepository_Create_Call{Call: _e.mock.On("Create", ctx, dailyChallenge)}
}

func (_c *MockDailyChallengeRepository_Create_Call) Run(run func(ctx context.Context, dailyChallenge *models.DailyChallenge)) *MockDailyChallengeRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.DailyChallenge))
	})
	return _c
}

func (_c *MockDailyChallengeRepository_Create_Call) Return(err error) *MockDailyChallengeRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDailyChallengeRepository_Create_Call) RunAndReturn(run func(ctx context.Context, dailyChallenge *models.DailyChallenge) error) *MockDailyChallengeRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function for the type MockDailyChallengeRepository
func (_mock *MockDailyChallengeRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDailyChallengeRepository_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockDailyChallengeRepository_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockDailyChallengeRepository_Expecter) Delete(ctx interface{}, id interface{}) *MockDailyChallengeRepository_Delete_Call {
	return &MockDailyChallengeRepository_Delete_Call{Call: _e.mock.On("Delete", ctx, id)}
}

func (_c *MockDailyChallengeRepository_Delete_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockDailyChallengeRepository_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockDailyChallengeRepository_Delete_Call) Return(err error) *MockDailyChallengeRepository_Delete_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDailyChallengeRepository_Delete_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) error) *MockDailyChallengeRepository_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// FindByChallengeNumber provides a mock function for the type MockDailyChallengeRepository
func (_mock *MockDailyChallengeRepository) FindByChallengeNumber(ctx context.Context, challengeNumber int) (*models.DailyChallenge, error) {
	ret := _mock.Called(ctx, challengeNumber)

	if len(ret) == 0 {
		panic("no return value specified for FindByChallengeNumber")
	}

	var r0 *models.DailyChallenge
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int) (*models.DailyChallenge, error)); ok {
		return returnFunc(ctx, challengeNumber)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int) *models.DailyChallenge); ok {
		r0 = returnFunc(ctx, challengeNumber)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.DailyChallenge)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int) error); ok {
		r1 = returnFunc(ctx, challengeNumber)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeRepository_FindByChallengeNumber_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByChallengeNumber'
type MockDailyChallengeRepository_FindByChallengeNumber_Call struct {
	*mock.Call
}

// FindByChallengeNumber is a helper method to define mock.On call
//   - ctx
//   - challengeNumber
func (_e *MockDailyChallengeRepository_Expecter) FindByChallengeNumber(ctx interface{}, challengeNumber interface{}) *MockDailyChallengeRepository_FindByChallengeNumber_Call {
	return &MockDailyChallengeRepository_FindByChallengeNumber_Call{Call: _e.mock.On("FindByChallengeNumber", ctx, challengeNumber)}
}

func (_c *MockDailyChallengeRepository_FindByChallengeNumber_Call) Run(run func(ctx context.Context, challengeNumber int)) *MockDailyChallengeRepository_FindByChallengeNumber_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int))
	})
	return _c
}

func (_c *MockDailyChallengeRepository_FindByChallengeNumber_Call) Return(dailyChallenge *models.DailyChallenge, err error) *MockDailyChallengeRepository_FindByChallengeNumber_Call {
	_c.Call.Return(dailyChallenge, err)
	return _c
}

func (_c *MockDailyChallengeRepository_FindByChallengeNumber_Call) RunAndReturn(run func(ctx context.Context, challengeNumber int) (*models.DailyChallenge, error)) *MockDailyChallengeRepository_FindByChallengeNumber_Call {
	_c.Call.Return(run)
	return _c
}

// FindByChallengeNumberAndDivison provides a mock function for the type MockDailyChallengeRepository
func (_mock *MockDailyChallengeRepository) FindByChallengeNumberAndDivison(ctx context.Context, challengeNumber int, division string) (*models.DailyChallenge, error) {
	ret := _mock.Called(ctx, challengeNumber, division)

	if len(ret) == 0 {
		panic("no return value specified for FindByChallengeNumberAndDivison")
	}

	var r0 *models.DailyChallenge
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, string) (*models.DailyChallenge, error)); ok {
		return returnFunc(ctx, challengeNumber, division)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, string) *models.DailyChallenge); ok {
		r0 = returnFunc(ctx, challengeNumber, division)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.DailyChallenge)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int, string) error); ok {
		r1 = returnFunc(ctx, challengeNumber, division)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeRepository_FindByChallengeNumberAndDivison_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByChallengeNumberAndDivison'
type MockDailyChallengeRepository_FindByChallengeNumberAndDivison_Call struct {
	*mock.Call
}

// FindByChallengeNumberAndDivison is a helper method to define mock.On call
//   - ctx
//   - challengeNumber
//   - division
func (_e *MockDailyChallengeRepository_Expecter) FindByChallengeNumberAndDivison(ctx interface{}, challengeNumber interface{}, division interface{}) *MockDailyChallengeRepository_FindByChallengeNumberAndDivison_Call {
	return &MockDailyChallengeRepository_FindByChallengeNumberAndDivison_Call{Call: _e.mock.On("FindByChallengeNumberAndDivison", ctx, challengeNumber, division)}
}

func (_c *MockDailyChallengeRepository_FindByChallengeNumberAndDivison_Call) Run(run func(ctx context.Context, challengeNumber int, division string)) *MockDailyChallengeRepository_FindByChallengeNumberAndDivison_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(string))
	})
	return _c
}

func (_c *MockDailyChallengeRepository_FindByChallengeNumberAndDivison_Call) Return(dailyChallenge *models.DailyChallenge, err error) *MockDailyChallengeRepository_FindByChallengeNumberAndDivison_Call {
	_c.Call.Return(dailyChallenge, err)
	return _c
}

func (_c *MockDailyChallengeRepository_FindByChallengeNumberAndDivison_Call) RunAndReturn(run func(ctx context.Context, challengeNumber int, division string) (*models.DailyChallenge, error)) *MockDailyChallengeRepository_FindByChallengeNumberAndDivison_Call {
	_c.Call.Return(run)
	return _c
}

// FindByID provides a mock function for the type MockDailyChallengeRepository
func (_mock *MockDailyChallengeRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.DailyChallenge, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindByID")
	}

	var r0 *models.DailyChallenge
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.DailyChallenge, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.DailyChallenge); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.DailyChallenge)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeRepository_FindByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByID'
type MockDailyChallengeRepository_FindByID_Call struct {
	*mock.Call
}

// FindByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockDailyChallengeRepository_Expecter) FindByID(ctx interface{}, id interface{}) *MockDailyChallengeRepository_FindByID_Call {
	return &MockDailyChallengeRepository_FindByID_Call{Call: _e.mock.On("FindByID", ctx, id)}
}

func (_c *MockDailyChallengeRepository_FindByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockDailyChallengeRepository_FindByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockDailyChallengeRepository_FindByID_Call) Return(dailyChallenge *models.DailyChallenge, err error) *MockDailyChallengeRepository_FindByID_Call {
	_c.Call.Return(dailyChallenge, err)
	return _c
}

func (_c *MockDailyChallengeRepository_FindByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.DailyChallenge, error)) *MockDailyChallengeRepository_FindByID_Call {
	_c.Call.Return(run)
	return _c
}

// FindOne provides a mock function for the type MockDailyChallengeRepository
func (_mock *MockDailyChallengeRepository) FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.DailyChallenge, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for FindOne")
	}

	var r0 *models.DailyChallenge
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOneOptions) (*models.DailyChallenge, error)); ok {
		return returnFunc(ctx, filter, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOneOptions) *models.DailyChallenge); ok {
		r0 = returnFunc(ctx, filter, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.DailyChallenge)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, ...*options.FindOneOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeRepository_FindOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOne'
type MockDailyChallengeRepository_FindOne_Call struct {
	*mock.Call
}

// FindOne is a helper method to define mock.On call
//   - ctx
//   - filter
//   - opts
func (_e *MockDailyChallengeRepository_Expecter) FindOne(ctx interface{}, filter interface{}, opts ...interface{}) *MockDailyChallengeRepository_FindOne_Call {
	return &MockDailyChallengeRepository_FindOne_Call{Call: _e.mock.On("FindOne",
		append([]interface{}{ctx, filter}, opts...)...)}
}

func (_c *MockDailyChallengeRepository_FindOne_Call) Run(run func(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions)) *MockDailyChallengeRepository_FindOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]*options.FindOneOptions)
		run(args[0].(context.Context), args[1].(bson.M), variadicArgs...)
	})
	return _c
}

func (_c *MockDailyChallengeRepository_FindOne_Call) Return(dailyChallenge *models.DailyChallenge, err error) *MockDailyChallengeRepository_FindOne_Call {
	_c.Call.Return(dailyChallenge, err)
	return _c
}

func (_c *MockDailyChallengeRepository_FindOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.DailyChallenge, error)) *MockDailyChallengeRepository_FindOne_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function for the type MockDailyChallengeRepository
func (_mock *MockDailyChallengeRepository) List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.DailyChallenge, error) {
	ret := _mock.Called(ctx, filter, opts)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*models.DailyChallenge
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, *options.FindOptions) ([]*models.DailyChallenge, error)); ok {
		return returnFunc(ctx, filter, opts)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, *options.FindOptions) []*models.DailyChallenge); ok {
		r0 = returnFunc(ctx, filter, opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.DailyChallenge)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, *options.FindOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeRepository_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type MockDailyChallengeRepository_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - ctx
//   - filter
//   - opts
func (_e *MockDailyChallengeRepository_Expecter) List(ctx interface{}, filter interface{}, opts interface{}) *MockDailyChallengeRepository_List_Call {
	return &MockDailyChallengeRepository_List_Call{Call: _e.mock.On("List", ctx, filter, opts)}
}

func (_c *MockDailyChallengeRepository_List_Call) Run(run func(ctx context.Context, filter bson.M, opts *options.FindOptions)) *MockDailyChallengeRepository_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M), args[2].(*options.FindOptions))
	})
	return _c
}

func (_c *MockDailyChallengeRepository_List_Call) Return(dailyChallenges []*models.DailyChallenge, err error) *MockDailyChallengeRepository_List_Call {
	_c.Call.Return(dailyChallenges, err)
	return _c
}

func (_c *MockDailyChallengeRepository_List_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.DailyChallenge, error)) *MockDailyChallengeRepository_List_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function for the type MockDailyChallengeRepository
func (_mock *MockDailyChallengeRepository) Update(ctx context.Context, dailyChallenge *models.DailyChallenge) error {
	ret := _mock.Called(ctx, dailyChallenge)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.DailyChallenge) error); ok {
		r0 = returnFunc(ctx, dailyChallenge)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDailyChallengeRepository_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockDailyChallengeRepository_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx
//   - dailyChallenge
func (_e *MockDailyChallengeRepository_Expecter) Update(ctx interface{}, dailyChallenge interface{}) *MockDailyChallengeRepository_Update_Call {
	return &MockDailyChallengeRepository_Update_Call{Call: _e.mock.On("Update", ctx, dailyChallenge)}
}

func (_c *MockDailyChallengeRepository_Update_Call) Run(run func(ctx context.Context, dailyChallenge *models.DailyChallenge)) *MockDailyChallengeRepository_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.DailyChallenge))
	})
	return _c
}

func (_c *MockDailyChallengeRepository_Update_Call) Return(err error) *MockDailyChallengeRepository_Update_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDailyChallengeRepository_Update_Call) RunAndReturn(run func(ctx context.Context, dailyChallenge *models.DailyChallenge) error) *MockDailyChallengeRepository_Update_Call {
	_c.Call.Return(run)
	return _c
}
