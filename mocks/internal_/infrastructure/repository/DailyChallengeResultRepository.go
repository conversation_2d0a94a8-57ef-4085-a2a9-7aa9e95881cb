// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockDailyChallengeResultRepository creates a new instance of MockDailyChallengeResultRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDailyChallengeResultRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDailyChallengeResultRepository {
	mock := &MockDailyChallengeResultRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockDailyChallengeResultRepository is an autogenerated mock type for the DailyChallengeResultRepository type
type MockDailyChallengeResultRepository struct {
	mock.Mock
}

type MockDailyChallengeResultRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDailyChallengeResultRepository) EXPECT() *MockDailyChallengeResultRepository_Expecter {
	return &MockDailyChallengeResultRepository_Expecter{mock: &_m.Mock}
}

// Aggregate provides a mock function for the type MockDailyChallengeResultRepository
func (_mock *MockDailyChallengeResultRepository) Aggregate(ctx context.Context, pipeline mongo.Pipeline) ([]*models.DailyChallengeResult, error) {
	ret := _mock.Called(ctx, pipeline)

	if len(ret) == 0 {
		panic("no return value specified for Aggregate")
	}

	var r0 []*models.DailyChallengeResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) ([]*models.DailyChallengeResult, error)); ok {
		return returnFunc(ctx, pipeline)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) []*models.DailyChallengeResult); ok {
		r0 = returnFunc(ctx, pipeline)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.DailyChallengeResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, mongo.Pipeline) error); ok {
		r1 = returnFunc(ctx, pipeline)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeResultRepository_Aggregate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Aggregate'
type MockDailyChallengeResultRepository_Aggregate_Call struct {
	*mock.Call
}

// Aggregate is a helper method to define mock.On call
//   - ctx
//   - pipeline
func (_e *MockDailyChallengeResultRepository_Expecter) Aggregate(ctx interface{}, pipeline interface{}) *MockDailyChallengeResultRepository_Aggregate_Call {
	return &MockDailyChallengeResultRepository_Aggregate_Call{Call: _e.mock.On("Aggregate", ctx, pipeline)}
}

func (_c *MockDailyChallengeResultRepository_Aggregate_Call) Run(run func(ctx context.Context, pipeline mongo.Pipeline)) *MockDailyChallengeResultRepository_Aggregate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(mongo.Pipeline))
	})
	return _c
}

func (_c *MockDailyChallengeResultRepository_Aggregate_Call) Return(dailyChallengeResults []*models.DailyChallengeResult, err error) *MockDailyChallengeResultRepository_Aggregate_Call {
	_c.Call.Return(dailyChallengeResults, err)
	return _c
}

func (_c *MockDailyChallengeResultRepository_Aggregate_Call) RunAndReturn(run func(ctx context.Context, pipeline mongo.Pipeline) ([]*models.DailyChallengeResult, error)) *MockDailyChallengeResultRepository_Aggregate_Call {
	_c.Call.Return(run)
	return _c
}

// AggregateProjected provides a mock function for the type MockDailyChallengeResultRepository
func (_mock *MockDailyChallengeResultRepository) AggregateProjected(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error) {
	ret := _mock.Called(ctx, pipeline)

	if len(ret) == 0 {
		panic("no return value specified for AggregateProjected")
	}

	var r0 *mongo.Cursor
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) (*mongo.Cursor, error)); ok {
		return returnFunc(ctx, pipeline)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) *mongo.Cursor); ok {
		r0 = returnFunc(ctx, pipeline)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.Cursor)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, mongo.Pipeline) error); ok {
		r1 = returnFunc(ctx, pipeline)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeResultRepository_AggregateProjected_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AggregateProjected'
type MockDailyChallengeResultRepository_AggregateProjected_Call struct {
	*mock.Call
}

// AggregateProjected is a helper method to define mock.On call
//   - ctx
//   - pipeline
func (_e *MockDailyChallengeResultRepository_Expecter) AggregateProjected(ctx interface{}, pipeline interface{}) *MockDailyChallengeResultRepository_AggregateProjected_Call {
	return &MockDailyChallengeResultRepository_AggregateProjected_Call{Call: _e.mock.On("AggregateProjected", ctx, pipeline)}
}

func (_c *MockDailyChallengeResultRepository_AggregateProjected_Call) Run(run func(ctx context.Context, pipeline mongo.Pipeline)) *MockDailyChallengeResultRepository_AggregateProjected_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(mongo.Pipeline))
	})
	return _c
}

func (_c *MockDailyChallengeResultRepository_AggregateProjected_Call) Return(cursor *mongo.Cursor, err error) *MockDailyChallengeResultRepository_AggregateProjected_Call {
	_c.Call.Return(cursor, err)
	return _c
}

func (_c *MockDailyChallengeResultRepository_AggregateProjected_Call) RunAndReturn(run func(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error)) *MockDailyChallengeResultRepository_AggregateProjected_Call {
	_c.Call.Return(run)
	return _c
}

// Count provides a mock function for the type MockDailyChallengeResultRepository
func (_mock *MockDailyChallengeResultRepository) Count(ctx context.Context, filter bson.M) (int64, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for Count")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) (int64, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) int64); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M) error); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeResultRepository_Count_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Count'
type MockDailyChallengeResultRepository_Count_Call struct {
	*mock.Call
}

// Count is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *MockDailyChallengeResultRepository_Expecter) Count(ctx interface{}, filter interface{}) *MockDailyChallengeResultRepository_Count_Call {
	return &MockDailyChallengeResultRepository_Count_Call{Call: _e.mock.On("Count", ctx, filter)}
}

func (_c *MockDailyChallengeResultRepository_Count_Call) Run(run func(ctx context.Context, filter bson.M)) *MockDailyChallengeResultRepository_Count_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M))
	})
	return _c
}

func (_c *MockDailyChallengeResultRepository_Count_Call) Return(n int64, err error) *MockDailyChallengeResultRepository_Count_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockDailyChallengeResultRepository_Count_Call) RunAndReturn(run func(ctx context.Context, filter bson.M) (int64, error)) *MockDailyChallengeResultRepository_Count_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function for the type MockDailyChallengeResultRepository
func (_mock *MockDailyChallengeResultRepository) Create(ctx context.Context, dailyChallengeResult *models.DailyChallengeResult) error {
	ret := _mock.Called(ctx, dailyChallengeResult)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.DailyChallengeResult) error); ok {
		r0 = returnFunc(ctx, dailyChallengeResult)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDailyChallengeResultRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockDailyChallengeResultRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - dailyChallengeResult
func (_e *MockDailyChallengeResultRepository_Expecter) Create(ctx interface{}, dailyChallengeResult interface{}) *MockDailyChallengeResultRepository_Create_Call {
	return &MockDailyChallengeResultRepository_Create_Call{Call: _e.mock.On("Create", ctx, dailyChallengeResult)}
}

func (_c *MockDailyChallengeResultRepository_Create_Call) Run(run func(ctx context.Context, dailyChallengeResult *models.DailyChallengeResult)) *MockDailyChallengeResultRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.DailyChallengeResult))
	})
	return _c
}

func (_c *MockDailyChallengeResultRepository_Create_Call) Return(err error) *MockDailyChallengeResultRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDailyChallengeResultRepository_Create_Call) RunAndReturn(run func(ctx context.Context, dailyChallengeResult *models.DailyChallengeResult) error) *MockDailyChallengeResultRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function for the type MockDailyChallengeResultRepository
func (_mock *MockDailyChallengeResultRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDailyChallengeResultRepository_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockDailyChallengeResultRepository_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockDailyChallengeResultRepository_Expecter) Delete(ctx interface{}, id interface{}) *MockDailyChallengeResultRepository_Delete_Call {
	return &MockDailyChallengeResultRepository_Delete_Call{Call: _e.mock.On("Delete", ctx, id)}
}

func (_c *MockDailyChallengeResultRepository_Delete_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockDailyChallengeResultRepository_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockDailyChallengeResultRepository_Delete_Call) Return(err error) *MockDailyChallengeResultRepository_Delete_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDailyChallengeResultRepository_Delete_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) error) *MockDailyChallengeResultRepository_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// FindByID provides a mock function for the type MockDailyChallengeResultRepository
func (_mock *MockDailyChallengeResultRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.DailyChallengeResult, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindByID")
	}

	var r0 *models.DailyChallengeResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.DailyChallengeResult, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.DailyChallengeResult); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.DailyChallengeResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeResultRepository_FindByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByID'
type MockDailyChallengeResultRepository_FindByID_Call struct {
	*mock.Call
}

// FindByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockDailyChallengeResultRepository_Expecter) FindByID(ctx interface{}, id interface{}) *MockDailyChallengeResultRepository_FindByID_Call {
	return &MockDailyChallengeResultRepository_FindByID_Call{Call: _e.mock.On("FindByID", ctx, id)}
}

func (_c *MockDailyChallengeResultRepository_FindByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockDailyChallengeResultRepository_FindByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockDailyChallengeResultRepository_FindByID_Call) Return(dailyChallengeResult *models.DailyChallengeResult, err error) *MockDailyChallengeResultRepository_FindByID_Call {
	_c.Call.Return(dailyChallengeResult, err)
	return _c
}

func (_c *MockDailyChallengeResultRepository_FindByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.DailyChallengeResult, error)) *MockDailyChallengeResultRepository_FindByID_Call {
	_c.Call.Return(run)
	return _c
}

// FindOne provides a mock function for the type MockDailyChallengeResultRepository
func (_mock *MockDailyChallengeResultRepository) FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.DailyChallengeResult, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for FindOne")
	}

	var r0 *models.DailyChallengeResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOneOptions) (*models.DailyChallengeResult, error)); ok {
		return returnFunc(ctx, filter, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOneOptions) *models.DailyChallengeResult); ok {
		r0 = returnFunc(ctx, filter, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.DailyChallengeResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, ...*options.FindOneOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeResultRepository_FindOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOne'
type MockDailyChallengeResultRepository_FindOne_Call struct {
	*mock.Call
}

// FindOne is a helper method to define mock.On call
//   - ctx
//   - filter
//   - opts
func (_e *MockDailyChallengeResultRepository_Expecter) FindOne(ctx interface{}, filter interface{}, opts ...interface{}) *MockDailyChallengeResultRepository_FindOne_Call {
	return &MockDailyChallengeResultRepository_FindOne_Call{Call: _e.mock.On("FindOne",
		append([]interface{}{ctx, filter}, opts...)...)}
}

func (_c *MockDailyChallengeResultRepository_FindOne_Call) Run(run func(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions)) *MockDailyChallengeResultRepository_FindOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]*options.FindOneOptions)
		run(args[0].(context.Context), args[1].(bson.M), variadicArgs...)
	})
	return _c
}

func (_c *MockDailyChallengeResultRepository_FindOne_Call) Return(dailyChallengeResult *models.DailyChallengeResult, err error) *MockDailyChallengeResultRepository_FindOne_Call {
	_c.Call.Return(dailyChallengeResult, err)
	return _c
}

func (_c *MockDailyChallengeResultRepository_FindOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.DailyChallengeResult, error)) *MockDailyChallengeResultRepository_FindOne_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function for the type MockDailyChallengeResultRepository
func (_mock *MockDailyChallengeResultRepository) List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.DailyChallengeResult, error) {
	ret := _mock.Called(ctx, filter, opts)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*models.DailyChallengeResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, *options.FindOptions) ([]*models.DailyChallengeResult, error)); ok {
		return returnFunc(ctx, filter, opts)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, *options.FindOptions) []*models.DailyChallengeResult); ok {
		r0 = returnFunc(ctx, filter, opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.DailyChallengeResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, *options.FindOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeResultRepository_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type MockDailyChallengeResultRepository_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - ctx
//   - filter
//   - opts
func (_e *MockDailyChallengeResultRepository_Expecter) List(ctx interface{}, filter interface{}, opts interface{}) *MockDailyChallengeResultRepository_List_Call {
	return &MockDailyChallengeResultRepository_List_Call{Call: _e.mock.On("List", ctx, filter, opts)}
}

func (_c *MockDailyChallengeResultRepository_List_Call) Run(run func(ctx context.Context, filter bson.M, opts *options.FindOptions)) *MockDailyChallengeResultRepository_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M), args[2].(*options.FindOptions))
	})
	return _c
}

func (_c *MockDailyChallengeResultRepository_List_Call) Return(dailyChallengeResults []*models.DailyChallengeResult, err error) *MockDailyChallengeResultRepository_List_Call {
	_c.Call.Return(dailyChallengeResults, err)
	return _c
}

func (_c *MockDailyChallengeResultRepository_List_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.DailyChallengeResult, error)) *MockDailyChallengeResultRepository_List_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function for the type MockDailyChallengeResultRepository
func (_mock *MockDailyChallengeResultRepository) Update(ctx context.Context, dailyChallengeResult *models.DailyChallengeResult) error {
	ret := _mock.Called(ctx, dailyChallengeResult)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.DailyChallengeResult) error); ok {
		r0 = returnFunc(ctx, dailyChallengeResult)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDailyChallengeResultRepository_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockDailyChallengeResultRepository_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx
//   - dailyChallengeResult
func (_e *MockDailyChallengeResultRepository_Expecter) Update(ctx interface{}, dailyChallengeResult interface{}) *MockDailyChallengeResultRepository_Update_Call {
	return &MockDailyChallengeResultRepository_Update_Call{Call: _e.mock.On("Update", ctx, dailyChallengeResult)}
}

func (_c *MockDailyChallengeResultRepository_Update_Call) Run(run func(ctx context.Context, dailyChallengeResult *models.DailyChallengeResult)) *MockDailyChallengeResultRepository_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.DailyChallengeResult))
	})
	return _c
}

func (_c *MockDailyChallengeResultRepository_Update_Call) Return(err error) *MockDailyChallengeResultRepository_Update_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDailyChallengeResultRepository_Update_Call) RunAndReturn(run func(ctx context.Context, dailyChallengeResult *models.DailyChallengeResult) error) *MockDailyChallengeResultRepository_Update_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function for the type MockDailyChallengeResultRepository
func (_mock *MockDailyChallengeResultRepository) UpdateOne(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions) error {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, update, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter, update)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, bson.M, ...*options.UpdateOptions) error); ok {
		r0 = returnFunc(ctx, filter, update, opts...)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDailyChallengeResultRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockDailyChallengeResultRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx
//   - filter
//   - update
//   - opts
func (_e *MockDailyChallengeResultRepository_Expecter) UpdateOne(ctx interface{}, filter interface{}, update interface{}, opts ...interface{}) *MockDailyChallengeResultRepository_UpdateOne_Call {
	return &MockDailyChallengeResultRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne",
		append([]interface{}{ctx, filter, update}, opts...)...)}
}

func (_c *MockDailyChallengeResultRepository_UpdateOne_Call) Run(run func(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions)) *MockDailyChallengeResultRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[3].([]*options.UpdateOptions)
		run(args[0].(context.Context), args[1].(bson.M), args[2].(bson.M), variadicArgs...)
	})
	return _c
}

func (_c *MockDailyChallengeResultRepository_UpdateOne_Call) Return(err error) *MockDailyChallengeResultRepository_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDailyChallengeResultRepository_UpdateOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions) error) *MockDailyChallengeResultRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}
