// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/mongo"
)

// NewMockDeletedUserRepository creates a new instance of MockDeletedUserRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDeletedUserRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDeletedUserRepository {
	mock := &MockDeletedUserRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockDeletedUserRepository is an autogenerated mock type for the DeletedUserRepository type
type MockDeletedUserRepository struct {
	mock.Mock
}

type MockDeletedUserRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDeletedUserRepository) EXPECT() *MockDeletedUserRepository_Expecter {
	return &MockDeletedUserRepository_Expecter{mock: &_m.Mock}
}

// InsertOne provides a mock function for the type MockDeletedUserRepository
func (_mock *MockDeletedUserRepository) InsertOne(ctx context.Context, document *models.User) (*mongo.InsertOneResult, error) {
	ret := _mock.Called(ctx, document)

	if len(ret) == 0 {
		panic("no return value specified for InsertOne")
	}

	var r0 *mongo.InsertOneResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.User) (*mongo.InsertOneResult, error)); ok {
		return returnFunc(ctx, document)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.User) *mongo.InsertOneResult); ok {
		r0 = returnFunc(ctx, document)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.InsertOneResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.User) error); ok {
		r1 = returnFunc(ctx, document)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDeletedUserRepository_InsertOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InsertOne'
type MockDeletedUserRepository_InsertOne_Call struct {
	*mock.Call
}

// InsertOne is a helper method to define mock.On call
//   - ctx
//   - document
func (_e *MockDeletedUserRepository_Expecter) InsertOne(ctx interface{}, document interface{}) *MockDeletedUserRepository_InsertOne_Call {
	return &MockDeletedUserRepository_InsertOne_Call{Call: _e.mock.On("InsertOne", ctx, document)}
}

func (_c *MockDeletedUserRepository_InsertOne_Call) Run(run func(ctx context.Context, document *models.User)) *MockDeletedUserRepository_InsertOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.User))
	})
	return _c
}

func (_c *MockDeletedUserRepository_InsertOne_Call) Return(insertOneResult *mongo.InsertOneResult, err error) *MockDeletedUserRepository_InsertOne_Call {
	_c.Call.Return(insertOneResult, err)
	return _c
}

func (_c *MockDeletedUserRepository_InsertOne_Call) RunAndReturn(run func(ctx context.Context, document *models.User) (*mongo.InsertOneResult, error)) *MockDeletedUserRepository_InsertOne_Call {
	_c.Call.Return(run)
	return _c
}
