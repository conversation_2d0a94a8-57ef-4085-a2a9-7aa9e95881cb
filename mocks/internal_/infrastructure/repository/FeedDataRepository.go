// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockFeedDataRepository creates a new instance of MockFeedDataRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFeedDataRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFeedDataRepository {
	mock := &MockFeedDataRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockFeedDataRepository is an autogenerated mock type for the FeedDataRepository type
type MockFeedDataRepository struct {
	mock.Mock
}

type MockFeedDataRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFeedDataRepository) EXPECT() *MockFeedDataRepository_Expecter {
	return &MockFeedDataRepository_Expecter{mock: &_m.Mock}
}

// CreateFeedData provides a mock function for the type MockFeedDataRepository
func (_mock *MockFeedDataRepository) CreateFeedData(ctx context.Context, feedData *models.FeedData) error {
	ret := _mock.Called(ctx, feedData)

	if len(ret) == 0 {
		panic("no return value specified for CreateFeedData")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.FeedData) error); ok {
		r0 = returnFunc(ctx, feedData)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockFeedDataRepository_CreateFeedData_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFeedData'
type MockFeedDataRepository_CreateFeedData_Call struct {
	*mock.Call
}

// CreateFeedData is a helper method to define mock.On call
//   - ctx
//   - feedData
func (_e *MockFeedDataRepository_Expecter) CreateFeedData(ctx interface{}, feedData interface{}) *MockFeedDataRepository_CreateFeedData_Call {
	return &MockFeedDataRepository_CreateFeedData_Call{Call: _e.mock.On("CreateFeedData", ctx, feedData)}
}

func (_c *MockFeedDataRepository_CreateFeedData_Call) Run(run func(ctx context.Context, feedData *models.FeedData)) *MockFeedDataRepository_CreateFeedData_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.FeedData))
	})
	return _c
}

func (_c *MockFeedDataRepository_CreateFeedData_Call) Return(err error) *MockFeedDataRepository_CreateFeedData_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockFeedDataRepository_CreateFeedData_Call) RunAndReturn(run func(ctx context.Context, feedData *models.FeedData) error) *MockFeedDataRepository_CreateFeedData_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeedDataById provides a mock function for the type MockFeedDataRepository
func (_mock *MockFeedDataRepository) GetFeedDataById(ctx context.Context, feedDataId primitive.ObjectID) (*models.FeedData, error) {
	ret := _mock.Called(ctx, feedDataId)

	if len(ret) == 0 {
		panic("no return value specified for GetFeedDataById")
	}

	var r0 *models.FeedData
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.FeedData, error)); ok {
		return returnFunc(ctx, feedDataId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.FeedData); ok {
		r0 = returnFunc(ctx, feedDataId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.FeedData)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, feedDataId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFeedDataRepository_GetFeedDataById_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeedDataById'
type MockFeedDataRepository_GetFeedDataById_Call struct {
	*mock.Call
}

// GetFeedDataById is a helper method to define mock.On call
//   - ctx
//   - feedDataId
func (_e *MockFeedDataRepository_Expecter) GetFeedDataById(ctx interface{}, feedDataId interface{}) *MockFeedDataRepository_GetFeedDataById_Call {
	return &MockFeedDataRepository_GetFeedDataById_Call{Call: _e.mock.On("GetFeedDataById", ctx, feedDataId)}
}

func (_c *MockFeedDataRepository_GetFeedDataById_Call) Run(run func(ctx context.Context, feedDataId primitive.ObjectID)) *MockFeedDataRepository_GetFeedDataById_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockFeedDataRepository_GetFeedDataById_Call) Return(feedData *models.FeedData, err error) *MockFeedDataRepository_GetFeedDataById_Call {
	_c.Call.Return(feedData, err)
	return _c
}

func (_c *MockFeedDataRepository_GetFeedDataById_Call) RunAndReturn(run func(ctx context.Context, feedDataId primitive.ObjectID) (*models.FeedData, error)) *MockFeedDataRepository_GetFeedDataById_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeedDataByIdGroup provides a mock function for the type MockFeedDataRepository
func (_mock *MockFeedDataRepository) GetFeedDataByIdGroup(ctx context.Context, feedIdGroup []primitive.ObjectID) ([]*models.FeedData, error) {
	ret := _mock.Called(ctx, feedIdGroup)

	if len(ret) == 0 {
		panic("no return value specified for GetFeedDataByIdGroup")
	}

	var r0 []*models.FeedData
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []primitive.ObjectID) ([]*models.FeedData, error)); ok {
		return returnFunc(ctx, feedIdGroup)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []primitive.ObjectID) []*models.FeedData); ok {
		r0 = returnFunc(ctx, feedIdGroup)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.FeedData)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, feedIdGroup)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFeedDataRepository_GetFeedDataByIdGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeedDataByIdGroup'
type MockFeedDataRepository_GetFeedDataByIdGroup_Call struct {
	*mock.Call
}

// GetFeedDataByIdGroup is a helper method to define mock.On call
//   - ctx
//   - feedIdGroup
func (_e *MockFeedDataRepository_Expecter) GetFeedDataByIdGroup(ctx interface{}, feedIdGroup interface{}) *MockFeedDataRepository_GetFeedDataByIdGroup_Call {
	return &MockFeedDataRepository_GetFeedDataByIdGroup_Call{Call: _e.mock.On("GetFeedDataByIdGroup", ctx, feedIdGroup)}
}

func (_c *MockFeedDataRepository_GetFeedDataByIdGroup_Call) Run(run func(ctx context.Context, feedIdGroup []primitive.ObjectID)) *MockFeedDataRepository_GetFeedDataByIdGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]primitive.ObjectID))
	})
	return _c
}

func (_c *MockFeedDataRepository_GetFeedDataByIdGroup_Call) Return(feedDatas []*models.FeedData, err error) *MockFeedDataRepository_GetFeedDataByIdGroup_Call {
	_c.Call.Return(feedDatas, err)
	return _c
}

func (_c *MockFeedDataRepository_GetFeedDataByIdGroup_Call) RunAndReturn(run func(ctx context.Context, feedIdGroup []primitive.ObjectID) ([]*models.FeedData, error)) *MockFeedDataRepository_GetFeedDataByIdGroup_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateLikeCount provides a mock function for the type MockFeedDataRepository
func (_mock *MockFeedDataRepository) UpdateLikeCount(ctx context.Context, feedDataId primitive.ObjectID, isLike bool) (bool, error) {
	ret := _mock.Called(ctx, feedDataId, isLike)

	if len(ret) == 0 {
		panic("no return value specified for UpdateLikeCount")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, bool) (bool, error)); ok {
		return returnFunc(ctx, feedDataId, isLike)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, bool) bool); ok {
		r0 = returnFunc(ctx, feedDataId, isLike)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, bool) error); ok {
		r1 = returnFunc(ctx, feedDataId, isLike)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFeedDataRepository_UpdateLikeCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateLikeCount'
type MockFeedDataRepository_UpdateLikeCount_Call struct {
	*mock.Call
}

// UpdateLikeCount is a helper method to define mock.On call
//   - ctx
//   - feedDataId
//   - isLike
func (_e *MockFeedDataRepository_Expecter) UpdateLikeCount(ctx interface{}, feedDataId interface{}, isLike interface{}) *MockFeedDataRepository_UpdateLikeCount_Call {
	return &MockFeedDataRepository_UpdateLikeCount_Call{Call: _e.mock.On("UpdateLikeCount", ctx, feedDataId, isLike)}
}

func (_c *MockFeedDataRepository_UpdateLikeCount_Call) Run(run func(ctx context.Context, feedDataId primitive.ObjectID, isLike bool)) *MockFeedDataRepository_UpdateLikeCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(bool))
	})
	return _c
}

func (_c *MockFeedDataRepository_UpdateLikeCount_Call) Return(b bool, err error) *MockFeedDataRepository_UpdateLikeCount_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockFeedDataRepository_UpdateLikeCount_Call) RunAndReturn(run func(ctx context.Context, feedDataId primitive.ObjectID, isLike bool) (bool, error)) *MockFeedDataRepository_UpdateLikeCount_Call {
	_c.Call.Return(run)
	return _c
}
