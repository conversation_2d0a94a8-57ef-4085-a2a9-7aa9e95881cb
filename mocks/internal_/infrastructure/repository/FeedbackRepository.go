// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
)

// NewMockFeedbackRepository creates a new instance of MockFeedbackRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFeedbackRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFeedbackRepository {
	mock := &MockFeedbackRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockFeedbackRepository is an autogenerated mock type for the FeedbackRepository type
type MockFeedbackRepository struct {
	mock.Mock
}

type MockFeedbackRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFeedbackRepository) EXPECT() *MockFeedbackRepository_Expecter {
	return &MockFeedbackRepository_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type MockFeedbackRepository
func (_mock *MockFeedbackRepository) Create(ctx context.Context, feedback *models.Feedback) error {
	ret := _mock.Called(ctx, feedback)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Feedback) error); ok {
		r0 = returnFunc(ctx, feedback)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockFeedbackRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockFeedbackRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - feedback
func (_e *MockFeedbackRepository_Expecter) Create(ctx interface{}, feedback interface{}) *MockFeedbackRepository_Create_Call {
	return &MockFeedbackRepository_Create_Call{Call: _e.mock.On("Create", ctx, feedback)}
}

func (_c *MockFeedbackRepository_Create_Call) Run(run func(ctx context.Context, feedback *models.Feedback)) *MockFeedbackRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.Feedback))
	})
	return _c
}

func (_c *MockFeedbackRepository_Create_Call) Return(err error) *MockFeedbackRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockFeedbackRepository_Create_Call) RunAndReturn(run func(ctx context.Context, feedback *models.Feedback) error) *MockFeedbackRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}
