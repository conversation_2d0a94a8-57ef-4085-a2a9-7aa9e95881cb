// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockFixturesRepository creates a new instance of MockFixturesRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFixturesRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFixturesRepository {
	mock := &MockFixturesRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockFixturesRepository is an autogenerated mock type for the FixturesRepository type
type MockFixturesRepository struct {
	mock.Mock
}

type MockFixturesRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFixturesRepository) EXPECT() *MockFixturesRepository_Expecter {
	return &MockFixturesRepository_Expecter{mock: &_m.Mock}
}

// BulkInsert provides a mock function for the type MockFixturesRepository
func (_mock *MockFixturesRepository) BulkInsert(ctx context.Context, fixtures []*models.Fictures) error {
	ret := _mock.Called(ctx, fixtures)

	if len(ret) == 0 {
		panic("no return value specified for BulkInsert")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*models.Fictures) error); ok {
		r0 = returnFunc(ctx, fixtures)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockFixturesRepository_BulkInsert_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BulkInsert'
type MockFixturesRepository_BulkInsert_Call struct {
	*mock.Call
}

// BulkInsert is a helper method to define mock.On call
//   - ctx
//   - fixtures
func (_e *MockFixturesRepository_Expecter) BulkInsert(ctx interface{}, fixtures interface{}) *MockFixturesRepository_BulkInsert_Call {
	return &MockFixturesRepository_BulkInsert_Call{Call: _e.mock.On("BulkInsert", ctx, fixtures)}
}

func (_c *MockFixturesRepository_BulkInsert_Call) Run(run func(ctx context.Context, fixtures []*models.Fictures)) *MockFixturesRepository_BulkInsert_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*models.Fictures))
	})
	return _c
}

func (_c *MockFixturesRepository_BulkInsert_Call) Return(err error) *MockFixturesRepository_BulkInsert_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockFixturesRepository_BulkInsert_Call) RunAndReturn(run func(ctx context.Context, fixtures []*models.Fictures) error) *MockFixturesRepository_BulkInsert_Call {
	_c.Call.Return(run)
	return _c
}

// GetByShowdownUserIDAndRound provides a mock function for the type MockFixturesRepository
func (_mock *MockFixturesRepository) GetByShowdownUserIDAndRound(ctx context.Context, showdownID primitive.ObjectID, participantID primitive.ObjectID, round int) (*models.Fictures, error) {
	ret := _mock.Called(ctx, showdownID, participantID, round)

	if len(ret) == 0 {
		panic("no return value specified for GetByShowdownUserIDAndRound")
	}

	var r0 *models.Fictures
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID, int) (*models.Fictures, error)); ok {
		return returnFunc(ctx, showdownID, participantID, round)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID, int) *models.Fictures); ok {
		r0 = returnFunc(ctx, showdownID, participantID, round)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Fictures)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID, int) error); ok {
		r1 = returnFunc(ctx, showdownID, participantID, round)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFixturesRepository_GetByShowdownUserIDAndRound_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByShowdownUserIDAndRound'
type MockFixturesRepository_GetByShowdownUserIDAndRound_Call struct {
	*mock.Call
}

// GetByShowdownUserIDAndRound is a helper method to define mock.On call
//   - ctx
//   - showdownID
//   - participantID
//   - round
func (_e *MockFixturesRepository_Expecter) GetByShowdownUserIDAndRound(ctx interface{}, showdownID interface{}, participantID interface{}, round interface{}) *MockFixturesRepository_GetByShowdownUserIDAndRound_Call {
	return &MockFixturesRepository_GetByShowdownUserIDAndRound_Call{Call: _e.mock.On("GetByShowdownUserIDAndRound", ctx, showdownID, participantID, round)}
}

func (_c *MockFixturesRepository_GetByShowdownUserIDAndRound_Call) Run(run func(ctx context.Context, showdownID primitive.ObjectID, participantID primitive.ObjectID, round int)) *MockFixturesRepository_GetByShowdownUserIDAndRound_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID), args[3].(int))
	})
	return _c
}

func (_c *MockFixturesRepository_GetByShowdownUserIDAndRound_Call) Return(fictures *models.Fictures, err error) *MockFixturesRepository_GetByShowdownUserIDAndRound_Call {
	_c.Call.Return(fictures, err)
	return _c
}

func (_c *MockFixturesRepository_GetByShowdownUserIDAndRound_Call) RunAndReturn(run func(ctx context.Context, showdownID primitive.ObjectID, participantID primitive.ObjectID, round int) (*models.Fictures, error)) *MockFixturesRepository_GetByShowdownUserIDAndRound_Call {
	_c.Call.Return(run)
	return _c
}

// GetFixturesByShowdownIDAndRound provides a mock function for the type MockFixturesRepository
func (_mock *MockFixturesRepository) GetFixturesByShowdownIDAndRound(ctx context.Context, showdownId primitive.ObjectID, round int) ([]*models.Fictures, error) {
	ret := _mock.Called(ctx, showdownId, round)

	if len(ret) == 0 {
		panic("no return value specified for GetFixturesByShowdownIDAndRound")
	}

	var r0 []*models.Fictures
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) ([]*models.Fictures, error)); ok {
		return returnFunc(ctx, showdownId, round)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) []*models.Fictures); ok {
		r0 = returnFunc(ctx, showdownId, round)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Fictures)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int) error); ok {
		r1 = returnFunc(ctx, showdownId, round)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFixturesRepository_GetFixturesByShowdownIDAndRound_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFixturesByShowdownIDAndRound'
type MockFixturesRepository_GetFixturesByShowdownIDAndRound_Call struct {
	*mock.Call
}

// GetFixturesByShowdownIDAndRound is a helper method to define mock.On call
//   - ctx
//   - showdownId
//   - round
func (_e *MockFixturesRepository_Expecter) GetFixturesByShowdownIDAndRound(ctx interface{}, showdownId interface{}, round interface{}) *MockFixturesRepository_GetFixturesByShowdownIDAndRound_Call {
	return &MockFixturesRepository_GetFixturesByShowdownIDAndRound_Call{Call: _e.mock.On("GetFixturesByShowdownIDAndRound", ctx, showdownId, round)}
}

func (_c *MockFixturesRepository_GetFixturesByShowdownIDAndRound_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, round int)) *MockFixturesRepository_GetFixturesByShowdownIDAndRound_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int))
	})
	return _c
}

func (_c *MockFixturesRepository_GetFixturesByShowdownIDAndRound_Call) Return(ficturess []*models.Fictures, err error) *MockFixturesRepository_GetFixturesByShowdownIDAndRound_Call {
	_c.Call.Return(ficturess, err)
	return _c
}

func (_c *MockFixturesRepository_GetFixturesByShowdownIDAndRound_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, round int) ([]*models.Fictures, error)) *MockFixturesRepository_GetFixturesByShowdownIDAndRound_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function for the type MockFixturesRepository
func (_mock *MockFixturesRepository) Update(ctx context.Context, fixture *models.Fictures) error {
	ret := _mock.Called(ctx, fixture)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Fictures) error); ok {
		r0 = returnFunc(ctx, fixture)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockFixturesRepository_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockFixturesRepository_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx
//   - fixture
func (_e *MockFixturesRepository_Expecter) Update(ctx interface{}, fixture interface{}) *MockFixturesRepository_Update_Call {
	return &MockFixturesRepository_Update_Call{Call: _e.mock.On("Update", ctx, fixture)}
}

func (_c *MockFixturesRepository_Update_Call) Run(run func(ctx context.Context, fixture *models.Fictures)) *MockFixturesRepository_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.Fictures))
	})
	return _c
}

func (_c *MockFixturesRepository_Update_Call) Return(err error) *MockFixturesRepository_Update_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockFixturesRepository_Update_Call) RunAndReturn(run func(ctx context.Context, fixture *models.Fictures) error) *MockFixturesRepository_Update_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function for the type MockFixturesRepository
func (_mock *MockFixturesRepository) UpdateOne(ctx context.Context, filter bson.M, update bson.M) error {
	ret := _mock.Called(ctx, filter, update)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, bson.M) error); ok {
		r0 = returnFunc(ctx, filter, update)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockFixturesRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockFixturesRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx
//   - filter
//   - update
func (_e *MockFixturesRepository_Expecter) UpdateOne(ctx interface{}, filter interface{}, update interface{}) *MockFixturesRepository_UpdateOne_Call {
	return &MockFixturesRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne", ctx, filter, update)}
}

func (_c *MockFixturesRepository_UpdateOne_Call) Run(run func(ctx context.Context, filter bson.M, update bson.M)) *MockFixturesRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M), args[2].(bson.M))
	})
	return _c
}

func (_c *MockFixturesRepository_UpdateOne_Call) Return(err error) *MockFixturesRepository_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockFixturesRepository_UpdateOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, update bson.M) error) *MockFixturesRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}
