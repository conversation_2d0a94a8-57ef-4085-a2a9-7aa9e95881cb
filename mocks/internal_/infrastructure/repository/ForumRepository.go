// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockForumRepository creates a new instance of MockForumRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockForumRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockForumRepository {
	mock := &MockForumRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockForumRepository is an autogenerated mock type for the ForumRepository type
type MockForumRepository struct {
	mock.Mock
}

type MockForumRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockForumRepository) EXPECT() *MockForumRepository_Expecter {
	return &MockForumRepository_Expecter{mock: &_m.Mock}
}

// CountForumReplies provides a mock function for the type MockForumRepository
func (_mock *MockForumRepository) CountForumReplies(ctx context.Context, threadID primitive.ObjectID) (int64, error) {
	ret := _mock.Called(ctx, threadID)

	if len(ret) == 0 {
		panic("no return value specified for CountForumReplies")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (int64, error)); ok {
		return returnFunc(ctx, threadID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) int64); ok {
		r0 = returnFunc(ctx, threadID)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, threadID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumRepository_CountForumReplies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountForumReplies'
type MockForumRepository_CountForumReplies_Call struct {
	*mock.Call
}

// CountForumReplies is a helper method to define mock.On call
//   - ctx
//   - threadID
func (_e *MockForumRepository_Expecter) CountForumReplies(ctx interface{}, threadID interface{}) *MockForumRepository_CountForumReplies_Call {
	return &MockForumRepository_CountForumReplies_Call{Call: _e.mock.On("CountForumReplies", ctx, threadID)}
}

func (_c *MockForumRepository_CountForumReplies_Call) Run(run func(ctx context.Context, threadID primitive.ObjectID)) *MockForumRepository_CountForumReplies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockForumRepository_CountForumReplies_Call) Return(n int64, err error) *MockForumRepository_CountForumReplies_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockForumRepository_CountForumReplies_Call) RunAndReturn(run func(ctx context.Context, threadID primitive.ObjectID) (int64, error)) *MockForumRepository_CountForumReplies_Call {
	_c.Call.Return(run)
	return _c
}

// CountForumThreads provides a mock function for the type MockForumRepository
func (_mock *MockForumRepository) CountForumThreads(ctx context.Context, forumID primitive.ObjectID) (int64, error) {
	ret := _mock.Called(ctx, forumID)

	if len(ret) == 0 {
		panic("no return value specified for CountForumThreads")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (int64, error)); ok {
		return returnFunc(ctx, forumID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) int64); ok {
		r0 = returnFunc(ctx, forumID)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, forumID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumRepository_CountForumThreads_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountForumThreads'
type MockForumRepository_CountForumThreads_Call struct {
	*mock.Call
}

// CountForumThreads is a helper method to define mock.On call
//   - ctx
//   - forumID
func (_e *MockForumRepository_Expecter) CountForumThreads(ctx interface{}, forumID interface{}) *MockForumRepository_CountForumThreads_Call {
	return &MockForumRepository_CountForumThreads_Call{Call: _e.mock.On("CountForumThreads", ctx, forumID)}
}

func (_c *MockForumRepository_CountForumThreads_Call) Run(run func(ctx context.Context, forumID primitive.ObjectID)) *MockForumRepository_CountForumThreads_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockForumRepository_CountForumThreads_Call) Return(n int64, err error) *MockForumRepository_CountForumThreads_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockForumRepository_CountForumThreads_Call) RunAndReturn(run func(ctx context.Context, forumID primitive.ObjectID) (int64, error)) *MockForumRepository_CountForumThreads_Call {
	_c.Call.Return(run)
	return _c
}

// CountForums provides a mock function for the type MockForumRepository
func (_mock *MockForumRepository) CountForums(ctx context.Context, clubID primitive.ObjectID) (int64, error) {
	ret := _mock.Called(ctx, clubID)

	if len(ret) == 0 {
		panic("no return value specified for CountForums")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (int64, error)); ok {
		return returnFunc(ctx, clubID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) int64); ok {
		r0 = returnFunc(ctx, clubID)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, clubID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumRepository_CountForums_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountForums'
type MockForumRepository_CountForums_Call struct {
	*mock.Call
}

// CountForums is a helper method to define mock.On call
//   - ctx
//   - clubID
func (_e *MockForumRepository_Expecter) CountForums(ctx interface{}, clubID interface{}) *MockForumRepository_CountForums_Call {
	return &MockForumRepository_CountForums_Call{Call: _e.mock.On("CountForums", ctx, clubID)}
}

func (_c *MockForumRepository_CountForums_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID)) *MockForumRepository_CountForums_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockForumRepository_CountForums_Call) Return(n int64, err error) *MockForumRepository_CountForums_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockForumRepository_CountForums_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID) (int64, error)) *MockForumRepository_CountForums_Call {
	_c.Call.Return(run)
	return _c
}

// CreateForum provides a mock function for the type MockForumRepository
func (_mock *MockForumRepository) CreateForum(ctx context.Context, forum *models.Forum) (*models.Forum, error) {
	ret := _mock.Called(ctx, forum)

	if len(ret) == 0 {
		panic("no return value specified for CreateForum")
	}

	var r0 *models.Forum
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Forum) (*models.Forum, error)); ok {
		return returnFunc(ctx, forum)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Forum) *models.Forum); ok {
		r0 = returnFunc(ctx, forum)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Forum)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.Forum) error); ok {
		r1 = returnFunc(ctx, forum)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumRepository_CreateForum_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateForum'
type MockForumRepository_CreateForum_Call struct {
	*mock.Call
}

// CreateForum is a helper method to define mock.On call
//   - ctx
//   - forum
func (_e *MockForumRepository_Expecter) CreateForum(ctx interface{}, forum interface{}) *MockForumRepository_CreateForum_Call {
	return &MockForumRepository_CreateForum_Call{Call: _e.mock.On("CreateForum", ctx, forum)}
}

func (_c *MockForumRepository_CreateForum_Call) Run(run func(ctx context.Context, forum *models.Forum)) *MockForumRepository_CreateForum_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.Forum))
	})
	return _c
}

func (_c *MockForumRepository_CreateForum_Call) Return(forum1 *models.Forum, err error) *MockForumRepository_CreateForum_Call {
	_c.Call.Return(forum1, err)
	return _c
}

func (_c *MockForumRepository_CreateForum_Call) RunAndReturn(run func(ctx context.Context, forum *models.Forum) (*models.Forum, error)) *MockForumRepository_CreateForum_Call {
	_c.Call.Return(run)
	return _c
}

// CreateForumReply provides a mock function for the type MockForumRepository
func (_mock *MockForumRepository) CreateForumReply(ctx context.Context, reply *models.ForumReply) (*models.ForumReply, error) {
	ret := _mock.Called(ctx, reply)

	if len(ret) == 0 {
		panic("no return value specified for CreateForumReply")
	}

	var r0 *models.ForumReply
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ForumReply) (*models.ForumReply, error)); ok {
		return returnFunc(ctx, reply)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ForumReply) *models.ForumReply); ok {
		r0 = returnFunc(ctx, reply)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ForumReply)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.ForumReply) error); ok {
		r1 = returnFunc(ctx, reply)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumRepository_CreateForumReply_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateForumReply'
type MockForumRepository_CreateForumReply_Call struct {
	*mock.Call
}

// CreateForumReply is a helper method to define mock.On call
//   - ctx
//   - reply
func (_e *MockForumRepository_Expecter) CreateForumReply(ctx interface{}, reply interface{}) *MockForumRepository_CreateForumReply_Call {
	return &MockForumRepository_CreateForumReply_Call{Call: _e.mock.On("CreateForumReply", ctx, reply)}
}

func (_c *MockForumRepository_CreateForumReply_Call) Run(run func(ctx context.Context, reply *models.ForumReply)) *MockForumRepository_CreateForumReply_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.ForumReply))
	})
	return _c
}

func (_c *MockForumRepository_CreateForumReply_Call) Return(forumReply *models.ForumReply, err error) *MockForumRepository_CreateForumReply_Call {
	_c.Call.Return(forumReply, err)
	return _c
}

func (_c *MockForumRepository_CreateForumReply_Call) RunAndReturn(run func(ctx context.Context, reply *models.ForumReply) (*models.ForumReply, error)) *MockForumRepository_CreateForumReply_Call {
	_c.Call.Return(run)
	return _c
}

// CreateForumThread provides a mock function for the type MockForumRepository
func (_mock *MockForumRepository) CreateForumThread(ctx context.Context, thread *models.ForumThread) (*models.ForumThread, error) {
	ret := _mock.Called(ctx, thread)

	if len(ret) == 0 {
		panic("no return value specified for CreateForumThread")
	}

	var r0 *models.ForumThread
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ForumThread) (*models.ForumThread, error)); ok {
		return returnFunc(ctx, thread)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ForumThread) *models.ForumThread); ok {
		r0 = returnFunc(ctx, thread)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ForumThread)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.ForumThread) error); ok {
		r1 = returnFunc(ctx, thread)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumRepository_CreateForumThread_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateForumThread'
type MockForumRepository_CreateForumThread_Call struct {
	*mock.Call
}

// CreateForumThread is a helper method to define mock.On call
//   - ctx
//   - thread
func (_e *MockForumRepository_Expecter) CreateForumThread(ctx interface{}, thread interface{}) *MockForumRepository_CreateForumThread_Call {
	return &MockForumRepository_CreateForumThread_Call{Call: _e.mock.On("CreateForumThread", ctx, thread)}
}

func (_c *MockForumRepository_CreateForumThread_Call) Run(run func(ctx context.Context, thread *models.ForumThread)) *MockForumRepository_CreateForumThread_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.ForumThread))
	})
	return _c
}

func (_c *MockForumRepository_CreateForumThread_Call) Return(forumThread *models.ForumThread, err error) *MockForumRepository_CreateForumThread_Call {
	_c.Call.Return(forumThread, err)
	return _c
}

func (_c *MockForumRepository_CreateForumThread_Call) RunAndReturn(run func(ctx context.Context, thread *models.ForumThread) (*models.ForumThread, error)) *MockForumRepository_CreateForumThread_Call {
	_c.Call.Return(run)
	return _c
}

// GetForum provides a mock function for the type MockForumRepository
func (_mock *MockForumRepository) GetForum(ctx context.Context, forumID primitive.ObjectID) (*models.Forum, error) {
	ret := _mock.Called(ctx, forumID)

	if len(ret) == 0 {
		panic("no return value specified for GetForum")
	}

	var r0 *models.Forum
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Forum, error)); ok {
		return returnFunc(ctx, forumID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Forum); ok {
		r0 = returnFunc(ctx, forumID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Forum)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, forumID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumRepository_GetForum_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetForum'
type MockForumRepository_GetForum_Call struct {
	*mock.Call
}

// GetForum is a helper method to define mock.On call
//   - ctx
//   - forumID
func (_e *MockForumRepository_Expecter) GetForum(ctx interface{}, forumID interface{}) *MockForumRepository_GetForum_Call {
	return &MockForumRepository_GetForum_Call{Call: _e.mock.On("GetForum", ctx, forumID)}
}

func (_c *MockForumRepository_GetForum_Call) Run(run func(ctx context.Context, forumID primitive.ObjectID)) *MockForumRepository_GetForum_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockForumRepository_GetForum_Call) Return(forum *models.Forum, err error) *MockForumRepository_GetForum_Call {
	_c.Call.Return(forum, err)
	return _c
}

func (_c *MockForumRepository_GetForum_Call) RunAndReturn(run func(ctx context.Context, forumID primitive.ObjectID) (*models.Forum, error)) *MockForumRepository_GetForum_Call {
	_c.Call.Return(run)
	return _c
}

// GetForumReplies provides a mock function for the type MockForumRepository
func (_mock *MockForumRepository) GetForumReplies(ctx context.Context, threadID primitive.ObjectID, pageNumber *int, pageSize *int, sort bson.D) ([]*models.ForumReply, error) {
	ret := _mock.Called(ctx, threadID, pageNumber, pageSize, sort)

	if len(ret) == 0 {
		panic("no return value specified for GetForumReplies")
	}

	var r0 []*models.ForumReply
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int, bson.D) ([]*models.ForumReply, error)); ok {
		return returnFunc(ctx, threadID, pageNumber, pageSize, sort)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int, bson.D) []*models.ForumReply); ok {
		r0 = returnFunc(ctx, threadID, pageNumber, pageSize, sort)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.ForumReply)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *int, *int, bson.D) error); ok {
		r1 = returnFunc(ctx, threadID, pageNumber, pageSize, sort)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumRepository_GetForumReplies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetForumReplies'
type MockForumRepository_GetForumReplies_Call struct {
	*mock.Call
}

// GetForumReplies is a helper method to define mock.On call
//   - ctx
//   - threadID
//   - pageNumber
//   - pageSize
//   - sort
func (_e *MockForumRepository_Expecter) GetForumReplies(ctx interface{}, threadID interface{}, pageNumber interface{}, pageSize interface{}, sort interface{}) *MockForumRepository_GetForumReplies_Call {
	return &MockForumRepository_GetForumReplies_Call{Call: _e.mock.On("GetForumReplies", ctx, threadID, pageNumber, pageSize, sort)}
}

func (_c *MockForumRepository_GetForumReplies_Call) Run(run func(ctx context.Context, threadID primitive.ObjectID, pageNumber *int, pageSize *int, sort bson.D)) *MockForumRepository_GetForumReplies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(*int), args[3].(*int), args[4].(bson.D))
	})
	return _c
}

func (_c *MockForumRepository_GetForumReplies_Call) Return(forumReplys []*models.ForumReply, err error) *MockForumRepository_GetForumReplies_Call {
	_c.Call.Return(forumReplys, err)
	return _c
}

func (_c *MockForumRepository_GetForumReplies_Call) RunAndReturn(run func(ctx context.Context, threadID primitive.ObjectID, pageNumber *int, pageSize *int, sort bson.D) ([]*models.ForumReply, error)) *MockForumRepository_GetForumReplies_Call {
	_c.Call.Return(run)
	return _c
}

// GetForumThread provides a mock function for the type MockForumRepository
func (_mock *MockForumRepository) GetForumThread(ctx context.Context, threadId primitive.ObjectID) (*models.ForumThread, error) {
	ret := _mock.Called(ctx, threadId)

	if len(ret) == 0 {
		panic("no return value specified for GetForumThread")
	}

	var r0 *models.ForumThread
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.ForumThread, error)); ok {
		return returnFunc(ctx, threadId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.ForumThread); ok {
		r0 = returnFunc(ctx, threadId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ForumThread)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, threadId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumRepository_GetForumThread_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetForumThread'
type MockForumRepository_GetForumThread_Call struct {
	*mock.Call
}

// GetForumThread is a helper method to define mock.On call
//   - ctx
//   - threadId
func (_e *MockForumRepository_Expecter) GetForumThread(ctx interface{}, threadId interface{}) *MockForumRepository_GetForumThread_Call {
	return &MockForumRepository_GetForumThread_Call{Call: _e.mock.On("GetForumThread", ctx, threadId)}
}

func (_c *MockForumRepository_GetForumThread_Call) Run(run func(ctx context.Context, threadId primitive.ObjectID)) *MockForumRepository_GetForumThread_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockForumRepository_GetForumThread_Call) Return(forumThread *models.ForumThread, err error) *MockForumRepository_GetForumThread_Call {
	_c.Call.Return(forumThread, err)
	return _c
}

func (_c *MockForumRepository_GetForumThread_Call) RunAndReturn(run func(ctx context.Context, threadId primitive.ObjectID) (*models.ForumThread, error)) *MockForumRepository_GetForumThread_Call {
	_c.Call.Return(run)
	return _c
}

// GetForumThreads provides a mock function for the type MockForumRepository
func (_mock *MockForumRepository) GetForumThreads(ctx context.Context, forumID primitive.ObjectID, pageNumber *int, pageSize *int, sort bson.D) ([]*models.ForumThread, error) {
	ret := _mock.Called(ctx, forumID, pageNumber, pageSize, sort)

	if len(ret) == 0 {
		panic("no return value specified for GetForumThreads")
	}

	var r0 []*models.ForumThread
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int, bson.D) ([]*models.ForumThread, error)); ok {
		return returnFunc(ctx, forumID, pageNumber, pageSize, sort)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int, bson.D) []*models.ForumThread); ok {
		r0 = returnFunc(ctx, forumID, pageNumber, pageSize, sort)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.ForumThread)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *int, *int, bson.D) error); ok {
		r1 = returnFunc(ctx, forumID, pageNumber, pageSize, sort)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumRepository_GetForumThreads_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetForumThreads'
type MockForumRepository_GetForumThreads_Call struct {
	*mock.Call
}

// GetForumThreads is a helper method to define mock.On call
//   - ctx
//   - forumID
//   - pageNumber
//   - pageSize
//   - sort
func (_e *MockForumRepository_Expecter) GetForumThreads(ctx interface{}, forumID interface{}, pageNumber interface{}, pageSize interface{}, sort interface{}) *MockForumRepository_GetForumThreads_Call {
	return &MockForumRepository_GetForumThreads_Call{Call: _e.mock.On("GetForumThreads", ctx, forumID, pageNumber, pageSize, sort)}
}

func (_c *MockForumRepository_GetForumThreads_Call) Run(run func(ctx context.Context, forumID primitive.ObjectID, pageNumber *int, pageSize *int, sort bson.D)) *MockForumRepository_GetForumThreads_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(*int), args[3].(*int), args[4].(bson.D))
	})
	return _c
}

func (_c *MockForumRepository_GetForumThreads_Call) Return(forumThreads []*models.ForumThread, err error) *MockForumRepository_GetForumThreads_Call {
	_c.Call.Return(forumThreads, err)
	return _c
}

func (_c *MockForumRepository_GetForumThreads_Call) RunAndReturn(run func(ctx context.Context, forumID primitive.ObjectID, pageNumber *int, pageSize *int, sort bson.D) ([]*models.ForumThread, error)) *MockForumRepository_GetForumThreads_Call {
	_c.Call.Return(run)
	return _c
}

// GetForums provides a mock function for the type MockForumRepository
func (_mock *MockForumRepository) GetForums(ctx context.Context, clubID primitive.ObjectID, pageNumber *int, pageSize *int, sort bson.D) ([]*models.Forum, error) {
	ret := _mock.Called(ctx, clubID, pageNumber, pageSize, sort)

	if len(ret) == 0 {
		panic("no return value specified for GetForums")
	}

	var r0 []*models.Forum
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int, bson.D) ([]*models.Forum, error)); ok {
		return returnFunc(ctx, clubID, pageNumber, pageSize, sort)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int, bson.D) []*models.Forum); ok {
		r0 = returnFunc(ctx, clubID, pageNumber, pageSize, sort)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Forum)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *int, *int, bson.D) error); ok {
		r1 = returnFunc(ctx, clubID, pageNumber, pageSize, sort)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumRepository_GetForums_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetForums'
type MockForumRepository_GetForums_Call struct {
	*mock.Call
}

// GetForums is a helper method to define mock.On call
//   - ctx
//   - clubID
//   - pageNumber
//   - pageSize
//   - sort
func (_e *MockForumRepository_Expecter) GetForums(ctx interface{}, clubID interface{}, pageNumber interface{}, pageSize interface{}, sort interface{}) *MockForumRepository_GetForums_Call {
	return &MockForumRepository_GetForums_Call{Call: _e.mock.On("GetForums", ctx, clubID, pageNumber, pageSize, sort)}
}

func (_c *MockForumRepository_GetForums_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, pageNumber *int, pageSize *int, sort bson.D)) *MockForumRepository_GetForums_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(*int), args[3].(*int), args[4].(bson.D))
	})
	return _c
}

func (_c *MockForumRepository_GetForums_Call) Return(forums []*models.Forum, err error) *MockForumRepository_GetForums_Call {
	_c.Call.Return(forums, err)
	return _c
}

func (_c *MockForumRepository_GetForums_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, pageNumber *int, pageSize *int, sort bson.D) ([]*models.Forum, error)) *MockForumRepository_GetForums_Call {
	_c.Call.Return(run)
	return _c
}
