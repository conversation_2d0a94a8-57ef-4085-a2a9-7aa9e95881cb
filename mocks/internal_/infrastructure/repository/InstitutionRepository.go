// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockInstitutionRepository creates a new instance of MockInstitutionRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockInstitutionRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockInstitutionRepository {
	mock := &MockInstitutionRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockInstitutionRepository is an autogenerated mock type for the InstitutionRepository type
type MockInstitutionRepository struct {
	mock.Mock
}

type MockInstitutionRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockInstitutionRepository) EXPECT() *MockInstitutionRepository_Expecter {
	return &MockInstitutionRepository_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type MockInstitutionRepository
func (_mock *MockInstitutionRepository) Create(ctx context.Context, input models.CreateInstitutionInput) (*models.Institution, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *models.Institution
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateInstitutionInput) (*models.Institution, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateInstitutionInput) *models.Institution); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Institution)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateInstitutionInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockInstitutionRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockInstitutionRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - input
func (_e *MockInstitutionRepository_Expecter) Create(ctx interface{}, input interface{}) *MockInstitutionRepository_Create_Call {
	return &MockInstitutionRepository_Create_Call{Call: _e.mock.On("Create", ctx, input)}
}

func (_c *MockInstitutionRepository_Create_Call) Run(run func(ctx context.Context, input models.CreateInstitutionInput)) *MockInstitutionRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.CreateInstitutionInput))
	})
	return _c
}

func (_c *MockInstitutionRepository_Create_Call) Return(institution *models.Institution, err error) *MockInstitutionRepository_Create_Call {
	_c.Call.Return(institution, err)
	return _c
}

func (_c *MockInstitutionRepository_Create_Call) RunAndReturn(run func(ctx context.Context, input models.CreateInstitutionInput) (*models.Institution, error)) *MockInstitutionRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function for the type MockInstitutionRepository
func (_mock *MockInstitutionRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockInstitutionRepository_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockInstitutionRepository_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockInstitutionRepository_Expecter) Delete(ctx interface{}, id interface{}) *MockInstitutionRepository_Delete_Call {
	return &MockInstitutionRepository_Delete_Call{Call: _e.mock.On("Delete", ctx, id)}
}

func (_c *MockInstitutionRepository_Delete_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockInstitutionRepository_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockInstitutionRepository_Delete_Call) Return(err error) *MockInstitutionRepository_Delete_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockInstitutionRepository_Delete_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) error) *MockInstitutionRepository_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// FindByID provides a mock function for the type MockInstitutionRepository
func (_mock *MockInstitutionRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.Institution, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindByID")
	}

	var r0 *models.Institution
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Institution, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Institution); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Institution)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockInstitutionRepository_FindByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByID'
type MockInstitutionRepository_FindByID_Call struct {
	*mock.Call
}

// FindByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockInstitutionRepository_Expecter) FindByID(ctx interface{}, id interface{}) *MockInstitutionRepository_FindByID_Call {
	return &MockInstitutionRepository_FindByID_Call{Call: _e.mock.On("FindByID", ctx, id)}
}

func (_c *MockInstitutionRepository_FindByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockInstitutionRepository_FindByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockInstitutionRepository_FindByID_Call) Return(institution *models.Institution, err error) *MockInstitutionRepository_FindByID_Call {
	_c.Call.Return(institution, err)
	return _c
}

func (_c *MockInstitutionRepository_FindByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.Institution, error)) *MockInstitutionRepository_FindByID_Call {
	_c.Call.Return(run)
	return _c
}

// FindBySlug provides a mock function for the type MockInstitutionRepository
func (_mock *MockInstitutionRepository) FindBySlug(ctx context.Context, slug string) (*models.Institution, error) {
	ret := _mock.Called(ctx, slug)

	if len(ret) == 0 {
		panic("no return value specified for FindBySlug")
	}

	var r0 *models.Institution
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*models.Institution, error)); ok {
		return returnFunc(ctx, slug)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *models.Institution); ok {
		r0 = returnFunc(ctx, slug)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Institution)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, slug)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockInstitutionRepository_FindBySlug_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindBySlug'
type MockInstitutionRepository_FindBySlug_Call struct {
	*mock.Call
}

// FindBySlug is a helper method to define mock.On call
//   - ctx
//   - slug
func (_e *MockInstitutionRepository_Expecter) FindBySlug(ctx interface{}, slug interface{}) *MockInstitutionRepository_FindBySlug_Call {
	return &MockInstitutionRepository_FindBySlug_Call{Call: _e.mock.On("FindBySlug", ctx, slug)}
}

func (_c *MockInstitutionRepository_FindBySlug_Call) Run(run func(ctx context.Context, slug string)) *MockInstitutionRepository_FindBySlug_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockInstitutionRepository_FindBySlug_Call) Return(institution *models.Institution, err error) *MockInstitutionRepository_FindBySlug_Call {
	_c.Call.Return(institution, err)
	return _c
}

func (_c *MockInstitutionRepository_FindBySlug_Call) RunAndReturn(run func(ctx context.Context, slug string) (*models.Institution, error)) *MockInstitutionRepository_FindBySlug_Call {
	_c.Call.Return(run)
	return _c
}

// Search provides a mock function for the type MockInstitutionRepository
func (_mock *MockInstitutionRepository) Search(ctx context.Context, filter models.InstitutionFilter) ([]*models.Institution, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for Search")
	}

	var r0 []*models.Institution
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.InstitutionFilter) ([]*models.Institution, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.InstitutionFilter) []*models.Institution); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Institution)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.InstitutionFilter) error); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockInstitutionRepository_Search_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Search'
type MockInstitutionRepository_Search_Call struct {
	*mock.Call
}

// Search is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *MockInstitutionRepository_Expecter) Search(ctx interface{}, filter interface{}) *MockInstitutionRepository_Search_Call {
	return &MockInstitutionRepository_Search_Call{Call: _e.mock.On("Search", ctx, filter)}
}

func (_c *MockInstitutionRepository_Search_Call) Run(run func(ctx context.Context, filter models.InstitutionFilter)) *MockInstitutionRepository_Search_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.InstitutionFilter))
	})
	return _c
}

func (_c *MockInstitutionRepository_Search_Call) Return(institutions []*models.Institution, err error) *MockInstitutionRepository_Search_Call {
	_c.Call.Return(institutions, err)
	return _c
}

func (_c *MockInstitutionRepository_Search_Call) RunAndReturn(run func(ctx context.Context, filter models.InstitutionFilter) ([]*models.Institution, error)) *MockInstitutionRepository_Search_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function for the type MockInstitutionRepository
func (_mock *MockInstitutionRepository) Update(ctx context.Context, institution *models.Institution) error {
	ret := _mock.Called(ctx, institution)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Institution) error); ok {
		r0 = returnFunc(ctx, institution)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockInstitutionRepository_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockInstitutionRepository_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx
//   - institution
func (_e *MockInstitutionRepository_Expecter) Update(ctx interface{}, institution interface{}) *MockInstitutionRepository_Update_Call {
	return &MockInstitutionRepository_Update_Call{Call: _e.mock.On("Update", ctx, institution)}
}

func (_c *MockInstitutionRepository_Update_Call) Run(run func(ctx context.Context, institution *models.Institution)) *MockInstitutionRepository_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.Institution))
	})
	return _c
}

func (_c *MockInstitutionRepository_Update_Call) Return(err error) *MockInstitutionRepository_Update_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockInstitutionRepository_Update_Call) RunAndReturn(run func(ctx context.Context, institution *models.Institution) error) *MockInstitutionRepository_Update_Call {
	_c.Call.Return(run)
	return _c
}
