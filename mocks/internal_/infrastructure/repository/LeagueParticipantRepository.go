// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockLeagueParticipantRepository creates a new instance of MockLeagueParticipantRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockLeagueParticipantRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockLeagueParticipantRepository {
	mock := &MockLeagueParticipantRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockLeagueParticipantRepository is an autogenerated mock type for the LeagueParticipantRepository type
type MockLeagueParticipantRepository struct {
	mock.Mock
}

type MockLeagueParticipantRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockLeagueParticipantRepository) EXPECT() *MockLeagueParticipantRepository_Expecter {
	return &MockLeagueParticipantRepository_Expecter{mock: &_m.Mock}
}

// CreateParticipant provides a mock function for the type MockLeagueParticipantRepository
func (_mock *MockLeagueParticipantRepository) CreateParticipant(ctx context.Context, participant *models.LeagueParticipant) error {
	ret := _mock.Called(ctx, participant)

	if len(ret) == 0 {
		panic("no return value specified for CreateParticipant")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.LeagueParticipant) error); ok {
		r0 = returnFunc(ctx, participant)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockLeagueParticipantRepository_CreateParticipant_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateParticipant'
type MockLeagueParticipantRepository_CreateParticipant_Call struct {
	*mock.Call
}

// CreateParticipant is a helper method to define mock.On call
//   - ctx
//   - participant
func (_e *MockLeagueParticipantRepository_Expecter) CreateParticipant(ctx interface{}, participant interface{}) *MockLeagueParticipantRepository_CreateParticipant_Call {
	return &MockLeagueParticipantRepository_CreateParticipant_Call{Call: _e.mock.On("CreateParticipant", ctx, participant)}
}

func (_c *MockLeagueParticipantRepository_CreateParticipant_Call) Run(run func(ctx context.Context, participant *models.LeagueParticipant)) *MockLeagueParticipantRepository_CreateParticipant_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.LeagueParticipant))
	})
	return _c
}

func (_c *MockLeagueParticipantRepository_CreateParticipant_Call) Return(err error) *MockLeagueParticipantRepository_CreateParticipant_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockLeagueParticipantRepository_CreateParticipant_Call) RunAndReturn(run func(ctx context.Context, participant *models.LeagueParticipant) error) *MockLeagueParticipantRepository_CreateParticipant_Call {
	_c.Call.Return(run)
	return _c
}

// GetParticipantByUserAndLeagueID provides a mock function for the type MockLeagueParticipantRepository
func (_mock *MockLeagueParticipantRepository) GetParticipantByUserAndLeagueID(ctx context.Context, userID primitive.ObjectID, leagueID primitive.ObjectID) (*models.LeagueParticipant, error) {
	ret := _mock.Called(ctx, userID, leagueID)

	if len(ret) == 0 {
		panic("no return value specified for GetParticipantByUserAndLeagueID")
	}

	var r0 *models.LeagueParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (*models.LeagueParticipant, error)); ok {
		return returnFunc(ctx, userID, leagueID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) *models.LeagueParticipant); ok {
		r0 = returnFunc(ctx, userID, leagueID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.LeagueParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID, leagueID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeagueParticipantRepository_GetParticipantByUserAndLeagueID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetParticipantByUserAndLeagueID'
type MockLeagueParticipantRepository_GetParticipantByUserAndLeagueID_Call struct {
	*mock.Call
}

// GetParticipantByUserAndLeagueID is a helper method to define mock.On call
//   - ctx
//   - userID
//   - leagueID
func (_e *MockLeagueParticipantRepository_Expecter) GetParticipantByUserAndLeagueID(ctx interface{}, userID interface{}, leagueID interface{}) *MockLeagueParticipantRepository_GetParticipantByUserAndLeagueID_Call {
	return &MockLeagueParticipantRepository_GetParticipantByUserAndLeagueID_Call{Call: _e.mock.On("GetParticipantByUserAndLeagueID", ctx, userID, leagueID)}
}

func (_c *MockLeagueParticipantRepository_GetParticipantByUserAndLeagueID_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, leagueID primitive.ObjectID)) *MockLeagueParticipantRepository_GetParticipantByUserAndLeagueID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockLeagueParticipantRepository_GetParticipantByUserAndLeagueID_Call) Return(leagueParticipant *models.LeagueParticipant, err error) *MockLeagueParticipantRepository_GetParticipantByUserAndLeagueID_Call {
	_c.Call.Return(leagueParticipant, err)
	return _c
}

func (_c *MockLeagueParticipantRepository_GetParticipantByUserAndLeagueID_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, leagueID primitive.ObjectID) (*models.LeagueParticipant, error)) *MockLeagueParticipantRepository_GetParticipantByUserAndLeagueID_Call {
	_c.Call.Return(run)
	return _c
}

// GetParticipantsByLeagueID provides a mock function for the type MockLeagueParticipantRepository
func (_mock *MockLeagueParticipantRepository) GetParticipantsByLeagueID(ctx context.Context, leagueID primitive.ObjectID) ([]*models.LeagueParticipant, error) {
	ret := _mock.Called(ctx, leagueID)

	if len(ret) == 0 {
		panic("no return value specified for GetParticipantsByLeagueID")
	}

	var r0 []*models.LeagueParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) ([]*models.LeagueParticipant, error)); ok {
		return returnFunc(ctx, leagueID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) []*models.LeagueParticipant); ok {
		r0 = returnFunc(ctx, leagueID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.LeagueParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, leagueID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeagueParticipantRepository_GetParticipantsByLeagueID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetParticipantsByLeagueID'
type MockLeagueParticipantRepository_GetParticipantsByLeagueID_Call struct {
	*mock.Call
}

// GetParticipantsByLeagueID is a helper method to define mock.On call
//   - ctx
//   - leagueID
func (_e *MockLeagueParticipantRepository_Expecter) GetParticipantsByLeagueID(ctx interface{}, leagueID interface{}) *MockLeagueParticipantRepository_GetParticipantsByLeagueID_Call {
	return &MockLeagueParticipantRepository_GetParticipantsByLeagueID_Call{Call: _e.mock.On("GetParticipantsByLeagueID", ctx, leagueID)}
}

func (_c *MockLeagueParticipantRepository_GetParticipantsByLeagueID_Call) Run(run func(ctx context.Context, leagueID primitive.ObjectID)) *MockLeagueParticipantRepository_GetParticipantsByLeagueID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockLeagueParticipantRepository_GetParticipantsByLeagueID_Call) Return(leagueParticipants []*models.LeagueParticipant, err error) *MockLeagueParticipantRepository_GetParticipantsByLeagueID_Call {
	_c.Call.Return(leagueParticipants, err)
	return _c
}

func (_c *MockLeagueParticipantRepository_GetParticipantsByLeagueID_Call) RunAndReturn(run func(ctx context.Context, leagueID primitive.ObjectID) ([]*models.LeagueParticipant, error)) *MockLeagueParticipantRepository_GetParticipantsByLeagueID_Call {
	_c.Call.Return(run)
	return _c
}

// GetParticipantsCountInLeague provides a mock function for the type MockLeagueParticipantRepository
func (_mock *MockLeagueParticipantRepository) GetParticipantsCountInLeague(ctx context.Context, leagueID primitive.ObjectID) int64 {
	ret := _mock.Called(ctx, leagueID)

	if len(ret) == 0 {
		panic("no return value specified for GetParticipantsCountInLeague")
	}

	var r0 int64
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) int64); ok {
		r0 = returnFunc(ctx, leagueID)
	} else {
		r0 = ret.Get(0).(int64)
	}
	return r0
}

// MockLeagueParticipantRepository_GetParticipantsCountInLeague_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetParticipantsCountInLeague'
type MockLeagueParticipantRepository_GetParticipantsCountInLeague_Call struct {
	*mock.Call
}

// GetParticipantsCountInLeague is a helper method to define mock.On call
//   - ctx
//   - leagueID
func (_e *MockLeagueParticipantRepository_Expecter) GetParticipantsCountInLeague(ctx interface{}, leagueID interface{}) *MockLeagueParticipantRepository_GetParticipantsCountInLeague_Call {
	return &MockLeagueParticipantRepository_GetParticipantsCountInLeague_Call{Call: _e.mock.On("GetParticipantsCountInLeague", ctx, leagueID)}
}

func (_c *MockLeagueParticipantRepository_GetParticipantsCountInLeague_Call) Run(run func(ctx context.Context, leagueID primitive.ObjectID)) *MockLeagueParticipantRepository_GetParticipantsCountInLeague_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockLeagueParticipantRepository_GetParticipantsCountInLeague_Call) Return(n int64) *MockLeagueParticipantRepository_GetParticipantsCountInLeague_Call {
	_c.Call.Return(n)
	return _c
}

func (_c *MockLeagueParticipantRepository_GetParticipantsCountInLeague_Call) RunAndReturn(run func(ctx context.Context, leagueID primitive.ObjectID) int64) *MockLeagueParticipantRepository_GetParticipantsCountInLeague_Call {
	_c.Call.Return(run)
	return _c
}
