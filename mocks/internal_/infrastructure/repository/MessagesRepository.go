// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockMessagesRepository creates a new instance of MockMessagesRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockMessagesRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockMessagesRepository {
	mock := &MockMessagesRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockMessagesRepository is an autogenerated mock type for the MessagesRepository type
type MockMessagesRepository struct {
	mock.Mock
}

type MockMessagesRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockMessagesRepository) EXPECT() *MockMessagesRepository_Expecter {
	return &MockMessagesRepository_Expecter{mock: &_m.Mock}
}

// CreateMessage provides a mock function for the type MockMessagesRepository
func (_mock *MockMessagesRepository) CreateMessage(ctx context.Context, message *models.Message) (primitive.ObjectID, error) {
	ret := _mock.Called(ctx, message)

	if len(ret) == 0 {
		panic("no return value specified for CreateMessage")
	}

	var r0 primitive.ObjectID
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Message) (primitive.ObjectID, error)); ok {
		return returnFunc(ctx, message)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Message) primitive.ObjectID); ok {
		r0 = returnFunc(ctx, message)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(primitive.ObjectID)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.Message) error); ok {
		r1 = returnFunc(ctx, message)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMessagesRepository_CreateMessage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateMessage'
type MockMessagesRepository_CreateMessage_Call struct {
	*mock.Call
}

// CreateMessage is a helper method to define mock.On call
//   - ctx
//   - message
func (_e *MockMessagesRepository_Expecter) CreateMessage(ctx interface{}, message interface{}) *MockMessagesRepository_CreateMessage_Call {
	return &MockMessagesRepository_CreateMessage_Call{Call: _e.mock.On("CreateMessage", ctx, message)}
}

func (_c *MockMessagesRepository_CreateMessage_Call) Run(run func(ctx context.Context, message *models.Message)) *MockMessagesRepository_CreateMessage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.Message))
	})
	return _c
}

func (_c *MockMessagesRepository_CreateMessage_Call) Return(objectID primitive.ObjectID, err error) *MockMessagesRepository_CreateMessage_Call {
	_c.Call.Return(objectID, err)
	return _c
}

func (_c *MockMessagesRepository_CreateMessage_Call) RunAndReturn(run func(ctx context.Context, message *models.Message) (primitive.ObjectID, error)) *MockMessagesRepository_CreateMessage_Call {
	_c.Call.Return(run)
	return _c
}

// GetMessagesByGroupID provides a mock function for the type MockMessagesRepository
func (_mock *MockMessagesRepository) GetMessagesByGroupID(ctx context.Context, groupID primitive.ObjectID, lastMessageId *primitive.ObjectID, pageSize int64, sortValue int) ([]*models.Message, error) {
	ret := _mock.Called(ctx, groupID, lastMessageId, pageSize, sortValue)

	if len(ret) == 0 {
		panic("no return value specified for GetMessagesByGroupID")
	}

	var r0 []*models.Message
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, int64, int) ([]*models.Message, error)); ok {
		return returnFunc(ctx, groupID, lastMessageId, pageSize, sortValue)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, int64, int) []*models.Message); ok {
		r0 = returnFunc(ctx, groupID, lastMessageId, pageSize, sortValue)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Message)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, int64, int) error); ok {
		r1 = returnFunc(ctx, groupID, lastMessageId, pageSize, sortValue)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMessagesRepository_GetMessagesByGroupID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMessagesByGroupID'
type MockMessagesRepository_GetMessagesByGroupID_Call struct {
	*mock.Call
}

// GetMessagesByGroupID is a helper method to define mock.On call
//   - ctx
//   - groupID
//   - lastMessageId
//   - pageSize
//   - sortValue
func (_e *MockMessagesRepository_Expecter) GetMessagesByGroupID(ctx interface{}, groupID interface{}, lastMessageId interface{}, pageSize interface{}, sortValue interface{}) *MockMessagesRepository_GetMessagesByGroupID_Call {
	return &MockMessagesRepository_GetMessagesByGroupID_Call{Call: _e.mock.On("GetMessagesByGroupID", ctx, groupID, lastMessageId, pageSize, sortValue)}
}

func (_c *MockMessagesRepository_GetMessagesByGroupID_Call) Run(run func(ctx context.Context, groupID primitive.ObjectID, lastMessageId *primitive.ObjectID, pageSize int64, sortValue int)) *MockMessagesRepository_GetMessagesByGroupID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(*primitive.ObjectID), args[3].(int64), args[4].(int))
	})
	return _c
}

func (_c *MockMessagesRepository_GetMessagesByGroupID_Call) Return(messages []*models.Message, err error) *MockMessagesRepository_GetMessagesByGroupID_Call {
	_c.Call.Return(messages, err)
	return _c
}

func (_c *MockMessagesRepository_GetMessagesByGroupID_Call) RunAndReturn(run func(ctx context.Context, groupID primitive.ObjectID, lastMessageId *primitive.ObjectID, pageSize int64, sortValue int) ([]*models.Message, error)) *MockMessagesRepository_GetMessagesByGroupID_Call {
	_c.Call.Return(run)
	return _c
}

// getLatestMessage provides a mock function for the type MockMessagesRepository
func (_mock *MockMessagesRepository) getLatestMessage(ctx context.Context, groupID primitive.ObjectID) (*models.Message, error) {
	ret := _mock.Called(ctx, groupID)

	if len(ret) == 0 {
		panic("no return value specified for getLatestMessage")
	}

	var r0 *models.Message
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Message, error)); ok {
		return returnFunc(ctx, groupID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Message); ok {
		r0 = returnFunc(ctx, groupID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Message)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, groupID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMessagesRepository_getLatestMessage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'getLatestMessage'
type MockMessagesRepository_getLatestMessage_Call struct {
	*mock.Call
}

// getLatestMessage is a helper method to define mock.On call
//   - ctx
//   - groupID
func (_e *MockMessagesRepository_Expecter) getLatestMessage(ctx interface{}, groupID interface{}) *MockMessagesRepository_getLatestMessage_Call {
	return &MockMessagesRepository_getLatestMessage_Call{Call: _e.mock.On("getLatestMessage", ctx, groupID)}
}

func (_c *MockMessagesRepository_getLatestMessage_Call) Run(run func(ctx context.Context, groupID primitive.ObjectID)) *MockMessagesRepository_getLatestMessage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockMessagesRepository_getLatestMessage_Call) Return(message *models.Message, err error) *MockMessagesRepository_getLatestMessage_Call {
	_c.Call.Return(message, err)
	return _c
}

func (_c *MockMessagesRepository_getLatestMessage_Call) RunAndReturn(run func(ctx context.Context, groupID primitive.ObjectID) (*models.Message, error)) *MockMessagesRepository_getLatestMessage_Call {
	_c.Call.Return(run)
	return _c
}
