// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"
	"time"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockPresetsRepository creates a new instance of MockPresetsRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPresetsRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPresetsRepository {
	mock := &MockPresetsRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockPresetsRepository is an autogenerated mock type for the PresetsRepository type
type MockPresetsRepository struct {
	mock.Mock
}

type MockPresetsRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPresetsRepository) EXPECT() *MockPresetsRepository_Expecter {
	return &MockPresetsRepository_Expecter{mock: &_m.Mock}
}

// BulkUpdateGlobalPresets provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) BulkUpdateGlobalPresets(context1 context.Context, globalPresets []*models.GlobalPreset) error {
	ret := _mock.Called(context1, globalPresets)

	if len(ret) == 0 {
		panic("no return value specified for BulkUpdateGlobalPresets")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*models.GlobalPreset) error); ok {
		r0 = returnFunc(context1, globalPresets)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsRepository_BulkUpdateGlobalPresets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BulkUpdateGlobalPresets'
type MockPresetsRepository_BulkUpdateGlobalPresets_Call struct {
	*mock.Call
}

// BulkUpdateGlobalPresets is a helper method to define mock.On call
//   - context1
//   - globalPresets
func (_e *MockPresetsRepository_Expecter) BulkUpdateGlobalPresets(context1 interface{}, globalPresets interface{}) *MockPresetsRepository_BulkUpdateGlobalPresets_Call {
	return &MockPresetsRepository_BulkUpdateGlobalPresets_Call{Call: _e.mock.On("BulkUpdateGlobalPresets", context1, globalPresets)}
}

func (_c *MockPresetsRepository_BulkUpdateGlobalPresets_Call) Run(run func(context1 context.Context, globalPresets []*models.GlobalPreset)) *MockPresetsRepository_BulkUpdateGlobalPresets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*models.GlobalPreset))
	})
	return _c
}

func (_c *MockPresetsRepository_BulkUpdateGlobalPresets_Call) Return(err error) *MockPresetsRepository_BulkUpdateGlobalPresets_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsRepository_BulkUpdateGlobalPresets_Call) RunAndReturn(run func(context1 context.Context, globalPresets []*models.GlobalPreset) error) *MockPresetsRepository_BulkUpdateGlobalPresets_Call {
	_c.Call.Return(run)
	return _c
}

// BulkUpdateUserPresetStats provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) BulkUpdateUserPresetStats(context1 context.Context, userPresetStatss []*models.UserPresetStats) error {
	ret := _mock.Called(context1, userPresetStatss)

	if len(ret) == 0 {
		panic("no return value specified for BulkUpdateUserPresetStats")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*models.UserPresetStats) error); ok {
		r0 = returnFunc(context1, userPresetStatss)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsRepository_BulkUpdateUserPresetStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BulkUpdateUserPresetStats'
type MockPresetsRepository_BulkUpdateUserPresetStats_Call struct {
	*mock.Call
}

// BulkUpdateUserPresetStats is a helper method to define mock.On call
//   - context1
//   - userPresetStatss
func (_e *MockPresetsRepository_Expecter) BulkUpdateUserPresetStats(context1 interface{}, userPresetStatss interface{}) *MockPresetsRepository_BulkUpdateUserPresetStats_Call {
	return &MockPresetsRepository_BulkUpdateUserPresetStats_Call{Call: _e.mock.On("BulkUpdateUserPresetStats", context1, userPresetStatss)}
}

func (_c *MockPresetsRepository_BulkUpdateUserPresetStats_Call) Run(run func(context1 context.Context, userPresetStatss []*models.UserPresetStats)) *MockPresetsRepository_BulkUpdateUserPresetStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*models.UserPresetStats))
	})
	return _c
}

func (_c *MockPresetsRepository_BulkUpdateUserPresetStats_Call) Return(err error) *MockPresetsRepository_BulkUpdateUserPresetStats_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsRepository_BulkUpdateUserPresetStats_Call) RunAndReturn(run func(context1 context.Context, userPresetStatss []*models.UserPresetStats) error) *MockPresetsRepository_BulkUpdateUserPresetStats_Call {
	_c.Call.Return(run)
	return _c
}

// BulkUpdateUserPresets provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) BulkUpdateUserPresets(context1 context.Context, userPresets []*models.UserPreset) error {
	ret := _mock.Called(context1, userPresets)

	if len(ret) == 0 {
		panic("no return value specified for BulkUpdateUserPresets")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*models.UserPreset) error); ok {
		r0 = returnFunc(context1, userPresets)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsRepository_BulkUpdateUserPresets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BulkUpdateUserPresets'
type MockPresetsRepository_BulkUpdateUserPresets_Call struct {
	*mock.Call
}

// BulkUpdateUserPresets is a helper method to define mock.On call
//   - context1
//   - userPresets
func (_e *MockPresetsRepository_Expecter) BulkUpdateUserPresets(context1 interface{}, userPresets interface{}) *MockPresetsRepository_BulkUpdateUserPresets_Call {
	return &MockPresetsRepository_BulkUpdateUserPresets_Call{Call: _e.mock.On("BulkUpdateUserPresets", context1, userPresets)}
}

func (_c *MockPresetsRepository_BulkUpdateUserPresets_Call) Run(run func(context1 context.Context, userPresets []*models.UserPreset)) *MockPresetsRepository_BulkUpdateUserPresets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*models.UserPreset))
	})
	return _c
}

func (_c *MockPresetsRepository_BulkUpdateUserPresets_Call) Return(err error) *MockPresetsRepository_BulkUpdateUserPresets_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsRepository_BulkUpdateUserPresets_Call) RunAndReturn(run func(context1 context.Context, userPresets []*models.UserPreset) error) *MockPresetsRepository_BulkUpdateUserPresets_Call {
	_c.Call.Return(run)
	return _c
}

// CreateGlobalPreset provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) CreateGlobalPreset(context1 context.Context, globalPreset *models.GlobalPreset) error {
	ret := _mock.Called(context1, globalPreset)

	if len(ret) == 0 {
		panic("no return value specified for CreateGlobalPreset")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GlobalPreset) error); ok {
		r0 = returnFunc(context1, globalPreset)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsRepository_CreateGlobalPreset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateGlobalPreset'
type MockPresetsRepository_CreateGlobalPreset_Call struct {
	*mock.Call
}

// CreateGlobalPreset is a helper method to define mock.On call
//   - context1
//   - globalPreset
func (_e *MockPresetsRepository_Expecter) CreateGlobalPreset(context1 interface{}, globalPreset interface{}) *MockPresetsRepository_CreateGlobalPreset_Call {
	return &MockPresetsRepository_CreateGlobalPreset_Call{Call: _e.mock.On("CreateGlobalPreset", context1, globalPreset)}
}

func (_c *MockPresetsRepository_CreateGlobalPreset_Call) Run(run func(context1 context.Context, globalPreset *models.GlobalPreset)) *MockPresetsRepository_CreateGlobalPreset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.GlobalPreset))
	})
	return _c
}

func (_c *MockPresetsRepository_CreateGlobalPreset_Call) Return(err error) *MockPresetsRepository_CreateGlobalPreset_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsRepository_CreateGlobalPreset_Call) RunAndReturn(run func(context1 context.Context, globalPreset *models.GlobalPreset) error) *MockPresetsRepository_CreateGlobalPreset_Call {
	_c.Call.Return(run)
	return _c
}

// CreateUserPreset provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) CreateUserPreset(context1 context.Context, userPreset *models.UserPreset) error {
	ret := _mock.Called(context1, userPreset)

	if len(ret) == 0 {
		panic("no return value specified for CreateUserPreset")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserPreset) error); ok {
		r0 = returnFunc(context1, userPreset)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsRepository_CreateUserPreset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUserPreset'
type MockPresetsRepository_CreateUserPreset_Call struct {
	*mock.Call
}

// CreateUserPreset is a helper method to define mock.On call
//   - context1
//   - userPreset
func (_e *MockPresetsRepository_Expecter) CreateUserPreset(context1 interface{}, userPreset interface{}) *MockPresetsRepository_CreateUserPreset_Call {
	return &MockPresetsRepository_CreateUserPreset_Call{Call: _e.mock.On("CreateUserPreset", context1, userPreset)}
}

func (_c *MockPresetsRepository_CreateUserPreset_Call) Run(run func(context1 context.Context, userPreset *models.UserPreset)) *MockPresetsRepository_CreateUserPreset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.UserPreset))
	})
	return _c
}

func (_c *MockPresetsRepository_CreateUserPreset_Call) Return(err error) *MockPresetsRepository_CreateUserPreset_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsRepository_CreateUserPreset_Call) RunAndReturn(run func(context1 context.Context, userPreset *models.UserPreset) error) *MockPresetsRepository_CreateUserPreset_Call {
	_c.Call.Return(run)
	return _c
}

// CreateUserPresetStats provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) CreateUserPresetStats(context1 context.Context, userPresetStats *models.UserPresetStats) error {
	ret := _mock.Called(context1, userPresetStats)

	if len(ret) == 0 {
		panic("no return value specified for CreateUserPresetStats")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserPresetStats) error); ok {
		r0 = returnFunc(context1, userPresetStats)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsRepository_CreateUserPresetStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUserPresetStats'
type MockPresetsRepository_CreateUserPresetStats_Call struct {
	*mock.Call
}

// CreateUserPresetStats is a helper method to define mock.On call
//   - context1
//   - userPresetStats
func (_e *MockPresetsRepository_Expecter) CreateUserPresetStats(context1 interface{}, userPresetStats interface{}) *MockPresetsRepository_CreateUserPresetStats_Call {
	return &MockPresetsRepository_CreateUserPresetStats_Call{Call: _e.mock.On("CreateUserPresetStats", context1, userPresetStats)}
}

func (_c *MockPresetsRepository_CreateUserPresetStats_Call) Run(run func(context1 context.Context, userPresetStats *models.UserPresetStats)) *MockPresetsRepository_CreateUserPresetStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.UserPresetStats))
	})
	return _c
}

func (_c *MockPresetsRepository_CreateUserPresetStats_Call) Return(err error) *MockPresetsRepository_CreateUserPresetStats_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsRepository_CreateUserPresetStats_Call) RunAndReturn(run func(context1 context.Context, userPresetStats *models.UserPresetStats) error) *MockPresetsRepository_CreateUserPresetStats_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteSavedPreset provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) DeleteSavedPreset(ctx context.Context, presetId primitive.ObjectID) error {
	ret := _mock.Called(ctx, presetId)

	if len(ret) == 0 {
		panic("no return value specified for DeleteSavedPreset")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, presetId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsRepository_DeleteSavedPreset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteSavedPreset'
type MockPresetsRepository_DeleteSavedPreset_Call struct {
	*mock.Call
}

// DeleteSavedPreset is a helper method to define mock.On call
//   - ctx
//   - presetId
func (_e *MockPresetsRepository_Expecter) DeleteSavedPreset(ctx interface{}, presetId interface{}) *MockPresetsRepository_DeleteSavedPreset_Call {
	return &MockPresetsRepository_DeleteSavedPreset_Call{Call: _e.mock.On("DeleteSavedPreset", ctx, presetId)}
}

func (_c *MockPresetsRepository_DeleteSavedPreset_Call) Run(run func(ctx context.Context, presetId primitive.ObjectID)) *MockPresetsRepository_DeleteSavedPreset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockPresetsRepository_DeleteSavedPreset_Call) Return(err error) *MockPresetsRepository_DeleteSavedPreset_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsRepository_DeleteSavedPreset_Call) RunAndReturn(run func(ctx context.Context, presetId primitive.ObjectID) error) *MockPresetsRepository_DeleteSavedPreset_Call {
	_c.Call.Return(run)
	return _c
}

// GetBulkGlobalPresets provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) GetBulkGlobalPresets(ctx context.Context, identifiers []string) (map[string]*models.GlobalPreset, error) {
	ret := _mock.Called(ctx, identifiers)

	if len(ret) == 0 {
		panic("no return value specified for GetBulkGlobalPresets")
	}

	var r0 map[string]*models.GlobalPreset
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string) (map[string]*models.GlobalPreset, error)); ok {
		return returnFunc(ctx, identifiers)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string) map[string]*models.GlobalPreset); ok {
		r0 = returnFunc(ctx, identifiers)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]*models.GlobalPreset)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = returnFunc(ctx, identifiers)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsRepository_GetBulkGlobalPresets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBulkGlobalPresets'
type MockPresetsRepository_GetBulkGlobalPresets_Call struct {
	*mock.Call
}

// GetBulkGlobalPresets is a helper method to define mock.On call
//   - ctx
//   - identifiers
func (_e *MockPresetsRepository_Expecter) GetBulkGlobalPresets(ctx interface{}, identifiers interface{}) *MockPresetsRepository_GetBulkGlobalPresets_Call {
	return &MockPresetsRepository_GetBulkGlobalPresets_Call{Call: _e.mock.On("GetBulkGlobalPresets", ctx, identifiers)}
}

func (_c *MockPresetsRepository_GetBulkGlobalPresets_Call) Run(run func(ctx context.Context, identifiers []string)) *MockPresetsRepository_GetBulkGlobalPresets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]string))
	})
	return _c
}

func (_c *MockPresetsRepository_GetBulkGlobalPresets_Call) Return(stringToGlobalPreset map[string]*models.GlobalPreset, err error) *MockPresetsRepository_GetBulkGlobalPresets_Call {
	_c.Call.Return(stringToGlobalPreset, err)
	return _c
}

func (_c *MockPresetsRepository_GetBulkGlobalPresets_Call) RunAndReturn(run func(ctx context.Context, identifiers []string) (map[string]*models.GlobalPreset, error)) *MockPresetsRepository_GetBulkGlobalPresets_Call {
	_c.Call.Return(run)
	return _c
}

// GetBulkUserPresets provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) GetBulkUserPresets(ctx context.Context, identifiers []string, userID primitive.ObjectID) (map[string]*models.UserPreset, error) {
	ret := _mock.Called(ctx, identifiers, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetBulkUserPresets")
	}

	var r0 map[string]*models.UserPreset
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string, primitive.ObjectID) (map[string]*models.UserPreset, error)); ok {
		return returnFunc(ctx, identifiers, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string, primitive.ObjectID) map[string]*models.UserPreset); ok {
		r0 = returnFunc(ctx, identifiers, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]*models.UserPreset)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []string, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, identifiers, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsRepository_GetBulkUserPresets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBulkUserPresets'
type MockPresetsRepository_GetBulkUserPresets_Call struct {
	*mock.Call
}

// GetBulkUserPresets is a helper method to define mock.On call
//   - ctx
//   - identifiers
//   - userID
func (_e *MockPresetsRepository_Expecter) GetBulkUserPresets(ctx interface{}, identifiers interface{}, userID interface{}) *MockPresetsRepository_GetBulkUserPresets_Call {
	return &MockPresetsRepository_GetBulkUserPresets_Call{Call: _e.mock.On("GetBulkUserPresets", ctx, identifiers, userID)}
}

func (_c *MockPresetsRepository_GetBulkUserPresets_Call) Run(run func(ctx context.Context, identifiers []string, userID primitive.ObjectID)) *MockPresetsRepository_GetBulkUserPresets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]string), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockPresetsRepository_GetBulkUserPresets_Call) Return(stringToUserPreset map[string]*models.UserPreset, err error) *MockPresetsRepository_GetBulkUserPresets_Call {
	_c.Call.Return(stringToUserPreset, err)
	return _c
}

func (_c *MockPresetsRepository_GetBulkUserPresets_Call) RunAndReturn(run func(ctx context.Context, identifiers []string, userID primitive.ObjectID) (map[string]*models.UserPreset, error)) *MockPresetsRepository_GetBulkUserPresets_Call {
	_c.Call.Return(run)
	return _c
}

// GetGlobalPresetByID provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) GetGlobalPresetByID(context1 context.Context, objectID primitive.ObjectID) (*models.GlobalPreset, error) {
	ret := _mock.Called(context1, objectID)

	if len(ret) == 0 {
		panic("no return value specified for GetGlobalPresetByID")
	}

	var r0 *models.GlobalPreset
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.GlobalPreset, error)); ok {
		return returnFunc(context1, objectID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.GlobalPreset); ok {
		r0 = returnFunc(context1, objectID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GlobalPreset)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(context1, objectID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsRepository_GetGlobalPresetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGlobalPresetByID'
type MockPresetsRepository_GetGlobalPresetByID_Call struct {
	*mock.Call
}

// GetGlobalPresetByID is a helper method to define mock.On call
//   - context1
//   - objectID
func (_e *MockPresetsRepository_Expecter) GetGlobalPresetByID(context1 interface{}, objectID interface{}) *MockPresetsRepository_GetGlobalPresetByID_Call {
	return &MockPresetsRepository_GetGlobalPresetByID_Call{Call: _e.mock.On("GetGlobalPresetByID", context1, objectID)}
}

func (_c *MockPresetsRepository_GetGlobalPresetByID_Call) Run(run func(context1 context.Context, objectID primitive.ObjectID)) *MockPresetsRepository_GetGlobalPresetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockPresetsRepository_GetGlobalPresetByID_Call) Return(globalPreset *models.GlobalPreset, err error) *MockPresetsRepository_GetGlobalPresetByID_Call {
	_c.Call.Return(globalPreset, err)
	return _c
}

func (_c *MockPresetsRepository_GetGlobalPresetByID_Call) RunAndReturn(run func(context1 context.Context, objectID primitive.ObjectID) (*models.GlobalPreset, error)) *MockPresetsRepository_GetGlobalPresetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetGlobalPresetByIdentifier provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) GetGlobalPresetByIdentifier(context1 context.Context, s string) (*models.GlobalPreset, error) {
	ret := _mock.Called(context1, s)

	if len(ret) == 0 {
		panic("no return value specified for GetGlobalPresetByIdentifier")
	}

	var r0 *models.GlobalPreset
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*models.GlobalPreset, error)); ok {
		return returnFunc(context1, s)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *models.GlobalPreset); ok {
		r0 = returnFunc(context1, s)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GlobalPreset)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(context1, s)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsRepository_GetGlobalPresetByIdentifier_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGlobalPresetByIdentifier'
type MockPresetsRepository_GetGlobalPresetByIdentifier_Call struct {
	*mock.Call
}

// GetGlobalPresetByIdentifier is a helper method to define mock.On call
//   - context1
//   - s
func (_e *MockPresetsRepository_Expecter) GetGlobalPresetByIdentifier(context1 interface{}, s interface{}) *MockPresetsRepository_GetGlobalPresetByIdentifier_Call {
	return &MockPresetsRepository_GetGlobalPresetByIdentifier_Call{Call: _e.mock.On("GetGlobalPresetByIdentifier", context1, s)}
}

func (_c *MockPresetsRepository_GetGlobalPresetByIdentifier_Call) Run(run func(context1 context.Context, s string)) *MockPresetsRepository_GetGlobalPresetByIdentifier_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockPresetsRepository_GetGlobalPresetByIdentifier_Call) Return(globalPreset *models.GlobalPreset, err error) *MockPresetsRepository_GetGlobalPresetByIdentifier_Call {
	_c.Call.Return(globalPreset, err)
	return _c
}

func (_c *MockPresetsRepository_GetGlobalPresetByIdentifier_Call) RunAndReturn(run func(context1 context.Context, s string) (*models.GlobalPreset, error)) *MockPresetsRepository_GetGlobalPresetByIdentifier_Call {
	_c.Call.Return(run)
	return _c
}

// GetGlobalPresets provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) GetGlobalPresets(context1 context.Context, n int, n1 int) (*models.GlobalPresets, error) {
	ret := _mock.Called(context1, n, n1)

	if len(ret) == 0 {
		panic("no return value specified for GetGlobalPresets")
	}

	var r0 *models.GlobalPresets
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, int) (*models.GlobalPresets, error)); ok {
		return returnFunc(context1, n, n1)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, int) *models.GlobalPresets); ok {
		r0 = returnFunc(context1, n, n1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GlobalPresets)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int, int) error); ok {
		r1 = returnFunc(context1, n, n1)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsRepository_GetGlobalPresets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGlobalPresets'
type MockPresetsRepository_GetGlobalPresets_Call struct {
	*mock.Call
}

// GetGlobalPresets is a helper method to define mock.On call
//   - context1
//   - n
//   - n1
func (_e *MockPresetsRepository_Expecter) GetGlobalPresets(context1 interface{}, n interface{}, n1 interface{}) *MockPresetsRepository_GetGlobalPresets_Call {
	return &MockPresetsRepository_GetGlobalPresets_Call{Call: _e.mock.On("GetGlobalPresets", context1, n, n1)}
}

func (_c *MockPresetsRepository_GetGlobalPresets_Call) Run(run func(context1 context.Context, n int, n1 int)) *MockPresetsRepository_GetGlobalPresets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(int))
	})
	return _c
}

func (_c *MockPresetsRepository_GetGlobalPresets_Call) Return(globalPresets *models.GlobalPresets, err error) *MockPresetsRepository_GetGlobalPresets_Call {
	_c.Call.Return(globalPresets, err)
	return _c
}

func (_c *MockPresetsRepository_GetGlobalPresets_Call) RunAndReturn(run func(context1 context.Context, n int, n1 int) (*models.GlobalPresets, error)) *MockPresetsRepository_GetGlobalPresets_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserPresetByID provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) GetUserPresetByID(context1 context.Context, objectID primitive.ObjectID) (*models.UserPreset, error) {
	ret := _mock.Called(context1, objectID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPresetByID")
	}

	var r0 *models.UserPreset
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.UserPreset, error)); ok {
		return returnFunc(context1, objectID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.UserPreset); ok {
		r0 = returnFunc(context1, objectID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserPreset)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(context1, objectID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsRepository_GetUserPresetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserPresetByID'
type MockPresetsRepository_GetUserPresetByID_Call struct {
	*mock.Call
}

// GetUserPresetByID is a helper method to define mock.On call
//   - context1
//   - objectID
func (_e *MockPresetsRepository_Expecter) GetUserPresetByID(context1 interface{}, objectID interface{}) *MockPresetsRepository_GetUserPresetByID_Call {
	return &MockPresetsRepository_GetUserPresetByID_Call{Call: _e.mock.On("GetUserPresetByID", context1, objectID)}
}

func (_c *MockPresetsRepository_GetUserPresetByID_Call) Run(run func(context1 context.Context, objectID primitive.ObjectID)) *MockPresetsRepository_GetUserPresetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockPresetsRepository_GetUserPresetByID_Call) Return(userPreset *models.UserPreset, err error) *MockPresetsRepository_GetUserPresetByID_Call {
	_c.Call.Return(userPreset, err)
	return _c
}

func (_c *MockPresetsRepository_GetUserPresetByID_Call) RunAndReturn(run func(context1 context.Context, objectID primitive.ObjectID) (*models.UserPreset, error)) *MockPresetsRepository_GetUserPresetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserPresetByIdentifier provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) GetUserPresetByIdentifier(context1 context.Context, s string, objectID primitive.ObjectID) (*models.UserPreset, error) {
	ret := _mock.Called(context1, s, objectID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPresetByIdentifier")
	}

	var r0 *models.UserPreset
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, primitive.ObjectID) (*models.UserPreset, error)); ok {
		return returnFunc(context1, s, objectID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, primitive.ObjectID) *models.UserPreset); ok {
		r0 = returnFunc(context1, s, objectID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserPreset)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, primitive.ObjectID) error); ok {
		r1 = returnFunc(context1, s, objectID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsRepository_GetUserPresetByIdentifier_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserPresetByIdentifier'
type MockPresetsRepository_GetUserPresetByIdentifier_Call struct {
	*mock.Call
}

// GetUserPresetByIdentifier is a helper method to define mock.On call
//   - context1
//   - s
//   - objectID
func (_e *MockPresetsRepository_Expecter) GetUserPresetByIdentifier(context1 interface{}, s interface{}, objectID interface{}) *MockPresetsRepository_GetUserPresetByIdentifier_Call {
	return &MockPresetsRepository_GetUserPresetByIdentifier_Call{Call: _e.mock.On("GetUserPresetByIdentifier", context1, s, objectID)}
}

func (_c *MockPresetsRepository_GetUserPresetByIdentifier_Call) Run(run func(context1 context.Context, s string, objectID primitive.ObjectID)) *MockPresetsRepository_GetUserPresetByIdentifier_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockPresetsRepository_GetUserPresetByIdentifier_Call) Return(userPreset *models.UserPreset, err error) *MockPresetsRepository_GetUserPresetByIdentifier_Call {
	_c.Call.Return(userPreset, err)
	return _c
}

func (_c *MockPresetsRepository_GetUserPresetByIdentifier_Call) RunAndReturn(run func(context1 context.Context, s string, objectID primitive.ObjectID) (*models.UserPreset, error)) *MockPresetsRepository_GetUserPresetByIdentifier_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserPresetCategories provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) GetUserPresetCategories(context1 context.Context, objectID primitive.ObjectID) (*models.AllPlayedPresetsOutput, error) {
	ret := _mock.Called(context1, objectID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPresetCategories")
	}

	var r0 *models.AllPlayedPresetsOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.AllPlayedPresetsOutput, error)); ok {
		return returnFunc(context1, objectID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.AllPlayedPresetsOutput); ok {
		r0 = returnFunc(context1, objectID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.AllPlayedPresetsOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(context1, objectID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsRepository_GetUserPresetCategories_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserPresetCategories'
type MockPresetsRepository_GetUserPresetCategories_Call struct {
	*mock.Call
}

// GetUserPresetCategories is a helper method to define mock.On call
//   - context1
//   - objectID
func (_e *MockPresetsRepository_Expecter) GetUserPresetCategories(context1 interface{}, objectID interface{}) *MockPresetsRepository_GetUserPresetCategories_Call {
	return &MockPresetsRepository_GetUserPresetCategories_Call{Call: _e.mock.On("GetUserPresetCategories", context1, objectID)}
}

func (_c *MockPresetsRepository_GetUserPresetCategories_Call) Run(run func(context1 context.Context, objectID primitive.ObjectID)) *MockPresetsRepository_GetUserPresetCategories_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockPresetsRepository_GetUserPresetCategories_Call) Return(allPlayedPresetsOutput *models.AllPlayedPresetsOutput, err error) *MockPresetsRepository_GetUserPresetCategories_Call {
	_c.Call.Return(allPlayedPresetsOutput, err)
	return _c
}

func (_c *MockPresetsRepository_GetUserPresetCategories_Call) RunAndReturn(run func(context1 context.Context, objectID primitive.ObjectID) (*models.AllPlayedPresetsOutput, error)) *MockPresetsRepository_GetUserPresetCategories_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserPresetStats provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) GetUserPresetStats(context1 context.Context, s string, time1 time.Time, objectID primitive.ObjectID) (*models.UserPresetStats, error) {
	ret := _mock.Called(context1, s, time1, objectID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPresetStats")
	}

	var r0 *models.UserPresetStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, time.Time, primitive.ObjectID) (*models.UserPresetStats, error)); ok {
		return returnFunc(context1, s, time1, objectID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, time.Time, primitive.ObjectID) *models.UserPresetStats); ok {
		r0 = returnFunc(context1, s, time1, objectID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserPresetStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, time.Time, primitive.ObjectID) error); ok {
		r1 = returnFunc(context1, s, time1, objectID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsRepository_GetUserPresetStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserPresetStats'
type MockPresetsRepository_GetUserPresetStats_Call struct {
	*mock.Call
}

// GetUserPresetStats is a helper method to define mock.On call
//   - context1
//   - s
//   - time1
//   - objectID
func (_e *MockPresetsRepository_Expecter) GetUserPresetStats(context1 interface{}, s interface{}, time1 interface{}, objectID interface{}) *MockPresetsRepository_GetUserPresetStats_Call {
	return &MockPresetsRepository_GetUserPresetStats_Call{Call: _e.mock.On("GetUserPresetStats", context1, s, time1, objectID)}
}

func (_c *MockPresetsRepository_GetUserPresetStats_Call) Run(run func(context1 context.Context, s string, time1 time.Time, objectID primitive.ObjectID)) *MockPresetsRepository_GetUserPresetStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(time.Time), args[3].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockPresetsRepository_GetUserPresetStats_Call) Return(userPresetStats *models.UserPresetStats, err error) *MockPresetsRepository_GetUserPresetStats_Call {
	_c.Call.Return(userPresetStats, err)
	return _c
}

func (_c *MockPresetsRepository_GetUserPresetStats_Call) RunAndReturn(run func(context1 context.Context, s string, time1 time.Time, objectID primitive.ObjectID) (*models.UserPresetStats, error)) *MockPresetsRepository_GetUserPresetStats_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserPresetStatsByDates provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) GetUserPresetStatsByDates(context1 context.Context, time1 time.Time, time11 time.Time, n int, objectID primitive.ObjectID, s string) ([]*models.UserPresetDayStats, error) {
	ret := _mock.Called(context1, time1, time11, n, objectID, s)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPresetStatsByDates")
	}

	var r0 []*models.UserPresetDayStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, time.Time, time.Time, int, primitive.ObjectID, string) ([]*models.UserPresetDayStats, error)); ok {
		return returnFunc(context1, time1, time11, n, objectID, s)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, time.Time, time.Time, int, primitive.ObjectID, string) []*models.UserPresetDayStats); ok {
		r0 = returnFunc(context1, time1, time11, n, objectID, s)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserPresetDayStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, time.Time, time.Time, int, primitive.ObjectID, string) error); ok {
		r1 = returnFunc(context1, time1, time11, n, objectID, s)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsRepository_GetUserPresetStatsByDates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserPresetStatsByDates'
type MockPresetsRepository_GetUserPresetStatsByDates_Call struct {
	*mock.Call
}

// GetUserPresetStatsByDates is a helper method to define mock.On call
//   - context1
//   - time1
//   - time11
//   - n
//   - objectID
//   - s
func (_e *MockPresetsRepository_Expecter) GetUserPresetStatsByDates(context1 interface{}, time1 interface{}, time11 interface{}, n interface{}, objectID interface{}, s interface{}) *MockPresetsRepository_GetUserPresetStatsByDates_Call {
	return &MockPresetsRepository_GetUserPresetStatsByDates_Call{Call: _e.mock.On("GetUserPresetStatsByDates", context1, time1, time11, n, objectID, s)}
}

func (_c *MockPresetsRepository_GetUserPresetStatsByDates_Call) Run(run func(context1 context.Context, time1 time.Time, time11 time.Time, n int, objectID primitive.ObjectID, s string)) *MockPresetsRepository_GetUserPresetStatsByDates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(time.Time), args[2].(time.Time), args[3].(int), args[4].(primitive.ObjectID), args[5].(string))
	})
	return _c
}

func (_c *MockPresetsRepository_GetUserPresetStatsByDates_Call) Return(userPresetDayStatss []*models.UserPresetDayStats, err error) *MockPresetsRepository_GetUserPresetStatsByDates_Call {
	_c.Call.Return(userPresetDayStatss, err)
	return _c
}

func (_c *MockPresetsRepository_GetUserPresetStatsByDates_Call) RunAndReturn(run func(context1 context.Context, time1 time.Time, time11 time.Time, n int, objectID primitive.ObjectID, s string) ([]*models.UserPresetDayStats, error)) *MockPresetsRepository_GetUserPresetStatsByDates_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserPresets provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) GetUserPresets(context1 context.Context, objectID primitive.ObjectID, b bool, n int, n1 int) (*models.UserPresets, error) {
	ret := _mock.Called(context1, objectID, b, n, n1)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPresets")
	}

	var r0 *models.UserPresets
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, bool, int, int) (*models.UserPresets, error)); ok {
		return returnFunc(context1, objectID, b, n, n1)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, bool, int, int) *models.UserPresets); ok {
		r0 = returnFunc(context1, objectID, b, n, n1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserPresets)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, bool, int, int) error); ok {
		r1 = returnFunc(context1, objectID, b, n, n1)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsRepository_GetUserPresets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserPresets'
type MockPresetsRepository_GetUserPresets_Call struct {
	*mock.Call
}

// GetUserPresets is a helper method to define mock.On call
//   - context1
//   - objectID
//   - b
//   - n
//   - n1
func (_e *MockPresetsRepository_Expecter) GetUserPresets(context1 interface{}, objectID interface{}, b interface{}, n interface{}, n1 interface{}) *MockPresetsRepository_GetUserPresets_Call {
	return &MockPresetsRepository_GetUserPresets_Call{Call: _e.mock.On("GetUserPresets", context1, objectID, b, n, n1)}
}

func (_c *MockPresetsRepository_GetUserPresets_Call) Run(run func(context1 context.Context, objectID primitive.ObjectID, b bool, n int, n1 int)) *MockPresetsRepository_GetUserPresets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(bool), args[3].(int), args[4].(int))
	})
	return _c
}

func (_c *MockPresetsRepository_GetUserPresets_Call) Return(userPresets *models.UserPresets, err error) *MockPresetsRepository_GetUserPresets_Call {
	_c.Call.Return(userPresets, err)
	return _c
}

func (_c *MockPresetsRepository_GetUserPresets_Call) RunAndReturn(run func(context1 context.Context, objectID primitive.ObjectID, b bool, n int, n1 int) (*models.UserPresets, error)) *MockPresetsRepository_GetUserPresets_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateGlobalPreset provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) UpdateGlobalPreset(context1 context.Context, v bson.M, v1 bson.M) error {
	ret := _mock.Called(context1, v, v1)

	if len(ret) == 0 {
		panic("no return value specified for UpdateGlobalPreset")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, bson.M) error); ok {
		r0 = returnFunc(context1, v, v1)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsRepository_UpdateGlobalPreset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateGlobalPreset'
type MockPresetsRepository_UpdateGlobalPreset_Call struct {
	*mock.Call
}

// UpdateGlobalPreset is a helper method to define mock.On call
//   - context1
//   - v
//   - v1
func (_e *MockPresetsRepository_Expecter) UpdateGlobalPreset(context1 interface{}, v interface{}, v1 interface{}) *MockPresetsRepository_UpdateGlobalPreset_Call {
	return &MockPresetsRepository_UpdateGlobalPreset_Call{Call: _e.mock.On("UpdateGlobalPreset", context1, v, v1)}
}

func (_c *MockPresetsRepository_UpdateGlobalPreset_Call) Run(run func(context1 context.Context, v bson.M, v1 bson.M)) *MockPresetsRepository_UpdateGlobalPreset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M), args[2].(bson.M))
	})
	return _c
}

func (_c *MockPresetsRepository_UpdateGlobalPreset_Call) Return(err error) *MockPresetsRepository_UpdateGlobalPreset_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsRepository_UpdateGlobalPreset_Call) RunAndReturn(run func(context1 context.Context, v bson.M, v1 bson.M) error) *MockPresetsRepository_UpdateGlobalPreset_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserPreset provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) UpdateUserPreset(context1 context.Context, v bson.M, v1 bson.M) error {
	ret := _mock.Called(context1, v, v1)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserPreset")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, bson.M) error); ok {
		r0 = returnFunc(context1, v, v1)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsRepository_UpdateUserPreset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserPreset'
type MockPresetsRepository_UpdateUserPreset_Call struct {
	*mock.Call
}

// UpdateUserPreset is a helper method to define mock.On call
//   - context1
//   - v
//   - v1
func (_e *MockPresetsRepository_Expecter) UpdateUserPreset(context1 interface{}, v interface{}, v1 interface{}) *MockPresetsRepository_UpdateUserPreset_Call {
	return &MockPresetsRepository_UpdateUserPreset_Call{Call: _e.mock.On("UpdateUserPreset", context1, v, v1)}
}

func (_c *MockPresetsRepository_UpdateUserPreset_Call) Run(run func(context1 context.Context, v bson.M, v1 bson.M)) *MockPresetsRepository_UpdateUserPreset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M), args[2].(bson.M))
	})
	return _c
}

func (_c *MockPresetsRepository_UpdateUserPreset_Call) Return(err error) *MockPresetsRepository_UpdateUserPreset_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsRepository_UpdateUserPreset_Call) RunAndReturn(run func(context1 context.Context, v bson.M, v1 bson.M) error) *MockPresetsRepository_UpdateUserPreset_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserPresetStats provides a mock function for the type MockPresetsRepository
func (_mock *MockPresetsRepository) UpdateUserPresetStats(context1 context.Context, v bson.M, v1 bson.M) error {
	ret := _mock.Called(context1, v, v1)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserPresetStats")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, bson.M) error); ok {
		r0 = returnFunc(context1, v, v1)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsRepository_UpdateUserPresetStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserPresetStats'
type MockPresetsRepository_UpdateUserPresetStats_Call struct {
	*mock.Call
}

// UpdateUserPresetStats is a helper method to define mock.On call
//   - context1
//   - v
//   - v1
func (_e *MockPresetsRepository_Expecter) UpdateUserPresetStats(context1 interface{}, v interface{}, v1 interface{}) *MockPresetsRepository_UpdateUserPresetStats_Call {
	return &MockPresetsRepository_UpdateUserPresetStats_Call{Call: _e.mock.On("UpdateUserPresetStats", context1, v, v1)}
}

func (_c *MockPresetsRepository_UpdateUserPresetStats_Call) Run(run func(context1 context.Context, v bson.M, v1 bson.M)) *MockPresetsRepository_UpdateUserPresetStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M), args[2].(bson.M))
	})
	return _c
}

func (_c *MockPresetsRepository_UpdateUserPresetStats_Call) Return(err error) *MockPresetsRepository_UpdateUserPresetStats_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsRepository_UpdateUserPresetStats_Call) RunAndReturn(run func(context1 context.Context, v bson.M, v1 bson.M) error) *MockPresetsRepository_UpdateUserPresetStats_Call {
	_c.Call.Return(run)
	return _c
}
