// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockPuzzleGameSeriesRepository creates a new instance of MockPuzzleGameSeriesRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPuzzleGameSeriesRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPuzzleGameSeriesRepository {
	mock := &MockPuzzleGameSeriesRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockPuzzleGameSeriesRepository is an autogenerated mock type for the PuzzleGameSeriesRepository type
type MockPuzzleGameSeriesRepository struct {
	mock.Mock
}

type MockPuzzleGameSeriesRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPuzzleGameSeriesRepository) EXPECT() *MockPuzzleGameSeriesRepository_Expecter {
	return &MockPuzzleGameSeriesRepository_Expecter{mock: &_m.Mock}
}

// CreatePuzzleGameSeries provides a mock function for the type MockPuzzleGameSeriesRepository
func (_mock *MockPuzzleGameSeriesRepository) CreatePuzzleGameSeries(ctx context.Context, game *models.GameSeries) (*models.GameSeries, error) {
	ret := _mock.Called(ctx, game)

	if len(ret) == 0 {
		panic("no return value specified for CreatePuzzleGameSeries")
	}

	var r0 *models.GameSeries
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GameSeries) (*models.GameSeries, error)); ok {
		return returnFunc(ctx, game)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GameSeries) *models.GameSeries); ok {
		r0 = returnFunc(ctx, game)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GameSeries)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.GameSeries) error); ok {
		r1 = returnFunc(ctx, game)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameSeriesRepository_CreatePuzzleGameSeries_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreatePuzzleGameSeries'
type MockPuzzleGameSeriesRepository_CreatePuzzleGameSeries_Call struct {
	*mock.Call
}

// CreatePuzzleGameSeries is a helper method to define mock.On call
//   - ctx
//   - game
func (_e *MockPuzzleGameSeriesRepository_Expecter) CreatePuzzleGameSeries(ctx interface{}, game interface{}) *MockPuzzleGameSeriesRepository_CreatePuzzleGameSeries_Call {
	return &MockPuzzleGameSeriesRepository_CreatePuzzleGameSeries_Call{Call: _e.mock.On("CreatePuzzleGameSeries", ctx, game)}
}

func (_c *MockPuzzleGameSeriesRepository_CreatePuzzleGameSeries_Call) Run(run func(ctx context.Context, game *models.GameSeries)) *MockPuzzleGameSeriesRepository_CreatePuzzleGameSeries_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.GameSeries))
	})
	return _c
}

func (_c *MockPuzzleGameSeriesRepository_CreatePuzzleGameSeries_Call) Return(gameSeries *models.GameSeries, err error) *MockPuzzleGameSeriesRepository_CreatePuzzleGameSeries_Call {
	_c.Call.Return(gameSeries, err)
	return _c
}

func (_c *MockPuzzleGameSeriesRepository_CreatePuzzleGameSeries_Call) RunAndReturn(run func(ctx context.Context, game *models.GameSeries) (*models.GameSeries, error)) *MockPuzzleGameSeriesRepository_CreatePuzzleGameSeries_Call {
	_c.Call.Return(run)
	return _c
}

// GetPuzzleGameSeriesByID provides a mock function for the type MockPuzzleGameSeriesRepository
func (_mock *MockPuzzleGameSeriesRepository) GetPuzzleGameSeriesByID(ctx context.Context, gameSeriesID primitive.ObjectID) (*models.GameSeries, error) {
	ret := _mock.Called(ctx, gameSeriesID)

	if len(ret) == 0 {
		panic("no return value specified for GetPuzzleGameSeriesByID")
	}

	var r0 *models.GameSeries
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.GameSeries, error)); ok {
		return returnFunc(ctx, gameSeriesID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.GameSeries); ok {
		r0 = returnFunc(ctx, gameSeriesID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GameSeries)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameSeriesID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameSeriesRepository_GetPuzzleGameSeriesByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPuzzleGameSeriesByID'
type MockPuzzleGameSeriesRepository_GetPuzzleGameSeriesByID_Call struct {
	*mock.Call
}

// GetPuzzleGameSeriesByID is a helper method to define mock.On call
//   - ctx
//   - gameSeriesID
func (_e *MockPuzzleGameSeriesRepository_Expecter) GetPuzzleGameSeriesByID(ctx interface{}, gameSeriesID interface{}) *MockPuzzleGameSeriesRepository_GetPuzzleGameSeriesByID_Call {
	return &MockPuzzleGameSeriesRepository_GetPuzzleGameSeriesByID_Call{Call: _e.mock.On("GetPuzzleGameSeriesByID", ctx, gameSeriesID)}
}

func (_c *MockPuzzleGameSeriesRepository_GetPuzzleGameSeriesByID_Call) Run(run func(ctx context.Context, gameSeriesID primitive.ObjectID)) *MockPuzzleGameSeriesRepository_GetPuzzleGameSeriesByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockPuzzleGameSeriesRepository_GetPuzzleGameSeriesByID_Call) Return(gameSeries *models.GameSeries, err error) *MockPuzzleGameSeriesRepository_GetPuzzleGameSeriesByID_Call {
	_c.Call.Return(gameSeries, err)
	return _c
}

func (_c *MockPuzzleGameSeriesRepository_GetPuzzleGameSeriesByID_Call) RunAndReturn(run func(ctx context.Context, gameSeriesID primitive.ObjectID) (*models.GameSeries, error)) *MockPuzzleGameSeriesRepository_GetPuzzleGameSeriesByID_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function for the type MockPuzzleGameSeriesRepository
func (_mock *MockPuzzleGameSeriesRepository) Update(ctx context.Context, gameSeries *models.GameSeries) error {
	ret := _mock.Called(ctx, gameSeries)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GameSeries) error); ok {
		r0 = returnFunc(ctx, gameSeries)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPuzzleGameSeriesRepository_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockPuzzleGameSeriesRepository_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx
//   - gameSeries
func (_e *MockPuzzleGameSeriesRepository_Expecter) Update(ctx interface{}, gameSeries interface{}) *MockPuzzleGameSeriesRepository_Update_Call {
	return &MockPuzzleGameSeriesRepository_Update_Call{Call: _e.mock.On("Update", ctx, gameSeries)}
}

func (_c *MockPuzzleGameSeriesRepository_Update_Call) Run(run func(ctx context.Context, gameSeries *models.GameSeries)) *MockPuzzleGameSeriesRepository_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.GameSeries))
	})
	return _c
}

func (_c *MockPuzzleGameSeriesRepository_Update_Call) Return(err error) *MockPuzzleGameSeriesRepository_Update_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPuzzleGameSeriesRepository_Update_Call) RunAndReturn(run func(ctx context.Context, gameSeries *models.GameSeries) error) *MockPuzzleGameSeriesRepository_Update_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function for the type MockPuzzleGameSeriesRepository
func (_mock *MockPuzzleGameSeriesRepository) UpdateOne(ctx context.Context, filter bson.M, update bson.M) error {
	ret := _mock.Called(ctx, filter, update)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, bson.M) error); ok {
		r0 = returnFunc(ctx, filter, update)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPuzzleGameSeriesRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockPuzzleGameSeriesRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx
//   - filter
//   - update
func (_e *MockPuzzleGameSeriesRepository_Expecter) UpdateOne(ctx interface{}, filter interface{}, update interface{}) *MockPuzzleGameSeriesRepository_UpdateOne_Call {
	return &MockPuzzleGameSeriesRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne", ctx, filter, update)}
}

func (_c *MockPuzzleGameSeriesRepository_UpdateOne_Call) Run(run func(ctx context.Context, filter bson.M, update bson.M)) *MockPuzzleGameSeriesRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M), args[2].(bson.M))
	})
	return _c
}

func (_c *MockPuzzleGameSeriesRepository_UpdateOne_Call) Return(err error) *MockPuzzleGameSeriesRepository_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPuzzleGameSeriesRepository_UpdateOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, update bson.M) error) *MockPuzzleGameSeriesRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}
