// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockPuzzleResultRepository creates a new instance of MockPuzzleResultRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPuzzleResultRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPuzzleResultRepository {
	mock := &MockPuzzleResultRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockPuzzleResultRepository is an autogenerated mock type for the PuzzleResultRepository type
type MockPuzzleResultRepository struct {
	mock.Mock
}

type MockPuzzleResultRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPuzzleResultRepository) EXPECT() *MockPuzzleResultRepository_Expecter {
	return &MockPuzzleResultRepository_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type MockPuzzleResultRepository
func (_mock *MockPuzzleResultRepository) Create(ctx context.Context, puzzleResult *models.PuzzleResult) error {
	ret := _mock.Called(ctx, puzzleResult)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PuzzleResult) error); ok {
		r0 = returnFunc(ctx, puzzleResult)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPuzzleResultRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockPuzzleResultRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - puzzleResult
func (_e *MockPuzzleResultRepository_Expecter) Create(ctx interface{}, puzzleResult interface{}) *MockPuzzleResultRepository_Create_Call {
	return &MockPuzzleResultRepository_Create_Call{Call: _e.mock.On("Create", ctx, puzzleResult)}
}

func (_c *MockPuzzleResultRepository_Create_Call) Run(run func(ctx context.Context, puzzleResult *models.PuzzleResult)) *MockPuzzleResultRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.PuzzleResult))
	})
	return _c
}

func (_c *MockPuzzleResultRepository_Create_Call) Return(err error) *MockPuzzleResultRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPuzzleResultRepository_Create_Call) RunAndReturn(run func(ctx context.Context, puzzleResult *models.PuzzleResult) error) *MockPuzzleResultRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function for the type MockPuzzleResultRepository
func (_mock *MockPuzzleResultRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPuzzleResultRepository_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockPuzzleResultRepository_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockPuzzleResultRepository_Expecter) Delete(ctx interface{}, id interface{}) *MockPuzzleResultRepository_Delete_Call {
	return &MockPuzzleResultRepository_Delete_Call{Call: _e.mock.On("Delete", ctx, id)}
}

func (_c *MockPuzzleResultRepository_Delete_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockPuzzleResultRepository_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockPuzzleResultRepository_Delete_Call) Return(err error) *MockPuzzleResultRepository_Delete_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPuzzleResultRepository_Delete_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) error) *MockPuzzleResultRepository_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// FindByID provides a mock function for the type MockPuzzleResultRepository
func (_mock *MockPuzzleResultRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.PuzzleResult, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindByID")
	}

	var r0 *models.PuzzleResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.PuzzleResult, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.PuzzleResult); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleResultRepository_FindByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByID'
type MockPuzzleResultRepository_FindByID_Call struct {
	*mock.Call
}

// FindByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockPuzzleResultRepository_Expecter) FindByID(ctx interface{}, id interface{}) *MockPuzzleResultRepository_FindByID_Call {
	return &MockPuzzleResultRepository_FindByID_Call{Call: _e.mock.On("FindByID", ctx, id)}
}

func (_c *MockPuzzleResultRepository_FindByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockPuzzleResultRepository_FindByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockPuzzleResultRepository_FindByID_Call) Return(puzzleResult *models.PuzzleResult, err error) *MockPuzzleResultRepository_FindByID_Call {
	_c.Call.Return(puzzleResult, err)
	return _c
}

func (_c *MockPuzzleResultRepository_FindByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.PuzzleResult, error)) *MockPuzzleResultRepository_FindByID_Call {
	_c.Call.Return(run)
	return _c
}

// FindOne provides a mock function for the type MockPuzzleResultRepository
func (_mock *MockPuzzleResultRepository) FindOne(ctx context.Context, filter bson.M) (*models.PuzzleResult, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for FindOne")
	}

	var r0 *models.PuzzleResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) (*models.PuzzleResult, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) *models.PuzzleResult); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M) error); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleResultRepository_FindOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOne'
type MockPuzzleResultRepository_FindOne_Call struct {
	*mock.Call
}

// FindOne is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *MockPuzzleResultRepository_Expecter) FindOne(ctx interface{}, filter interface{}) *MockPuzzleResultRepository_FindOne_Call {
	return &MockPuzzleResultRepository_FindOne_Call{Call: _e.mock.On("FindOne", ctx, filter)}
}

func (_c *MockPuzzleResultRepository_FindOne_Call) Run(run func(ctx context.Context, filter bson.M)) *MockPuzzleResultRepository_FindOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M))
	})
	return _c
}

func (_c *MockPuzzleResultRepository_FindOne_Call) Return(puzzleResult *models.PuzzleResult, err error) *MockPuzzleResultRepository_FindOne_Call {
	_c.Call.Return(puzzleResult, err)
	return _c
}

func (_c *MockPuzzleResultRepository_FindOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M) (*models.PuzzleResult, error)) *MockPuzzleResultRepository_FindOne_Call {
	_c.Call.Return(run)
	return _c
}

// GetSubmissionsBetweenDates provides a mock function for the type MockPuzzleResultRepository
func (_mock *MockPuzzleResultRepository) GetSubmissionsBetweenDates(ctx context.Context, startDate string, endDate string) ([]*models.PuzzleResult, error) {
	ret := _mock.Called(ctx, startDate, endDate)

	if len(ret) == 0 {
		panic("no return value specified for GetSubmissionsBetweenDates")
	}

	var r0 []*models.PuzzleResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) ([]*models.PuzzleResult, error)); ok {
		return returnFunc(ctx, startDate, endDate)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) []*models.PuzzleResult); ok {
		r0 = returnFunc(ctx, startDate, endDate)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.PuzzleResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = returnFunc(ctx, startDate, endDate)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleResultRepository_GetSubmissionsBetweenDates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSubmissionsBetweenDates'
type MockPuzzleResultRepository_GetSubmissionsBetweenDates_Call struct {
	*mock.Call
}

// GetSubmissionsBetweenDates is a helper method to define mock.On call
//   - ctx
//   - startDate
//   - endDate
func (_e *MockPuzzleResultRepository_Expecter) GetSubmissionsBetweenDates(ctx interface{}, startDate interface{}, endDate interface{}) *MockPuzzleResultRepository_GetSubmissionsBetweenDates_Call {
	return &MockPuzzleResultRepository_GetSubmissionsBetweenDates_Call{Call: _e.mock.On("GetSubmissionsBetweenDates", ctx, startDate, endDate)}
}

func (_c *MockPuzzleResultRepository_GetSubmissionsBetweenDates_Call) Run(run func(ctx context.Context, startDate string, endDate string)) *MockPuzzleResultRepository_GetSubmissionsBetweenDates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *MockPuzzleResultRepository_GetSubmissionsBetweenDates_Call) Return(puzzleResults []*models.PuzzleResult, err error) *MockPuzzleResultRepository_GetSubmissionsBetweenDates_Call {
	_c.Call.Return(puzzleResults, err)
	return _c
}

func (_c *MockPuzzleResultRepository_GetSubmissionsBetweenDates_Call) RunAndReturn(run func(ctx context.Context, startDate string, endDate string) ([]*models.PuzzleResult, error)) *MockPuzzleResultRepository_GetSubmissionsBetweenDates_Call {
	_c.Call.Return(run)
	return _c
}

// GetSubmissionsBetweenDatesByType provides a mock function for the type MockPuzzleResultRepository
func (_mock *MockPuzzleResultRepository) GetSubmissionsBetweenDatesByType(ctx context.Context, startDate string, endDate string, puzzleType models.PuzzleType) ([]*models.PuzzleResult, error) {
	ret := _mock.Called(ctx, startDate, endDate, puzzleType)

	if len(ret) == 0 {
		panic("no return value specified for GetSubmissionsBetweenDatesByType")
	}

	var r0 []*models.PuzzleResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, models.PuzzleType) ([]*models.PuzzleResult, error)); ok {
		return returnFunc(ctx, startDate, endDate, puzzleType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, models.PuzzleType) []*models.PuzzleResult); ok {
		r0 = returnFunc(ctx, startDate, endDate, puzzleType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.PuzzleResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string, models.PuzzleType) error); ok {
		r1 = returnFunc(ctx, startDate, endDate, puzzleType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleResultRepository_GetSubmissionsBetweenDatesByType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSubmissionsBetweenDatesByType'
type MockPuzzleResultRepository_GetSubmissionsBetweenDatesByType_Call struct {
	*mock.Call
}

// GetSubmissionsBetweenDatesByType is a helper method to define mock.On call
//   - ctx
//   - startDate
//   - endDate
//   - puzzleType
func (_e *MockPuzzleResultRepository_Expecter) GetSubmissionsBetweenDatesByType(ctx interface{}, startDate interface{}, endDate interface{}, puzzleType interface{}) *MockPuzzleResultRepository_GetSubmissionsBetweenDatesByType_Call {
	return &MockPuzzleResultRepository_GetSubmissionsBetweenDatesByType_Call{Call: _e.mock.On("GetSubmissionsBetweenDatesByType", ctx, startDate, endDate, puzzleType)}
}

func (_c *MockPuzzleResultRepository_GetSubmissionsBetweenDatesByType_Call) Run(run func(ctx context.Context, startDate string, endDate string, puzzleType models.PuzzleType)) *MockPuzzleResultRepository_GetSubmissionsBetweenDatesByType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(models.PuzzleType))
	})
	return _c
}

func (_c *MockPuzzleResultRepository_GetSubmissionsBetweenDatesByType_Call) Return(puzzleResults []*models.PuzzleResult, err error) *MockPuzzleResultRepository_GetSubmissionsBetweenDatesByType_Call {
	_c.Call.Return(puzzleResults, err)
	return _c
}

func (_c *MockPuzzleResultRepository_GetSubmissionsBetweenDatesByType_Call) RunAndReturn(run func(ctx context.Context, startDate string, endDate string, puzzleType models.PuzzleType) ([]*models.PuzzleResult, error)) *MockPuzzleResultRepository_GetSubmissionsBetweenDatesByType_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserRank provides a mock function for the type MockPuzzleResultRepository
func (_mock *MockPuzzleResultRepository) GetUserRank(ctx context.Context, score int, puzzleId primitive.ObjectID) *int {
	ret := _mock.Called(ctx, score, puzzleId)

	if len(ret) == 0 {
		panic("no return value specified for GetUserRank")
	}

	var r0 *int
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, primitive.ObjectID) *int); ok {
		r0 = returnFunc(ctx, score, puzzleId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*int)
		}
	}
	return r0
}

// MockPuzzleResultRepository_GetUserRank_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserRank'
type MockPuzzleResultRepository_GetUserRank_Call struct {
	*mock.Call
}

// GetUserRank is a helper method to define mock.On call
//   - ctx
//   - score
//   - puzzleId
func (_e *MockPuzzleResultRepository_Expecter) GetUserRank(ctx interface{}, score interface{}, puzzleId interface{}) *MockPuzzleResultRepository_GetUserRank_Call {
	return &MockPuzzleResultRepository_GetUserRank_Call{Call: _e.mock.On("GetUserRank", ctx, score, puzzleId)}
}

func (_c *MockPuzzleResultRepository_GetUserRank_Call) Run(run func(ctx context.Context, score int, puzzleId primitive.ObjectID)) *MockPuzzleResultRepository_GetUserRank_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockPuzzleResultRepository_GetUserRank_Call) Return(n *int) *MockPuzzleResultRepository_GetUserRank_Call {
	_c.Call.Return(n)
	return _c
}

func (_c *MockPuzzleResultRepository_GetUserRank_Call) RunAndReturn(run func(ctx context.Context, score int, puzzleId primitive.ObjectID) *int) *MockPuzzleResultRepository_GetUserRank_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function for the type MockPuzzleResultRepository
func (_mock *MockPuzzleResultRepository) List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.PuzzleResult, error) {
	ret := _mock.Called(ctx, filter, opts)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*models.PuzzleResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, *options.FindOptions) ([]*models.PuzzleResult, error)); ok {
		return returnFunc(ctx, filter, opts)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, *options.FindOptions) []*models.PuzzleResult); ok {
		r0 = returnFunc(ctx, filter, opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.PuzzleResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, *options.FindOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleResultRepository_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type MockPuzzleResultRepository_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - ctx
//   - filter
//   - opts
func (_e *MockPuzzleResultRepository_Expecter) List(ctx interface{}, filter interface{}, opts interface{}) *MockPuzzleResultRepository_List_Call {
	return &MockPuzzleResultRepository_List_Call{Call: _e.mock.On("List", ctx, filter, opts)}
}

func (_c *MockPuzzleResultRepository_List_Call) Run(run func(ctx context.Context, filter bson.M, opts *options.FindOptions)) *MockPuzzleResultRepository_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M), args[2].(*options.FindOptions))
	})
	return _c
}

func (_c *MockPuzzleResultRepository_List_Call) Return(puzzleResults []*models.PuzzleResult, err error) *MockPuzzleResultRepository_List_Call {
	_c.Call.Return(puzzleResults, err)
	return _c
}

func (_c *MockPuzzleResultRepository_List_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.PuzzleResult, error)) *MockPuzzleResultRepository_List_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function for the type MockPuzzleResultRepository
func (_mock *MockPuzzleResultRepository) Update(ctx context.Context, puzzleResult *models.PuzzleResult) error {
	ret := _mock.Called(ctx, puzzleResult)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PuzzleResult) error); ok {
		r0 = returnFunc(ctx, puzzleResult)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPuzzleResultRepository_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockPuzzleResultRepository_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx
//   - puzzleResult
func (_e *MockPuzzleResultRepository_Expecter) Update(ctx interface{}, puzzleResult interface{}) *MockPuzzleResultRepository_Update_Call {
	return &MockPuzzleResultRepository_Update_Call{Call: _e.mock.On("Update", ctx, puzzleResult)}
}

func (_c *MockPuzzleResultRepository_Update_Call) Run(run func(ctx context.Context, puzzleResult *models.PuzzleResult)) *MockPuzzleResultRepository_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.PuzzleResult))
	})
	return _c
}

func (_c *MockPuzzleResultRepository_Update_Call) Return(err error) *MockPuzzleResultRepository_Update_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPuzzleResultRepository_Update_Call) RunAndReturn(run func(ctx context.Context, puzzleResult *models.PuzzleResult) error) *MockPuzzleResultRepository_Update_Call {
	_c.Call.Return(run)
	return _c
}
