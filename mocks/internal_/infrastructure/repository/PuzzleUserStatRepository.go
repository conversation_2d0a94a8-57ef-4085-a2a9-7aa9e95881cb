// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockPuzzleUserStatRepository creates a new instance of MockPuzzleUserStatRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPuzzleUserStatRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPuzzleUserStatRepository {
	mock := &MockPuzzleUserStatRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockPuzzleUserStatRepository is an autogenerated mock type for the PuzzleUserStatRepository type
type MockPuzzleUserStatRepository struct {
	mock.Mock
}

type MockPuzzleUserStatRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPuzzleUserStatRepository) EXPECT() *MockPuzzleUserStatRepository_Expecter {
	return &MockPuzzleUserStatRepository_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type MockPuzzleUserStatRepository
func (_mock *MockPuzzleUserStatRepository) Create(ctx context.Context, userStat *models.PuzzleUserStats) error {
	ret := _mock.Called(ctx, userStat)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PuzzleUserStats) error); ok {
		r0 = returnFunc(ctx, userStat)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPuzzleUserStatRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockPuzzleUserStatRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - userStat
func (_e *MockPuzzleUserStatRepository_Expecter) Create(ctx interface{}, userStat interface{}) *MockPuzzleUserStatRepository_Create_Call {
	return &MockPuzzleUserStatRepository_Create_Call{Call: _e.mock.On("Create", ctx, userStat)}
}

func (_c *MockPuzzleUserStatRepository_Create_Call) Run(run func(ctx context.Context, userStat *models.PuzzleUserStats)) *MockPuzzleUserStatRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.PuzzleUserStats))
	})
	return _c
}

func (_c *MockPuzzleUserStatRepository_Create_Call) Return(err error) *MockPuzzleUserStatRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPuzzleUserStatRepository_Create_Call) RunAndReturn(run func(ctx context.Context, userStat *models.PuzzleUserStats) error) *MockPuzzleUserStatRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// FindByID provides a mock function for the type MockPuzzleUserStatRepository
func (_mock *MockPuzzleUserStatRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.PuzzleUserStats, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindByID")
	}

	var r0 *models.PuzzleUserStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.PuzzleUserStats, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.PuzzleUserStats); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleUserStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleUserStatRepository_FindByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByID'
type MockPuzzleUserStatRepository_FindByID_Call struct {
	*mock.Call
}

// FindByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockPuzzleUserStatRepository_Expecter) FindByID(ctx interface{}, id interface{}) *MockPuzzleUserStatRepository_FindByID_Call {
	return &MockPuzzleUserStatRepository_FindByID_Call{Call: _e.mock.On("FindByID", ctx, id)}
}

func (_c *MockPuzzleUserStatRepository_FindByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockPuzzleUserStatRepository_FindByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockPuzzleUserStatRepository_FindByID_Call) Return(puzzleUserStats *models.PuzzleUserStats, err error) *MockPuzzleUserStatRepository_FindByID_Call {
	_c.Call.Return(puzzleUserStats, err)
	return _c
}

func (_c *MockPuzzleUserStatRepository_FindByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.PuzzleUserStats, error)) *MockPuzzleUserStatRepository_FindByID_Call {
	_c.Call.Return(run)
	return _c
}

// FindByUserID provides a mock function for the type MockPuzzleUserStatRepository
func (_mock *MockPuzzleUserStatRepository) FindByUserID(ctx context.Context, userID primitive.ObjectID, puzzleType models.PuzzleType) (*models.PuzzleUserStats, error) {
	ret := _mock.Called(ctx, userID, puzzleType)

	if len(ret) == 0 {
		panic("no return value specified for FindByUserID")
	}

	var r0 *models.PuzzleUserStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.PuzzleType) (*models.PuzzleUserStats, error)); ok {
		return returnFunc(ctx, userID, puzzleType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.PuzzleType) *models.PuzzleUserStats); ok {
		r0 = returnFunc(ctx, userID, puzzleType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleUserStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, models.PuzzleType) error); ok {
		r1 = returnFunc(ctx, userID, puzzleType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleUserStatRepository_FindByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByUserID'
type MockPuzzleUserStatRepository_FindByUserID_Call struct {
	*mock.Call
}

// FindByUserID is a helper method to define mock.On call
//   - ctx
//   - userID
//   - puzzleType
func (_e *MockPuzzleUserStatRepository_Expecter) FindByUserID(ctx interface{}, userID interface{}, puzzleType interface{}) *MockPuzzleUserStatRepository_FindByUserID_Call {
	return &MockPuzzleUserStatRepository_FindByUserID_Call{Call: _e.mock.On("FindByUserID", ctx, userID, puzzleType)}
}

func (_c *MockPuzzleUserStatRepository_FindByUserID_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, puzzleType models.PuzzleType)) *MockPuzzleUserStatRepository_FindByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(models.PuzzleType))
	})
	return _c
}

func (_c *MockPuzzleUserStatRepository_FindByUserID_Call) Return(puzzleUserStats *models.PuzzleUserStats, err error) *MockPuzzleUserStatRepository_FindByUserID_Call {
	_c.Call.Return(puzzleUserStats, err)
	return _c
}

func (_c *MockPuzzleUserStatRepository_FindByUserID_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, puzzleType models.PuzzleType) (*models.PuzzleUserStats, error)) *MockPuzzleUserStatRepository_FindByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function for the type MockPuzzleUserStatRepository
func (_mock *MockPuzzleUserStatRepository) List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.PuzzleUserStats, error) {
	ret := _mock.Called(ctx, filter, opts)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*models.PuzzleUserStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, *options.FindOptions) ([]*models.PuzzleUserStats, error)); ok {
		return returnFunc(ctx, filter, opts)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, *options.FindOptions) []*models.PuzzleUserStats); ok {
		r0 = returnFunc(ctx, filter, opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.PuzzleUserStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, *options.FindOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleUserStatRepository_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type MockPuzzleUserStatRepository_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - ctx
//   - filter
//   - opts
func (_e *MockPuzzleUserStatRepository_Expecter) List(ctx interface{}, filter interface{}, opts interface{}) *MockPuzzleUserStatRepository_List_Call {
	return &MockPuzzleUserStatRepository_List_Call{Call: _e.mock.On("List", ctx, filter, opts)}
}

func (_c *MockPuzzleUserStatRepository_List_Call) Run(run func(ctx context.Context, filter bson.M, opts *options.FindOptions)) *MockPuzzleUserStatRepository_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M), args[2].(*options.FindOptions))
	})
	return _c
}

func (_c *MockPuzzleUserStatRepository_List_Call) Return(puzzleUserStatss []*models.PuzzleUserStats, err error) *MockPuzzleUserStatRepository_List_Call {
	_c.Call.Return(puzzleUserStatss, err)
	return _c
}

func (_c *MockPuzzleUserStatRepository_List_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.PuzzleUserStats, error)) *MockPuzzleUserStatRepository_List_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function for the type MockPuzzleUserStatRepository
func (_mock *MockPuzzleUserStatRepository) Update(ctx context.Context, userStat *models.PuzzleUserStats) error {
	ret := _mock.Called(ctx, userStat)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PuzzleUserStats) error); ok {
		r0 = returnFunc(ctx, userStat)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPuzzleUserStatRepository_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockPuzzleUserStatRepository_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx
//   - userStat
func (_e *MockPuzzleUserStatRepository_Expecter) Update(ctx interface{}, userStat interface{}) *MockPuzzleUserStatRepository_Update_Call {
	return &MockPuzzleUserStatRepository_Update_Call{Call: _e.mock.On("Update", ctx, userStat)}
}

func (_c *MockPuzzleUserStatRepository_Update_Call) Run(run func(ctx context.Context, userStat *models.PuzzleUserStats)) *MockPuzzleUserStatRepository_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.PuzzleUserStats))
	})
	return _c
}

func (_c *MockPuzzleUserStatRepository_Update_Call) Return(err error) *MockPuzzleUserStatRepository_Update_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPuzzleUserStatRepository_Update_Call) RunAndReturn(run func(ctx context.Context, userStat *models.PuzzleUserStats) error) *MockPuzzleUserStatRepository_Update_Call {
	_c.Call.Return(run)
	return _c
}
