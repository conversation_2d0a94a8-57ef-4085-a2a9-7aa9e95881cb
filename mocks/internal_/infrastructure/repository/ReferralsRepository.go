// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockReferralsRepository creates a new instance of MockReferralsRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockReferralsRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockReferralsRepository {
	mock := &MockReferralsRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockReferralsRepository is an autogenerated mock type for the ReferralsRepository type
type MockReferralsRepository struct {
	mock.Mock
}

type MockReferralsRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockReferralsRepository) EXPECT() *MockReferralsRepository_Expecter {
	return &MockReferralsRepository_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type MockReferralsRepository
func (_mock *MockReferralsRepository) Create(ctx context.Context, referral *models.Referral) error {
	ret := _mock.Called(ctx, referral)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Referral) error); ok {
		r0 = returnFunc(ctx, referral)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockReferralsRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockReferralsRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - referral
func (_e *MockReferralsRepository_Expecter) Create(ctx interface{}, referral interface{}) *MockReferralsRepository_Create_Call {
	return &MockReferralsRepository_Create_Call{Call: _e.mock.On("Create", ctx, referral)}
}

func (_c *MockReferralsRepository_Create_Call) Run(run func(ctx context.Context, referral *models.Referral)) *MockReferralsRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.Referral))
	})
	return _c
}

func (_c *MockReferralsRepository_Create_Call) Return(err error) *MockReferralsRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockReferralsRepository_Create_Call) RunAndReturn(run func(ctx context.Context, referral *models.Referral) error) *MockReferralsRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// FindByID provides a mock function for the type MockReferralsRepository
func (_mock *MockReferralsRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.Referral, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindByID")
	}

	var r0 *models.Referral
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Referral, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Referral); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Referral)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockReferralsRepository_FindByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByID'
type MockReferralsRepository_FindByID_Call struct {
	*mock.Call
}

// FindByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockReferralsRepository_Expecter) FindByID(ctx interface{}, id interface{}) *MockReferralsRepository_FindByID_Call {
	return &MockReferralsRepository_FindByID_Call{Call: _e.mock.On("FindByID", ctx, id)}
}

func (_c *MockReferralsRepository_FindByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockReferralsRepository_FindByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockReferralsRepository_FindByID_Call) Return(referral *models.Referral, err error) *MockReferralsRepository_FindByID_Call {
	_c.Call.Return(referral, err)
	return _c
}

func (_c *MockReferralsRepository_FindByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.Referral, error)) *MockReferralsRepository_FindByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserByReferralCode provides a mock function for the type MockReferralsRepository
func (_mock *MockReferralsRepository) GetUserByReferralCode(ctx context.Context, referralCode string) (*models.User, error) {
	ret := _mock.Called(ctx, referralCode)

	if len(ret) == 0 {
		panic("no return value specified for GetUserByReferralCode")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*models.User, error)); ok {
		return returnFunc(ctx, referralCode)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *models.User); ok {
		r0 = returnFunc(ctx, referralCode)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, referralCode)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockReferralsRepository_GetUserByReferralCode_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserByReferralCode'
type MockReferralsRepository_GetUserByReferralCode_Call struct {
	*mock.Call
}

// GetUserByReferralCode is a helper method to define mock.On call
//   - ctx
//   - referralCode
func (_e *MockReferralsRepository_Expecter) GetUserByReferralCode(ctx interface{}, referralCode interface{}) *MockReferralsRepository_GetUserByReferralCode_Call {
	return &MockReferralsRepository_GetUserByReferralCode_Call{Call: _e.mock.On("GetUserByReferralCode", ctx, referralCode)}
}

func (_c *MockReferralsRepository_GetUserByReferralCode_Call) Run(run func(ctx context.Context, referralCode string)) *MockReferralsRepository_GetUserByReferralCode_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockReferralsRepository_GetUserByReferralCode_Call) Return(user *models.User, err error) *MockReferralsRepository_GetUserByReferralCode_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockReferralsRepository_GetUserByReferralCode_Call) RunAndReturn(run func(ctx context.Context, referralCode string) (*models.User, error)) *MockReferralsRepository_GetUserByReferralCode_Call {
	_c.Call.Return(run)
	return _c
}
