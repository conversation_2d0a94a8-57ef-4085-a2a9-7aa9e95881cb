// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockShowdownLeaderboardRepository creates a new instance of MockShowdownLeaderboardRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockShowdownLeaderboardRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockShowdownLeaderboardRepository {
	mock := &MockShowdownLeaderboardRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockShowdownLeaderboardRepository is an autogenerated mock type for the ShowdownLeaderboardRepository type
type MockShowdownLeaderboardRepository struct {
	mock.Mock
}

type MockShowdownLeaderboardRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockShowdownLeaderboardRepository) EXPECT() *MockShowdownLeaderboardRepository_Expecter {
	return &MockShowdownLeaderboardRepository_Expecter{mock: &_m.Mock}
}

// BulkInsert provides a mock function for the type MockShowdownLeaderboardRepository
func (_mock *MockShowdownLeaderboardRepository) BulkInsert(ctx context.Context, participants []*models.LeaderParticipantEntity) error {
	ret := _mock.Called(ctx, participants)

	if len(ret) == 0 {
		panic("no return value specified for BulkInsert")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*models.LeaderParticipantEntity) error); ok {
		r0 = returnFunc(ctx, participants)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownLeaderboardRepository_BulkInsert_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BulkInsert'
type MockShowdownLeaderboardRepository_BulkInsert_Call struct {
	*mock.Call
}

// BulkInsert is a helper method to define mock.On call
//   - ctx
//   - participants
func (_e *MockShowdownLeaderboardRepository_Expecter) BulkInsert(ctx interface{}, participants interface{}) *MockShowdownLeaderboardRepository_BulkInsert_Call {
	return &MockShowdownLeaderboardRepository_BulkInsert_Call{Call: _e.mock.On("BulkInsert", ctx, participants)}
}

func (_c *MockShowdownLeaderboardRepository_BulkInsert_Call) Run(run func(ctx context.Context, participants []*models.LeaderParticipantEntity)) *MockShowdownLeaderboardRepository_BulkInsert_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*models.LeaderParticipantEntity))
	})
	return _c
}

func (_c *MockShowdownLeaderboardRepository_BulkInsert_Call) Return(err error) *MockShowdownLeaderboardRepository_BulkInsert_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownLeaderboardRepository_BulkInsert_Call) RunAndReturn(run func(ctx context.Context, participants []*models.LeaderParticipantEntity) error) *MockShowdownLeaderboardRepository_BulkInsert_Call {
	_c.Call.Return(run)
	return _c
}

// GetLeaderboardParticipantsPaginated provides a mock function for the type MockShowdownLeaderboardRepository
func (_mock *MockShowdownLeaderboardRepository) GetLeaderboardParticipantsPaginated(ctx context.Context, showdownID primitive.ObjectID, page int, pageSize int) ([]*models.LeaderParticipantEntity, error) {
	ret := _mock.Called(ctx, showdownID, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetLeaderboardParticipantsPaginated")
	}

	var r0 []*models.LeaderParticipantEntity
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) ([]*models.LeaderParticipantEntity, error)); ok {
		return returnFunc(ctx, showdownID, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) []*models.LeaderParticipantEntity); ok {
		r0 = returnFunc(ctx, showdownID, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.LeaderParticipantEntity)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int, int) error); ok {
		r1 = returnFunc(ctx, showdownID, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownLeaderboardRepository_GetLeaderboardParticipantsPaginated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLeaderboardParticipantsPaginated'
type MockShowdownLeaderboardRepository_GetLeaderboardParticipantsPaginated_Call struct {
	*mock.Call
}

// GetLeaderboardParticipantsPaginated is a helper method to define mock.On call
//   - ctx
//   - showdownID
//   - page
//   - pageSize
func (_e *MockShowdownLeaderboardRepository_Expecter) GetLeaderboardParticipantsPaginated(ctx interface{}, showdownID interface{}, page interface{}, pageSize interface{}) *MockShowdownLeaderboardRepository_GetLeaderboardParticipantsPaginated_Call {
	return &MockShowdownLeaderboardRepository_GetLeaderboardParticipantsPaginated_Call{Call: _e.mock.On("GetLeaderboardParticipantsPaginated", ctx, showdownID, page, pageSize)}
}

func (_c *MockShowdownLeaderboardRepository_GetLeaderboardParticipantsPaginated_Call) Run(run func(ctx context.Context, showdownID primitive.ObjectID, page int, pageSize int)) *MockShowdownLeaderboardRepository_GetLeaderboardParticipantsPaginated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int), args[3].(int))
	})
	return _c
}

func (_c *MockShowdownLeaderboardRepository_GetLeaderboardParticipantsPaginated_Call) Return(leaderParticipantEntitys []*models.LeaderParticipantEntity, err error) *MockShowdownLeaderboardRepository_GetLeaderboardParticipantsPaginated_Call {
	_c.Call.Return(leaderParticipantEntitys, err)
	return _c
}

func (_c *MockShowdownLeaderboardRepository_GetLeaderboardParticipantsPaginated_Call) RunAndReturn(run func(ctx context.Context, showdownID primitive.ObjectID, page int, pageSize int) ([]*models.LeaderParticipantEntity, error)) *MockShowdownLeaderboardRepository_GetLeaderboardParticipantsPaginated_Call {
	_c.Call.Return(run)
	return _c
}

// GetShowdownLeaderboardParticipantsCount provides a mock function for the type MockShowdownLeaderboardRepository
func (_mock *MockShowdownLeaderboardRepository) GetShowdownLeaderboardParticipantsCount(ctx context.Context, showdownID primitive.ObjectID) (int64, error) {
	ret := _mock.Called(ctx, showdownID)

	if len(ret) == 0 {
		panic("no return value specified for GetShowdownLeaderboardParticipantsCount")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (int64, error)); ok {
		return returnFunc(ctx, showdownID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) int64); ok {
		r0 = returnFunc(ctx, showdownID)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownLeaderboardRepository_GetShowdownLeaderboardParticipantsCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShowdownLeaderboardParticipantsCount'
type MockShowdownLeaderboardRepository_GetShowdownLeaderboardParticipantsCount_Call struct {
	*mock.Call
}

// GetShowdownLeaderboardParticipantsCount is a helper method to define mock.On call
//   - ctx
//   - showdownID
func (_e *MockShowdownLeaderboardRepository_Expecter) GetShowdownLeaderboardParticipantsCount(ctx interface{}, showdownID interface{}) *MockShowdownLeaderboardRepository_GetShowdownLeaderboardParticipantsCount_Call {
	return &MockShowdownLeaderboardRepository_GetShowdownLeaderboardParticipantsCount_Call{Call: _e.mock.On("GetShowdownLeaderboardParticipantsCount", ctx, showdownID)}
}

func (_c *MockShowdownLeaderboardRepository_GetShowdownLeaderboardParticipantsCount_Call) Run(run func(ctx context.Context, showdownID primitive.ObjectID)) *MockShowdownLeaderboardRepository_GetShowdownLeaderboardParticipantsCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownLeaderboardRepository_GetShowdownLeaderboardParticipantsCount_Call) Return(n int64, err error) *MockShowdownLeaderboardRepository_GetShowdownLeaderboardParticipantsCount_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockShowdownLeaderboardRepository_GetShowdownLeaderboardParticipantsCount_Call) RunAndReturn(run func(ctx context.Context, showdownID primitive.ObjectID) (int64, error)) *MockShowdownLeaderboardRepository_GetShowdownLeaderboardParticipantsCount_Call {
	_c.Call.Return(run)
	return _c
}
