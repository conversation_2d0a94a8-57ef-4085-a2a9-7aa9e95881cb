// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockShowdownParticipantRepository creates a new instance of MockShowdownParticipantRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockShowdownParticipantRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockShowdownParticipantRepository {
	mock := &MockShowdownParticipantRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockShowdownParticipantRepository is an autogenerated mock type for the ShowdownParticipantRepository type
type MockShowdownParticipantRepository struct {
	mock.Mock
}

type MockShowdownParticipantRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockShowdownParticipantRepository) EXPECT() *MockShowdownParticipantRepository_Expecter {
	return &MockShowdownParticipantRepository_Expecter{mock: &_m.Mock}
}

// BulkUpdate provides a mock function for the type MockShowdownParticipantRepository
func (_mock *MockShowdownParticipantRepository) BulkUpdate(ctx context.Context, participants []*models.ShowdownParticipant) error {
	ret := _mock.Called(ctx, participants)

	if len(ret) == 0 {
		panic("no return value specified for BulkUpdate")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*models.ShowdownParticipant) error); ok {
		r0 = returnFunc(ctx, participants)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownParticipantRepository_BulkUpdate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BulkUpdate'
type MockShowdownParticipantRepository_BulkUpdate_Call struct {
	*mock.Call
}

// BulkUpdate is a helper method to define mock.On call
//   - ctx
//   - participants
func (_e *MockShowdownParticipantRepository_Expecter) BulkUpdate(ctx interface{}, participants interface{}) *MockShowdownParticipantRepository_BulkUpdate_Call {
	return &MockShowdownParticipantRepository_BulkUpdate_Call{Call: _e.mock.On("BulkUpdate", ctx, participants)}
}

func (_c *MockShowdownParticipantRepository_BulkUpdate_Call) Run(run func(ctx context.Context, participants []*models.ShowdownParticipant)) *MockShowdownParticipantRepository_BulkUpdate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*models.ShowdownParticipant))
	})
	return _c
}

func (_c *MockShowdownParticipantRepository_BulkUpdate_Call) Return(err error) *MockShowdownParticipantRepository_BulkUpdate_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownParticipantRepository_BulkUpdate_Call) RunAndReturn(run func(ctx context.Context, participants []*models.ShowdownParticipant) error) *MockShowdownParticipantRepository_BulkUpdate_Call {
	_c.Call.Return(run)
	return _c
}

// CountByShowdown provides a mock function for the type MockShowdownParticipantRepository
func (_mock *MockShowdownParticipantRepository) CountByShowdown(ctx context.Context, showdownId primitive.ObjectID) (int64, error) {
	ret := _mock.Called(ctx, showdownId)

	if len(ret) == 0 {
		panic("no return value specified for CountByShowdown")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (int64, error)); ok {
		return returnFunc(ctx, showdownId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) int64); ok {
		r0 = returnFunc(ctx, showdownId)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownParticipantRepository_CountByShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountByShowdown'
type MockShowdownParticipantRepository_CountByShowdown_Call struct {
	*mock.Call
}

// CountByShowdown is a helper method to define mock.On call
//   - ctx
//   - showdownId
func (_e *MockShowdownParticipantRepository_Expecter) CountByShowdown(ctx interface{}, showdownId interface{}) *MockShowdownParticipantRepository_CountByShowdown_Call {
	return &MockShowdownParticipantRepository_CountByShowdown_Call{Call: _e.mock.On("CountByShowdown", ctx, showdownId)}
}

func (_c *MockShowdownParticipantRepository_CountByShowdown_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID)) *MockShowdownParticipantRepository_CountByShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownParticipantRepository_CountByShowdown_Call) Return(n int64, err error) *MockShowdownParticipantRepository_CountByShowdown_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockShowdownParticipantRepository_CountByShowdown_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID) (int64, error)) *MockShowdownParticipantRepository_CountByShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function for the type MockShowdownParticipantRepository
func (_mock *MockShowdownParticipantRepository) Create(ctx context.Context, participant *models.ShowdownParticipant) error {
	ret := _mock.Called(ctx, participant)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ShowdownParticipant) error); ok {
		r0 = returnFunc(ctx, participant)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownParticipantRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockShowdownParticipantRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - participant
func (_e *MockShowdownParticipantRepository_Expecter) Create(ctx interface{}, participant interface{}) *MockShowdownParticipantRepository_Create_Call {
	return &MockShowdownParticipantRepository_Create_Call{Call: _e.mock.On("Create", ctx, participant)}
}

func (_c *MockShowdownParticipantRepository_Create_Call) Run(run func(ctx context.Context, participant *models.ShowdownParticipant)) *MockShowdownParticipantRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.ShowdownParticipant))
	})
	return _c
}

func (_c *MockShowdownParticipantRepository_Create_Call) Return(err error) *MockShowdownParticipantRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownParticipantRepository_Create_Call) RunAndReturn(run func(ctx context.Context, participant *models.ShowdownParticipant) error) *MockShowdownParticipantRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function for the type MockShowdownParticipantRepository
func (_mock *MockShowdownParticipantRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownParticipantRepository_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockShowdownParticipantRepository_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockShowdownParticipantRepository_Expecter) Delete(ctx interface{}, id interface{}) *MockShowdownParticipantRepository_Delete_Call {
	return &MockShowdownParticipantRepository_Delete_Call{Call: _e.mock.On("Delete", ctx, id)}
}

func (_c *MockShowdownParticipantRepository_Delete_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockShowdownParticipantRepository_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownParticipantRepository_Delete_Call) Return(err error) *MockShowdownParticipantRepository_Delete_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownParticipantRepository_Delete_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) error) *MockShowdownParticipantRepository_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteByShowdownAndUser provides a mock function for the type MockShowdownParticipantRepository
func (_mock *MockShowdownParticipantRepository) DeleteByShowdownAndUser(ctx context.Context, showdownId primitive.ObjectID, userID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, showdownId, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteByShowdownAndUser")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, showdownId, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, showdownId, userID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownId, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownParticipantRepository_DeleteByShowdownAndUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteByShowdownAndUser'
type MockShowdownParticipantRepository_DeleteByShowdownAndUser_Call struct {
	*mock.Call
}

// DeleteByShowdownAndUser is a helper method to define mock.On call
//   - ctx
//   - showdownId
//   - userID
func (_e *MockShowdownParticipantRepository_Expecter) DeleteByShowdownAndUser(ctx interface{}, showdownId interface{}, userID interface{}) *MockShowdownParticipantRepository_DeleteByShowdownAndUser_Call {
	return &MockShowdownParticipantRepository_DeleteByShowdownAndUser_Call{Call: _e.mock.On("DeleteByShowdownAndUser", ctx, showdownId, userID)}
}

func (_c *MockShowdownParticipantRepository_DeleteByShowdownAndUser_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, userID primitive.ObjectID)) *MockShowdownParticipantRepository_DeleteByShowdownAndUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownParticipantRepository_DeleteByShowdownAndUser_Call) Return(b bool, err error) *MockShowdownParticipantRepository_DeleteByShowdownAndUser_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockShowdownParticipantRepository_DeleteByShowdownAndUser_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, userID primitive.ObjectID) (bool, error)) *MockShowdownParticipantRepository_DeleteByShowdownAndUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type MockShowdownParticipantRepository
func (_mock *MockShowdownParticipantRepository) GetByID(ctx context.Context, id primitive.ObjectID) (*models.ShowdownParticipant, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *models.ShowdownParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.ShowdownParticipant, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.ShowdownParticipant); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ShowdownParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownParticipantRepository_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type MockShowdownParticipantRepository_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockShowdownParticipantRepository_Expecter) GetByID(ctx interface{}, id interface{}) *MockShowdownParticipantRepository_GetByID_Call {
	return &MockShowdownParticipantRepository_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *MockShowdownParticipantRepository_GetByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockShowdownParticipantRepository_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownParticipantRepository_GetByID_Call) Return(showdownParticipant *models.ShowdownParticipant, err error) *MockShowdownParticipantRepository_GetByID_Call {
	_c.Call.Return(showdownParticipant, err)
	return _c
}

func (_c *MockShowdownParticipantRepository_GetByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.ShowdownParticipant, error)) *MockShowdownParticipantRepository_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByShowdownIDAndUserID provides a mock function for the type MockShowdownParticipantRepository
func (_mock *MockShowdownParticipantRepository) GetByShowdownIDAndUserID(ctx context.Context, showdownId primitive.ObjectID, userID primitive.ObjectID) (*models.ShowdownParticipant, error) {
	ret := _mock.Called(ctx, showdownId, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetByShowdownIDAndUserID")
	}

	var r0 *models.ShowdownParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (*models.ShowdownParticipant, error)); ok {
		return returnFunc(ctx, showdownId, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) *models.ShowdownParticipant); ok {
		r0 = returnFunc(ctx, showdownId, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ShowdownParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownId, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownParticipantRepository_GetByShowdownIDAndUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByShowdownIDAndUserID'
type MockShowdownParticipantRepository_GetByShowdownIDAndUserID_Call struct {
	*mock.Call
}

// GetByShowdownIDAndUserID is a helper method to define mock.On call
//   - ctx
//   - showdownId
//   - userID
func (_e *MockShowdownParticipantRepository_Expecter) GetByShowdownIDAndUserID(ctx interface{}, showdownId interface{}, userID interface{}) *MockShowdownParticipantRepository_GetByShowdownIDAndUserID_Call {
	return &MockShowdownParticipantRepository_GetByShowdownIDAndUserID_Call{Call: _e.mock.On("GetByShowdownIDAndUserID", ctx, showdownId, userID)}
}

func (_c *MockShowdownParticipantRepository_GetByShowdownIDAndUserID_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, userID primitive.ObjectID)) *MockShowdownParticipantRepository_GetByShowdownIDAndUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownParticipantRepository_GetByShowdownIDAndUserID_Call) Return(showdownParticipant *models.ShowdownParticipant, err error) *MockShowdownParticipantRepository_GetByShowdownIDAndUserID_Call {
	_c.Call.Return(showdownParticipant, err)
	return _c
}

func (_c *MockShowdownParticipantRepository_GetByShowdownIDAndUserID_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, userID primitive.ObjectID) (*models.ShowdownParticipant, error)) *MockShowdownParticipantRepository_GetByShowdownIDAndUserID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByUserID provides a mock function for the type MockShowdownParticipantRepository
func (_mock *MockShowdownParticipantRepository) GetByUserID(ctx context.Context, userID primitive.ObjectID) ([]*models.ShowdownParticipant, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetByUserID")
	}

	var r0 []*models.ShowdownParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) ([]*models.ShowdownParticipant, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) []*models.ShowdownParticipant); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.ShowdownParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownParticipantRepository_GetByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByUserID'
type MockShowdownParticipantRepository_GetByUserID_Call struct {
	*mock.Call
}

// GetByUserID is a helper method to define mock.On call
//   - ctx
//   - userID
func (_e *MockShowdownParticipantRepository_Expecter) GetByUserID(ctx interface{}, userID interface{}) *MockShowdownParticipantRepository_GetByUserID_Call {
	return &MockShowdownParticipantRepository_GetByUserID_Call{Call: _e.mock.On("GetByUserID", ctx, userID)}
}

func (_c *MockShowdownParticipantRepository_GetByUserID_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockShowdownParticipantRepository_GetByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownParticipantRepository_GetByUserID_Call) Return(showdownParticipants []*models.ShowdownParticipant, err error) *MockShowdownParticipantRepository_GetByUserID_Call {
	_c.Call.Return(showdownParticipants, err)
	return _c
}

func (_c *MockShowdownParticipantRepository_GetByUserID_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) ([]*models.ShowdownParticipant, error)) *MockShowdownParticipantRepository_GetByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// GetParticipantsByUserAndShowdowns provides a mock function for the type MockShowdownParticipantRepository
func (_mock *MockShowdownParticipantRepository) GetParticipantsByUserAndShowdowns(ctx context.Context, showdownIds []*primitive.ObjectID, userId primitive.ObjectID) ([]*models.ShowdownParticipant, error) {
	ret := _mock.Called(ctx, showdownIds, userId)

	if len(ret) == 0 {
		panic("no return value specified for GetParticipantsByUserAndShowdowns")
	}

	var r0 []*models.ShowdownParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*primitive.ObjectID, primitive.ObjectID) ([]*models.ShowdownParticipant, error)); ok {
		return returnFunc(ctx, showdownIds, userId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*primitive.ObjectID, primitive.ObjectID) []*models.ShowdownParticipant); ok {
		r0 = returnFunc(ctx, showdownIds, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.ShowdownParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []*primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownIds, userId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownParticipantRepository_GetParticipantsByUserAndShowdowns_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetParticipantsByUserAndShowdowns'
type MockShowdownParticipantRepository_GetParticipantsByUserAndShowdowns_Call struct {
	*mock.Call
}

// GetParticipantsByUserAndShowdowns is a helper method to define mock.On call
//   - ctx
//   - showdownIds
//   - userId
func (_e *MockShowdownParticipantRepository_Expecter) GetParticipantsByUserAndShowdowns(ctx interface{}, showdownIds interface{}, userId interface{}) *MockShowdownParticipantRepository_GetParticipantsByUserAndShowdowns_Call {
	return &MockShowdownParticipantRepository_GetParticipantsByUserAndShowdowns_Call{Call: _e.mock.On("GetParticipantsByUserAndShowdowns", ctx, showdownIds, userId)}
}

func (_c *MockShowdownParticipantRepository_GetParticipantsByUserAndShowdowns_Call) Run(run func(ctx context.Context, showdownIds []*primitive.ObjectID, userId primitive.ObjectID)) *MockShowdownParticipantRepository_GetParticipantsByUserAndShowdowns_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*primitive.ObjectID), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownParticipantRepository_GetParticipantsByUserAndShowdowns_Call) Return(showdownParticipants []*models.ShowdownParticipant, err error) *MockShowdownParticipantRepository_GetParticipantsByUserAndShowdowns_Call {
	_c.Call.Return(showdownParticipants, err)
	return _c
}

func (_c *MockShowdownParticipantRepository_GetParticipantsByUserAndShowdowns_Call) RunAndReturn(run func(ctx context.Context, showdownIds []*primitive.ObjectID, userId primitive.ObjectID) ([]*models.ShowdownParticipant, error)) *MockShowdownParticipantRepository_GetParticipantsByUserAndShowdowns_Call {
	_c.Call.Return(run)
	return _c
}

// GetShowdownParticipantsCount provides a mock function for the type MockShowdownParticipantRepository
func (_mock *MockShowdownParticipantRepository) GetShowdownParticipantsCount(ctx context.Context, showdownID primitive.ObjectID) (int64, error) {
	ret := _mock.Called(ctx, showdownID)

	if len(ret) == 0 {
		panic("no return value specified for GetShowdownParticipantsCount")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (int64, error)); ok {
		return returnFunc(ctx, showdownID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) int64); ok {
		r0 = returnFunc(ctx, showdownID)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownParticipantRepository_GetShowdownParticipantsCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShowdownParticipantsCount'
type MockShowdownParticipantRepository_GetShowdownParticipantsCount_Call struct {
	*mock.Call
}

// GetShowdownParticipantsCount is a helper method to define mock.On call
//   - ctx
//   - showdownID
func (_e *MockShowdownParticipantRepository_Expecter) GetShowdownParticipantsCount(ctx interface{}, showdownID interface{}) *MockShowdownParticipantRepository_GetShowdownParticipantsCount_Call {
	return &MockShowdownParticipantRepository_GetShowdownParticipantsCount_Call{Call: _e.mock.On("GetShowdownParticipantsCount", ctx, showdownID)}
}

func (_c *MockShowdownParticipantRepository_GetShowdownParticipantsCount_Call) Run(run func(ctx context.Context, showdownID primitive.ObjectID)) *MockShowdownParticipantRepository_GetShowdownParticipantsCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownParticipantRepository_GetShowdownParticipantsCount_Call) Return(n int64, err error) *MockShowdownParticipantRepository_GetShowdownParticipantsCount_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockShowdownParticipantRepository_GetShowdownParticipantsCount_Call) RunAndReturn(run func(ctx context.Context, showdownID primitive.ObjectID) (int64, error)) *MockShowdownParticipantRepository_GetShowdownParticipantsCount_Call {
	_c.Call.Return(run)
	return _c
}

// GetShowdownParticipantsOfLastPass provides a mock function for the type MockShowdownParticipantRepository
func (_mock *MockShowdownParticipantRepository) GetShowdownParticipantsOfLastPass(ctx context.Context, showdownID primitive.ObjectID, roundCount int) ([]*models.ShowdownParticipant, error) {
	ret := _mock.Called(ctx, showdownID, roundCount)

	if len(ret) == 0 {
		panic("no return value specified for GetShowdownParticipantsOfLastPass")
	}

	var r0 []*models.ShowdownParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) ([]*models.ShowdownParticipant, error)); ok {
		return returnFunc(ctx, showdownID, roundCount)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) []*models.ShowdownParticipant); ok {
		r0 = returnFunc(ctx, showdownID, roundCount)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.ShowdownParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int) error); ok {
		r1 = returnFunc(ctx, showdownID, roundCount)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownParticipantRepository_GetShowdownParticipantsOfLastPass_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShowdownParticipantsOfLastPass'
type MockShowdownParticipantRepository_GetShowdownParticipantsOfLastPass_Call struct {
	*mock.Call
}

// GetShowdownParticipantsOfLastPass is a helper method to define mock.On call
//   - ctx
//   - showdownID
//   - roundCount
func (_e *MockShowdownParticipantRepository_Expecter) GetShowdownParticipantsOfLastPass(ctx interface{}, showdownID interface{}, roundCount interface{}) *MockShowdownParticipantRepository_GetShowdownParticipantsOfLastPass_Call {
	return &MockShowdownParticipantRepository_GetShowdownParticipantsOfLastPass_Call{Call: _e.mock.On("GetShowdownParticipantsOfLastPass", ctx, showdownID, roundCount)}
}

func (_c *MockShowdownParticipantRepository_GetShowdownParticipantsOfLastPass_Call) Run(run func(ctx context.Context, showdownID primitive.ObjectID, roundCount int)) *MockShowdownParticipantRepository_GetShowdownParticipantsOfLastPass_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int))
	})
	return _c
}

func (_c *MockShowdownParticipantRepository_GetShowdownParticipantsOfLastPass_Call) Return(showdownParticipants []*models.ShowdownParticipant, err error) *MockShowdownParticipantRepository_GetShowdownParticipantsOfLastPass_Call {
	_c.Call.Return(showdownParticipants, err)
	return _c
}

func (_c *MockShowdownParticipantRepository_GetShowdownParticipantsOfLastPass_Call) RunAndReturn(run func(ctx context.Context, showdownID primitive.ObjectID, roundCount int) ([]*models.ShowdownParticipant, error)) *MockShowdownParticipantRepository_GetShowdownParticipantsOfLastPass_Call {
	_c.Call.Return(run)
	return _c
}

// GetShowdownParticipantsPaginated provides a mock function for the type MockShowdownParticipantRepository
func (_mock *MockShowdownParticipantRepository) GetShowdownParticipantsPaginated(ctx context.Context, showdownID primitive.ObjectID, page int, pageSize int) ([]*models.ShowdownParticipant, error) {
	ret := _mock.Called(ctx, showdownID, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetShowdownParticipantsPaginated")
	}

	var r0 []*models.ShowdownParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) ([]*models.ShowdownParticipant, error)); ok {
		return returnFunc(ctx, showdownID, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) []*models.ShowdownParticipant); ok {
		r0 = returnFunc(ctx, showdownID, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.ShowdownParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int, int) error); ok {
		r1 = returnFunc(ctx, showdownID, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownParticipantRepository_GetShowdownParticipantsPaginated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShowdownParticipantsPaginated'
type MockShowdownParticipantRepository_GetShowdownParticipantsPaginated_Call struct {
	*mock.Call
}

// GetShowdownParticipantsPaginated is a helper method to define mock.On call
//   - ctx
//   - showdownID
//   - page
//   - pageSize
func (_e *MockShowdownParticipantRepository_Expecter) GetShowdownParticipantsPaginated(ctx interface{}, showdownID interface{}, page interface{}, pageSize interface{}) *MockShowdownParticipantRepository_GetShowdownParticipantsPaginated_Call {
	return &MockShowdownParticipantRepository_GetShowdownParticipantsPaginated_Call{Call: _e.mock.On("GetShowdownParticipantsPaginated", ctx, showdownID, page, pageSize)}
}

func (_c *MockShowdownParticipantRepository_GetShowdownParticipantsPaginated_Call) Run(run func(ctx context.Context, showdownID primitive.ObjectID, page int, pageSize int)) *MockShowdownParticipantRepository_GetShowdownParticipantsPaginated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int), args[3].(int))
	})
	return _c
}

func (_c *MockShowdownParticipantRepository_GetShowdownParticipantsPaginated_Call) Return(showdownParticipants []*models.ShowdownParticipant, err error) *MockShowdownParticipantRepository_GetShowdownParticipantsPaginated_Call {
	_c.Call.Return(showdownParticipants, err)
	return _c
}

func (_c *MockShowdownParticipantRepository_GetShowdownParticipantsPaginated_Call) RunAndReturn(run func(ctx context.Context, showdownID primitive.ObjectID, page int, pageSize int) ([]*models.ShowdownParticipant, error)) *MockShowdownParticipantRepository_GetShowdownParticipantsPaginated_Call {
	_c.Call.Return(run)
	return _c
}

// GetTopParticipantsByShowdown provides a mock function for the type MockShowdownParticipantRepository
func (_mock *MockShowdownParticipantRepository) GetTopParticipantsByShowdown(ctx context.Context, showdownId primitive.ObjectID, limit int64) ([]*models.ShowdownParticipant, error) {
	ret := _mock.Called(ctx, showdownId, limit)

	if len(ret) == 0 {
		panic("no return value specified for GetTopParticipantsByShowdown")
	}

	var r0 []*models.ShowdownParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int64) ([]*models.ShowdownParticipant, error)); ok {
		return returnFunc(ctx, showdownId, limit)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int64) []*models.ShowdownParticipant); ok {
		r0 = returnFunc(ctx, showdownId, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.ShowdownParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int64) error); ok {
		r1 = returnFunc(ctx, showdownId, limit)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownParticipantRepository_GetTopParticipantsByShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTopParticipantsByShowdown'
type MockShowdownParticipantRepository_GetTopParticipantsByShowdown_Call struct {
	*mock.Call
}

// GetTopParticipantsByShowdown is a helper method to define mock.On call
//   - ctx
//   - showdownId
//   - limit
func (_e *MockShowdownParticipantRepository_Expecter) GetTopParticipantsByShowdown(ctx interface{}, showdownId interface{}, limit interface{}) *MockShowdownParticipantRepository_GetTopParticipantsByShowdown_Call {
	return &MockShowdownParticipantRepository_GetTopParticipantsByShowdown_Call{Call: _e.mock.On("GetTopParticipantsByShowdown", ctx, showdownId, limit)}
}

func (_c *MockShowdownParticipantRepository_GetTopParticipantsByShowdown_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, limit int64)) *MockShowdownParticipantRepository_GetTopParticipantsByShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int64))
	})
	return _c
}

func (_c *MockShowdownParticipantRepository_GetTopParticipantsByShowdown_Call) Return(showdownParticipants []*models.ShowdownParticipant, err error) *MockShowdownParticipantRepository_GetTopParticipantsByShowdown_Call {
	_c.Call.Return(showdownParticipants, err)
	return _c
}

func (_c *MockShowdownParticipantRepository_GetTopParticipantsByShowdown_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, limit int64) ([]*models.ShowdownParticipant, error)) *MockShowdownParticipantRepository_GetTopParticipantsByShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function for the type MockShowdownParticipantRepository
func (_mock *MockShowdownParticipantRepository) Update(ctx context.Context, participant *models.ShowdownParticipant) error {
	ret := _mock.Called(ctx, participant)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ShowdownParticipant) error); ok {
		r0 = returnFunc(ctx, participant)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownParticipantRepository_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockShowdownParticipantRepository_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx
//   - participant
func (_e *MockShowdownParticipantRepository_Expecter) Update(ctx interface{}, participant interface{}) *MockShowdownParticipantRepository_Update_Call {
	return &MockShowdownParticipantRepository_Update_Call{Call: _e.mock.On("Update", ctx, participant)}
}

func (_c *MockShowdownParticipantRepository_Update_Call) Run(run func(ctx context.Context, participant *models.ShowdownParticipant)) *MockShowdownParticipantRepository_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.ShowdownParticipant))
	})
	return _c
}

func (_c *MockShowdownParticipantRepository_Update_Call) Return(err error) *MockShowdownParticipantRepository_Update_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownParticipantRepository_Update_Call) RunAndReturn(run func(ctx context.Context, participant *models.ShowdownParticipant) error) *MockShowdownParticipantRepository_Update_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateRounds provides a mock function for the type MockShowdownParticipantRepository
func (_mock *MockShowdownParticipantRepository) UpdateRounds(ctx context.Context, participantID primitive.ObjectID, rounds []*models.ShowdownRound) error {
	ret := _mock.Called(ctx, participantID, rounds)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRounds")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, []*models.ShowdownRound) error); ok {
		r0 = returnFunc(ctx, participantID, rounds)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownParticipantRepository_UpdateRounds_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRounds'
type MockShowdownParticipantRepository_UpdateRounds_Call struct {
	*mock.Call
}

// UpdateRounds is a helper method to define mock.On call
//   - ctx
//   - participantID
//   - rounds
func (_e *MockShowdownParticipantRepository_Expecter) UpdateRounds(ctx interface{}, participantID interface{}, rounds interface{}) *MockShowdownParticipantRepository_UpdateRounds_Call {
	return &MockShowdownParticipantRepository_UpdateRounds_Call{Call: _e.mock.On("UpdateRounds", ctx, participantID, rounds)}
}

func (_c *MockShowdownParticipantRepository_UpdateRounds_Call) Run(run func(ctx context.Context, participantID primitive.ObjectID, rounds []*models.ShowdownRound)) *MockShowdownParticipantRepository_UpdateRounds_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].([]*models.ShowdownRound))
	})
	return _c
}

func (_c *MockShowdownParticipantRepository_UpdateRounds_Call) Return(err error) *MockShowdownParticipantRepository_UpdateRounds_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownParticipantRepository_UpdateRounds_Call) RunAndReturn(run func(ctx context.Context, participantID primitive.ObjectID, rounds []*models.ShowdownRound) error) *MockShowdownParticipantRepository_UpdateRounds_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateShowdownRoundsBatch provides a mock function for the type MockShowdownParticipantRepository
func (_mock *MockShowdownParticipantRepository) UpdateShowdownRoundsBatch(ctx context.Context, roundDtos map[primitive.ObjectID]*models.ShowdownRoundDto, showdownID primitive.ObjectID) error {
	ret := _mock.Called(ctx, roundDtos, showdownID)

	if len(ret) == 0 {
		panic("no return value specified for UpdateShowdownRoundsBatch")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, map[primitive.ObjectID]*models.ShowdownRoundDto, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, roundDtos, showdownID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownParticipantRepository_UpdateShowdownRoundsBatch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateShowdownRoundsBatch'
type MockShowdownParticipantRepository_UpdateShowdownRoundsBatch_Call struct {
	*mock.Call
}

// UpdateShowdownRoundsBatch is a helper method to define mock.On call
//   - ctx
//   - roundDtos
//   - showdownID
func (_e *MockShowdownParticipantRepository_Expecter) UpdateShowdownRoundsBatch(ctx interface{}, roundDtos interface{}, showdownID interface{}) *MockShowdownParticipantRepository_UpdateShowdownRoundsBatch_Call {
	return &MockShowdownParticipantRepository_UpdateShowdownRoundsBatch_Call{Call: _e.mock.On("UpdateShowdownRoundsBatch", ctx, roundDtos, showdownID)}
}

func (_c *MockShowdownParticipantRepository_UpdateShowdownRoundsBatch_Call) Run(run func(ctx context.Context, roundDtos map[primitive.ObjectID]*models.ShowdownRoundDto, showdownID primitive.ObjectID)) *MockShowdownParticipantRepository_UpdateShowdownRoundsBatch_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(map[primitive.ObjectID]*models.ShowdownRoundDto), args[2].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockShowdownParticipantRepository_UpdateShowdownRoundsBatch_Call) Return(err error) *MockShowdownParticipantRepository_UpdateShowdownRoundsBatch_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownParticipantRepository_UpdateShowdownRoundsBatch_Call) RunAndReturn(run func(ctx context.Context, roundDtos map[primitive.ObjectID]*models.ShowdownRoundDto, showdownID primitive.ObjectID) error) *MockShowdownParticipantRepository_UpdateShowdownRoundsBatch_Call {
	_c.Call.Return(run)
	return _c
}
