// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockStreakShieldTransactionRepository creates a new instance of MockStreakShieldTransactionRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockStreakShieldTransactionRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockStreakShieldTransactionRepository {
	mock := &MockStreakShieldTransactionRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockStreakShieldTransactionRepository is an autogenerated mock type for the StreakShieldTransactionRepository type
type MockStreakShieldTransactionRepository struct {
	mock.Mock
}

type MockStreakShieldTransactionRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockStreakShieldTransactionRepository) EXPECT() *MockStreakShieldTransactionRepository_Expecter {
	return &MockStreakShieldTransactionRepository_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type MockStreakShieldTransactionRepository
func (_mock *MockStreakShieldTransactionRepository) Create(ctx context.Context, transaction *models.StreakShieldTransaction) error {
	ret := _mock.Called(ctx, transaction)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.StreakShieldTransaction) error); ok {
		r0 = returnFunc(ctx, transaction)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockStreakShieldTransactionRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockStreakShieldTransactionRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - transaction
func (_e *MockStreakShieldTransactionRepository_Expecter) Create(ctx interface{}, transaction interface{}) *MockStreakShieldTransactionRepository_Create_Call {
	return &MockStreakShieldTransactionRepository_Create_Call{Call: _e.mock.On("Create", ctx, transaction)}
}

func (_c *MockStreakShieldTransactionRepository_Create_Call) Run(run func(ctx context.Context, transaction *models.StreakShieldTransaction)) *MockStreakShieldTransactionRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.StreakShieldTransaction))
	})
	return _c
}

func (_c *MockStreakShieldTransactionRepository_Create_Call) Return(err error) *MockStreakShieldTransactionRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockStreakShieldTransactionRepository_Create_Call) RunAndReturn(run func(ctx context.Context, transaction *models.StreakShieldTransaction) error) *MockStreakShieldTransactionRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// FindAll provides a mock function for the type MockStreakShieldTransactionRepository
func (_mock *MockStreakShieldTransactionRepository) FindAll(ctx context.Context, page int, pageSize int) ([]*models.StreakShieldTransaction, int, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for FindAll")
	}

	var r0 []*models.StreakShieldTransaction
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, int) ([]*models.StreakShieldTransaction, int, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, int) []*models.StreakShieldTransaction); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.StreakShieldTransaction)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int, int) int); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, int, int) error); ok {
		r2 = returnFunc(ctx, page, pageSize)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// MockStreakShieldTransactionRepository_FindAll_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindAll'
type MockStreakShieldTransactionRepository_FindAll_Call struct {
	*mock.Call
}

// FindAll is a helper method to define mock.On call
//   - ctx
//   - page
//   - pageSize
func (_e *MockStreakShieldTransactionRepository_Expecter) FindAll(ctx interface{}, page interface{}, pageSize interface{}) *MockStreakShieldTransactionRepository_FindAll_Call {
	return &MockStreakShieldTransactionRepository_FindAll_Call{Call: _e.mock.On("FindAll", ctx, page, pageSize)}
}

func (_c *MockStreakShieldTransactionRepository_FindAll_Call) Run(run func(ctx context.Context, page int, pageSize int)) *MockStreakShieldTransactionRepository_FindAll_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(int))
	})
	return _c
}

func (_c *MockStreakShieldTransactionRepository_FindAll_Call) Return(streakShieldTransactions []*models.StreakShieldTransaction, n int, err error) *MockStreakShieldTransactionRepository_FindAll_Call {
	_c.Call.Return(streakShieldTransactions, n, err)
	return _c
}

func (_c *MockStreakShieldTransactionRepository_FindAll_Call) RunAndReturn(run func(ctx context.Context, page int, pageSize int) ([]*models.StreakShieldTransaction, int, error)) *MockStreakShieldTransactionRepository_FindAll_Call {
	_c.Call.Return(run)
	return _c
}

// FindByID provides a mock function for the type MockStreakShieldTransactionRepository
func (_mock *MockStreakShieldTransactionRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.StreakShieldTransaction, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindByID")
	}

	var r0 *models.StreakShieldTransaction
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.StreakShieldTransaction, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.StreakShieldTransaction); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.StreakShieldTransaction)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockStreakShieldTransactionRepository_FindByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByID'
type MockStreakShieldTransactionRepository_FindByID_Call struct {
	*mock.Call
}

// FindByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockStreakShieldTransactionRepository_Expecter) FindByID(ctx interface{}, id interface{}) *MockStreakShieldTransactionRepository_FindByID_Call {
	return &MockStreakShieldTransactionRepository_FindByID_Call{Call: _e.mock.On("FindByID", ctx, id)}
}

func (_c *MockStreakShieldTransactionRepository_FindByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockStreakShieldTransactionRepository_FindByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockStreakShieldTransactionRepository_FindByID_Call) Return(streakShieldTransaction *models.StreakShieldTransaction, err error) *MockStreakShieldTransactionRepository_FindByID_Call {
	_c.Call.Return(streakShieldTransaction, err)
	return _c
}

func (_c *MockStreakShieldTransactionRepository_FindByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.StreakShieldTransaction, error)) *MockStreakShieldTransactionRepository_FindByID_Call {
	_c.Call.Return(run)
	return _c
}

// FindByUserID provides a mock function for the type MockStreakShieldTransactionRepository
func (_mock *MockStreakShieldTransactionRepository) FindByUserID(ctx context.Context, userID primitive.ObjectID, page int, pageSize int) ([]*models.StreakShieldTransaction, int, error) {
	ret := _mock.Called(ctx, userID, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for FindByUserID")
	}

	var r0 []*models.StreakShieldTransaction
	var r1 int
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) ([]*models.StreakShieldTransaction, int, error)); ok {
		return returnFunc(ctx, userID, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) []*models.StreakShieldTransaction); ok {
		r0 = returnFunc(ctx, userID, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.StreakShieldTransaction)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int, int) int); ok {
		r1 = returnFunc(ctx, userID, page, pageSize)
	} else {
		r1 = ret.Get(1).(int)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, primitive.ObjectID, int, int) error); ok {
		r2 = returnFunc(ctx, userID, page, pageSize)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// MockStreakShieldTransactionRepository_FindByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByUserID'
type MockStreakShieldTransactionRepository_FindByUserID_Call struct {
	*mock.Call
}

// FindByUserID is a helper method to define mock.On call
//   - ctx
//   - userID
//   - page
//   - pageSize
func (_e *MockStreakShieldTransactionRepository_Expecter) FindByUserID(ctx interface{}, userID interface{}, page interface{}, pageSize interface{}) *MockStreakShieldTransactionRepository_FindByUserID_Call {
	return &MockStreakShieldTransactionRepository_FindByUserID_Call{Call: _e.mock.On("FindByUserID", ctx, userID, page, pageSize)}
}

func (_c *MockStreakShieldTransactionRepository_FindByUserID_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, page int, pageSize int)) *MockStreakShieldTransactionRepository_FindByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int), args[3].(int))
	})
	return _c
}

func (_c *MockStreakShieldTransactionRepository_FindByUserID_Call) Return(streakShieldTransactions []*models.StreakShieldTransaction, n int, err error) *MockStreakShieldTransactionRepository_FindByUserID_Call {
	_c.Call.Return(streakShieldTransactions, n, err)
	return _c
}

func (_c *MockStreakShieldTransactionRepository_FindByUserID_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, page int, pageSize int) ([]*models.StreakShieldTransaction, int, error)) *MockStreakShieldTransactionRepository_FindByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function for the type MockStreakShieldTransactionRepository
func (_mock *MockStreakShieldTransactionRepository) UpdateOne(ctx context.Context, filter bson.M, update bson.M) error {
	ret := _mock.Called(ctx, filter, update)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, bson.M) error); ok {
		r0 = returnFunc(ctx, filter, update)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockStreakShieldTransactionRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockStreakShieldTransactionRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx
//   - filter
//   - update
func (_e *MockStreakShieldTransactionRepository_Expecter) UpdateOne(ctx interface{}, filter interface{}, update interface{}) *MockStreakShieldTransactionRepository_UpdateOne_Call {
	return &MockStreakShieldTransactionRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne", ctx, filter, update)}
}

func (_c *MockStreakShieldTransactionRepository_UpdateOne_Call) Run(run func(ctx context.Context, filter bson.M, update bson.M)) *MockStreakShieldTransactionRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M), args[2].(bson.M))
	})
	return _c
}

func (_c *MockStreakShieldTransactionRepository_UpdateOne_Call) Return(err error) *MockStreakShieldTransactionRepository_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockStreakShieldTransactionRepository_UpdateOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, update bson.M) error) *MockStreakShieldTransactionRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}
