// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockUserGameBucketRepository creates a new instance of MockUserGameBucketRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserGameBucketRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserGameBucketRepository {
	mock := &MockUserGameBucketRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockUserGameBucketRepository is an autogenerated mock type for the UserGameBucketRepository type
type MockUserGameBucketRepository struct {
	mock.Mock
}

type MockUserGameBucketRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserGameBucketRepository) EXPECT() *MockUserGameBucketRepository_Expecter {
	return &MockUserGameBucketRepository_Expecter{mock: &_m.Mock}
}

// AddGame provides a mock function for the type MockUserGameBucketRepository
func (_mock *MockUserGameBucketRepository) AddGame(ctx context.Context, userID primitive.ObjectID, game *models.UserGame) (bool, error) {
	ret := _mock.Called(ctx, userID, game)

	if len(ret) == 0 {
		panic("no return value specified for AddGame")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *models.UserGame) (bool, error)); ok {
		return returnFunc(ctx, userID, game)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *models.UserGame) bool); ok {
		r0 = returnFunc(ctx, userID, game)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *models.UserGame) error); ok {
		r1 = returnFunc(ctx, userID, game)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserGameBucketRepository_AddGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddGame'
type MockUserGameBucketRepository_AddGame_Call struct {
	*mock.Call
}

// AddGame is a helper method to define mock.On call
//   - ctx
//   - userID
//   - game
func (_e *MockUserGameBucketRepository_Expecter) AddGame(ctx interface{}, userID interface{}, game interface{}) *MockUserGameBucketRepository_AddGame_Call {
	return &MockUserGameBucketRepository_AddGame_Call{Call: _e.mock.On("AddGame", ctx, userID, game)}
}

func (_c *MockUserGameBucketRepository_AddGame_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, game *models.UserGame)) *MockUserGameBucketRepository_AddGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(*models.UserGame))
	})
	return _c
}

func (_c *MockUserGameBucketRepository_AddGame_Call) Return(b bool, err error) *MockUserGameBucketRepository_AddGame_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockUserGameBucketRepository_AddGame_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, game *models.UserGame) (bool, error)) *MockUserGameBucketRepository_AddGame_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function for the type MockUserGameBucketRepository
func (_mock *MockUserGameBucketRepository) Create(ctx context.Context, bucket *models.UserGameBucket) error {
	ret := _mock.Called(ctx, bucket)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserGameBucket) error); ok {
		r0 = returnFunc(ctx, bucket)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserGameBucketRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockUserGameBucketRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - bucket
func (_e *MockUserGameBucketRepository_Expecter) Create(ctx interface{}, bucket interface{}) *MockUserGameBucketRepository_Create_Call {
	return &MockUserGameBucketRepository_Create_Call{Call: _e.mock.On("Create", ctx, bucket)}
}

func (_c *MockUserGameBucketRepository_Create_Call) Run(run func(ctx context.Context, bucket *models.UserGameBucket)) *MockUserGameBucketRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.UserGameBucket))
	})
	return _c
}

func (_c *MockUserGameBucketRepository_Create_Call) Return(err error) *MockUserGameBucketRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserGameBucketRepository_Create_Call) RunAndReturn(run func(ctx context.Context, bucket *models.UserGameBucket) error) *MockUserGameBucketRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function for the type MockUserGameBucketRepository
func (_mock *MockUserGameBucketRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserGameBucketRepository_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockUserGameBucketRepository_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockUserGameBucketRepository_Expecter) Delete(ctx interface{}, id interface{}) *MockUserGameBucketRepository_Delete_Call {
	return &MockUserGameBucketRepository_Delete_Call{Call: _e.mock.On("Delete", ctx, id)}
}

func (_c *MockUserGameBucketRepository_Delete_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockUserGameBucketRepository_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserGameBucketRepository_Delete_Call) Return(err error) *MockUserGameBucketRepository_Delete_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserGameBucketRepository_Delete_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) error) *MockUserGameBucketRepository_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// Find provides a mock function for the type MockUserGameBucketRepository
func (_mock *MockUserGameBucketRepository) Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.UserGameBucket, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Find")
	}

	var r0 []*models.UserGameBucket
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) ([]*models.UserGameBucket, error)); ok {
		return returnFunc(ctx, filter, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) []*models.UserGameBucket); ok {
		r0 = returnFunc(ctx, filter, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserGameBucket)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, ...*options.FindOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserGameBucketRepository_Find_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Find'
type MockUserGameBucketRepository_Find_Call struct {
	*mock.Call
}

// Find is a helper method to define mock.On call
//   - ctx
//   - filter
//   - opts
func (_e *MockUserGameBucketRepository_Expecter) Find(ctx interface{}, filter interface{}, opts ...interface{}) *MockUserGameBucketRepository_Find_Call {
	return &MockUserGameBucketRepository_Find_Call{Call: _e.mock.On("Find",
		append([]interface{}{ctx, filter}, opts...)...)}
}

func (_c *MockUserGameBucketRepository_Find_Call) Run(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions)) *MockUserGameBucketRepository_Find_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]*options.FindOptions)
		run(args[0].(context.Context), args[1].(bson.M), variadicArgs...)
	})
	return _c
}

func (_c *MockUserGameBucketRepository_Find_Call) Return(userGameBuckets []*models.UserGameBucket, err error) *MockUserGameBucketRepository_Find_Call {
	_c.Call.Return(userGameBuckets, err)
	return _c
}

func (_c *MockUserGameBucketRepository_Find_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.UserGameBucket, error)) *MockUserGameBucketRepository_Find_Call {
	_c.Call.Return(run)
	return _c
}

// FindOne provides a mock function for the type MockUserGameBucketRepository
func (_mock *MockUserGameBucketRepository) FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.UserGameBucket, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for FindOne")
	}

	var r0 *models.UserGameBucket
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOneOptions) (*models.UserGameBucket, error)); ok {
		return returnFunc(ctx, filter, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOneOptions) *models.UserGameBucket); ok {
		r0 = returnFunc(ctx, filter, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserGameBucket)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, ...*options.FindOneOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserGameBucketRepository_FindOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOne'
type MockUserGameBucketRepository_FindOne_Call struct {
	*mock.Call
}

// FindOne is a helper method to define mock.On call
//   - ctx
//   - filter
//   - opts
func (_e *MockUserGameBucketRepository_Expecter) FindOne(ctx interface{}, filter interface{}, opts ...interface{}) *MockUserGameBucketRepository_FindOne_Call {
	return &MockUserGameBucketRepository_FindOne_Call{Call: _e.mock.On("FindOne",
		append([]interface{}{ctx, filter}, opts...)...)}
}

func (_c *MockUserGameBucketRepository_FindOne_Call) Run(run func(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions)) *MockUserGameBucketRepository_FindOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]*options.FindOneOptions)
		run(args[0].(context.Context), args[1].(bson.M), variadicArgs...)
	})
	return _c
}

func (_c *MockUserGameBucketRepository_FindOne_Call) Return(userGameBucket *models.UserGameBucket, err error) *MockUserGameBucketRepository_FindOne_Call {
	_c.Call.Return(userGameBucket, err)
	return _c
}

func (_c *MockUserGameBucketRepository_FindOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.UserGameBucket, error)) *MockUserGameBucketRepository_FindOne_Call {
	_c.Call.Return(run)
	return _c
}

// GetActiveUserGameBucket provides a mock function for the type MockUserGameBucketRepository
func (_mock *MockUserGameBucketRepository) GetActiveUserGameBucket(ctx context.Context, userID primitive.ObjectID) (*models.UserGameBucket, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetActiveUserGameBucket")
	}

	var r0 *models.UserGameBucket
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.UserGameBucket, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.UserGameBucket); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserGameBucket)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserGameBucketRepository_GetActiveUserGameBucket_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetActiveUserGameBucket'
type MockUserGameBucketRepository_GetActiveUserGameBucket_Call struct {
	*mock.Call
}

// GetActiveUserGameBucket is a helper method to define mock.On call
//   - ctx
//   - userID
func (_e *MockUserGameBucketRepository_Expecter) GetActiveUserGameBucket(ctx interface{}, userID interface{}) *MockUserGameBucketRepository_GetActiveUserGameBucket_Call {
	return &MockUserGameBucketRepository_GetActiveUserGameBucket_Call{Call: _e.mock.On("GetActiveUserGameBucket", ctx, userID)}
}

func (_c *MockUserGameBucketRepository_GetActiveUserGameBucket_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockUserGameBucketRepository_GetActiveUserGameBucket_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserGameBucketRepository_GetActiveUserGameBucket_Call) Return(userGameBucket *models.UserGameBucket, err error) *MockUserGameBucketRepository_GetActiveUserGameBucket_Call {
	_c.Call.Return(userGameBucket, err)
	return _c
}

func (_c *MockUserGameBucketRepository_GetActiveUserGameBucket_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) (*models.UserGameBucket, error)) *MockUserGameBucketRepository_GetActiveUserGameBucket_Call {
	_c.Call.Return(run)
	return _c
}

// GetGamesPaginated provides a mock function for the type MockUserGameBucketRepository
func (_mock *MockUserGameBucketRepository) GetGamesPaginated(ctx context.Context, userID primitive.ObjectID, page int, pageSize int) ([]primitive.ObjectID, error) {
	ret := _mock.Called(ctx, userID, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetGamesPaginated")
	}

	var r0 []primitive.ObjectID
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) ([]primitive.ObjectID, error)); ok {
		return returnFunc(ctx, userID, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) []primitive.ObjectID); ok {
		r0 = returnFunc(ctx, userID, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]primitive.ObjectID)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int, int) error); ok {
		r1 = returnFunc(ctx, userID, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserGameBucketRepository_GetGamesPaginated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGamesPaginated'
type MockUserGameBucketRepository_GetGamesPaginated_Call struct {
	*mock.Call
}

// GetGamesPaginated is a helper method to define mock.On call
//   - ctx
//   - userID
//   - page
//   - pageSize
func (_e *MockUserGameBucketRepository_Expecter) GetGamesPaginated(ctx interface{}, userID interface{}, page interface{}, pageSize interface{}) *MockUserGameBucketRepository_GetGamesPaginated_Call {
	return &MockUserGameBucketRepository_GetGamesPaginated_Call{Call: _e.mock.On("GetGamesPaginated", ctx, userID, page, pageSize)}
}

func (_c *MockUserGameBucketRepository_GetGamesPaginated_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, page int, pageSize int)) *MockUserGameBucketRepository_GetGamesPaginated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int), args[3].(int))
	})
	return _c
}

func (_c *MockUserGameBucketRepository_GetGamesPaginated_Call) Return(objectIDs []primitive.ObjectID, err error) *MockUserGameBucketRepository_GetGamesPaginated_Call {
	_c.Call.Return(objectIDs, err)
	return _c
}

func (_c *MockUserGameBucketRepository_GetGamesPaginated_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, page int, pageSize int) ([]primitive.ObjectID, error)) *MockUserGameBucketRepository_GetGamesPaginated_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserGameBucket provides a mock function for the type MockUserGameBucketRepository
func (_mock *MockUserGameBucketRepository) GetUserGameBucket(ctx context.Context, userID primitive.ObjectID, bucketNum int) (*models.UserGameBucket, error) {
	ret := _mock.Called(ctx, userID, bucketNum)

	if len(ret) == 0 {
		panic("no return value specified for GetUserGameBucket")
	}

	var r0 *models.UserGameBucket
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) (*models.UserGameBucket, error)); ok {
		return returnFunc(ctx, userID, bucketNum)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) *models.UserGameBucket); ok {
		r0 = returnFunc(ctx, userID, bucketNum)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserGameBucket)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int) error); ok {
		r1 = returnFunc(ctx, userID, bucketNum)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserGameBucketRepository_GetUserGameBucket_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserGameBucket'
type MockUserGameBucketRepository_GetUserGameBucket_Call struct {
	*mock.Call
}

// GetUserGameBucket is a helper method to define mock.On call
//   - ctx
//   - userID
//   - bucketNum
func (_e *MockUserGameBucketRepository_Expecter) GetUserGameBucket(ctx interface{}, userID interface{}, bucketNum interface{}) *MockUserGameBucketRepository_GetUserGameBucket_Call {
	return &MockUserGameBucketRepository_GetUserGameBucket_Call{Call: _e.mock.On("GetUserGameBucket", ctx, userID, bucketNum)}
}

func (_c *MockUserGameBucketRepository_GetUserGameBucket_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, bucketNum int)) *MockUserGameBucketRepository_GetUserGameBucket_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int))
	})
	return _c
}

func (_c *MockUserGameBucketRepository_GetUserGameBucket_Call) Return(userGameBucket *models.UserGameBucket, err error) *MockUserGameBucketRepository_GetUserGameBucket_Call {
	_c.Call.Return(userGameBucket, err)
	return _c
}

func (_c *MockUserGameBucketRepository_GetUserGameBucket_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, bucketNum int) (*models.UserGameBucket, error)) *MockUserGameBucketRepository_GetUserGameBucket_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserGameBuckets provides a mock function for the type MockUserGameBucketRepository
func (_mock *MockUserGameBucketRepository) GetUserGameBuckets(ctx context.Context, userID primitive.ObjectID, opts ...*options.FindOptions) ([]*models.UserGameBucket, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, userID, opts)
	} else {
		tmpRet = _mock.Called(ctx, userID)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for GetUserGameBuckets")
	}

	var r0 []*models.UserGameBucket
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, ...*options.FindOptions) ([]*models.UserGameBucket, error)); ok {
		return returnFunc(ctx, userID, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, ...*options.FindOptions) []*models.UserGameBucket); ok {
		r0 = returnFunc(ctx, userID, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserGameBucket)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, ...*options.FindOptions) error); ok {
		r1 = returnFunc(ctx, userID, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserGameBucketRepository_GetUserGameBuckets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserGameBuckets'
type MockUserGameBucketRepository_GetUserGameBuckets_Call struct {
	*mock.Call
}

// GetUserGameBuckets is a helper method to define mock.On call
//   - ctx
//   - userID
//   - opts
func (_e *MockUserGameBucketRepository_Expecter) GetUserGameBuckets(ctx interface{}, userID interface{}, opts ...interface{}) *MockUserGameBucketRepository_GetUserGameBuckets_Call {
	return &MockUserGameBucketRepository_GetUserGameBuckets_Call{Call: _e.mock.On("GetUserGameBuckets",
		append([]interface{}{ctx, userID}, opts...)...)}
}

func (_c *MockUserGameBucketRepository_GetUserGameBuckets_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, opts ...*options.FindOptions)) *MockUserGameBucketRepository_GetUserGameBuckets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]*options.FindOptions)
		run(args[0].(context.Context), args[1].(primitive.ObjectID), variadicArgs...)
	})
	return _c
}

func (_c *MockUserGameBucketRepository_GetUserGameBuckets_Call) Return(userGameBuckets []*models.UserGameBucket, err error) *MockUserGameBucketRepository_GetUserGameBuckets_Call {
	_c.Call.Return(userGameBuckets, err)
	return _c
}

func (_c *MockUserGameBucketRepository_GetUserGameBuckets_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, opts ...*options.FindOptions) ([]*models.UserGameBucket, error)) *MockUserGameBucketRepository_GetUserGameBuckets_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function for the type MockUserGameBucketRepository
func (_mock *MockUserGameBucketRepository) Update(ctx context.Context, bucket *models.UserGameBucket) error {
	ret := _mock.Called(ctx, bucket)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserGameBucket) error); ok {
		r0 = returnFunc(ctx, bucket)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserGameBucketRepository_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockUserGameBucketRepository_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx
//   - bucket
func (_e *MockUserGameBucketRepository_Expecter) Update(ctx interface{}, bucket interface{}) *MockUserGameBucketRepository_Update_Call {
	return &MockUserGameBucketRepository_Update_Call{Call: _e.mock.On("Update", ctx, bucket)}
}

func (_c *MockUserGameBucketRepository_Update_Call) Run(run func(ctx context.Context, bucket *models.UserGameBucket)) *MockUserGameBucketRepository_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.UserGameBucket))
	})
	return _c
}

func (_c *MockUserGameBucketRepository_Update_Call) Return(err error) *MockUserGameBucketRepository_Update_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserGameBucketRepository_Update_Call) RunAndReturn(run func(ctx context.Context, bucket *models.UserGameBucket) error) *MockUserGameBucketRepository_Update_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function for the type MockUserGameBucketRepository
func (_mock *MockUserGameBucketRepository) UpdateOne(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions) error {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, update, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter, update)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, bson.M, ...*options.UpdateOptions) error); ok {
		r0 = returnFunc(ctx, filter, update, opts...)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserGameBucketRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockUserGameBucketRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx
//   - filter
//   - update
//   - opts
func (_e *MockUserGameBucketRepository_Expecter) UpdateOne(ctx interface{}, filter interface{}, update interface{}, opts ...interface{}) *MockUserGameBucketRepository_UpdateOne_Call {
	return &MockUserGameBucketRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne",
		append([]interface{}{ctx, filter, update}, opts...)...)}
}

func (_c *MockUserGameBucketRepository_UpdateOne_Call) Run(run func(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions)) *MockUserGameBucketRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[3].([]*options.UpdateOptions)
		run(args[0].(context.Context), args[1].(bson.M), args[2].(bson.M), variadicArgs...)
	})
	return _c
}

func (_c *MockUserGameBucketRepository_UpdateOne_Call) Return(err error) *MockUserGameBucketRepository_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserGameBucketRepository_UpdateOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions) error) *MockUserGameBucketRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}
