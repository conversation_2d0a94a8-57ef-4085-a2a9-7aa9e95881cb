// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockUserRepository creates a new instance of MockUserRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserRepository {
	mock := &MockUserRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockUserRepository is an autogenerated mock type for the UserRepository type
type MockUserRepository struct {
	mock.Mock
}

type MockUserRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserRepository) EXPECT() *MockUserRepository_Expecter {
	return &MockUserRepository_Expecter{mock: &_m.Mock}
}

// Aggregate provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) Aggregate(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) ([]*models.User, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, pipeline, opts)
	} else {
		tmpRet = _mock.Called(ctx, pipeline)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Aggregate")
	}

	var r0 []*models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline, ...*options.AggregateOptions) ([]*models.User, error)); ok {
		return returnFunc(ctx, pipeline, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline, ...*options.AggregateOptions) []*models.User); ok {
		r0 = returnFunc(ctx, pipeline, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, mongo.Pipeline, ...*options.AggregateOptions) error); ok {
		r1 = returnFunc(ctx, pipeline, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_Aggregate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Aggregate'
type MockUserRepository_Aggregate_Call struct {
	*mock.Call
}

// Aggregate is a helper method to define mock.On call
//   - ctx
//   - pipeline
//   - opts
func (_e *MockUserRepository_Expecter) Aggregate(ctx interface{}, pipeline interface{}, opts ...interface{}) *MockUserRepository_Aggregate_Call {
	return &MockUserRepository_Aggregate_Call{Call: _e.mock.On("Aggregate",
		append([]interface{}{ctx, pipeline}, opts...)...)}
}

func (_c *MockUserRepository_Aggregate_Call) Run(run func(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions)) *MockUserRepository_Aggregate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]*options.AggregateOptions)
		run(args[0].(context.Context), args[1].(mongo.Pipeline), variadicArgs...)
	})
	return _c
}

func (_c *MockUserRepository_Aggregate_Call) Return(users []*models.User, err error) *MockUserRepository_Aggregate_Call {
	_c.Call.Return(users, err)
	return _c
}

func (_c *MockUserRepository_Aggregate_Call) RunAndReturn(run func(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) ([]*models.User, error)) *MockUserRepository_Aggregate_Call {
	_c.Call.Return(run)
	return _c
}

// AggregateProjected provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) AggregateProjected(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) (*mongo.Cursor, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, pipeline, opts)
	} else {
		tmpRet = _mock.Called(ctx, pipeline)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for AggregateProjected")
	}

	var r0 *mongo.Cursor
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline, ...*options.AggregateOptions) (*mongo.Cursor, error)); ok {
		return returnFunc(ctx, pipeline, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline, ...*options.AggregateOptions) *mongo.Cursor); ok {
		r0 = returnFunc(ctx, pipeline, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.Cursor)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, mongo.Pipeline, ...*options.AggregateOptions) error); ok {
		r1 = returnFunc(ctx, pipeline, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_AggregateProjected_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AggregateProjected'
type MockUserRepository_AggregateProjected_Call struct {
	*mock.Call
}

// AggregateProjected is a helper method to define mock.On call
//   - ctx
//   - pipeline
//   - opts
func (_e *MockUserRepository_Expecter) AggregateProjected(ctx interface{}, pipeline interface{}, opts ...interface{}) *MockUserRepository_AggregateProjected_Call {
	return &MockUserRepository_AggregateProjected_Call{Call: _e.mock.On("AggregateProjected",
		append([]interface{}{ctx, pipeline}, opts...)...)}
}

func (_c *MockUserRepository_AggregateProjected_Call) Run(run func(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions)) *MockUserRepository_AggregateProjected_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]*options.AggregateOptions)
		run(args[0].(context.Context), args[1].(mongo.Pipeline), variadicArgs...)
	})
	return _c
}

func (_c *MockUserRepository_AggregateProjected_Call) Return(cursor *mongo.Cursor, err error) *MockUserRepository_AggregateProjected_Call {
	_c.Call.Return(cursor, err)
	return _c
}

func (_c *MockUserRepository_AggregateProjected_Call) RunAndReturn(run func(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) (*mongo.Cursor, error)) *MockUserRepository_AggregateProjected_Call {
	_c.Call.Return(run)
	return _c
}

// BulkWrite provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) BulkWrite(ctx context.Context, models1 []mongo.WriteModel, opts ...*options.BulkWriteOptions) (*mongo.BulkWriteResult, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, models1, opts)
	} else {
		tmpRet = _mock.Called(ctx, models1)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for BulkWrite")
	}

	var r0 *mongo.BulkWriteResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []mongo.WriteModel, ...*options.BulkWriteOptions) (*mongo.BulkWriteResult, error)); ok {
		return returnFunc(ctx, models1, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []mongo.WriteModel, ...*options.BulkWriteOptions) *mongo.BulkWriteResult); ok {
		r0 = returnFunc(ctx, models1, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.BulkWriteResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []mongo.WriteModel, ...*options.BulkWriteOptions) error); ok {
		r1 = returnFunc(ctx, models1, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_BulkWrite_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BulkWrite'
type MockUserRepository_BulkWrite_Call struct {
	*mock.Call
}

// BulkWrite is a helper method to define mock.On call
//   - ctx
//   - models1
//   - opts
func (_e *MockUserRepository_Expecter) BulkWrite(ctx interface{}, models1 interface{}, opts ...interface{}) *MockUserRepository_BulkWrite_Call {
	return &MockUserRepository_BulkWrite_Call{Call: _e.mock.On("BulkWrite",
		append([]interface{}{ctx, models1}, opts...)...)}
}

func (_c *MockUserRepository_BulkWrite_Call) Run(run func(ctx context.Context, models1 []mongo.WriteModel, opts ...*options.BulkWriteOptions)) *MockUserRepository_BulkWrite_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]*options.BulkWriteOptions)
		run(args[0].(context.Context), args[1].([]mongo.WriteModel), variadicArgs...)
	})
	return _c
}

func (_c *MockUserRepository_BulkWrite_Call) Return(bulkWriteResult *mongo.BulkWriteResult, err error) *MockUserRepository_BulkWrite_Call {
	_c.Call.Return(bulkWriteResult, err)
	return _c
}

func (_c *MockUserRepository_BulkWrite_Call) RunAndReturn(run func(ctx context.Context, models1 []mongo.WriteModel, opts ...*options.BulkWriteOptions) (*mongo.BulkWriteResult, error)) *MockUserRepository_BulkWrite_Call {
	_c.Call.Return(run)
	return _c
}

// Count provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) Count(ctx context.Context, filter bson.M) (int64, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for Count")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) (int64, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) int64); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M) error); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_Count_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Count'
type MockUserRepository_Count_Call struct {
	*mock.Call
}

// Count is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *MockUserRepository_Expecter) Count(ctx interface{}, filter interface{}) *MockUserRepository_Count_Call {
	return &MockUserRepository_Count_Call{Call: _e.mock.On("Count", ctx, filter)}
}

func (_c *MockUserRepository_Count_Call) Run(run func(ctx context.Context, filter bson.M)) *MockUserRepository_Count_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M))
	})
	return _c
}

func (_c *MockUserRepository_Count_Call) Return(n int64, err error) *MockUserRepository_Count_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockUserRepository_Count_Call) RunAndReturn(run func(ctx context.Context, filter bson.M) (int64, error)) *MockUserRepository_Count_Call {
	_c.Call.Return(run)
	return _c
}

// CountDocuments provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) CountDocuments(ctx context.Context, filter bson.M) (int64, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for CountDocuments")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) (int64, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) int64); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M) error); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_CountDocuments_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountDocuments'
type MockUserRepository_CountDocuments_Call struct {
	*mock.Call
}

// CountDocuments is a helper method to define mock.On call
//   - ctx
//   - filter
func (_e *MockUserRepository_Expecter) CountDocuments(ctx interface{}, filter interface{}) *MockUserRepository_CountDocuments_Call {
	return &MockUserRepository_CountDocuments_Call{Call: _e.mock.On("CountDocuments", ctx, filter)}
}

func (_c *MockUserRepository_CountDocuments_Call) Run(run func(ctx context.Context, filter bson.M)) *MockUserRepository_CountDocuments_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bson.M))
	})
	return _c
}

func (_c *MockUserRepository_CountDocuments_Call) Return(n int64, err error) *MockUserRepository_CountDocuments_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockUserRepository_CountDocuments_Call) RunAndReturn(run func(ctx context.Context, filter bson.M) (int64, error)) *MockUserRepository_CountDocuments_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) Create(ctx context.Context, user *models.User) error {
	ret := _mock.Called(ctx, user)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.User) error); ok {
		r0 = returnFunc(ctx, user)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockUserRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - user
func (_e *MockUserRepository_Expecter) Create(ctx interface{}, user interface{}) *MockUserRepository_Create_Call {
	return &MockUserRepository_Create_Call{Call: _e.mock.On("Create", ctx, user)}
}

func (_c *MockUserRepository_Create_Call) Run(run func(ctx context.Context, user *models.User)) *MockUserRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.User))
	})
	return _c
}

func (_c *MockUserRepository_Create_Call) Return(err error) *MockUserRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserRepository_Create_Call) RunAndReturn(run func(ctx context.Context, user *models.User) error) *MockUserRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// DecrementUserFollowersCount provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) DecrementUserFollowersCount(ctx context.Context, userId primitive.ObjectID) error {
	ret := _mock.Called(ctx, userId)

	if len(ret) == 0 {
		panic("no return value specified for DecrementUserFollowersCount")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, userId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserRepository_DecrementUserFollowersCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DecrementUserFollowersCount'
type MockUserRepository_DecrementUserFollowersCount_Call struct {
	*mock.Call
}

// DecrementUserFollowersCount is a helper method to define mock.On call
//   - ctx
//   - userId
func (_e *MockUserRepository_Expecter) DecrementUserFollowersCount(ctx interface{}, userId interface{}) *MockUserRepository_DecrementUserFollowersCount_Call {
	return &MockUserRepository_DecrementUserFollowersCount_Call{Call: _e.mock.On("DecrementUserFollowersCount", ctx, userId)}
}

func (_c *MockUserRepository_DecrementUserFollowersCount_Call) Run(run func(ctx context.Context, userId primitive.ObjectID)) *MockUserRepository_DecrementUserFollowersCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserRepository_DecrementUserFollowersCount_Call) Return(err error) *MockUserRepository_DecrementUserFollowersCount_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserRepository_DecrementUserFollowersCount_Call) RunAndReturn(run func(ctx context.Context, userId primitive.ObjectID) error) *MockUserRepository_DecrementUserFollowersCount_Call {
	_c.Call.Return(run)
	return _c
}

// DecrementUserFollowingsCount provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) DecrementUserFollowingsCount(ctx context.Context, userId primitive.ObjectID) error {
	ret := _mock.Called(ctx, userId)

	if len(ret) == 0 {
		panic("no return value specified for DecrementUserFollowingsCount")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, userId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserRepository_DecrementUserFollowingsCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DecrementUserFollowingsCount'
type MockUserRepository_DecrementUserFollowingsCount_Call struct {
	*mock.Call
}

// DecrementUserFollowingsCount is a helper method to define mock.On call
//   - ctx
//   - userId
func (_e *MockUserRepository_Expecter) DecrementUserFollowingsCount(ctx interface{}, userId interface{}) *MockUserRepository_DecrementUserFollowingsCount_Call {
	return &MockUserRepository_DecrementUserFollowingsCount_Call{Call: _e.mock.On("DecrementUserFollowingsCount", ctx, userId)}
}

func (_c *MockUserRepository_DecrementUserFollowingsCount_Call) Run(run func(ctx context.Context, userId primitive.ObjectID)) *MockUserRepository_DecrementUserFollowingsCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserRepository_DecrementUserFollowingsCount_Call) Return(err error) *MockUserRepository_DecrementUserFollowingsCount_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserRepository_DecrementUserFollowingsCount_Call) RunAndReturn(run func(ctx context.Context, userId primitive.ObjectID) error) *MockUserRepository_DecrementUserFollowingsCount_Call {
	_c.Call.Return(run)
	return _c
}

// DecrementUserFriendsCount provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) DecrementUserFriendsCount(ctx context.Context, userId primitive.ObjectID) error {
	ret := _mock.Called(ctx, userId)

	if len(ret) == 0 {
		panic("no return value specified for DecrementUserFriendsCount")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, userId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserRepository_DecrementUserFriendsCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DecrementUserFriendsCount'
type MockUserRepository_DecrementUserFriendsCount_Call struct {
	*mock.Call
}

// DecrementUserFriendsCount is a helper method to define mock.On call
//   - ctx
//   - userId
func (_e *MockUserRepository_Expecter) DecrementUserFriendsCount(ctx interface{}, userId interface{}) *MockUserRepository_DecrementUserFriendsCount_Call {
	return &MockUserRepository_DecrementUserFriendsCount_Call{Call: _e.mock.On("DecrementUserFriendsCount", ctx, userId)}
}

func (_c *MockUserRepository_DecrementUserFriendsCount_Call) Run(run func(ctx context.Context, userId primitive.ObjectID)) *MockUserRepository_DecrementUserFriendsCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserRepository_DecrementUserFriendsCount_Call) Return(err error) *MockUserRepository_DecrementUserFriendsCount_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserRepository_DecrementUserFriendsCount_Call) RunAndReturn(run func(ctx context.Context, userId primitive.ObjectID) error) *MockUserRepository_DecrementUserFriendsCount_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserRepository_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockUserRepository_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockUserRepository_Expecter) Delete(ctx interface{}, id interface{}) *MockUserRepository_Delete_Call {
	return &MockUserRepository_Delete_Call{Call: _e.mock.On("Delete", ctx, id)}
}

func (_c *MockUserRepository_Delete_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockUserRepository_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserRepository_Delete_Call) Return(err error) *MockUserRepository_Delete_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserRepository_Delete_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) error) *MockUserRepository_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUser provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) DeleteUser(userId primitive.ObjectID) error {
	ret := _mock.Called(userId)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUser")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(primitive.ObjectID) error); ok {
		r0 = returnFunc(userId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserRepository_DeleteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUser'
type MockUserRepository_DeleteUser_Call struct {
	*mock.Call
}

// DeleteUser is a helper method to define mock.On call
//   - userId
func (_e *MockUserRepository_Expecter) DeleteUser(userId interface{}) *MockUserRepository_DeleteUser_Call {
	return &MockUserRepository_DeleteUser_Call{Call: _e.mock.On("DeleteUser", userId)}
}

func (_c *MockUserRepository_DeleteUser_Call) Run(run func(userId primitive.ObjectID)) *MockUserRepository_DeleteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserRepository_DeleteUser_Call) Return(err error) *MockUserRepository_DeleteUser_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserRepository_DeleteUser_Call) RunAndReturn(run func(userId primitive.ObjectID) error) *MockUserRepository_DeleteUser_Call {
	_c.Call.Return(run)
	return _c
}

// EstimatedDocumentCount provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) EstimatedDocumentCount(ctx context.Context, opts ...*options.EstimatedDocumentCountOptions) (int64, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, opts)
	} else {
		tmpRet = _mock.Called(ctx)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for EstimatedDocumentCount")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, ...*options.EstimatedDocumentCountOptions) (int64, error)); ok {
		return returnFunc(ctx, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, ...*options.EstimatedDocumentCountOptions) int64); ok {
		r0 = returnFunc(ctx, opts...)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, ...*options.EstimatedDocumentCountOptions) error); ok {
		r1 = returnFunc(ctx, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_EstimatedDocumentCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EstimatedDocumentCount'
type MockUserRepository_EstimatedDocumentCount_Call struct {
	*mock.Call
}

// EstimatedDocumentCount is a helper method to define mock.On call
//   - ctx
//   - opts
func (_e *MockUserRepository_Expecter) EstimatedDocumentCount(ctx interface{}, opts ...interface{}) *MockUserRepository_EstimatedDocumentCount_Call {
	return &MockUserRepository_EstimatedDocumentCount_Call{Call: _e.mock.On("EstimatedDocumentCount",
		append([]interface{}{ctx}, opts...)...)}
}

func (_c *MockUserRepository_EstimatedDocumentCount_Call) Run(run func(ctx context.Context, opts ...*options.EstimatedDocumentCountOptions)) *MockUserRepository_EstimatedDocumentCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[1].([]*options.EstimatedDocumentCountOptions)
		run(args[0].(context.Context), variadicArgs...)
	})
	return _c
}

func (_c *MockUserRepository_EstimatedDocumentCount_Call) Return(n int64, err error) *MockUserRepository_EstimatedDocumentCount_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockUserRepository_EstimatedDocumentCount_Call) RunAndReturn(run func(ctx context.Context, opts ...*options.EstimatedDocumentCountOptions) (int64, error)) *MockUserRepository_EstimatedDocumentCount_Call {
	_c.Call.Return(run)
	return _c
}

// Find provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.User, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Find")
	}

	var r0 []*models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) ([]*models.User, error)); ok {
		return returnFunc(ctx, filter, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) []*models.User); ok {
		r0 = returnFunc(ctx, filter, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, ...*options.FindOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_Find_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Find'
type MockUserRepository_Find_Call struct {
	*mock.Call
}

// Find is a helper method to define mock.On call
//   - ctx
//   - filter
//   - opts
func (_e *MockUserRepository_Expecter) Find(ctx interface{}, filter interface{}, opts ...interface{}) *MockUserRepository_Find_Call {
	return &MockUserRepository_Find_Call{Call: _e.mock.On("Find",
		append([]interface{}{ctx, filter}, opts...)...)}
}

func (_c *MockUserRepository_Find_Call) Run(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions)) *MockUserRepository_Find_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]*options.FindOptions)
		run(args[0].(context.Context), args[1].(bson.M), variadicArgs...)
	})
	return _c
}

func (_c *MockUserRepository_Find_Call) Return(users []*models.User, err error) *MockUserRepository_Find_Call {
	_c.Call.Return(users, err)
	return _c
}

func (_c *MockUserRepository_Find_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.User, error)) *MockUserRepository_Find_Call {
	_c.Call.Return(run)
	return _c
}

// FindOne provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.User, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for FindOne")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOneOptions) (*models.User, error)); ok {
		return returnFunc(ctx, filter, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOneOptions) *models.User); ok {
		r0 = returnFunc(ctx, filter, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, ...*options.FindOneOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_FindOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOne'
type MockUserRepository_FindOne_Call struct {
	*mock.Call
}

// FindOne is a helper method to define mock.On call
//   - ctx
//   - filter
//   - opts
func (_e *MockUserRepository_Expecter) FindOne(ctx interface{}, filter interface{}, opts ...interface{}) *MockUserRepository_FindOne_Call {
	return &MockUserRepository_FindOne_Call{Call: _e.mock.On("FindOne",
		append([]interface{}{ctx, filter}, opts...)...)}
}

func (_c *MockUserRepository_FindOne_Call) Run(run func(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions)) *MockUserRepository_FindOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]*options.FindOneOptions)
		run(args[0].(context.Context), args[1].(bson.M), variadicArgs...)
	})
	return _c
}

func (_c *MockUserRepository_FindOne_Call) Return(user *models.User, err error) *MockUserRepository_FindOne_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockUserRepository_FindOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.User, error)) *MockUserRepository_FindOne_Call {
	_c.Call.Return(run)
	return _c
}

// GetByEmail provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) GetByEmail(ctx context.Context, email string) (*models.User, error) {
	ret := _mock.Called(ctx, email)

	if len(ret) == 0 {
		panic("no return value specified for GetByEmail")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*models.User, error)); ok {
		return returnFunc(ctx, email)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *models.User); ok {
		r0 = returnFunc(ctx, email)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, email)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_GetByEmail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByEmail'
type MockUserRepository_GetByEmail_Call struct {
	*mock.Call
}

// GetByEmail is a helper method to define mock.On call
//   - ctx
//   - email
func (_e *MockUserRepository_Expecter) GetByEmail(ctx interface{}, email interface{}) *MockUserRepository_GetByEmail_Call {
	return &MockUserRepository_GetByEmail_Call{Call: _e.mock.On("GetByEmail", ctx, email)}
}

func (_c *MockUserRepository_GetByEmail_Call) Run(run func(ctx context.Context, email string)) *MockUserRepository_GetByEmail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockUserRepository_GetByEmail_Call) Return(user *models.User, err error) *MockUserRepository_GetByEmail_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockUserRepository_GetByEmail_Call) RunAndReturn(run func(ctx context.Context, email string) (*models.User, error)) *MockUserRepository_GetByEmail_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) GetByID(ctx context.Context, id primitive.ObjectID) (*models.User, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.User, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.User); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type MockUserRepository_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockUserRepository_Expecter) GetByID(ctx interface{}, id interface{}) *MockUserRepository_GetByID_Call {
	return &MockUserRepository_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *MockUserRepository_GetByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockUserRepository_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserRepository_GetByID_Call) Return(user *models.User, err error) *MockUserRepository_GetByID_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockUserRepository_GetByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.User, error)) *MockUserRepository_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByUsername provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) GetByUsername(ctx context.Context, username string) (*models.User, error) {
	ret := _mock.Called(ctx, username)

	if len(ret) == 0 {
		panic("no return value specified for GetByUsername")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*models.User, error)); ok {
		return returnFunc(ctx, username)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *models.User); ok {
		r0 = returnFunc(ctx, username)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, username)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_GetByUsername_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByUsername'
type MockUserRepository_GetByUsername_Call struct {
	*mock.Call
}

// GetByUsername is a helper method to define mock.On call
//   - ctx
//   - username
func (_e *MockUserRepository_Expecter) GetByUsername(ctx interface{}, username interface{}) *MockUserRepository_GetByUsername_Call {
	return &MockUserRepository_GetByUsername_Call{Call: _e.mock.On("GetByUsername", ctx, username)}
}

func (_c *MockUserRepository_GetByUsername_Call) Run(run func(ctx context.Context, username string)) *MockUserRepository_GetByUsername_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockUserRepository_GetByUsername_Call) Return(user *models.User, err error) *MockUserRepository_GetByUsername_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockUserRepository_GetByUsername_Call) RunAndReturn(run func(ctx context.Context, username string) (*models.User, error)) *MockUserRepository_GetByUsername_Call {
	_c.Call.Return(run)
	return _c
}

// GetFriendsLeaderboardByRatingType provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) GetFriendsLeaderboardByRatingType(ctx context.Context, userID primitive.ObjectID, page int64, pageSize int64, ratingType string) (*models.UserLeaderboardPage, error) {
	ret := _mock.Called(ctx, userID, page, pageSize, ratingType)

	if len(ret) == 0 {
		panic("no return value specified for GetFriendsLeaderboardByRatingType")
	}

	var r0 *models.UserLeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int64, int64, string) (*models.UserLeaderboardPage, error)); ok {
		return returnFunc(ctx, userID, page, pageSize, ratingType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int64, int64, string) *models.UserLeaderboardPage); ok {
		r0 = returnFunc(ctx, userID, page, pageSize, ratingType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserLeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int64, int64, string) error); ok {
		r1 = returnFunc(ctx, userID, page, pageSize, ratingType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_GetFriendsLeaderboardByRatingType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFriendsLeaderboardByRatingType'
type MockUserRepository_GetFriendsLeaderboardByRatingType_Call struct {
	*mock.Call
}

// GetFriendsLeaderboardByRatingType is a helper method to define mock.On call
//   - ctx
//   - userID
//   - page
//   - pageSize
//   - ratingType
func (_e *MockUserRepository_Expecter) GetFriendsLeaderboardByRatingType(ctx interface{}, userID interface{}, page interface{}, pageSize interface{}, ratingType interface{}) *MockUserRepository_GetFriendsLeaderboardByRatingType_Call {
	return &MockUserRepository_GetFriendsLeaderboardByRatingType_Call{Call: _e.mock.On("GetFriendsLeaderboardByRatingType", ctx, userID, page, pageSize, ratingType)}
}

func (_c *MockUserRepository_GetFriendsLeaderboardByRatingType_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, page int64, pageSize int64, ratingType string)) *MockUserRepository_GetFriendsLeaderboardByRatingType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int64), args[3].(int64), args[4].(string))
	})
	return _c
}

func (_c *MockUserRepository_GetFriendsLeaderboardByRatingType_Call) Return(userLeaderboardPage *models.UserLeaderboardPage, err error) *MockUserRepository_GetFriendsLeaderboardByRatingType_Call {
	_c.Call.Return(userLeaderboardPage, err)
	return _c
}

func (_c *MockUserRepository_GetFriendsLeaderboardByRatingType_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, page int64, pageSize int64, ratingType string) (*models.UserLeaderboardPage, error)) *MockUserRepository_GetFriendsLeaderboardByRatingType_Call {
	_c.Call.Return(run)
	return _c
}

// GetFriendsTopPlayers provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) GetFriendsTopPlayers(ctx context.Context, userID primitive.ObjectID, limit int) (*models.TopPlayersLeaderboard, error) {
	ret := _mock.Called(ctx, userID, limit)

	if len(ret) == 0 {
		panic("no return value specified for GetFriendsTopPlayers")
	}

	var r0 *models.TopPlayersLeaderboard
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) (*models.TopPlayersLeaderboard, error)); ok {
		return returnFunc(ctx, userID, limit)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) *models.TopPlayersLeaderboard); ok {
		r0 = returnFunc(ctx, userID, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.TopPlayersLeaderboard)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int) error); ok {
		r1 = returnFunc(ctx, userID, limit)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_GetFriendsTopPlayers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFriendsTopPlayers'
type MockUserRepository_GetFriendsTopPlayers_Call struct {
	*mock.Call
}

// GetFriendsTopPlayers is a helper method to define mock.On call
//   - ctx
//   - userID
//   - limit
func (_e *MockUserRepository_Expecter) GetFriendsTopPlayers(ctx interface{}, userID interface{}, limit interface{}) *MockUserRepository_GetFriendsTopPlayers_Call {
	return &MockUserRepository_GetFriendsTopPlayers_Call{Call: _e.mock.On("GetFriendsTopPlayers", ctx, userID, limit)}
}

func (_c *MockUserRepository_GetFriendsTopPlayers_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, limit int)) *MockUserRepository_GetFriendsTopPlayers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int))
	})
	return _c
}

func (_c *MockUserRepository_GetFriendsTopPlayers_Call) Return(topPlayersLeaderboard *models.TopPlayersLeaderboard, err error) *MockUserRepository_GetFriendsTopPlayers_Call {
	_c.Call.Return(topPlayersLeaderboard, err)
	return _c
}

func (_c *MockUserRepository_GetFriendsTopPlayers_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, limit int) (*models.TopPlayersLeaderboard, error)) *MockUserRepository_GetFriendsTopPlayers_Call {
	_c.Call.Return(run)
	return _c
}

// GetGlobalTopPlayers provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) GetGlobalTopPlayers(ctx context.Context, limit int) (*models.TopPlayersLeaderboard, error) {
	ret := _mock.Called(ctx, limit)

	if len(ret) == 0 {
		panic("no return value specified for GetGlobalTopPlayers")
	}

	var r0 *models.TopPlayersLeaderboard
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int) (*models.TopPlayersLeaderboard, error)); ok {
		return returnFunc(ctx, limit)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int) *models.TopPlayersLeaderboard); ok {
		r0 = returnFunc(ctx, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.TopPlayersLeaderboard)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int) error); ok {
		r1 = returnFunc(ctx, limit)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_GetGlobalTopPlayers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGlobalTopPlayers'
type MockUserRepository_GetGlobalTopPlayers_Call struct {
	*mock.Call
}

// GetGlobalTopPlayers is a helper method to define mock.On call
//   - ctx
//   - limit
func (_e *MockUserRepository_Expecter) GetGlobalTopPlayers(ctx interface{}, limit interface{}) *MockUserRepository_GetGlobalTopPlayers_Call {
	return &MockUserRepository_GetGlobalTopPlayers_Call{Call: _e.mock.On("GetGlobalTopPlayers", ctx, limit)}
}

func (_c *MockUserRepository_GetGlobalTopPlayers_Call) Run(run func(ctx context.Context, limit int)) *MockUserRepository_GetGlobalTopPlayers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int))
	})
	return _c
}

func (_c *MockUserRepository_GetGlobalTopPlayers_Call) Return(topPlayersLeaderboard *models.TopPlayersLeaderboard, err error) *MockUserRepository_GetGlobalTopPlayers_Call {
	_c.Call.Return(topPlayersLeaderboard, err)
	return _c
}

func (_c *MockUserRepository_GetGlobalTopPlayers_Call) RunAndReturn(run func(ctx context.Context, limit int) (*models.TopPlayersLeaderboard, error)) *MockUserRepository_GetGlobalTopPlayers_Call {
	_c.Call.Return(run)
	return _c
}

// GetPaginatedUsersByGlobalRank provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) GetPaginatedUsersByGlobalRank(ctx context.Context, page int64, pageSize int64) ([]*models.User, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetPaginatedUsersByGlobalRank")
	}

	var r0 []*models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int64, int64) ([]*models.User, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int64, int64) []*models.User); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int64, int64) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_GetPaginatedUsersByGlobalRank_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPaginatedUsersByGlobalRank'
type MockUserRepository_GetPaginatedUsersByGlobalRank_Call struct {
	*mock.Call
}

// GetPaginatedUsersByGlobalRank is a helper method to define mock.On call
//   - ctx
//   - page
//   - pageSize
func (_e *MockUserRepository_Expecter) GetPaginatedUsersByGlobalRank(ctx interface{}, page interface{}, pageSize interface{}) *MockUserRepository_GetPaginatedUsersByGlobalRank_Call {
	return &MockUserRepository_GetPaginatedUsersByGlobalRank_Call{Call: _e.mock.On("GetPaginatedUsersByGlobalRank", ctx, page, pageSize)}
}

func (_c *MockUserRepository_GetPaginatedUsersByGlobalRank_Call) Run(run func(ctx context.Context, page int64, pageSize int64)) *MockUserRepository_GetPaginatedUsersByGlobalRank_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int64))
	})
	return _c
}

func (_c *MockUserRepository_GetPaginatedUsersByGlobalRank_Call) Return(users []*models.User, err error) *MockUserRepository_GetPaginatedUsersByGlobalRank_Call {
	_c.Call.Return(users, err)
	return _c
}

func (_c *MockUserRepository_GetPaginatedUsersByGlobalRank_Call) RunAndReturn(run func(ctx context.Context, page int64, pageSize int64) ([]*models.User, error)) *MockUserRepository_GetPaginatedUsersByGlobalRank_Call {
	_c.Call.Return(run)
	return _c
}

// GetPaginatedUsersByRatingAndCreationTime provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) GetPaginatedUsersByRatingAndCreationTime(ctx context.Context, page int64, pageSize int64) ([]*models.User, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetPaginatedUsersByRatingAndCreationTime")
	}

	var r0 []*models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int64, int64) ([]*models.User, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int64, int64) []*models.User); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int64, int64) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_GetPaginatedUsersByRatingAndCreationTime_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPaginatedUsersByRatingAndCreationTime'
type MockUserRepository_GetPaginatedUsersByRatingAndCreationTime_Call struct {
	*mock.Call
}

// GetPaginatedUsersByRatingAndCreationTime is a helper method to define mock.On call
//   - ctx
//   - page
//   - pageSize
func (_e *MockUserRepository_Expecter) GetPaginatedUsersByRatingAndCreationTime(ctx interface{}, page interface{}, pageSize interface{}) *MockUserRepository_GetPaginatedUsersByRatingAndCreationTime_Call {
	return &MockUserRepository_GetPaginatedUsersByRatingAndCreationTime_Call{Call: _e.mock.On("GetPaginatedUsersByRatingAndCreationTime", ctx, page, pageSize)}
}

func (_c *MockUserRepository_GetPaginatedUsersByRatingAndCreationTime_Call) Run(run func(ctx context.Context, page int64, pageSize int64)) *MockUserRepository_GetPaginatedUsersByRatingAndCreationTime_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int64))
	})
	return _c
}

func (_c *MockUserRepository_GetPaginatedUsersByRatingAndCreationTime_Call) Return(users []*models.User, err error) *MockUserRepository_GetPaginatedUsersByRatingAndCreationTime_Call {
	_c.Call.Return(users, err)
	return _c
}

func (_c *MockUserRepository_GetPaginatedUsersByRatingAndCreationTime_Call) RunAndReturn(run func(ctx context.Context, page int64, pageSize int64) ([]*models.User, error)) *MockUserRepository_GetPaginatedUsersByRatingAndCreationTime_Call {
	_c.Call.Return(run)
	return _c
}

// GetPaginatedUsersByRatingType provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) GetPaginatedUsersByRatingType(ctx context.Context, page int64, pageSize int64, ratingType string) ([]*models.User, error) {
	ret := _mock.Called(ctx, page, pageSize, ratingType)

	if len(ret) == 0 {
		panic("no return value specified for GetPaginatedUsersByRatingType")
	}

	var r0 []*models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int64, int64, string) ([]*models.User, error)); ok {
		return returnFunc(ctx, page, pageSize, ratingType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int64, int64, string) []*models.User); ok {
		r0 = returnFunc(ctx, page, pageSize, ratingType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int64, int64, string) error); ok {
		r1 = returnFunc(ctx, page, pageSize, ratingType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_GetPaginatedUsersByRatingType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPaginatedUsersByRatingType'
type MockUserRepository_GetPaginatedUsersByRatingType_Call struct {
	*mock.Call
}

// GetPaginatedUsersByRatingType is a helper method to define mock.On call
//   - ctx
//   - page
//   - pageSize
//   - ratingType
func (_e *MockUserRepository_Expecter) GetPaginatedUsersByRatingType(ctx interface{}, page interface{}, pageSize interface{}, ratingType interface{}) *MockUserRepository_GetPaginatedUsersByRatingType_Call {
	return &MockUserRepository_GetPaginatedUsersByRatingType_Call{Call: _e.mock.On("GetPaginatedUsersByRatingType", ctx, page, pageSize, ratingType)}
}

func (_c *MockUserRepository_GetPaginatedUsersByRatingType_Call) Run(run func(ctx context.Context, page int64, pageSize int64, ratingType string)) *MockUserRepository_GetPaginatedUsersByRatingType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int64), args[3].(string))
	})
	return _c
}

func (_c *MockUserRepository_GetPaginatedUsersByRatingType_Call) Return(users []*models.User, err error) *MockUserRepository_GetPaginatedUsersByRatingType_Call {
	_c.Call.Return(users, err)
	return _c
}

func (_c *MockUserRepository_GetPaginatedUsersByRatingType_Call) RunAndReturn(run func(ctx context.Context, page int64, pageSize int64, ratingType string) ([]*models.User, error)) *MockUserRepository_GetPaginatedUsersByRatingType_Call {
	_c.Call.Return(run)
	return _c
}

// GetTopUsersByCountry provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) GetTopUsersByCountry(ctx context.Context, countryCode string, limit int64) ([]*models.User, error) {
	ret := _mock.Called(ctx, countryCode, limit)

	if len(ret) == 0 {
		panic("no return value specified for GetTopUsersByCountry")
	}

	var r0 []*models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64) ([]*models.User, error)); ok {
		return returnFunc(ctx, countryCode, limit)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64) []*models.User); ok {
		r0 = returnFunc(ctx, countryCode, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, int64) error); ok {
		r1 = returnFunc(ctx, countryCode, limit)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_GetTopUsersByCountry_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTopUsersByCountry'
type MockUserRepository_GetTopUsersByCountry_Call struct {
	*mock.Call
}

// GetTopUsersByCountry is a helper method to define mock.On call
//   - ctx
//   - countryCode
//   - limit
func (_e *MockUserRepository_Expecter) GetTopUsersByCountry(ctx interface{}, countryCode interface{}, limit interface{}) *MockUserRepository_GetTopUsersByCountry_Call {
	return &MockUserRepository_GetTopUsersByCountry_Call{Call: _e.mock.On("GetTopUsersByCountry", ctx, countryCode, limit)}
}

func (_c *MockUserRepository_GetTopUsersByCountry_Call) Run(run func(ctx context.Context, countryCode string, limit int64)) *MockUserRepository_GetTopUsersByCountry_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int64))
	})
	return _c
}

func (_c *MockUserRepository_GetTopUsersByCountry_Call) Return(users []*models.User, err error) *MockUserRepository_GetTopUsersByCountry_Call {
	_c.Call.Return(users, err)
	return _c
}

func (_c *MockUserRepository_GetTopUsersByCountry_Call) RunAndReturn(run func(ctx context.Context, countryCode string, limit int64) ([]*models.User, error)) *MockUserRepository_GetTopUsersByCountry_Call {
	_c.Call.Return(run)
	return _c
}

// GetTopUsersByRating provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) GetTopUsersByRating(ctx context.Context, limit int64) ([]*models.User, error) {
	ret := _mock.Called(ctx, limit)

	if len(ret) == 0 {
		panic("no return value specified for GetTopUsersByRating")
	}

	var r0 []*models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int64) ([]*models.User, error)); ok {
		return returnFunc(ctx, limit)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int64) []*models.User); ok {
		r0 = returnFunc(ctx, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = returnFunc(ctx, limit)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_GetTopUsersByRating_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTopUsersByRating'
type MockUserRepository_GetTopUsersByRating_Call struct {
	*mock.Call
}

// GetTopUsersByRating is a helper method to define mock.On call
//   - ctx
//   - limit
func (_e *MockUserRepository_Expecter) GetTopUsersByRating(ctx interface{}, limit interface{}) *MockUserRepository_GetTopUsersByRating_Call {
	return &MockUserRepository_GetTopUsersByRating_Call{Call: _e.mock.On("GetTopUsersByRating", ctx, limit)}
}

func (_c *MockUserRepository_GetTopUsersByRating_Call) Run(run func(ctx context.Context, limit int64)) *MockUserRepository_GetTopUsersByRating_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *MockUserRepository_GetTopUsersByRating_Call) Return(users []*models.User, err error) *MockUserRepository_GetTopUsersByRating_Call {
	_c.Call.Return(users, err)
	return _c
}

func (_c *MockUserRepository_GetTopUsersByRating_Call) RunAndReturn(run func(ctx context.Context, limit int64) ([]*models.User, error)) *MockUserRepository_GetTopUsersByRating_Call {
	_c.Call.Return(run)
	return _c
}

// GetTotalUsersCount provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) GetTotalUsersCount(ctx context.Context, isGuest bool, ratingType *string) (int64, error) {
	ret := _mock.Called(ctx, isGuest, ratingType)

	if len(ret) == 0 {
		panic("no return value specified for GetTotalUsersCount")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bool, *string) (int64, error)); ok {
		return returnFunc(ctx, isGuest, ratingType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bool, *string) int64); ok {
		r0 = returnFunc(ctx, isGuest, ratingType)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bool, *string) error); ok {
		r1 = returnFunc(ctx, isGuest, ratingType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_GetTotalUsersCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTotalUsersCount'
type MockUserRepository_GetTotalUsersCount_Call struct {
	*mock.Call
}

// GetTotalUsersCount is a helper method to define mock.On call
//   - ctx
//   - isGuest
//   - ratingType
func (_e *MockUserRepository_Expecter) GetTotalUsersCount(ctx interface{}, isGuest interface{}, ratingType interface{}) *MockUserRepository_GetTotalUsersCount_Call {
	return &MockUserRepository_GetTotalUsersCount_Call{Call: _e.mock.On("GetTotalUsersCount", ctx, isGuest, ratingType)}
}

func (_c *MockUserRepository_GetTotalUsersCount_Call) Run(run func(ctx context.Context, isGuest bool, ratingType *string)) *MockUserRepository_GetTotalUsersCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bool), args[2].(*string))
	})
	return _c
}

func (_c *MockUserRepository_GetTotalUsersCount_Call) Return(n int64, err error) *MockUserRepository_GetTotalUsersCount_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockUserRepository_GetTotalUsersCount_Call) RunAndReturn(run func(ctx context.Context, isGuest bool, ratingType *string) (int64, error)) *MockUserRepository_GetTotalUsersCount_Call {
	_c.Call.Return(run)
	return _c
}

// GetUsersByIDs provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) GetUsersByIDs(ctx context.Context, ids []primitive.ObjectID) ([]*models.User, error) {
	ret := _mock.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for GetUsersByIDs")
	}

	var r0 []*models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []primitive.ObjectID) ([]*models.User, error)); ok {
		return returnFunc(ctx, ids)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []primitive.ObjectID) []*models.User); ok {
		r0 = returnFunc(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_GetUsersByIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUsersByIDs'
type MockUserRepository_GetUsersByIDs_Call struct {
	*mock.Call
}

// GetUsersByIDs is a helper method to define mock.On call
//   - ctx
//   - ids
func (_e *MockUserRepository_Expecter) GetUsersByIDs(ctx interface{}, ids interface{}) *MockUserRepository_GetUsersByIDs_Call {
	return &MockUserRepository_GetUsersByIDs_Call{Call: _e.mock.On("GetUsersByIDs", ctx, ids)}
}

func (_c *MockUserRepository_GetUsersByIDs_Call) Run(run func(ctx context.Context, ids []primitive.ObjectID)) *MockUserRepository_GetUsersByIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserRepository_GetUsersByIDs_Call) Return(users []*models.User, err error) *MockUserRepository_GetUsersByIDs_Call {
	_c.Call.Return(users, err)
	return _c
}

func (_c *MockUserRepository_GetUsersByIDs_Call) RunAndReturn(run func(ctx context.Context, ids []primitive.ObjectID) ([]*models.User, error)) *MockUserRepository_GetUsersByIDs_Call {
	_c.Call.Return(run)
	return _c
}

// GetUsersRatingCount provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) GetUsersRatingCount(ctx context.Context, minRating int, maxRating int) ([]*models.UserRatingCount, error) {
	ret := _mock.Called(ctx, minRating, maxRating)

	if len(ret) == 0 {
		panic("no return value specified for GetUsersRatingCount")
	}

	var r0 []*models.UserRatingCount
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, int) ([]*models.UserRatingCount, error)); ok {
		return returnFunc(ctx, minRating, maxRating)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, int) []*models.UserRatingCount); ok {
		r0 = returnFunc(ctx, minRating, maxRating)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserRatingCount)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int, int) error); ok {
		r1 = returnFunc(ctx, minRating, maxRating)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_GetUsersRatingCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUsersRatingCount'
type MockUserRepository_GetUsersRatingCount_Call struct {
	*mock.Call
}

// GetUsersRatingCount is a helper method to define mock.On call
//   - ctx
//   - minRating
//   - maxRating
func (_e *MockUserRepository_Expecter) GetUsersRatingCount(ctx interface{}, minRating interface{}, maxRating interface{}) *MockUserRepository_GetUsersRatingCount_Call {
	return &MockUserRepository_GetUsersRatingCount_Call{Call: _e.mock.On("GetUsersRatingCount", ctx, minRating, maxRating)}
}

func (_c *MockUserRepository_GetUsersRatingCount_Call) Run(run func(ctx context.Context, minRating int, maxRating int)) *MockUserRepository_GetUsersRatingCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(int))
	})
	return _c
}

func (_c *MockUserRepository_GetUsersRatingCount_Call) Return(userRatingCounts []*models.UserRatingCount, err error) *MockUserRepository_GetUsersRatingCount_Call {
	_c.Call.Return(userRatingCounts, err)
	return _c
}

func (_c *MockUserRepository_GetUsersRatingCount_Call) RunAndReturn(run func(ctx context.Context, minRating int, maxRating int) ([]*models.UserRatingCount, error)) *MockUserRepository_GetUsersRatingCount_Call {
	_c.Call.Return(run)
	return _c
}

// IncrementUserFollowersCount provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) IncrementUserFollowersCount(ctx context.Context, userId primitive.ObjectID) error {
	ret := _mock.Called(ctx, userId)

	if len(ret) == 0 {
		panic("no return value specified for IncrementUserFollowersCount")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, userId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserRepository_IncrementUserFollowersCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IncrementUserFollowersCount'
type MockUserRepository_IncrementUserFollowersCount_Call struct {
	*mock.Call
}

// IncrementUserFollowersCount is a helper method to define mock.On call
//   - ctx
//   - userId
func (_e *MockUserRepository_Expecter) IncrementUserFollowersCount(ctx interface{}, userId interface{}) *MockUserRepository_IncrementUserFollowersCount_Call {
	return &MockUserRepository_IncrementUserFollowersCount_Call{Call: _e.mock.On("IncrementUserFollowersCount", ctx, userId)}
}

func (_c *MockUserRepository_IncrementUserFollowersCount_Call) Run(run func(ctx context.Context, userId primitive.ObjectID)) *MockUserRepository_IncrementUserFollowersCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserRepository_IncrementUserFollowersCount_Call) Return(err error) *MockUserRepository_IncrementUserFollowersCount_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserRepository_IncrementUserFollowersCount_Call) RunAndReturn(run func(ctx context.Context, userId primitive.ObjectID) error) *MockUserRepository_IncrementUserFollowersCount_Call {
	_c.Call.Return(run)
	return _c
}

// IncrementUserFollowingsCount provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) IncrementUserFollowingsCount(ctx context.Context, userId primitive.ObjectID) error {
	ret := _mock.Called(ctx, userId)

	if len(ret) == 0 {
		panic("no return value specified for IncrementUserFollowingsCount")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, userId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserRepository_IncrementUserFollowingsCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IncrementUserFollowingsCount'
type MockUserRepository_IncrementUserFollowingsCount_Call struct {
	*mock.Call
}

// IncrementUserFollowingsCount is a helper method to define mock.On call
//   - ctx
//   - userId
func (_e *MockUserRepository_Expecter) IncrementUserFollowingsCount(ctx interface{}, userId interface{}) *MockUserRepository_IncrementUserFollowingsCount_Call {
	return &MockUserRepository_IncrementUserFollowingsCount_Call{Call: _e.mock.On("IncrementUserFollowingsCount", ctx, userId)}
}

func (_c *MockUserRepository_IncrementUserFollowingsCount_Call) Run(run func(ctx context.Context, userId primitive.ObjectID)) *MockUserRepository_IncrementUserFollowingsCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserRepository_IncrementUserFollowingsCount_Call) Return(err error) *MockUserRepository_IncrementUserFollowingsCount_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserRepository_IncrementUserFollowingsCount_Call) RunAndReturn(run func(ctx context.Context, userId primitive.ObjectID) error) *MockUserRepository_IncrementUserFollowingsCount_Call {
	_c.Call.Return(run)
	return _c
}

// IncrementUserFriendsCount provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) IncrementUserFriendsCount(ctx context.Context, userId primitive.ObjectID) error {
	ret := _mock.Called(ctx, userId)

	if len(ret) == 0 {
		panic("no return value specified for IncrementUserFriendsCount")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, userId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserRepository_IncrementUserFriendsCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IncrementUserFriendsCount'
type MockUserRepository_IncrementUserFriendsCount_Call struct {
	*mock.Call
}

// IncrementUserFriendsCount is a helper method to define mock.On call
//   - ctx
//   - userId
func (_e *MockUserRepository_Expecter) IncrementUserFriendsCount(ctx interface{}, userId interface{}) *MockUserRepository_IncrementUserFriendsCount_Call {
	return &MockUserRepository_IncrementUserFriendsCount_Call{Call: _e.mock.On("IncrementUserFriendsCount", ctx, userId)}
}

func (_c *MockUserRepository_IncrementUserFriendsCount_Call) Run(run func(ctx context.Context, userId primitive.ObjectID)) *MockUserRepository_IncrementUserFriendsCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserRepository_IncrementUserFriendsCount_Call) Return(err error) *MockUserRepository_IncrementUserFriendsCount_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserRepository_IncrementUserFriendsCount_Call) RunAndReturn(run func(ctx context.Context, userId primitive.ObjectID) error) *MockUserRepository_IncrementUserFriendsCount_Call {
	_c.Call.Return(run)
	return _c
}

// SearchByName provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) SearchByName(ctx context.Context, name string, limit int64) ([]*models.User, error) {
	ret := _mock.Called(ctx, name, limit)

	if len(ret) == 0 {
		panic("no return value specified for SearchByName")
	}

	var r0 []*models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64) ([]*models.User, error)); ok {
		return returnFunc(ctx, name, limit)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64) []*models.User); ok {
		r0 = returnFunc(ctx, name, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, int64) error); ok {
		r1 = returnFunc(ctx, name, limit)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRepository_SearchByName_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SearchByName'
type MockUserRepository_SearchByName_Call struct {
	*mock.Call
}

// SearchByName is a helper method to define mock.On call
//   - ctx
//   - name
//   - limit
func (_e *MockUserRepository_Expecter) SearchByName(ctx interface{}, name interface{}, limit interface{}) *MockUserRepository_SearchByName_Call {
	return &MockUserRepository_SearchByName_Call{Call: _e.mock.On("SearchByName", ctx, name, limit)}
}

func (_c *MockUserRepository_SearchByName_Call) Run(run func(ctx context.Context, name string, limit int64)) *MockUserRepository_SearchByName_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int64))
	})
	return _c
}

func (_c *MockUserRepository_SearchByName_Call) Return(users []*models.User, err error) *MockUserRepository_SearchByName_Call {
	_c.Call.Return(users, err)
	return _c
}

func (_c *MockUserRepository_SearchByName_Call) RunAndReturn(run func(ctx context.Context, name string, limit int64) ([]*models.User, error)) *MockUserRepository_SearchByName_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateHasUnlockedAllGames provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) UpdateHasUnlockedAllGames(ctx context.Context, userID primitive.ObjectID, unlocked bool) error {
	ret := _mock.Called(ctx, userID, unlocked)

	if len(ret) == 0 {
		panic("no return value specified for UpdateHasUnlockedAllGames")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, bool) error); ok {
		r0 = returnFunc(ctx, userID, unlocked)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserRepository_UpdateHasUnlockedAllGames_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateHasUnlockedAllGames'
type MockUserRepository_UpdateHasUnlockedAllGames_Call struct {
	*mock.Call
}

// UpdateHasUnlockedAllGames is a helper method to define mock.On call
//   - ctx
//   - userID
//   - unlocked
func (_e *MockUserRepository_Expecter) UpdateHasUnlockedAllGames(ctx interface{}, userID interface{}, unlocked interface{}) *MockUserRepository_UpdateHasUnlockedAllGames_Call {
	return &MockUserRepository_UpdateHasUnlockedAllGames_Call{Call: _e.mock.On("UpdateHasUnlockedAllGames", ctx, userID, unlocked)}
}

func (_c *MockUserRepository_UpdateHasUnlockedAllGames_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, unlocked bool)) *MockUserRepository_UpdateHasUnlockedAllGames_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(bool))
	})
	return _c
}

func (_c *MockUserRepository_UpdateHasUnlockedAllGames_Call) Return(err error) *MockUserRepository_UpdateHasUnlockedAllGames_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserRepository_UpdateHasUnlockedAllGames_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, unlocked bool) error) *MockUserRepository_UpdateHasUnlockedAllGames_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) UpdateOne(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions) error {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, update, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter, update)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, bson.M, ...*options.UpdateOptions) error); ok {
		r0 = returnFunc(ctx, filter, update, opts...)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockUserRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx
//   - filter
//   - update
//   - opts
func (_e *MockUserRepository_Expecter) UpdateOne(ctx interface{}, filter interface{}, update interface{}, opts ...interface{}) *MockUserRepository_UpdateOne_Call {
	return &MockUserRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne",
		append([]interface{}{ctx, filter, update}, opts...)...)}
}

func (_c *MockUserRepository_UpdateOne_Call) Run(run func(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions)) *MockUserRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[3].([]*options.UpdateOptions)
		run(args[0].(context.Context), args[1].(bson.M), args[2].(bson.M), variadicArgs...)
	})
	return _c
}

func (_c *MockUserRepository_UpdateOne_Call) Return(err error) *MockUserRepository_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserRepository_UpdateOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions) error) *MockUserRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserStatikCoins provides a mock function for the type MockUserRepository
func (_mock *MockUserRepository) UpdateUserStatikCoins(ctx context.Context, userID primitive.ObjectID, coins int) error {
	ret := _mock.Called(ctx, userID, coins)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserStatikCoins")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) error); ok {
		r0 = returnFunc(ctx, userID, coins)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserRepository_UpdateUserStatikCoins_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserStatikCoins'
type MockUserRepository_UpdateUserStatikCoins_Call struct {
	*mock.Call
}

// UpdateUserStatikCoins is a helper method to define mock.On call
//   - ctx
//   - userID
//   - coins
func (_e *MockUserRepository_Expecter) UpdateUserStatikCoins(ctx interface{}, userID interface{}, coins interface{}) *MockUserRepository_UpdateUserStatikCoins_Call {
	return &MockUserRepository_UpdateUserStatikCoins_Call{Call: _e.mock.On("UpdateUserStatikCoins", ctx, userID, coins)}
}

func (_c *MockUserRepository_UpdateUserStatikCoins_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, coins int)) *MockUserRepository_UpdateUserStatikCoins_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(int))
	})
	return _c
}

func (_c *MockUserRepository_UpdateUserStatikCoins_Call) Return(err error) *MockUserRepository_UpdateUserStatikCoins_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserRepository_UpdateUserStatikCoins_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, coins int) error) *MockUserRepository_UpdateUserStatikCoins_Call {
	_c.Call.Return(run)
	return _c
}
