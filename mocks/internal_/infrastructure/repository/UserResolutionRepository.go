// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// NewMockUserResolutionRepository creates a new instance of MockUserResolutionRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserResolutionRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserResolutionRepository {
	mock := &MockUserResolutionRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockUserResolutionRepository is an autogenerated mock type for the UserResolutionRepository type
type MockUserResolutionRepository struct {
	mock.Mock
}

type MockUserResolutionRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserResolutionRepository) EXPECT() *MockUserResolutionRepository_Expecter {
	return &MockUserResolutionRepository_Expecter{mock: &_m.Mock}
}

// FindByUserID provides a mock function for the type MockUserResolutionRepository
func (_mock *MockUserResolutionRepository) FindByUserID(ctx context.Context, userID primitive.ObjectID) (*models.UserResolution, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for FindByUserID")
	}

	var r0 *models.UserResolution
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.UserResolution, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.UserResolution); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserResolution)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserResolutionRepository_FindByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByUserID'
type MockUserResolutionRepository_FindByUserID_Call struct {
	*mock.Call
}

// FindByUserID is a helper method to define mock.On call
//   - ctx
//   - userID
func (_e *MockUserResolutionRepository_Expecter) FindByUserID(ctx interface{}, userID interface{}) *MockUserResolutionRepository_FindByUserID_Call {
	return &MockUserResolutionRepository_FindByUserID_Call{Call: _e.mock.On("FindByUserID", ctx, userID)}
}

func (_c *MockUserResolutionRepository_FindByUserID_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockUserResolutionRepository_FindByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserResolutionRepository_FindByUserID_Call) Return(userResolution *models.UserResolution, err error) *MockUserResolutionRepository_FindByUserID_Call {
	_c.Call.Return(userResolution, err)
	return _c
}

func (_c *MockUserResolutionRepository_FindByUserID_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) (*models.UserResolution, error)) *MockUserResolutionRepository_FindByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// InsertOne provides a mock function for the type MockUserResolutionRepository
func (_mock *MockUserResolutionRepository) InsertOne(ctx context.Context, document *models.UserResolution) (*mongo.InsertOneResult, error) {
	ret := _mock.Called(ctx, document)

	if len(ret) == 0 {
		panic("no return value specified for InsertOne")
	}

	var r0 *mongo.InsertOneResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserResolution) (*mongo.InsertOneResult, error)); ok {
		return returnFunc(ctx, document)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserResolution) *mongo.InsertOneResult); ok {
		r0 = returnFunc(ctx, document)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.InsertOneResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.UserResolution) error); ok {
		r1 = returnFunc(ctx, document)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserResolutionRepository_InsertOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InsertOne'
type MockUserResolutionRepository_InsertOne_Call struct {
	*mock.Call
}

// InsertOne is a helper method to define mock.On call
//   - ctx
//   - document
func (_e *MockUserResolutionRepository_Expecter) InsertOne(ctx interface{}, document interface{}) *MockUserResolutionRepository_InsertOne_Call {
	return &MockUserResolutionRepository_InsertOne_Call{Call: _e.mock.On("InsertOne", ctx, document)}
}

func (_c *MockUserResolutionRepository_InsertOne_Call) Run(run func(ctx context.Context, document *models.UserResolution)) *MockUserResolutionRepository_InsertOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.UserResolution))
	})
	return _c
}

func (_c *MockUserResolutionRepository_InsertOne_Call) Return(insertOneResult *mongo.InsertOneResult, err error) *MockUserResolutionRepository_InsertOne_Call {
	_c.Call.Return(insertOneResult, err)
	return _c
}

func (_c *MockUserResolutionRepository_InsertOne_Call) RunAndReturn(run func(ctx context.Context, document *models.UserResolution) (*mongo.InsertOneResult, error)) *MockUserResolutionRepository_InsertOne_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function for the type MockUserResolutionRepository
func (_mock *MockUserResolutionRepository) UpdateOne(ctx context.Context, pledge *models.UserResolution) (*mongo.UpdateResult, error) {
	ret := _mock.Called(ctx, pledge)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 *mongo.UpdateResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserResolution) (*mongo.UpdateResult, error)); ok {
		return returnFunc(ctx, pledge)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserResolution) *mongo.UpdateResult); ok {
		r0 = returnFunc(ctx, pledge)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.UpdateResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.UserResolution) error); ok {
		r1 = returnFunc(ctx, pledge)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserResolutionRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockUserResolutionRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx
//   - pledge
func (_e *MockUserResolutionRepository_Expecter) UpdateOne(ctx interface{}, pledge interface{}) *MockUserResolutionRepository_UpdateOne_Call {
	return &MockUserResolutionRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne", ctx, pledge)}
}

func (_c *MockUserResolutionRepository_UpdateOne_Call) Run(run func(ctx context.Context, pledge *models.UserResolution)) *MockUserResolutionRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.UserResolution))
	})
	return _c
}

func (_c *MockUserResolutionRepository_UpdateOne_Call) Return(updateResult *mongo.UpdateResult, err error) *MockUserResolutionRepository_UpdateOne_Call {
	_c.Call.Return(updateResult, err)
	return _c
}

func (_c *MockUserResolutionRepository_UpdateOne_Call) RunAndReturn(run func(ctx context.Context, pledge *models.UserResolution) (*mongo.UpdateResult, error)) *MockUserResolutionRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}
