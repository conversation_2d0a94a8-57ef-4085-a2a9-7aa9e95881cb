// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockUserShadowBanRepository creates a new instance of MockUserShadowBanRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserShadowBanRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserShadowBanRepository {
	mock := &MockUserShadowBanRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockUserShadowBanRepository is an autogenerated mock type for the UserShadowBanRepository type
type MockUserShadowBanRepository struct {
	mock.Mock
}

type MockUserShadowBanRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserShadowBanRepository) EXPECT() *MockUserShadowBanRepository_Expecter {
	return &MockUserShadowBanRepository_Expecter{mock: &_m.Mock}
}

// CountAll provides a mock function for the type MockUserShadowBanRepository
func (_mock *MockUserShadowBanRepository) CountAll(ctx context.Context) (int64, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CountAll")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (int64, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) int64); ok {
		r0 = returnFunc(ctx)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserShadowBanRepository_CountAll_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountAll'
type MockUserShadowBanRepository_CountAll_Call struct {
	*mock.Call
}

// CountAll is a helper method to define mock.On call
//   - ctx
func (_e *MockUserShadowBanRepository_Expecter) CountAll(ctx interface{}) *MockUserShadowBanRepository_CountAll_Call {
	return &MockUserShadowBanRepository_CountAll_Call{Call: _e.mock.On("CountAll", ctx)}
}

func (_c *MockUserShadowBanRepository_CountAll_Call) Run(run func(ctx context.Context)) *MockUserShadowBanRepository_CountAll_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockUserShadowBanRepository_CountAll_Call) Return(n int64, err error) *MockUserShadowBanRepository_CountAll_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockUserShadowBanRepository_CountAll_Call) RunAndReturn(run func(ctx context.Context) (int64, error)) *MockUserShadowBanRepository_CountAll_Call {
	_c.Call.Return(run)
	return _c
}

// CountAllByStatus provides a mock function for the type MockUserShadowBanRepository
func (_mock *MockUserShadowBanRepository) CountAllByStatus(ctx context.Context, status models.UserShadowBanStatus) (int64, error) {
	ret := _mock.Called(ctx, status)

	if len(ret) == 0 {
		panic("no return value specified for CountAllByStatus")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.UserShadowBanStatus) (int64, error)); ok {
		return returnFunc(ctx, status)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.UserShadowBanStatus) int64); ok {
		r0 = returnFunc(ctx, status)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.UserShadowBanStatus) error); ok {
		r1 = returnFunc(ctx, status)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserShadowBanRepository_CountAllByStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountAllByStatus'
type MockUserShadowBanRepository_CountAllByStatus_Call struct {
	*mock.Call
}

// CountAllByStatus is a helper method to define mock.On call
//   - ctx
//   - status
func (_e *MockUserShadowBanRepository_Expecter) CountAllByStatus(ctx interface{}, status interface{}) *MockUserShadowBanRepository_CountAllByStatus_Call {
	return &MockUserShadowBanRepository_CountAllByStatus_Call{Call: _e.mock.On("CountAllByStatus", ctx, status)}
}

func (_c *MockUserShadowBanRepository_CountAllByStatus_Call) Run(run func(ctx context.Context, status models.UserShadowBanStatus)) *MockUserShadowBanRepository_CountAllByStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.UserShadowBanStatus))
	})
	return _c
}

func (_c *MockUserShadowBanRepository_CountAllByStatus_Call) Return(n int64, err error) *MockUserShadowBanRepository_CountAllByStatus_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockUserShadowBanRepository_CountAllByStatus_Call) RunAndReturn(run func(ctx context.Context, status models.UserShadowBanStatus) (int64, error)) *MockUserShadowBanRepository_CountAllByStatus_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function for the type MockUserShadowBanRepository
func (_mock *MockUserShadowBanRepository) Create(ctx context.Context, shadowBan *models.UserShadowBan) error {
	ret := _mock.Called(ctx, shadowBan)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserShadowBan) error); ok {
		r0 = returnFunc(ctx, shadowBan)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserShadowBanRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockUserShadowBanRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx
//   - shadowBan
func (_e *MockUserShadowBanRepository_Expecter) Create(ctx interface{}, shadowBan interface{}) *MockUserShadowBanRepository_Create_Call {
	return &MockUserShadowBanRepository_Create_Call{Call: _e.mock.On("Create", ctx, shadowBan)}
}

func (_c *MockUserShadowBanRepository_Create_Call) Run(run func(ctx context.Context, shadowBan *models.UserShadowBan)) *MockUserShadowBanRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*models.UserShadowBan))
	})
	return _c
}

func (_c *MockUserShadowBanRepository_Create_Call) Return(err error) *MockUserShadowBanRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserShadowBanRepository_Create_Call) RunAndReturn(run func(ctx context.Context, shadowBan *models.UserShadowBan) error) *MockUserShadowBanRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function for the type MockUserShadowBanRepository
func (_mock *MockUserShadowBanRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserShadowBanRepository_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockUserShadowBanRepository_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockUserShadowBanRepository_Expecter) Delete(ctx interface{}, id interface{}) *MockUserShadowBanRepository_Delete_Call {
	return &MockUserShadowBanRepository_Delete_Call{Call: _e.mock.On("Delete", ctx, id)}
}

func (_c *MockUserShadowBanRepository_Delete_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockUserShadowBanRepository_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserShadowBanRepository_Delete_Call) Return(err error) *MockUserShadowBanRepository_Delete_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserShadowBanRepository_Delete_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) error) *MockUserShadowBanRepository_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// Find provides a mock function for the type MockUserShadowBanRepository
func (_mock *MockUserShadowBanRepository) Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.UserShadowBan, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Find")
	}

	var r0 []*models.UserShadowBan
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) ([]*models.UserShadowBan, error)); ok {
		return returnFunc(ctx, filter, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) []*models.UserShadowBan); ok {
		r0 = returnFunc(ctx, filter, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserShadowBan)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, ...*options.FindOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserShadowBanRepository_Find_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Find'
type MockUserShadowBanRepository_Find_Call struct {
	*mock.Call
}

// Find is a helper method to define mock.On call
//   - ctx
//   - filter
//   - opts
func (_e *MockUserShadowBanRepository_Expecter) Find(ctx interface{}, filter interface{}, opts ...interface{}) *MockUserShadowBanRepository_Find_Call {
	return &MockUserShadowBanRepository_Find_Call{Call: _e.mock.On("Find",
		append([]interface{}{ctx, filter}, opts...)...)}
}

func (_c *MockUserShadowBanRepository_Find_Call) Run(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions)) *MockUserShadowBanRepository_Find_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]*options.FindOptions)
		run(args[0].(context.Context), args[1].(bson.M), variadicArgs...)
	})
	return _c
}

func (_c *MockUserShadowBanRepository_Find_Call) Return(userShadowBans []*models.UserShadowBan, err error) *MockUserShadowBanRepository_Find_Call {
	_c.Call.Return(userShadowBans, err)
	return _c
}

func (_c *MockUserShadowBanRepository_Find_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.UserShadowBan, error)) *MockUserShadowBanRepository_Find_Call {
	_c.Call.Return(run)
	return _c
}

// FindAll provides a mock function for the type MockUserShadowBanRepository
func (_mock *MockUserShadowBanRepository) FindAll(ctx context.Context, page int64, pageSize int64) ([]*models.UserShadowBan, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for FindAll")
	}

	var r0 []*models.UserShadowBan
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int64, int64) ([]*models.UserShadowBan, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int64, int64) []*models.UserShadowBan); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserShadowBan)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int64, int64) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserShadowBanRepository_FindAll_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindAll'
type MockUserShadowBanRepository_FindAll_Call struct {
	*mock.Call
}

// FindAll is a helper method to define mock.On call
//   - ctx
//   - page
//   - pageSize
func (_e *MockUserShadowBanRepository_Expecter) FindAll(ctx interface{}, page interface{}, pageSize interface{}) *MockUserShadowBanRepository_FindAll_Call {
	return &MockUserShadowBanRepository_FindAll_Call{Call: _e.mock.On("FindAll", ctx, page, pageSize)}
}

func (_c *MockUserShadowBanRepository_FindAll_Call) Run(run func(ctx context.Context, page int64, pageSize int64)) *MockUserShadowBanRepository_FindAll_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int64))
	})
	return _c
}

func (_c *MockUserShadowBanRepository_FindAll_Call) Return(userShadowBans []*models.UserShadowBan, err error) *MockUserShadowBanRepository_FindAll_Call {
	_c.Call.Return(userShadowBans, err)
	return _c
}

func (_c *MockUserShadowBanRepository_FindAll_Call) RunAndReturn(run func(ctx context.Context, page int64, pageSize int64) ([]*models.UserShadowBan, error)) *MockUserShadowBanRepository_FindAll_Call {
	_c.Call.Return(run)
	return _c
}

// FindAllByStatus provides a mock function for the type MockUserShadowBanRepository
func (_mock *MockUserShadowBanRepository) FindAllByStatus(ctx context.Context, status models.UserShadowBanStatus, page int64, pageSize int64) ([]*models.UserShadowBan, error) {
	ret := _mock.Called(ctx, status, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for FindAllByStatus")
	}

	var r0 []*models.UserShadowBan
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.UserShadowBanStatus, int64, int64) ([]*models.UserShadowBan, error)); ok {
		return returnFunc(ctx, status, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.UserShadowBanStatus, int64, int64) []*models.UserShadowBan); ok {
		r0 = returnFunc(ctx, status, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserShadowBan)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.UserShadowBanStatus, int64, int64) error); ok {
		r1 = returnFunc(ctx, status, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserShadowBanRepository_FindAllByStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindAllByStatus'
type MockUserShadowBanRepository_FindAllByStatus_Call struct {
	*mock.Call
}

// FindAllByStatus is a helper method to define mock.On call
//   - ctx
//   - status
//   - page
//   - pageSize
func (_e *MockUserShadowBanRepository_Expecter) FindAllByStatus(ctx interface{}, status interface{}, page interface{}, pageSize interface{}) *MockUserShadowBanRepository_FindAllByStatus_Call {
	return &MockUserShadowBanRepository_FindAllByStatus_Call{Call: _e.mock.On("FindAllByStatus", ctx, status, page, pageSize)}
}

func (_c *MockUserShadowBanRepository_FindAllByStatus_Call) Run(run func(ctx context.Context, status models.UserShadowBanStatus, page int64, pageSize int64)) *MockUserShadowBanRepository_FindAllByStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.UserShadowBanStatus), args[2].(int64), args[3].(int64))
	})
	return _c
}

func (_c *MockUserShadowBanRepository_FindAllByStatus_Call) Return(userShadowBans []*models.UserShadowBan, err error) *MockUserShadowBanRepository_FindAllByStatus_Call {
	_c.Call.Return(userShadowBans, err)
	return _c
}

func (_c *MockUserShadowBanRepository_FindAllByStatus_Call) RunAndReturn(run func(ctx context.Context, status models.UserShadowBanStatus, page int64, pageSize int64) ([]*models.UserShadowBan, error)) *MockUserShadowBanRepository_FindAllByStatus_Call {
	_c.Call.Return(run)
	return _c
}

// FindByID provides a mock function for the type MockUserShadowBanRepository
func (_mock *MockUserShadowBanRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.UserShadowBan, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindByID")
	}

	var r0 *models.UserShadowBan
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.UserShadowBan, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.UserShadowBan); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserShadowBan)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserShadowBanRepository_FindByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByID'
type MockUserShadowBanRepository_FindByID_Call struct {
	*mock.Call
}

// FindByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *MockUserShadowBanRepository_Expecter) FindByID(ctx interface{}, id interface{}) *MockUserShadowBanRepository_FindByID_Call {
	return &MockUserShadowBanRepository_FindByID_Call{Call: _e.mock.On("FindByID", ctx, id)}
}

func (_c *MockUserShadowBanRepository_FindByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockUserShadowBanRepository_FindByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserShadowBanRepository_FindByID_Call) Return(userShadowBan *models.UserShadowBan, err error) *MockUserShadowBanRepository_FindByID_Call {
	_c.Call.Return(userShadowBan, err)
	return _c
}

func (_c *MockUserShadowBanRepository_FindByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.UserShadowBan, error)) *MockUserShadowBanRepository_FindByID_Call {
	_c.Call.Return(run)
	return _c
}

// FindByUserID provides a mock function for the type MockUserShadowBanRepository
func (_mock *MockUserShadowBanRepository) FindByUserID(ctx context.Context, userID primitive.ObjectID) (*models.UserShadowBan, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for FindByUserID")
	}

	var r0 *models.UserShadowBan
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.UserShadowBan, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.UserShadowBan); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserShadowBan)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserShadowBanRepository_FindByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByUserID'
type MockUserShadowBanRepository_FindByUserID_Call struct {
	*mock.Call
}

// FindByUserID is a helper method to define mock.On call
//   - ctx
//   - userID
func (_e *MockUserShadowBanRepository_Expecter) FindByUserID(ctx interface{}, userID interface{}) *MockUserShadowBanRepository_FindByUserID_Call {
	return &MockUserShadowBanRepository_FindByUserID_Call{Call: _e.mock.On("FindByUserID", ctx, userID)}
}

func (_c *MockUserShadowBanRepository_FindByUserID_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockUserShadowBanRepository_FindByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID))
	})
	return _c
}

func (_c *MockUserShadowBanRepository_FindByUserID_Call) Return(userShadowBan *models.UserShadowBan, err error) *MockUserShadowBanRepository_FindByUserID_Call {
	_c.Call.Return(userShadowBan, err)
	return _c
}

func (_c *MockUserShadowBanRepository_FindByUserID_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) (*models.UserShadowBan, error)) *MockUserShadowBanRepository_FindByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateStatus provides a mock function for the type MockUserShadowBanRepository
func (_mock *MockUserShadowBanRepository) UpdateStatus(ctx context.Context, id primitive.ObjectID, status models.UserShadowBanStatus) error {
	ret := _mock.Called(ctx, id, status)

	if len(ret) == 0 {
		panic("no return value specified for UpdateStatus")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.UserShadowBanStatus) error); ok {
		r0 = returnFunc(ctx, id, status)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserShadowBanRepository_UpdateStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateStatus'
type MockUserShadowBanRepository_UpdateStatus_Call struct {
	*mock.Call
}

// UpdateStatus is a helper method to define mock.On call
//   - ctx
//   - id
//   - status
func (_e *MockUserShadowBanRepository_Expecter) UpdateStatus(ctx interface{}, id interface{}, status interface{}) *MockUserShadowBanRepository_UpdateStatus_Call {
	return &MockUserShadowBanRepository_UpdateStatus_Call{Call: _e.mock.On("UpdateStatus", ctx, id, status)}
}

func (_c *MockUserShadowBanRepository_UpdateStatus_Call) Run(run func(ctx context.Context, id primitive.ObjectID, status models.UserShadowBanStatus)) *MockUserShadowBanRepository_UpdateStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(primitive.ObjectID), args[2].(models.UserShadowBanStatus))
	})
	return _c
}

func (_c *MockUserShadowBanRepository_UpdateStatus_Call) Return(err error) *MockUserShadowBanRepository_UpdateStatus_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserShadowBanRepository_UpdateStatus_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID, status models.UserShadowBanStatus) error) *MockUserShadowBanRepository_UpdateStatus_Call {
	_c.Call.Return(run)
	return _c
}
