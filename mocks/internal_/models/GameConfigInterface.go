// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package models

import (
	mock "github.com/stretchr/testify/mock"
)

// NewMockGameConfigInterface creates a new instance of MockGameConfigInterface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockGameConfigInterface(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockGameConfigInterface {
	mock := &MockGameConfigInterface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockGameConfigInterface is an autogenerated mock type for the GameConfigInterface type
type MockGameConfigInterface struct {
	mock.Mock
}

type MockGameConfigInterface_Expecter struct {
	mock *mock.Mock
}

func (_m *MockGameConfigInterface) EXPECT() *MockGameConfigInterface_Expecter {
	return &MockGameConfigInterface_Expecter{mock: &_m.Mock}
}

// GetDifficultyLevel provides a mock function for the type MockGameConfigInterface
func (_mock *MockGameConfigInterface) GetDifficultyLevel() []int {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetDifficultyLevel")
	}

	var r0 []int
	if returnFunc, ok := ret.Get(0).(func() []int); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int)
		}
	}
	return r0
}

// MockGameConfigInterface_GetDifficultyLevel_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDifficultyLevel'
type MockGameConfigInterface_GetDifficultyLevel_Call struct {
	*mock.Call
}

// GetDifficultyLevel is a helper method to define mock.On call
func (_e *MockGameConfigInterface_Expecter) GetDifficultyLevel() *MockGameConfigInterface_GetDifficultyLevel_Call {
	return &MockGameConfigInterface_GetDifficultyLevel_Call{Call: _e.mock.On("GetDifficultyLevel")}
}

func (_c *MockGameConfigInterface_GetDifficultyLevel_Call) Run(run func()) *MockGameConfigInterface_GetDifficultyLevel_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockGameConfigInterface_GetDifficultyLevel_Call) Return(ints []int) *MockGameConfigInterface_GetDifficultyLevel_Call {
	_c.Call.Return(ints)
	return _c
}

func (_c *MockGameConfigInterface_GetDifficultyLevel_Call) RunAndReturn(run func() []int) *MockGameConfigInterface_GetDifficultyLevel_Call {
	_c.Call.Return(run)
	return _c
}

// GetGameType provides a mock function for the type MockGameConfigInterface
func (_mock *MockGameConfigInterface) GetGameType() interface{} {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetGameType")
	}

	var r0 interface{}
	if returnFunc, ok := ret.Get(0).(func() interface{}); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}
	return r0
}

// MockGameConfigInterface_GetGameType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGameType'
type MockGameConfigInterface_GetGameType_Call struct {
	*mock.Call
}

// GetGameType is a helper method to define mock.On call
func (_e *MockGameConfigInterface_Expecter) GetGameType() *MockGameConfigInterface_GetGameType_Call {
	return &MockGameConfigInterface_GetGameType_Call{Call: _e.mock.On("GetGameType")}
}

func (_c *MockGameConfigInterface_GetGameType_Call) Run(run func()) *MockGameConfigInterface_GetGameType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockGameConfigInterface_GetGameType_Call) Return(ifaceVal interface{}) *MockGameConfigInterface_GetGameType_Call {
	_c.Call.Return(ifaceVal)
	return _c
}

func (_c *MockGameConfigInterface_GetGameType_Call) RunAndReturn(run func() interface{}) *MockGameConfigInterface_GetGameType_Call {
	_c.Call.Return(run)
	return _c
}

// GetNumPlayers provides a mock function for the type MockGameConfigInterface
func (_mock *MockGameConfigInterface) GetNumPlayers() *int {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetNumPlayers")
	}

	var r0 *int
	if returnFunc, ok := ret.Get(0).(func() *int); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*int)
		}
	}
	return r0
}

// MockGameConfigInterface_GetNumPlayers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetNumPlayers'
type MockGameConfigInterface_GetNumPlayers_Call struct {
	*mock.Call
}

// GetNumPlayers is a helper method to define mock.On call
func (_e *MockGameConfigInterface_Expecter) GetNumPlayers() *MockGameConfigInterface_GetNumPlayers_Call {
	return &MockGameConfigInterface_GetNumPlayers_Call{Call: _e.mock.On("GetNumPlayers")}
}

func (_c *MockGameConfigInterface_GetNumPlayers_Call) Run(run func()) *MockGameConfigInterface_GetNumPlayers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockGameConfigInterface_GetNumPlayers_Call) Return(n *int) *MockGameConfigInterface_GetNumPlayers_Call {
	_c.Call.Return(n)
	return _c
}

func (_c *MockGameConfigInterface_GetNumPlayers_Call) RunAndReturn(run func() *int) *MockGameConfigInterface_GetNumPlayers_Call {
	_c.Call.Return(run)
	return _c
}

// GetTimeLimit provides a mock function for the type MockGameConfigInterface
func (_mock *MockGameConfigInterface) GetTimeLimit() *int {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetTimeLimit")
	}

	var r0 *int
	if returnFunc, ok := ret.Get(0).(func() *int); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*int)
		}
	}
	return r0
}

// MockGameConfigInterface_GetTimeLimit_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTimeLimit'
type MockGameConfigInterface_GetTimeLimit_Call struct {
	*mock.Call
}

// GetTimeLimit is a helper method to define mock.On call
func (_e *MockGameConfigInterface_Expecter) GetTimeLimit() *MockGameConfigInterface_GetTimeLimit_Call {
	return &MockGameConfigInterface_GetTimeLimit_Call{Call: _e.mock.On("GetTimeLimit")}
}

func (_c *MockGameConfigInterface_GetTimeLimit_Call) Run(run func()) *MockGameConfigInterface_GetTimeLimit_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockGameConfigInterface_GetTimeLimit_Call) Return(n *int) *MockGameConfigInterface_GetTimeLimit_Call {
	_c.Call.Return(n)
	return _c
}

func (_c *MockGameConfigInterface_GetTimeLimit_Call) RunAndReturn(run func() *int) *MockGameConfigInterface_GetTimeLimit_Call {
	_c.Call.Return(run)
	return _c
}
