// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package models

import (
	"matiksOfficial/matiks-server-go/internal/constants"

	mock "github.com/stretchr/testify/mock"
)

// NewMockUserEvent creates a new instance of MockUserEvent. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserEvent(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserEvent {
	mock := &MockUserEvent{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockUserEvent is an autogenerated mock type for the UserEvent type
type MockUserEvent struct {
	mock.Mock
}

type MockUserEvent_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserEvent) EXPECT() *MockUserEvent_Expecter {
	return &MockUserEvent_Expecter{mock: &_m.Mock}
}

// GetEventType provides a mock function for the type MockUserEvent
func (_mock *MockUserEvent) GetEventType() constants.UserEvent {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetEventType")
	}

	var r0 constants.UserEvent
	if returnFunc, ok := ret.Get(0).(func() constants.UserEvent); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Get(0).(constants.UserEvent)
	}
	return r0
}

// MockUserEvent_GetEventType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetEventType'
type MockUserEvent_GetEventType_Call struct {
	*mock.Call
}

// GetEventType is a helper method to define mock.On call
func (_e *MockUserEvent_Expecter) GetEventType() *MockUserEvent_GetEventType_Call {
	return &MockUserEvent_GetEventType_Call{Call: _e.mock.On("GetEventType")}
}

func (_c *MockUserEvent_GetEventType_Call) Run(run func()) *MockUserEvent_GetEventType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockUserEvent_GetEventType_Call) Return(userEvent constants.UserEvent) *MockUserEvent_GetEventType_Call {
	_c.Call.Return(userEvent)
	return _c
}

func (_c *MockUserEvent_GetEventType_Call) RunAndReturn(run func() constants.UserEvent) *MockUserEvent_GetEventType_Call {
	_c.Call.Return(run)
	return _c
}

// IsUserEvent provides a mock function for the type MockUserEvent
func (_mock *MockUserEvent) IsUserEvent() {
	_mock.Called()
	return
}

// MockUserEvent_IsUserEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsUserEvent'
type MockUserEvent_IsUserEvent_Call struct {
	*mock.Call
}

// IsUserEvent is a helper method to define mock.On call
func (_e *MockUserEvent_Expecter) IsUserEvent() *MockUserEvent_IsUserEvent_Call {
	return &MockUserEvent_IsUserEvent_Call{Call: _e.mock.On("IsUserEvent")}
}

func (_c *MockUserEvent_IsUserEvent_Call) Run(run func()) *MockUserEvent_IsUserEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockUserEvent_IsUserEvent_Call) Return() *MockUserEvent_IsUserEvent_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockUserEvent_IsUserEvent_Call) RunAndReturn(run func()) *MockUserEvent_IsUserEvent_Call {
	_c.Run(run)
	return _c
}
