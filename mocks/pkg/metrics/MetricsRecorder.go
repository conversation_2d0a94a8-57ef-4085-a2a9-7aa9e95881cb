// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package metrics

import (
	"context"
	"time"

	mock "github.com/stretchr/testify/mock"
)

// NewMockMetricsRecorder creates a new instance of MockMetricsRecorder. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockMetricsRecorder(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockMetricsRecorder {
	mock := &MockMetricsRecorder{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockMetricsRecorder is an autogenerated mock type for the MetricsRecorder type
type MockMetricsRecorder struct {
	mock.Mock
}

type MockMetricsRecorder_Expecter struct {
	mock *mock.Mock
}

func (_m *MockMetricsRecorder) EXPECT() *MockMetricsRecorder_Expecter {
	return &MockMetricsRecorder_Expecter{mock: &_m.Mock}
}

// RecordGraphQLError provides a mock function for the type MockMetricsRecorder
func (_mock *MockMetricsRecorder) RecordGraphQLError(ctx context.Context, errorType string) {
	_mock.Called(ctx, errorType)
	return
}

// MockMetricsRecorder_RecordGraphQLError_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RecordGraphQLError'
type MockMetricsRecorder_RecordGraphQLError_Call struct {
	*mock.Call
}

// RecordGraphQLError is a helper method to define mock.On call
//   - ctx
//   - errorType
func (_e *MockMetricsRecorder_Expecter) RecordGraphQLError(ctx interface{}, errorType interface{}) *MockMetricsRecorder_RecordGraphQLError_Call {
	return &MockMetricsRecorder_RecordGraphQLError_Call{Call: _e.mock.On("RecordGraphQLError", ctx, errorType)}
}

func (_c *MockMetricsRecorder_RecordGraphQLError_Call) Run(run func(ctx context.Context, errorType string)) *MockMetricsRecorder_RecordGraphQLError_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockMetricsRecorder_RecordGraphQLError_Call) Return() *MockMetricsRecorder_RecordGraphQLError_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockMetricsRecorder_RecordGraphQLError_Call) RunAndReturn(run func(ctx context.Context, errorType string)) *MockMetricsRecorder_RecordGraphQLError_Call {
	_c.Run(run)
	return _c
}

// RecordGraphQLOperation provides a mock function for the type MockMetricsRecorder
func (_mock *MockMetricsRecorder) RecordGraphQLOperation(ctx context.Context, operationType string, operationName string, duration time.Duration) {
	_mock.Called(ctx, operationType, operationName, duration)
	return
}

// MockMetricsRecorder_RecordGraphQLOperation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RecordGraphQLOperation'
type MockMetricsRecorder_RecordGraphQLOperation_Call struct {
	*mock.Call
}

// RecordGraphQLOperation is a helper method to define mock.On call
//   - ctx
//   - operationType
//   - operationName
//   - duration
func (_e *MockMetricsRecorder_Expecter) RecordGraphQLOperation(ctx interface{}, operationType interface{}, operationName interface{}, duration interface{}) *MockMetricsRecorder_RecordGraphQLOperation_Call {
	return &MockMetricsRecorder_RecordGraphQLOperation_Call{Call: _e.mock.On("RecordGraphQLOperation", ctx, operationType, operationName, duration)}
}

func (_c *MockMetricsRecorder_RecordGraphQLOperation_Call) Run(run func(ctx context.Context, operationType string, operationName string, duration time.Duration)) *MockMetricsRecorder_RecordGraphQLOperation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(time.Duration))
	})
	return _c
}

func (_c *MockMetricsRecorder_RecordGraphQLOperation_Call) Return() *MockMetricsRecorder_RecordGraphQLOperation_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockMetricsRecorder_RecordGraphQLOperation_Call) RunAndReturn(run func(ctx context.Context, operationType string, operationName string, duration time.Duration)) *MockMetricsRecorder_RecordGraphQLOperation_Call {
	_c.Run(run)
	return _c
}

// RecordWebsocketError provides a mock function for the type MockMetricsRecorder
func (_mock *MockMetricsRecorder) RecordWebsocketError(ctx context.Context, errorType string) {
	_mock.Called(ctx, errorType)
	return
}

// MockMetricsRecorder_RecordWebsocketError_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RecordWebsocketError'
type MockMetricsRecorder_RecordWebsocketError_Call struct {
	*mock.Call
}

// RecordWebsocketError is a helper method to define mock.On call
//   - ctx
//   - errorType
func (_e *MockMetricsRecorder_Expecter) RecordWebsocketError(ctx interface{}, errorType interface{}) *MockMetricsRecorder_RecordWebsocketError_Call {
	return &MockMetricsRecorder_RecordWebsocketError_Call{Call: _e.mock.On("RecordWebsocketError", ctx, errorType)}
}

func (_c *MockMetricsRecorder_RecordWebsocketError_Call) Run(run func(ctx context.Context, errorType string)) *MockMetricsRecorder_RecordWebsocketError_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockMetricsRecorder_RecordWebsocketError_Call) Return() *MockMetricsRecorder_RecordWebsocketError_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockMetricsRecorder_RecordWebsocketError_Call) RunAndReturn(run func(ctx context.Context, errorType string)) *MockMetricsRecorder_RecordWebsocketError_Call {
	_c.Run(run)
	return _c
}

// RecordWebsocketMessage provides a mock function for the type MockMetricsRecorder
func (_mock *MockMetricsRecorder) RecordWebsocketMessage(ctx context.Context, messageType string, duration time.Duration) {
	_mock.Called(ctx, messageType, duration)
	return
}

// MockMetricsRecorder_RecordWebsocketMessage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RecordWebsocketMessage'
type MockMetricsRecorder_RecordWebsocketMessage_Call struct {
	*mock.Call
}

// RecordWebsocketMessage is a helper method to define mock.On call
//   - ctx
//   - messageType
//   - duration
func (_e *MockMetricsRecorder_Expecter) RecordWebsocketMessage(ctx interface{}, messageType interface{}, duration interface{}) *MockMetricsRecorder_RecordWebsocketMessage_Call {
	return &MockMetricsRecorder_RecordWebsocketMessage_Call{Call: _e.mock.On("RecordWebsocketMessage", ctx, messageType, duration)}
}

func (_c *MockMetricsRecorder_RecordWebsocketMessage_Call) Run(run func(ctx context.Context, messageType string, duration time.Duration)) *MockMetricsRecorder_RecordWebsocketMessage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(time.Duration))
	})
	return _c
}

func (_c *MockMetricsRecorder_RecordWebsocketMessage_Call) Return() *MockMetricsRecorder_RecordWebsocketMessage_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockMetricsRecorder_RecordWebsocketMessage_Call) RunAndReturn(run func(ctx context.Context, messageType string, duration time.Duration)) *MockMetricsRecorder_RecordWebsocketMessage_Call {
	_c.Run(run)
	return _c
}

// WebsocketConnected provides a mock function for the type MockMetricsRecorder
func (_mock *MockMetricsRecorder) WebsocketConnected(ctx context.Context) {
	_mock.Called(ctx)
	return
}

// MockMetricsRecorder_WebsocketConnected_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WebsocketConnected'
type MockMetricsRecorder_WebsocketConnected_Call struct {
	*mock.Call
}

// WebsocketConnected is a helper method to define mock.On call
//   - ctx
func (_e *MockMetricsRecorder_Expecter) WebsocketConnected(ctx interface{}) *MockMetricsRecorder_WebsocketConnected_Call {
	return &MockMetricsRecorder_WebsocketConnected_Call{Call: _e.mock.On("WebsocketConnected", ctx)}
}

func (_c *MockMetricsRecorder_WebsocketConnected_Call) Run(run func(ctx context.Context)) *MockMetricsRecorder_WebsocketConnected_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockMetricsRecorder_WebsocketConnected_Call) Return() *MockMetricsRecorder_WebsocketConnected_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockMetricsRecorder_WebsocketConnected_Call) RunAndReturn(run func(ctx context.Context)) *MockMetricsRecorder_WebsocketConnected_Call {
	_c.Run(run)
	return _c
}

// WebsocketDisconnected provides a mock function for the type MockMetricsRecorder
func (_mock *MockMetricsRecorder) WebsocketDisconnected(ctx context.Context) {
	_mock.Called(ctx)
	return
}

// MockMetricsRecorder_WebsocketDisconnected_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WebsocketDisconnected'
type MockMetricsRecorder_WebsocketDisconnected_Call struct {
	*mock.Call
}

// WebsocketDisconnected is a helper method to define mock.On call
//   - ctx
func (_e *MockMetricsRecorder_Expecter) WebsocketDisconnected(ctx interface{}) *MockMetricsRecorder_WebsocketDisconnected_Call {
	return &MockMetricsRecorder_WebsocketDisconnected_Call{Call: _e.mock.On("WebsocketDisconnected", ctx)}
}

func (_c *MockMetricsRecorder_WebsocketDisconnected_Call) Run(run func(ctx context.Context)) *MockMetricsRecorder_WebsocketDisconnected_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockMetricsRecorder_WebsocketDisconnected_Call) Return() *MockMetricsRecorder_WebsocketDisconnected_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockMetricsRecorder_WebsocketDisconnected_Call) RunAndReturn(run func(ctx context.Context)) *MockMetricsRecorder_WebsocketDisconnected_Call {
	_c.Run(run)
	return _c
}
