package config

import (
	"errors"
	"fmt"
	"log"

	"github.com/spf13/viper"
)

type Config struct {
	ServerPort                         string   `mapstructure:"PORT"`
	MongoURI                           string   `mapstructure:"MONGODB_URI"`
	MongoDB                            string   `mapstructure:"MONGODB_NAME"`
	JWTSecret                          string   `mapstructure:"JWT_SECRET"`
	GoogleClientID                     string   `mapstructure:"GOOGLE_CLIENT_ID"`
	GoogleClientSecret                 string   `mapstructure:"GOOGLE_CLIENT_SECRET"`
	Environment                        string   `mapstructure:"ENVIRONMENT"`
	RedisAddr                          string   `mapstructure:"REDIS_ADDR"`
	AllowedOrigins                     []string `mapstructure:"ALLOWED_ORIGINS"`
	FirebaseStorageServiceKey          string   `mapstructure:"FIREBASE_STORAGE_SERVICE_ACCOUNT_KEY"`
	FirebasePushNotificationServiceKey string   `mapstructure:"FIREBASE_PUSH_NOTIFICATION_SERVICE_ACCOUNT_KEY"`
	SMTPHost                           string   `mapstructure:"SMTP_HOST"`
	SMTPPort                           int      `mapstructure:"SMTP_PORT"`
	SMTPPassword                       string   `mapstructure:"SMTP_PASSWORD"`
	SMTPFromEmail                      string   `mapstructure:"SMTP_FROM_EMAIL"`
	PProfPort                          string   `mapstructure:"PPROF_PORT"`
	GoogleCloudProject                 string   `mapstructure:"GOOGLE_CLOUD_PROJECT"`
	IsProfilerEnabled                  bool     `mapstructure:"IS_PROFILER_ENABLED"`
	CDNServiceKey                      string   `mapstructure:"CDN_SERVICE_ACCOUNT_KEY"`
	CDNUrlMap                          string   `mapstructure:"CDN_URL_MAP"`
	CDNHost                            string   `mapstructure:"CDN_HOST"`
}

func Load() (*Config, error) {
	viper.SetConfigFile(".env")
	viper.AutomaticEnv()
	viper.BindEnv("MONGODB_URI")
	viper.BindEnv("MONGODB_NAME")
	viper.BindEnv("JWT_SECRET")
	viper.BindEnv("GOOGLE_CLIENT_ID")
	viper.BindEnv("GOOGLE_CLIENT_SECRET")
	viper.BindEnv("ENVIRONMENT")
	viper.BindEnv("REDIS_ADDR")
	viper.BindEnv("ALLOWED_ORIGINS")
	viper.BindEnv("PORT")
	viper.BindEnv("FIREBASE_STORAGE_SERVICE_ACCOUNT_KEY")
	viper.BindEnv("FIREBASE_PUSH_NOTIFICATION_SERVICE_ACCOUNT_KEY")
	viper.BindEnv("SMTP_HOST")
	viper.BindEnv("SMTP_PORT")
	viper.BindEnv("SMTP_PASSWORD")
	viper.BindEnv("SMTP_FROM_EMAIL")
	viper.BindEnv("PPROF_PORT")
	viper.BindEnv("GOOGLE_CLOUD_PROJECT")
	viper.BindEnv("IS_PROFILER_ENABLED")
	viper.BindEnv("CDN_SERVICE_ACCOUNT_KEY")
	viper.BindEnv("CDN_URL_MAP")
	viper.BindEnv("CDN_HOST")

	if err := viper.ReadInConfig(); err != nil {
		var configFileNotFoundError viper.ConfigFileNotFoundError
		if !errors.As(err, &configFileNotFoundError) {
			log.Println("Warning: .env-local file not found. Using environment variables.")
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	viper.SetDefault("ENVIRONMENT", "local")
	viper.SetDefault("PORT", "4000")
	viper.SetDefault("REDIS_ADDR", "localhost:6379")
	viper.SetDefault("PPROF_PORT", "6060")
	viper.SetDefault("GOOGLE_CLOUD_PROJECT", "matiks-go")
	viper.SetDefault("ALLOWED_ORIGINS", []string{"http://localhost:8081"})
	viper.SetDefault("IS_PROFILER_ENABLED", true)
	viper.SetDefault("CDN_HOST", "cdn.matiks.com")
	viper.SetDefault("CDN_URL_MAP", "matiks-cdn-map")

	return &config, nil
}
