package zlog

import (
	"context"
	"os"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/pkg/config"

	"github.com/go-chi/chi/v5/middleware"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var logger *zap.Logger

func NewLogger(cfg *config.Config) *zap.Logger {
	environment := cfg.Environment
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "timestamp",
		LevelKey:       "severity",
		NameKey:        "logger",
		Caller<PERSON>ey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "message",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    encodeLevel,
		EncodeTime:     zapcore.RFC3339NanoTimeEncoder,
		EncodeDuration: zapcore.MillisDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	var core zapcore.Core
	if environment == constants.ProdEnvironment || environment == constants.DevEnvironment {
		jsonEncoder := zapcore.NewJSONEncoder(encoderConfig)
		core = zapcore.NewCore(jsonEncoder, zapcore.AddSync(os.Stdout), zap.NewAtomicLevelAt(zap.DebugLevel))
	} else {
		consoleEncoder := zapcore.NewConsoleEncoder(encoderConfig)
		core = zapcore.NewCore(consoleEncoder, zapcore.AddSync(os.Stdout), zap.NewAtomicLevelAt(zap.DebugLevel))
	}

	zapLogger := zap.New(core, zap.AddCallerSkip(1))

	logger = zapLogger

	return zapLogger
}

func Info(ctx context.Context, msg string, fields ...zap.Field) {
	logger.With(contextFields(ctx)...).Info(msg, fields...)
}

func Error(ctx context.Context, msg string, err error, fields ...zap.Field) {
	if err != nil {
		fields = append(fields, zap.Error(err))
	}
	logger.With(contextFields(ctx)...).Error(msg, fields...)
}

func Warn(ctx context.Context, msg string, fields ...zap.Field) {
	logger.With(contextFields(ctx)...).Warn(msg, fields...)
}

func Debug(ctx context.Context, msg string, fields ...zap.Field) {
	logger.With(contextFields(ctx)...).Debug(msg, fields...)
}

func Sync() error {
	return logger.Sync()
}

func contextFields(ctx context.Context) []zap.Field {
	fields := make([]zap.Field, 0, 2)
	if requestID := middleware.GetReqID(ctx); requestID != "" {
		fields = append(fields, zap.String("requestID", requestID))
	}
	if userID, ok := ctx.Value(constants.UserContextKey).(string); ok {
		fields = append(fields, zap.String("userID", userID))
	}
	if gameID, ok := ctx.Value(constants.GameIDKey).(string); ok {
		fields = append(fields, zap.String("gameID", gameID))
	}
	return fields
}

func encodeLevel(l zapcore.Level, enc zapcore.PrimitiveArrayEncoder) {
	switch l {
	case zapcore.DebugLevel:
		enc.AppendString("DEBUG")
	case zapcore.InfoLevel:
		enc.AppendString("INFO")
	case zapcore.WarnLevel:
		enc.AppendString("WARNING")
	case zapcore.ErrorLevel:
		enc.AppendString("ERROR")
	case zapcore.DPanicLevel:
		enc.AppendString("CRITICAL")
	case zapcore.PanicLevel:
		enc.AppendString("ALERT")
	case zapcore.FatalLevel:
		enc.AppendString("EMERGENCY")
	}
}
