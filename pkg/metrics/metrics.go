package metrics

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/pkg/config"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	cloudmetrics "github.com/GoogleCloudPlatform/opentelemetry-operations-go/exporter/metric"
	metricSdk "go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/resource"
	semconv "go.opentelemetry.io/otel/semconv/v1.17.0"

	"go.opentelemetry.io/otel/metric"

	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/fx"
)

type MetricsRecorder interface {
	RecordGraphQLOperation(ctx context.Context, operationType, operationName string, duration time.Duration)
	RecordGraphQLError(ctx context.Context, errorType string)
	WebsocketConnected(ctx context.Context)
	WebsocketDisconnected(ctx context.Context)
	RecordWebsocketMessage(ctx context.Context, messageType string, duration time.Duration)
	RecordWebsocketError(ctx context.Context, errorType string)
}

type activeMetrics struct {
	gqlOperations   metric.Int64Counter
	gqlErrors       metric.Int64Counter
	gqlDuration     metric.Float64Histogram
	wsConnections   metric.Int64UpDownCounter
	wsMessages      metric.Int64Counter
	wsErrors        metric.Int64Counter
	wsLatency       metric.Float64Histogram
	concurrentUsers metric.Int64UpDownCounter
}

type noopMetrics struct{}

var instance MetricsRecorder = &noopMetrics{}

func NewMetrics(lc fx.Lifecycle, cfg *config.Config) (MetricsRecorder, error) {
	projectID := cfg.GoogleCloudProject
	serviceName := constants.DevServiceName
	if cfg.Environment == constants.ProdEnvironment {
		serviceName = constants.ProdServiceName
	}

	exporter, err := cloudmetrics.New(
		cloudmetrics.WithProjectID(projectID),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create cloud monitoring exporter: %v", err)
	}

	reader := metricSdk.NewPeriodicReader(exporter,
		metricSdk.WithInterval(60*time.Second), // Export metrics every 60 seconds
	)

	meterProvider := metricSdk.NewMeterProvider(
		metricSdk.WithReader(reader),
		metricSdk.WithResource(resource.NewWithAttributes(
			semconv.SchemaURL,
			semconv.ServiceName(serviceName),
			semconv.ServiceVersion(constants.Version),
			semconv.DeploymentEnvironment(cfg.Environment),
		)),
	)

	meter := meterProvider.Meter(serviceName)

	// GraphQL metrics
	gqlOperations, err := meter.Int64Counter(
		"graphql.operations_total",
		metric.WithDescription("Total number of GraphQL operations"),
	)
	if err != nil {
		return instance, err
	}

	gqlErrors, err := meter.Int64Counter(
		"graphql.errors_total",
		metric.WithDescription("Total number of GraphQL errors"),
	)
	if err != nil {
		return instance, err
	}

	gqlDuration, err := meter.Float64Histogram(
		"graphql.duration_seconds",
		metric.WithDescription("Duration of GraphQL operations"),
	)
	if err != nil {
		return instance, err
	}

	// WebSocket metrics
	wsConnections, err := meter.Int64UpDownCounter(
		"websocket.connections",
		metric.WithDescription("Number of active WebSocket connections"),
	)
	if err != nil {
		return instance, err
	}

	wsMessages, err := meter.Int64Counter(
		"websocket.messages_total",
		metric.WithDescription("Total number of WebSocket messages"),
	)
	if err != nil {
		return instance, err
	}

	wsErrors, err := meter.Int64Counter(
		"websocket.errors_total",
		metric.WithDescription("Total number of WebSocket errors"),
	)
	if err != nil {
		return instance, err
	}

	wsLatency, err := meter.Float64Histogram(
		"websocket.latency_seconds",
		metric.WithDescription("WebSocket message latency"),
	)
	if err != nil {
		return instance, err
	}

	concurrentUsers, err := meter.Int64UpDownCounter(
		"users.concurrent",
		metric.WithDescription("Number of concurrent users"),
	)
	if err != nil {
		return instance, err
	}

	active := &activeMetrics{
		gqlOperations:   gqlOperations,
		gqlErrors:       gqlErrors,
		gqlDuration:     gqlDuration,
		wsConnections:   wsConnections,
		wsMessages:      wsMessages,
		wsErrors:        wsErrors,
		wsLatency:       wsLatency,
		concurrentUsers: concurrentUsers,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Metrics initialized and ready.")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down metrics...")
			if err := meterProvider.Shutdown(ctx); err != nil {
				zlog.Error(ctx, "Error shutting down meter provider", err)
			}
			return nil
		},
	})

	instance = active
	return instance, nil
}

func Get() MetricsRecorder {
	return instance
}

// GraphQL metric methods
func (m *activeMetrics) RecordGraphQLOperation(ctx context.Context, operationType, operationName string, duration time.Duration) {
	attrs := []attribute.KeyValue{
		attribute.String("operation_type", operationType),
		attribute.String("operation_name", operationName),
	}

	m.gqlOperations.Add(ctx, 1, metric.WithAttributes(attrs...))
	m.gqlDuration.Record(ctx, duration.Seconds(), metric.WithAttributes(attrs...))
}

func (m *activeMetrics) RecordGraphQLError(ctx context.Context, errorType string) {
	m.gqlErrors.Add(ctx, 1, metric.WithAttributes(
		attribute.String("error_type", errorType),
	))
}

// WebSocket metric methods
func (m *activeMetrics) WebsocketConnected(ctx context.Context) {
	m.wsConnections.Add(ctx, 1)
	m.concurrentUsers.Add(ctx, 1)
}

func (m *activeMetrics) WebsocketDisconnected(ctx context.Context) {
	m.wsConnections.Add(ctx, -1)
	m.concurrentUsers.Add(ctx, -1)
}

func (m *activeMetrics) RecordWebsocketMessage(ctx context.Context, messageType string, duration time.Duration) {
	attrs := []attribute.KeyValue{
		attribute.String("message_type", messageType),
	}

	m.wsMessages.Add(ctx, 1, metric.WithAttributes(attrs...))
	m.wsLatency.Record(ctx, duration.Seconds(), metric.WithAttributes(attrs...))
}

func (m *activeMetrics) RecordWebsocketError(ctx context.Context, errorType string) {
	m.wsErrors.Add(ctx, 1, metric.WithAttributes(
		attribute.String("error_type", errorType),
	))
}

func (m *noopMetrics) RecordGraphQLOperation(ctx context.Context, operationType, operationName string, duration time.Duration) {
}
func (m *noopMetrics) RecordGraphQLError(ctx context.Context, errorType string) {}
func (m *noopMetrics) WebsocketConnected(ctx context.Context)                   {}
func (m *noopMetrics) WebsocketDisconnected(ctx context.Context)                {}
func (m *noopMetrics) RecordWebsocketMessage(ctx context.Context, messageType string, duration time.Duration) {
}
func (m *noopMetrics) RecordWebsocketError(ctx context.Context, errorType string) {}

var Module = fx.Module(
	"metrics",
	fx.Provide(NewMetrics),
)
