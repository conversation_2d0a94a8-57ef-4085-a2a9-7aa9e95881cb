package slicesustils

import (
	"testing"
)

func TestContains(t *testing.T) {
	tests := []struct {
		name     string
		slice    []int
		element  int
		expected bool
	}{
		{
			name:     "element exists in slice",
			slice:    []int{1, 2, 3, 4, 5},
			element:  3,
			expected: true,
		},
		{
			name:     "element does not exist in slice",
			slice:    []int{1, 2, 3, 4, 5},
			element:  6,
			expected: false,
		},
		{
			name:     "empty slice",
			slice:    []int{},
			element:  1,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Contains(tt.slice, tt.element)
			if result != tt.expected {
				t.Errorf("Contains(%v, %v) = %v, expected %v", tt.slice, tt.element, result, tt.expected)
			}
		})
	}
}

func TestContainsString(t *testing.T) {
	tests := []struct {
		name     string
		slice    []string
		element  string
		expected bool
	}{
		{
			name:     "element exists in slice",
			slice:    []string{"apple", "banana", "cherry"},
			element:  "banana",
			expected: true,
		},
		{
			name:     "element does not exist in slice",
			slice:    []string{"apple", "banana", "cherry"},
			element:  "orange",
			expected: false,
		},
		{
			name:     "empty slice",
			slice:    []string{},
			element:  "apple",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Contains(tt.slice, tt.element)
			if result != tt.expected {
				t.Errorf("Contains(%v, %v) = %v, expected %v", tt.slice, tt.element, result, tt.expected)
			}
		})
	}
}
