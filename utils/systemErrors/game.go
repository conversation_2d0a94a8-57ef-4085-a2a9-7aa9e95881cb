package systemErrors

import "errors"

var (
	ErrGameAlreadyEnded      = errors.New("game already ended")
	ErrGameCancelled         = errors.New("game cancelled")
	ErrGameNotStarted        = errors.New("game not started yet")
	ErrGameAlreadyStarted    = errors.New("game already started")
	ErrGameNotInStartedState = errors.New("game not in started state")
	ErrGameNotInReadyState   = errors.New("game not in ready state")
	ErrGameNotInCreatedState = errors.New("game not in created state")
)
