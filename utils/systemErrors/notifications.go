package systemErrors

import "errors"

var (
	ErrNotificationFoundNil                 = errors.New("notification not found")
	ErrNotificationUserIDNil                = errors.New("user id is nil")
	ErrNotificationSentAtNil                = errors.New("sent at is nil")
	ErrNotificationFeedNil                  = errors.New("feed is nil")
	ErrNotificationFeedTypeNil              = errors.New("feed type is nil")
	ErrNotificationFeedForFriendsNil        = errors.New("feed for friends is nil")
	ErrNotificationFeedAdditionalInfoNil    = errors.New("feed additional info is nil")
	ErrNotificationFeedForFriendsSameUserId = errors.New("feed for friends same user id")
	ErrNotificationFeedDataNil              = errors.New("feed data is nil")
	ErrNotificationFeedTitleNil             = errors.New("feed title is nil")
	ErrNotificationAdditionalInfoNil        = errors.New("additional info is nil")
	ErrPublishUserEventFailed               = errors.New("failed to publish user event")
	ErrNotificationFeedConnectionRequestNil = errors.New("feed connection request is nil")
	ErrNotificationInAppTypeNil             = errors.New("in app type is nil")
	ErrNotificationNoPushTokens             = errors.New("no push tokens found")
	ErrUnsupportedInAppType                 = errors.New("unsupported in app type")
)
